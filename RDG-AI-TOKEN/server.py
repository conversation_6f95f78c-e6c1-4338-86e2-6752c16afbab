from flask import Flask, request, jsonify
from flask_cors import CORS 
import auth
from waitress import serve

app = Flask(__name__)
CORS(app) 

@app.route('/generate-token', methods=['POST'])
def generate_token():
    user_data = request.json
    print(user_data)

    user = auth.User(identifier=user_data['identifier'], metadata=user_data['metadata'])
    print(type(user))
    token = auth.create_jwt(user)
    return jsonify({'token': token})

if __name__ == '__main__':
    serve(app, host="0.0.0.0", port=4000)
