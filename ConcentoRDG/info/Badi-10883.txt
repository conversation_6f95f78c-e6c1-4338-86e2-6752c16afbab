@startuml
User -> Save: Create Multi Derivation Value Rule using Data Model(dm) and Entity(e)
Save -> Backend_Service: Send dm, e, type of derivation(cross entity or no cross entity)
note right
Check for BADi Implementation for dm-e 
combination. If found, check whether 
the BADI is saved as a local object or 
imported from a different system
end note
alt Combination for type, dm, e exists
    alt BADi is stored in local object or imported from different system
        Backend_Service -> Save: Response: Message Type: Error, Message: Cannot read Badi details
        Save -> User: Display Error Message: Cannot save details in Badi. Contact administrator
    else <PERSON><PERSON> is saved in Tr object in same system
        Backend_Service -> Save: Response: Success: Empty Response
        Save -> User: Ask Transport request and Package Details
        User -> Save: Select Transport and Package
        Save -> Backend_Service: POST request to save the rule in the badi implementation
    end
else Combination does not exist
    Backend_Service -> Save: Response: List of Active BADI Implementations that can be selected
    Save -> User: Ask User to select BADI Implementation from list or create a new BADI Implementation
    alt Select available BADI Implementation
        Save -> User: Ask Transport request and Package Details
        User -> Save: Select Transport and Package
        Save -> Backend_Service: POST request to save the rule in the selected badi implementation
    else Create New BADI Implementation
        User -> Save: Input Implementation Name
        Save -> User: Ask Transport request and Package Details
        User -> Save: Select Transport and Package
        Save -> Backend_Service: POST request to create implementation in rule service
        Save -> Backend_Service: POST request to save the rule in the selected badi implementation
    end
end
@enduml