import globals from "globals";
import stylisticJs from "@stylistic/eslint-plugin-js"

/**
Rule Severities

To change a rule’s severity, set the rule ID equal to one of these values:

    "off" or 0 - turn the rule off
    "warn" or 1 - turn the rule on as a warning (doesn’t affect exit code)
    "error" or 2 - turn the rule on as an error (exit code is 1 when triggered)

Rules are typically set to "error" to enforce compliance with the rule during continuous integration testing, pre-commit checks, and pull request merging because doing so causes ESLint to exit with a non-zero exit code.

If you don’t want to enforce compliance with a rule but would still like ESLint to report the rule’s violations, set the severity to "warn". This is typically used when introducing a new rule that will eventually be set to "error", when a rule is flagging something other than a potential buildtime or runtime error (such as an unused variable), or when a rule cannot determine with certainty that a problem has been found (when a rule might have false positives and need manual review).
 */

export default [
{
    linterOptions: {
        "reportUnusedDisableDirectives": "warn",
    },
    ignores: [
        "webapp/libs/CustomLayout.js",
        "dist/**/*.js", "dist/**/*.*.js",
        "webapp/test/**/*.js", "webapp/test/**/*.*.js",
        "CloudFoundry/**/*.js", "CloudFoundry/**/*.*.js"
    ],
    plugins: {
        "@stylistic/js": stylisticJs
    },
    languageOptions: {
        globals: {
            ...globals.browser,
            ...globals.jquery,
            jQuery: true,
            sap: true,
            tinyMCE: "readonly"
        },

        ecmaVersion: "latest",
        sourceType: "module",

        parserOptions: {
            ecmaFeatures: {
                jsx: true,
            },
        },
    },

    rules: {
        "block-scoped-var": "error",
        "camelcase": ["error", 
            {
                allow:[
                    "PR_STEP_ACTSet", // Used in the ApproveAssignment.controller.js as a parameter of incoming oData param
                    "invalid_elements", // Used for configuring tinyMCE editor (Rich Text Editor)
                ]
            }
        ],

        "comma-spacing": ["error", {
            "after": true,
            "before": false,
        }],

        "default-case": "error",

        "dot-notation": ["error", {
            "allowKeywords": true,
        }],

        "eqeqeq": ["error", "always"],
        "new-cap": "error",
        "no-alert": "error",
        "no-array-constructor": "error",
        "no-caller": "error",
        "no-cond-assign": "error",
        "no-console": "error",
        "no-constant-condition": "error",
        "no-control-regex": "error",
        "no-debugger": "error",
        "no-delete-var": "error",
        "no-dupe-args": "error",
        "no-dupe-keys": "error",
        "no-duplicate-case": "error",
        "no-empty": "error",
        "no-empty-character-class": "error",
        "no-eval": "error",
        "no-extra-boolean-cast": "error",
        "no-extra-semi": "error",
        "no-fallthrough": "error",
        "no-func-assign": "error",
        "no-implied-eval": "error",
        "no-invalid-regexp": "error",
        "no-irregular-whitespace": "error",
        "no-label-var": "error",
        "no-native-reassign": "error",
        "no-negated-in-lhs": "error",
        "no-new": "error",
        "no-new-func": "error",
        "no-new-wrappers": "error",
        "no-obj-calls": "error",
        "no-object-constructor": "error",
        "no-octal": "error",
        "no-octal-escape": "error",
        "no-redeclare": "error",
        "no-regex-spaces": "error",
        "no-return-assign": "error",
        "no-sequences": "error",
        "no-shadow": "error",
        "no-shadow-restricted-names": "error",
        "no-spaced-func": "error",
        "no-sparse-arrays": "error",
        "no-throw-literal": "error",
        "no-undef": "error",
        "no-unreachable": "error",
        "no-unused-vars": ["error", {
            "argsIgnorePattern": "^ignored*",
            "caughtErrors": "none",
            "args": "after-used",
            "vars": "all",
        }],
        "no-use-before-define": "error",
        "no-var": "error",
        "no-void": "error",
        "no-warning-comments": "error",
        "quotes": ["error", "double"],
        "semi": ["error", "always"],
        "space-unary-ops": "error",
        "use-isnan": "error",
        "valid-typeof": "error",
        /** 
         * Rules modified or added after 09-Oct-2024.
         * Some errors have been suppressed or enabled. 
         * Rules that need to be followed going forward have been moved to warn. These 
         * will eventually be moved to error to enforce.
         */
        "complexity": [
            "warn", { 
                /* Set to 100 on 08-Oct-2024. Migrating to Lint 9* enabled this by default. 
                Will need to be set to 50 as a short term target.
                */
                "max": 30
        }],
        "array-callback-return":["off", {
            "allowImplicit": true,
            "checkForEach": true,
            "allowVoid": true
        }],
        "curly": [ "off", "all" ],
        "global-require": "error",
        "@stylistic/js/indent": ["off", 
            "tab",
            { "SwitchCase": 1 }
        ],
        "max-depth": ["warn", 6],
        "no-implicit-globals": "error",
        "no-nested-ternary": "warn"
    },
}];