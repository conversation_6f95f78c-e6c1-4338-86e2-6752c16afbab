@startuml


frame DevActions {
    :LocalDev: 
    [VScode]
    LocalDev <-- VScode  : Pull
    LocalDev --> VScode  : Clone/Commit/Push

}

title Deploy Pipeline for all Syniti environment


frame gitHub {
    package DevBranch {
        [Dev]  
    }
    package QASBranch {
        [QAS]
    }
    package preMasterBranch {
        [preMaster]
    }
    package masteBranch {
        [Master]
    }
}

Dev -[bold]-> QAS
QAS -[bold]-> preMaster
preMaster -[bold]-> Master

VScode -[dotted]-> Dev

cloud SD2andT42 #Green { 
    file deployQAS.yml 
}
cloud S41 #LawnGreen { 
    file deployDEV.yml
}
cloud MG1 #YellowGreen { 
    file deployMaster.yml
}
cloud S42 #LightSeaGreen { 
    file process_TBD
}
person CustomerEnv 

Dev -[dashed]-> S41 : AutoDeployUsingPipeline
QAS -[dashed]-> SD2andT42 : AutoDeployUsingPipeline
preMaster -[dashed]-> MG1 : ManualDeployUsingVsCode
Master -[dashed]-> S42 : ManualDeployUsingVsCode
Master -[bold]-> CustomerEnv : ManualDeployUsingVsCode

@enduml