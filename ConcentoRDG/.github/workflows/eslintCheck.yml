name: <PERSON><PERSON><PERSON> - E<PERSON><PERSON> Check
on:
  pull_request:
    branches: [ "Dev" ]

env:
  AWS_REGION: us-east-2
  BTP_NEO_ENDPOINT: https://us3.hana.ondemand.com
    
jobs:
  Build-app:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 1
      - uses: actions/setup-node@v4
        with:
          node-version: 20
      - run: npm install -g npm@latest 
      - run: npm i @ui5/cli@latest -g

  Run-Eslint:
    runs-on: ubuntu-latest
    needs: [Build-app]
    steps:
      - uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 1
      - uses: actions/setup-node@v4
        with:
          node-version: 20
      - run: npm install globals
      - run: npm install -g eslint@9.12.0
      - run: eslint --max-warnings 29 --config eslint.config.mjs webapp

  Check-Deploy:
    runs-on: ubuntu-latest
    needs: [Run-Eslint]
    steps:
      - uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 1
      - name: Check Deploy
        id: CloudNeo-dokcer-build
        env:
          file: ./CloudNeo/DockerfileTest
          Repository_name: rdgs41
          neo_endpoint: https://us3.hana.ondemand.com
        run: |
          docker build -f $file -t $Repository_name . --build-arg HOST=$neo_endpoint --build-arg USR=${{secrets.NEO_USERNAME}} --build-arg PASS=${{secrets.NEO_PASSWORD}} --build-arg SUBACCOUNT=${{secrets.S41_SUBACCOUNT}}
