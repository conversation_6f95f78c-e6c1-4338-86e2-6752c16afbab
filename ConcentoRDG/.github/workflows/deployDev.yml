name: Deploy to DEV Neo S41
on:
  pull_request:
    types: 
      - closed
jobs:
    BTP-Deploy-to-DEV-Neo-S41:
      if: (github.event.pull_request.merged == true && github.base_ref == 'Dev')
      runs-on: ubuntu-latest
      name: Deploy to DEV Neo S41
      steps:
        - name: Checkout repo
          uses: actions/checkout@v3 
          
        - name: Docker Build
          id: CloudNeo-dokcer-build
          env:
            file: ./CloudNeo/Dockerfile
            Repository_name: rdgs41
            neo_endpoint: https://us3.hana.ondemand.com
          run: |
            docker build -f $file -t $Repository_name . --build-arg HOST=$neo_endpoint --build-arg USR=${{secrets.NEO_USERNAME}} --build-arg PASS=${{secrets.NEO_PASSWORD}} --build-arg SUBACCOUNT=${{secrets.S41_SUBACCOUNT}}
    BTP-CF-Deploy-S41:
      if: (github.event.pull_request.merged == true && github.base_ref == 'Dev')
      runs-on: ubuntu-latest
      steps:
        - uses: actions/checkout@v4.1.1
          with:
            fetch-depth: 1
        - name: Docker Build
          id: CloudFoundry-dokcer-build
          env:
            file: ./CloudFoundry/Dockerfile
            Repository_name: rdgcfs41
            cf_endpoint: https://api.cf.us10.hana.ondemand.com
          run: |
            docker build -f $file -t $Repository_name . --build-arg ENDPOINT=$cf_endpoint --build-arg USR=${{secrets.CF_USERNAME}} --build-arg PASS=${{secrets.CF_PASSWORD}} --build-arg ORG="Data-Migration-Resources-Inc._rdgs41-q7acg6s4" --build-arg SPACE=RDG --no-cache


      # name: Test AWS ECR CF
      # runs-on: ubuntu-latest
      # steps:
      #   - name: Checkout repo
      #     uses: actions/checkout@v3 

      #   - name: Configure AWS credentials
      #     uses: aws-actions/configure-aws-credentials@v4
      #     with:
      #       aws-region: ${{ env.AWS_REGION }}
      #       aws-access-key-id: ${{ secrets.AWS_SECRET_ACCESS_KEY_ID }}
      #       aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      #   - name: Login to AWS ECR
      #     uses: aws-actions/amazon-ecr-login@v2
          
      #   - name: Build, tag, and push image to Amazon ECR
      #     id: build-image
      #     env:
      #       Repository_name: rdgs41
      #       IMAGE_TAG: latest
      #       file: ./CloudFoundry/Dockerfile
      #       ECR_registry: 475180123368.dkr.ecr.us-east-2.amazonaws.com
      #     run: |
      #       docker build -f $file -t $Repository_name . --build-arg ENDPOINT=${{ env.BTP_CF_ENDPOINT }} --build-arg USR=${{secrets.CF_USERNAME}} --build-arg PASS=${{secrets.CF_PASSWORD}} --build-arg ORG="Data Migration Resources Inc._rdg-lab-g4ygl9bi" --build-arg SPACE=RDGLAB --no-cache
      #       docker tag $Repository_name:$IMAGE_TAG $ECR_registry/$Repository_name:$IMAGE_TAG
      #       docker push $ECR_registry/$Repository_name:$IMAGE_TAG

# name: Deploy to DEV Neo
# on:
#   pull_request:
#     types: 
#       - closed
# jobs:
#     BTP-NEO-Deploy:
#       if: (github.event.pull_request.merged == true && github.base_ref == 'Dev')
#       runs-on: ubuntu-latest
#       steps:
#         - name: Docker Hub Autentication
#           uses: docker/login-action@v3
#           with:
#             username: ${{secrets.DOCKERHUB_USER}}
#             password: ${{secrets.DOCKERHUB_PWD}}
#         - uses: actions/checkout@v4.1.1
#           with:
#             fetch-depth: 1
#         - name: Docker images for Neo
#           uses: docker/build-push-action@v5
#           with:
#             context: ./
#             file: ./CloudNeo/Dockerfile
#             push: true
#             labels: neos41
#             tags: | 
#               thiagobattaglinsyniti/rdgneos41:v1
#               thiagobattaglinsyniti/rdgneos41:latest
#             build-args: | 
#               HOST=https://us3.hana.ondemand.com
#               USR=${{secrets.NEO_USERNAME}}
#               PASS=${{secrets.NEO_PASSWORD}}
#               SUBACCOUNT=${{secrets.S41_SUBACCOUNT}}

  # BTP-CF-Deploy-Using-Docker:
  #   runs-on: ubuntu-latest
  #   needs: [BTP-NEO-Deploy]
  #   steps:
  #     - uses: actions/checkout@v4.1.1
  #       with:
  #         fetch-depth: 1
  #     - name: Docker Hub Autentication
  #       uses: docker/login-action@v2.1.0
  #       with:
  #         username: ${{secrets.DOCKERHUB_USER}}
  #         password: ${{secrets.DOCKERHUB_PWD}}
  #     - name: Docker images test
  #       uses: docker/build-push-action@v3.2.0
  #       with:
  #         context: ./
  #         file: ./CloudFoundry/Dockerfile
  #         push: true
  #         labels: concentordg
  #         tags: | 
  #           thiagobattaglinsyniti/rdgs41:v1
  #           thiagobattaglinsyniti/rdgs41:latest
  #         build-args: | 
  #           ENDPOINT=https://api.cf.us10.hana.ondemand.com
  #           USR=${{secrets.CF_USERNAME}}
  #           PASS=${{secrets.CF_PASSWORD}}
  #           ORG=Data-Migration-Resources-Inc._rdgs41-q7acg6s4
  #           SPACE=RDG
