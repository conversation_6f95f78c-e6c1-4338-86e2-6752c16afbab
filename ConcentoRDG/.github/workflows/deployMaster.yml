name: Deploy to Master
on:
  pull_request:
    types:
      - closed
env:
  NEO_ENDPOINT: https://us3.hana.ondemand.com
jobs:
  BTP-NEO-Deploy-MG1:
    if: (github.event.pull_request.merged == true && github.base_ref == 'master')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.1.1
      - name: Docker Build
        id: CloudNeo-dokcer-build
        env:
          file: ./CloudNeo/Dockerfile
          Repository_name: rdgneomg1
        run: |
          docker build -f $file -t $Repository_name . --build-arg HOST=$NEO_ENDPOINT --build-arg USR=${{secrets.PROD_NEO_USERNAME}} --build-arg PASS=${{secrets.PROD_NEO_PASSWORD}} --build-arg SUBACCOUNT=${{secrets.MG1_SUBACCOUNT}}  
  BTP-NEO-Deploy-S42:
    needs: [BTP-NEO-Deploy-MG1]
    if: (github.event.pull_request.merged == true && github.base_ref == 'master')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.1.1
      - name: Docker Build
        id: CloudNeo-dokcer-build
        env:
          file: ./CloudNeo/Dockerfile
          Repository_name: rdgneos42
        run: |
          docker build -f $file -t $Repository_name . --build-arg HOST=$NEO_ENDPOINT --build-arg USR=${{secrets.PROD_NEO_USERNAME}} --build-arg PASS=${{secrets.PROD_NEO_PASSWORD}} --build-arg SUBACCOUNT=${{secrets.S42_SUBACCOUNT}}
  BTP-CF-Deploy-S42:
    needs: [BTP-NEO-Deploy-MG1]
    if: (github.event.pull_request.merged == true && github.base_ref == 'master')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.1.1
      - name: Docker Build
        id: CloudFoundry-dokcer-build
        env:
          file: ./CloudFoundry/Dockerfile
          Repository_name: rdgcfs42
          cf_endpoint: https://api.cf.us10.hana.ondemand.com
        run: |
          docker build -f $file -t $Repository_name . --build-arg ENDPOINT=$cf_endpoint --build-arg USR=${{secrets.CF_USERNAME}} --build-arg PASS=${{secrets.CF_PASSWORD}} --build-arg ORG="Data Migration Resources Inc._s42-5ycndk4p" --build-arg SPACE=RDG --no-cache