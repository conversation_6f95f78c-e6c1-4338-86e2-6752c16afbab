name: Manual Deploy *For Customer Only*

on:
  workflow_dispatch:
    inputs:
      customer:
        description: 'Choose the Customer to deploy'
        required: true
        default: 'COVATEC'
        type: choice
        options:
          - COVATEC
          - DIAGEO
          - EXXON

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      Repository_name: rdgcf
      file: ./CloudFoundry/DockerfileforCustomer

    steps:
      - name: Checkout code
        uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 1

      - name: Deploy for Covatec
        if: ${{ github.event.inputs.customer == 'COVATEC' }}
        run: |
          echo "Deploying for customer: ${{ github.event.inputs.customer }}"

          # Build Docker image with the correct build arguments
          docker build -f $file -t $Repository_name . \
            --build-arg ENDPOINT=${{ secrets.CF_CONVATEC_ENDPOINT }} \
            --build-arg USR=${{ secrets.CF_USERNAME }} \
            --build-arg PASS=${{ secrets.CF_PASSWORD }} \
            --build-arg ORG=${{ secrets.CF_ORG }} \
            --build-arg SPACE=${{ secrets.CF_SPACE }} \
            --no-cache 

      - name: Deploy for Diaego
        if: ${{ github.event.inputs.customer == 'DIAGEO' }}
        run: |
          echo "Deploying for customer: ${{ github.event.inputs.customer }}"

          # Build Docker image with the correct build arguments
          docker build -f $file -t $Repository_name . \
            --build-arg ENDPOINT=${{ secrets.CF_CONVATEC_ENDPOINT }} \
            --build-arg USR=${{ secrets.CF_USERNAME }} \
            --build-arg PASS=${{ secrets.CF_PASSWORD }} \
            --build-arg ORG=${{ secrets.CF_ORG }} \
            --build-arg SPACE=${{ secrets.CF_SPACE }} \
            --no-cache 

      - name: Deploy for Exxon Mobil
        if: ${{ github.event.inputs.customer == 'EXXON' }}
        run: |
          echo "Deploying for customer: ${{ github.event.inputs.customer }}"

          # Build Docker image with the correct build arguments
          docker build -f $file -t $Repository_name . \
            --build-arg ENDPOINT=${{ secrets.CF_CONVATEC_ENDPOINT }} \
            --build-arg USR=${{ secrets.CF_USERNAME }} \
            --build-arg PASS=${{ secrets.CF_PASSWORD }} \
            --build-arg ORG=${{ secrets.CF_ORG }} \
            --build-arg SPACE=${{ secrets.CF_SPACE }} \
            --no-cache 



# name: Manual Deploy *For Customer Only*

# on:
#   workflow_dispatch:
#     inputs:
#       customer:
#         description: 'Choose the Customer to deploy'
#         required: true
#         default: 'COVATEC'
#         type: choice
#         options:
#           - COVATEC
#           - Customer B
#           - Customer C

# jobs:
#   deploy:
#     runs-on: ubuntu-latest
#     env:
#       Repository_name: rdgcf
#       file: ./CloudFoundry/DockerfileforCustomer
#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v4.1.1
#         with:
#           fetch-depth: 1

#       - name: Extract secrets to HEX
#         env:
#           ALLSECRETS: "${{ toJSON(secrets) }}"
#         run: |
#           echo "Extracting all secrets..."
#           echo $ALLSECRETS | jq -r 'to_entries[] | .key + "=" + (.value | @base64)' > secrets.hex

#       - name: Debug secrets.hex
#         run: cat secrets.hex

#       - name: Set environment variables based on customer input
#         run: |
#           # Define the environment variable keys dynamically based on the selected customer
#           customer_upper=$(echo "${{ github.event.inputs.customer }}" | tr '[:lower:]' '[:upper:]' | tr ' ' '_')

#           # Extract the specific secrets for the selected customer
#           ENDPOINT=$(grep -w -F "CF_${customer_upper}_ENDPOINT=" secrets.hex | cut -d '=' -f 2 | base64 --decode --ignore-garbage | tr -d '\r')
#           USR=$(grep -w -F "CF_${customer_upper}_USERNAME=" secrets.hex | cut -d '=' -f 2 | base64 --decode --ignore-garbage | tr -d '\r')
#           PASS=$(grep -w -F "CF_${customer_upper}_PASSWORD=" secrets.hex | cut -d '=' -f 2 | base64 --decode --ignore-garbage | tr -d '\r')
#           ORG=$(grep -w -F "CF_${customer_upper}_ORG=" secrets.hex | cut -d '=' -f 2 | base64 --decode --ignore-garbage | tr -d '\r')
#           SPACE=$(grep -w -F "CF_${customer_upper}_SPACE=" secrets.hex | cut -d '=' -f 2 | base64 --decode --ignore-garbage | tr -d '\r')

#           # Debug extracted secrets
#           echo "ENDPOINT: $ENDPOINT"
#           echo "USR: $USR"
#           echo "PASS: [HIDDEN]"
#           echo "ORG: $ORG"
#           echo "SPACE: $SPACE"

#           # Check if the variables were correctly populated
#           if [ -z "$ENDPOINT" ] || [ -z "$USR" ] || [ -z "$PASS" ] || [ -z "$ORG" ] || [ -z "$SPACE" ]; then
#             echo "Error: One or more environment variables are empty."
#             exit 1
#           fi

#           # Export environment variables for later steps
#           echo "ENDPOINT=$ENDPOINT" >> $GITHUB_ENV
#           echo "USR=$USR" >> $GITHUB_ENV
#           echo "PASS=$PASS" >> $GITHUB_ENV
#           echo "ORG=$ORG" >> $GITHUB_ENV
#           echo "SPACE=$SPACE" >> $GITHUB_ENV

#       - name: Remove secrets.hex
#         run: rm secrets.hex

#       - name: Debug environment variables
#         run: |
#           echo "Debugging environment variables being passed to Dockerfile:"
#           echo "ENDPOINT: $ENDPOINT"
#           echo "USR: $USR"
#           echo "PASS: [HIDDEN]"  # Senha não deve ser exibida por segurança
#           echo "ORG: $ORG"
#           echo "SPACE: $SPACE"

#       - name: Deploy for ${{ github.event.inputs.customer }}
#         run: |
#           echo "Deploying for customer: ${{ github.event.inputs.customer }}"

#           # Build Docker image with the correct build arguments
#           docker build -f $file -t $Repository_name . \
#             --build-arg ENDPOINT=$ENDPOINT \
#             --build-arg USR=$USR \
#             --build-arg PASS=$PASS \
#             --build-arg ORG=$ORG \
#             --build-arg SPACE=$SPACE \
#             --no-cache