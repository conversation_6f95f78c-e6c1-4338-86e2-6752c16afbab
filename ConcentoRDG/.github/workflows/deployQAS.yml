name: Deploy to Test
on:
  pull_request:
    types:
      - closed
jobs:
  BTP-NEO-Deploy-T42:
    if: (github.event.pull_request.merged == true && github.base_ref == 'QAS')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 1
      - name: Docker Build
        id: CloudNeo-dokcer-build
        env:
          file: ./CloudNeo/Dockerfile
          Repository_name: rdgneot42
          neo_endpoint: https://us3.hana.ondemand.com
        run: |
          docker build -f $file -t $Repository_name . --build-arg HOST=$neo_endpoint --build-arg USR=${{secrets.NEO_USERNAME}} --build-arg PASS=${{secrets.NEO_PASSWORD}} --build-arg SUBACCOUNT=${{secrets.T42_SUBACCOUNT}}
  BTP-CF-Deploy-T42:
    if: (github.event.pull_request.merged == true && github.base_ref == 'QAS')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 1
      - name: Docker Build
        id: CloudFoundry-dokcer-build
        env:
          file: ./CloudFoundry/Dockerfile
          Repository_name: rdgcft42
          cf_endpoint: https://api.cf.us10.hana.ondemand.com
        run: |
          docker build -f $file -t $Repository_name . --build-arg ENDPOINT=$cf_endpoint --build-arg USR=${{secrets.CF_USERNAME}} --build-arg PASS=${{secrets.CF_PASSWORD}} --build-arg ORG="Data Migration Resources Inc._rdgt42-j8y2x95q" --build-arg SPACE=RDGT42 --no-cache