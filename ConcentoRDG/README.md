RDG for VsCode
===

Description
---

We are using [SAPUI5](https://sapui5.hana.ondemand.com/) framework to develop, [SAP BTP](https://account.hana.ondemand.com/) 
and [Cloud Foundry](https://www.cloudfoundry.org) to deploy, so knowledge of Javascript, UI5, BTP and CF is assumed.

Changes that need to be made before running locally
---

Create a file named 'userInfo.json' under the 'webapp' folder with the following content:
```javascript
{
  "firstname": "yourFirstName",
  "name": "yourSUser",
  "email": "<EMAIL>",
  "lastname": "yourLastName"
}
```

Process to Update Theme Files (ref: https://community.sap.com/t5/technology-blogs-by-sap/sap-tech-bytes-ui-theme-designer-applying-custom-theme-to-ui5-application/ba-p/********)
---
1. Open Theme editor  https://themedesigner-h3dea9a6e.dispatcher.us3.hana.ondemand.com/index.html
2. Edit the theme as necessary 
3. Save Theme
4. Export Theme -> Optional Settings(for Experts) -> (Source files + CSS Resources + Base Theme Resources) + Uncheck UR (Unified rendering)
5. Save and Extract the export zip file
6. Delete the UI5 folder from themev2 and replace with the downloaded UI5 folder 
7. Delete the Base folder from themev2 and replace with the downloaded UI5 folder 

Prerequisites
---

You will need the following installed on your machine:
1. Git client installed
2. NodeJS 16+ installed
3. CloudFoundry CLI installed
4. Git credentials are configured to the Syniti Azure Devops organization
5. Credentials for SAP BTP CloudFoundry

[Need Help? First Time Setup Guide here](https://concento.visualstudio.com/RDG/_wiki/wikis/Zeus.wiki/76/0.-Install-programs-and-tools)


Running the app locally:
---
```bash
$ git clone https://github.com/BackOfficeAssoc/ConcentoRDG.git
Cloning into 'ConcentoRDG'...
```

```bash
$ cd ConcentoRDG
$ git checkout -b <prefix/azure task id/localBranchName>
```

```bash
$ npm install
$ npm run start
```

The last command will automatically open your Browser in the project's main page.

Testing the app
---

Access http://localhost:8080 or http://localhost:8080/index.html in your Chrome browser to use the application.

**Local branch prefix code**
---

:warning: **NEVER FORGET TO CREATE A BRANCH BEFORE STARTING DEVELOPMENT**: Be very careful here!

|prefixName| prefixCode | Description|
|-----|-----|--------------------|
|Feature|feature|is for adding, refactoring or removing a feature|
|Bugfix|bugfix|is for fixing a bug|
|Hotfix|hotfix| is for changing code with a temporary solution and/or without following the usual process (usually because of an emergency)|

<!-- |Fix | fix |bugs resolution, code refactor, documentation, tests|
|Feature| feature |non-breaking change which adds functionality|
|Breaking change| brkchange |discontinued features or fix that would cause existing functionality to not work as expected|
|Environment promotion| envpromo |merge request to deploy new feature to quality, preprod or master branches| -->

Examples:
```bash
$ git branch feature/4321/create-new-button-component
$ git branch bugfix/1234/error-datamodel-when-save
$ git branch hotfix/no-ref/DataModel-not-working
```

:warning: **NEVER FORGET TO CREATE A BRANCH BEFORE STARTING DEVELOPMENT**: Be very careful here!

**Commits**
---

[![Board Status](https://img.shields.io/badge/Jira-Board-blue)](https://entota.atlassian.net/jira/software/c/projects/RDG/boards/237)

With the implementation of the Azure Devops Task [12599](https://concento.visualstudio.com/RDG/_workitems/edit/12599) that links GitHub to Azure Devops, it is now necessary to place AB#<TaskId> in all commits descriptions.

:warning: **NEVER FORGET ADD THE AZURE DEVOPS TASK ID**: Be very careful here!

Example
```bash
$ git commit -m "AB#12599 Link ADO Work Items with Github Pull Requests"
```

Syniti SAP Environment
---

|MDG Backend|Deploy Branch| Technical Name |RDG Neo Link|CF Link|Purpose|IP|SID|
|-----|-----|---|---|---|------|-------|-------|
|S41|Dev|h3dea9a6e|https://supernovafj-h3dea9a6e.dispatcher.us3.hana.ondemand.com|https://rdgs41-q7acg6s4.launchpad.cfapps.us10.hana.ondemand.com/8e20c535-329f-47f3-9f4a-f1bd550fdc72.ConcentoRDG.ConcentoRDGMTA-1.18.0/index.html|Development|**************|S41|
|~~SD2~~|~~StagingDev~~|~~mvv8hif3cb~~|~~https://supernovafj-mvv8hif3cb.dispatcher.us3.hana.ondemand.com~~| |~~ECC Testing~~|~~************~~|~~SD2~~|
|T42|StagingDev|g5olchcx3v|https://supernovafj-g5olchcx3v.dispatcher.us3.hana.ondemand.com/index.html|https://rdgt42-j8y2x95q.launchpad.cfapps.us10.hana.ondemand.com/ac607f11-71af-44fc-be0f-d13256075842.ConcentoRDG.ConcentoRDGMTA-1.18.0/index.html|S4 Testing|ec2-54-161-147-160.compute-1.amazonaws.com|T42|
|MG1|Master|i1zxfy5p4r|https://supernovafj-i1zxfy5p4r.dispatcher.us3.hana.ondemand.com||Training|MG1CLNT300|
|S42|Master|d3a8zyhewk|https://supernovafj-d3a8zyhewk.dispatcher.us3.hana.ondemand.com|https://s42-5ycndk4p.launchpad.cfapps.us10.hana.ondemand.com/7fbb22a0-8c1f-402c-a70b-0206510a5260.ConcentoRDG.ConcentoRDGMTA-1.18.0/index.html	|Demo|**************|S42|
