{"welcomeFile": "/index.html", "logoutPage": "/logout.html", "routes": [{"path": "/resources", "target": {"type": "service", "name": "sapui5", "preferLocal": true, "entryPath": "/resources", "version": "1.134.1"}, "description": "SAPUI5 Resources"}, {"path": "services/userapi", "target": {"type": "service", "name": "userapi"}}, {"path": "/test-resources", "target": {"type": "service", "name": "sapui5", "entryPath": "/test-resources", "version": "1.134.1"}, "description": "SAPUI5 Resources"}, {"path": "/webapp/resources", "target": {"type": "service", "name": "sapui5", "preferLocal": true, "entryPath": "/resources", "version": "1.134.1"}, "description": "SAPUI5 Resources"}, {"path": "/webapp/test-resources", "target": {"type": "service", "name": "sapui5", "entryPath": "/test-resources", "version": "1.134.1"}, "description": "SAPUI5 Test Resources"}, {"path": "/sap/opu/odata", "target": {"type": "destination", "name": "Supernova", "entryPath": "/sap/opu/odata"}, "description": "Supernova oData"}], "sendWelcomeFileRedirect": true, "headerWhiteList": ["sap-transportrequest"]}