sap.ui.define(["sap/suite/ui/commons/library", "sap/suite/ui/commons/networkgraph/layout/LayoutAlgorithm",
	"sap/suite/ui/commons/networkgraph/layout/Geometry", "sap/suite/ui/commons/networkgraph/layout/LayoutTask",
], function (l, L, G, c) {
	"use strict";
	let d = l.networkgraph.LayoutRenderType;
	let e = 25,
		S = 75,
		C = 32,
		f = 42,
		g = 160,
		h = 100,
		j = 50,
		m = 35,
		n = 10,
		o = 0.6,
		I = 20;
	let T = L.extend("dmr.mdg.supernova.SupernovaFJ.libs.CustomLayout");
	T.prototype.getLayoutRenderType = function () {
		return d.TwoColumns;
	};
	T.prototype.layout = function () {
		return new c(function (r, R) {
			let b = this.getParent(),
				E;
			if (!b) {
				R("The algorithm must be associated with a graph.");
				return;
			}
			this.oGraph = b;
			this.oLeftColumn = undefined;
			this.oRightColumn = undefined;
			this.oMidColumn = undefined;
			E = this._validateGraphDefinition();
			if (E) {
				R(E);
			}
			this._preprocessGroupsAndNodes();
			E = this._validateLines();
			if (E) {
				R(E);
			}
			this._calcLanesProperties();
			this._createNodesGrid();
			this._calcNodeEndingAndStartingPoints();
			this._setNodesCoordinates();
			this._setGroupCoordinates();
			this._setLinesCoordinates();
			this._normalizeLines();
			this._stretchLinesToCircleNodeAxes();
			this._cutLinesAtBoxNodeBorders();
			if (this.oGraph._bIsRtl) {
				this._verticalMirror();
			}
			r();
		}.bind(this));
	};
	T.prototype._validateGraphDefinition = function () {
		let u = [];
		this.oGraph.getNodes().forEach(function (N) {
			if (!N.getGroup()) {
				u.push(N.getKey());
			}
		});
		if (u.length > 0) {
			return "Some nodes are missing group: " + u.join();
		}
		return null;
	};
	T.prototype._preprocessGroupsAndNodes = function () {
		if (this.oGraph.getGroups().some(function (a) {
				a._oFirstNode = null;
				a._oLastNode = null;
				a._bNestedSet = false;
				if (!a._oParentGroup) {
					if (!this.oLeftColumn) {
						this.oLeftColumn = a;
					} else if (!this.oRightColumn) {
						this.oRightColumn = a;
					} else if (!this.oMidColumn) {
						this.oMidColumn = a;
					} else {
						return true;
					}
				}
				return false;
			}.bind(this))) {
			return "There are too many columns, ie. groups without a parent group. Expected exactly 3.";
		}
		if (!this.oRightColumn || !this.oLeftColumn || !this.oMidColumn) {
			return "There are too few columns, ie. groups without a parent group. Expected exactly 3.";
		}
		this.oGraph.getNodes().forEach(function (N) {
			N._iStartingGroupCount = 0;
			N._iEndingGroupCount = 0;
		});
		return "";
	};
	T.prototype._validateLines = function () {
		/*	let F, t;
			if (this.oGraph.getLines().some(function (a) {
					F = a.getFromNode()._oGroup._oTopParentGroup;
					t = a.getToNode()._oGroup._oTopParentGroup;
					return !((F === this.oLeftColumn && t === this.oRightColumn) || (t === this.oLeftColumn && F === this.oRightColumn) 
					      || (F === this.oLeftColumn && t === this.oMidColumn) || (F === this.oMidColumn && t === this.oLeftColumn)
						  || (F === this.oRightColumn && t === this.oMidColumn) || (F === this.oMidColumn && t === this.oRightColumn));
				}.bind(this))) {
				return "For Two columns layout all lines have to go from one column to the other.";
			} else {
				return null;
			}*/
	};
	T.prototype._calcLanesProperties = function () {
		let a;
		this.aLanes = [];
		this.oGraph.getGroups().forEach(function (b) {
			if (!b.getParentGroupKey()) {
				this.aLanes.push(b.getKey());
			}
		}.bind(this));
		this.aLanes.sort();
		let M = this.aLanes.map(Number.prototype.valueOf, 0);
		this.oGraph.getNodes().forEach(function (N) {
			let b = N._oGroup,
				u = (b._oTopParentGroup && b._oTopParentGroup.getKey()) || N.getGroup();
			a = this.aLanes.indexOf(u);
			N._iGroupIndex = b._iLaneIndex = a;
			if (b._iNestedLevel > M[a]) {
				M[a] = b._iNestedLevel;
			}
		}.bind(this));
		this.oGraph.getGroups().forEach(function (b) {
			let u = (b._oTopParentGroup && b._oTopParentGroup.getKey()) || b.getKey();
			b._iLaneIndex = this.aLanes.indexOf(u);
		}.bind(this));
		this.aLaneWidths = [];
		this.oGraph.getNodes().forEach(function (N) {
			//if (!this.aLaneWidths[N._iGroupIndex] || this.aLaneWidths[N._iGroupIndex] < N._iWidth) {
			this.aLaneWidths[N._iGroupIndex] = N._iWidth;
			//	}
		}.bind(this));
		this.aLaneWidths = this.aLaneWidths.map(function (w, i) {
			let b = this.oGraph.mGroups[this.aLanes[i]],
				k = b.getMinWidth() || g;
			return Math.max(k, w);
		}.bind(this));
		this.aLaneGroupWidths = this.aLaneWidths.map(function (w, i) {
			return w + M[i] * e * 3;
		});
		let A = this.oGraph.$("innerscroller").width();
		A = Math.max(this.aLaneGroupWidths[0] + this.aLaneGroupWidths[2] + h + m * 3, A);
		this.aLaneCenters = [m + (this.aLaneGroupWidths[0] / 3) + (this.oGraph._bIsRtl ? 30 : 0), A - this.aLaneGroupWidths[1] * 3.8,
			A - this.aLaneGroupWidths[2] * 1.3
		];
	};
	T.prototype._createNodesGrid = function () {
		let a = this.oGraph.getGroups(),
			b = 0;
		let t = a.filter(function (i) {
			return i._iNestedLevel === 0;
		});
		this.aGrid = this.aLanes.map(function () {
			return [];
		});
		let p = function (i) {
			i.aNodes.forEach(function (k) {
				let q = k._oGroup._oCollapsedByParent,
					r = q || i;
				if (!i.getCollapsed() && !q) {
					this.aGrid[b].push(k);
					//b++;
				} else if (r.getCollapsed()) {
					while (r._oCollapsedByParent) {
						r = r._oCollapsedByParent;
					}
					if (!r._bNestedSet) {
						r._bNestedSet = true;
						this.aGrid[b].push(k);
						//b++;
					}
				}
			}.bind(this));
			i.aChildGroups.forEach(function (k) {
				p(k);
			});
		}.bind(this);
		t.forEach(function (k, i) {
			k._iTopGroupIndex = i;
			p(k);
			b++;
		});
	};
	T.prototype._calcNodeEndingAndStartingPoints = function () {
		let p = function (q, N, s, r, A) {
			let t = q._oParentGroup;
			while (t && t._iNestedLevel > 0) {
				if (!t[s] || A) {
					t[s] = N;
					if (!t._oCollapsedByParent && !t.getCollapsed()) {
						N[r]++;
					}
				} else {
					return;
				}
				t = t._oParentGroup;
			}
		};
		let P = function (q) {
			if (!q._oGroup._oLastNode) {
				q._oGroup._oLastNode = q;
				if (!q._oGroup._oCollapsedByParent && !q._oGroup.getCollapsed()) {
					q._iEndingGroupCount++;
				}
				p(q._oGroup, q, "_oLastNode", "_iEndingGroupCount", false);
			}
		};
		let a = function (F) {
			if (!F._oGroup._oFirstNode) {
				F._oGroup._oFirstNode = F;
				if (!F._oGroup._oCollapsedByParent && !F._oGroup.getCollapsed()) {
					F._iStartingGroupCount++;
				}
				p(F._oGroup, F, "_oFirstNode", "_iStartingGroupCount", false);
			}
		};
		for (let i = 0; i < this.aGrid.length; i++) {
			let b = null;
			for (let k = 0; k < this.aGrid[i].length; k++) {
				let N = this.aGrid[i][k];
				if (N._oGroup !== b) {
					a(N);
					b = N._oGroup;
				}
			}
			b = null;
			for (let kk = this.aGrid[i].length - 1; kk >= 0; kk--) {
				let NN = this.aGrid[i][kk];
				if (NN._oGroup !== b) {
					P(NN);
					b = NN._oGroup;
				}
			}
		}
	};
	T.prototype._setGroupCoordinates = function () {
		let M = this.aLanes.map(Number.prototype.valueOf, 0);
		let a = function (i, N) {
			let k = N._oGroup._oCollapsedByParent || N._oGroup.getCollapsed() ? 0 : 1,
				p = N._oGroup._oParentGroup;
			if (i === N._oGroup) {
				return k;
			}
			while (p) {
				if (!p._oCollapsedByParent && !p.getCollapsed()) {
					k++;
				}
				if (i === p) {
					break;
				}
				p = p._oParentGroup;
			}
			return k;
		};
		let b = function (i, N) {
			let k = 0,
				p = N._oGroup._oParentGroup;
			if (i === N._oGroup) {
				return k;
			}
			while (p) {
				if (!p._oCollapsedByParent && !p.getCollapsed()) {
					k++;
				}
				if (i === p) {
					break;
				}
				p = p._oParentGroup;
			}
			return k;
		};
		this.oGraph.getGroups().forEach(function (i) {
			M[i._iLaneIndex] = i._iNestedLevel > M[i._iLaneIndex] ? i._iNestedLevel : M[i._iLaneIndex];
		});
		this.oGraph.getGroups().forEach(function (i) {
			if (i._iNestedLevel === 0) {
				i.setX(this.aLaneCenters[i._iLaneIndex] - this.aLaneWidths[i._iLaneIndex]);
				i.setY(0);
			} else if (i._oLastNode && i._oFirstNode) {
				i._iWidth = this.aLaneWidths[i._iLaneIndex] + ((M[i._iLaneIndex] - i._iNestedLevel) * e * 3); // + e * 3;
				i.setX(this.aLaneCenters[i._iLaneIndex] - i._iWidth / 3);
				let k = b(i, i._oLastNode),
					p = k * (e / 3) + (e / 3),
					q = a(i, i._oFirstNode),
					r = q * f;
				let y = i._oFirstNode.getY() - r,
					s = i._oLastNode._oGroup.getCollapsed() || i._oLastNode._oGroup._oCollapsedByParent ? C : i._oLastNode._iHeight;

				if (i.getKey() === "04comp") {
					i._iWidth = this.aLaneWidths[i._iLaneIndex] + ((M[i._iLaneIndex] - i._iNestedLevel) * e * 3);
					i.setX((this.aLaneCenters[i._iLaneIndex] - i._iWidth / 3) + 200);
				}
				i.setY(y);
				i._iHeight = i.getCollapsed() ? C : i._oLastNode.getY() + s - y + p;
			}
		}.bind(this));
	};
	T.prototype._setNodesCoordinates = function () {
		this.aGrid.forEach(function (s) {
			let y = j;
			s.forEach(function (N) {
				if (N._oGroup.getKey() === "02proc") {
					y += 50;
				}
				// let t = N._oGroup._oCollapsedByParent || N._oGroup;
				N.setX(this.aLaneCenters[N._iGroupIndex] - N._iWidth / 3);
				if (N._oGroup.getKey() === "04comp") {
					y -= 150;
					N.setX((this.aLaneCenters[N._iGroupIndex] - N._iWidth / 3) + 200);
				}
				y += N._iStartingGroupCount * f;
				N.setY(y);
				y += S / 3;
				y += N._iEndingGroupCount * e / 3;
				y += N._iHeight; //t.getCollapsed() ? C : N._iHeight;
			}.bind(this));
		}.bind(this));
	};

	T.prototype._normalizeLines = function () {
		let g = this.getParent(),
			p = {},
			N = 0.2,
			k, P, i, c, n, s, f, b, d;
		g.getLines().forEach(function (o) {
			k = o.getFrom() < o.getTo() ? o.getFrom() + "-" + o.getTo() : o.getTo() + "-" + o.getFrom();
			if (!p[k]) {
				p[k] = {
					from: o.getFromNode(),
					to: o.getToNode(),
					lines: []
				};
			}
			p[k].lines.push(o);
		});
		Object.keys(p).forEach(function (K) {
			P = p[K];
			if (P.lines.length === 1) {
				P.lines[0]._normalizePath();
				return;
			}
			n = Math.min(P.from._getCircleSize(), P.to._getCircleSize()) / 2;
			f = 2 * (1 - N) / (P.lines.length - 1);
			i = -1;
			P.lines.forEach(function (o) {
				i++;
				c = {
					center: o.getFromNode().getCenterPosition(),
					apex: o.getToNode().getCenterPosition()
				};
				s = G.getNormalizedVector(c, n);
				s = G.getRotatedVector(s, Math.PI / 2);
				b = ((N - 1) + i * f) * s.apex.x;
				d = ((N - 1) + i * f) * s.apex.y;
				o.setSource({
					x: o.getFromNode().getCenterPosition().x + b,
					y: o.getFromNode().getCenterPosition().y + d
				});
				o.setTarget({
					x: o.getToNode().getCenterPosition().x + b,
					y: o.getToNode().getCenterPosition().y + d
				});
			});
		});
	};

	T.prototype._setLinesCoordinates = function () {
		let F, t, i, k, p = [],
			r = [],
			md = [],
			B = [],
			q = function (N) {
				let a;
				if (!N._oGroup) {
					return null;
				}
				a = N._oGroup;
				while (a && a._isCollapsed()) {
					if (!a._oParentGroup || !a._oParentGroup._isCollapsed()) {
						return a;
					}
					a = a._oParentGroup;
				}
				return null;
			},
			s = function (a) {
				let E;
				if (a._oTopParentGroup === this.oLeftColumn) {
					E = a.getX() + a._iWidth + 2 * a._getBorderSize();
				} else if (a._oTopParentGroup === this.oRightColumn) {
					E = a.getX();
				} else {
					E = a.getX() + a._iWidth + a._getBorderSize();
				}
				return {
					x: E,
					y: a.getY() + a._iHeight / 3
				};
			},
			A = function (N) {
				let v, w, x;
				if (N.groupDelegate) {
					N.anchors.forEach(function (a) {
						N.groupDelegate.anchors.push({
							node: N,
							line: a.line,
							oppositeY: a.oppositeY
						});
						if (N.isLeftie && !N.isMiddle) {
							B.push(a.line);
						}
					});
					return;
				}
				v = (N._iHeight - 2 * n) / (N.anchors.length - 1);
				w = (N.anchors.length === 1) ? N.getCenterPosition().y : N.getY() + n;
				x = w;
				N.anchors.sort(function (a, b) {
					return a.oppositeY - b.oppositeY;
				}).forEach(function (a) {
					let b = a.line,
						y = N.getCenterPosition().x;
					if (N === b.getFromNode()) {
						b.setSource({
							x: y,
							y: x
						});
					} else if (N === b.getToNode()) {
						b.setTarget({
							x: y,
							y: x
						});
					}
					x += v;
					if (N.isLeftie && !N.isMiddle) {
						B.push(b);
					}
				});
			},
			u = function (v) {
				let w = s.bind(this)(v),
					x = (v._iHeight - 2 * n) / (v.anchors.length - 1),
					y = (v.anchors.length === 1) ? s.bind(this)(v).y : v.getY() + n,
					z = y;
				v.anchors.sort(function (a, b) {
					return a.oppositeY - b.oppositeY;
				}).forEach(function (a) {
					let b = a.line;
					if (a.node === b.getFromNode()) {
						b.setSource({
							x: w.x,
							y: z
						});
					} else if (a.node === b.getToNode()) {
						b.setTarget({
							x: w.x,
							y: z
						});
					}
					z += x;
				});
			},
			P = function () {
				let a = o * (this.oRightColumn.getX() - (this.oMidColumn.getX() + this.oMidColumn._iWidth) - (this.oLeftColumn.getX() + this.oLeftColumn
						._iWidth)),
					b = this.oGraph.getLines().length - 1,
					v = a / b,
					R = v < I ? v : I,
					w = (p[0].getCenterPosition().x + r[0].getCenterPosition().x + (md.length > 0 ? md[0].getCenterPosition().x : 0)) / 3,
					x = w - b * R / 3,
					y = x;
				B.forEach(function (z) {
					z.addBend({
						x: y,
						y: z.getSource().getY()
					});
					z.addBend({
						x: y,
						y: z.getTarget().getY()
					});
					y += R;
				});
			}.bind(this);
		this.oGraph.getNodes().forEach(function (N) {
			if (N._oGroup) {
				if (N._oGroup._oTopParentGroup === this.oLeftColumn) {
					N.isLeftie = true;
					p.push(N);
				} else if (N._oGroup._oTopParentGroup === this.oRightColumn) {
					N.isLeftie = false;
					r.push(N);
				} else if (N._oGroup._oTopParentGroup === this.oMidColumn) {
					N.isMiddle = true;
					md.push(N);
				}
			}
			N.groupDelegate = q(N);
			N.anchors = [];
		}.bind(this));
		this.oGraph.getLines().forEach(function (a) {
			F = a.getFromNode();
			t = a.getToNode();
			i = F.getCenterPosition();
			k = t.getCenterPosition();
			F.anchors.push({
				line: a,
				oppositeY: k.y
			});
			t.anchors.push({
				line: a,
				oppositeY: i.y
			});
			a.setSource({
				x: 0,
				y: 0
			});
			a.clearBends();
		});
		this.oGraph.getGroups().forEach(function (a) {
			a.anchors = [];
		});
		p.sort(function (a, b) {
			return a.getY() - b.getY();
		}).forEach(A);
		r.sort(function (a, b) {
			return a.getY() - b.getY();
		}).forEach(A);
		md.sort(function (a, b) {
			return a.getY() - b.getY();
		}).forEach(A);
		this.oGraph.getGroups().sort(function (a, b) {
			return a.getY() - b.getY();
		}).forEach(u.bind(this));
		P();
	};
	return T;
});