sap.ui.define([
	"sap/ui/base/Object",
	"sap/m/MessageBox",
	"sap/ui/model/json/JSONModel",
	"sap/ui/core/BusyIndicator",
	"sap/m/MessageToast",
	"sap/ui/util/XMLHelper",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities"
], function (Object, MessageBox, JSONModel, BusyIndicator, MessageToast, XMLHelper, Utilities) {
	"use strict";
	return Object.extend("dmr.mdg.supernova.SupernovaFJ.libs.DataService", {
		constructor: function (
			oController, // The controller object for the view to which this object belongs
			sServiceModelName, // The name of the service model defined in manifest
			sUrl, // Url to access 
			sTargetDataModelName, // The model name to which the respond data is to be assigned
			sDataPath, // Path of the data in the response
			sActionTitle // A generic text that will be used during error display
		) {
			// Check inputs 
			if ( //!oController ||
				sServiceModelName.length === 0 ||
				sUrl.length === 0
				// sTargetDataModelName.length === 0 ||
				// sDataPath.length === 0
			) {
				throw new Error("Input paratmeters invalid");
			}
			if (oController) {
				this.oDataModel = oController.getOwnerComponent().getModel(sServiceModelName);
				if (this.oDataModel) {
                    this.oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
                } else {
                    throw new Error("Service " + sServiceModelName + " not defined in the manifest.");
                }
			}
			this.oController = oController;
			this.sServiceModelName = sServiceModelName;
			this.sUrl = sUrl;
			this.sTargetDataModelName = sTargetDataModelName;
			this.sDataPath = sDataPath;
			this.sActionTitle = sActionTitle;
			this.bShowBusyIndicator = true;
			this.sToastMessage = null;
			//Array consists of all S4H services which has an ECC alternate service 
			this.arrEccDependency = ["DRF_MODEL_LIST"];
			this.arrVersionDependency = ["MDCONSOLIDATION"];
		},

		/**
		 * Invoke the GET method on the URL and retrieve the information
		 * @param {object} [oCallbacks] : Optional object containing the following parameters 
		 *      success: Object with the following data 
		 *           fCallback: Callback function to be invoked on success. 
		 *				Takes two params
		 *					1. oParam received as part of the success object
		 *					2. Service Response Data
		 *           oParam: Object / array to be passed to the callback
		 *      error: Object with the following data
		 *           fCallback: Callback function to be invoked on error 
		 *				Takes two params
		 *					1. oParam received as part of the error object
		 *					2. Service Error Data
		 *           oParam: Object / array to be passed on the callback
		 * @param {object} [oUrlParameters] : Option data map with the url parameters
		 */
		getData: function (oCallbacks, oUrlParameters) {
			let oThisObject = this;
			let oCurrentSystemPromise = oThisObject.oController.getCurrentSystem();
			oCurrentSystemPromise.then(function(sSystem) {
				if(sSystem.IS_S4H === "ECC" && oThisObject.arrEccDependency.includes(oThisObject.sServiceModelName)) {
					oThisObject.sServiceModelName = oThisObject.sServiceModelName + "_ECC";
					oThisObject.oDataModel = oThisObject.oController.getOwnerComponent().getModel(oThisObject.sServiceModelName);
					if (oThisObject.oDataModel) {
						oThisObject.oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
					} else {
						throw new Error("Service " + oThisObject.sServiceModelName + " not defined in the manifest.");
					}
				} else if(!sSystem.Version && oThisObject.arrVersionDependency.includes(oThisObject.sServiceModelName)) {
					oThisObject.sServiceModelName = oThisObject.sServiceModelName + "_ECC";
					oThisObject.oDataModel = oThisObject.oController.getOwnerComponent().getModel(oThisObject.sServiceModelName);
					if (oThisObject.oDataModel) {
						oThisObject.oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
					} else {
						throw new Error("Service " + oThisObject.sServiceModelName + " not defined in the manifest.");
					}
				}
				let sURL = oThisObject.sUrl;
				let promiseShowPopupAlert;
				let oGetParameterObject = {
					method: "GET",
					success: function (oData) {
	
						// Invoke the success callback
						oThisObject._invokeGetSuccess(oThisObject, oCallbacks, oData);
						
						if (oThisObject.bShowBusyIndicator === true ) {
							BusyIndicator.hide();
						}
	
						// Show a toast message 
						if ( oThisObject.sToastMessage ) {
							promiseShowPopupAlert =
							Utilities.showPopupAlert(oThisObject.sToastMessage, MessageBox.Icon.INFORMATION, "");
							promiseShowPopupAlert.then(function () {
							});
						}	
					},
					error: function (oError) {
	
						// Invoke the error callback before performing any other action
						oThisObject._invokeError(oThisObject, oCallbacks, oError);
						if (oThisObject.bShowBusyIndicator === true ) {
							BusyIndicator.hide();
						}
					}
				};
	
				// If URL Parameters were provided add them to the parameter object 
				if (oUrlParameters) {
					oGetParameterObject.urlParameters = oUrlParameters;
				}
				
				if (oThisObject.bShowBusyIndicator === true ) {
					BusyIndicator.show(300);
				}
				oThisObject.oDataModel.read(sURL, oGetParameterObject);
					
			});
		},

		/**
		 * Invoke the PUT or UPDATE method on the URL and store or update the information. 
		 * 
		 * @param {boolean} bUpdate: Set to true if the request is an update, false if NEW
		 * @param {object} oData: Object to be written to the service
		 * @param {object} [oDataTemplate]: Optional. The object definition of the data being stored / updated. 
		 *			The second parameter oObject will be copied to this template before being written to the 
		 *          service. 
		 * @param {object} [oCallbacks] : Optional object containing the following parameters 
		 *      success: Object with the following data 
		 *           fCallback: Callback function to be invoked on success
		 *           oParam: Object / array to be passed to the callback
		 *      error: Object with the following data
		 *           fCallback: Callback function to be invoked on error 
		 *           oParam: Object / array to be passed on the callback
		 */
		saveData: function (bUpdate, oData, oDataTemplate, oCallbacks, oHeaders) {
			let oThisObject = this;
			let oCurrentSystemPromise = oThisObject.oController.getCurrentSystem();
			oCurrentSystemPromise.then(function(sSystem) {
				if(sSystem.IS_S4H === "ECC" && oThisObject.arrEccDependency.includes(oThisObject.sServiceModelName)) {
					oThisObject.sServiceModelName = oThisObject.sServiceModelName + "_ECC";
					oThisObject.oDataModel = oThisObject.oController.getOwnerComponent().getModel(oThisObject.sServiceModelName);
					if (oThisObject.oDataModel) {
						oThisObject.oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
					} else {
						throw new Error("Service " + oThisObject.sServiceModelName + " not defined in the manifest.");
					}
				} else if(!sSystem.Version && oThisObject.arrVersionDependency.includes(oThisObject.sServiceModelName)) {
					oThisObject.sServiceModelName = oThisObject.sServiceModelName + "_ECC";
					oThisObject.oDataModel = oThisObject.oController.getOwnerComponent().getModel(oThisObject.sServiceModelName);
					if (oThisObject.oDataModel) {
						oThisObject.oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
					} else {
						throw new Error("Service " + oThisObject.sServiceModelName + " not defined in the manifest.");
					}
				}
				// Get the object to be sent to the service
				let oServicePayload = oThisObject._copyDataToTemplate(oData, true, oDataTemplate);
				let promiseShowPopupAlert;
				let oWriteParameterObject = {
					success: function (oDataResult) {
	
						// Invoke the success callback
						oThisObject._invokeWriteSuccess(oThisObject, oCallbacks, oDataResult);
						
						if (oThisObject.bShowBusyIndicator === true ) {
							BusyIndicator.hide();
						}
						// Show a toast message 
						if ( oThisObject.sToastMessage ) {
							promiseShowPopupAlert =
								Utilities.showPopupAlert(oThisObject.sToastMessage, MessageBox.Icon.INFORMATION, "");
								promiseShowPopupAlert.then(function () {
								});
						}
					},
					error: function (oError) {
	
						// Invoke the error callback before performing any other action
						oThisObject._invokeError(oThisObject, oCallbacks, oError);
						
						if (oThisObject.bShowBusyIndicator === true ) {
							BusyIndicator.hide();
						}
					}
				};
				
				if (oThisObject.bShowBusyIndicator === true ) {
					BusyIndicator.show(300);
				}
				if (oHeaders) {
					oThisObject.oDataModel.setHeaders(oHeaders);
				}
				oThisObject.oDataModel.create(oThisObject.sUrl, oServicePayload, oWriteParameterObject);
			});
		},
		deleteData: function (bUpdate, oData, oDataTemplate, oCallbacks, oUrlParameters, oHeaders) {
			let oThisObject = this;
			let oCurrentSystemPromise = oThisObject.oController.getCurrentSystem();
			oCurrentSystemPromise.then(function(sSystem) {
				if(sSystem.IS_S4H === "ECC" && oThisObject.arrEccDependency.includes(oThisObject.sServiceModelName)) {
					oThisObject.sServiceModelName = oThisObject.sServiceModelName + "_ECC";
					oThisObject.oDataModel = oThisObject.oController.getOwnerComponent().getModel(oThisObject.sServiceModelName);
					if (oThisObject.oDataModel) {
						oThisObject.oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
					} else {
						throw new Error("Service " + oThisObject.sServiceModelName + " not defined in the manifest.");
					}
				} else if(!sSystem.Version && oThisObject.arrVersionDependency.includes(oThisObject.sServiceModelName)) {
					oThisObject.sServiceModelName = oThisObject.sServiceModelName + "_ECC";
					oThisObject.oDataModel = oThisObject.oController.getOwnerComponent().getModel(oThisObject.sServiceModelName);
					if (oThisObject.oDataModel) {
						oThisObject.oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
					} else {
						throw new Error("Service " + oThisObject.sServiceModelName + " not defined in the manifest.");
					}
				}
				let oDeleteParameterObject = {
					method: "DELETE",
					success: function (oDataResult, oResponse) {
						
						if(oResponse.statusCode === "204"){
							oResponse.statusText = "Delete Successful";
						}
	
						// Invoke the success callback
						oThisObject._invokeGetSuccess(oThisObject, oCallbacks, oResponse);
						
						if (oThisObject.bShowBusyIndicator === true ) {
							BusyIndicator.hide();
						}
	
						// Show a toast message 
						if ( oThisObject.sToastMessage ) {
							MessageToast.show(oThisObject.sToastMessage);
						}	
					},
					error: function (oError) {
	
						// Invoke the error callback before performing any other action
						oThisObject._invokeError(oThisObject, oCallbacks, oError);
						if (oThisObject.bShowBusyIndicator === true ) {
							BusyIndicator.hide();
						}
					}
				};
				if (oUrlParameters) {
					oDeleteParameterObject.urlParameters = oUrlParameters;
				}
				if (oThisObject.bShowBusyIndicator === true ) {
					BusyIndicator.show(300);
				}
				if (oHeaders) {
					oThisObject.oDataModel.setHeaders(oHeaders);
				}
				oThisObject.oDataModel.remove(oThisObject.sUrl, oDeleteParameterObject);
			});
		},		
		/**
		 * Enable/Disable Busy Indicator when the data service is called
		 * By default, it is on, i.e. The busy Indicator will be shown
		 * @param {boolean} bShowIndicator
		 *			true  : Busy Indicator will be shown
		 *			false : Busy Indicator will not be shown
		 * @return Return the class itself for function chaining
		 */ 
		showBusyIndicator: function(bShowIndicator) {
			this.bShowBusyIndicator = bShowIndicator;
			return this;
		},
		
		
		/**
		 * Enable/Disable Message Toast when the data service is called
		 * By default, it is turned off, i.e. No Message Toast will be shown
		 * @param {string} sToastMessage
		 *			Specify the message which needs to be shown as a Message Toast
		 * @return Return the class itself for function chaining
		 */ 
		setToastMessage: function(sToastMessage) {
			this.sToastMessage = sToastMessage;
			return this;
		},
		
		/**
		 * Internal function to copy the data from the input object to the object template. 
		 * return the input object if the template is not provided. 
		 * @param {object} oData: Object to be written to the service
		 * @param {boolean} bIncludeNull:  Specify if null properties also need to be copied. Unused now. 
		 * @param {object} [oDataTemplate]: Optional. The object definition of the data being stored / updated. 
		 *			The second parameter oObject will be copied to this template before being written to the 
		 *          service. 
		 */
		_copyDataToTemplate: function (oData, bIncludeNull, oDataTemplate) {
			// if the template is not provided, return the oData object
			if (!oDataTemplate) {
				let oDataCopy = {};
				jQuery.extend(true, oDataCopy, oData);
				return oDataCopy;
			}

			// Create a new object and copy the keys
			let oTemplateCopy = {};
			jQuery.extend(true, oTemplateCopy, oDataTemplate);

			// If the template is provided create a new object of type template and copy the data
			for (let key in oDataTemplate) {
				if (oData[key]) {
					oTemplateCopy[key] = oData[key];
				}
			}

			return oTemplateCopy;
		},

		/**
		 * Internal function to invoke the success callback after a write service call. 
		 */
		_invokeWriteSuccess: function (oThisObject, oCallbacks, oResponseData) {
			// invoke the callback with the parameters provided
			if (oCallbacks && oCallbacks.success && oCallbacks.success.fCallback) {
				oCallbacks.success.fCallback(oCallbacks.success.oParam, oResponseData);
			}
		},

		/**
		 * Internal function to invoke the success callback after a write service call. 
		 */
		_invokeWriteFailure: function (oThisObject, oCallbacks, oResponseError) {
			// invoke the callback with the parameters provided
			if (oCallbacks && oCallbacks.success && oCallbacks.success.fCallback) {
				oCallbacks.success.fCallback(oCallbacks.success.oParam, oResponseError);
			}
		},

		/**
		 * Internal function to invoke the success callback after a service call. 
		 */
		_invokeGetSuccess: function (oThisObject, oCallbacks, oResponseData) {
			// invoke the callback with the parameters provided
			if (oCallbacks && oCallbacks.success && oCallbacks.success.fCallback) {
				oCallbacks.success.fCallback(oCallbacks.success.oParam, oResponseData);
			} else {
				// If success callback is provided, pass on the data or assign the data to the view 
				let oJsonModel = new JSONModel();

				// Set the data to the json model 
				oJsonModel.setData(oResponseData);

				// Set the data model to the view.
				oThisObject.oController.getView().setModel(oJsonModel, oThisObject.sTargetDataModelName);
			}

		},

		/**
		 * Internal function to invoke the error callback after a service call.  
		 */
		_invokeError: function (oThisObject, oCallbacks, oResponseError) {
			let sErrorMessage = "";
			// Invoke the error callback before performing any other action
			if (oCallbacks && oCallbacks.error && oCallbacks.error.fCallback) {
				oCallbacks.error.fCallback(oCallbacks.error.oParam, oResponseError);
			} else {
				
				// Parse the error message for display
				try {
					let oErrorMessage = JSON.parse(oResponseError.responseText);
					sErrorMessage = oErrorMessage.error.message.value;
				} catch (err) {

					let xmlDoc = XMLHelper.parse(oResponseError.responseText);
					let bodyTags = xmlDoc.getElementsByTagName("body");
					let h1Tags = [];
					if(bodyTags.length > 0){
						h1Tags = bodyTags[0].getElementsByTagName("h1");
					}

					let childNodes = [];
					if(h1Tags.length > 0){
						childNodes = h1Tags[0].childNodes;
					}
					
					sErrorMessage = "";
					if(childNodes.length > 0){
						sErrorMessage = childNodes[0].nodeValue;
					}
				}
				
				if(oResponseError.statusCode === 503){
					sErrorMessage += "\n\nPossible login time out. Refresh to continue. Any unsaved changes will be lost.";
				}
				Utilities.showPopupAlert(sErrorMessage, MessageBox.Icon.ERROR, oResponseError.message);
			}
		}
	});
});