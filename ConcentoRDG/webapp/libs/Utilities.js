
sap.ui.define([
	"sap/m/MessageBox"
], function (MessageBox) {
	"use strict";
	return {
		/**
		 * Sends a notification to the notification controller. 
		 * 
		 * @param {sap.ui.core.mvc.Controller} oController 
		 * @param {sap.ui.core.MessageType} messageType 
		 * @param {String} sTitle 
		 * @param {String} sDescription 
		 */
		sendNotification: function(oController, messageType, sTitle, sDescription, sGroupName){
			let oMessageInfo = { 
				type: messageType,
				title: sTitle,
				description: sDescription, 
				groupName: sGroupName
			  };
		
			oController.getOwnerComponent().getEventBus().publish("onNotification", oMessageInfo);
		},
		showPopupAlert: function (message, type, title, actions) {
			if (actions === null){
				actions = [sap.m.MessageBox.Action.OK];
			}
			let promise = new Promise(function (resolve, reject) {
				MessageBox.show(
					message, {
						icon: type,
						title: title,
						actions: actions,
						onClose: function (oAction) {
								if (oAction === sap.m.MessageBox.Action.YES || oAction === sap.m.MessageBox.Action.OK || oAction === sap.m.MessageBox.Action.DELETE) {
									resolve();
								} else {
									reject();
								}
						}
					}
					);
			});
			return promise;
		},

		getMessageIconCoreForType: function(sMessageType){
			let iconMessage = sap.ui.core.MessageType.Error;
			switch(sMessageType){
				case "E": 
				iconMessage = sap.ui.core.MessageType.Error;
				break;

				case "S": 
				iconMessage = sap.ui.core.MessageType.Success;
				break;

				default: 
				iconMessage = sap.ui.core.MessageType.Information;
				break;
			}

			return iconMessage;
		},

		getMessageIconBoxForType: function(sMessageType){
			let iconMessage = MessageBox.Icon.ERROR;
			switch(sMessageType){
				case "E": 
				iconMessage = MessageBox.Icon.ERROR;
				break;

				case "S": 
				iconMessage = MessageBox.Icon.SUCCESS;
				break;

				default: 
				iconMessage = MessageBox.Icon.INFORMATION;
				break;
			}

			return iconMessage;
		},

		sendMessagetoAI: function(oController, sQueryMessage){

			let oMessageInfo = {
				message: sQueryMessage,
			};
		
			oController.getOwnerComponent().getEventBus().publish("copilot_send_message", oMessageInfo);
		}
	};
});