/* Enter your custom styles here */

/*td div.sapMInputBaseContentWrapper {*/
/*    border: none;*/
/*    background-color: transparent !important;*/
/*    outline: none !important;*/
/*}*/

/*input[readonly="readonly"],*/
/*input[disabled="disabled"] {*/
/*    border-bottom: none !important;*/
/*}*/

/*input {*/
/*    border-bottom: solid !important;*/
/*    border-width: 1px !important;*/
/*    border-color: blue !important;*/
/*}*/

/*input:focus {*/
/*    border-color: green !important;*/
/*}*/

/*.sapMListTblCell:not(:first-child) {*/
    /*border-left: 1px solid #ccc;*/
    /*border-right: 1px solid #ccc;*/
/*}*/


/* CONCENTO THEME */

.dmrBackGroundImage {
    background-color: #d5ddfb;
    background-image: url("../img/Background-Dark.png");
    background-repeat: no-repeat;
    background-position: left bottom;
    background-size: 100% 45%;
}

.titleUnidentified{
	font-size:1.4em;
}

.requiredInfo {
	color: red;
	position: absolute;
	right: 0px;
}

.sapMITBContent {
    padding: 0!important;
}

.toolbarBackground{
    border-radius: 0.75rem;
}

.toolbarBackground, .toolbarBackground>div>ul>li {
    background-color: #92C0F7!important;
}

.sapFFCL .sapFFCLColumn.sapFFCLColumnInset {
    padding: 0!important;
}

/*table .sapUiIcon {*/
/*    color: #4d6079;*/
/*}*/


/* Generic Tile Background Color */

/*.dmrGenericTile {*/
    /*    background-color: #4d6079; */
/*    background-color: #69788D;*/
/*    color: #ffffff;*/
/*}*/

/*.dmrGenericTile:hover {*/
/*    background-color: #305978;*/
/*    color: #ffffff;*/
/*}*/


/* Generic Tile contents to have white font  */

/*.dmrGenericTile * {*/
/*    color: #FFFFFF !important;*/
/*}*/

/*.dmrSelected {*/
/*    background-color: #B9D535 !important;*/
/*}*/


/* List / Table Headers */

/*[role="toolbar"],*/
/*li header,*/
/*[data-sap-ui^="groupHeader"] {*/
/*    background-color: #4d6079 !important;*/
/*}*/

/*[role="toolbar"] *,*/
/*li header *,*/
/*[data-sap-ui^="groupHeader"] {*/
/*    color: #FFFFFF !important;*/
/*    background-color: #4d6079 !important;*/
/*}*/

/*.sapMPageHeader * {*/
/*    background-color: white !important;*/
/*    color: #4d6079 !important;*/
/*}*/

/*.transparentBar,*/
/*.transparentBar * {*/
/*    background-color: #FFFFFF !important;*/
/*    color: #4d6079 !important;*/
/*}*/


/* Table / List Selected Row color */

/*table [aria-selected="true"],*/
/*li[aria-selected="true"] {*/
/*    background-color: #B9D535 !important;*/
/*}*/

/*thead {*/
/*    font-style: bold;*/
/*}*/

/*.whiteBackGround {*/
/*    background-color: rgba(255, 255, 255, 0.5) !important;*/
/*}*/


/* Search input all over must be white background */

/*.sapSuiteUiCommonsNetworkGraphSearchField form {*/
/*    background-color: #FFFFFF !important;*/
/*}*/

/*.sapSuiteUiCommonsNetworkGraphSearchField form [title^="Search"] {*/
/*    color: #4d6079 !important;*/
/*}*/


/* Selected nodes have no background color .. just the titles which are already blue */

/*.sapSuiteUiCommonsNetworkElementSelected {*/
/*    background-color: rgba(0, 0, 0, 0) !important;*/
/*}*/


/* for Dynamic Rules  */
/*.tableColumnDriving * {*/
    /* background-color: #cdced1;*/
/*    color: blue;*/
/*}*/

/* for Dynamic Rules  */
/*.tableColumnWorkflow * {*/
    /* background-color: #cdced1;*/
/*    color: blue;*/
/*}*/

/*.tableColumnDeriving * {*/
    /* background-color: #d8ffca; */
/*    color: green;*/
/*}*/

/*.roundButton .sapMBtnInner {*/
/*  border-radius: 1.5rem;*/
/*}*/

/*.DMRListGroupHeaderStyle {*/
	/* border-bottom: 3px solid #4d6079; */
/*	background-color: #cdced1;*/
/*}*/

/*.witheText{*/
/*	color: #FFFFFF*/
/*}*/