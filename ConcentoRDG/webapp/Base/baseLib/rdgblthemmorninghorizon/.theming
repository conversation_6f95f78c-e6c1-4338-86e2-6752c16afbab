{"mVersionDependency": {"Base.baseLib.sap_horizon": "11.1.41", "Base.baseLib": "11.1.41", "Base": "11.1.41"}, "sEntity": "Theme", "sId": "rdgblthemmorninghorizon", "sLabel": "RDG Morning Horizon", "oExtends": "Base.baseLib.sap_horizon", "aFiles": ["base", "preview", "css_variables", "custom"], "sTextDirections": "LTR", "sType": "STANDARD", "sThemability": "PUBLIC", "mData": {"mThemeMasterState": {"mSourceInfo": {"sCreated": "2022-09-09T13:55:56.442Z", "sLastModified": "2022-09-15T10:52:05.792Z", "aVersionHistory": [{"sDate": "2022-09-09T13:55:56.442Z", "mVersion": {"UI5": "1.106.0", "Base": "11.1.41", "UR": "10.30.7.346909.0"}}]}, "mBuildInfo": {"sLastBuilt": "2022-09-15T10:52:16.855Z", "mVersion": {"UI5": "1.106.0", "Base": "11.1.41", "UR": "10.30.7.346909.0"}}}}, "sReleaseStage": "GENERAL_AVAILABILITY", "sDeploymentType": "MANUAL", "aBadgeParameters": [{"Id": "sapBrandColor", "CssValue": "#0070f2", "Label": "Brand Color", "Tags": "Quick,Base,Color"}, {"Id": "sapHighlightColor", "CssValue": "#0070f2", "Label": "Highlight Color", "Tags": "Quick,Base,Content,Color"}, {"Id": "sapBaseColor", "CssValue": "#fff", "Label": "Base Color", "Tags": "Quick,Base,Color"}, {"Id": "sapShellColor", "CssValue": "#fff", "Label": "Shell Header Color", "Tags": "Quick,Base,Shell,Color"}, {"Id": "sapBackgroundColor", "CssValue": "#f5f6f7", "Label": "Background Color", "Tags": "Quick,Base,Color"}, {"Id": "sapTextColor", "CssValue": "#1d2d3e", "Label": "Text Color", "Tags": "Quick,Base,Content,Color,Font"}, {"Id": "sapLinkColor", "CssValue": "#0070f2", "Label": "Link Color", "Tags": "Quick,Base,Content,Color,Font,Link"}]}