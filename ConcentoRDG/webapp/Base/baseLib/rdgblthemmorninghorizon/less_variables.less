/* Copyright (c) 2012-2020 SAP SE or an SAP affiliate company. All rights reserved..  Theming Engine 1.70.1 */
@sapBrandColor: #0070f2;
@sapHighlightColor: #0070f2;
@sapBaseColor: #fff;
@sapShellColor: #fff;
@sapBackgroundColor: #f5f6f7;
@sapFontFamily: "72", "72full", Arial, Helvetica, sans-serif;
@sapFontLightFamily: "72-Light", "72-Lightfull", "72", "72full", Arial, Helvetica, sans-serif;
@sapFontBoldFamily: "72-Bold", "72-Boldfull", "72", "72full", Arial, Helvetica, sans-serif;
@sapFontSemiboldFamily: "72-Semibold", "72-Semiboldfull", "72", "72full", Arial, Helvetica, sans-serif;
@sapFontSemiboldDuplexFamily: "72-SemiboldDuplex", "72-SemiboldDuplexfull", "72", "72full", Arial, Helvetica, sans-serif;
@sapFontBlackFamily: "72Black", "72", "72full", Arial, Helvetica, sans-serif;
@sapFontHeaderFamily: "72-Bold", "72-Boldfull", "72", "72full", Arial, Helvetica, sans-serif;
@sapFontSize: .875rem;
@sapFontSmallSize: .75rem;
@sapFontLargeSize: 1rem;
@sapFontHeader1Size: 3rem;
@sapFontHeader2Size: 2rem;
@sapFontHeader3Size: 1.5rem;
@sapFontHeader4Size: 1.25rem;
@sapFontHeader5Size: 1rem;
@sapFontHeader6Size: .875rem;
@sapTextColor: #1d2d3e;
@sapLinkColor: #0070f2;
@sapLink_Hover_Color: #0064d9;
@sapLink_Active_Color: #0064d9;
@sapLink_Visited_Color: #0064d9;
@sapLink_InvertedColor: #bfddff;
@sapLink_SubtleColor: #1d2d3e;
@sapCompanyLogo: none;
@sapBackgroundImage: none;
@sapBackgroundImageOpacity: 1.0;
@sapBackgroundImageRepeat: false;
@sapSelectedColor: #0070f2;
@sapActiveColor: #dee2e5;
@sapHighlightTextColor: #fff;
@sapTitleColor: #1d2d3e;
@sapNegativeColor: #aa0808;
@sapCriticalColor: #e76500;
@sapPositiveColor: #256f3a;
@sapInformativeColor: #0070f2;
@sapNeutralColor: #788fa6;
@sapNegativeElementColor: #f53232;
@sapCriticalElementColor: #e76500;
@sapPositiveElementColor: #30914c;
@sapInformativeElementColor: #0070f2;
@sapNeutralElementColor: #788fa6;
@sapNegativeTextColor: #aa0808;
@sapPositiveTextColor: #256f3a;
@sapCriticalTextColor: #b95100;
@sapInformativeTextColor: #0070f2;
@sapNeutralTextColor: #1d2d3e;
@sapNeutralBorderColor: #788fa6;
@sapErrorColor: #aa0808;
@sapErrorBorderColor: #f53232;
@sapWarningColor: #e76500;
@sapWarningBorderColor: #e76500;
@sapSuccessColor: #256f3a;
@sapSuccessBorderColor: #30914c;
@sapInformationColor: #0070f2;
@sapInformationBorderColor: #0070f2;
@sapErrorBackground: #ffeaf4;
@sapWarningBackground: #fff8d6;
@sapSuccessBackground: #f5fae5;
@sapInformationBackground: #e1f4ff;
@sapNeutralBackground: #eff1f2;
@sapIndicationColor_1: #840606;
@sapIndicationColor_1_Background: #840606;
@sapIndicationColor_1_BorderColor: #840606;
@sapIndicationColor_1_Hover_Background: #6c0505;
@sapIndicationColor_1_Active_Background: #530404;
@sapIndicationColor_1_TextColor: #fff;
@sapIndicationColor_2: #aa0808;
@sapIndicationColor_2_Background: #aa0808;
@sapIndicationColor_2_BorderColor: #aa0808;
@sapIndicationColor_2_Hover_Background: #920707;
@sapIndicationColor_2_Active_Background: #790606;
@sapIndicationColor_2_TextColor: #fff;
@sapIndicationColor_3: #b95100;
@sapIndicationColor_3_Background: #e76500;
@sapIndicationColor_3_BorderColor: #e76500;
@sapIndicationColor_3_Hover_Background: #d85e00;
@sapIndicationColor_3_Active_Background: #c85800;
@sapIndicationColor_3_TextColor: #fff;
@sapIndicationColor_4: #256f3a;
@sapIndicationColor_4_Background: #256f3a;
@sapIndicationColor_4_BorderColor: #256f3a;
@sapIndicationColor_4_Hover_Background: #1f5c30;
@sapIndicationColor_4_Active_Background: #184926;
@sapIndicationColor_4_TextColor: #fff;
@sapIndicationColor_5: #0070f2;
@sapIndicationColor_5_Background: #0070f2;
@sapIndicationColor_5_BorderColor: #0070f2;
@sapIndicationColor_5_Hover_Background: #0064d9;
@sapIndicationColor_5_Active_Background: #0058bf;
@sapIndicationColor_5_TextColor: #fff;
@sapIndicationColor_6: #046c7a;
@sapIndicationColor_6_Background: #046c7a;
@sapIndicationColor_6_BorderColor: #046c7a;
@sapIndicationColor_6_Hover_Background: #035661;
@sapIndicationColor_6_Active_Background: #024049;
@sapIndicationColor_6_TextColor: #fff;
@sapIndicationColor_7: #5d36ff;
@sapIndicationColor_7_Background: #5d36ff;
@sapIndicationColor_7_BorderColor: #5d36ff;
@sapIndicationColor_7_Hover_Background: #481cff;
@sapIndicationColor_7_Active_Background: #3403ff;
@sapIndicationColor_7_TextColor: #fff;
@sapIndicationColor_8: #a100c2;
@sapIndicationColor_8_Background: #a100c2;
@sapIndicationColor_8_BorderColor: #a100c2;
@sapIndicationColor_8_Hover_Background: #8c00a9;
@sapIndicationColor_8_Active_Background: #77008f;
@sapIndicationColor_8_TextColor: #fff;
@sapElement_LineHeight: 2.75rem;
@sapElement_Height: 2.25rem;
@sapElement_BorderWidth: .0625rem;
@sapElement_BorderCornerRadius: .75rem;
@sapElement_Compact_LineHeight: 2rem;
@sapElement_Compact_Height: 1.625rem;
@sapElement_Condensed_LineHeight: 1.5rem;
@sapElement_Condensed_Height: 1.375rem;
@sapContent_LineHeight: 1.5;
@sapContent_IconHeight: 1rem;
@sapContent_IconColor: #1d2d3e;
@sapContent_ContrastIconColor: #fff;
@sapContent_NonInteractiveIconColor: #758ca4;
@sapContent_MarkerIconColor: #5d36ff;
@sapContent_MarkerTextColor: #046c7a;
@sapContent_MeasureIndicatorColor: #556b81;
@sapContent_Selected_MeasureIndicatorColor: #0070f2;
@sapContent_ImagePlaceholderBackground: #eaecee;
@sapContent_ImagePlaceholderForegroundColor: #5b738b;
@sapContent_RatedColor: #d27700;
@sapContent_UnratedColor: #758ca4;
@sapContent_BusyColor: #0070f2;
@sapContent_FocusColor: #0070f2;
@sapContent_FocusStyle: solid;
@sapContent_FocusWidth: .125rem;
@sapContent_ContrastFocusColor: #fff;
@sapContent_ShadowColor: #556b82;
@sapContent_ContrastShadowColor: #fff;
@sapContent_Shadow0: 0 0 0.125rem 0 rgba(85,107,130,0.16), 0 0.125rem 0.25rem 0 rgba(85,107,130,0.16);
@sapContent_Shadow1: 0 0 0 0.0625rem rgba(85,107,130,0.42), 0 0.125rem 0.5rem 0 rgba(85,107,130,0.3);
@sapContent_Shadow2: 0 0 0 0.0625rem rgba(85,107,130,0.42), 0 0.625rem 1.875rem 0 rgba(85,107,130,0.3);
@sapContent_Shadow3: 0 0 0 0.0625rem rgba(85,107,130,0.42), 0 1.25rem 5rem 0 rgba(85,107,130,0.3);
@sapContent_TextShadow: 0 0 0.125rem #fff;
@sapContent_ContrastTextShadow: 0 0 0.0625rem rgba(0,0,0,0.7);
@sapContent_HeaderShadow: 0 0.125rem 0.125rem 0 rgba(85,107,130,0.05), inset 0 -0.0625rem 0 0 #d9d9d9;
@sapContent_Interaction_Shadow: 0 0 0.125rem 0 rgba(85,107,130,0.72);
@sapContent_Selected_Shadow: 0 0 0.125rem 0 rgba(0,112,242,0.72);
@sapContent_Negative_Shadow: 0 0 0.125rem 0 rgba(170,8,8,0.72);
@sapContent_Critical_Shadow: 0 0 0.125rem 0 rgba(231,101,0,0.72);
@sapContent_Positive_Shadow: 0 0 0.125rem 0 rgba(37,111,58,0.72);
@sapContent_Informative_Shadow: 0 0 0.125rem 0 rgba(0,112,242,0.72);
@sapContent_Neutral_Shadow: 0 0 0.125rem 0 rgba(120,143,166,0.72);
@sapContent_SearchHighlightColor: #dafdf5;
@sapContent_HelpColor: #188918;
@sapContent_LabelColor: #556b82;
@sapContent_MonospaceFontFamily: "72Mono", "72Monofull", lucida console, monospace;
@sapContent_MonospaceBoldFontFamily: "72Mono-Bold", "72Mono-Boldfull", lucida console, monospace;
@sapContent_IconFontFamily: "SAP-icons";
@sapContent_DisabledTextColor: rgba(29,45,62,0.6);
@sapContent_DisabledOpacity: 0.4;
@sapContent_ContrastTextThreshold: 0.65;
@sapContent_ContrastTextColor: #fff;
@sapContent_ForegroundColor: #efefef;
@sapContent_ForegroundBorderColor: #758ca4;
@sapContent_ForegroundTextColor: #1d2d3e;
@sapContent_BadgeBackground: #aa0808;
@sapContent_BadgeTextColor: #fff;
@sapContent_Placeholderloading_Background: #ccc;
@sapContent_Placeholderloading_Gradient: linear-gradient(to right, #ccc 0%, #ccc 20%, #999 50%, #ccc 80%, #ccc 100%);
@sapContent_DragAndDropActiveColor: #0070f2;
@sapContent_Selected_Background: #fff;
@sapContent_Selected_TextColor: #0070f2;
@sapContent_Selected_Hover_Background: #fff;
@sapContent_Selected_ForegroundColor: #0070f2;
@sapContent_ForcedColorAdjust: none;
@sapContent_Illustrative_Color1: #5d36ff;
@sapContent_Illustrative_Color2: #0070f2;
@sapContent_Illustrative_Color3: #f58b00;
@sapContent_Illustrative_Color4: #00144a;
@sapContent_Illustrative_Color5: #a9b4be;
@sapContent_Illustrative_Color6: #d5dadd;
@sapContent_Illustrative_Color7: #ebf8ff;
@sapContent_Illustrative_Color8: #fff;
@sapContent_Illustrative_Color9: #64edd2;
@sapContent_Illustrative_Color10: #ebf8ff;
@sapContent_Illustrative_Color11: #f31ded;
@sapContent_Illustrative_Color12: #00a800;
@sapContent_Illustrative_Color13: #005dc9;
@sapContent_Illustrative_Color14: #004da5;
@sapContent_Illustrative_Color15: #cc7400;
@sapContent_Illustrative_Color16: #3b0ac6;
@sapContent_Illustrative_Color17: #00a58a;
@sapContent_Illustrative_Color18: #d1efff;
@sapContent_Illustrative_Color19: #b8e6ff;
@sapContent_Illustrative_Color20: #9eddff;
@sapShell_Background: #f5f6f7;
@sapShell_BackgroundImage: linear-gradient(to bottom, #f5f6f7, #f5f6f7);
@sapShell_BackgroundGradient: linear-gradient(to bottom, #f5f6f7, #f5f6f7);
@sapShell_BackgroundImageOpacity: 1.0;
@sapShell_BackgroundImageRepeat: false;
@sapShell_BorderColor: #fff;
@sapShell_TextColor: #1d2d3e;
@sapShell_InteractiveBackground: #eff1f2;
@sapShell_InteractiveTextColor: #1d2d3e;
@sapShell_InteractiveBorderColor: #556b81;
@sapShell_GroupTitleTextColor: #1d2d3e;
@sapShell_GroupTitleTextShadow: 0 0 .125rem #fff;
@sapShell_Hover_Background: #fff;
@sapShell_Active_Background: #fff;
@sapShell_Active_TextColor: #0070f2;
@sapShell_Selected_Background: #fff;
@sapShell_Selected_TextColor: #0070f2;
@sapShell_Selected_Hover_Background: #fff;
@sapShell_Favicon: none;
@sapShell_Navigation_Background: #fff;
@sapShell_Navigation_SelectedColor: #0070f2;
@sapShell_Navigation_Selected_TextColor: #0070f2;
@sapShell_Navigation_TextColor: #1d2d3e;
@sapShell_Navigation_Hover_Background: #fff;
@sapShell_Navigation_Active_Background: #fff;
@sapShell_Navigation_Active_TextColor: #0070f2;
@sapShell_Shadow: 0 0.125rem 0.125rem 0 rgba(85,107,130,0.1), inset 0 -0.0625rem 0 0 rgba(85,107,130,0.2);
@sapShell_NegativeColor: #aa0808;
@sapShell_CriticalColor: #b95100;
@sapShell_PositiveColor: #256f3a;
@sapShell_InformativeColor: #0070f2;
@sapShell_NeutralColor: #1d2d3e;
@sapAvatar_1_Background: #fff3b8;
@sapAvatar_1_BorderColor: #fff3b8;
@sapAvatar_1_TextColor: #a45d00;
@sapAvatar_2_Background: #ffd0e7;
@sapAvatar_2_BorderColor: #ffd0e7;
@sapAvatar_2_TextColor: #aa0808;
@sapAvatar_3_Background: #ffdbe7;
@sapAvatar_3_BorderColor: #ffdbe7;
@sapAvatar_3_TextColor: #ba066c;
@sapAvatar_4_Background: #ffdcf3;
@sapAvatar_4_BorderColor: #ffdcf3;
@sapAvatar_4_TextColor: #a100c2;
@sapAvatar_5_Background: #ded3ff;
@sapAvatar_5_BorderColor: #ded3ff;
@sapAvatar_5_TextColor: #552cff;
@sapAvatar_6_Background: #d1efff;
@sapAvatar_6_BorderColor: #d1efff;
@sapAvatar_6_TextColor: #0057d2;
@sapAvatar_7_Background: #c2fcee;
@sapAvatar_7_BorderColor: #c2fcee;
@sapAvatar_7_TextColor: #046c7a;
@sapAvatar_8_Background: #ebf5cb;
@sapAvatar_8_BorderColor: #ebf5cb;
@sapAvatar_8_TextColor: #256f3a;
@sapAvatar_9_Background: #ddccf0;
@sapAvatar_9_BorderColor: #ddccf0;
@sapAvatar_9_TextColor: #6c32a9;
@sapAvatar_10_Background: #eaecee;
@sapAvatar_10_BorderColor: #eaecee;
@sapAvatar_10_TextColor: #556b82;
@sapButton_BorderWidth: .0625rem;
@sapButton_BorderCornerRadius: .5rem;
@sapButton_Background: #eaecee;
@sapButton_BorderColor: #eaecee;
@sapButton_TextColor: #1d2d3e;
@sapButton_Hover_Background: #fff;
@sapButton_Hover_BorderColor: #fff;
@sapButton_Hover_TextColor: #1d2d3e;
@sapButton_IconColor: #1d2d3e;
@sapButton_Active_Background: #fff;
@sapButton_Active_BorderColor: #0070f2;
@sapButton_Active_TextColor: #0070f2;
@sapButton_Emphasized_Background: #0070f2;
@sapButton_Emphasized_BorderColor: #0070f2;
@sapButton_Emphasized_TextColor: #fff;
@sapButton_Emphasized_Hover_Background: #0064d9;
@sapButton_Emphasized_Hover_BorderColor: #0064d9;
@sapButton_Emphasized_Hover_TextColor: #fff;
@sapButton_Emphasized_Active_Background: #fff;
@sapButton_Emphasized_Active_BorderColor: #0070f2;
@sapButton_Emphasized_Active_TextColor: #0070f2;
@sapButton_Emphasized_TextShadow: transparent;
@sapButton_Emphasized_FontWeight: bold;
@sapButton_Reject_Background: #ffd6e9;
@sapButton_Reject_BorderColor: #ffd6e9;
@sapButton_Reject_Hover_Background: #fff;
@sapButton_Reject_Hover_BorderColor: #fff;
@sapButton_Reject_Hover_TextColor: #aa0808;
@sapButton_Reject_Active_Background: #fff;
@sapButton_Reject_Active_BorderColor: #f53232;
@sapButton_Reject_Active_TextColor: #aa0808;
@sapButton_Reject_TextColor: #aa0808;
@sapButton_Reject_Selected_Background: #fff;
@sapButton_Reject_Selected_BorderColor: #f53232;
@sapButton_Reject_Selected_TextColor: #aa0808;
@sapButton_Reject_Selected_Hover_Background: #fff;
@sapButton_Reject_Selected_Hover_BorderColor: #f53232;
@sapButton_Accept_Background: #ebf5cb;
@sapButton_Accept_BorderColor: #ebf5cb;
@sapButton_Accept_Hover_Background: #fff;
@sapButton_Accept_Hover_BorderColor: #fff;
@sapButton_Accept_Hover_TextColor: #256f3a;
@sapButton_Accept_Active_Background: #fff;
@sapButton_Accept_Active_BorderColor: #30914c;
@sapButton_Accept_Active_TextColor: #256f3a;
@sapButton_Accept_TextColor: #256f3a;
@sapButton_Accept_Selected_Background: #fff;
@sapButton_Accept_Selected_BorderColor: #30914c;
@sapButton_Accept_Selected_TextColor: #256f3a;
@sapButton_Accept_Selected_Hover_Background: #fff;
@sapButton_Accept_Selected_Hover_BorderColor: #30914c;
@sapButton_Lite_Background: transparent;
@sapButton_Lite_BorderColor: transparent;
@sapButton_Lite_TextColor: #0064d9;
@sapButton_Lite_Hover_Background: #fff;
@sapButton_Lite_Hover_BorderColor: #fff;
@sapButton_Lite_Hover_TextColor: #0064d9;
@sapButton_Lite_Active_Background: #fff;
@sapButton_Lite_Active_BorderColor: #0070f2;
@sapButton_Selected_Background: #fff;
@sapButton_Selected_BorderColor: #0070f2;
@sapButton_Selected_TextColor: #0070f2;
@sapButton_Selected_Hover_Background: #fff;
@sapButton_Selected_Hover_BorderColor: #0070f2;
@sapButton_Attention_Background: #fff3b7;
@sapButton_Attention_BorderColor: #fff3b7;
@sapButton_Attention_TextColor: #b95100;
@sapButton_Attention_Hover_Background: #fff;
@sapButton_Attention_Hover_BorderColor: #fff;
@sapButton_Attention_Hover_TextColor: #b95100;
@sapButton_Attention_Active_Background: #fff;
@sapButton_Attention_Active_BorderColor: #ff8d34;
@sapButton_Attention_Active_TextColor: #b95100;
@sapButton_Attention_Selected_Background: #fff;
@sapButton_Attention_Selected_BorderColor: #ff8d34;
@sapButton_Attention_Selected_TextColor: #b95100;
@sapButton_Attention_Selected_Hover_Background: #fff;
@sapButton_Attention_Selected_Hover_BorderColor: #ff8d34;
@sapButton_Negative_Background: #f53232;
@sapButton_Negative_BorderColor: #f53232;
@sapButton_Negative_TextColor: #fff;
@sapButton_Negative_Hover_Background: #e90b0b;
@sapButton_Negative_Hover_BorderColor: #e90b0b;
@sapButton_Negative_Hover_TextColor: #fff;
@sapButton_Negative_Active_Background: #d50a0a;
@sapButton_Negative_Active_BorderColor: #d50a0a;
@sapButton_Negative_Active_TextColor: #fff;
@sapButton_Critical_Background: #e76500;
@sapButton_Critical_BorderColor: #e76500;
@sapButton_Critical_TextColor: #fff;
@sapButton_Critical_Hover_Background: #dd6100;
@sapButton_Critical_Hover_BorderColor: #dd6100;
@sapButton_Critical_Hover_TextColor: #fff;
@sapButton_Critical_Active_Background: #d35c00;
@sapButton_Critical_Active_BorderColor: #d35c00;
@sapButton_Critical_Active_TextColor: #fff;
@sapButton_Success_Background: #30914c;
@sapButton_Success_BorderColor: #30914c;
@sapButton_Success_TextColor: #fff;
@sapButton_Success_Hover_Background: #2c8646;
@sapButton_Success_Hover_BorderColor: #2c8646;
@sapButton_Success_Hover_TextColor: #fff;
@sapButton_Success_Active_Background: #287a40;
@sapButton_Success_Active_BorderColor: #287a40;
@sapButton_Success_Active_TextColor: #fff;
@sapButton_Information_Background: #0070f2;
@sapButton_Information_BorderColor: #0070f2;
@sapButton_Information_TextColor: #fff;
@sapButton_Information_Hover_Background: #0064d9;
@sapButton_Information_Hover_BorderColor: #0064d9;
@sapButton_Information_Hover_TextColor: #fff;
@sapButton_Information_Active_Background: #0058bf;
@sapButton_Information_Active_BorderColor: #0058bf;
@sapButton_Information_Active_TextColor: #fff;
@sapButton_Neutral_Background: #788fa6;
@sapButton_Neutral_BorderColor: #788fa6;
@sapButton_Neutral_TextColor: #fff;
@sapButton_Neutral_Hover_Background: #637d97;
@sapButton_Neutral_Hover_BorderColor: #637d97;
@sapButton_Neutral_Hover_TextColor: #fff;
@sapButton_Neutral_Active_Background: #5d758e;
@sapButton_Neutral_Active_BorderColor: #5d758e;
@sapButton_Neutral_Active_TextColor: #fff;
@sapButton_Track_Background: #788fa6;
@sapButton_Track_BorderColor: #788fa6;
@sapButton_Track_TextColor: #fff;
@sapButton_Track_Hover_Background: #637d97;
@sapButton_Track_Hover_BorderColor: #637d97;
@sapButton_Track_Selected_Background: #0070f2;
@sapButton_Track_Selected_BorderColor: #0070f2;
@sapButton_Track_Selected_TextColor: #fff;
@sapButton_Track_Selected_Hover_Background: #0064d9;
@sapButton_Track_Selected_Hover_BorderColor: #0064d9;
@sapButton_Handle_Background: #fff;
@sapButton_Handle_BorderColor: #fff;
@sapButton_Handle_TextColor: #1d2d3e;
@sapButton_Handle_Hover_Background: #fff;
@sapButton_Handle_Hover_BorderColor: rgba(255,255,255,0.5);
@sapButton_Handle_Selected_Background: #fff;
@sapButton_Handle_Selected_BorderColor: #fff;
@sapButton_Handle_Selected_TextColor: #0070f2;
@sapButton_Handle_Selected_Hover_Background: #fff;
@sapButton_Handle_Selected_Hover_BorderColor: rgba(255,255,255,0.5);
@sapButton_Track_Negative_Background: #f53232;
@sapButton_Track_Negative_BorderColor: #f53232;
@sapButton_Track_Negative_TextColor: #fff;
@sapButton_Track_Negative_Hover_Background: #e90b0b;
@sapButton_Track_Negative_Hover_BorderColor: #e90b0b;
@sapButton_Handle_Negative_Background: #fff;
@sapButton_Handle_Negative_BorderColor: #fff;
@sapButton_Handle_Negative_TextColor: #aa0808;
@sapButton_Handle_Negative_Hover_Background: #fff;
@sapButton_Handle_Negative_Hover_BorderColor: rgba(255,255,255,0.5);
@sapButton_Track_Positive_Background: #30914c;
@sapButton_Track_Positive_BorderColor: #30914c;
@sapButton_Track_Positive_TextColor: #fff;
@sapButton_Track_Positive_Hover_Background: #2c8646;
@sapButton_Track_Positive_Hover_BorderColor: #2c8646;
@sapButton_Handle_Positive_Background: #fff;
@sapButton_Handle_Positive_BorderColor: #fff;
@sapButton_Handle_Positive_TextColor: #256f3a;
@sapButton_Handle_Positive_Hover_Background: #fff;
@sapButton_Handle_Positive_Hover_BorderColor: rgba(255,255,255,0.5);
@sapButton_TokenBackground: #fff;
@sapButton_TokenBorderColor: #758ca4;
@sapField_Background: #eff1f2;
@sapField_BackgroundStyle: 0 100% / 100% .0625rem no-repeat linear-gradient(0deg, #556b81, #556b81) border-box;
@sapField_TextColor: #131e29;
@sapField_PlaceholderTextColor: #556b82;
@sapField_BorderColor: #556b81;
@sapField_HelpBackground: #eff1f2;
@sapField_BorderWidth: .0625rem;
@sapField_BorderStyle: none;
@sapField_BorderCornerRadius: .25rem;
@sapField_Hover_Background: #fff;
@sapField_Hover_BackgroundStyle: 0 100% / 100% .0625rem no-repeat linear-gradient(0deg, #556b81, #556b81) border-box;
@sapField_Hover_BorderColor: #556b81;
@sapField_Hover_HelpBackground: #fff;
@sapField_Active_BorderColor: #0070f2;
@sapField_Focus_Background: #fff;
@sapField_Focus_BorderColor: #0070f2;
@sapField_Focus_HelpBackground: #fff;
@sapField_ReadOnly_Background: transparent;
@sapField_ReadOnly_BackgroundStyle: 0 100% / 0.375rem .0625rem repeat-x linear-gradient(90deg, #556b81 0, #556b81 0.25rem, transparent 0.25rem) border-box;
@sapField_ReadOnly_BorderColor: #556b81;
@sapField_ReadOnly_BorderStyle: none;
@sapField_ReadOnly_HelpBackground: transparent;
@sapField_RequiredColor: #ba066c;
@sapField_InvalidColor: #f53232;
@sapField_InvalidBackground: #ffeaf4;
@sapField_InvalidBackgroundStyle: 0 100% / 100% .125rem no-repeat linear-gradient(0deg, #f53232, #f53232) border-box;
@sapField_InvalidBorderWidth: .125rem;
@sapField_InvalidBorderStyle: none;
@sapField_WarningColor: #e76500;
@sapField_WarningBackground: #fff8d6;
@sapField_WarningBackgroundStyle: 0 100% / 100% .125rem no-repeat linear-gradient(0deg, #e76500, #e76500) border-box;
@sapField_WarningBorderWidth: .125rem;
@sapField_WarningBorderStyle: none;
@sapField_SuccessColor: #30914c;
@sapField_SuccessBackground: #f5fae5;
@sapField_SuccessBackgroundStyle: 0 100% / 100% .0625rem no-repeat linear-gradient(0deg, #30914c, #30914c) border-box;
@sapField_SuccessBorderWidth: .0625rem;
@sapField_SuccessBorderStyle: none;
@sapField_InformationColor: #0070f2;
@sapField_InformationBackground: #e1f4ff;
@sapField_InformationBackgroundStyle: 0 100% / 100% .125rem no-repeat linear-gradient(0deg, #0070f2, #0070f2) border-box;
@sapField_InformationBorderWidth: .125rem;
@sapField_InformationBorderStyle: none;
@sapGroup_TitleBackground: #fff;
@sapGroup_TitleBorderColor: #a8b2bd;
@sapGroup_TitleTextColor: #1d2d3e;
@sapGroup_Title_FontSize: 1rem;
@sapGroup_ContentBackground: #fff;
@sapGroup_ContentBorderColor: #d9d9d9;
@sapGroup_BorderWidth: .0625rem;
@sapGroup_BorderCornerRadius: .5rem;
@sapGroup_FooterBackground: transparent;
@sapPopover_BorderCornerRadius: .5rem;
@sapToolbar_Background: #fff;
@sapToolbar_SeparatorColor: #d9d9d9;
@sapList_HeaderBackground: #fff;
@sapList_HeaderBorderColor: #a8b2bd;
@sapList_HeaderTextColor: #1d2d3e;
@sapList_BorderColor: #e5e5e5;
@sapList_TextColor: #1d2d3e;
@sapList_Active_TextColor: #1d2d3e;
@sapList_BorderWidth: .0625rem;
@sapList_Active_Background: #dee2e5;
@sapList_SelectionBackgroundColor: #ebf8ff;
@sapList_SelectionBorderColor: #0070f2;
@sapList_Hover_SelectionBackground: #dcf3ff;
@sapList_Background: #fff;
@sapList_Hover_Background: #eaecee;
@sapList_AlternatingBackground: #f5f6f7;
@sapList_GroupHeaderBackground: #fff;
@sapList_GroupHeaderBorderColor: #a8b2bd;
@sapList_GroupHeaderTextColor: #1d2d3e;
@sapList_FooterBackground: #fff;
@sapList_FooterTextColor: #1d2d3e;
@sapList_TableGroupHeaderBackground: #eff1f2;
@sapList_TableGroupHeaderBorderColor: #a8b2bd;
@sapList_TableGroupHeaderTextColor: #1d2d3e;
@sapList_TableFooterBorder: #a8b2bd;
@sapList_TableFixedBorderColor: #8c8c8c;
@sapMessage_ErrorBorderColor: #ff8ec4;
@sapMessage_WarningBorderColor: #ffe770;
@sapMessage_SuccessBorderColor: #cee67e;
@sapMessage_InformationBorderColor: #7bcfff;
@sapProgress_Background: #d5dadd;
@sapProgress_BorderColor: #d5dadd;
@sapProgress_TextColor: #1d2d3e;
@sapProgress_FontSize: .875rem;
@sapProgress_NegativeBackground: #ffdbec;
@sapProgress_NegativeBorderColor: #ffdbec;
@sapProgress_NegativeTextColor: #1d2d3e;
@sapProgress_CriticalBackground: #fff4bd;
@sapProgress_CriticalBorderColor: #fff4bd;
@sapProgress_CriticalTextColor: #1d2d3e;
@sapProgress_PositiveBackground: #e5f2ba;
@sapProgress_PositiveBorderColor: #e5f2ba;
@sapProgress_PositiveTextColor: #1d2d3e;
@sapProgress_InformationBackground: #cdedff;
@sapProgress_InformationBorderColor: #cdedff;
@sapProgress_InformationTextColor: #1d2d3e;
@sapProgress_Value_Background: #788fa6;
@sapProgress_Value_BorderColor: #d5dadd;
@sapProgress_Value_TextColor: #788fa6;
@sapProgress_Value_NegativeBackground: #f53232;
@sapProgress_Value_NegativeBorderColor: #f53232;
@sapProgress_Value_NegativeTextColor: #f53232;
@sapProgress_Value_CriticalBackground: #e76500;
@sapProgress_Value_CriticalBorderColor: #e76500;
@sapProgress_Value_CriticalTextColor: #e76500;
@sapProgress_Value_PositiveBackground: #30914c;
@sapProgress_Value_PositiveBorderColor: #30914c;
@sapProgress_Value_PositiveTextColor: #30914c;
@sapProgress_Value_InformationBackground: #0070f2;
@sapProgress_Value_InformationBorderColor: #0070f2;
@sapProgress_Value_InformationTextColor: #0070f2;
@sapScrollBar_FaceColor: #7b91a8;
@sapScrollBar_TrackColor: #fff;
@sapScrollBar_BorderColor: #7b91a8;
@sapScrollBar_SymbolColor: #1d2d3e;
@sapScrollBar_Dimension: .75rem;
@sapScrollBar_Hover_FaceColor: #6f87a0;
@sapSlider_Background: #d5dadd;
@sapSlider_BorderColor: #d5dadd;
@sapSlider_Selected_Background: #0070f2;
@sapSlider_Selected_BorderColor: #0070f2;
@sapSlider_HandleBackground: #fff;
@sapSlider_HandleBorderColor: #fff;
@sapSlider_RangeHandleBackground: #fff;
@sapSlider_Hover_HandleBackground: #fff;
@sapSlider_Hover_HandleBorderColor: #0070f2;
@sapSlider_Hover_RangeHandleBackground: transparent;
@sapSlider_Active_HandleBackground: #fff;
@sapSlider_Active_HandleBorderColor: #fff;
@sapSlider_Active_RangeHandleBackground: transparent;
@sapPageHeader_Background: #fff;
@sapPageHeader_BorderColor: #d9d9d9;
@sapPageHeader_TextColor: #1d2d3e;
@sapPageFooter_Background: #fff;
@sapPageFooter_BorderColor: #d9d9d9;
@sapPageFooter_TextColor: #1d2d3e;
@sapInfobar_Background: #c2fcee;
@sapInfobar_Hover_Background: #fff;
@sapInfobar_Active_Background: #fff;
@sapInfobar_NonInteractive_Background: #f5f6f7;
@sapInfobar_TextColor: #046c7a;
@sapObjectHeader_Background: #fff;
@sapObjectHeader_BorderColor: #d9d9d9;
@sapObjectHeader_Hover_Background: #eaecee;
@sapObjectHeader_Title_TextColor: #1d2d3e;
@sapObjectHeader_Title_FontSize: 2rem;
@sapObjectHeader_Title_SnappedFontSize: 1.25rem;
@sapObjectHeader_Title_FontFamily: "72Black", "72", "72full", Arial, Helvetica, sans-serif;
@sapObjectHeader_Subtitle_TextColor: #556b82;
@sapBlockLayer_Background: #000;
@sapTile_Background: #fff;
@sapTile_Hover_Background: #fff;
@sapTile_Active_Background: #f5f5f5;
@sapTile_BorderColor: transparent;
@sapTile_BorderCornerRadius: 1rem;
@sapTile_TitleTextColor: #1d2d3e;
@sapTile_TextColor: #556b82;
@sapTile_IconColor: #556b82;
@sapTile_SeparatorColor: #ccc;
@sapTile_Interactive_BorderColor: #b3b3b3;
@sapTile_OverlayBackground: rgba(255,255,255,0.96);
@sapTile_OverlayForegroundColor: #1d2d3e;
@sapAccentColor1: #d27700;
@sapAccentColor2: #aa0808;
@sapAccentColor3: #ba066c;
@sapAccentColor4: #a100c2;
@sapAccentColor5: #5d36ff;
@sapAccentColor6: #0057d2;
@sapAccentColor7: #046c7a;
@sapAccentColor8: #256f3a;
@sapAccentColor9: #6c32a9;
@sapAccentColor10: #5b738b;
@sapAccentBackgroundColor1: #fff3b8;
@sapAccentBackgroundColor2: #ffd0e7;
@sapAccentBackgroundColor3: #ffdbe7;
@sapAccentBackgroundColor4: #ffdcf3;
@sapAccentBackgroundColor5: #ded3ff;
@sapAccentBackgroundColor6: #d1efff;
@sapAccentBackgroundColor7: #c2fcee;
@sapAccentBackgroundColor8: #ebf5cb;
@sapAccentBackgroundColor9: #ddccf0;
@sapAccentBackgroundColor10: #eaecee;
@sapLegend_WorkingBackground: #fff;
@sapLegend_NonWorkingBackground: #ebebeb;
@sapLegend_CurrentDateTime: #a100c2;
@sapLegendColor1: #c35500;
@sapLegendColor2: #d23a0a;
@sapLegendColor3: #df1278;
@sapLegendColor4: #840606;
@sapLegendColor5: #cc00dc;
@sapLegendColor6: #0057d2;
@sapLegendColor7: #07838f;
@sapLegendColor8: #188918;
@sapLegendColor9: #5b738b;
@sapLegendColor10: #7800a4;
@sapLegendColor11: #a93e00;
@sapLegendColor12: #aa2608;
@sapLegendColor13: #ba066c;
@sapLegendColor14: #8d2a00;
@sapLegendColor15: #4e247a;
@sapLegendColor16: #002a86;
@sapLegendColor17: #035663;
@sapLegendColor18: #1e592f;
@sapLegendColor19: #1a4796;
@sapLegendColor20: #470ced;
@sapLegendBackgroundColor1: #ffef9f;
@sapLegendBackgroundColor2: #feeae1;
@sapLegendBackgroundColor3: #fbf6f8;
@sapLegendBackgroundColor4: #fbebeb;
@sapLegendBackgroundColor5: #ffe5fe;
@sapLegendBackgroundColor6: #d1efff;
@sapLegendBackgroundColor7: #c2fcee;
@sapLegendBackgroundColor8: #f5fae5;
@sapLegendBackgroundColor9: #f5f6f7;
@sapLegendBackgroundColor10: #fff0fa;
@sapLegendBackgroundColor11: #fff8d6;
@sapLegendBackgroundColor12: #fff6f6;
@sapLegendBackgroundColor13: #f7ebef;
@sapLegendBackgroundColor14: #f1ecd5;
@sapLegendBackgroundColor15: #f0e7f8;
@sapLegendBackgroundColor16: #ebf8ff;
@sapLegendBackgroundColor17: #dafdf5;
@sapLegendBackgroundColor18: #ebf5cb;
@sapLegendBackgroundColor19: #fafdff;
@sapLegendBackgroundColor20: #eceeff;
@sapChart_OrderedColor_1: #1b90ff;
@sapChart_OrderedColor_2: #f26018;
@sapChart_OrderedColor_3: #179c6f;
@sapChart_OrderedColor_4: #f62866;
@sapChart_OrderedColor_5: #863fd5;
@sapChart_OrderedColor_6: #04a29d;
@sapChart_OrderedColor_7: #0070f2;
@sapChart_OrderedColor_8: #cc00dc;
@sapChart_OrderedColor_9: #8396a8;
@sapChart_OrderedColor_10: #f54747;
@sapChart_OrderedColor_11: #20578c;
@sapChart_Bad: #f53232;
@sapChart_Critical: #e76500;
@sapChart_Good: #30914c;
@sapChart_Neutral: #788fa6;
@sapChart_Sequence_1: #1b90ff;
@sapChart_Sequence_2: #f26018;
@sapChart_Sequence_3: #179c6f;
@sapChart_Sequence_4: #f62866;
@sapChart_Sequence_5: #863fd5;
@sapChart_Sequence_6: #04a29d;
@sapChart_Sequence_7: #0070f2;
@sapChart_Sequence_8: #cc00dc;
@sapChart_Sequence_9: #8396a8;
@sapChart_Sequence_10: #f54747;
@sapChart_Sequence_11: #20578c;
@sapChart_Sequence_Neutral: #788fa6;
@sapFontUrl_SAP-icons_ttf: url('../../../Base/baseLib/sap_horizon/fonts/SAP-icons.ttf');
@sapFontUrl_SAP-icons_woff: url('../../../Base/baseLib/sap_horizon/fonts/SAP-icons.woff');
@sapFontUrl_SAP-icons_woff2: url('../../../Base/baseLib/sap_horizon/fonts/SAP-icons.woff2');
@sapFontUrl_SAP-icons-TNT_ttf: url('../../../Base/baseLib/baseTheme/fonts/SAP-icons-TNT.ttf');
@sapFontUrl_SAP-icons-TNT_woff: url('../../../Base/baseLib/baseTheme/fonts/SAP-icons-TNT.woff');
@sapFontUrl_SAP-icons-TNT_woff2: url('../../../Base/baseLib/baseTheme/fonts/SAP-icons-TNT.woff2');
@sapFontUrl_SAP-icons-Business-Suite_ttf: url('../../../Base/baseLib/baseTheme/fonts/BusinessSuiteInAppSymbols.ttf');
@sapFontUrl_SAP-icons-Business-Suite_woff: url('../../../Base/baseLib/baseTheme/fonts/BusinessSuiteInAppSymbols.woff');
@sapFontUrl_SAP-icons-Business-Suite_woff2: url('../../../Base/baseLib/baseTheme/fonts/BusinessSuiteInAppSymbols.woff2');
@sapFontUrl_72_Regular_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Regular.woff2');
@sapFontUrl_72_Regular_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Regular.woff');
@sapFontUrl_72_Regular_full_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Regular-full.woff2');
@sapFontUrl_72_Regular_full_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Regular-full.woff');
@sapFontUrl_72_Bold_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Bold.woff2');
@sapFontUrl_72_Bold_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Bold.woff');
@sapFontUrl_72_Bold_full_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Bold-full.woff2');
@sapFontUrl_72_Bold_full_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Bold-full.woff');
@sapFontUrl_72_Semibold_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Semibold.woff2');
@sapFontUrl_72_Semibold_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Semibold.woff');
@sapFontUrl_72_Semibold_full_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Semibold-full.woff2');
@sapFontUrl_72_Semibold_full_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Semibold-full.woff');
@sapFontUrl_72_SemiboldDuplex_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-SemiboldDuplex.woff2');
@sapFontUrl_72_SemiboldDuplex_woff: url('../../../Base/baseLib/baseTheme/fonts/72-SemiboldDuplex.woff');
@sapFontUrl_72_SemiboldDuplex_full_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-SemiboldDuplex-full.woff2');
@sapFontUrl_72_SemiboldDuplex_full_woff: url('../../../Base/baseLib/baseTheme/fonts/72-SemiboldDuplex-full.woff');
@sapFontUrl_72_Light_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Light.woff2');
@sapFontUrl_72_Light_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Light.woff');
@sapFontUrl_72_Light_full_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Light-full.woff2');
@sapFontUrl_72_Light_full_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Light-full.woff');
@sapFontUrl_72_Black_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Black.woff2');
@sapFontUrl_72_Black_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Black.woff');
@sapFontUrl_72_Black_full_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Black-full.woff2');
@sapFontUrl_72_Black_full_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Black-full.woff');
@sapFontUrl_72_BoldItalic_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-BoldItalic.woff2');
@sapFontUrl_72_BoldItalic_woff: url('../../../Base/baseLib/baseTheme/fonts/72-BoldItalic.woff');
@sapFontUrl_72_BoldItalic_full_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-BoldItalic-full.woff2');
@sapFontUrl_72_BoldItalic_full_woff: url('../../../Base/baseLib/baseTheme/fonts/72-BoldItalic-full.woff');
@sapFontUrl_72_Condensed_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Condensed.woff2');
@sapFontUrl_72_Condensed_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Condensed.woff');
@sapFontUrl_72_Condensed_full_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Condensed-full.woff2');
@sapFontUrl_72_Condensed_full_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Condensed-full.woff');
@sapFontUrl_72_CondensedBold_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-CondensedBold.woff2');
@sapFontUrl_72_CondensedBold_woff: url('../../../Base/baseLib/baseTheme/fonts/72-CondensedBold.woff');
@sapFontUrl_72_CondensedBold_full_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-CondensedBold-full.woff2');
@sapFontUrl_72_CondensedBold_full_woff: url('../../../Base/baseLib/baseTheme/fonts/72-CondensedBold-full.woff');
@sapFontUrl_72_Italic_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Italic.woff2');
@sapFontUrl_72_Italic_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Italic.woff');
@sapFontUrl_72_Italic_full_woff2: url('../../../Base/baseLib/baseTheme/fonts/72-Italic-full.woff2');
@sapFontUrl_72_Italic_full_woff: url('../../../Base/baseLib/baseTheme/fonts/72-Italic-full.woff');
@sapFontUrl_72Mono_Regular_woff2: url('../../../Base/baseLib/baseTheme/fonts/72Mono-Regular.woff2');
@sapFontUrl_72Mono_Regular_woff: url('../../../Base/baseLib/baseTheme/fonts/72Mono-Regular.woff');
@sapFontUrl_72Mono_Regular_full_woff2: url('../../../Base/baseLib/baseTheme/fonts/72Mono-Regular-full.woff2');
@sapFontUrl_72Mono_Regular_full_woff: url('../../../Base/baseLib/baseTheme/fonts/72Mono-Regular-full.woff');
@sapFontUrl_72Mono_Bold_woff2: url('../../../Base/baseLib/baseTheme/fonts/72Mono-Bold.woff2');
@sapFontUrl_72Mono_Bold_woff: url('../../../Base/baseLib/baseTheme/fonts/72Mono-Bold.woff');
@sapFontUrl_72Mono_Bold_full_woff2: url('../../../Base/baseLib/baseTheme/fonts/72Mono-Bold-full.woff2');
@sapFontUrl_72Mono_Bold_full_woff: url('../../../Base/baseLib/baseTheme/fonts/72Mono-Bold-full.woff');