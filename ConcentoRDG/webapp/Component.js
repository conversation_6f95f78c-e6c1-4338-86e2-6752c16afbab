sap.ui.define([
	"sap/ui/core/UIComponent",
	"sap/ui/Device",
	"dmr/mdg/supernova/SupernovaFJ/model/models",
	"sap/ui/core/IconPool"
], function (UIComponent, Device, models, IconPool) {
	"use strict";

	return UIComponent.extend("dmr.mdg.supernova.SupernovaFJ.Component", {

		metadata: {
			manifest: "json"
		},

		/**
		 * The component is initialized by UI5 automatically during the startup of the app and calls the init method once.
		 * @public
		 * @override
		 */
		init: function () {
			// call the base component's init function
			UIComponent.prototype.init.apply(this, arguments);

			// enable routing
			this.getRouter().initialize();

			// set the device model
			this.setModel(models.createDeviceModel(), "device");

			// DISABLING THIS ICON GROUP TO REDUCE LOAD IMPACT
			// //Fiori Theme font family and URI
			// let sapIconsTnt = {
			// 	fontFamily: "SAP-icons-TNT",
			// 	fontURI: sap.ui.require.toUrl("sap/tnt/themes/base/fonts/")
			// };
			// //Registering to the icon pool
			// IconPool.registerFont(sapIconsTnt);

			//SAP Business Suite Theme font family and URI
			let businessSuiteInAppSymbols = {
				fontFamily: "BusinessSuiteInAppSymbols",
				fontURI: sap.ui.require.toUrl("sap/ushell/themes/base/fonts/")
			};
			//Registering to the icon pool
			IconPool.registerFont(businessSuiteInAppSymbols);
		}
	});
});