sap.ui.define([
	"sap/ui/core/UIComponent",
	"sap/ui/model/json/JSONModel",
	"sap/ui/core/BusyIndicator",
	"sap/suite/ui/generic/template/extensionAPI/ReuseComponentSupport"
], function(UIComponent, JSONModel, BusyIndicator, ReuseComponentSupport){
	"use strict";
	let Component = UIComponent.extend("dmr.components.SearchList.Component", {
		metadata: {
			manifest: "json",
			// "abstract": false, 
			properties: {
				// The data that will be displayed in the list 
				listData: {type: "array", defaultValue: [], name: "listData"},
				// mapping : The name mapping for the title, description and info 
				dataMapping: {type: "object", defaultValue: {
					"title": "title",
					"description": "description",
					"info": "info",
					"infoState": "infoState"
				}}, 
				// Header to be displayed for the list 
				headerText: {type: "string", defaultValue: ""},
				// The data path to be used for group. If not specified, grouping is not enabled 
				listGroupingPath: {type: "string", defaultValue: undefined}, 
				// The data path to use for the unread display 
				unreadPath: {type: "string", defaultValue: undefined},
				// Setting these parameters will enable the action button and make it visible
				buttonInfo: { type: "object", defaultValue: {
					icon: undefined,
					toolTip: undefined
				} },
				// Setting these parameters will enable the second action button and make it visible
				secondButtonInfo: { type: "object", defaultValue: {
					icon: undefined,
					toolTip: undefined
				} }
			},
			aggregations: {},
			events: {
				// Selection Change event 
				selectionChange: {
					parameters: {
						selectedItem: {type: "object"}
					}
				}, 
				actionPressed: {
					parameter: {
						
					}
				},
				secondActionPressed: {
					parameter: {
						
					}
				}
			}
		}
	});

	/**
	 * Get selected item title 
	 */
	Component.prototype.getSelectedItemTitle = function(){
		let oSelectedItem = this._searchList.getSelectedItem();
		if(oSelectedItem) {
			return oSelectedItem.getTitle();	
		} else {
			return undefined;
		}
		
	};
	
	/**
	 * Set selected item by title 
	 */
	Component.prototype.setSelectedItemByTitle = function(sTitle) {
		// Get the list of items from the list 
		let arrListItem = this._searchList.getItems();
		
		// Search for the item with the specified title 
		let aSelectedItems = arrListItem.filter((item) => item.getTitle() === sTitle);
		
		if (aSelectedItems.length > 0) {
			aSelectedItems.forEach(oSelectedItem => {
				this._searchList.setSelectedItem(oSelectedItem);
				// Fire the event to the calling component
				this.fireSelectionChange({
					selectedItem: oSelectedItem,
					data: oSelectedItem ? oSelectedItem.getBindingContext("searchableListModel").getObject().data : undefined
				});
			});
		}

	};
	
	/**
	 * Set selected item by title and then by text
	 */
	Component.prototype.setSelectedItemByTitleText = function(sTitle, sText) {
		// Get the list of items from the list 
		let arrListItem = this._searchList.getItems();
		
		// Search for the item with the specified title 
		let oSelectedItem = arrListItem.find(function(item){
			if(item.getTitle() === sTitle && item.getDescription() === sText) {
				return true; 
			}
			return false;
		});
		
		this._searchList.setSelectedItem(oSelectedItem);

		// Fire the event to the calling component
		this.fireSelectionChange({
			selectedItem: oSelectedItem,
			data: oSelectedItem.getBindingContext("searchableListModel").getObject().data
		});
	};
	
	/**
	 * Remove selected item from the List
	 */
	Component.prototype.removeSelectedItem = function() {
		if(this._searchList.getProperty("mode") === "SingleSelectMaster") {
			// Get the current selected item from the list
			let oSelectedItem = this._searchList.getSelectedItem();
			
			if(oSelectedItem) {
				this._searchList.setSelectedItem(oSelectedItem, false);
			}
		}
	};
	
	/**
	 * Remove selected items from the List of mode MultiSelect
	 */
	Component.prototype.removeMultiSelectedItems = function() {
		if(this._searchList.getProperty("mode") === "MultiSelect") {
			// Get the current selected items from the list
			let oSelectedItems = this._searchList.getSelectedItems();
			if(oSelectedItems) {
				oSelectedItems.forEach(oSelectedItem => {
					this._searchList.setSelectedItem(oSelectedItem, false);
				});
				
			}
		}
		
	};
	
	/**
	 * Change mode of list
	 */
	Component.prototype.setMode = function(sMode) {
		if(sMode) {
			this._searchList.setProperty("mode", sMode);
		}
		
	};

	/**
	 * Change rememberSelections of list
	 */
	Component.prototype.setRememberSelections = function(sOption) {
		this._searchList.setProperty("rememberSelections", sOption);
	};
	
	/**
	 * Returns all the selected items for Multi Select List.
	 * To be used for Multi Select List. If List is not of mode MultiSelect, returns empty array
	 */
	Component.prototype.getMultiSelectedItems = function() {
		let oComponent = this;
		let arrItems = [];
		if(oComponent._searchList.getProperty("mode") === "MultiSelect") {
			let arrSelectedItems = this._searchList.getSelectedItems();
			arrSelectedItems.forEach(oSelectedItem => {
				let oItemBinding = this.getModel("searchableListModel").getProperty(oSelectedItem.getBindingContextPath());
				if(oItemBinding) {
					arrItems.push(oItemBinding);
				}
			});
		}
		return arrItems;
	};
	 
	/**
	 * Just create the base information and the model
	 */
	Component.prototype.init = function(){
		// Call the parent init
		UIComponent.prototype.init.apply(this, arguments);
		
	    //Transform this component into a reuse component for Fiori Elements:
	    ReuseComponentSupport.mixInto(this);
	};
	
	/**
	 * Return the header text 
	 */
	Component.prototype.getHeaderText = function(){
		
		return this.getProperty("headerText");	
	}; 
	
	/**
	 * Set the header text 
	 */
	Component.prototype.setHeaderText = function(sHeaderText){
		this.setProperty("headerText", sHeaderText);
		
		// set the list header text 
		this._searchList.setHeaderText(sHeaderText);
		this._searchList.setNoDataText("No " + sHeaderText + " available for selection");
		this._searchField.setPlaceholder("Search " + sHeaderText);
	}; 
	
	/**
	 * Get action button info
	 */
	Component.prototype.getButtonInfo = function(){
		return this.getProperty("buttonInfo");
	};

	/**
	 * Get second action button info
	 */
	 Component.prototype.getSecondButtonInfo = function(){
		return this.getProperty("seconfButtonInfo");
	};
	
	/**
	 * Set action button info 
	 */
	Component.prototype.setButtonInfo = function(oButtonInfo){
		this.setProperty("buttonInfo", oButtonInfo);
		
		this._actionButton.setVisible(false);
		// Update the button parameters
		if(oButtonInfo.icon)
		{
			this._actionButton.setIcon(oButtonInfo.icon);
			// Make the button visible only if the icon is provided. NO ICON, NO BUTTON
			this._actionButton.setVisible(true);
			// Attach the event handler 
			this._actionButton.attachPress(this.onActionButtonPress, this);
		}
		
		if(oButtonInfo.toolTip)
		{
			this._actionButton.setTooltip(oButtonInfo.toolTip);
		}
	};

	/**
	 * Task 12413 - Filter rules, field properties if any rules are created for the crtype( UI5 Only task )
	 * Update action button info
	 */
	Component.prototype.updateButtonInfo = function(oButtonInfo){
		if (oButtonInfo.icon) {
			this._actionButton.setIcon(oButtonInfo.icon);
		}
		if (oButtonInfo.toolTip) {
			this._actionButton.setTooltip(oButtonInfo.toolTip);
		}
	};

	/**
	 * Task 12413 - Filter rules, field properties if any rules are created for the crtype( UI5 Only task )
	 * Get Tool tip
	 */
	Component.prototype.getTooltip = function(){
		return this._actionButton.getTooltip();
	};

	/**
	 * Set second action button info 
	 */
	 Component.prototype.setSecondButtonInfo = function(oButtonInfo){
		this.setProperty("secondButtonInfo", oButtonInfo);

		this._secondActionButton.setVisible(false);
		// Update the button parameters
		if(oButtonInfo.icon)
		{
			this._secondActionButton.setIcon(oButtonInfo.icon);
			// Make the button visible only if the icon is provided. NO ICON, NO BUTTON
			this._secondActionButton.setVisible(true);
			// Attach the event handler 
			this._secondActionButton.attachPress(this.onSecondActionButtonPress, this);
		}
		
		if(oButtonInfo.toolTip)
		{
			this._secondActionButton.setTooltip(oButtonInfo.toolTip);
		}

		
	}; 
	
	/**
	 * Return the list group path
	 */
	Component.prototype.getListGroupingPath = function(){
		return this.getProperty("listGroupingPath");
	};
	
	/**
	 * Set the list group path
	 */
	Component.prototype.setListGroupingPath = function(sGroupingPath){
		this.setProperty("listGroupingPath", sGroupingPath);
		
		// Update the list binding info
		this.setListAggregationBinding(this);
	};
	
	/** 
	 * Set the list unread mapping. Store the setting parameter and also enable the setting on the list 
	 */ 
	Component.prototype.setUnreadPath = function(sUnreadPath)
	{
		this.setProperty("unreadPath", sUnreadPath);
		
		this._searchList.setShowUnread((sUnreadPath && sUnreadPath.length > 0)?true: false);
	};
	
	/**
	 * Return the list data that was set 
	 */
	Component.prototype.getListData = function(){
		return this.getProperty("listData");
	};
	
	/**
	 * When the data is set from the called, loop through the data and assign the data into a 
	 * format readable by the list component. 
	 * All data must be assigned either to title, description or info 
	 */
	Component.prototype.setListData = function(arrListData)
	{
		this.setProperty("listData", arrListData);

		let arrMappedListData = []; 
		
		// Clear the list data if any and also the selections of the mode is MultiSelect 
		if(this._searchList.getProperty("mode") === "MultiSelect") {
			this._searchList.removeSelections(true);
		}
		this.getModel("searchableListModel").setProperty("/listData", arrMappedListData);
		this.getModel("searchableListModel").refresh();
		

		// if the data is not valid or if there are no items, return
		if(!arrListData || arrListData.length === 0) {
			return;
		}
		
		// get the mapping details 
		let oMappingDetails = this.getDataMapping();
		let sTitleMapping = oMappingDetails.title;
		let sDescriptionMapping = oMappingDetails.description;
		let sInfoMapping = oMappingDetails.info;
		let sInfoStateMapping = oMappingDetails.infoState;
		let sGroupBY = this.getListGroupingPath();
		let sUnreadPath = this.getUnreadPath();
		
		for(let i = 0; i < arrListData.length; i++)
		{
			let oListItem = {};
			if(sTitleMapping && arrListData[i][sTitleMapping])
			{
				oListItem.title = arrListData[i][sTitleMapping];
			}
			 
			if(sDescriptionMapping && arrListData[i][sDescriptionMapping])
			{
				oListItem.description = arrListData[i][sDescriptionMapping];
			}

			if(sInfoMapping && arrListData[i][sInfoMapping])
			{
				oListItem.info = arrListData[i][sInfoMapping];
			}

			if(sInfoStateMapping && arrListData[i][sInfoStateMapping])
			{
				oListItem.infoState = arrListData[i][sInfoStateMapping];
			}
			
			if(sGroupBY && arrListData[i][sGroupBY])
			{
				oListItem.groupBy = arrListData[i][sGroupBY];
			}
			
			if(sUnreadPath && arrListData[i][sUnreadPath])
			{
				oListItem.unread = arrListData[i][sUnreadPath];
			}
			
			// Store the complete array element so that the calling view may access it if needed
			oListItem.data = arrListData[i];
			arrMappedListData.push(oListItem);
		}
		
		// store the data to the model
		this.getModel("searchableListModel").setSizeLimit(arrMappedListData.length);
		this.getModel("searchableListModel").setProperty("/listData", arrMappedListData);
	};
	
	/**
	 * When action button is pressed fire the event
	 */
	Component.prototype.onActionButtonPress = function(){

		// Fire the event to the calling component
		this.fireActionPressed();
	};

	/**
	 * When second action button is pressed fire the event
	 */
	 Component.prototype.onSecondActionButtonPress = function(){

		// Fire the event to the calling component
		this.fireSecondActionPressed();
	};
	
	/**
	 * When a list item is selected trigger the event to the parent component
	 */
	 Component.prototype.onListItemSelected = function(oEvent){
		// Read the selected item name from the model 
		let oSelectedListItem = oEvent.getSource().getSelectedItem();
		
		if(oSelectedListItem) {
			// Fire the event to the calling component
			this.fireSelectionChange({
				selectedItem: oSelectedListItem,
				data: oSelectedListItem.getBindingContext("searchableListModel").getObject().data
			});	
		}

		
	 };
	
	/**
	 * Run when search criteria is enterd by the user
	 */
	 Component.prototype.onSearchLiveChange = function(oEvent){
		let sQuery = oEvent ? oEvent.getSource().getValue() : "";
		
		// Get the data for list and filter the aggregation
		let aFilters = [];
		this.removeSelectedItem();

		if (sQuery && sQuery.length > 0) {
			aFilters.push(					
				new sap.ui.model.Filter({
					path: "title", 
					operator: sap.ui.model.FilterOperator.Contains, 
					caseSensitive: false,
					value1: sQuery})
				);

			aFilters.push(					
				new sap.ui.model.Filter({
					path: "description", 
					operator: sap.ui.model.FilterOperator.Contains, 
					caseSensitive: false,
					value1: sQuery})
				);

			aFilters.push(					
				new sap.ui.model.Filter({
					path: "info", 
					operator: sap.ui.model.FilterOperator.Contains, 
					caseSensitive: false,
					value1: sQuery})
				);
		}
			
			let oFilter; 
			if( aFilters.length > 0 )
			{
				oFilter = new sap.ui.model.Filter({
					filters: aFilters, 
					and: false
				});
			}
			else
			{
				oFilter = aFilters;
			}
			
			this._searchList.getBinding("items").filter(oFilter);
	 };

	 /* Bug 11301 - Navigation to and from business rules and properties screens
	 	clearSearchField funtion is to be used when we want to clear the Search field value of the list
	  */
	 Component.prototype.clearSearchField = function(){
		this._searchField.setValue("");
		this.onSearchLiveChange(null);
	 };
	
	/**
	 * Set the list aggregation binding
	 */
	 Component.prototype.setListAggregationBinding = function(oComponent){
		let oSortNGroup = undefined;  
	 	
	 	if(oComponent.getListGroupingPath())
	 	{
			oSortNGroup = new sap.ui.model.Sorter({
				path: "groupBy",
				descending: false,
				group: function(oContext) { return oContext.getProperty("groupBy"); }
			});
	 	}
	 	
	 	oComponent._searchList.bindAggregation("items", {
			path: "searchableListModel>/listData",
			sorter: oSortNGroup,
			groupHeaderFactory: function(oGroup){
				let sGroupKey = "No Description";
				if(oGroup.key) {
					sGroupKey = oGroup.key;
				}
				let sGroupId = sGroupKey.replace(/ /g, "_");
		 		
		 		let oGroupHeaderListItem = new sap.m.GroupHeaderListItem({
					title: sGroupKey,
					id: "groupHeaderID_" + sGroupId,
					type: sap.m.ListType.Active,
					upperCase: false
				});
				oGroupHeaderListItem.attachPress(oComponent.groupHeaderPressed, oComponent);
				
				return oGroupHeaderListItem;
			},
			template: new sap.m.StandardListItem({
				type: sap.m.ListType.Active,
				wrapping: true, 
				unread: "{searchableListModel>unread}",
				title: "{searchableListModel>title}",
				description: "{searchableListModel>description}",
				info: "{searchableListModel>info}",
				infoState: "{= ${searchableListModel>infoState} ? ${searchableListModel>infoState} : 'None'}", 
				visible: "{= ${searchableListModel>/selectedHeaderTitle} === undefined || ${searchableListModel>/selectedHeaderTitle} === ${searchableListModel>data/" + oComponent.getListGroupingPath() + "} }"
			})
		});
	};
	 
	/**
	 * Store the title of the selected header into the model. The list items will be displayed 
	 * if the corresponding item in the item matches the selected header
	 */
	Component.prototype.groupHeaderPressed = function(oEvent){
		let oComponent = this;
		let sSelectedHeader = oEvent.getSource().getTitle(); 
	 	
	 	if(oComponent._searchList.getProperty("mode") === "MultiSelect") {
	 		let oListItems = oComponent._searchList.getItems();
	 		let sSelectAllFlag;
	 		oListItems.some((oListItem) => {
	 			let oItemBinding = this.getModel("searchableListModel").getProperty(oListItem.getBindingContextPath());
	 			if(oItemBinding) {
	 				if(oItemBinding.groupBy === sSelectedHeader) {
	 					if(oListItem.getSelected()) {
	 						sSelectAllFlag = "";
	 						return false;
	 					} else {
	 						sSelectAllFlag = "X";
	 						return true;
	 					}
	 				}
	 			}
	 			return false;
	 		});
	 		
	 		//If atleast one item is unselected for the group, select all items for the group. If all items are selected, then unselect all items for the group
 			oListItems.forEach((oListItem) => {
 				let oItemBinding = this.getModel("searchableListModel").getProperty(oListItem.getBindingContextPath());
 				if(oItemBinding) {
 					if(oItemBinding.groupBy === sSelectedHeader) {
 						if(sSelectAllFlag === "X") {
 							oListItem.setSelected(true);
 						} else if(sSelectAllFlag === "") {
 							oListItem.setSelected(false);
 						}
 					}
 				}
 			});
	 		
	 	} else {
			// Store the header title to the model 
			this.getModel("searchableListModel").setProperty("/selectedHeaderTitle", sSelectedHeader);
	 	}
	};
	
	/**
	 * Create the content. 
	 * > List with default settings and no bindings 
	 * > Include the toolbar 
	 *		> Search Box : VISIBLE [Cannot be hidden]
	 *		> action button : NOT VISIBLE BY DEFAULT
	 */
	Component.prototype.createContent = function() {

		this.setModel(new JSONModel(), "searchableListModel");
		
		// initialize the data 
		this.listData = [];
		this.headerText = undefined;
		this.buttonInfo = {};

		this._searchList = new sap.m.List({
			mode: sap.m.ListMode.SingleSelectMaster,
			sticky: [sap.m.Sticky.InfoToolbar, sap.m.Sticky.HeaderToolbar],
			rememberSelections: false, 
			growing: false, 
			growingScrollToLoad: true, 
			growingThreshold: 100, 
			select: this.onListItemSelected.bind(this)
		}); 
		
		this._searchList.setModel(this.getModel("searchableListModel"));
		this.setListAggregationBinding(this);
		
		// Create the infoToolBar
		let oToolbar = new sap.m.Toolbar({
			design: sap.m.ToolbarDesign.Transparent,
			style: sap.m.ToolbarStyle.Clear
		});
		
		// TO DO style class assignment for the tool bar
		oToolbar.addStyleClass("transparentBar");
	
		// Create search input for filtering 
		let oSearchField = new sap.m.SearchField({
			enabled: true,
			visible: true,
			placeholder: "Search",
			ariaLabelledBy: "Search",
			showSearchButton: false,
			liveChange: this.onSearchLiveChange.bind(this)
		});
		

		// Create the action button 
		let oActionButton = new sap.m.Button({
			id: this.createId("buttonIcon"),
			width: "3em",
			visible: false
		});

		// Create the second action button 
		let oSecondActionButton = new sap.m.Button({
			width: "3em",
			visible: false
		});
		
		// Add the components to the toolbar
		this._searchField = oSearchField;
		oToolbar.addContent(oSearchField);
		oToolbar.addContent(oActionButton);
		oToolbar.addContent(oSecondActionButton);           
		
		// Add the toolbar to the list infoToolbar aggregation
		this._searchList.setInfoToolbar(oToolbar);
		
		// store the action button handle for access
		this._actionButton = oActionButton;

		// store the second action button handle for access
		this._secondActionButton = oSecondActionButton;
		
		return this._searchList;
	};
	
	return Component;
});