{"_version": "1.9.0", "sap.app": {"id": "dmr.components.BADISelect", "type": "component", "i18n": "i18n/i18n.properties", "title": "{{title}}", "description": "{{description}}", "applicationVersion": {"version": "1.0.0"}, "dataSources": {"YGW_ZEUS_BADI_SEARCH_SRV": {"uri": "/sap/opu/odata/RDG/GW_ZEUS_BADI_SEARCH_SRV/", "type": "OData", "settings": {"localUri": "localService/YGW_ZEUS_BADI_SEARCH_SRV/metadata.xml"}}}}, "sap.ui": {"technology": "UI5", "icons": {"icon": "", "favIcon": "", "phone": "", "phone@2": "", "tablet": "", "tablet@2": ""}, "deviceTypes": {"desktop": true, "tablet": true, "phone": true}}, "sap.ui5": {"componentName": "dmr.components.BADISelect", "dependencies": {"minUI5Version": "1.74.0"}, "contentDensities": {"compact": true, "cozy": false}, "models": {"GET_BADI_LIST": {"type": "sap.ui.model.odata.v2.ODataModel", "settings": {"defaultOperationMode": "Server", "defaultBindingMode": "OneWay", "defaultCountMode": "Request"}, "dataSource": "YGW_ZEUS_BADI_SEARCH_SRV", "preload": false}}}}