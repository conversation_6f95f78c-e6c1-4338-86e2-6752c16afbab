<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="dmr.components"
	displayBlock="true">
	<Dialog title="Create BAdI Implementation" >
		<l:VerticalLayout class="sapUiContentPadding" width="100%">
			<VBox class="sapUiSmallMarginEnd">
				<Label text="BAdI Implementation:" labelFor="idBADITypeName"/>
				<Input 
					id="idBADITypeName"
					value="{BADIDialogModel>/BADICreation/BADIName}"
					valueState="{= ${BADIDialogModel>/BADICreation/ValidBADIName} ? 'None' : 'Error'}"
					valueStateText="BAdI Implementation name must start with Y or Z"
					liveChange=".onChangeBADIName"
					change=".onChangeBADIName"
					maxLength="25">
				</Input>
			</VBox>
		</l:VerticalLayout>
		<beginButton>
			<Button text="Create" press=".onPressCreateBADI" 
				enabled="{= ${BADIDialogModel>/BADICreation/ValidBADIName}}"
				id="saveCreate"/>
		</beginButton>
		<endButton>
			<Button text="Cancel" press=".onPressCancelCreateBADI" id="cancelCreate"/>
		</endButton>
	</Dialog>
</c:FragmentDefinition>