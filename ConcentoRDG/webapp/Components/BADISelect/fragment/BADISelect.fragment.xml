<c:FragmentDefinition 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form"
	controllerName="dmr.components"
	displayBlock="true">
	<Dialog title="Select BAdI Implementation">
		

		<f:SimpleForm id="formBADIUser" 
			visible="{= !${BADIDialogModel>/needFourEyesBadi} &amp;&amp; !${BADIDialogModel>/needDfpBadi} &amp;&amp; !${BADIDialogModel>/needDynamicBadi} 
						&amp;&amp; !${BADIDialogModel>/needParallelBadi} &amp;&amp; !${BADIDialogModel>/needSMethodBadi} &amp;&amp; !${BADIDialogModel>/needAgentBadi}}" 
			editable="true" width="100%" class="sapUiNoContentPadding">

			<Label  text="BAdI Implementation" labelFor="searchBADI"/>
			<HBox>
				<SearchField id="searchBADI" liveChange=".onSearchBADIList" />
				<Button width="3em"
					iconFirst="true" icon="sap-icon://add" tooltip="Create New BAdI Implementation" 
					press=".onBADISelectionCreateNew" id="idCreateNewBADI" class="sapUiSmallMarginBegin"/>
			</HBox>
		</f:SimpleForm>
		<MessageStrip
			text="Filter exceeds 1000 limit"
			type="Error"
			showIcon="true"
			class="sapUiTinyMarginBottom"
			visible="{= !${BADIDialogModel>/validBadi}}"
		/>
		<List id="BADIRequestList"
			visible="{= !${BADIDialogModel>/needFourEyesBadi} &amp;&amp; !${BADIDialogModel>/needDfpBadi} &amp;&amp; !${BADIDialogModel>/needDynamicBadi} 
						&amp;&amp; !${BADIDialogModel>/needParallelBadi} &amp;&amp; !${BADIDialogModel>/needSMethodBadi} &amp;&amp; !${BADIDialogModel>/needAgentBadi}}"
			items="{ path: 'BADIDialogModel>/list' }" 
			selectionChange=".onBADISelectionChange"
			mode="SingleSelectMaster" growing="true" growingThreshold="50"
			includeItemInSelection="true">
			<StandardListItem 
				title="{BADIDialogModel>BadiImpl}" info="Filter Count: {BADIDialogModel>FilterCondCount}"/>
		</List>

		<VBox alignItems="Center" class="sapUiTinyMarginTop">
			<VBox visible="{BADIDialogModel>/needFourEyesBadi}">
				<Label text="4 eyes BAdI Implementation " />
				<HBox width="100%" >
					<ComboBox id="idCbxFourEyesBadi" 
							items="{BADIDialogModel>/fourEyesBadiList}" 
							selectedKey="{BADIDialogModel>/selectedFourEyesBADIName}" 
							class="sapUiSmallMarginEnd" change=".onChangeBadiSelection"
							enabled="{BADIDialogModel>/enableFourEyes}"
							showSecondaryValues="true"
							valueStateText="Filter exceeds 1000 limit"
							selectionChange=".onChangeBadi">
						<c:ListItem key="{BADIDialogModel>BadiImpl}" text="{BADIDialogModel>BadiImpl}" additionalText="Filter Count: {BADIDialogModel>FilterCondCount}"/>
					</ComboBox>
					<Button id="btnCreateFourEyesBadi" enabled="{BADIDialogModel>/enableFourEyes}" icon="sap-icon://add" tooltip="Create New BAdI Implementation" press=".onBADISelectionCreateNew" />
				</HBox>
			</VBox>
			<VBox visible="{BADIDialogModel>/needDfpBadi}">
				<Label text="4 eyes Dfp BAdI Implementation" />
				<HBox width="100%" >
					<ComboBox id="idCbxDfpBadi" 
							items="{BADIDialogModel>/dfpBadiList}" 
							selectedKey="{BADIDialogModel>/selectedDfpBADIName}" 
							class="sapUiSmallMarginEnd" change=".onChangeBadiSelection"
							enabled="{BADIDialogModel>/enableDfp}"
							showSecondaryValues="true"
							valueStateText="Filter exceeds 1000 limit"
							selectionChange=".onChangeBadi">
						<c:ListItem key="{BADIDialogModel>BadiImpl}" text="{BADIDialogModel>BadiImpl}" additionalText="Filter Count: {BADIDialogModel>FilterCondCount}"/>
					</ComboBox>
					<Button id="btnCreateDfpBadi" enabled="{BADIDialogModel>/enableDfp}" icon="sap-icon://add" tooltip="Create New BAdI Implementation" press=".onBADISelectionCreateNew" />
				</HBox>
			</VBox>
			<VBox visible="{BADIDialogModel>/needDynamicBadi}">
				<Label text="Dynamic BAdI Implementation" />
				<HBox width="100%" >
					<ComboBox id="idCbxDynamicBadi" 
							items="{BADIDialogModel>/dynamicBadiList}" 
							selectedKey="{BADIDialogModel>/selectedDynamicBADIName}" 
							class="sapUiSmallMarginEnd" change=".onChangeBadiSelection"
							enabled="{BADIDialogModel>/enableDynamic}"
							showSecondaryValues="true"
							valueStateText="Filter exceeds 1000 limit"
							selectionChange=".onChangeBadi">
						<c:ListItem key="{BADIDialogModel>BadiImpl}" text="{BADIDialogModel>BadiImpl}" additionalText="Filter Count: {BADIDialogModel>FilterCondCount}"/>
					</ComboBox>
					<Button id="btnCreateDynamicBadi" enabled="{BADIDialogModel>/enableDynamic}" icon="sap-icon://add" tooltip="Create New BAdI Implementation" press=".onBADISelectionCreateNew" />
				</HBox>
			</VBox>
			<VBox visible="{BADIDialogModel>/needParallelBadi}">
				<Label text="Parallel BAdI Implementation" />
				<HBox width="100%" >
					<ComboBox id="idCbxParallelBadi" 
							items="{BADIDialogModel>/parallelBadiList}" 
							selectedKey="{BADIDialogModel>/selectedParallelBADIName}" 
							class="sapUiSmallMarginEnd" change=".onChangeBadiSelection"
							enabled="{BADIDialogModel>/enableParallel}"
							showSecondaryValues="true"
							valueStateText="Filter exceeds 1000 limit"
							selectionChange=".onChangeBadi">
						<c:ListItem key="{BADIDialogModel>BadiImpl}" text="{BADIDialogModel>BadiImpl}" additionalText="Filter Count: {BADIDialogModel>FilterCondCount}"/>
					</ComboBox>
					<Button id="btnCreateParallelBadi" enabled="{BADIDialogModel>/enableParallel}" icon="sap-icon://add" tooltip="Create New BAdI Implementation" press=".onBADISelectionCreateNew" />
				</HBox>
			</VBox>
			<VBox visible="{BADIDialogModel>/needSMethodBadi}">
				<Label text="System Method BAdI Implementation" />
				<HBox width="100%" >
					<ComboBox id="idCbxSystemMBadi" 
							items="{BADIDialogModel>/sMethodBadiList}" 
							selectedKey="{BADIDialogModel>/selectedSMethodBADIName}" 
							class="sapUiSmallMarginEnd" change=".onChangeBadiSelection"
							enabled="{BADIDialogModel>/enableSMethod}"
							showSecondaryValues="true"
							valueStateText="Filter exceeds 1000 limit"
							selectionChange=".onChangeBadi">
						<c:ListItem key="{BADIDialogModel>BadiImpl}" text="{BADIDialogModel>BadiImpl}" additionalText="Filter Count: {BADIDialogModel>FilterCondCount}"/>
					</ComboBox>
					<Button id="btnCreateSMethodBadi" enabled="{BADIDialogModel>/enableSMethod}" icon="sap-icon://add" tooltip="Create New BAdI Implementation" press=".onBADISelectionCreateNew" />
				</HBox>
			</VBox>
			<VBox visible="{BADIDialogModel>/needAgentBadi}">
				<Label text="Check Agent BAdI Implementation" />
				<HBox width="100%" >
					<ComboBox id="idCbxAgentBadi" 
							items="{BADIDialogModel>/agentBadiList}" 
							selectedKey="{BADIDialogModel>/selectedAgentBADIName}" 
							class="sapUiSmallMarginEnd" change=".onChangeBadiSelection"
							enabled="{BADIDialogModel>/enableAgent}"
							showSecondaryValues="true"
							valueStateText="Filter exceeds 1000 limit"
							selectionChange=".onChangeBadi">
						<c:ListItem key="{BADIDialogModel>BadiImpl}" text="{BADIDialogModel>BadiImpl}" additionalText="Filter Count: {BADIDialogModel>FilterCondCount}"/>
					</ComboBox>
					<Button id="btnCreateAgentBadi" enabled="{BADIDialogModel>/enableAgent}" icon="sap-icon://add" tooltip="Create New BAdI Implementation" press=".onBADISelectionCreateNew" />
				</HBox>
			</VBox>
		</VBox>
		<buttons>
			<Button text="Select" press=".onBADISelectionConfirm" 
				visible="{= !${BADIDialogModel>/needFourEyesBadi} &amp;&amp; !${BADIDialogModel>/needDfpBadi} &amp;&amp; !${BADIDialogModel>/needDynamicBadi} 
							&amp;&amp; !${BADIDialogModel>/needParallelBadi} 
							&amp;&amp; !${BADIDialogModel>/needSMethodBadi} &amp;&amp; !${BADIDialogModel>/needAgentBadi}}"
				enabled="{= ${BADIDialogModel>/selectedBADIName} !== '' &amp;&amp; ${BADIDialogModel>/validBadi} === true}"
				id="save" ariaLabelledBy="save" tooltip="Select the BAdI Implementation"/>

			<Button text="Select" press=".onBADISelectionConfirm" 
				visible="{= ${BADIDialogModel>/needFourEyesBadi} || ${BADIDialogModel>/needDfpBadi} || ${BADIDialogModel>/needDynamicBadi} 
							|| ${BADIDialogModel>/needParallelBadi} || ${BADIDialogModel>/needSMethodBadi} || ${BADIDialogModel>/needAgentBadi}}"
				enabled="{= ((${BADIDialogModel>/needFourEyesBadi} &amp;&amp; ${BADIDialogModel>/selectedFourEyesBADIName} !== '') || !${BADIDialogModel>/needFourEyesBadi})
							&amp;&amp; ((${BADIDialogModel>/needDfpBadi} &amp;&amp; ${BADIDialogModel>/selectedDfpBADIName} !== '') || !${BADIDialogModel>/needDfpBadi})
							&amp;&amp; ((${BADIDialogModel>/needDynamicBadi} &amp;&amp; ${BADIDialogModel>/selectedDynamicBADIName} !== '') || !${BADIDialogModel>/needDynamicBadi})
							&amp;&amp; ((${BADIDialogModel>/needParallelBadi} &amp;&amp; ${BADIDialogModel>/selectedParallelBADIName} !== '') || !${BADIDialogModel>/needParallelBadi})
							&amp;&amp; ((${BADIDialogModel>/needSMethodBadi} &amp;&amp; ${BADIDialogModel>/selectedSMethodBADIName} !== '') || !${BADIDialogModel>/needSMethodBadi})
							&amp;&amp; ((${BADIDialogModel>/needAgentBadi} &amp;&amp; ${BADIDialogModel>/selectedAgentBADIName} !== '') || !${BADIDialogModel>/needAgentBadi})
							&amp;&amp; ${BADIDialogModel>/counterInvalidBadi} === 0}"
				id="saveBadisForWF" ariaLabelledBy="save" tooltip="Select the BAdI Implementation"/>
		</buttons>
	</Dialog>
</c:FragmentDefinition>