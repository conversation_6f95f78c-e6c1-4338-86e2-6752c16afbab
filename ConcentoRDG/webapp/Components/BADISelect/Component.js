sap.ui.define([
	"sap/ui/core/UIComponent",
	"jquery.sap.global",
	"sap/ui/model/json/JSONModel",
	"sap/ui/core/BusyIndicator",
	"dmr/mdg/supernova/SupernovaFJ/model/ModelMessages"
], function(UIComponent, jQuery, JSONModel, BusyIndicator, ModelMessages){
	"use strict";
	let Component = UIComponent.extend("dmr.components.BADISelect.Component", {
		metadata: {
			manifest: "json",
			"abstract": false, 
			properties: {
				ruleDetails: {type: "object", defaultValue: {
					ruleType: "",
					usmdModel: "",
					usmdEntity: "",
					UsmdCrtype: "",
					featureType: "",
					crossEntity: "",
					needFourEyesBadi: false,
					needDfpBadi: false,
					needDynamicBadi: false,
					needParallelBadi: false,
					needSMethodBadi: false,
					needAgentBadi: false
				}}
			},
			aggregations: {},
			events: {
				// Return the selected value through the event
				BADISelected: {
					parameters: {
						BADI: {type: "object"}
					}
				}
			}
		}
	});

	Component.prototype.ModelMessages = ModelMessages;
	
	/**
	 * Just create the base information and the model
	 */
	Component.prototype.init = function(){
		// Call the parent init
		UIComponent.prototype.init.apply(this, arguments);
		this.setModel(new JSONModel({
			BADICreation: {BADIName: "", ValidBADIName: false}, 
			selectedBADIName: "",
			validBadi: true,
			needFourEyesBadi: false,
			needDfpBadi: false,
			needDynamicBadi: false,
			needParallelBadi: false,
			needSMethodBadi: false,
			needAgentBadi: false,
			enableFourEyes: true,
			enableDfp: true,
			enableDynamic: true,
			enableParallel: true,
			enableSMethod: true,
			enableAgent: true,
			counterInvalidBadi: 0 
		}), "BADIDialogModel");
	};
	
	/**
	 * Create the content. 
	 *  > The dialog 
	 *  > Button [Should be marked invisible, except for debugging]
	 * 
	 * The button is created as a base on which to attach the dialog to.  This will allow the model
	 * to be passed on the dialog. 
	 */
	Component.prototype.createContent = function() {
		let oBADISelectionDialog, oBADICreationDialog, oButton;
		
		// Create the BADI Selection Dialog
		oBADISelectionDialog = this._getBADISelectionDialog();
		oButton = this._getOpenButton();
		oButton.addDependent(oBADISelectionDialog);

		// Create the BADI Creation Dialog
		oBADICreationDialog = this._getBADICreationDialog();
		oButton.addDependent(oBADICreationDialog);
		return oButton;
	};
	
	/**
	 * A place holder button to allow attaching the dialog as a dependent. Should be hidden.
	 */
	Component.prototype._getOpenButton = function () {
		if (!this._oBtn) {
			this._oBtn = 
				new sap.m.Button(this.createId("BADISelect"), {
					text: "Test Button",
					visible: false
				});
			
			// if the visibility is true attach the handler
			if(this._oBtn.getVisible() === true)
			{
				this._oBtn.attachPress(this, this._onShowBADISelectionDialog, this);
			}
		}
		return this._oBtn;
	};

	/**
	 * The dialog fragment that is to be displayed when this component is invoked
	 */
	Component.prototype._getBADICreationDialog = function() {
		if(!this._oBADICreationDialog) {
			this._oBADICreationDialog = sap.ui.xmlfragment(
				this.getId(), 
				"dmr.components.BADISelect.fragment.BADICreationDialog",
				this
			);
		}
		
		return this._oBADICreationDialog;
	};
	
	/**
	 * The dialog fragment that is to be displayed when this component is invoked
	 */
	Component.prototype._getBADISelectionDialog = function() {
		if(!this._oBADIDialog) {
			this._oBADIDialog = sap.ui.xmlfragment(
				this.getId(), 
				"dmr.components.BADISelect.fragment.BADISelect",
				this
			);
		}
		
		return this._oBADIDialog;
	};

	/**
	 * This opens the dialog and retrieves the data to be displayed in the dialog. 
	 */
	Component.prototype._onShowBADISelectionDialog = function(sCalledFrom) {
	    BusyIndicator.show(200);
		let oBADIDialogModel = this.getModel("BADIDialogModel");
		let {needFourEyesBadi, needDfpBadi, needDynamicBadi, needParallelBadi, needSMethodBadi, needAgentBadi} = this.getRuleDetails();
		oBADIDialogModel.setProperty("/needFourEyesBadi", needFourEyesBadi ? needFourEyesBadi : false);
		oBADIDialogModel.setProperty("/needDfpBadi", needDfpBadi ? needDfpBadi : false);
		oBADIDialogModel.setProperty("/needDynamicBadi", needDynamicBadi ? needDynamicBadi : false);
		oBADIDialogModel.setProperty("/needParallelBadi", needParallelBadi ? needParallelBadi : false);
		oBADIDialogModel.setProperty("/needSMethodBadi", needSMethodBadi ? needSMethodBadi : false);
		oBADIDialogModel.setProperty("/needAgentBadi", needAgentBadi ? needAgentBadi : false);
	    // Remove any previous selections on the list 
	    let oBADIRequestList = sap.ui.getCore().byId(this.getId() + "--BADIRequestList");
		oBADIRequestList.removeSelections(true);
		
		//Remove any previous BADI filter 
		let oSearchInput = sap.ui.getCore().byId(this.getId() + "--searchBADI");
		oSearchInput.setValue();
		
		// refresh the filters
		sap.ui.getCore()
			.byId(this.getId() + "--BADIRequestList")
			.getBinding("items")
			.filter();
	    
	    this._getBADIList(sCalledFrom);
	};
	
	/*
	* List called when dialog is shown
	*/
	Component.prototype._getBADIList = async function(sCalledFrom) {
		let thisComponent = this;
		let oBADIDialogModel = this.getModel("BADIDialogModel");
		let {ruleType, usmdModel, usmdEntity, usmdCrtype, featureType, crossEntity, needFourEyesBadi, needDfpBadi, needDynamicBadi, needParallelBadi, needSMethodBadi, needAgentBadi} = this.getRuleDetails();
		let oBADIRequestList = sap.ui.getCore().byId(this.getId() + "--BADIRequestList");
		
		if (featureType) {
			
			let oUrlParameters = {
				"$filter": "RuleType eq '" + ruleType + "' and UsmdModel eq '" + usmdModel +"' and UsmdEntity eq '" + usmdEntity + "' and FeatureType eq '" + featureType + "' and CrossEntity eq '" + crossEntity + "'"
				+ ((usmdCrtype) ? (" and UsmdCrtype eq '" + usmdCrtype + "'") : ""),
				"$expand": "BADI_SEARCHTOBADI_LIST,BADI_SEARCHTOMESSAGE",
				"$format": "json"
			};
			
			let oDataModel = this.getModel("GET_BADI_LIST");
			oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
			
			let oGetParameterObject = {
				method: "GET",
				success: function (oData) {
					let arrMessages = [];
					jQuery.extend(true, arrMessages, oData.results[0].BADI_SEARCHTOMESSAGE.results);
					let cContainsError = undefined;
					arrMessages.some((oMessage) => {
						if(oMessage.MessageType === "E") {
							cContainsError = "X";
							return true;
						}
						return false;
					});
					BusyIndicator.hide();
					// Update the list 
					if(!cContainsError && oData.results[0].BadiImpl) {
						thisComponent.onBADISelectionConfirm(oData.results[0].BadiImpl);
					} else if(cContainsError) {
						thisComponent.ModelMessages.showMessagesDialog(thisComponent, arrMessages, thisComponent._oBtn);
					} else {
						let promise = new Promise(function (resolve) {
							if(sCalledFrom){
								resolve();
							}else{
								sap.m.MessageBox.show(
									"BAdI Implementation not found. Please select one.", {
										icon: sap.m.MessageBox.Icon.ERROR,
										title: "BAdI Implementation not found",
										actions: [sap.m.MessageBox.Action.OK],
										onClose: function () {
											resolve();
										}
									}
								);
							}
						});
						promise.then(function(){
							let _oBADIDialog = thisComponent._getBADISelectionDialog();
							_oBADIDialog.open();
						});
					}
					thisComponent.getModel("BADIDialogModel").setProperty("/list", oData.results[0].BADI_SEARCHTOBADI_LIST.results);
					oBADIRequestList.setNoDataText("No BAdI Implementations available for selected criteria");
					// Reset the selection status
					thisComponent.getModel("BADIDialogModel").setProperty("/selectedBADIName", "");
					thisComponent.getModel("BADIDialogModel").setProperty("/validBadi", true);
				},
				error: function () {
					BusyIndicator.hide();
					oBADIRequestList.setNoDataText("No data");
				},
				urlParameters: oUrlParameters
			};
			
			oDataModel.read("/BADI_SEARCHSet", oGetParameterObject);

		}else{
			this.bShowMessage = true;
			let bGetBadiListSuccess = true; 
			if (needFourEyesBadi) {
				bGetBadiListSuccess = await this._getWFBadiList("2", sCalledFrom);
			}else{
				oBADIDialogModel.setProperty("/selectedFourEyesBADIName", "");
				oBADIDialogModel.setProperty("/isNewFourEyesBADI", "");
			}
			if (bGetBadiListSuccess && needDfpBadi) {
				bGetBadiListSuccess = await this._getWFBadiList("4", sCalledFrom);
			}else{
				oBADIDialogModel.setProperty("/selectedDfpBADIName", "");
				oBADIDialogModel.setProperty("/isNewDfpBADI", "");
			}
			if (bGetBadiListSuccess && needDynamicBadi) {
				bGetBadiListSuccess = await this._getWFBadiList("5", sCalledFrom);
			}else{
				oBADIDialogModel.setProperty("/selectedDynamicBADIName", "");
				oBADIDialogModel.setProperty("/isNewDynamicBADI", "");
			}
			if (bGetBadiListSuccess && needParallelBadi) {
				bGetBadiListSuccess = await this._getWFBadiList("6", sCalledFrom);
			}else{
				oBADIDialogModel.setProperty("/selectedParallelBADIName", "");
				oBADIDialogModel.setProperty("/isNewParallelBADI", "");
			}
			if (bGetBadiListSuccess && needSMethodBadi) {
				bGetBadiListSuccess = await this._getWFBadiList("7", sCalledFrom);
			}else{
				oBADIDialogModel.setProperty("/selectedSMethodBADIName", "");
				oBADIDialogModel.setProperty("/isNewSMethodBADI", "");
			}
			if (bGetBadiListSuccess && needAgentBadi) {
				bGetBadiListSuccess = await this._getWFBadiList("8", sCalledFrom);
			}else{
				oBADIDialogModel.setProperty("/selectedAgentBADIName", "");
				oBADIDialogModel.setProperty("/isNewAgentBADI", "");
			}
			
			if (this.bShowMessage && !sCalledFrom) {
				this.onBADISelectionConfirm();
			}

			BusyIndicator.hide();
		}

	};

	Component.prototype._getWFBadiList = function (sRuleType, sCalledFrom) {
		let thisComponent = this;
		let oBADIDialogModel = thisComponent.getModel("BADIDialogModel");
		let promise = new Promise((resolve) => {
			if (sCalledFrom) {
				let _oBADIDialog = thisComponent._getBADISelectionDialog();
				_oBADIDialog.open();
				BusyIndicator.hide();
				resolve(true);
			}else{
				let { usmdCrtype, usmdModel } = this.getRuleDetails();
				let oUrlParameters = {
					"$filter": "RuleType eq '" + sRuleType + "' and UsmdModel eq '" + usmdModel + "' and UsmdCrtype eq '" + usmdCrtype + "'",
					"$expand": "BADI_SEARCHTOBADI_LIST,BADI_SEARCHTOMESSAGE",
					"$format": "json"
				};
				
				let oDataModel = this.getModel("GET_BADI_LIST");
					oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
					
				let oGetParameterObject = {
					method: "GET",
					success: function (oData) {
						let arrMessages = [];
						jQuery.extend(true, arrMessages, oData.results[0].BADI_SEARCHTOMESSAGE.results);
						let cContainsError = undefined;
						arrMessages.some((oMessage) => {
							if(oMessage.MessageType === "E") {
								cContainsError = "X";
								return true;
							}
							return false;
						});
						
						let sSavedBadi = "";
						if(!cContainsError && oData.results[0].BadiImpl) {
							sSavedBadi = oData.results[0].BadiImpl;
						}

						let aBadiList = oData.results[0].BADI_SEARCHTOBADI_LIST.results;

						if (sRuleType === "2") {
							oBADIDialogModel.setProperty("/fourEyesBadiList", aBadiList);
							if (sSavedBadi) {
								oBADIDialogModel.setProperty("/selectedFourEyesBADIName", sSavedBadi);
								oBADIDialogModel.setProperty("/enableFourEyes", false);
							}else{
								oBADIDialogModel.setProperty("/selectedFourEyesBADIName", "");
								oBADIDialogModel.setProperty("/enableFourEyes", true);
							}
							oBADIDialogModel.setProperty("/isNewFourEyesBADI", "");
						
						}else if (sRuleType === "4") {
							oBADIDialogModel.setProperty("/dfpBadiList", aBadiList);
							if (sSavedBadi) {
								oBADIDialogModel.setProperty("/selectedDfpBADIName", sSavedBadi);
								oBADIDialogModel.setProperty("/enableDfp", false);
							}else{
								oBADIDialogModel.setProperty("/selectedDfpBADIName", "");
								oBADIDialogModel.setProperty("/enableDfp", true);
							}
							oBADIDialogModel.setProperty("/isNewDfpBADI", "");
						}else if (sRuleType === "5") {
							oBADIDialogModel.setProperty("/dynamicBadiList", aBadiList);
							if (sSavedBadi) {
								oBADIDialogModel.setProperty("/selectedDynamicBADIName", sSavedBadi);
								oBADIDialogModel.setProperty("/enableDynamic", false);
							}else{
								oBADIDialogModel.setProperty("/selectedDynamicBADIName", "");
								oBADIDialogModel.setProperty("/enableDynamic", true);
							}
							oBADIDialogModel.setProperty("/isNewDynamicBADI", "");
						}else if(sRuleType === "6"){
							oBADIDialogModel.setProperty("/parallelBadiList", aBadiList);
							if (sSavedBadi) {
								oBADIDialogModel.setProperty("/selectedParallelBADIName", sSavedBadi);
								oBADIDialogModel.setProperty("/enableParallel", false);
							}else{
								oBADIDialogModel.setProperty("/selectedParallelBADIName", "");
								oBADIDialogModel.setProperty("/enableParallel", true);
							}
							oBADIDialogModel.setProperty("/isNewParallelBADI", "");
						}else if(sRuleType === "7"){
							oBADIDialogModel.setProperty("/sMethodBadiList", aBadiList);
							if (sSavedBadi) {
								oBADIDialogModel.setProperty("/selectedSMethodBADIName", sSavedBadi);
								oBADIDialogModel.setProperty("/enableSMethod", false);
							}else{
								oBADIDialogModel.setProperty("/selectedSMethodBADIName", "");
								oBADIDialogModel.setProperty("/enableSMethod", true);
							}
							oBADIDialogModel.setProperty("/isNewSMethodBADI", "");
						}else if(sRuleType === "8"){
							oBADIDialogModel.setProperty("/agentBadiList", aBadiList);
							if (sSavedBadi) {
								oBADIDialogModel.setProperty("/selectedAgentBADIName", sSavedBadi);
								oBADIDialogModel.setProperty("/enableAgent", false);
							}else{
								oBADIDialogModel.setProperty("/selectedAgentBADIName", "");
								oBADIDialogModel.setProperty("/enableAgent", true);
							}
							oBADIDialogModel.setProperty("/isNewAgentBADI", "");
						}

						if(cContainsError) {
							thisComponent.ModelMessages.showMessagesDialog(thisComponent, arrMessages, thisComponent._oBtn)
							.then(function(){
								// Return false for error situation
								resolve(false);
							});
						} else {
							if (thisComponent.bShowMessage && !sSavedBadi) {
								thisComponent.bShowMessage = false;
								let promiseMessage = new Promise(function (resolveMessage) {
									sap.m.MessageBox.show(
										"BAdI Implementation not found. Please select one.", {
											icon: sap.m.MessageBox.Icon.ERROR,
											title: "BAdI Implementation not found",
											actions: [sap.m.MessageBox.Action.OK],
											onClose: function () {
												resolveMessage();
												resolve(true);
											}
										}
									);
								});
								promiseMessage.then(function(){
									let _oBADIDialog = thisComponent._getBADISelectionDialog();
									_oBADIDialog.open();
								});
							}else{
								resolve(true);
							}
						}
						
					},
					error: function () {
						resolve(false);
						BusyIndicator.hide();
					},
					urlParameters: oUrlParameters
				};
				
				oDataModel.read("/BADI_SEARCHSet", oGetParameterObject);
			}
		});
		return promise;
	};

	
	/**
	 * Public open function to be used by the parent components. 
	 * This opens the dialog and retrieves the data to be displayed in the dialog. 
	 */
	Component.prototype.open = function () {
		let oBADIDialogModel = this.getModel("BADIDialogModel");
		oBADIDialogModel.setProperty("/counterInvalidBadi", 0);
		let aIds = ["idCbxFourEyesBadi", "idCbxDfpBadi", "idCbxDynamicBadi", "idCbxParallelBadi", "idCbxSystemMBadi", "idCbxAgentBadi"];
		for (let i = 0; i < aIds.length; i++) {
			const sId = aIds[i];
			let oControl = sap.ui.getCore().byId(this.getId() + `--${sId}`);
			oControl.setValueState("None");
		}
		this._onShowBADISelectionDialog();
	};

	/**
	 * The list selection change handler. Used to store the current selected BADI. 
	 */
	Component.prototype.onBADISelectionChange = function(oEvent){
		// Get the selected BADI and store to the model 
		let oBadiDialogModel = this.getModel("BADIDialogModel");
		let oSource = oEvent.getSource();
		let oSelectedItem = oSource.getSelectedItem();
		let sPath = oSelectedItem.getBindingContext("BADIDialogModel").getPath();
		let iFilterCounter = oBadiDialogModel.getProperty(sPath + "/FilterCondCount");
		
		if (iFilterCounter < 1000) {
			// Store the BADI to the model 
			let sSelectedBADI = oSelectedItem.getTitle(); 
			oBadiDialogModel.setProperty("/selectedBADIName", sSelectedBADI);
			oBadiDialogModel.setProperty("/validBadi", true);
		}else{
			oBadiDialogModel.setProperty("/validBadi", false);
		}
	};

	/**
	 * The ComboBox selection change handler. Used to check BADI selection. 
	 */
	Component.prototype.onChangeBadi = function(oEvent) {
		// Get the selected BADI and store to the model 
		let oBadiDialogModel = this.getModel("BADIDialogModel");
		let oSource = oEvent.getSource();
		let oSelectedItem = oSource.getSelectedItem();
		let sPath = oSelectedItem.getBindingContext("BADIDialogModel").getPath();
		let iFilterCounter = oBadiDialogModel.getProperty(sPath + "/FilterCondCount");
		let iCounterInvalidBadi = oBadiDialogModel.getProperty("/counterInvalidBadi");
		let sValueState = oSource.getValueState();
		
		if (iFilterCounter < 1000) {
			if (sValueState === "Error") {
				oBadiDialogModel.setProperty("/counterInvalidBadi", iCounterInvalidBadi - 1);
				oSource.setValueState("None");
			}
		}else{
			if (sValueState !== "Error") {
				oSource.setValueState("Error");
				oBadiDialogModel.setProperty("/counterInvalidBadi", iCounterInvalidBadi + 1);
			}
		}
	};
	
	/**
	 * The confirm / Select button press on the dialog. Since the dialog select button is enabled only when
	 * an item on the list is selected, it is assured that some BADI has been selected when this callback
	 * is received. 
	 * Fire the event with the selected BADI and close the dialog
	 */
	Component.prototype.onBADISelectionConfirm = function(currentBADI, isNewBADI){
		let { featureType, needFourEyesBadi, needDfpBadi, needDynamicBadi, needParallelBadi, needSMethodBadi, needAgentBadi } = this.getRuleDetails();
		let oBADIDialogModel = this.getModel("BADIDialogModel");
		if(featureType) {
			// Read the selected item name from the model 
			let sSelectedBADI = oBADIDialogModel.getProperty("/selectedBADIName");
	
			// Validate if the function is called from another function
			if(typeof (currentBADI) === "string"){
				sSelectedBADI = currentBADI;
			}
				
			// Fire the event to the calling component
			this.fireBADISelected({
				BADISelected: sSelectedBADI,
				isNewBADI: (isNewBADI) ? "X" : ""
			});
		}else{
			let sSelectedFourEyesBADIName = oBADIDialogModel.getProperty("/selectedFourEyesBADIName");
			let sSelectedDfpBADIName = oBADIDialogModel.getProperty("/selectedDfpBADIName");
			let sSelectedDynamicBADIName = oBADIDialogModel.getProperty("/selectedDynamicBADIName");
			let sSelectedParallelBADIName = oBADIDialogModel.getProperty("/selectedParallelBADIName");
			let sSelectedSMethodBADIName = oBADIDialogModel.getProperty("/selectedSMethodBADIName");
			let sSelectedAgentBADIName = oBADIDialogModel.getProperty("/selectedAgentBADIName");

			let bIsNewFourEyesBADI = oBADIDialogModel.getProperty("/isNewFourEyesBADI");
			let bIsNewDfpBADI = oBADIDialogModel.getProperty("/isNewDfpBADI");
			let bIsNewDynamicBADI = oBADIDialogModel.getProperty("/isNewDynamicBADI");
			let bIsNewParallelBADI = oBADIDialogModel.getProperty("/isNewParallelBADI");
			let bIsNewSMethodBADI = oBADIDialogModel.getProperty("/isNewSMethodBADI");
			let bIsNewAgentBADI = oBADIDialogModel.getProperty("/isNewAgentBADI");

			// Fire the event to the calling component
			if (needFourEyesBadi || needDfpBadi || needDynamicBadi || needParallelBadi || needSMethodBadi || needAgentBadi) {
				this.fireBADISelected({
					BADISelectionNeeded: true, 
					fourEyesBADIName: sSelectedFourEyesBADIName,
					dfpBADIName: sSelectedDfpBADIName,
					dynamicBADIName: sSelectedDynamicBADIName,
					parallelBADIName: sSelectedParallelBADIName,
					methodBADIName: sSelectedSMethodBADIName,
					agentBADIName: sSelectedAgentBADIName,
					isNewFourEyesBADI: bIsNewFourEyesBADI ? bIsNewFourEyesBADI : "",
					isNewDfpBADI: bIsNewDfpBADI ? bIsNewDfpBADI : "",
					isNewDynamicBADI: bIsNewDynamicBADI ? bIsNewDynamicBADI : "",
					isNewParallelBADI: bIsNewParallelBADI ? bIsNewParallelBADI : "",
					isNewSMethodBADI: bIsNewSMethodBADI ? bIsNewSMethodBADI : "",
					isNewAgentBADI: bIsNewAgentBADI ? bIsNewAgentBADI : ""
				});
			}else{
				this.fireBADISelected({
					BADISelectionNeeded: false 
				});
			}
		}
		
		// Close the dialog
		this._getBADISelectionDialog().close();
	};
	     
	
	/**
	 * Called when the search content is changed. Filter the list on the name. 
	 * The filtering is case insensitive. 
	 */
	Component.prototype.onSearchBADIList = function (oEvent) {

		// add filter for search
		let aFilters;

		// Get the search text
		let sQuery = oEvent.getSource().getValue();

		if (sQuery && sQuery.length > 0) {
			aFilters = new sap.ui.model.Filter({
				filters: [
					new sap.ui.model.Filter({
							path: "BadiImpl",
							operator: sap.ui.model.FilterOperator.Contains,
							value1: sQuery,
							caseSensitive: false
						})
				],
				and: false
			});
		}
		
		// update list binding
		sap.ui.getCore()
			.byId(this.getId() + "--BADIRequestList")
			.getBinding("items")
			.filter(aFilters);
	};
	
	/**
	 * On BADI creation dialog open request
	 * 1. Close the BADI selection dialog 
	 * 2. Open the BADI creation dialog
	 */
	Component.prototype.onBADISelectionCreateNew = function(oEvent){
		let oControl = oEvent.getSource();
		let sId = oEvent.getParameter("id");
		let aParentItems = oControl.getParent().getItems();
		this.oComboBox = aParentItems[0];
		// Close the selection dialog
		this._getBADISelectionDialog().close();
		// // Reset the model content for the dialog
		this.getModel("BADIDialogModel").setProperty("/BADICreation", {
			BADIName: "",
			ValidBADIName: false,
			createBadiFor: sId
		});
		// Open the creation dialog
		this._getBADICreationDialog().open();
	};

	Component.prototype.onChangeBadiSelection = function(oEvent){
		let oComboBox = oEvent.getSource();
		let oBADIDialogModel = this.getModel("BADIDialogModel");
		let sPath = oComboBox.getBindingPath("items");
		let aItemsList = oBADIDialogModel.getProperty(sPath);
		let oLastItem = aItemsList[aItemsList.length - 1];
		if (oLastItem.hasOwnProperty("isNew")){
			aItemsList.pop();
			oBADIDialogModel.refresh();
		}

		if (sPath === "/fourEyesBadiList") {
			oBADIDialogModel.setProperty("/isNewFourEyesBADI", "");
		}else if(sPath === "/dfpBadiList") {
			oBADIDialogModel.setProperty("/isNewDfpBADI", "");
		}else if(sPath === "/dynamicBadiList"){
			oBADIDialogModel.setProperty("/isNewDynamicBADI", "");
		}else if(sPath === "/parallelBadiList"){
			oBADIDialogModel.setProperty("/isNewParallelBADI", "");
		}else if(sPath === "/sMethodBadiList"){
			oBADIDialogModel.setProperty("/isNewSMethodBADI", "");
		}else if(sPath === "/agentBadiList"){
			oBADIDialogModel.setProperty("/isNewAgentBADI", "");
		}
	};
	
	/**
	 * Send the request for the BADI creation.
	 * If the creation is successful, open the BADI selection dialog with the updated list 
	 * If the creation is not successful, show a message and wait
	 */
	Component.prototype.onPressCreateBADI = function(){
		let oThisObject = this;

		// Get the model data from the dialog 
		let oBADIInfo = this.getModel("BADIDialogModel").getProperty("/BADICreation");
		let {ruleType, usmdModel, featureType, crossEntity, needFourEyesBadi, needDfpBadi, needDynamicBadi, needParallelBadi, needSMethodBadi, needAgentBadi} = this.getRuleDetails();
		let oUrlParameters;
		if (featureType) {
			//Bug 12882 - Added new parameters to filter better in the backend
			oUrlParameters = {
				"$filter": `RuleType eq '${ruleType}' and BadiImpl eq '${oBADIInfo.BADIName}' and UsmdModel eq '${usmdModel}' and FeatureType eq '${featureType}' and CrossEntity eq '${crossEntity}'`,
				"$expand": "BADI_SEARCHTOBADI_LIST,BADI_SEARCHTOMESSAGE",
				"$format": "json"
			};
		}else{
			if(oBADIInfo.createBadiFor.includes("btnCreateFourEyesBadi")){
				ruleType = "2";
			}else if(oBADIInfo.createBadiFor.includes("btnCreateDfpBadi")){
				ruleType = "4";
			}else if(oBADIInfo.createBadiFor.includes("btnCreateDynamicBadi")){
				ruleType = "5";
			}else if(oBADIInfo.createBadiFor.includes("btnCreateParallelBadi")){
				ruleType = "6";
			}else if(oBADIInfo.createBadiFor.includes("btnCreateSMethodBadi")){
				ruleType = "7";
			}else if(oBADIInfo.createBadiFor.includes("btnCreateAgentBadi")){
				ruleType = "8";
			}
			oUrlParameters = {
				"$filter": `RuleType eq '${ruleType}' and BadiImpl eq '${oBADIInfo.BADIName}' and UsmdModel eq '${usmdModel}'`,
				"$expand": "BADI_SEARCHTOBADI_LIST,BADI_SEARCHTOMESSAGE",
				"$format": "json"
			};
		}

		let oWriteParameterObject = {
			success: function (oDataResult) {
				// Hide the busy indicator
				BusyIndicator.hide();
				
				//Bug 12882 - Changed condition since we receive a new response
				if(oDataResult.results[0].Exists){
					sap.m.MessageBox.show("BAdI Implementation already exists, please enter another.", {
						icon: sap.m.MessageBox.Icon.ERROR,
						title: "Error",
						actions: [sap.m.MessageBox.Action.OK]
					});
				}else{
					oThisObject._getBADICreationDialog().close();
					if (needFourEyesBadi || needDfpBadi || needDynamicBadi || needParallelBadi || needSMethodBadi || needAgentBadi) {
						oThisObject._confirmBadiCreationForWF(oBADIInfo);

					}else{
						oThisObject.onBADISelectionConfirm(oBADIInfo.BADIName, true);
					}
				}
			},
			error: function () {
				// Hide the busy indicator 
				BusyIndicator.hide();
				
				// Show an error message ..
				sap.m.MessageBox.show("BAdI Implementation failed!", {
					icon: sap.m.MessageBox.Icon.ERROR,
					title: "Error",
					actions: [sap.m.MessageBox.Action.OK]
				});
			},
			urlParameters: oUrlParameters
		};
		
		BusyIndicator.show(200);
		
		let oDataModel = this.getModel("GET_BADI_LIST");
		oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
		//Bug 12882 - Changed entity set to check if BAdI already exists
		oDataModel.read("/BADI_SEARCHSet", oWriteParameterObject);	
	};
	
	Component.prototype._confirmBadiCreationForWF = function(oBADIInfo) {
		let oBADIDialogModel = this.getModel("BADIDialogModel");
		let aBadisList;
		if(oBADIInfo.createBadiFor.includes("btnCreateFourEyesBadi")){
			aBadisList = oBADIDialogModel.getProperty("/fourEyesBadiList");
			oBADIDialogModel.setProperty("/selectedFourEyesBADIName", oBADIInfo.BADIName);
			oBADIDialogModel.setProperty("/isNewFourEyesBADI", "X");
		}else if(oBADIInfo.createBadiFor.includes("btnCreateDfpBadi")){
			aBadisList = oBADIDialogModel.getProperty("/dfpBadiList");
			oBADIDialogModel.setProperty("/selectedDfpBADIName", oBADIInfo.BADIName);
			oBADIDialogModel.setProperty("/isNewDfpBADI", "X");
		}else if(oBADIInfo.createBadiFor.includes("btnCreateDynamicBadi")){
			aBadisList = oBADIDialogModel.getProperty("/dynamicBadiList");
			oBADIDialogModel.setProperty("/selectedDynamicBADIName", oBADIInfo.BADIName);
			oBADIDialogModel.setProperty("/isNewDynamicBADI", "X");
		}else if(oBADIInfo.createBadiFor.includes("btnCreateParallelBadi")){
			aBadisList = oBADIDialogModel.getProperty("/parallelBadiList");
			oBADIDialogModel.setProperty("/selectedParallelBADIName", oBADIInfo.BADIName);
			oBADIDialogModel.setProperty("/isNewParallelBADI", "X");
		}else if(oBADIInfo.createBadiFor.includes("btnCreateSMethodBadi")){
			aBadisList = oBADIDialogModel.getProperty("/sMethodBadiList");
			oBADIDialogModel.setProperty("/selectedSMethodBADIName", oBADIInfo.BADIName);
			oBADIDialogModel.setProperty("/isNewSMethodBADI", "X");
		}else if(oBADIInfo.createBadiFor.includes("btnCreateAgentBadi")){
			aBadisList = oBADIDialogModel.getProperty("/agentBadiList");
			oBADIDialogModel.setProperty("/selectedAgentBADIName", oBADIInfo.BADIName);
			oBADIDialogModel.setProperty("/isNewAgentBADI", "X");
		}

		aBadisList.push({
			BadiImpl: oBADIInfo.BADIName,
			FilterCondCount: 0,
			isNew: true
		});

		if (this.oComboBox.getValueState() === "Error") {
			let iCounterInvalidBadi = oBADIDialogModel.getProperty("/counterInvalidBadi");
			this.oComboBox.setValueState("None");
			oBADIDialogModel.setProperty("/counterInvalidBadi", iCounterInvalidBadi - 1);
		}

		oBADIDialogModel.refresh();
		this._onShowBADISelectionDialog("createForWF");
		BusyIndicator.hide();
	};

	Component.prototype.onChangeBADIName = function(oEvent) {
		oEvent.getSource().setValue(oEvent.getSource().getValue().toUpperCase().replace(/\s+/g, ""));
		let badiName = oEvent.getSource().getValue();
		if (badiName.slice(0, 1) === "Z" || badiName.slice(0, 1) === "Y") {
			this.getModel("BADIDialogModel").setProperty("/BADICreation/ValidBADIName", true);
		}else{
			this.getModel("BADIDialogModel").setProperty("/BADICreation/ValidBADIName", false);
		}
	};

	/**
	 * Close the BADI creation dialog
	 */
	Component.prototype.onPressCancelCreateBADI = function() {
		// Close the creation dialog 
		this._getBADICreationDialog().close();
		
		// Show the BADI selection dialog
		this._onShowBADISelectionDialog("cancelCreate");
	};
	
	return Component;
});