sap.ui.define([
	"sap/ui/core/UIComponent",
	"sap/ui/model/json/JSONModel",
	"sap/ui/core/BusyIndicator",
	"sap/suite/ui/generic/template/extensionAPI/ReuseComponentSupport",
	"./model/FormulaTable",
	"./model/GetLists",
	"./FormulaType"
], function(UIComponent, JSONModel, BusyIndicator, ReuseComponentSupport, FormulaTable, GetListsModel, FormulaType){
	"use strict";
	let Component = UIComponent.extend("dmr.components.FormulaEdit.Component", {
		metadata: {
			manifest: "json",
			properties: {
				formulaType: {type: "string", defaultValue: FormulaType.FORMULA},
				formulaTypeSelected: {type: "string", defaultValue: ""},
				formulaText: {type: "string", defaultValue: ""},
				ruleNumber: {type: "string", defaultValue:""},
				colName: {type: "string", defaultValue:""},
				value: {type: "string", defaultValue:""},
				dataModel: {type: "string", defaultValue:""},
				crType: {type: "string", defaultValue: ""},
				attribute: {type: "string", defaultValue: ""},
				entity: {type: "string", defaultValue: ""},
				/* If restrictEntitySelection is set to true, the entity list will be limited to 
				 the "entity" specified in the property entity */
				restrictEntitySelection: {type: "boolean", defaultValue: false},
				isCrossEntity: {type: "string", defaultValue: false},
				tableStruct: {type: "object", default: ""},
				delimiter: {type: "string", default: ""}
			},
			aggregations: {
				
			},
			events: {
				// Return the selected value through the event
				editComplete: {
					parameters: {
						formulas: {type: "Object"}
					}
				}
			}
		}
	});
	
	Component.prototype.FormulaTableFragment = FormulaTable;
	Component.prototype.GetListsModel = GetListsModel;

	/**
	 * Create the base information and the model
	 */
	Component.prototype.init = function(){
		// Call the parent init
		UIComponent.prototype.init.apply(this, arguments);

		//Transform this component into a reuse component for Fiori Elements:
		ReuseComponentSupport.mixInto(this);
		this.attachModelContextChange(undefined, this.modelContextChange, this);
	};

	Component.prototype.getIsCrossEntity = function() {
		return this.getProperty("isCrossEntity") === "X" ? true: false;
	};

	Component.prototype.setFormulaType = function(sFormulaType){
		this.setProperty("formulaType", sFormulaType);
		this.getComponentModel()
			.setProperty("/formulaType", sFormulaType);
	};

	Component.prototype.setFormulaText = function(sFormulaText){
		this.setProperty("formulaText", sFormulaText);
		this.getComponentModel()
			.setProperty("/formulaText", sFormulaText);
	};

	Component.prototype.getComponentModel = function(){
		// Initialize the data model
		let oJSONModel = this.getModel();
		if(!oJSONModel) {
			oJSONModel = new JSONModel();
			this.setModel(oJSONModel);
		}

		return oJSONModel;
	};

	/**
	 * Create the content. 
	 */
	Component.prototype.createContent = function() {
		let oHBox = new sap.m.HBox({
			alignContent: "SpaceBetween"
		});
		let oComponent = this;

		let sButtonTooltip = "Edit Formula";
		let sButtonIcon = "sap-icon://fx";
		if(this.getFormulaType() === FormulaType.PLACEHOLDER){
			sButtonTooltip = "Edit Placeholders";
			sButtonIcon = "sap-icon://add-product";
		}

		this.oDerivationTableButton = new sap.m.Button({
			icon: sButtonIcon,
			tooltip: sButtonTooltip,
			visible: true,
			enabled: true
		})
		.attachPress(this, this.onPressFormulaTableButton, this)
		.addStyleClass("sapUiTinyMarginEnd");

		// Visible only for Type = FORMULA
		this.oInputComponent = (new sap.m.Input({
			editable: false,
			visible: {
				parts: ["/formulaType"],
				formatter: function(sFormulaType){
					return (sFormulaType === FormulaType.FORMULA);
				}
			},
			value: {
				parts: ["/formulaText"],
				formatter: function(sFormulaText){
					return sFormulaText;
				}
			},
			tooltip: {
				parts: ["/formulaText"],
				formatter: function(sFormulaText){
					return sFormulaText;
				}
			}
		}))
		.setLayoutData(new sap.m.FlexItemData({growFactor: 4}));
		
		// Create the Formula Table Dialog
		this._getFormulaTableDialog()
		.then(function(oDialogControl){
			oComponent.oDerivationTableButton.addDependent(oDialogControl);
		});

		oHBox.addItem(this.oInputComponent);
		oHBox.addItem(this.oDerivationTableButton);
		
		return oHBox;
	};

	/**
	 * The dialog fragment that is to be displayed when this button is clicked
	 */
	Component.prototype._getFormulaTableDialog = function() {
		if(!this._oFormulaTableDialog) {
			this._oFormulaTableDialog = sap.ui.core.Fragment.load({
				id: this.createId("FormulaTableDialog"),
				type: "XML",
				name: "dmr.components.FormulaEdit.fragment.FormulaTable",
				controller: this.FormulaTableFragment
			});
		}
		return this._oFormulaTableDialog;
	};

	Component.prototype.onAfterRendering = function(){
		// if the component is called for MATH OR CONCAT, build the PREVIEW TEXT
		if(this.getFormulaType() === FormulaType.FORMULA ){
			let arrTransformedTable = this._transformTableStruct(this.getTableStruct());
			let sFormulaTypeSelected = this.getFormulaTypeSelected();
			let oPreviewInfo = {FORMULA_PREVIEW_TEXT:""};
			if(sFormulaTypeSelected === "1"){
				oPreviewInfo = this.FormulaTableFragment.buildConcatePreviewText(arrTransformedTable, this.getDelimiter());
			} else if( sFormulaTypeSelected === "2") {
				oPreviewInfo = this.FormulaTableFragment.buildMathPreviewText(arrTransformedTable);
			}
			this.setFormulaText(oPreviewInfo.FORMULA_PREVIEW_TEXT);
		}
	};

	/**
	 * Context Changed Event
	 * Fired when models or contexts are changed on this object 
	 *		(either by calling setModel/setBindingContext or due to propagation)
	 * this.path is final path where formula table details should reside
	 */					
	 Component.prototype.modelContextChange = function(oEvent){
		let oBindingContext = oEvent.getSource().getBindingContext();
		if(oBindingContext === undefined) return;

		this.sPath = oBindingContext.getPath();		
	};


	Component.prototype._transformTableStruct = function(arrTableStruct){
		let arrTable = [];
		for(let i = 0; i < arrTableStruct?.length; i++){
			let oElement = arrTableStruct[i];
			arrTable.push({
				Operator: oElement.operator,
				Paranthesis: oElement.paranthesis,
				fixedVal: oElement.fixedVal,
				placeholder: oElement.placeholder,
				UsmdAttribute: oElement.usmdAttribute,
				UsmdEntity: oElement.usmdEntity,
				UsmdModel: oElement.usmdModel
			});
		}
		return arrTable;
	};

	/**
	 * Press Event on Formula Table Button
	 */
	Component.prototype.onPressFormulaTableButton = function() {
		let sFormulaType = this.getFormulaType();
		let sFormulaTypeSelected = this.getFormulaTypeSelected();
		let sFormulaText = this.getFormulaText();
		let sValue = this.getValue();
		let sDataModel = this.getDataModel();
		let sCrType = this.getCrType();
		let sEntityFullName = this.getEntity();
		let sEntity = sEntityFullName?.split("__")[0]; 
		let sAttribute = this.getAttribute();
		let bRestrictEntitySelection = this.getRestrictEntitySelection();
		let bCrossEntity = this.getIsCrossEntity();
		let arrTable = this.getTableStruct();
		// Make a copy of the array so that an update to the table does not effect the bound values 
		let arrToUpdate = this._transformTableStruct(arrTable);

		// Get the data from the parent model 
		let oFormulaTableInitInfo = {
			FORMULA_TYPE: sFormulaType,
			FORMULA_TYPE_SELECTED: sFormulaTypeSelected,
			FORMULA_PREVIEW_TEXT: sFormulaText,
			VALUE: sValue,
			DATAMODEL: sDataModel,
			CRTYPE: sCrType,
			ENTITY: sEntity,
			ATTRIBUTE: sAttribute, 
			RESTRICTENTITYSELECTION: bRestrictEntitySelection,
			ISCROSSENTITY: bCrossEntity,
			DELIMITER: this.getDelimiter(),
			TABLE: arrToUpdate
		};

		this.FormulaTableFragment.init(this, oFormulaTableInitInfo);
		this.FormulaTableFragment.openFormulaTableDialog(this);
	};

	Component.prototype.onCompleteFormulaEdit = function(oData){

		let arrTableStruct = this.getTableStruct();
		if(!arrTableStruct){
			arrTableStruct = [];
		}
		
		// Remove the unnecessary content from the data before sending the event
		// Also update the tableStruct property
		for(let i = 0; i < oData.TABLE.length; i++){
			let oTableElement = oData.TABLE[i];
			delete oTableElement.arrAttributeList;
			delete oTableElement.attributeListBusy;

			// If the table struct index is empty, initialize it
			if(arrTableStruct[i] === undefined){
				arrTableStruct[i] = {};
			}

			// Store the data back to the propagated property
			arrTableStruct[i].fixedVal = oTableElement.fixedVal;
			arrTableStruct[i].operator = oTableElement.Operator;
			arrTableStruct[i].paranthesis = oTableElement.Paranthesis;
			arrTableStruct[i].placeholder = oTableElement.placeholder;
			arrTableStruct[i].usmdAttribute = oTableElement.UsmdAttribute;
			arrTableStruct[i].usmdEntity = oTableElement.UsmdEntity;
			arrTableStruct[i].usmdModel = oTableElement.UsmdModel;
		}

		this.setTableStruct(arrTableStruct);
		this.setDelimiter(oData.DELIMITER);		
		this.setFormulaText(oData.FORMULA_PREVIEW_TEXT);
		this.setFormulaTypeSelected(oData.FORMULA_TYPE_SELECTED);

		this.fireEditComplete({
			ruleNumber: this.getRuleNumber(),
			colName: this.getColName(),
			path: this.sPath,
			Data: oData
		});
	};


	return Component;
}); 