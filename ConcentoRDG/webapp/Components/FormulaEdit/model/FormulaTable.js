sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"sap/ui/model/json/JSONModel",
	"sap/m/MessageBox",
	"../FormulaType",
	"./GetLists",
], function (Utilities, JSONModel, MessageBox, FormulaType, GetListsModel) {
	"use strict";
	let FormulaTable = {};
	
	FormulaTable.init = function(oComponent, oInitData) {

		this.COMPONENT = oComponent;
		let oJSONModel = this.getFragmentModel(oComponent);
		let oFragmentData = {
			completionError: true,
			FORMULA_TYPE: oInitData.FORMULA_TYPE,
			DATA: oInitData
		};

		// Update the formula type selection choice to be displayed in the dialog 
		let arrFormulaTypeList;
		if(oInitData.FORMULA_TYPE === FormulaType.FORMULA) {
			oFragmentData.selectedType = 
				(oInitData.FORMULA_TYPE_SELECTED === "2")? "MATH": "CONCAT";

			arrFormulaTypeList = [
				{key: "CONCAT", text: this._geti18nText("formulatable.selectedtype.concatenate")},
				{key: "MATH", text: this._geti18nText("formulatable.selectedtype.mathematical")},
			];
		} else {
			oFragmentData.selectedType = "PLACEHOLDER";
			arrFormulaTypeList = [
				{key: "PLACEHOLDER", text: this._geti18nText("formulatable.selectedtype.placeholder")}
			];
		}
		oFragmentData.formulaTableTypeList = arrFormulaTypeList;

		// If the init data is not available, fill the information into the table
		if(!oInitData.TABLE || oInitData.TABLE.length === 0) {
			oFragmentData.DATA.TABLE = this.buildTableInfo(oFragmentData.selectedType, oInitData.VALUE);			
		}

		// Initialize the data 
		oJSONModel.setProperty("/", oFragmentData);
	};

	FormulaTable.buildTableInfo = function(sSelectedType, sValueText) {
		let arrTableInfo = [];

		if(sSelectedType !== "PLACEHOLDER") return arrTableInfo;

		const regex = /&\d*/g;
		const elements = sValueText
							.match(regex)
							.sort()
							.filter((item,
								index, arr) => arr.indexOf(item) === index);

		for(let i = 0; i < elements.length; i++) {
			arrTableInfo.push({
				placeholder: elements[i]
			});
		}

		return arrTableInfo;
	};

	/**
	 * 
	 * @param {*} ignoredSelectedType Not used, but needed for check trigger
	 * @param {*} sFixedValue 
	 * @param {*} sEntity 
	 * @param {*} sAttribute 
	 * @param {*} ignoredArrAttributeList Not used, but needed for check trigger 
	 * @param {*} ignoredDelimiter  Not used, but needed for check trigger
	 * @param {*} ignoredOperator  Not used, but needed for check trigger
	 * @param {*} ignoredParanthesis  Not used, but needed for check trigger
	 * @returns 
	 */
	FormulaTable.columnCompletionCheck = function(ignoredSelectedType, sFixedValue, sEntity, sAttribute, ignoredArrAttributeList, ignoredDelimiter, ignoredOperator, ignoredParanthesis) {
		let promise = new Promise((resolve) => {
			this.updatePreview().then(function(){

				// if a closing bracket is entered, then the entity, attribute and fixed Value should be empty
				if(ignoredParanthesis === ")" && (sEntity || sAttribute || sFixedValue)){
					resolve("Error");
				}

				// If a closing bracket is not entered and the other fields are empty, then it is an error
				if(!(ignoredParanthesis === ")") && !sFixedValue && (!sEntity || !sAttribute )){
					resolve("Error");
					return;
				}
		
				resolve("Success");		
			});
		});

		return promise;
	};

	FormulaTable.updatePreview = function(){
		let oComponent = this.COMPONENT;
		let oModel = this.getFragmentModel(oComponent);
		let oModelData = oModel.getProperty("/");
		let sSelectedType = oModelData.selectedType;
		let arrTable = oModelData.DATA.TABLE;
		let sPreviewText = oModelData.DATA.VALUE;
		let oDataReturned = {
			FORMULA_PREVIEW_TEXT: sPreviewText,
			bError: false
		};

		// Default the error to true before the check is done.
		oModel.setProperty("/DATA/completionError", true);
		oModel.setProperty("/DATA/FORMULA_PREVIEW_TEXT", this._geti18nText("formulatable.previewtext.validating"));	

		let promiseReturn = new Promise((resolve) => {
			// Get the value, replace the place holder values at the respective placeholder locations
			if(sSelectedType === "PLACEHOLDER") {
				this._updatePlaceHolderPreview(arrTable, sPreviewText).then(function(oReturnedInfo){
					oDataReturned = oReturnedInfo;
					resolve(oDataReturned);
				});
			} else if(sSelectedType === "CONCAT") {
				let sDelimiter = oModelData.DATA.DELIMITER;
				this._updateStringConcatPreview(arrTable, sDelimiter).then(function(oReturnedInfo){
					oDataReturned = oReturnedInfo;
					resolve(oDataReturned);
				});
			} else if (sSelectedType === "MATH") {
				let oMasterData = oModelData.DATA; 
				this._updateMathFormulaPreview(oMasterData, arrTable).then(function(oReturnedInfo){
					oDataReturned = oReturnedInfo;
					resolve(oDataReturned);
				});
			}
		});

		promiseReturn.then(function(oResult){
			oModel.setProperty("/DATA/completionError", oResult.bError);
			oModel.setProperty("/DATA/FORMULA_PREVIEW_TEXT", oResult.FORMULA_PREVIEW_TEXT);	
		});
		return promiseReturn;
	};

	// This function uses the provided table data and delimiter to build the preview text. This is used internally and also from the main component. 
	FormulaTable.buildMathPreviewText = function(arrTable){
		let arrFormulaText = [];
		let sFormulaText = "";
		for(let i = 0; i < arrTable.length; i++){
			let oRow = arrTable[i];

			// Stop action if any of the required fields are empty
			if(!oRow.fixedVal && (!oRow.UsmdEntity || !oRow.UsmdAttribute) && !(oRow.Paranthesis === ")")) {
				return {
					FORMULA_PREVIEW_TEXT: this._geti18nText("formulatable.previewtext.validationError"),
					bError: true
				};
			}

			if(oRow.Paranthesis) { arrFormulaText.push(oRow.Paranthesis); }
			if(oRow.UsmdEntity && oRow.UsmdAttribute) { arrFormulaText.push("{{" + oRow.UsmdEntity + "-" + oRow.UsmdAttribute + "}}"); }
			if(oRow.fixedVal) { arrFormulaText.push(oRow.fixedVal); }
			if(oRow.Operator) { arrFormulaText.push(oRow.Operator); }
		}
		sFormulaText = arrFormulaText.join(" ");

		return {
			FORMULA_PREVIEW_TEXT: sFormulaText,
			bError: false
		};
	};

	FormulaTable._updateMathFormulaPreview = function(oMasterData, arrTable){

		let oThisFragment = this;

		let oFormulaResponse = this.buildMathPreviewText(arrTable);

		if(oFormulaResponse.bError) {
			return Promise.resolve(oFormulaResponse);
		}

		let sFormulaText = oFormulaResponse.FORMULA_PREVIEW_TEXT;
		
		let arrValidatedTable = [];
		// Make a copy of the table data and remove all the attribute list information
		jQuery.extend(true, arrValidatedTable, arrTable);
		arrValidatedTable.forEach((oRow) => {
			delete oRow.arrAttributeList;
			delete oRow.attributeListBusy;
			oRow.FixedVal = oRow.fixedVal;
			delete oRow.fixedVal;
		});
		
		let oDataToWrite = {
			"Transport": undefined,
			"UsmdCreqType": oMasterData.CRTYPE,
			"RsEntity": oMasterData.ENTITY,
			"RsAttribute": oMasterData.ATTRIBUTE,
			"UsmdModel": oMasterData.DATAMODEL,
			"MODELTOCONCAT": arrValidatedTable,
			"MODELTOMESSAGE": [
				{
					"UsmdModel": oMasterData.UsmdModel,
					"MessageType": "",
					"Message": ""
				}
			]
		};

		let promise = new Promise((resolve) => {
			let oWriteParameterObject = {
				success: function (oDataResult) {
					let sPreviewMessage = "";
					let arrMessages = oDataResult.MODELTOMESSAGE.results;
					let bError = false;
					arrMessages?.forEach((oMessage) => {
						if(oMessage.MessageType === "E") {
							bError = true;
						}
						if(oMessage.Message) {
							sPreviewMessage += oMessage.Message + "\n";
						}
					});
					if(bError === false){
						sPreviewMessage = sFormulaText;
					}

					resolve({
						FORMULA_PREVIEW_TEXT: sPreviewMessage,
						bError: bError
					});
				},
				error: function (error) {
					if(error.aborted) {
						// Abort due to a new request, do not treat it as success.
						resolve({
							FORMULA_PREVIEW_TEXT: oThisFragment._geti18nText("formulatable.previewtext.validating"),
							bError: true
						});
						return;
					}

					resolve({
						FORMULA_PREVIEW_TEXT: oThisFragment._geti18nText("formulatable.previewtext.unableToValidate"),
						bError: true
					});
				}
			};

			// if there is a pending request, abort it
			if(oThisFragment.ValidateRequest) {
				oThisFragment.ValidateRequest.abort();
			}

			let oDataModel = this.COMPONENT.getModel("VALIDATE_EXPRESSION");
			oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
			oThisFragment.ValidateRequest = oDataModel.create("/MODELSet", oDataToWrite, oWriteParameterObject);
		});

		return promise;

	};

	// This function uses the provided table data and delimiter to build the preview text. This is used internally and also from the main component. 
	FormulaTable.buildConcatePreviewText = function(arrTable, sDelimiter){
		let arrPreviewText = [];
		let bError = false; 

		// Get the values from each row and append if there is no error 
		for(let i = 0; i < arrTable.length; i++){
			let oRow = arrTable[i];

			if(oRow.UsmdAttribute && oRow.UsmdEntity) {
				arrPreviewText.push("{{" + oRow.UsmdEntity + "-" + oRow.UsmdAttribute + "}}");
			} else if (oRow.fixedVal) {
				arrPreviewText.push(oRow.fixedVal);
			} else {
				bError = true;
				// Clear the array and assign the error message
				arrPreviewText = [this._geti18nText("formulatable.previewtext.validationError")]; 
				break;
			}
		}

		return {
			FORMULA_PREVIEW_TEXT: arrPreviewText.join(sDelimiter),
			bError: bError
		};
	};

	FormulaTable._updateStringConcatPreview = function(arrTable, sDelimiter){
		let oResponseInformation = this.buildConcatePreviewText(arrTable, sDelimiter);
		return Promise.resolve(oResponseInformation);
	};

	FormulaTable._updatePlaceHolderPreview = function(arrTable, sPreviewText){
		let bError = false;
		// Get the value, replace the place holder values at the respective placeholder locations
		for(let i = 0; i < arrTable.length; i++){
			let oRow = arrTable[i];
			let sPlaceHolder = oRow.placeholder;
			let sReplacementText = oRow.fixedVal;
			// No fixed value, use entity arrtibute place holder. Valid only if the list for entity and attribute have been retrieved
			if(!oRow.fixedVal && 
				(oRow.UsmdEntity && oRow.UsmdAttribute && (oRow.arrAttributeList?.length > 0))){
				sReplacementText = "{{" + oRow.UsmdEntity + "-" + oRow.UsmdAttribute + "}}";
			}

			// If no placeholder option has been chose, display error
			if(!sReplacementText ) {
				bError = true;
				sPreviewText = " - " + this._geti18nText("formulatable.previewtext.validationError");
				break;
			} else {
				sPreviewText = sPreviewText.replaceAll(sPlaceHolder, sReplacementText);
			}
		}

		let arrSplitString = sPreviewText.split("-");
		arrSplitString.shift();
		sPreviewText = arrSplitString.join("-");

		return Promise.resolve({
			FORMULA_PREVIEW_TEXT: sPreviewText,
			bError: bError
		});
	};

	FormulaTable.getFragmentModel = function(oComponent){
		// Initialize the data model
		let oJSONModel = oComponent.getModel("formulaTableModel");
		if(!oJSONModel) {
			oJSONModel = new JSONModel();
			oComponent.setModel(oJSONModel, "formulaTableModel");
		}

		return oJSONModel;
	};

	/*
	*	Create and open the Formula Table Fragment
	*	oComponent refers to the Component
	*/
	FormulaTable.openFormulaTableDialog = function(oComponent) {
		let oFragmentDialog = this;

		// Open the Formula table dialog
		oComponent._getFormulaTableDialog()
		.then(function(oFormulaTableDialog){
			oFormulaTableDialog.open();
		});
		
		oFragmentDialog._updateEntityList(oComponent);
	};                          
	
	/**
	 * Update the entity list, the attribute list and the selection validations
	 */
	FormulaTable._updateEntityList= function(oComponent) {
		let oFragmentDialog = this;
		let oFragmentTableModel = oFragmentDialog.getFragmentModel(oComponent);
		let oFragmentData = oFragmentTableModel.getProperty("/");

		oFragmentTableModel.setProperty("/updatingDataBusy", true);

		let promise = new Promise(function(resolve){
			// Retrieve the entity list to be populated
			let oData = oFragmentData.DATA;
			GetListsModel.getEntityList(oComponent, oData.DATAMODEL, oData.CRTYPE, false)
			.then(function(arrEntityList) {
				let arrFilteredList = arrEntityList;
				if(oData.ISCROSSENTITY){
					arrFilteredList = arrEntityList.filter(entity => entity.UsmdEntity !== oData.ENTITY);
				} else 
				// Do not allow the user to select any entity other than the one provided in the settings
				if (oData.RESTRICTENTITYSELECTION && oFragmentData.selectedType === "CONCAT") {
					arrFilteredList = arrEntityList.filter(entity => entity.UsmdEntity === oData.ENTITY);
				}

				oFragmentTableModel.setProperty("/ENTITYLIST", arrFilteredList);
				return arrFilteredList;
			})
			/**
			 * Run through the rows 
			 * 	1. Check if the entity selected exists in the entity list. Reset Entity if not existing. 
			 *  2. Update the Attribute list 
			 */
			.then(async function(arrFilteredList){
				let oTableData = oData.TABLE;
				for(let i = 0; i < oTableData.length; i++){
					let oRowData = oTableData[i];
					// Check if the entity has been assigned and if it exists in the entity list 
					let oSelectedEntity = arrFilteredList.find((entity) => entity.UsmdEntity === oRowData.UsmdEntity);
					if(!oSelectedEntity){
						oRowData.UsmdEntity = undefined;
					}

					// Entity exists, update the attribute list 
					await oFragmentDialog._updateAttributeListForRow(oRowData.UsmdEntity, i);
				}
				resolve();
			})
			.finally(() => 	oFragmentTableModel.setProperty("/updatingDataBusy", false));
		});

		return promise;
	};

	/*
	* Click Event on Save Button
	* On click of Button, prepare the expected text to be shown to user
	* Close the dialog and send all details back to ValueCheckRule Model
	*/
	FormulaTable.onFormulaTableSave = async function() {
		let oFragmentDialog = this;
		let oFragmentTableModel = oFragmentDialog.getFragmentModel(this.COMPONENT);
		let oFragmentData = oFragmentTableModel.getProperty("/");
		let oFormulaTableDialog = await this.COMPONENT._getFormulaTableDialog();

		// Fill the completed table and return to the component
		this.COMPONENT.onCompleteFormulaEdit(oFragmentData.DATA);

		oFormulaTableDialog.close();
	};

	/*
	* Selection Change Event on Entity Column
	* On Selection of Entity, collect list of Attributes from server and bind item aggregation for current row
	* On change of Entity, re-bind the attribute list. 
	* Clear the attribute selection if previously selected attribute is not a part of re-binded list
	*/
	FormulaTable.onChangeEntity = function(oEvent) {
		let sChangeRowPath = oEvent.getSource().getBindingContext("formulaTableModel").getPath();
		let iChangeRowIndex = parseInt(sChangeRowPath.substring(sChangeRowPath.lastIndexOf("/") + 1));
		let sSelectedEntity = oEvent.getSource().getSelectedKey();

		this._updateAttributeListForRow(sSelectedEntity, iChangeRowIndex);
	};
	
	FormulaTable._updateAttributeListForRow = function(sSelectedEntity, iChangeRowIndex) {
		let oFragmentDialog = this;
		let oComponent = oFragmentDialog.COMPONENT;
		let oFragmentTableModel = oFragmentDialog.getFragmentModel(oComponent);
		let oFragmentData = oFragmentTableModel.getProperty("/");

		let oData = oFragmentData.DATA;
		let oTableData = oData.TABLE;
		
		oTableData[iChangeRowIndex].attributeListBusy = true;

		let promiseAttributeList = 
			GetListsModel.getEntityAttributeList(oComponent, oData.DATAMODEL, sSelectedEntity, undefined);
		promiseAttributeList.then(function(arrAttributeList){
			if(oFragmentData.selectedType !== "MATH") {
				oTableData[iChangeRowIndex].arrAttributeList = arrAttributeList.filter((oAttribute) => {
					return oAttribute.DATATYPE === "CHAR" || oAttribute.DATATYPE === "CUKY" || oAttribute.DATATYPE === "LANG" || oAttribute.DATATYPE === "LCHR" || 
							oAttribute.DATATYPE === "LRAW" || oAttribute.DATATYPE === "NUMC" || oAttribute.DATATYPE === "PREC" || oAttribute.DATATYPE === "RAW" || 
							oAttribute.DATATYPE === "RSTR" || oAttribute.DATATYPE === "SSTR" || oAttribute.DATATYPE === "STRG" || oAttribute.DATATYPE === "UNIT" ;
				});
			} else {
				oTableData[iChangeRowIndex].arrAttributeList = arrAttributeList.filter((oAttribute) => {
					return oAttribute.DATATYPE !== "CHAR" && oAttribute.DATATYPE !== "CUKY" && oAttribute.DATATYPE !== "LANG" && oAttribute.DATATYPE !== "LCHR" && 
							oAttribute.DATATYPE !== "LRAW" && oAttribute.DATATYPE !== "PREC" && oAttribute.DATATYPE !== "RAW" && 
							oAttribute.DATATYPE !== "RSTR" && oAttribute.DATATYPE !== "SSTR" && oAttribute.DATATYPE !== "STRG" && oAttribute.DATATYPE !== "UNIT" &&
							oAttribute.DATATYPE !== "DATS" && oAttribute.DATATYPE !== "TIMS";
				});
			}
			
			let oMatchedAttribute = oTableData[iChangeRowIndex].arrAttributeList.find((oAttribute) => {
				return oAttribute.UsmdAttribute === oTableData[iChangeRowIndex].UsmdAttribute;
			});
			if(!oMatchedAttribute) {
				oTableData[iChangeRowIndex].UsmdAttribute = "";
			}
			
		})
		.finally(function(){
			oTableData[iChangeRowIndex].attributeListBusy = false;
			oFragmentTableModel.setProperty("/DATA/TABLE", oTableData);
		});

		return promiseAttributeList;
	};

	/**
	 * Formatter function on editable property for Entity and Attribute
	 * Controls to be disabled when user enters fixed value
	 */
	FormulaTable.editEntityAttribute = function(sType, sFixedVal, arrEntityList) {

		if(!arrEntityList || arrEntityList.length === 0) {
			return false;
		}

		if(sFixedVal) {
			return false;
		} else {
			return true;
		}
	};
	
	/**
	 * Formatter function on editable property for Fixed Value
	 * Controls to be disabled when user enters entity or attribute
	 */
	FormulaTable.editFixedVal = function(sType, sUsmdEntity, sUsmdAttribute) {

		if(sUsmdEntity || sUsmdAttribute) {
			return false;
		} else {
			return true;
		}
	};

	/**
	 * Check the validity of the entered value. Any value is valid for the placeholder and concatenate.
	 * For the Math, the value should be a number
	 * 
	 */
	FormulaTable.checkFixedValue = function(sFixedVal, sSelectedType){
		if(sSelectedType === "MATH") {
			if(isNaN(sFixedVal)){
				return "Error";
			}
		}

		return "None";
	};

	/**
	 * Clean to be performed on dialog close. 
	 */
	FormulaTable.beforeDialogClose = async function() {
		let oJSONModel = this.getFragmentModel(this.COMPONENT);
		oJSONModel.setProperty("/", {});
	};

	FormulaTable.onFormulaTableCancel = async function(){
		let oFormulaTableDialog = await this.COMPONENT._getFormulaTableDialog();
		oFormulaTableDialog.close();
	};

		/*
	* Click Event on Add
	* If user has selected a row, then add row after the selected row. Else, append the row at the end
	*/
	FormulaTable.onAddTableRow = function() {
		let oComponent = this.COMPONENT;
		let oFormulaTableModel = this.getFragmentModel(oComponent);
		let oFormulaTableDetails = oFormulaTableModel.getProperty("/");

		let iSelectedRowIndex = this._getTableSelectedIndex(true);
		
		let oNewRow = { UsmdModel: oComponent.getDataModel() };
		oFormulaTableDetails.DATA.TABLE.splice(iSelectedRowIndex + 1, 0, oNewRow);		
		oFormulaTableModel.refresh();
	};
	
	/**
	 * Click Event on Delete
	 * If user has selected a row, then delete row. Else, show an error popup for user to select a row before deleting
	 */
	FormulaTable.onDeleteTableRow = function() {
		let oComponent = this.COMPONENT;
		let oFormulaTableModel = this.getFragmentModel(oComponent);
		let oFormulaTableDetails = oFormulaTableModel.getProperty("/");

		let iSelectedRowIndex = this._getTableSelectedIndex(false);

		// If nothng is selected, show an error message
		if(iSelectedRowIndex === -1) {
			Utilities.showPopupAlert(
				this._geti18nText("formulatable.delete.rownotselected.error.message"), 
				MessageBox.Icon.ERROR, 
				this._geti18nText("formulatable.delete.rownotselected.error.title"));
			return;
		}

		oFormulaTableDetails.DATA.TABLE.splice(iSelectedRowIndex, 1);
		oFormulaTableModel.refresh();
	};
	
	FormulaTable.onChangeSelectionType = async function() {
		let oFormulaTableModel = this.getFragmentModel(this.COMPONENT);
		let oFormulaTableDetails = oFormulaTableModel.getProperty("/");
		let arrFormulaTableArray = oFormulaTableDetails.DATA.TABLE;
		let iItems = arrFormulaTableArray.length;
		
		// Store the translated formula type back to the Component
		// Default to CONCAT or PLACEHOLDER
		oFormulaTableDetails.DATA.FORMULA_TYPE_SELECTED = "1";
		if(oFormulaTableDetails.selectedType === "MATH") {
			oFormulaTableDetails.DATA.FORMULA_TYPE_SELECTED = "2";
		}

		// If the selection is not "MATH", clear the Paranthesis and Operator fields
		if(oFormulaTableDetails.selectedType !== "MATH") {
			for(let i = 0; i < iItems; i++){
				arrFormulaTableArray[i].Paranthesis = "";
				arrFormulaTableArray[i].Operator = "";
			}
		}
		oFormulaTableModel.refresh();

		// Update the Entity and Attribute List 
		this._updateEntityList(this.COMPONENT);
	};

	/**
	 * return -1 if nothing is seleted, else the selected row index (0 based)
	 * @param {*} bReturnRowCount  if true, return the row count if nothing is selected. Used in add so that an element could be added at the end.
	 */
	FormulaTable._getTableSelectedIndex = function(bReturnRowCount){
		let oFormulaTable = this._getTableUiComponent();
		let oSelectedItem = oFormulaTable.getSelectedItem();
	
		let iSelectedIndex = -1;
		if(oSelectedItem) {
			let sSelectedRowPath = oSelectedItem.getBindingContextPath();
			//Get the row index selected
			iSelectedIndex = parseInt(sSelectedRowPath.split("/").pop());
		}

		if(iSelectedIndex === -1 && bReturnRowCount) {
			iSelectedIndex = oFormulaTable.getItems().length;
		}

		if(oSelectedItem){
			oSelectedItem.setSelected(false);
		}

		return iSelectedIndex;
	};

	FormulaTable._getTableUiComponent = function(){
		let oComponent = this.COMPONENT;
		let oFormulaTable = sap.ui.getCore().byId(oComponent.getId() + "---FormulaTableDialog" + "--idFormulaTable");
		return oFormulaTable;
	};

	FormulaTable._geti18nText = function(i18nKey, arrParameters) {
		// If the resource bundle has not been read already, read it 
		if(this.i18nResourceBundle === undefined) {
			this.i18nResourceBundle = this.COMPONENT.getModel("i18n").getResourceBundle();
		}

		let sTextToDisplay = this.i18nResourceBundle.getText(i18nKey, arrParameters, false);
		return sTextToDisplay;
	};
	
	return FormulaTable;
});