sap.ui.define([
	
], function () {
	"use strict";
	return {
		/**
		 * Retrieve the details of all the attribute that match criteria 
		 * 0. oController : The Component Handle
		 * 1. sModelName: DataMode Name [Mandatory Parameter] 
		 * 2. sEntityName: Entity Name [Mandatory Parameter]
		 * 3. sAttributeName: Attribute Name [Optional Parameter]
		 */
		getEntityAttributeList: function(oController, sModelName, sEntityName, sAttributeName) {
			let promise = new Promise(function (resolve, reject) {
				
				let sFilterParam = "UsmdModel eq '" + sModelName + "' and UsmdEntity eq '" + sEntityName + "'";
				
				// If attribute name is provided add the filter criteria
				if(sAttributeName)
				{
					sFilterParam += " and UsmdAttribute eq '" + sAttributeName + "'";
				}
	
				let oUrlParameters = {
					"$filter": sFilterParam,
					"$format": "json"
				};
				
				let oDataModel = oController.getModel("GETATTR_DETAILS_LIST");
				oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
				
				let oGetParameterObject = {
					method: "GET",
					success: function (oData) {
						resolve(oData.results); 
					},
					error: function (oErrorData) {
						reject(oErrorData);
					},
					urlParameters: oUrlParameters
				};
				
				oDataModel.read("/ATTR_DISPSet", oGetParameterObject);
			});

			return promise; 
		},
		
		/**
		 * Retrieve the list of entities for the specified Data Model 
		 * 0. oController: Component handle
		 * 1. sModelName: DataMode Name [Mandatory Parameter]
		 * 2. sCRType: Change Request Type ID [Optional Parameter]
		 * 3. bShowNotEntity: Boolean to determine properties with IsNotEntity (like CR_HEADER) should be shown in entity list [Optional Parameter]
		 */
		getEntityList: function(oController, sModelName, sCrType, bShowNotEntity) {
			let promise = new Promise(function(resolve, reject) {
				let sFilters = "UsmdModel eq '" + sModelName + "'";
				
				// Bug 12199 - Field Property Shows Entities which are not part of the CR type - 0G
				if (sCrType) {
					sFilters = sFilters + " and Usmdcreqtype eq '" + sCrType + "'";
				}

				let oUrlParameters = {
					"$filter": sFilters,
					"$format": "json"
				};
				
				let oDataModel = oController.getModel("GET_ENTITY_LIST");
				oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});

				let oGetParameterObject = {
					method: "GET",
					success: function (oData) {
						let arrEntities = [];
						if(!bShowNotEntity) {
							arrEntities = oData.results.filter(oEntity => {
								return oEntity.Isnotentity !== "X";
							});
						} else {
							arrEntities = oData.results;
						}
						resolve(arrEntities);
					},
					error: function (oErrorData) {
						reject(oErrorData);
					},
					urlParameters: oUrlParameters
				};
				
				oDataModel.read("/ENTITY_DISPSet", oGetParameterObject);
			});
			return promise;
		}
		
	};
});