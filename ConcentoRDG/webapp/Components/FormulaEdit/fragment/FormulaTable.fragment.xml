<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc"
	xmlns:u="sap.ui.unified"
	xmlns:c="sap.ui.core"
	xmlns="sap.m"
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form"
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="dmr.components"
	displayBlock="true">
	<Dialog title="{i18n>formulatable.title}" contentWidth="60%" beforeClose=".beforeDialogClose">
		<l:VerticalLayout class="sapUiContentPadding" width="100%" busy="{formulaTableModel>/updatingDataBusy}">
			<l:content>
				<Table id="idFormulaTable" alternateRowColors="true" width="100%" rememberSelections="false" 
				items="{path: 'formulaTableModel>/DATA/TABLE'}" mode="SingleSelectMaster">
					<headerToolbar>
						<OverflowToolbar>
							<HBox alignItems="Center" class="sapUiSmallMarginEnd">
								<Label text="Type" labelFor="idFormulaType" required="true" class="sapUiSmallMarginEnd"/>
								<Select 
									forceSelection="true" selectedKey="{formulaTableModel>/selectedType}" id="idFormulaType" 
									items="{path:'formulaTableModel>/formulaTableTypeList'}" 
									change=".onChangeSelectionType">
										<c:Item key="{formulaTableModel>key}" text="{formulaTableModel>text}"/>
								</Select>
							</HBox>
							<HBox alignItems="Center" visible="{= ${formulaTableModel>/selectedType} === 'CONCAT'}">
								<Label text="{i18n>formulatable.delimiter.label}" labelFor="idDelimTable" class="sapUiSmallMarginEnd"/>
								<Input id="idDelimTable" value="{formulaTableModel>/DATA/DELIMITER}" placeholder="{i18n>formulatable.delimite.placeholder}"/>
							</HBox>
							<ToolbarSpacer/>
							<Button icon="sap-icon://add" text="{i18n>formulatable.button.add.text}" id="addRow" press=".onAddTableRow" visible="{= ${formulaTableModel>/selectedType} !== 'PLACEHOLDER'}"/>
							<Button icon="sap-icon://delete" text="{i18n>formulatable.button.delete.text}" id="deleteRow" press=".onDeleteTableRow" visible="{= ${formulaTableModel>/selectedType} !== 'PLACEHOLDER'}"/>
						</OverflowToolbar>
					</headerToolbar>
					<infoToolbar>
					
					</infoToolbar>
					<columns>
						<Column width="6em" hAlign="Center" visible="{= ${formulaTableModel>/selectedType} === 'MATH'}">
							<Label design="Bold" text="( )" />
						</Column>
						<Column visible="{= ${formulaTableModel>/selectedType} === 'PLACEHOLDER'}" >
							<Label design="Bold" text="{i18n>formulatable.column.placeholder.title}" wrapping="true"/>
						</Column>
						<Column>
							<Label design="Bold" text="{i18n>formulatable.column.entity.title}" wrapping="true"/>
						</Column>
						<Column>
							<Label design="Bold" text="{i18n>formulatable.column.attribute.title}" wrapping="true"/>
						</Column>
						<Column>
							<Label design="Bold" text="{i18n>formulatable.column.fixedvalue.title}" wrapping="true"/>
						</Column>
						<Column width="6em" hAlign="Center" visible="{= ${formulaTableModel>/selectedType} === 'MATH'}">
							<c:Icon src="sap-icon://simulate" size="1.5em"/>
						</Column>
					</columns>
					<items>
						<ColumnListItem highlight="{formatter: '.columnCompletionCheck', parts: ['formulaTableModel>/selectedType', 'formulaTableModel>fixedVal', 'formulaTableModel>UsmdEntity',  'formulaTableModel>UsmdAttribute', 'formulaTableModel>arrAttributeList', 'formulaTableModel>/DATA/DELIMITER', 'formulaTableModel>Operator', 'formulaTableModel>Paranthesis']}"> 
							<cells>
								<Select id="idSelectForTabParanthesis" 
									visible="{= ${formulaTableModel>/selectedType} === 'MATH'}"
									selectedKey="{formulaTableModel>Paranthesis}">
									<items>
										<c:Item key="" text=""/>
										<c:Item key="(" text="("/>
										<c:Item key=")" text=")"/>
									</items>
								</Select>
								
								<Input id="idPlaceholder" value="{formulaTableModel>placeholder}" 
									visible="{= ${formulaTableModel>/selectedType} === 'PLACEHOLDER'}"
									editable="false"/>

								<ComboBox width="100%" id="idCBoxForTabEntities"
									selectedKey="{formulaTableModel>UsmdEntity}"
									items="{path: 'formulaTableModel>/ENTITYLIST', templateShareable:false, length: 2000}" 
									editable="{formatter: '.editEntityAttribute', parts: ['formulaTableModel>/selectedType', 'formulaTableModel>fixedVal', 'formulaTableModel>/ENTITYLIST']}"
									selectionChange=".onChangeEntity">
									<c:Item key="{formulaTableModel>UsmdEntity}"
										text="{= ${formulaTableModel>UsmdEntity}.concat(${formulaTableModel>Txtlg}==='' ? '' : ' - '.concat(${formulaTableModel>Txtlg}))}"/>
								</ComboBox>
								
								<ComboBox id="idCBoxForTabAttributes" width="100%"
									selectedKey="{formulaTableModel>UsmdAttribute}"
									busy="{formulaTableModel>attributeListBusy}"
									busyIndicatorDelay="0"
									editable="{formatter: '.editEntityAttribute', parts: ['formulaTableModel>/selectedType', 'formulaTableModel>fixedVal', 'formulaTableModel>/ENTITYLIST']}"
									items="{path: 'formulaTableModel>arrAttributeList', templateShareable:false, length: 2000}" >
									<c:ListItem key="{formulaTableModel>UsmdAttribute}"
										text="{formulaTableModel>UsmdAttribute} - {formulaTableModel>Txtlg}"
										additionalText="{formulaTableModel>Txtlg}"/>
								</ComboBox>
								
								<Input id="idIpForTabFixedValue" value="{formulaTableModel>fixedVal}" valueLiveUpdate="true"
								editable="{formatter: '.editFixedVal', parts: ['formulaTableModel>/selectedType', 'formulaTableModel>UsmdEntity', 'formulaTableModel>UsmdAttribute']}"
								valueStateText="{i18n>formulatable.column.fixedvalue.valuetext}"
								valueState="{formatter:'.checkFixedValue', parts: ['formulaTableModel>fixedVal', 'formulaTableModel>/selectedType']}"
								/>
								
								<Select id="idSelectForTabOperator" selectedKey="{formulaTableModel>Operator}">
									<items>
										<c:Item key="" text=""/>
										<c:Item key="+" text="+"/>
										<c:Item key="-" text="-"/>
										<c:Item key="*" text="*"/>
										<c:Item key="/" text="/"/>
									</items>
								</Select>
							</cells>
						</ColumnListItem>
					</items>
				</Table>
				<VBox alignItems="Start" >
					<ObjectStatus text="{i18n>formulatable.preview.text}" 
						state="{= !${formulaTableModel>/DATA/completionError}? 'Success': 'Error'}"/>
					<Title id="idDerivPreviewText" level="H4" width="100%" wrapping="true"
						text="{formulaTableModel>/DATA/FORMULA_PREVIEW_TEXT}"/>
				</VBox>
			</l:content>
		</l:VerticalLayout>

		<beginButton> 
			<Button 
				text="{i18n>formulatable.button.save.text}" 
				enabled="{= !${formulaTableModel>/DATA/completionError}}"
				press=".onFormulaTableSave" id="save"/>
		</beginButton>
		<endButton>
			<Button text="{i18n>formulatable.button.cancel.text}" press=".onFormulaTableCancel" id="cancel"/>
		</endButton>

	</Dialog>
	
</c:FragmentDefinition>