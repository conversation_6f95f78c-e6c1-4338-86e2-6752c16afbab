sap.ui.define([
	"sap/ui/core/UIComponent",
	"sap/ui/model/json/JSONModel",
	"sap/ui/core/BusyIndicator",
    "sap/m/MessageBox",
	"./TransportRequestType"
], function(UIComponent, JSONModel, BusyIndicator, MessageBox, TransportRequestType){
	"use strict";
	let Component = UIComponent.extend("dmr.components.TransportPackage.Component", {
		metadata: {
			manifest: "json",
			"abstract": false, 
			properties: {
				// The user name must be passed for this to function correctly
				user: {type: "string", name: "user"},
                // Parent Component provided input. Returned back as is...
                sourceContext: {type: "string", defaultValue: null},
			},
			aggregations: {},
			events: {
				// Return the selected value through the event
				selectionComplete: {
					parameters: {
						selection: {type: "object"}
					}
				}
			}
		}
	});
    /**
     * Create and return a model 
     */
    Component.prototype._getModel = function() {
        let MODEL_NAME = "transportPackageModel";

        let oModel = this.getModel(MODEL_NAME);
        if(oModel === undefined) {
            this.setModel(new JSONModel(), MODEL_NAME);
            oModel = this.getModel(MODEL_NAME);
        }

        return oModel;
    };

    /* Read and return the property specified */
    Component.prototype._getModelProperty = function(sPropertyPath){
        return this._getModel().getProperty(sPropertyPath);
    };

    Component.prototype._setModelProperty = function(sPropertyPath, anyValue){
        this._getModel().setProperty(sPropertyPath, anyValue);
    };

	/**
	 * Just create the base information and the model
	 */
	Component.prototype.init = function(){
		// Call the parent init
		UIComponent.prototype.init.apply(this, arguments);
		
        this._getModel();
	};

	/**
	 * Create the content. 
	 *  > The dialog 
	 *  > Button [Should be marked invisible, except for debugging]
	 * 
	 * The button is created as a base on which to attach the dialog to.  This will allow the model
	 * to be passed on the dialog. 
	 */
	Component.prototype.createContent = function() {
		let oTransportSelectionDialog, oTransportCreationDialog, oButton;
		
		// Create the transport Selection Dialog
		oTransportSelectionDialog = this._getTransportPackageSelectionDialog();
		oButton = this._getOpenButton();
		oButton.addDependent(oTransportSelectionDialog);

		// Create the transport Creation Dialog
		oTransportCreationDialog = this._getTransportCreationDialog();
		oButton.addDependent(oTransportCreationDialog);

		return oButton;
	};

    /**
	 * The dialog fragment that is to be displayed when this component is invoked
	 */
	Component.prototype._getTransportPackageSelectionDialog = function() {
		if(!this._oTSD) {
			this._oTSD = sap.ui.xmlfragment(
				this.getId(), 
				"dmr.components.TransportPackage.fragments.TransportPackageSelectionDialog",
				this
			);
		}
		
		return this._oTSD;
	};

    /**
	 * Public open function to be used by the parent components. 
	 * This opens the dialog and retrieves the data to be displayed in the dialog. 
     * 
     * Returns a promise that can be waited on to receive the selected items
     * @param bShowCustomizingTransport: Display the customizing transport selection
     * @param sPreselectCustomizingTr: The transport that is to be preselected 
     * @param bShowWorkbenchTransport: Display the workbench transport selection
     * @param sPreselectWorkbenchTr: The transport that is to be preselected
     * @param bShowPackage: Display the package selection
     * @param sPreselectPackage: The package to be preselected
     * @param bFilterTransportByPackageClass: 
     *      true: filter the transports based on selected package
     *      false: filter the package based on transport selected
     *      undefined: No filtering
	 */
	Component.prototype.open = function (
            bShowCustomizingTransport, 
            sPreselectCustomizingTr,
            bShowWorkbenchTransport,
            sPreselectWorkbenchTr,
            bShowPackage, 
            sPreselectPackage,
            bFilterTransportByPackageClass) {

        let thisComponent = this;

        // validate inputs 
        if(bShowCustomizingTransport === undefined || bShowWorkbenchTransport === undefined || bShowPackage === undefined){
            throw new Error("TransportPackage selection component parameters invalid");
        }

        // Read the properties to initialize previous content if necessary. Used for searchText only (currently)
        let oModelProperties = this._getModelProperty("/");

        // Default information based on inputs
        this._setModelProperty("/", {
            excludeLocalTransports: true,
            customizing: {
                show: bShowCustomizingTransport,
                preSelect: sPreselectCustomizingTr,
                searchText: oModelProperties?.customizing?.searchText,
                list: [],
                busy: false 
            },
            workbench: {
                show: bShowWorkbenchTransport,
                preSelect: sPreselectWorkbenchTr,
                searchText: oModelProperties?.workbench?.searchText,
                list: [],
                busy: false
            },
            package: {
                show: bShowPackage,
                preSelect: sPreselectPackage,
                searchText: oModelProperties?.package?.searchText,
                list: [],
                busy: false
            },
            bFilterTransportByPackageClass: bFilterTransportByPackageClass
        });

        let promiseTransportPackageDialog = new Promise(function(resolve, reject){
            thisComponent.promiseResolve = resolve;
            thisComponent.promiseReject = reject;
            thisComponent._onShowTransportSelectionDialog();
        });

        return promiseTransportPackageDialog;
	};

	/**
	 * This opens the dialog and retrieves the data to be displayed in the dialog. 
	 */
	Component.prototype._onShowTransportSelectionDialog = function() {
		let thisComponent = this;
	    let oTSD = this._getTransportPackageSelectionDialog();
	    oTSD.open();

        let oModelData = this._getModelProperty("/");

        // Update the user info to the search boxes
        let sUser = this.getUser();
        // sUser = "RLAU";
        
        // Load the user list 
        let promiseGetList = this._getUserList();
        promiseGetList.then(function(arrUsers){
            thisComponent._setModelProperty("/userList", arrUsers);
        });

        this._setDialogWidth(oModelData.customizing.show, oModelData.workbench.show, oModelData.package.show);

        // If Customizing is to be displayed
        if(oModelData.customizing.show === true){
            this._setModelProperty("/customizing/user", sUser);
            this._loadTransports(sUser, TransportRequestType.CUSTOMIZING);
        }

        if(oModelData.workbench.show === true) {
            this._setModelProperty("/workbench/user", sUser);
            this._loadTransports(sUser, TransportRequestType.WORKBENCH);
        }

        if(oModelData.package.show === true) {
            this._getPackagesList();
        }
		
	};

    Component.prototype._getTransportModelPath = function(sTransportType) {
        let sPropertyPath = "/customizing/";
        if(sTransportType === TransportRequestType.WORKBENCH){
            sPropertyPath = "/workbench/";
        }

        return sPropertyPath;
    };

    Component.prototype._loadTransports = function(sUser, sTransportType) {
        let thisComponent = this;
        let sPropertyPath = this._getTransportModelPath(sTransportType);

        thisComponent._setModelProperty(sPropertyPath + "busy", true);
        thisComponent._setModelProperty(sPropertyPath + "list", []);

        let promiseGetList = this._getUserList();
        promiseGetList.then(function(arrUsers){
            let sValidatedUser = sUser;
            let oUserFound = arrUsers.find((oUser) => {
                return oUser.Bname === sUser.toUpperCase();
            });

            if(!oUserFound){
                thisComponent._setModelProperty(sPropertyPath + "userValueState", {
                    state: sap.ui.core.ValueState.Error,
                    text: "User not found"
                });
            } else {
                thisComponent._setModelProperty(sPropertyPath + "userValueState", {
                    state: sap.ui.core.ValueState.None,
                    text: ""
                });
            }

            return sValidatedUser;
        })
        .then(function(sValidatedUser){
            return thisComponent._getTransportList(sValidatedUser, sTransportType);
        })
        .then(function(arrTransports){
            let bExcludeLocalTransports = thisComponent._getModelProperty("/excludeLocalTransports");
            if(bExcludeLocalTransports){
                arrTransports = arrTransports.filter(function(transport){
                    // Filter out transports with no target system
                    if(!transport.Tarsystem){
                        return false;
                    }

                    return true;
                });
            }
            thisComponent._setModelProperty(sPropertyPath + "list", arrTransports);
        })
        .finally(function(){
            thisComponent._setModelProperty(sPropertyPath + "busy", false);
        });
    };

    Component.prototype.onListUpdateFinished = function(oEvent) {

        // This is to be executed only once when the component is opened. If the transport or
        // package is to be preselected. Once the preselect is complete, reset the preselect
        // so that this is not executed again. 
        let oModelInfo = this._getModelProperty("/");
        let bCustomzingList = false; 
        let bWorkbenchList = false; 
        let bPackageList = false;
        let oSource = oEvent.getSource();
        let sListID = oSource.getId();

        if(sListID.endsWith("custTransportRequestList")){
            bCustomzingList = true;
        } else if (sListID.endsWith("packageList")) {
            bPackageList = true;
        } else {
            bWorkbenchList = true;
        }

        // If the event is from the cutomizing list and the preselect is not set, return
        if( bCustomzingList && !oModelInfo.customizing?.preSelect) {
            return;
        }

        // If the event is from the workbench list and the preselect is not set, return
        if( bWorkbenchList && !oModelInfo.workbench?.preSelect) {
            return;
        }

        // If the event is from the package list and the preselect is not set, return
        if( bPackageList && !oModelInfo.package?.preSelect) {
            return;
        }

        let sPreselectTransport = undefined;
        if(bCustomzingList) {
            sPreselectTransport = oModelInfo.customizing.preSelect;
        } else if (bWorkbenchList) {
            sPreselectTransport = oModelInfo.workbench.preSelect;
        } else {
            sPreselectTransport = oModelInfo.package.preSelect;
        }

        let oItemToSelect = oSource.getItems().find(function(item){
            let sTitle = item.getTitle();
            if(sTitle === sPreselectTransport){
                return true;
            }
            return false;
        });

        if(oItemToSelect){
            oItemToSelect.focus();
            oSource.setSelectedItem(oItemToSelect);
            oSource.fireSelectionChange({
                listItem: oItemToSelect,
                selected: true
            });

            // Clear if there is a selection. Sometimes the event fires before the list is refreshed
            if(bCustomzingList) {
                // Clear the preselect setting
                this._setModelProperty("/customizing/preSelect", undefined);
            } else if (bWorkbenchList) {
                // Clear the preselect setting
                this._setModelProperty("/workbench/preSelect", undefined);
            } else {
                // Clear the preselect setting
                this._setModelProperty("/package/preSelect", undefined);
            }
        }
    };

    Component.prototype._getUserList = function() {
        let thisComponent = this;

        // if the get request has already be sent, return the promise
        if (thisComponent.promiseUserList){
            return thisComponent.promiseUserList;
        }

        // else generate a promise and return
        thisComponent.promiseUserList = new Promise(function(resolve, reject){
            let oUserSearhParameters = {
                "$filter": "substringof('',Bname)",
                "$format": "json"
            };
            let oUserSearchModel = thisComponent.getModel("userSearch");
            oUserSearchModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
                
            let oGetUserListObject = {
                method: "GET",
                success: function (oData) {
                   thisComponent._setModelProperty("/arrUserList", oData.results);
                   resolve(oData.results);
                },
                error: function () {
                    reject();
                },
                urlParameters: oUserSearhParameters
            };
        
            oUserSearchModel.read("/USERADDRSet", oGetUserListObject);    
        });

        return thisComponent.promiseUserList;
    };

	/** 
	 * Get all the relevant transports based on Username
	 * To be called when
	 *   	Dialog is shown - Show transports of current logged in user
	 *		User value is changed - Show transports of the user selected only if user enters a valid user
	 *		User is selected from suggestion box
	 *		Transport value is changed - Live Search on Transport Search field and user name is not provided
	*/
	Component.prototype._getTransportList = function(sUser, sTrType) {
		let thisComponent = this;

        let promiseTransportList = new Promise(function(resolve, reject){
            let sFilter = "TrType eq '" + sTrType + "'";

            if(sUser) {
				sFilter += " and As4user eq '" + sUser + "'";
            }

            let oUrlParameters = {
                "$filter": sFilter,
                "$format": "json"
            };
            
            let oDataModel = thisComponent.getModel("transportList");
            oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
            
            let oGetParameterObject = {
                method: "GET",
                success: function (oTrData) {
                    resolve(oTrData.results);
                },
                error: function () {
                    reject();
                },
                urlParameters: oUrlParameters
            };
            
            oDataModel.read("/TR_SEARCHSet", oGetParameterObject);
        });

		return promiseTransportList;
	};

    Component.prototype._getPackagesList = function() {
        let thisComponent = this;
		let oDataModel = this.getModel("GET_PACKAGE_LIST");
		oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
	
		let promise = new Promise(function (resolve, reject) {
			thisComponent._setModelProperty("/package/busy", true);
			let oUrlParameters = {
				"$filter": "startswith(Devclass,'" + "Y" + "') or startswith(Devclass,'" + "Z" + "')",
				"$format": "json"
			};
			
			let oGetParameterObject = {
				method: "GET",
				success: function (oData) {

                    let arrPackageList = oData.results.sort(function(package1, package2){
                        return (package1.Devclass > package2.Devclass);
                    });
                    thisComponent._setModelProperty("/package/list", arrPackageList);
                    thisComponent._setModelProperty("/package/busy", false);
					resolve(arrPackageList); 
				},
				error: function (oErrorData) {
                    thisComponent._setModelProperty("/package/busy", false);
					reject(oErrorData);
				},
				urlParameters: oUrlParameters
			};
			
			oDataModel.read("/PACKAGE_SEARCHSet", oGetParameterObject);
		});
		return promise;
	};

	/**
	 * A place holder button to allow attaching the dialog as a dependent. Should be hidden.
	 */
	Component.prototype._getOpenButton = function () {
		if (!this._oBtn) {
			this._oBtn = 
				new sap.m.Button(this.createId("TransportPackage"), {
					text: "Test Button",
					visible: false
				});
			
			// if the visibility is true attach the handler
			if(this._oBtn.getVisible() === true)
			{
				this._oBtn.attachPress(this, this._onShowTransportSelectionDialog, this);
			}
		}
		return this._oBtn;
	};

	/*
	* Change Event for User.
	* Fetch the Transports for the currently selected User and transport type
	* Set appropraite No Data Text
	*/
	Component.prototype.onChangeUser = function(oEvent) {
		let oSource = oEvent.getSource();
        let sSourceName = oSource.getName();
        let sUserName = oSource.getValue();
        let sTransportType = TransportRequestType.WORKBENCH;
        if(sSourceName === "CustomizingUser"){
            sTransportType = TransportRequestType.CUSTOMIZING;
        }

        this._loadTransports(sUserName, sTransportType);
	};

	/**
	 * Called when the search content is changed.
	 * Get the transport list for TR search without user
	 * Filter the list either on the name or description. 
	 * The filtering is case insensitive. 
	 */
	Component.prototype.onSearchTransportList = function (oEvent) {
        let oSource = oEvent.getSource(); 
		let sQuery = oEvent.getSource().getValue(); // Get the search text

        // Get the parent List Search  -> HBox     -> VBox     -> Toolbar  -> List 
        let oListComponent =   oSource.getParent().getParent().getParent().getParent();

		let aFilters; // add filter for search
		if (sQuery && sQuery.length > 0) {
			aFilters = new sap.ui.model.Filter({
				filters: [
					new sap.ui.model.Filter({
							path: "Strkorr",
							operator: sap.ui.model.FilterOperator.Contains,
							value1: sQuery,
							caseSensitive: false
						}),
					new sap.ui.model.Filter({
							path: "As4text",
							operator: sap.ui.model.FilterOperator.Contains,
							value1: sQuery,
							caseSensitive: false
						})
				],
				and: false
			});
		}
		
		// update list binding
        oListComponent
            .getBinding("items")
			.filter(aFilters);
	};

	/**
	 * Called when the search content is changed.
	 * Get the transport list for TR search without user
	 * Filter the list either on the name or description. 
	 * The filtering is case insensitive. 
	 */
	Component.prototype.onSearchPackageList = function (oEvent) {
        let oSource = oEvent.getSource(); 
		let sQuery = oEvent.getSource().getValue(); // Get the search text

        // Get the parent List Search  -> VBox     -> Toolbar  -> List 
        let oListComponent =   oSource.getParent().getParent().getParent();

		let aFilters; // add filter for search
        if (sQuery && sQuery.length > 0) {
			aFilters = new sap.ui.model.Filter({
				filters: [
					new sap.ui.model.Filter({
							path: "Devclass",
							operator: sap.ui.model.FilterOperator.Contains,
							value1: sQuery,
							caseSensitive: false
						}),
					new sap.ui.model.Filter({
							path: "Ctext",
							operator: sap.ui.model.FilterOperator.Contains,
							value1: sQuery,
							caseSensitive: false
						})
				],
				and: false
			});
		}
		
		// update list binding
        oListComponent
            .getBinding("items")
			.filter(aFilters);
	};

    Component.prototype.onChangeExcludeLocalTransports = function(){
        let oModelSettings = this._getModelProperty("/");

        if(oModelSettings.customizing.show){
            this._loadTransports(oModelSettings.customizing.user, TransportRequestType.CUSTOMIZING);
        }
        
        if(oModelSettings.workbench.show){
            this._loadTransports(oModelSettings.workbench.user, TransportRequestType.WORKBENCH);
        }
    };

	/**
	 * The list selection change handler. Used to store the current selected transport. 
	 */
	Component.prototype.onTransportSelectionChange = function(oEvent){
		// Get the selected Transport and store to the model 
		let oSource = oEvent.getSource();
        let sListId = oSource.getId();

        let sTransportType = TransportRequestType.WORKBENCH;
        if(sListId.endsWith("custTransportRequestList")){
            sTransportType = TransportRequestType.CUSTOMIZING;
        }

        let sPath = this._getTransportModelPath(sTransportType);

        this._setModelProperty(sPath + "selectedPath", oSource.getSelectedContexts()[0].getPath());
	};

    Component.prototype.onPackageSelectionChange = function(oEvent){
		// Get the selected Transport and store to the model 
		let oSource = oEvent.getSource();

        this._setModelProperty("/package/selectedPath", oSource.getSelectedContexts()[0].getPath());
	};

    Component.prototype.getSplitterCustomizingWidth = function(bShowCustomizing, bShowWorkbench, bShowPackage) {
        if(bShowCustomizing === false){
            return "0%";
        }

        return this._getSplitterWidth(bShowCustomizing, bShowWorkbench, bShowPackage);
    };

    Component.prototype.getSplitterWorkbenchWidth = function(bShowCustomizing, bShowWorkbench, bShowPackage) {
        if(bShowWorkbench === false){
            return "0%";
        }

        return this._getSplitterWidth(bShowCustomizing, bShowWorkbench, bShowPackage);
    };

    Component.prototype.getSplitterPackageWidth = function(bShowCustomizing, bShowWorkbench, bShowPackage) {
        if(bShowPackage === false){
            return "0%";
        }

        return this._getSplitterWidth(bShowCustomizing, bShowWorkbench, bShowPackage);
    };

    Component.prototype._setDialogWidth = function(bShowCustomizing, bShowWorkbench, bShowPackage){
        let iCount = bShowCustomizing + bShowWorkbench + bShowPackage;
        let iWidth = Math.ceil(60 * (iCount / 3));
        iWidth = iWidth < 30? 30: iWidth;

        this._getTransportPackageSelectionDialog().setContentWidth(iWidth + "%");
    };


    Component.prototype._getSplitterWidth = function(bShowCustomizing, bShowWorkbench, bShowPackage){
        let iListCount = 0;
        
        if(bShowCustomizing){
            iListCount++;
        }

        if(bShowWorkbench) {
            iListCount++;
        }

        if(bShowPackage) {
            iListCount++;
        }

        let sListWidth = "33.3%";
        if(iListCount === 1) {
            sListWidth = "100%";
        } else if (iListCount === 2) {
            sListWidth = "50%";
        }
        return sListWidth;
    };

	/**
	 * The confirm / Select button press on the dialog. Since the dialog select button is enabled only when
	 * an item on the list is selected, it is assured that the necessary transports / package has been
     * selected when this callback is received. 
	 * Fire the event with the selected transport and close the dialog
	 */
	Component.prototype.onTransportSelectionConfirm = function(){
        let oModelInfo = this._getModelProperty("/");
        let bCustomizingSet = true;
        let bWorkbenchSet = true; 
        let bPackageSet = true;
        let oResponse = {
            customizingTransport: undefined,
            workbenchTransport: undefined,
            workbenchTargetSystem: undefined,
            package: undefined,
            sourceContext: this.getSourceContext()
        };

        if(oModelInfo.customizing.show){
            // default to false
            bCustomizingSet = false;
            if(oModelInfo.customizing.selectedPath){
                bCustomizingSet = true;
                oResponse.customizingTransport = 
                    this._getModelProperty(oModelInfo.customizing.selectedPath).Strkorr;
            }
        }

        if(oModelInfo.workbench.show){
            // default to false
            bWorkbenchSet = false;
            if(oModelInfo.workbench.selectedPath){
                bWorkbenchSet = true;
                oResponse.workbenchTransport = 
                    this._getModelProperty(oModelInfo.workbench.selectedPath).Strkorr;
                oResponse.workbenchTargetSystem = 
                    this._getModelProperty(oModelInfo.workbench.selectedPath).Tarsystem;
            }
        }

        if(oModelInfo.package.show){
            // default to false
            bPackageSet = false;
            if(oModelInfo.package.selectedPath){
                bPackageSet = true;
                oResponse.package = 
                this._getModelProperty(oModelInfo.package.selectedPath).Devclass;
            }
        }

        if(bCustomizingSet && bWorkbenchSet && bPackageSet){
            // Close the dialog
            this._getTransportPackageSelectionDialog().close();

       		// Fire the event to the calling component
            this.fireSelectionComplete(oResponse);

            // Retrieve the resolve handler and call
            this.promiseResolve(oResponse);
            return;
        }

        // If a property was not selected
        let sErrorText = "Please select _PARAMS_ to confirm.";
        let arrParams = [];
        if(bCustomizingSet === false) {
            arrParams.push("Customizing Transport");
        }

        if(bWorkbenchSet === false) {
            arrParams.push("Workbench Transport");
        }

        if(bPackageSet === false) {
            arrParams.push("Package");
        }

        sErrorText = sErrorText.replace("_PARAMS_", arrParams.join(", "));

        MessageBox.error(sErrorText, {
            title: "Mandatory selections missing",
            actions: sap.m.MessageBox.Action.OK
        });
	};

	/**
	 * Called on cancel. Close the dialog
	 */
	Component.prototype.onCloseTransportSelectDialog = function(){
        let oThisObject = this;
		this._getTransportPackageSelectionDialog().close();

        let oModelInfo = this._getModelProperty("/");
        let arrSelections = [];
        if(oModelInfo.customizing.show || oModelInfo.workbench.show){
            arrSelections.push("Transport");
        }

        if(oModelInfo.package.show){
            arrSelections.push("Package");
        }

        let sMessage = "No " + arrSelections.join(", ") + " was selected. Action will be cancelled.";

        MessageBox.alert(sMessage, {
            title: "Alert",
            actions: sap.m.MessageBox.Action.OK,
            onClose: function(){
                oThisObject.promiseReject();
            }
        });
	};

    /**
     * Stop the popup from being closed using the escape key
     */
    Component.prototype.onEscapePress = function() {
        let promiseEscape = new Promise(function (resolve){
           resolve(true);
        });

        return promiseEscape;
    };

    /**
	 * The dialog fragment that is to be displayed when this component is invoked
	 */
	Component.prototype._getTransportCreationDialog = function() {
		if(!this._oTCD) {
			this._oTCD = sap.ui.xmlfragment(
				this.getId(), 
				"dmr.components.TransportPackage.fragments.TransportCreation",
				this
			);
		}
		
		return this._oTCD;
	};
	
	/**
	 * On transport creation dialog open request
	 * 1. Open the transport creation Popover
	 */
	Component.prototype.onCreateNewTransport = function(oEvent){
        let oSource = oEvent.getSource();
        let sButtonId = oSource.getId();

        let sTransportType = TransportRequestType.WORKBENCH;
        if(sButtonId.endsWith("idCreateTransportCustomizing")){
            sTransportType = TransportRequestType.CUSTOMIZING;
        }

        this._setModelProperty("/CreateTransport", {
            type: sTransportType,
            description: ""
        });

		// Open the creation dialog
		this._getTransportCreationDialog().openBy(oSource);
	};
	
	/**
	 * Send the request for the transport creation.
	 * If the creation is successful, 
     * 1. Close the popover 
     * 2. Reload the transports 
     * 
	 * If the creation is not successful, show a message and wait
	 */
	Component.prototype.onPressCreateTransport = function(){
		let oThisObject = this;

		// Get the model data from the dialog 
		let oTransportInfo = this._getModelProperty("/CreateTransport");

        let promise = new Promise(function(resolve, reject) {
            let oWriteParameterObject = {
                success: function () {
                    resolve();
                },
                error: function () {
                    reject();
                }
            };

            // Send the save request
            let oDataModel = oThisObject.getModel("transportCreate");
            oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
            // Service payload
            let oDataPayload = {
                "Owner": oThisObject.getUser(),
                "TRType": oTransportInfo.type,
                "TRText": oTransportInfo.description,
                "SubUser": oThisObject.getUser()
            };
            oDataModel.create( "/CREATETRSet", oDataPayload, oWriteParameterObject);
        });

        BusyIndicator.show(0); // Show immedeately
        promise.then(function(){
            // Close the dialog
            oThisObject._getTransportCreationDialog().close();

            // Reload the transports
            oThisObject._loadTransports(oThisObject.getUser(), oTransportInfo.type);
        })
        .catch(function(){
            // Show an error message ..
            sap.m.MessageBox.show("Transport creation failed!", {
                icon: sap.m.MessageBox.Icon.ERROR,
                title: "Error",
                actions: [sap.m.MessageBox.Action.OK]
            });            
        })
        .finally(() => BusyIndicator.hide());
	};
	
	/**
	 * Close the transport creation dialog
	 */
	Component.prototype.onPressCancelCreateTransport = function() {
		// Close the creation dialog 
		this._getTransportCreationDialog().close();
	};
	return Component;
});