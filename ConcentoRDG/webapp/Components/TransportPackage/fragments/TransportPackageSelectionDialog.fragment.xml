<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="dmr.components"
	displayBlock="true">
	<Dialog contentHeight="50vh" escapeHandler=".onEscapePress" verticalScrolling="false" 
		contentWidth="60%" 
		class="sapUiTinyMarginBegin sapUiTinyMarginEnd">
		<customHeader>
			<Toolbar> 
				<Title text="Select Transport and Package" level="H1"/>
				<ToolbarSpacer />
				<CheckBox text="Exclude Local Transports" selected="{transportPackageModel>/excludeLocalTransports}" select="onChangeExcludeLocalTransports"/>
			</Toolbar>
		</customHeader>

		<l:Splitter height="100%" >

			<!-- Customzing Transport List Display -->
			<Panel height="50vh" class="sapUiNoContentPadding" visible="{transportPackageModel>/customizing/show}">
				<layoutData>
					<l:SplitterLayoutData resizable="false"
						size="{ 	parts: [
                	          	'transportPackageModel>/customizing/show',
								'transportPackageModel>/workbench/show',
								'transportPackageModel>/package/show'],
                        	formatter: '.getSplitterCustomizingWidth' }" />
				</layoutData>
				<List id="custTransportRequestList" 
					items="{ path: 'transportPackageModel>/customizing/list', templateShareable:true, length: 2000 }" updateFinished="onListUpdateFinished"
					busy="{transportPackageModel>/customizing/busy}" busyIndicatorDelay="0"
					sticky="InfoToolbar,HeaderToolbar"
					selectionChange=".onTransportSelectionChange"
					rememberSelections="false"
					noDataText="No Transports available for user. Click on + to add a new transport."
					mode="SingleSelectMaster" includeItemInSelection="true">
					<headerToolbar> 
						<Toolbar>
							<Title text="Customizing Transports" level="H1"/>
						</Toolbar>
					</headerToolbar>

					<infoToolbar>
						<Toolbar height="6em">
							<VBox >
								<HBox alignItems="Center" height="2.5em">
									<Label text="User" labelFor="inputCustomizingTrUser" showColon="true" class="sapUiTinyMarginEnd"/>
									<Input id="inputCustomizingTrUser" name="CustomizingUser"
									showSuggestion="true" startSuggestion="1" filterSuggests="true" 
									change="onChangeUser" suggestionItems="{path: 'transportPackageModel>/userList', templateShareable:false, length: 5000}"
									valueState="{transportPackageModel>/customizing/userValueState/state}"
									valueStateText="{transportPackageModel>/customizing/userValueState/text}"
									valueLiveUpdate="true"
									value="{transportPackageModel>/customizing/user}" placeholder="Name ...">
										<suggestionItems>
											<c:Item 
												key="{transportPackageModel>Bname}"
												text="{transportPackageModel>Bname}"/>
										</suggestionItems>
									</Input>
								</HBox>
								<HBox >
									<Button width="2em"
										visible="{transportPackageModel>/ShowCreateTransportOption}"
										iconFirst="true" icon="sap-icon://add" tooltip="Create New Transport Request" 
										press=".onCreateNewTransport" id="idCreateTransportCustomizing"/>
									<SearchField id="searchCustTransport"  
										placeholder="Search Transport" 
										liveChange=".onSearchTransportList" 
										value="{transportPackageModel>/customizing/searchText}"/>
								</HBox>
							</VBox>
						</Toolbar>
					</infoToolbar>
					<ObjectListItem
						title="{transportPackageModel>Strkorr}" number="{transportPackageModel>Tarsystem}">
						<ObjectAttribute text="{transportPackageModel>As4text}" />
					</ObjectListItem>
				</List>
			</Panel>

			<!-- Workbench Transport List Display -->
			<Panel height="50vh" class="sapUiNoContentPadding" visible="{transportPackageModel>/workbench/show}">
				<layoutData>
					<l:SplitterLayoutData resizable="false"
						size="{ 	parts: [
                	          	'transportPackageModel>/customizing/show',
								'transportPackageModel>/workbench/show',
								'transportPackageModel>/package/show'],
                        	formatter: '.getSplitterWorkbenchWidth' }" />
				</layoutData>
				<List id="workbenchTransportRequestList" 
					items="{ path: 'transportPackageModel>/workbench/list', templateShareable:true, length: 2000 }" updateFinished="onListUpdateFinished"
					busy="{transportPackageModel>/workbench/busy}" busyIndicatorDelay="0"
					sticky="InfoToolbar,HeaderToolbar"
					selectionChange=".onTransportSelectionChange"
					rememberSelections="false"
					noDataText="No Transports available for user. Click on + to add a new transport."
					mode="SingleSelectMaster" includeItemInSelection="true">
					<headerToolbar> 
						<Toolbar>
							<Title text="Workbench Transports" level="H1"/>
						</Toolbar>
					</headerToolbar>
					<infoToolbar>
						<Toolbar height="6em">
							<VBox>
								<HBox alignItems="Center">
									<Label text="User" labelFor="inputWorkbenchTrUser" showColon="true" class="sapUiTinyMarginEnd"/>
									<Input id="inputWorkbenchTrUser" name="WorkbenchUser"
									showSuggestion="true" startSuggestion="1" filterSuggests="true"
									suggestionItems="{path: 'transportPackageModel>/userList', templateShareable:false, length: 5000}"
									change="onChangeUser" 
									valueState="{transportPackageModel>/workbench/userValueState/state}"
									valueStateText="{transportPackageModel>/workbench/userValueState/text}"
									valueLiveUpdate="true"
									value="{transportPackageModel>/workbench/user}" placeholder="Name ...">
										<suggestionItems>
											<c:Item 
												key="{transportPackageModel>Bname}"
												text="{transportPackageModel>Bname}"/>
										</suggestionItems>
									</Input>
								</HBox>
								<HBox>
									<Button width="2em"
										visible="{transportPackageModel>/ShowCreateTransportOption}"
										iconFirst="true" icon="sap-icon://add" tooltip="Create New Transport Request" 
										press=".onCreateNewTransport" id="idCreateTransportWorkbench"/>
									<SearchField id="searchWorkbenchTransport"  
										placeholder="Search Transport" 
										liveChange=".onSearchTransportList" 
										value="{transportPackageModel>/workbench/searchText}"/>
								</HBox>
							</VBox >
						</Toolbar>
					</infoToolbar>		
					<ObjectListItem
						title="{transportPackageModel>Strkorr}" number="{transportPackageModel>Tarsystem}">
						<ObjectAttribute text="{transportPackageModel>As4text}" />
					</ObjectListItem>
				</List>
			</Panel>

			<!-- Package List Display -->
			<Panel height="50vh" class="sapUiNoContentPadding" visible="{transportPackageModel>/package/show}">
				<layoutData>
					<l:SplitterLayoutData resizable="false"
						size="{ 	parts: [
                	          	'transportPackageModel>/customizing/show',
								'transportPackageModel>/workbench/show',
								'transportPackageModel>/package/show'],
                        	formatter: '.getSplitterPackageWidth' }" />
				</layoutData>
				<List id="packageList"
					items="{ path: 'transportPackageModel>/package/list', sorter: { path: 'DevClass' }, templateShareable:true, length: 2000 }" updateFinished="onListUpdateFinished"
					busy="{transportPackageModel>/package/busy}" busyIndicatorDelay="0"
					sticky="InfoToolbar,HeaderToolbar"
					selectionChange=".onPackageSelectionChange"
					rememberSelections="false"
					mode="SingleSelectMaster" includeItemInSelection="true">
					<headerToolbar> 
						<Toolbar>
							<Title text="Packages" level="H1"/>
						</Toolbar>
					</headerToolbar>
					<infoToolbar>
						<Toolbar height="6em">
							<VBox fitContainer="true" justifyContent="End">
								<SearchField id="selPackage" liveChange=".onSearchPackageList" placeholder="Search Package" ariaDescribedBy="Search Package"
									value="{transportPackageModel>/package/searchText}"
									ariaLabelledBy="selPackage"/>
							</VBox>
						</Toolbar>
					</infoToolbar>	
					<ObjectListItem
						title="{transportPackageModel>Devclass}">
						<ObjectAttribute text="{transportPackageModel>Ctext}" />
					</ObjectListItem>
				</List>
			</Panel >
		</l:Splitter>
		<beginButton>
			<Button text="Cancel" press=".onCloseTransportSelectDialog" id="cancel" ariaLabelledBy="cancel" />
		</beginButton>
		<endButton>
			<Button text="Confirm" press=".onTransportSelectionConfirm"	id="save" type="Emphasized" ariaLabelledBy="save" />
		</endButton>
	</Dialog>
</c:FragmentDefinition>