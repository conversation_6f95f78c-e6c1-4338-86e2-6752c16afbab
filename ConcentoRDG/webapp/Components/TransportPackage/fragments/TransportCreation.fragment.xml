<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="dmr.components"
	displayBlock="true">
	<Popover title="Create Transport" contentMinWidth="20em" class="sapUiContentPadding" modal="true">
		<VBox  width="100%">
			<VBox class="sapUiSmallMarginBottom">
				<Label text="Transport Type" labelFor="idTransportTypeSelect"/>
				<Select 
					id="idTransportTypeSelect" 
					forceSelection="false" 
					autoAdjustWidth="false"
					width="100%"
					enabled="false"
					selectedKey="{transportPackageModel>/CreateTransport/type}">
					<items>
						<c:Item key="W" text="W - Customizing Transport"/>
						<c:Item key="K" text="K - Workbench"/>
					</items>
				</Select>
			</VBox>

			<VBox >
				<Label text="Transport Description" labelFor="idTransportTypeDescription" required="true"/>
				<Input 
					id="idTransportTypeDescription" valueLiveUpdate="true"
					value="{transportPackageModel>/CreateTransport/description}">
				</Input>
			</VBox>
		</VBox>
		<footer> 
			<HBox justifyContent="End" class="sapUiTinyMarginEnd sapUiTinyMarginBottom">
				<Button text="Cancel" press=".onPressCancelCreateTransport" class="sapUiSmallMarginEnd" id="cancelCreate"/>
				<Button text="Create" press=".onPressCreateTransport" id="saveCreate" 
					enabled="{= ${transportPackageModel>/CreateTransport/description}.length > 0}"/>
			</HBox>
		</footer>
	</Popover>
</c:FragmentDefinition>