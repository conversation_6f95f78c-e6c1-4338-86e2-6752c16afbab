{"_version": "1.9.0", "sap.app": {"id": "dmr.components.TransportPackage", "type": "component", "i18n": "i18n/i18n.properties", "title": "Transport and Package Selection", "description": "Transport and Package Selection Component.", "applicationVersion": {"version": "1.0.0"}, "dataSources": {"TRANSPORT_SERVICE": {"uri": "/sap/opu/odata/RDG/GW_ZEUS_TR_SEARCH_SRV/", "type": "OData", "settings": {"localUri": "localService/YGW_ZEUS_TR_SEARCH_SRV_02/metadata.xml"}}, "TRANSPORT_CREATE": {"uri": "/sap/opu/odata/RDG/GW_ZEUS_CREATE_TR_SRV/", "type": "OData", "settings": {"localUri": "localService/YGW_ZEUS_CREATE_TR_SRV_01/metadata.xml"}}, "USER_SEARCH": {"uri": "/sap/opu/odata/RDG/GW_ZEUS_USER_SRCH_HELP_SRV/", "type": "OData", "settings": {"localUri": "localService/YGW_ZEUS_USER_SRCH_HELP_SRV/metadata.xml"}}, "YGW_ZEUS_PACKAGE_SEARCH": {"uri": "/sap/opu/odata/RDG/GW_ZEUS_PACKAGE_SEARCH_SRV/", "type": "OData", "settings": {"localUri": "localService/YGW_ZEUS_PACKAGE_SEARCH_SRV_01/metadata.xml"}}}}, "sap.ui": {"technology": "UI5", "icons": {"icon": "", "favIcon": "", "phone": "", "phone@2": "", "tablet": "", "tablet@2": ""}, "deviceTypes": {"desktop": true, "tablet": true, "phone": true}}, "sap.ui5": {"componentName": "dmr.components.TransportPackage", "dependencies": {"minUI5Version": "1.74.0"}, "contentDensities": {"compact": true, "cozy": false}, "models": {"transportList": {"type": "sap.ui.model.odata.v2.ODataModel", "settings": {"defaultOperationMode": "Server", "defaultBindingMode": "OneWay", "defaultCountMode": "Request"}, "dataSource": "TRANSPORT_SERVICE", "preload": true}, "transportCreate": {"type": "sap.ui.model.odata.v2.ODataModel", "settings": {"defaultOperationMode": "Server", "defaultBindingMode": "OneWay", "defaultCountMode": "Request"}, "dataSource": "TRANSPORT_CREATE", "preload": true}, "userSearch": {"type": "sap.ui.model.odata.v2.ODataModel", "settings": {"defaultOperationMode": "Server", "defaultBindingMode": "OneWay", "defaultCountMode": "Request"}, "dataSource": "USER_SEARCH", "preload": false}, "GET_PACKAGE_LIST": {"type": "sap.ui.model.odata.v2.ODataModel", "settings": {"defaultOperationMode": "Server", "defaultBindingMode": "OneWay", "defaultCountMode": "Request"}, "dataSource": "YGW_ZEUS_PACKAGE_SEARCH", "preload": false}}}}