sap.ui.define([
	"sap/ui/core/UIComponent",
	"sap/ui/model/json/JSONModel",
	"sap/suite/ui/generic/template/extensionAPI/ReuseComponentSupport",
	"dmr/mdg/supernova/SupernovaFJ/controller/Workflow/DMRNode"
], function(UIComponent, JSONModel, ReuseComponentSupport, DMRNode){
	"use strict";
	let Component = UIComponent.extend("dmr.components.TemplateSelect.Component", {
		metadata: {
			manifest: "json",
			// "abstract": false, 
			properties: {
				dataModel: {type: "string", defaultValue: ""},
				// Selected template whose details will be shown as default
				selectedTemplate: {type: "string", defaultValue: ""},
				sourceContext: {type: "string", defaultValue: null}
			},
			aggregations: {},
			events: {
				// Return the selected value through the event
				selectionChange: {
					parameters: {
						selectedItem: {type: "object"}
					}
				}
			}
		}
	});

	/**
	 * Just create the base information and the model
	 */
	Component.prototype.init = function(){
		// Call the parent init
		UIComponent.prototype.init.apply(this, arguments);
		
	    //Transform this component into a reuse component for Fiori Elements:
	    ReuseComponentSupport.mixInto(this);
	    // this.setModel(new JSONModel(),"templateModel");
	    
	    this.attachModelContextChange(undefined, this.modelContextChange, this);
	};
	
	Component.prototype.setDataModel = function(sDataModel){
		this.setProperty("dataModel", sDataModel);
		this._fillTemplateList();
	};
	
	/**
	 * Set the items of combobox
	 */
	Component.prototype._fillTemplateList = function() {
		let thisComponent = this;
		let oUrlParameters = {
			"$filter": "DATAMODEL eq '" + thisComponent.getDataModel() + "'",
			"$format": "json"
		};
		
		let oDataModel = this.getModel("emailTemplateList");
		oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
		
		let oGetParameterObject = {
			method: "GET",
			success: function (oData) {
				// Update the model length to ensure all items are displayed.
				thisComponent._oComboBox.getModel().setSizeLimit(oData.results.length);

				// Update the list 
				thisComponent.getModel("templateModel").setProperty("/results", oData.results);
			},
			error: function () {

			},
			urlParameters: oUrlParameters
		};
		
		oDataModel.read("/EMAILTEMPLISTSet", oGetParameterObject);
	};
	
	/**
	 * Navigate to Email Templates Screen on click of button
	 */
	Component.prototype.onClickManageTemplates = function() {
		let selectedTemplate = this._oComboBox.getSelectedKey() ? this._oComboBox.getSelectedKey() : "None";
		let url = window.location.href.split("#")[0]+ "#/EmailTemplates/*" + selectedTemplate;
		window.open(url);
	};

	/**
	 * Context Changed Event
	 * Fired when models or contexts are changed on this object 
	 *		(either by calling setModel/setBindingContext or due to propagation)
	 *	Rebind Properties in case of changees
	 *  Email Template and Manage Email Template to be disabled when target node selected is merge node and node type is parallel or parallel child
	 */					
	Component.prototype.modelContextChange = function(oEvent){
		let sPath = oEvent.getSource().getBindingContext().getPath();
		this._oComboBox.setModel(this.getBindingContext().getModel(), "parentModel");
		this._oComboBox.bindProperty("selectedKey", {path: "parentModel>" + sPath + "/Emailtemp"});
		
		this._oComboBox.bindProperty("editable", {
			parts: [{path: "parentModel>" + sPath + "/TargetNode"},
					{path: "parentModel>/ConnectionEditor/nodeTypeEdited"},
					{path: "parentModel>/ConnectionEditor/mergeNodeForParallel"}],
			formatter: (sTargetNode, iNodeType, sMergeNode) => {
				return ( sTargetNode === sMergeNode && ( iNodeType === DMRNode.Types.Parallel || iNodeType === DMRNode.Types.ParallelChild ) ) ? false : true;
			}
		});
		this._oButton.setModel(this.getBindingContext().getModel(), "parentModel");
		this._oButton.bindProperty("enabled", {
			parts: [{path: "parentModel>" + sPath + "/TargetNode"},
					{path: "parentModel>/ConnectionEditor/nodeTypeEdited"},
					{path: "parentModel>/ConnectionEditor/mergeNodeForParallel"}],
			formatter: (sTargetNode, iNodeType, sMergeNode) => {
				return ( sTargetNode === sMergeNode && ( iNodeType === DMRNode.Types.Parallel || iNodeType === DMRNode.Types.ParallelChild ) ) ? false : true;
			}
		});
	};

	/**
	 * Create the content. 
	 */
	Component.prototype.createContent = function() {
		this.setModel(new JSONModel(), "templateModel");
		let oHBox = new sap.m.HBox();
		this._oComboBox = new sap.m.ComboBox({
			// editable: true,
			id: this.createId("email-template-list"),
			width: "100%",
			visible: true,
			tooltip: "Email Template",
			selectionChange: this.onEmailTemplateSelected.bind(this)
		});
		this._oComboBox.setModel(this.getModel("templateModel"));
		this._oComboBox.bindAggregation("items", {
			path: "/results",
			templateShareable: true,
			template: new sap.ui.core.Item({
				key: "{TEMPLATE}",
				text: "{TEMPLATE} " + "- {DESCRIPTION}"})
		});
		this._oComboBox.attachSelectionChange(this, this.onEmailTemplateSelected, this);
		this._oButton = new sap.m.Button({
				text: "",
				id: this.createId("manage-email-template"),
				icon: "sap-icon://inspect",
				tooltip: "Manage Email Templates"
			}).attachPress(this, this.onClickManageTemplates, this);
		oHBox.addItem(this._oComboBox);
		oHBox.addItem(this._oButton);
		return oHBox;
	};
	
	 Component.prototype.onEmailTemplateSelected = function(oEvent){
		// Read the selected item name from the model 
		let oSelectedTemplate = oEvent.getSource().getSelectedItem();
		// Fire the event to the calling component
		this.fireSelectionChange({
			selectedItem: oSelectedTemplate
		});
	 };

	return Component;
});