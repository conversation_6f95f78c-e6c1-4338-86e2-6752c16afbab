{"_version": "1.9.0", "sap.app": {"id": "dmr.components.TemplateSelect", "type": "component", "i18n": "i18n/i18n.properties", "title": "{{title}}", "description": "{{description}}", "applicationVersion": {"version": "1.0.0"}, "dataSources": {"EMAILTEMPLATE_SERVICE": {"uri": "/sap/opu/odata/RDG/GW_ZEUS_EMAIL_TEMPLATE_SRV/", "type": "OData", "settings": {"localUri": "localService/YGW_ZEUS_EMAIL_TEMPLATE_SRV_01/metadata.xml"}}}}, "sap.ui": {"technology": "UI5", "icons": {"icon": "", "favIcon": "", "phone": "", "phone@2": "", "tablet": "", "tablet@2": ""}, "deviceTypes": {"desktop": true, "tablet": true, "phone": true}}, "sap.ui5": {"componentName": "dmr.components.TemplateSelect", "dependencies": {"minUI5Version": "1.74.0"}, "contentDensities": {"compact": true, "cozy": false}, "models": {"emailTemplateList": {"type": "sap.ui.model.odata.v2.ODataModel", "settings": {"defaultOperationMode": "Server", "defaultBindingMode": "OneWay", "defaultCountMode": "Request"}, "dataSource": "EMAILTEMPLATE_SERVICE", "preload": true}}}}