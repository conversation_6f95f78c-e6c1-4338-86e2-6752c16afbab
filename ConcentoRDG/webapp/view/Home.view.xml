<mvc:View
    controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Home"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns:app="sap.ui.core.CustomData"
    xmlns:core="sap.ui.core"
    displayBlock="true"
    xmlns="sap.m"
>
    <App id="app">
        <pages>
            <Page id="dashboard" showFooter="true" showHeader="true" showSubHeader="false" class="sapUiContentPadding dmrBackGroundImage" >
                <headerContent>
                    <mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.shared.MainMenu" />
                </headerContent>
                <VBox justifyContent="Center" alignItems="Center" alignContent="Center" width="100%" fitContainer="true">

                <HBox wrap="Wrap" width="100%"  alignItems="Center" alignContent="Center" fitContainer="true"
                    justifyContent="Center" items="{ path: 'mainMenuModel>/items', templateShareable: true}">
                    <items>
					    <VBox  class="sapUiContentPadding">
							<Title width="100%" titleStyle="H6" text="{mainMenuModel>categoryName}"/>
                            <FlexBox wrap="Wrap" alignItems="Center" alignContent="Center" fitContainer="true" justifyContent="Start"
                                items="{ path: 'mainMenuModel>items', templateShareable: true}" >
                                <items>
                                    <GenericTile class="sapUiTinyMarginBegin sapUiTinyMarginTop" ariaRole="link" 
										header="{mainMenuModel>title}" subheader="{= ${mainMenuModel>beta} === true? '[Beta]': ''}"
                                        press="onMenuItemPressed"
                                        app:mydata="mainMenuModel>target"
                                        visible="{= ${mainMenuModel>enabled} &amp;&amp; ${mainMenuModel>hideOnHomePage} !== true }"
                                    >
                                        <TileContent>
                                            <VBox >
                                                <core:Icon src="sap-icon://{mainMenuModel>icon}" size="2.5em" class="sapUiTinyMarginBottom"/>
                                                <Text class="sapMTileCntFooterTextColorNeutral" text="{mainMenuModel>description}" wrapping="true"/>
                                            </VBox>
                                        </TileContent>
                                    </GenericTile>
                                </items>
                            </FlexBox>
						</VBox>
                    </items>
                </HBox>
                </VBox>
               <footer>
                <Toolbar design="Transparent">
                 <!-- Bug 13207 - Changed InfoLabel to ObjectStatus -->
                <ObjectStatus text="{= ${i18n>systemVersion} + ${mainMenuModel>/appVersion}}" id="txtVersion" inverted="true" state="Indication05"/>

                </Toolbar>
               </footer>
            </Page>
        </pages>
    </App>
</mvc:View>
