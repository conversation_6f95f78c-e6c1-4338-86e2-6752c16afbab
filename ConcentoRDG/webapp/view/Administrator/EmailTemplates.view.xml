<mvc:View xmlns:core="sap.ui.core" xmlns:mvc="sap.ui.core.mvc" xmlns="sap.m" xmlns:l="sap.ui.layout" xmlns:f="sap.ui.layout.form"
	xmlns:n="sap.suite.ui.commons.networkgraph" xmlns:layout="sap.suite.ui.commons.networkgraph.layout"
	xmlns:rte="sap.ui.richtexteditor"
	xmlns:html="http://www.w3.org/1999/xhtml" xmlns:r="sap.ui.richtexteditor"
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Administrator.EmailTemplates">
	<Page title="Manage Email Templates"  titleLevel="H1" enableScrolling="false" showNavButton="true" navButtonPress="onBackPress" class="sapUiContentPadding">
		<headerContent>
			<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.shared.MainMenu"/>
		</headerContent>
		<content>
            <core:ComponentContainer id="idTransportPackageSelection" name="dmr.components.TransportPackage" async="true"
                componentCreated="onTransportPackageDialogCreated" />
			<l:Splitter id="idSplitter1" class="sapUiNoContentPadding">
				<VBox id="layout0" fitContainer="true">
					<layoutData>
						<l:SplitterLayoutData size="20%" resizable="false"/>
					</layoutData>
					<core:ComponentContainer id="emailTeplatesSearchList" name="dmr.components.SearchList" async="false"
						componentCreated="onEmailTemplatesSearchListCreated"/>
				</VBox>
				<l:Splitter id="idSplitter2" orientation="Vertical" class="sapUiNoContentPadding">
					<VBox id="layout1" class="sapUiNoContentPadding sapUiTinyMarginBottom">
						<layoutData>
							<l:SplitterLayoutData resizable="false"/>
						</layoutData>
						<Toolbar class="sapUiTinyMarginBottom">
							<Title id="TitleTemplate" text="Template Details" class="sapUiTinyMarginBegin"/>
							<ToolbarSpacer/>
							<Button text="Save" id="save" iconFirst="true" icon="sap-icon://save" press="onSaveTemplate" enabled="{formatter: '.createNewEmailTemplateEnabled', 
								parts: ['ViewEmailTemplate>/templateDetails/DATAMODEL', 'ViewEmailTemplate>/templateDetails/TEMPLATE', 'ViewEmailTemplate>/templateDetails/SUBJECT']}"></Button>
							<Button text="Delete" id="delete" iconFirst="true" icon="sap-icon://delete" press="onDeleteTemplate" 
								visible="{= ${ViewEmailTemplate>/templateType} === 'existing' &amp;&amp; !${ViewEmailTemplate>/workflowTemplate}}"></Button>
						</Toolbar>
						<HBox class="sapUiSmallMarginBegin sapUiSmallMarginEnd sapUiTinyMarginTop sapUiTinyMarginBottom">
							<VBox class="sapUiSmallMarginEnd">
								<Label id="dataModel" text="Data Model"/>
								<ComboBox id="idEmailTemplateComboBox" items="{path:'ViewEmailTemplate>/dataModelsList', templateShareable:false}"
									selectedKey="{ViewEmailTemplate>/templateDetails/DATAMODEL}" selectionChange=".onSelectDataModel"
									valueStateText="Datamodel must not be empty" enabled="{=${ViewEmailTemplate>/templateType} === 'new' ? true : false}"
									valueState="{= !${ViewEmailTemplate>/templateDetails/DATAMODEL} ? 'Error' : 'None'}" ariaLabelledBy="dataModel">
									<core:ListItem key="{ViewEmailTemplate>Model}" text="{ViewEmailTemplate>Model}-{ViewEmailTemplate>Desc}"/>
								</ComboBox>
							</VBox>
							<VBox class="sapUiSmallMarginEnd">
								<Label id="templateName" text="Template"/>
								<Input value="{ViewEmailTemplate>/templateDetails/TEMPLATE}" id="idTemplate" enabled="{=${ViewEmailTemplate>/templateType} !== undefined ? true : false}"
								valueStateText="Email Template name must not be empty" liveChange="onLiveChangeTemplate"
								valueState="{= !${ViewEmailTemplate>/templateDetails/TEMPLATE} ? 'Error' : 'None'}" ariaLabelledBy="templateName"/>
							</VBox>
							<VBox class="sapUiSmallMarginEnd">
								<Label id="templateDescription" text="Description"/>
								<Input value="{ViewEmailTemplate>/templateDetails/DESCRIPTION}" id="idTemplateDescription"
								enabled="{=${ViewEmailTemplate>/templateType} !== undefined ? true : false}" width="100%" ariaLabelledBy="templateDescription"/>
							</VBox>
						</HBox>
						<VBox class="sapUiSmallMarginBegin sapUiSmallMarginEnd sapUiTinyMarginTop sapUiTinyMarginBottom">
							<Label id="templateSubject" text="Subject"/>
							<!-- Task 12689: Change request process documents ( Email template ) (UI) -->
							<Input value="{ViewEmailTemplate>/templateDetails/SUBJECT}" id="idSubject" liveChange="onLiveChangeSubject"
							valueStateText="Subject must not be empty" enabled="{=${ViewEmailTemplate>/templateType} !== undefined ? true : false}"
							valueState="Error" width="100%" ariaLabelledBy="templateSubject"/>
							<!-- END Task 12689: Change request process documents ( Email template ) (UI) -->
						</VBox>
					</VBox>
					<VBox id="idVerticalLayout" fitContainer="true" class="sapUiContentPadding" width="100%">
						<layoutData>
							<l:SplitterLayoutData size="72%" resizable="false"/>
						</layoutData>
						<rte:RichTextEditor 
							id="idRichTextEditor"
							width="100%" height="100%"
							value="{ViewEmailTemplate>/templateDetails/BODY_TEXT}" 
							ready=".onRichTextEditorReady"
							beforeEditorInit=".onBeforeEditorInit"
						/>
					</VBox>

				</l:Splitter>
				<VBox id="layout2" class="sapUiNoContentPadding"  fitContainer="true">
					<layoutData>
						<l:SplitterLayoutData size="15%" resizable="false"/>
					</layoutData>
					<l:Splitter orientation="Vertical" height="87vh" class="sapUiNoContentPadding">
						<VBox class="sapUiSmallMarginEnd sapUiTinyMarginBottom">
							<layoutData>
								<l:SplitterLayoutData size="35%" resizable="false"/>
							</layoutData>
							<Toolbar class="sapUiTinyMarginBottom transparentBar">
								<Title level="H2" text="Select Attribute" class="sapUiTinyMarginBegin"/>
								<ToolbarSpacer/>
								<Button id="btnInsert" icon="" text="Insert" press="onInsertAttribute" enabled="false"/>
							</Toolbar>
							<!-- Task 12689: Change request process documents ( Email template ) (UI) -->
							<VBox class="sapUiSmallMarginBegin sapUiSmallMarginEnd sapUiTinyMarginTop sapUiTinyMarginBottom">
								<HBox alignItems="Center">
									<Title level="H2" text="Attribute Value" />
									<Switch id="placeholdersModeSwitch" state="true" customTextOn="New" customTextOff="Old" change=".onPlaceholdersModeSwitchChange"/>
								</HBox>
							</VBox>
							<VBox class="sapUiSmallMarginBegin sapUiSmallMarginEnd sapUiTinyMarginTop sapUiTinyMarginBottom">
								<Label text="Entity" labelFor="idCBoxEntitiesEmail"/>
								<ComboBox id="idCBoxEntitiesEmail" width="100%" placeholder="Select Entity" selectedKey="{ViewEmailTemplate>/selectedEntity}"
									items="{ path: 'ViewEmailTemplate>/entityList', sorter: { path: 'UsmdEntity' } }" change=".onEntitySelectionChangeHandler"
									enabled="{= ${ViewEmailTemplate>/templateDetails/DATAMODEL} ? true : false}">
									<core:ListItem key="{ViewEmailTemplate>UsmdEntity}"
										text="{= ${ViewEmailTemplate>UsmdEntity}.concat(${ViewEmailTemplate>Txtlg}.length > 0 ? ' - '.concat(${ViewEmailTemplate>Txtlg}) : '')}"/>
								</ComboBox>
							</VBox>
							<VBox class="sapUiSmallMarginBegin sapUiSmallMarginEnd sapUiTinyMarginTop sapUiTinyMarginBottom">
								<Label text="Attribute" labelFor="idCBoxAttributesEmail"/>
								<!-- Task 12689: Change request process documents ( Email template ) (UI) -->
								<ComboBox id="idCBoxAttributesEmail" width="100%" placeholder="Select Attribute" selectedKey="{ViewEmailTemplate>/selectedAttribute}"
									items="{ path: 'ViewEmailTemplate>/attributeList', length: 2000, sorter: { path: 'UsmdAttribute' } }"
									enabled="false" change=".onAttributeSelectionChangeHandler">
								<!-- END Task 12689: Change request process documents ( Email template ) (UI) -->
									<core:ListItem key="{ViewEmailTemplate>UsmdAttribute}"
										text="{= ${ViewEmailTemplate>UsmdAttribute}.concat(${ViewEmailTemplate>Txtlg}===null?'':' - '.concat(${ViewEmailTemplate>Txtlg}))}"
										additionalText="{ViewEmailTemplate>Txtlg}"/>
								</ComboBox>
							</VBox>
						</VBox>
						<!-- END Task 12689: Change request process documents ( Email template ) (UI) -->
						<VBox class="sapUiNoContentPadding">
							<layoutData>
								<l:SplitterLayoutData size="60%" resizable="false"/>
							</layoutData>
							<core:ComponentContainer id="PlaceholderSearchList" name="dmr.components.SearchList" async="false"
								componentCreated="onPlaceHolderSearchListCreated"/>
						</VBox>
					</l:Splitter>
				</VBox>
			</l:Splitter>
		</content>
	</Page>
</mvc:View>