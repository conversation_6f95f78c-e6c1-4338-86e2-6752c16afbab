<mvc:View
    controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Administrator.ProcessAnalytics.MiddleAnalyticalList"
    xmlns="sap.uxap" xmlns:m="sap.m" xmlns:mvc="sap.ui.core.mvc"  xmlns:c="sap.ui.core" 
    c:require="{backGroundJob: 'dmr/mdg/supernova/SupernovaFJ/model/BackGroundJob'}">
    <ObjectPageLayout id="MiddleObjectPageLayout" headerContentPinnable="false"
        showTitleInHeaderContent="true" isChildPage="true" useIconTabBar="true" upperCaseAnchorBar="false"
        sectionChange=".onSectionSelectionChange" >
        <headerTitle>
            <ObjectPageDynamicHeaderTitle>
                <heading> 
                    <m:VBox>
                        <m:Title text="{MiddleAnalyticalList>/detailView/Model} - {MiddleAnalyticalList>/detailView/Description}"
                            visible="{= !!${MiddleAnalyticalList>/detailView/Model} }"
                            wrapping="true" class="sapUiSmallMarginEnd" />
                        <m:ObjectStatus title="{i18n>analyticalViews.common.status.title}" text="{MiddleAnalyticalList>/detailView/Status}"
                            visible="{= !!${MiddleAnalyticalList>/detailView/Model} }"
                            state="{ path: 'MiddleAnalyticalList>/detailView/Status', formatter: 'backGroundJob.formatterViewState' }"/>
                    </m:VBox>
                </heading>
                <actions>
                    <m:Button tooltip="{i18n>analyticalViews.middle.generateModelView.toottip}" icon="sap-icon://activate" press="onGenerateModelView" />
                </actions>
            </ObjectPageDynamicHeaderTitle>
        </headerTitle>
        <headerContent>
            <m:FlexBox wrap="Wrap" fitContainer="true" >
                <m:ObjectAttribute title="{i18n>analyticalViews.middle.generatedBy.title}" text="{MiddleAnalyticalList>/detailView/GeneratedBy}" class="sapUiSmallMarginEnd" 
                visible="{= !!${MiddleAnalyticalList>/detailView/Model} }"/>
                <m:ObjectAttribute title="{i18n>analyticalViews.middle.generatedOn.title}" text="{MiddleAnalyticalList>/detailView/FinishedAt}" 
                visible="{= !!${MiddleAnalyticalList>/detailView/Model} }"/>
            </m:FlexBox>
        </headerContent>
        <sections>
            <ObjectPageSection title="{i18n>analyticalViews.common.process.title}" titleUppercase="false" id="processAnalyticalListView">
                <ObjectPageSubSection titleUppercase="false">
                    <m:Table id="processAnalyticalTable" width="auto" mode="SingleSelectMaster" growing="true"
                        items="{path: 'MiddleAnalyticalList>/processAnaViews', templateShareable:true}"
                        itemPress=".onAnalyticalViewSelectionChange"
                        noDataText="{i18n>analyticalViews.middle.viewList.noData}"
                        sticky="HeaderToolbar,InfoToolbar,ColumnHeaders"
                    >
                        <m:headerToolbar>
                            <m:OverflowToolbar class="transparentBar">
                                <m:ToolbarSpacer />
                                <m:Button id="btnAdd" tooltip="{i18n>analyticalViews.middle.addNewProcessView.tooltip}" icon="sap-icon://add" 
                                    press="onAddView" enabled="{= ${MiddleAnalyticalList>/detailView/Model} !== undefined}" />
                            </m:OverflowToolbar>
                        </m:headerToolbar>
                        <m:columns>
                            <m:Column>
                                <m:Text text="{i18n>analyticalViews.middle.table.viewName.column}" />
                            </m:Column>
                            <m:Column hAlign="End">
                                <m:Text text="{i18n>analyticalViews.common.status.title}" />
                            </m:Column>
                        </m:columns>
                        <m:items>
                            <m:ColumnListItem type="Navigation">
                                <m:cells>
                                    <m:ObjectIdentifier 
                                        text="{MiddleAnalyticalList>Description}"
                                        title="{MiddleAnalyticalList>ViewName}"
                                    />
                                    <m:ObjectStatus text="{MiddleAnalyticalList>Status}"
                                        state="{ path: 'MiddleAnalyticalList>Status', formatter: 'backGroundJob.formatterViewState' }"
                                    />
                                </m:cells>
                            </m:ColumnListItem>
                        </m:items>
                    </m:Table>
                </ObjectPageSubSection>
            </ObjectPageSection>
            <ObjectPageSection title="{i18n>analyticalViews.common.change.title}" titleUppercase="false" id="changeAnalyticalListView" >
                <ObjectPageSubSection titleUppercase="false">
                    <m:Table id="changeAnalyticalTable" width="auto" mode="SingleSelectMaster" growing="true"
                        items="{path: 'MiddleAnalyticalList>/changeAnaViews', templateShareable:true}"
                        itemPress=".onAnalyticalViewSelectionChange"
                        noDataText="{i18n>analyticalViews.middle.viewList.noData}"
                        sticky="HeaderToolbar,InfoToolbar,ColumnHeaders"
                    >
                        <m:headerToolbar>
                            <m:OverflowToolbar class="transparentBar">
                                <m:ToolbarSpacer />
                                <m:Button id="btnAddChange" tooltip="{i18n>analyticalViews.middle.addNewChangeView.tooltip}" icon="sap-icon://add" 
                                    press="onAddView" enabled="{= ${MiddleAnalyticalList>/detailView/Model} !== undefined}" />
                            </m:OverflowToolbar>
                        </m:headerToolbar>
                        <m:columns>
                            <m:Column>
                                <m:Text text="{i18n>analyticalViews.middle.table.viewName.column}"/>
                            </m:Column>
                            <m:Column hAlign="End">
                                <m:Text text="{i18n>analyticalViews.common.status.title}" />
                            </m:Column>
                        </m:columns>
                        <m:items>
                            <m:ColumnListItem type="Navigation">
                                <m:cells>
                                    <m:ObjectIdentifier text="{MiddleAnalyticalList>Description}" title="{MiddleAnalyticalList>ViewName}" />
                                    <m:ObjectStatus text="{MiddleAnalyticalList>Status}"
                                        state="{ path: 'MiddleAnalyticalList>Status', formatter: 'backGroundJob.formatterViewState' }"
                                    />
                                </m:cells>
                            </m:ColumnListItem>
                        </m:items>
                    </m:Table>
                </ObjectPageSubSection>
            </ObjectPageSection>
        </sections>
    </ObjectPageLayout>
</mvc:View>
