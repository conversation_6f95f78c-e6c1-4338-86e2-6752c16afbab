<mvc:View
    controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Administrator.ProcessAnalytics.EndAnalyticalDetail"
    xmlns="sap.uxap" xmlns:m="sap.m" xmlns:mvc="sap.ui.core.mvc" xmlns:c="sap.ui.core" xmlns:layout="sap.ui.layout" 
    c:require="{backGroundJob: 'dmr/mdg/supernova/SupernovaFJ/model/BackGroundJob'}">
    
    <ObjectPageLayout id="EndObjectPageLayout" headerContentPinnable="false" showTitleInHeaderContent="true" isChildPage="true" >
        <headerTitle>
            <ObjectPageDynamicHeaderTitle>
                <heading>
                    <m:VBox>
                        <m:Title text="{EndAnalyticalDetail>/detailView/ViewName}" wrapping="true"/>
                         <m:ObjectStatus title="{i18n>analyticalViews.common.status.title}" text="{EndAnalyticalDetail>/detailView/Status}"
                            visible="{= !!${EndAnalyticalDetail>/detailView/Status} }"
                            state="{ path: 'EndAnalyticalDetail>/detailView/Status', formatter: 'backGroundJob.formatterViewState' }"/>
                    </m:VBox>
                </heading>
                <actions>
                    <m:Button tooltip="{i18n>analyticalViews.end.editView.tooltip}"
                        text="{i18n>analyticalViews.end.editView.text}" 
                        icon="sap-icon://edit" press="onEditView"
                        visible="{= ${ parts: [
                                    'EndAnalyticalDetail>/isEditViewEnabled',
                                    'EndAnalyticalDetail>/detailView/isNewView'],
                                formatter: '.isEditModeFormatter' } === false }"
                        />

                    <m:Button tooltip="{i18n>analyticalViews.end.deleteView.tooltip}"  
                        text="{i18n>analyticalViews.end.deleteView.text}" 
                        icon="sap-icon://delete" press="onDeleteAnalyticalView"
                        visible="{= ${ parts: [
                                    'EndAnalyticalDetail>/isEditViewEnabled',
                                    'EndAnalyticalDetail>/detailView/isNewView'],
                                formatter: '.isEditModeFormatter' } === false }"
                        />

                    <m:Button tooltip="{i18n>analyticalViews.end.saveView.tooltip}"  
                        text="{i18n>analyticalViews.end.saveView.text}" 
                        icon="sap-icon://save" press="onSaveAnalyticalView"
                        visible="{= ${ parts: [
                                    'EndAnalyticalDetail>/isEditViewEnabled',
                                    'EndAnalyticalDetail>/detailView/isNewView'],
                                formatter: '.isEditModeFormatter' } === true }"
                    />
                    <m:Button tooltip="{i18n>analyticalViews.end.cancelEdit.tooltip}" 
                        text="{i18n>analyticalViews.end.cancelEdit.tooltip}" 
                        icon="sap-icon://decline" press=".onCancelEdit"
                        visible="{= ${ parts: [
                                    'EndAnalyticalDetail>/isEditViewEnabled',
                                    'EndAnalyticalDetail>/detailView/isNewView'],
                                formatter: '.isEditModeFormatter' } === true }"
                    />

                    <m:Button tooltip="{i18n>analyticalViews.end.generateView.tooltip}" 
                        text="{i18n>analyticalViews.end.generateView.text}" 
                        icon="sap-icon://activate" press="onGenerateAnalyticalView"
                        visible="{= ${ parts: [
                                    'EndAnalyticalDetail>/isEditViewEnabled',
                                    'EndAnalyticalDetail>/detailView/isNewView'],
                                formatter: '.isEditModeFormatter' } === false }"
                    />
                    <m:Button tooltip="{i18n>analyticalViews.end.publishfiorireport.tooltip}" 
                        text="{i18n>analyticalViews.end.publishfiorireport.text}" 
                        icon="sap-icon://manager-insight" press="onPublishFioriReport"
                        visible="{= ${ parts: [
                                    'EndAnalyticalDetail>/isEditViewEnabled',
                                    'EndAnalyticalDetail>/detailView/isNewView',
                                    'EndAnalyticalDetail>/detailView/FioriReportExists'],
                                formatter: '.isEditModeFormatter' } === false }"
                    />
                </actions>
            </ObjectPageDynamicHeaderTitle>
        </headerTitle>
        <headerContent>
            <c:ComponentContainer id="idTransportPackageSelection" name="dmr.components.TransportPackage" async="true"
                componentCreated="onTransportPackageDialogCreated" />
            <m:FlexBox wrap="Wrap" fitContainer="true" >
                <m:ObjectAttribute class="sapUiSmallMarginEnd" 
                    title="{i18n>analyticalViews.end.generatedBy.title}" 
                    text="{EndAnalyticalDetail>/detailView/GeneratedBy}"
                    visible="{= !!${EndAnalyticalDetail>/detailView/GeneratedBy} }" />
                <m:ObjectAttribute title="{i18n>analyticalViews.end.lastChangedOn.title}" text="{EndAnalyticalDetail>/detailView/FinishedAt}" 
                    visible="{= !!${EndAnalyticalDetail>/detailView/FinishedAt} }"/>
            </m:FlexBox>
        </headerContent>

        <ObjectPageSection title="{i18n>analyticalViews.end.section.viewDetails.title}" titleUppercase="false" id="ViewDetails" >
            <ObjectPageSubSection titleUppercase="false">
                <m:FlexBox wrap="Wrap" alignItems="Start" width="100%">
                    <!-- <layout:Grid containerQuery="true" defaultSpan="XL2 L4" > -->
                        <m:VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd" 
                            visible="{= ${EndAnalyticalDetail>/detailView/isNewView} === true }">
                            <m:layoutData>
						        <m:FlexItemData growFactor="2" />
					        </m:layoutData>
                            <m:Label 
                                text="{i18n>analyticalViews.end.section.viewDetails.name.title}" required="true" />
                            <m:Input maxLength="20" width="100%"
                                editable="{= ${EndAnalyticalDetail>/detailView/isNewView} === true }"
                                value="{EndAnalyticalDetail>/detailView/ViewName}"
                                submit="onSubmitViewName" 
                                liveChange="onLiveChangeViewName"
                                valueState="{EndAnalyticalDetail>/detailView/viewNameValueState}"
                                valueStateText="{EndAnalyticalDetail>/detailView/viewNameValueStateText}"
                            />
                        </m:VBox>
                        <m:VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
                            <m:layoutData>
						        <m:FlexItemData growFactor="3" />
					        </m:layoutData>
                            <m:Label 
                                text="{i18n>analyticalViews.end.section.viewDetails.description.title}" required="true" />
                            <m:Input maxLength="60" width="100%"
                                valueStateText="{i18n>analyticalViews.end.section.viewDetails.inputRequired.valueState}"
                                value="{EndAnalyticalDetail>/detailView/Description}"
                                valueState="{EndAnalyticalDetail>/detailView/viewDescriptionValueState}"
                                editable="{= ${ parts: [
                                            'EndAnalyticalDetail>/isEditViewEnabled',
                                            'EndAnalyticalDetail>/detailView/isNewView'],
                                        formatter: '.isEditModeFormatter' } === true }"
                            />
                        </m:VBox>
                        <m:VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
                            <m:layoutData>
						        <m:FlexItemData growFactor="2" />
					        </m:layoutData>
                            <m:Label text="{i18n>analyticalViews.end.section.viewDetails.mainEntity.title}" required="true" />
                            <m:ComboBox id="MainEntity" width="100%"
                                valueStateText="{i18n>analyticalViews.end.section.viewDetails.inputRequired.valueState}"
                                selectedKey="{EndAnalyticalDetail>/detailView/MainEntity}"
                                items="{path: 'EndAnalyticalDetail>/mainEntityList', templateShareable: false, length: 2000}"
                                selectionChange="onMainEntitySelectionChange"
                                valueState="{EndAnalyticalDetail>/detailView/viewMainEntityValueState}"
                                editable="{= ${ parts: [
                                            'EndAnalyticalDetail>/isEditViewEnabled',
                                            'EndAnalyticalDetail>/detailView/isNewView'],
                                        formatter: '.isEditModeFormatter' } === true }"
                            >
                                <c:ListItem
                                    text="{EndAnalyticalDetail>MainEntity} - {EndAnalyticalDetail>Description}"
                                    key="{EndAnalyticalDetail>MainEntity}"
                                />
                            </m:ComboBox>
                        </m:VBox>
                        <m:VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
                            <m:layoutData>
						        <m:FlexItemData growFactor="2" />
					        </m:layoutData>
                            <m:Label text="{i18n>analyticalViews.end.section.viewDetails.package.title}" required="true" />
                            <m:ComboBox id="Package" width="100%"
                                valueStateText="{i18n>analyticalViews.end.section.viewDetails.inputRequired.valueState}"
                                selectedKey="{EndAnalyticalDetail>/detailView/Package}"
                                items="{path: 'EndAnalyticalDetail>/packageList', templateShareable: false, length: 2000}"
                                valueState="{EndAnalyticalDetail>/detailView/viewPackageValueState}"
                                editable="{= ${ parts: [
                                            'EndAnalyticalDetail>/isEditViewEnabled',
                                            'EndAnalyticalDetail>/detailView/isNewView'],
                                        formatter: '.isEditModeFormatter' } === true }"
                            >
                                <c:ListItem
                                    text="{EndAnalyticalDetail>Package} - {EndAnalyticalDetail>Description}"
                                    key="{EndAnalyticalDetail>Package}"
                                />
                            </m:ComboBox>
                        </m:VBox>
                        <m:VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
                            <m:layoutData>
						        <m:FlexItemData growFactor="1" />
					        </m:layoutData>
                            <m:Label text="{i18n>analyticalViews.end.section.viewDetails.analysisTarget.title}" />
                            <m:ComboBox id="AnaTarget" width="100%"
                                selectedKey="{EndAnalyticalDetail>/detailView/AnaTarget}"
                                items="{path: 'EndAnalyticalDetail>/anaTargetList', templateShareable: false, length: 2000}"
                                editable="{= ${ parts: [
                                            'EndAnalyticalDetail>/isEditViewEnabled',
                                            'EndAnalyticalDetail>/detailView/isNewView'],
                                        formatter: '.isEditModeFormatter' } === true }"
                            >
                                <c:ListItem
                                    text="{EndAnalyticalDetail>TargetKey} - {EndAnalyticalDetail>TargetDescription}"
                                    key="{EndAnalyticalDetail>TargetKey}"
                                />
                            </m:ComboBox>
                        </m:VBox>
                        <m:VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd" visible="{= ${EndAnalyticalDetail>/Section} !== 'processAnalyticalListView' ? true: false}">
                            <m:layoutData>
						        <m:FlexItemData growFactor="2" />
					        </m:layoutData>
                            <m:Label text="{i18n>analyticalViews.end.section.viewDetails.logDomain.title}"/>
                            <m:ComboBox id="LogDomain" width="100%"
                                selectedKey="{EndAnalyticalDetail>/detailView/Domain}"
                                value="{EndAnalyticalDetail>/detailView/Domain}"
                                items="{path: 'EndAnalyticalDetail>/logDomain', templateShareable: false, length: 2000}"
                                editable="{= ${ parts: [
                                            'EndAnalyticalDetail>/isEditViewEnabled',
                                            'EndAnalyticalDetail>/detailView/isNewView'],
                                        formatter: '.isEditModeFormatter' } === true }"
                            >
                                <c:ListItem
                                    text="{EndAnalyticalDetail>Name} - {EndAnalyticalDetail>Description}"
                                    key="{EndAnalyticalDetail>Name}"
                                />
                            </m:ComboBox>
                        </m:VBox>
                        <m:VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd" visible="{= ${EndAnalyticalDetail>/Section} !== 'processAnalyticalListView' ? true: false}">
                            <m:layoutData>
						        <m:FlexItemData growFactor="2" />
					        </m:layoutData>
                            <m:Label text="{i18n>analyticalViews.end.section.viewDetails.noAuthCheck.title}" />
                            <m:CheckBox width="100%"
                                selected="{EndAnalyticalDetail>/detailView/AuthCheck}"
                                editable="{= ${ parts: [
                                            'EndAnalyticalDetail>/isEditViewEnabled',
                                            'EndAnalyticalDetail>/detailView/isNewView'],
                                        formatter: '.isEditModeFormatter' } === true }"
                            />
                        </m:VBox>
                    <!-- </layout:Grid> -->
                </m:FlexBox>
            </ObjectPageSubSection>
        </ObjectPageSection>

        <ObjectPageSection title="{i18n>analyticalViews.end.section.fieldSelection.title}" 
            titleUppercase="false" id="FieldSelectionID" >
            <ObjectPageSubSection titleUppercase="false">
                <m:Table sticky="HeaderToolbar,InfoToolbar,ColumnHeaders" id="idAnalyticalTable"
                    popinLayout="GridLarge"
                    delete=".onDeleteRow"
                    items="{path: 'EndAnalyticalDetail>/Fields' , templateShareable: false, length: 2000}"
                    growing="true"
                    mode="{= ${ parts: [
                                'EndAnalyticalDetail>/isEditViewEnabled',
                                'EndAnalyticalDetail>/detailView/isNewView'],
                            formatter: '.isEditModeFormatter' } ? 'Delete' : 'None' }"                                
                >
                    <m:headerToolbar>
                        <m:OverflowToolbar class="transparentBar">
                            <m:ToolbarSpacer />
                            <m:Button id="btnAddAttribute" 
                                tooltip="{i18n>analyticalViews.end.section.fieldSelection.addAttr.toottip}" 
                                icon="sap-icon://add" press="onAddNewAttributes"
                                enabled="{= ${EndAnalyticalDetail>/detailView/ViewName} !== undefined &amp;&amp; 
                                    ${ parts: [
                                                'EndAnalyticalDetail>/isEditViewEnabled',
                                                'EndAnalyticalDetail>/detailView/isNewView'],
                                            formatter: '.isEditModeFormatter' } === true }"
                            />
                        </m:OverflowToolbar>
                    </m:headerToolbar>
                    <m:columns>
                        <m:Column>
                            <m:Label text="{i18n>analyticalViews.end.section.fieldSelection.table.entity.column}" required="true" />
                        </m:Column>
                        <m:Column>
                            <m:Label text="{i18n>analyticalViews.end.section.fieldSelection.table.attribute.column}" required="true" />
                        </m:Column>
                        <m:Column>
                            <m:Label text="{i18n>analyticalViews.end.section.fieldSelection.table.logDomain.column}" />
                        </m:Column>
                        <m:Column visible="{= ${EndAnalyticalDetail>/Section} !== 'processAnalyticalListView' ? true: false}" >
                            <m:Label text="{i18n>analyticalViews.end.section.fieldSelection.table.drillDown.column}" />
                        </m:Column>
                    </m:columns>
                    <m:items>
                        <m:ColumnListItem>
                            <m:cells>
                                <!-- Bug 13427 - Live Search for the Attribute: Inconsistent behavior -->
                                <!-- Bug 13424 - Key-Description value inconsistent in analytic views -->
                                <m:Select id="Entity" change="onChangeEntity" width="100%"
                                    selectedKey="{EndAnalyticalDetail>Entity}" forceSelection="false"
                                    busy="{EndAnalyticalDetail>/EntityAttributeBusy}" busyIndicatorSize="Auto"
                                    items="{path: 'EndAnalyticalDetail>/entityList', templateShareable: false, length: 2000}"
                                    editable="{= ${ parts: [
                                                'EndAnalyticalDetail>/isEditViewEnabled',
                                                'EndAnalyticalDetail>/detailView/isNewView'],
                                            formatter: '.isEditModeFormatter' } === true }"
                                >
                                    <c:Item 
                                        text="{EndAnalyticalDetail>Entity} - {EndAnalyticalDetail>Description}"
                                        key="{EndAnalyticalDetail>Entity}"
                                    />
                                </m:Select>

                                <!-- Bug 13427 - Live Search for the Attribute: Inconsistent behavior -->
                                <!-- Bug 13424 - Key-Description value inconsistent in analytic views -->
                                <m:Select id="Attribute" width="100%"
                                    selectedKey="{EndAnalyticalDetail>Attribute}" forceSelection="false"
                                    busy="{EndAnalyticalDetail>/EntityAttributeBusy}" busyIndicatorSize="Auto"
                                    items="{path: 'EndAnalyticalDetail>arrAttributeList', templateShareable: false, length: 2000}"
                                    editable="{= ${ parts: [
                                                'EndAnalyticalDetail>/isEditViewEnabled',
                                                'EndAnalyticalDetail>/detailView/isNewView'],
                                            formatter: '.isEditModeFormatter' } === true }"
                                >
                                    <c:Item
                                        text="{EndAnalyticalDetail>Attribute} - {EndAnalyticalDetail>Description}"
                                        key="{EndAnalyticalDetail>Attribute}"
                                    />
                                </m:Select>

                                <!-- Bug 13427 - Live Search for the Attribute: Inconsistent behavior -->
                                <!-- Bug 13424 - Key-Description value inconsistent in analytic views -->
                                <m:Select id="logDomain" width="100%"
                                    selectedKey="{EndAnalyticalDetail>Domain}" forceSelection="false"
                                    items="{path: 'EndAnalyticalDetail>/logDomain', templateShareable: false, length: 2000}"
                                    editable="{= (${EndAnalyticalDetail>IsReport} === true || ${EndAnalyticalDetail>/Section} === 'processAnalyticalListView') 
                                        &amp;&amp; ${ parts: [
                                                'EndAnalyticalDetail>/isEditViewEnabled',
                                                'EndAnalyticalDetail>/detailView/isNewView'],
                                            formatter: '.isEditModeFormatter' } === true }"
                                >
                                    <c:Item
                                        text="{EndAnalyticalDetail>Name} - {EndAnalyticalDetail>Description}"
                                        key="{EndAnalyticalDetail>Name}"
                                    />
                                </m:Select>

                                <m:CheckBox id="isReportField" selected="{EndAnalyticalDetail>IsReport}"
                                    visible="{= ${EndAnalyticalDetail>/Section} !== 'processAnalyticalListView' ? true: false}"
                                    editable="{= ${ parts: [
                                                'EndAnalyticalDetail>/isEditViewEnabled',
                                                'EndAnalyticalDetail>/detailView/isNewView'],
                                            formatter: '.isEditModeFormatter' } === true }"                                                
                                />
                            </m:cells>
                        </m:ColumnListItem>
                    </m:items>
                </m:Table>
            </ObjectPageSubSection>
        </ObjectPageSection>
    </ObjectPageLayout>
</mvc:View>
