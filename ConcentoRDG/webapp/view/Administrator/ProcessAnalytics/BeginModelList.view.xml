<mvc:View displayBlock="true" height="100%" 
    controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Administrator.ProcessAnalytics.BeginModelList" xmlns="sap.m" xmlns:mvc="sap.ui.core.mvc" xmlns:c="sap.ui.core" 
    c:require="{backGroundJob: 'dmr/mdg/supernova/SupernovaFJ/model/BackGroundJob'}">

    <ScrollContainer height="100%" vertical="true" focusable="true">
        <List id="dataModelsList" selectionChange="onModelItemPress" mode="SingleSelectMaster" headerText="{i18n>analyticalViews.begin.list.title}"
            sticky="HeaderToolbar"
            items="{path: 'DataModel>/masterView', templateShareable:true}" >
            <items>
            <!--  -->
                <ObjectListItem type="Navigation" press=".onListItemPress"
                    title="{DataModel>Model} - {DataModel>Description}" >
                    <attributes>
                        <ObjectAttribute title="{i18n>analyticalViews.common.process.title}" text="{DataModel>ProcessAnalyticalView}"
                            visible="{= ${DataModel>ProcessAnalyticalView} !== 0 ? true : false}"
                        />
                        <ObjectAttribute title="{i18n>analyticalViews.common.change.title}" text="{DataModel>ChangeAnalyticalView}"
                            visible="{= ${DataModel>ChangeAnalyticalView} !== 0 ? true : false}"
                        />
                    </attributes>
                    <firstStatus>
                        <ObjectStatus
                            text="{DataModel>Status}"
                            state="{ path: 'DataModel>Status', formatter: 'backGroundJob.formatterViewState' }"
                        />
                    </firstStatus>
                </ObjectListItem>
            </items>
        </List>
    </ScrollContainer>
</mvc:View>
