<mvc:View
    displayBlock="true" controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Administrator.ProcessAnalytics.MainAnalytics"
    height="100%" xmlns:f="sap.f" xmlns:m="sap.m" xmlns:mvc="sap.ui.core.mvc" >
    <m:Page
        id="dynamicPageId" title="Manage Process Analytics" titleLevel="H1" showNavButton="true"
        navButtonPress="onBackPress" class="sapUiContentPadding" >
        <m:headerContent>
            <mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.shared.MainMenu" />
        </m:headerContent>
        <f:FlexibleColumnLayout id="flexibleColumnLayout" layout='{/viewLayout}' stateChange=".onStateChanged" >
            <f:beginColumnPages  >
                <mvc:XMLView id="beginView" viewName="dmr.mdg.supernova.SupernovaFJ.view.Administrator.ProcessAnalytics.BeginModelList" />
            </f:beginColumnPages>
            <f:midColumnPages>
                <mvc:XMLView id="detailView" viewName="dmr.mdg.supernova.SupernovaFJ.view.Administrator.ProcessAnalytics.MiddleAnalyticalList" />
            </f:midColumnPages>
            <f:endColumnPages>
                <mvc:XMLView id="detaildetailView" viewName="dmr.mdg.supernova.SupernovaFJ.view.Administrator.ProcessAnalytics.EndAnalyticalDetail" />
            </f:endColumnPages>
        </f:FlexibleColumnLayout>
    </m:Page>
</mvc:View>
