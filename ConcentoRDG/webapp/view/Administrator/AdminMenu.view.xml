<mvc:View controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Administrator.AdminMenu" xmlns:core="sap.ui.core" xmlns:mvc="sap.ui.core.mvc"
	xmlns:app="sap.ui.core.CustomData" displayBlock="true" xmlns="sap.m"
	xmlns:html="http://www.w3.org/1999/xhtml">
	<App id="app">
		
		<pages>

			<Page id="dashboard" showFooter="false" showHeader="true" showSubHeader="false" 
				showNavButton="true" navButtonPress="onBackPress" class="sapUiContentPadding dmrBackGroundImage">
				<headerContent>
					<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.shared.MainMenu"/>
				</headerContent>
				<FlexBox wrap="Wrap" width="100%" height="70%" class="sapUiSmallMargin" alignItems="Center" justifyContent="Center" items="{ path: 'adminMenuModel>/items'}">
					<items>
						<GenericTile class="sapUiTinyMargin dmrGenericTile" header="{adminMenuModel>title}" subheader="{= ${adminMenuModel>beta} === true? '[Beta]': ''}"
							press="onAdminMenuItemPressed" app:mydata="adminMenuModel>target" 
							visible="{= ${adminMenuModel>enabled}}">
							<TileContent footer="{adminMenuModel>title}">
								<ImageContent src="sap-icon://{adminMenuModel>icon}"/>
							</TileContent>
						</GenericTile>
					</items>
				</FlexBox>
			</Page>
		</pages>
	</App>
</mvc:View>