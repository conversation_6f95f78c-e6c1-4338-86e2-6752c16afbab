<c:FragmentDefinition
	xmlns:mvc="sap.ui.core.mvc"
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true"
>
	<Dialog title="Create Step Status">
		<l:VerticalLayout class="sapUiContentPadding" width="100%">
			<l:content>
				<VBox>
				<VBox>
					<Label required="true" text="Status"/>
					<HBox>
						<Input value="{approverModel>/StepStatusDetailsFragment/UsmdCreqStatus}"
							valueState="{approverModel>/StepStatusDetailsFragment/statusValueState}"
							valueStateText="{approverModel>/StepStatusDetailsFragment/statusValueStateText}"/>
						<Select 
							class="sapUiTinyMarginBegin"
							selectedKey= "{approverModel>/StepStatusDetailsFragment/UsmdCreqObmain}"
							forceSelection="false"
							items="{ path: 'approverModel>/cr_OBMAIN/results', templateShareable:true }">
							<c:Item
								key="{approverModel>UsmdCreqObmain}"
								text="{approverModel>UsmdCreqObmain} - {approverModel>UsmdCreqObmainTxt}"/>
						</Select>		
					</HBox>
				</VBox>
				<VBox>
					<Label text="Status Description"></Label>
					<Input value="{approverModel>/StepStatusDetailsFragment/Txtmi}"/>
				</VBox>
				</VBox>
			</l:content>
		</l:VerticalLayout>
		
		<beginButton>
			<Button 
				text="Create" 
				press=".NodeConnectionsTableDialog.stepStatusCreateClicked" id="BtnStatusCreate"/>
		</beginButton>
		<endButton>
			<Button
				text="Cancel"
				press=".NodeConnectionsTableDialog.stepStatusCancelClicked" id="BtnStatusCancel"/>
		</endButton>
	
	</Dialog>
</c:FragmentDefinition>