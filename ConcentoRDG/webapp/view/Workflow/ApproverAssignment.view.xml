<mvc:View controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Workflow.ApproverAssignment" xmlns:m="sap.m" xmlns:core="sap.ui.core"
	xmlns:n="sap.suite.ui.commons.networkgraph" xmlns:layout="sap.suite.ui.commons.networkgraph.layout" xmlns:mvc="sap.ui.core.mvc"
	xmlns:app="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1" width="100%">
	<n:Graph enableWheelZoom="false" orientation="LeftRight" nodes="{/nodes}" lines="{/lines}" groups="{/groups}"
		id="graph">
		<n:statuses>
			<n:Status key="AttributeLabel" contentColor="green"/>
			<n:Status key="Dashed" borderWidth="2px" borderStyle="dashed"/>
			<n:Status key="LineBorderStatus" title="Line border status" borderColor="red" backgroundColor="red" borderWidth="2px" borderStyle="3,3"/>
			<n:Status key="GroupCustomStatus" title="Group custom status" contentColor="white" backgroundColor="darkblue" borderColor="darkblue"/>
		</n:statuses>
		<n:layoutData>
			<m:FlexItemData growFactor="1" shrinkFactor="1" baseSize="0%" minWidth="300px"/>
		</n:layoutData>
		<!--<n:layoutAlgorithm>
			<layout:LayeredLayout mergeEdges="false" nodePlacement="Simple"/>
		</n:layoutAlgorithm>-->
		<n:nodes>
			<n:Node maxWidth="{maxWidth}" headerCheckBoxState="{headerCheckBoxState}" key="{key}" title="{title}" icon="{icon}" group="{group}"
				showExpandButton="{showExpandButton}" showActionLinksButton="{showActionLinksButton}" showDetailButton="{showDetailButton}"
				actionButtons="{path: 'actions', templateShareable:true}" attributes="{path:'attributes', templateShareable:true}" shape="{shape}"
				status="{status}">
				<n:attributes>
					<n:ElementAttribute icon="{icon}" labelStatus="{status}" valueStatus="{status}" label="{label}" value="{value}" visible="{visible}"/>
				</n:attributes>
				<n:actionButtons>
					<n:ActionButton icon="{icon}" press="{= ${handler} }" app:key="{key}"/>
				</n:actionButtons>
			</n:Node>
		</n:nodes>
		<n:lines>
			<n:Line arrowPosition="Middle" arrowOrientation="ParentOf" stretchToCenter="true" from="{from}" to="{to}" title="{title}" status="{status}">
				<n:actionButtons></n:actionButtons>
			</n:Line>
		</n:lines>
		<!-- Create 3 groups for Initiate, Complete/ Close and Actions -->
		<n:groups>
			<n:Group key="{key}" title="{title}" parentGroupKey="{parentGroupKey}"></n:Group>
		</n:groups>
	</n:Graph>
	<core:ComponentContainer
		id="workflowApproverTransportPackageSelectDialog"
		name="dmr.components.TransportPackage"
		async="true"
		componentCreated="onTransportPackageSelectionDialogCreated"/>

	<core:ComponentContainer 
		id="BADISelectDialog" 
		name="dmr.components.BADISelect" 
		async="true"
		componentCreated="onBADISelectionDialogCreated"/>
	
</mvc:View>