{"nodeBoxWidth": 200, "nodes": [{"key": "A0", "title": "A0: Requestor", "icon": "sap-icon://begin", "status": "Success", "group": "01init", "showActionLinksButton": false, "showDetailButton": false, "showExpandButton": false, "attributes": [{"label": "Step Actions:", "value": "Init,07,08", "visible": false}], "actionType": [{"action": "08", "actdesc": "withdraw"}, {"action": "09", "actdesc": "activate"}]}, {"key": "92", "title": "92: Withdraw/Discard", "icon": "sap-icon://delete", "status": "Warning", "group": "05actv", "groups": "03actv", "showActionLinksButton": false, "showDetailButton": false, "showExpandButton": false, "attributes": []}, {"key": "91", "title": "91: Activation", "icon": "sap-icon://activity-assigned-to-goal", "status": "Success", "group": "05actv", "detailbutton": false, "showActionLinksButton": false, "showDetailButton": false, "showExpandButton": false, "attributes": [{"label": "Step Actions:", "value": "31,32,33", "visible": false}], "actionType": [{"action": "31", "actdesc": "Success"}, {"action": "32", "actdesc": "Failed"}, {"action": "33", "actdesc": "Failed(Snapshot)"}]}, {"key": "99", "title": "99: Complete", "icon": "sap-icon://complete", "status": "Success", "group": "05actv", "groups": "04comp", "detailbutton": false, "showActionLinksButton": false, "showDetailButton": false, "showExpandButton": false, "attributes": []}, {"key": "Approver<PERSON><PERSON>-<PERSON><PERSON>", "title": "Approver", "icon": "sap-icon://person", "status": "Warning", "showActionLinksButton": false, "showDetailButton": false, "showExpandButton": false, "attributes": []}], "lines": [{"from": "A0", "to": "92", "title": "08", "description": "Withdraw", "press": "linePress"}, {"from": "91", "to": "99", "title": "31", "description": "Activation Successful", "press": "linePress"}, {"from": "92", "to": "99", "title": "", "description": "", "press": "linePress"}]}