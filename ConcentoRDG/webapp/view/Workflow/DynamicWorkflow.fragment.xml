<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="dmr.components"
	c:require="{DMRNode: 'dmr/mdg/supernova/SupernovaFJ/controller/Workflow/DMRNode'}" displayBlock="true">
	<Dialog title="{= ${approverModel>/dynamic/workflowType} === DMRNode.Types.Dynamic ? 'Dynamic' : 'Parallel' } Workflow" draggable="true">
		<!-- 
			Base Model : approverModel
			Mandatory Propety : /derviation
		-->
		<!-- Task 10758 - selectionChange event was removed -->
		<!-- Task 10759 - the mode property of the table was changed to "Delete"
						  Button "Enable Delete Rules" was removed
						  property "enabled" of some buttons was removed -->
		<Table id="idDerivationTable" alternateRowColors="true" mode="Delete"
			delete=".DynamicWorkflowDialog.deleteRowHandler"
			sticky="HeaderToolbar,InfoToolbar,ColumnHeaders"
			updateFinished=".DynamicWorkflowDialog.dynamicTableUpdatedHandler">
			<headerToolbar>
				<OverflowToolbar class="transparentBara" height="100%">
					<f:SimpleForm id="idDynamicForm" editable="true" layout="ColumnLayout" adjustLabelSpan="true" 
						emptySpanS="0" emptySpanM="0" emptySpanL="0" emptySpanXL="0" columnsXL="2" columnsL="2" columnsM="2" singleContainerFullSize="false"  width="60%">
					
						<Label text="Description" labelFor="idApproverDes" showColon="true"/>
						<Input id="idApproverDes" placeholder="Enter Description" value="{approverModel>/dynamic/Apprdesc}" 
							maxLength="40" width="100%"/>

						<Label text="Compare By" labelFor="idRulesCBox" showColon="true"/>
						<ComboBox id="idRuleSetCBox" selectionChange=".DynamicWorkflowDialog.onRuleSetCBoxChange" width="100%" 
								  selectedKey="{approverModel>/dynamic/Changedoc}"  enabled="{approverModel>/dynamic/ebChangedoc}">
							<c:ListItem key="AV" text="Attribute Values"/>
							<c:ListItem key="AC" text="Attribute Changes"/>
						</ComboBox>
					</f:SimpleForm>

				</OverflowToolbar>
			</headerToolbar>
			<infoToolbar>
				<OverflowToolbar class="transparentBar">
					<Button id="btnAddDrivingAttribute" tooltip="Add Attribute"
							text="Add Attribute" icon="sap-icon://table-column" visible="{= ${approverModel>/dynamic/Changedoc} === 'AV'}"
							press=".DynamicWorkflowDialog.addAttributeColumnHandler" iconFirst="false"/>
					<Button id="btnAddNewDerivationRule" tooltip="Add Rule" iconFirst="false" text="Add Rule" icon="sap-icon://table-row" 
						enabled="{= ${approverModel>/dynamic/tableHasColumns} || ${approverModel>/dynamic/Changedoc} === 'AC'}"
						press=".DynamicWorkflowDialog.addNewRuleHandler"/>
					<ToolbarSpacer/>
				</OverflowToolbar>
			</infoToolbar>
			<columns></columns>
			<items></items>
		</Table>
		<beginButton>
			<Button id="btnSaveConfigurationFooter" tooltip="Save Configuration"
				enabled="{ path:'approverModel>/dynamic/tableHasColumns', formatter:'.DynamicWorkflowDialog.getRuleButtonState'}"
				iconFirst="true" text="Save Configuration" icon="sap-icon://save" type="Transparent"
				press=".DynamicWorkflowDialog.saveDynamicWorkflowConfigurationHandler"/>
		</beginButton>
		<endButton>
			<Button id="btnCancelFooter" tooltip="Cancel"
				iconFirst="true" text="Cancel" icon="sap-icon://decline" type="Transparent"
				press=".DynamicWorkflowDialog.cancelDynamicWorkflowConfigurationHandler"/>
		</endButton>
	</Dialog>
</c:FragmentDefinition>