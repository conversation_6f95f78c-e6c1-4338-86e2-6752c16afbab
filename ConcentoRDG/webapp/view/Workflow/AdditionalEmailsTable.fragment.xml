<c:FragmentDefinition 
	xmlns:c="sap.ui.core" 
	xmlns="sap.ui.table" 
	xmlns:m="sap.m"
	controllerName="" displayBlock="true">
	<m:Dialog title="Additional Emails" draggable="true" contentWidth="30%">
		<!--<m:subHeader>-->
		<!--	<m:OverflowToolbar>-->
		<!--		<m:ToolbarSpacer/>-->
		<!--		<m:Button id="btnAddRow" icon="sap-icon://add" type="Transparent" tooltip="Add Row" press=".AdditionalEmailsTableDialog.onAddRowPressed"/>-->
		<!--		<m:Button id="btnDeleteRow" icon="sap-icon://delete" type="Transparent" tooltip="Delete Row" press=".AdditionalEmailsTableDialog.onDeleteRowPressed"/>-->
		<!--	</m:OverflowToolbar>-->
		<!--</m:subHeader>-->

		<m:List id="additionalEmailList" sticky="HeaderToolbar" 
			mode="Delete" delete=".AdditionalEmailsTableDialog.onDeleteRowPressed"
			items="{ 
					path: 'approverModel>/ConnectionEditor/connectionsList/addlEmailList'
				}">
			<m:headerToolbar>
				<m:Toolbar>
					<m:Text id="header" class="sapUiSmallMargin" text="Email Address" />
					<m:ToolbarSpacer></m:ToolbarSpacer>
					<m:Button id="btnAddRow" icon="sap-icon://add" tooltip="Add Row" press=".AdditionalEmailsTableDialog.onAddRowPressed" />
				</m:Toolbar>
			</m:headerToolbar>
				<m:CustomListItem >
					<m:HBox wrap="NoWrap" justifyContent="SpaceBetween">
						<m:VBox width="90%" class="sapUiTinyMarginBegin">
							<m:Input id="idAdditionalEmail" value="{approverModel>AddlEmail}" type="Email" valueLiveUpdate="true"
							valueState="{parts:['approverModel>AddlEmail'], formatter:'.AdditionalEmailsTableDialog.validateEmail'}" valueStateText="Enter a valid EMail" placeholder="Enter Email ..."/>
						</m:VBox> 
					</m:HBox>
				</m:CustomListItem>
		</m:List>
		<m:List id="additionalEmailAttributeList" sticky="HeaderToolbar" 
			mode="Delete" delete=".AdditionalEmailsTableDialog.onDeleteAttrRowPressed"
			items="{ 
					path: 'approverModel>/ConnectionEditor/connectionsList/addlEmailAttrList'
				}">
			<m:headerToolbar>
				<m:Toolbar>
					<m:Text id="headerAttribute" class="sapUiSmallMargin" text="Email via Attribute" />
					<m:ToolbarSpacer></m:ToolbarSpacer>
					<m:Button id="btnAddRowAttribute" icon="sap-icon://add" tooltip="Add Row" press=".AdditionalEmailsTableDialog.onAddAttrRowPressed" />
				</m:Toolbar>
			</m:headerToolbar>
				<m:CustomListItem >
					<m:HBox wrap="NoWrap" justifyContent="SpaceBetween" width="90%" class="sapUiTinyMarginBegin">
							<m:ComboBox id="idEmailEntity" placeholder="Select Entity"
								selectedKey="{approverModel>AddlEmailEntity}"
								items="{ path: 'approverModel>/ConnectionEditor/EntitiesList', templateShareable:true, length: 2000 }"
								selectionChange=".AdditionalEmailsTableDialog.onEntitySelectionChange" valueState="{= ${approverModel>AddlEmailEntity} ? 'None' : 'Error' }"
								valueStateText="Entity cannot be empty">
								<c:ListItem key="{approverModel>UsmdEntity}"
									text="{= ${approverModel>UsmdEntity}.concat(${approverModel>Txtlg}==='' ? '' : ' - '.concat(${approverModel>Txtlg}))}" />
							</m:ComboBox>

							<m:ComboBox id="idEmailAttribute" placeholder="Select Attribute"
								selectedKey="{approverModel>AddlEmailAttribute}"
								items="{ path: 'approverModel>attributeList', templateShareable:true, length: 2000 }" 
								valueState="{= ${approverModel>AddlEmailAttribute} ? 'None' : 'Error' }" valueStateText="Select relevant Attribute for the selected Entity">
								<c:ListItem key="{approverModel>UsmdAttribute}"
									text="{approverModel>UsmdAttribute} - {approverModel>Txtlg}"
									additionalText="{approverModel>Txtlg}"/>
							</m:ComboBox>
					</m:HBox>
				</m:CustomListItem>
		</m:List>
		<m:beginButton>
			<m:Button text="Save" press=".AdditionalEmailsTableDialog.additionalEmailsSaveClicked" id="save"/>
		</m:beginButton>
		<m:endButton>
			<m:Button text="Cancel" press=".AdditionalEmailsTableDialog.additionalEmailsCancelClicked" id="cancel"/>
		</m:endButton>
	</m:Dialog>
</c:FragmentDefinition>