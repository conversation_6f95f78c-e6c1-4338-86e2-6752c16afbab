<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:customdata="sap.ui.core.CustomData"
	xmlns:c="sap.ui.core" 
	xmlns:m="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true">
	<m:Dialog title="Select Approver">
		<l:VerticalLayout class="sapUiContentPadding" width="100%">
			<l:content>
				<!--<m:VBox class="sapUiSmallMarginEnd">-->
				<!--	<m:Label text="On" labelFor="idApproverAction"/>-->
				<!--	<m:Select -->
				<!--		valueStateText="Must Select and Action" -->
				<!--		id="idApproverAction" -->
				<!--		forceSelection="false" -->
				<!--		autoAdjustWidth="false"-->
				<!--		width="100%"-->
				<!--		selectedKey="{approverModel>/ApprConnection/action}"-->
				<!--		items="{-->
				<!--			path: 'approverModel>/ApprConnection/actions'-->
				<!--		}">-->
				<!--		<c:Item key="{approverModel>action}" text="{{approverModel>action}}"/>-->
				<!--	</m:Select>-->
				<!--</m:VBox>-->
				<!--<m:VBox class="sapUiSmallMarginEnd">-->
				<!--	<m:Label text="Next State" labelFor="idNextState"/>-->
				<!--	<m:ComboBox -->
				<!--		width="100%"-->
				<!--		id="idNextState"-->
				<!--		selectedKey= "{approverModel>/ApprConnection/nextState}"-->
				<!--		enabled="{= ${approverModel>/ApprConnection/action} !== ''}"-->
				<!--		items="{-->
				<!--			path: 'approverModel>/ApprConnection/stateList'-->
				<!--		}">-->
				<!--		<c:Item key="{approverModel>state}" text="{approverModel>state}" />-->
				<!--	</m:ComboBox>-->
				<!--</m:VBox>-->
			</l:content>
		</l:VerticalLayout>
		<m:beginButton>
			<m:Button 
				text="Save" 
				enabled="{= ${approverModel>/ApprConnection/action} !== '' &amp;&amp; ${approverModel>/ApprConnection/nextState} !== ''}"
				press=".onApproverConnectionSave" id="save"/>
		</m:beginButton>
		<m:endButton>
			<m:Button text="Cancel" press=".onApproverConnectionCancel" id="cancel"/>
		</m:endButton>
	</m:Dialog>
</c:FragmentDefinition>