<c:FragmentDefinition
	xmlns:mvc="sap.ui.core.mvc"
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true"
>
	<Dialog title="Create Custom Reason">
		<l:VerticalLayout class="sapUiContentPadding" width="100%">
			<l:content>
				<VBox>
				<VBox>
					<Label required="true" text="Reason" visible="{= ${approverModel>/ReasonDetailsFragment/reasonType} === 'CRReason'}"/>
					<Label required="true" text="Reason for Rejection" visible="{= ${approverModel>/ReasonDetailsFragment/reasonType} === 'CRRejectReason'}"/>
					<HBox>
						<Input value="{approverModel>/ReasonDetailsFragment/UsmdReason}" maxLength="2"
							valueState="{approverModel>/ReasonDetailsFragment/reasonValueState}"
							valueStateText="{approverModel>/ReasonDetailsFragment/reasonValueStateText}"
							visible="{= ${approverModel>/ReasonDetailsFragment/reasonType} === 'CRReason'}"/>
							
						<Input value="{approverModel>/ReasonDetailsFragment/UsmdReasonRej}" maxLength="2"
							valueState="{approverModel>/ReasonDetailsFragment/reasonValueState}"
							valueStateText="{approverModel>/ReasonDetailsFragment/reasonValueStateText}"
							visible="{= ${approverModel>/ReasonDetailsFragment/reasonType} === 'CRRejectReason'}"/>
					</HBox>
				</VBox>
				<VBox>
					<Label text="Description"></Label>
					<Input value="{approverModel>/ReasonDetailsFragment/Desc}"/>
				</VBox>
				</VBox>
			</l:content>
		</l:VerticalLayout>
		
		<beginButton>
			<Button 
				text="Create" 
				press=".NodeConnectionsTableDialog.reasonCreateClicked" id="BtnReasonCreate"/>
		</beginButton>
		<endButton>
			<Button
				text="Cancel"
				press=".NodeConnectionsTableDialog.reasonCancelClicked" id="BtnReasonCancel"/>
		</endButton>
	
	</Dialog>
</c:FragmentDefinition>