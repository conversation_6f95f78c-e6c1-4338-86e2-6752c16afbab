<core:FragmentDefinition
	xmlns="sap.m"
	xmlns:core="sap.ui.core">
	<Popover
		id="idCopyChangeRequestPopover"
		title="{viewWorkflow>/oCopyChangeRequest/popoverTitle}"
        afterClose = "onAfterClosePopover"
		class="sapUiResponsivePadding--header sapUiResponsivePadding--footer">
        <VBox>
            <VBox class="sapUiSmallMarginTop sapUiSmallMarginBegin sapUiSmallMarginEnd">
                <Label text="Data Model" required="true"/>
                <!-- Bug 12554 - Limited CR types available while copy -->
                <ComboBox id="idCopyCrTypeDataModel"
                    editable="{= ${viewWorkflow>/oCopyChangeRequest/action} === 'Copy'}" 
                    selectedKey="{viewWorkflow>/oCopyChangeRequest/selectedDataModel}"
                    selectionChange="onChangeNewCrTypeDataModel"
                    items="{ path: 'viewWorkflow>/arrDataModel', length: '2000' }">
                    <core:Item key="{viewWorkflow>Model}" text="{viewWorkflow>Model} - {viewWorkflow>Desc}"/>
                </ComboBox>
            </VBox>
            <VBox class="sapUiSmallMarginTop sapUiSmallMarginBottom sapUiSmallMarginBegin sapUiSmallMarginEnd">
                <Label text="Change Request" required="true"/>
                <!-- Bug 12554 - Limited CR types available while copy -->
                <ComboBox id="idCopyCrTypeCopyFrom"
                    selectedKey="{viewWorkflow>/oCopyChangeRequest/selectedCopyFrom}"
                    items="{ path: 'viewWorkflow>/oCopyChangeRequest/arrCrList', length: '2000' }">
                    <core:Item key="{viewWorkflow>UsmdCreqType}" text="{viewWorkflow>UsmdCreqType} - {viewWorkflow>Txtmi}"/>
                </ComboBox>
            </VBox>
            
        </VBox>
		<footer>
			<OverflowToolbar>
				<ToolbarSpacer/>
				<Button
					id="idButtonCopy"
					text="OK"
					press="onCopyChangeRequestType" 
                    enabled="{formatter: '.copyCrTypeEnabled', 
							parts: ['viewWorkflow>/oCopyChangeRequest/selectedDataModel', 'viewWorkflow>/oCopyChangeRequest/selectedCopyFrom']}"
                    />
			</OverflowToolbar>
		</footer>
	</Popover>
</core:FragmentDefinition>