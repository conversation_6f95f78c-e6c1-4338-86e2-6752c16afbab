<c:FragmentDefinition 
	xmlns:c="sap.ui.core" 
	xmlns="sap.ui.table" 
	xmlns:m="sap.m"
	c:require="{DMRNode: 'dmr/mdg/supernova/SupernovaFJ/controller/Workflow/DMRNode'}"
	controllerName="" displayBlock="true">
	<m:Dialog title="{approverModel>/ConnectionEditor/dialogTitle}" draggable="true" contentHeight="100%" contentWidth="100%">
		<m:subHeader>
			<m:OverflowToolbar>
				<m:ToolbarSpacer/>
				<m:Button id="btnAddRow" icon="sap-icon://add" type="Transparent" tooltip="Add Row" press=".NodeConnectionsTableDialog.onAddRowPressed"
					enabled="{= ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.Parallel &amp;&amp; ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.Merge &amp;&amp; ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.ParallelChild }" 
					/>
				<m:Button id="btnDeleteRow" 
					enabled="{= ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.Parallel &amp;&amp; ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.Merge &amp;&amp; ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.ParallelChild }" 
					icon="sap-icon://delete" type="Transparent" tooltip="Delete Row" press=".NodeConnectionsTableDialog.onDeleteRowPressed"/>
			</m:OverflowToolbar>
		</m:subHeader>

		<!-- RDG-80: added the length parameter to 2000 to show more than 100 rows-->
		<m:List id="nodeConnectionList" mode="SingleSelectMaster" sticky="HeaderToolbar"
			items="{ 
					path: 'approverModel>/ConnectionEditor/connectionsList', 
					sorter: { path: 'ListHeader', descending: false, group: true }, 
					length: 2000,
					groupHeaderFactory: '.NodeConnectionsTableDialog.getGroupHeader' 
				}">
			<m:headerToolbar>
				<m:OverflowToolbar>
					<m:VBox width="14%" class="sapUiTinyMarginBegin">
						<m:Label text="Change Request Priority" labelFor="changeRequestPrioritySelect"/>
					</m:VBox>
					<m:VBox width="14%" class="sapUiTinyMarginBegin">
						<m:Label text="Change Request Reason" labelFor="changeRequestReasonSelect"/>
					</m:VBox>
					<m:VBox width="14%" class="sapUiTinyMarginBegin">
						<m:Label text="Reason of Rejection" labelFor="changeRequestRejectReasonSelect"/>
					</m:VBox>
					<m:VBox width="14%" class="sapUiTinyMarginBegin">
						<m:Label text="Target State" labelFor="changeRequestTargetStateSelect"/>
					</m:VBox>
					<m:VBox width="14%" class="sapUiTinyMarginBegin sapUiSmallMarginEnd" 
						visible="{= ${approverModel>/ConnectionEditor/nodeTypeEdited} === DMRNode.Types.Parallel || ${approverModel>/ConnectionEditor/nodeTypeEdited} === DMRNode.Types.ParallelChild || ${approverModel>/ConnectionEditor/nodeTypeEdited} === DMRNode.Types.Merge}">
						<m:Label text="Sign" labelFor="changeRequestStatusDescriptionSelect"/>
					</m:VBox>
					<m:VBox width="14%" class="sapUiTinyMarginBegin sapUiTinyMarginEnd">
						<m:Label text="Status" labelFor="changeRequestStatusSelect"/>
					</m:VBox>
					<m:VBox width="14%" class="sapUiTinyMarginBegin sapUiSmallMarginEnd">
						<m:Label text="Status Description" labelFor="changeRequestStatusDescriptionSelect"/>
					</m:VBox>
					<m:VBox width="14%" class="sapUiTinyMarginBegin sapUiSmallMarginEnd">
						<m:Label text="Email Template" labelFor="templateSelectSearchList"/>
					</m:VBox>
					<!-- <m:VBox width="1%" class="sapUiTinyMarginBegin sapUiSmallMarginEnd">
						<m:Button icon="sap-icon://add-contact" vAlign="Middle"
					        tooltip="Add Email"/>
					</m:VBox> -->
				</m:OverflowToolbar>
			</m:headerToolbar>
			<m:CustomListItem >
				<m:HBox wrap="NoWrap" justifyContent="SpaceBetween">
					<m:VBox width="14%" class="sapUiTinyMarginBegin">
						<m:Select id="changeRequestPrioritySelect" width="100%" tooltip="Change Request Priority"
							enabled="{= ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.Parallel &amp;&amp; ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.Merge &amp;&amp; ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.ParallelChild }"
							selectedKey="{approverModel>CRPriority}" forceSelection="false"
							items="{ path: 'approverModel>/ConnectionEditor/CRPriorityList', templateShareable:true }">
							<c:Item key="{approverModel>UsmdPriority}"
								text="{= ${approverModel>UsmdPriority}.concat(${approverModel>UsmdPriority}.length > 0 ? ' - ': '').concat(${approverModel>Txtmi})}"/>
						</m:Select>
					</m:VBox> 
					<m:VBox width="14%" class="sapUiTinyMarginBegin">
						<m:Select id="changeRequestReasonSelect" width="100%" tooltip="Change Request Reason"
							enabled="{= ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.Parallel &amp;&amp; ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.Merge &amp;&amp; ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.ParallelChild }"
							selectedKey="{approverModel>CRReason}" forceSelection="false" change=".NodeConnectionsTableDialog.onReasonChange"
							items="{ path: 'approverModel>/ConnectionEditor/CRReasonList', templateShareable:true, length: '2000'}">
							<c:Item key="{approverModel>UsmdReason}"
								text="{= ${approverModel>UsmdReason}.concat(${approverModel>UsmdReason}.length > 0 ? ' - ': '').concat(${approverModel>Txtmi})}"/>
						</m:Select>
					</m:VBox>
					<m:VBox width="14%" class="sapUiTinyMarginBegin">
						<m:Select id="changeRequestRejectReasonSelect" width="100%" tooltip="Reason of Rejection"
							enabled="{= ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.Parallel &amp;&amp; ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.Merge &amp;&amp; ${approverModel>/ConnectionEditor/nodeTypeEdited} !== DMRNode.Types.ParallelChild }"
							selectedKey="{approverModel>CRRejectionReason}" forceSelection="false" change=".NodeConnectionsTableDialog.onRejectionReasonChange"
							items="{ path: 'approverModel>/ConnectionEditor/CRRejectReasonList', templateShareable:true,  length: '2000'}">
							<c:Item key="{approverModel>UsmdReasonRej}"
								text="{= ${approverModel>UsmdReasonRej}.concat(${approverModel>UsmdReasonRej}.length > 0 ? ' - ': '').concat(${approverModel>Txtmi})}"/>
						</m:Select>
					</m:VBox>
					<m:VBox width="14%" class="sapUiTinyMarginBegin">
						<m:Select id="changeRequestTargetStateSelect" width="100%" tooltip="Target State"
								selectedKey="{approverModel>TargetNode}" forceSelection="false" change=".NodeConnectionsTableDialog.onTargetStateChange"
							items="{ path: 'approverModel>/ConnectionEditor/targetNodeList', templateShareable:true }">
							<c:Item key="{approverModel>key}" text="{approverModel>key} - {approverModel>title}"/>
						</m:Select>
					</m:VBox>
					<m:VBox width="14%" class="sapUiTinyMarginBegin sapUiTinyMarginEnd" 
						visible="{= ${approverModel>/ConnectionEditor/nodeTypeEdited} === DMRNode.Types.Parallel || ${approverModel>/ConnectionEditor/nodeTypeEdited} === DMRNode.Types.ParallelChild || ${approverModel>/ConnectionEditor/nodeTypeEdited} === DMRNode.Types.Merge}">
						<m:Select id="changeRequestSignSelect" width="100%" tooltip="Sign"
								change=".NodeConnectionsTableDialog.onSelectItemSelectHandler(
										$event, ${approverModel>UsmdCrAction}, ${approverModel>/ConnectionEditor/nodeTypeEdited})"
							selectedKey="{approverModel>Sign}" forceSelection="false">
							<m:items>
								<c:Item key="" text=""/>
								<c:Item key="P" text="Positive"/>
								<c:Item key="X" text="Negative"/>
							</m:items>
						</m:Select>
					</m:VBox>
					<m:VBox width="14%" class="sapUiTinyMarginBegin sapUiTinyMarginEnd">
						<m:Select id="changeRequestStatusSelect" width="100%" tooltip="Status"
								selectedKey="{approverModel>CRStatus}" forceSelection="false" change=".NodeConnectionsTableDialog.onStatusChange"
								items="{ path: 'approverModel>/crStatus/results', templateShareable:true }">
							<c:Item key="{approverModel>UsmdCreqStatus}" text="{approverModel>UsmdCreqStatus} - {approverModel>Txtmi}"/>
						</m:Select>
					</m:VBox>
					<m:VBox width="14%" class="sapUiTinyMarginBegin sapUiTinyMarginEnd">
						<m:Select id="changeRequestStatusDescriptionSelect" width="100%" tooltip="Status Description"
							selectedKey="{approverModel>CR_OBMAIN}" forceSelection="false" editable="false"
							items="{ path: 'approverModel>/cr_OBMAIN/results', templateShareable:true }">
							<c:Item key="{approverModel>UsmdCreqObmain}"
								text="{= ${approverModel>UsmdCreqObmain}.concat(${approverModel>UsmdCreqObmain}.length > 0 ? ' - ': '').concat(${approverModel>UsmdCreqObmainTxt})}"/>
						</m:Select>
					</m:VBox>
					<m:VBox width="14%" class="sapUiTinyMarginBegin sapUiTinyMarginEnd">
						<c:ComponentContainer
						id="templateSelectSearchList"
						name="dmr.components.TemplateSelect"
						async="false"
						componentCreated=".NodeConnectionsTableDialog.onTemplateSelectSearchListCreated"/>
					</m:VBox>
					<m:VBox width="3%" class="sapUiTinyMarginBegin sapUiTinyMarginEnd">
						 <m:Button
					        icon="sap-icon://add-contact"
					        enabled="{= ${approverModel>Emailtemp} ? true : false}"
					        tooltip="{approverModel>AddlEmails}"
					        press=".NodeConnectionsTableDialog.openAdditionalEmailsForm"/>
					</m:VBox>
				</m:HBox>
			</m:CustomListItem>
		</m:List>
		<m:beginButton>
			<m:Button text="Save" press=".NodeConnectionsTableDialog.editConnectionsSaveClicked" id="save"/>
		</m:beginButton>
		<m:endButton>
			<m:Button text="Cancel" press=".NodeConnectionsTableDialog.editConnectionsCancelClicked" id="cancel"/>
		</m:endButton>
	</m:Dialog>
</c:FragmentDefinition>
