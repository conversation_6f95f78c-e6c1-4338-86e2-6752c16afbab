<mvc:View 
	xmlns="sap.m" xmlns:mvc="sap.ui.core.mvc" xmlns:c="sap.ui.core"
	width="100%"
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.UIProperties.FieldPropertyConfig"
	busyIndicatorDelay="0">
	<Toolbar class="sapStepsHeader">
        <ToolbarSpacer/>
		<Label text="Highlight Changes:" />
		<Switch name="highlightChanges" state="{fieldPropertiesModel>/highlightChanges}" customTextOn="Yes" customTextOff="No"
			enabled="{= ${fieldPropertiesModel>/selectedStep} !== undefined &amp;&amp; ${fieldPropertiesModel>/selectedCRType} !== undefined &amp;&amp; ${fieldPropertiesModel>/HCIsEditable}}"
			change=".onChangeHighlightChanges"/>
		<Button id="btnImportByStepType" text="Import" 
			enabled="{= ${fieldPropertiesModel>/selectedStep} !== undefined &amp;&amp; ${fieldPropertiesModel>/selectedCRType} !== undefined}"
			tooltip="Import Rules" press="onImportByStepTypeClick"/>
		<Button id="btnSelectEntities" text="Select Entities"
			enabled="{= ${fieldPropertiesModel>/selectedStep} !== undefined &amp;&amp; ${fieldPropertiesModel>/selectedCRType} !== undefined}"
			tooltip="Select Entities" press=".onClickSelectEntities" />
    </Toolbar>
        <IconTabBar id="iconTabBar" visible="false" select=".onSelectEntityTab" />
		<VBox id="idUnidentifiedEntitiesBox">
			<c:Fragment fragmentName="dmr.mdg.supernova.SupernovaFJ.view.UIProperties.UnidentifiedEntities" type="XML" />
		</VBox>
		<c:ComponentContainer id="uiPropConfigTransportPackageComponent" name="dmr.components.TransportPackage" async="true"
		componentCreated="onTransportPackageSelectionDialogCreated"/>
		<c:ComponentContainer id="BADISelectDialog" name="dmr.components.BADISelect" async="true"
		componentCreated="onBADISelectionDialogCreated"/>		

		
</mvc:View>