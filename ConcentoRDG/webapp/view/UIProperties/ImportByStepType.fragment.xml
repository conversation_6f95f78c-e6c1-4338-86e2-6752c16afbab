<!-- Task 10868 - UI Properties Copy by Step Type - UI5 -->
<c:FragmentDefinition 
	xmlns:c="sap.ui.core" 
	xmlns:m="sap.m" 
	controllerName="" 
	displayBlock="true">
	
	<m:Dialog title="Import Field Properties">
		<m:VBox class="sapUiSmallMarginTop sapUiSmallMarginBegin sapUiSmallMarginEnd">
			<m:Label 
				labelFor="idImportFromCRType"
				text="From Change Request Type"/>
			<!-- Bug 12554 - Limited CR types available while copy -->
			<m:ComboBox 
				id="idImportFromCRType"
				items="{ path: 'fieldPropertiesModel>/importByStepType/changeRequestList', length: '2000' }"
				selectedKey="{fieldPropertiesModel>/importByStepType/selectedCRType}"
				change="onImportByStepTypeChangeCRType"
				required="true">
				<c:ListItem
					key="{fieldPropertiesModel>UsmdCreqType}" 
					text="{fieldPropertiesModel>UsmdCreqType} - {fieldPropertiesModel>Txtmi}"/>
			</m:ComboBox>

			<m:Label 
				labelFor="idImportFromStepType" 
				text="From Step Type"
				class="sapUiSmallMarginTop"/>
			<m:ComboBox 
				id="idImportFromStepType"
				items="{fieldPropertiesModel>/importByStepType/stepTypesList}"  
				selectedKey="{fieldPropertiesModel>/importByStepType/selectedStepType}"
				change="onImportByStepTypeChangeStepType"
				required="true">
				<c:ListItem
					key="{fieldPropertiesModel>UsmdCreqStep}" 
					text="{fieldPropertiesModel>UsmdCreqStep} - {fieldPropertiesModel>Txtmi}"/>
			</m:ComboBox>
			
			<c:ComponentContainer name="dmr.components.SearchList" async="false" id="ImportByStepTypeList"
				componentCreated="onImportByStepTypeListCreated"/>
		</m:VBox>

		<m:beginButton>
		<!-- Bug 13573: added ebSaveImport to enabled property -->
			<m:Button text="Save" press="onImportByStepTypeSave" id="idImportByStepTypeSave" enabled="{fieldPropertiesModel>/importByStepType/ebSaveImport}"/>
		</m:beginButton>
		<m:endButton>
			<m:Button text="Cancel" press="onImportByStepTypeCancel" id="idImportByStepTypeCancel"/>
		</m:endButton>
	</m:Dialog>

</c:FragmentDefinition>