<!-- Task 10260 - Provide a better User interface for the SIngle value derivation (Check Expression) -->
<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="dmr.components"
	displayBlock="true">
	
        <Dialog title="User-Defined Expression" contentWidth="400px">
                <l:VerticalLayout class="sapUiContentPadding" width="100%">
		        <l:content>
                                <HBox>
                                        <Button id="btnOpenParenthesis" text="(" press=".UserDefinedExpressionModel.onClickOpenParenthesis" class="sapUiSmallMarginEnd"/>
                                        <Button id="btnCloseParenthesis" text=")" press=".UserDefinedExpressionModel.onClickCloseParenthesis" class="sapUiSmallMarginEnd"/>
                                        <Button id="btnNewAttribute" text="New Attribute" press=".UserDefinedExpressionModel.onClickNewAttribute" class="sapUiSmallMarginEnd"/>
                                        <Button id="btnAndOperator" text="AND" press=".UserDefinedExpressionModel.onClickAndOperator" class="sapUiSmallMarginEnd"/>
                                        <Button id="btnOrOperator" text="OR" press=".UserDefinedExpressionModel.onClickOrOperator"/>
                                </HBox>

                                <Input id="txtUserDefinedExpression"
                                       value="{fieldPropertiesDetails>/singleValueUIDetails/userDefinedExpression}"
                                       placeholder="Click the buttons above to build your expression..."
                                       valueState="{fieldPropertiesDetails>/singleValueUIDetails/userDefinedExpressionValueState}"
                                       valueStateText="{fieldPropertiesDetails>/singleValueUIDetails/userDefinedExpressionValueStateText}"/>
                        </l:content>
		</l:VerticalLayout>

                <beginButton>
                        <Button id="btnSave" text="Save" press=".UserDefinedExpressionModel.onClickSave"/>
                </beginButton>
                <endButton>
                        <Button id="btnCancel" text="Cancel" press=".UserDefinedExpressionModel.onClickCancel"/>
                </endButton>
	</Dialog>
	
</c:FragmentDefinition>