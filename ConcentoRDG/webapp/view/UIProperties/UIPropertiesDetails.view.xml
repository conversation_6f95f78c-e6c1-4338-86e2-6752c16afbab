<mvc:View xmlns="sap.m" xmlns:c="sap.ui.core" xmlns:l="sap.ui.layout" xmlns:f="sap.f" xmlns:mvc="sap.ui.core.mvc"
	xmlns:z="dmr.mdg.supernova.SupernovaFJ.controller.UIProperties" width="100%"
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.UIProperties.UIPropertiesDetails" xmlns:html="http://www.w3.org/1999/xhtml">
	<Toolbar class="sapMTBHeader">
		<Button text="{= ${fieldPropertiesDetails>/AttributePath/CRName}}" type="Transparent" enabled="false" tooltip="CR Name"/>
		<Label visible="{= ${fieldPropertiesDetails>/AttributePath/DataModel} !== undefined}" text="{= '>>'}"/>
		<Button visible="{= ${fieldPropertiesDetails>/AttributePath/DataModel} !== undefined}"
			text="{= ${fieldPropertiesDetails>/AttributePath/DataModel}}" type="Transparent" enabled="false" tooltip="Atrribute Path Data Model"/>
		<Label visible="{= ${fieldPropertiesDetails>/AttributePath/EntityName} !== undefined}" text="{= '>>'}"/>
		<Label visible="{= ${fieldPropertiesDetails>/AttributePath/EntityName} !== undefined}"
			text="{= ${fieldPropertiesDetails>/AttributePath/EntityName}}" tooltip="Entity Name"/>
		<MenuButton visible="{= ${fieldPropertiesDetails>/AttributePath/EntityName} !== undefined}" type="Accept" buttonMode="Regular"
			useDefaultActionOnly="true" tooltip="Entity Menu for Value Field Property">
			<menu>
				<Menu itemSelected="onEntityMenuAction">
					<MenuItem id="idNewSingleValueUIPropertyItem" text="Single Value Field Property" icon="sap-icon://filter-fields" press=".addNewUIProperty"/>
					<MenuItem id="idNewMultiValueUIPropertyItem" text="Multi Value Field Property" icon="sap-icon://filter-fields" press=".addNewUIProperty"/>
				</Menu>
			</menu>
		</MenuButton>
		<Label visible="{= ${fieldPropertiesDetails>/AttributePath/RuleName} !== undefined}" text="{= '>>'}"/>
		<Label visible="{= ${fieldPropertiesDetails>/AttributePath/RuleName} !== undefined}"
			text="{= ${fieldPropertiesDetails>/AttributePath/RuleName}}"/>
		<ToolbarSpacer/>
		<Button id="btnImportFieldProp" text="Import" tooltip="Import Rules" press="onClickImportUIProperties"
			visible="{= ${fieldPropertiesDetails>/AttributePath/CRName} !== undefined}"/>
	</Toolbar>
	<VBox class="sapUiContentPadding" width="100%" height="85vh" fitContainer="true" id="idUIPropertiesPlaceHolder"></VBox>
	<c:ComponentContainer id="uiPropDetTransportPackageSelectionDialog" name="dmr.components.TransportPackage" async="true"
		componentCreated="onTransportPackageDialogCreated"/>

	<c:ComponentContainer id="BADISelectDialog" name="dmr.components.BADISelect" async="true"
		componentCreated="onBADISelectionDialogCreated"/>
</mvc:View>