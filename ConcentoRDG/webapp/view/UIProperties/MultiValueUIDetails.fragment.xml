<c:FragmentDefinition xmlns:mvc="sap.ui.core.mvc" xmlns:u="sap.ui.unified" xmlns:c="sap.ui.core" xmlns="sap.m" xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.UIProperties.UIPropertiesDetails" displayBlock="true">
	<VBox height="100%">
		<!-- 
			Base Model : fieldPropertiesDetails
			Mandatory Propety : /derviation
		-->
		<!-- Task 12487 - Add User Rule Name to Rules/Properties -->
		<VBox class="sapUiSmallMarginBegin sapUiSmallMarginTop sapUiSmallMarginBottom">
			<Label text="Rule Name" labelFor="idMultiValueUIUserRuleName"/>
			<Input 
				id="idMultiValueUIUserRuleName" width="25%"
				value="{fieldPropertiesDetails>/multiValueUI/userRuleName}">
			</Input>
		</VBox>
		<!-- Task 10758 - selectionChange event was removed -->
		<!-- Task 10759 - the mode property of the table was changed to "Delete"
						  Button "Enable Delete Rules" was removed
						  property "enabled" of some buttons was removed -->
		<Table id="idMultiValueUITable" alternateRowColors="true" mode="Delete"
			autoPopinMode="true" contextualWidth="Auto" popinLayout="GridLarge"
			delete=".MultiValueDetailsModel.deleteRow"
			sticky="HeaderToolbar,InfoToolbar,ColumnHeaders">
			<headerToolbar>
				<OverflowToolbar class="transparentBara">
					<Button id="btnAddDrivingAttribute" tooltip="Add Driving Attribute"
						text="Add Driving Attribute" icon="sap-icon://add-equipment"
						press=".MultiValueDetailsModel.addAttributeColumn"/>
					<ToolbarSpacer/>
					<Title id="TblTitle" text="Multi UI Property"/>
					<ToolbarSpacer/>
					<Button id="btnAddDerivingAttribute" tooltip="Add Deriving Attribute"
						iconFirst="false" text="Add Deriving Attribute"
						icon="sap-icon://activity-assigned-to-goal" press=".MultiValueDetailsModel.addAttributeColumn"/>
				</OverflowToolbar>
			</headerToolbar>
			<infoToolbar>
				<OverflowToolbar class="transparentBar">
					<Button id="btnAddNewDerivationRule" tooltip="Add Property" iconFirst="false" text="Add Rule" icon="sap-icon://write-new"
						enabled="{ path:'fieldPropertiesDetails>/multiValueUI/tableHasColumns', formatter:'.MultiValueDetailsModel.getRuleButtonState'}"
						press=".MultiValueDetailsModel.addNewRule"/>
					<ToolbarSpacer/>
					<Button id="btnDeleteRule" text="Delete UI Property" tooltip="Delete UI Property" iconFirst="false" icon="sap-icon://delete"
						press=".MultiValueDetailsModel.onRuleDeletePressed" enabled="{= !${fieldPropertiesDetails>/lockedActions}.includes('D')}"/>
					<ToolbarSpacer/>
					<Button id="btnSaveConfiguration" tooltip="Save Configuration"
						enabled="{parts: [{path:'fieldPropertiesDetails>/multiValueUI/tableHasColumns'},{path:'fieldPropertiesDetails>/multiValueUI/tableHasDrivingColumns'},{path:'fieldPropertiesDetails>/multiValueUI/tableHasRules'}], formatter:'.MultiValueDetailsModel.getRuleButtonState'}"
						iconFirst="true" text="Save Configuration" icon="sap-icon://save" press=".MultiValueDetailsModel.saveMultiUIValueConfiguration"/>
				</OverflowToolbar>
			</infoToolbar>
			<columns></columns>
			<items></items>
		</Table>
	</VBox>
</c:FragmentDefinition>