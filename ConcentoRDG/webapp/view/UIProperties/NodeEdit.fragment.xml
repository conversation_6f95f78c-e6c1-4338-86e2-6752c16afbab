<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.UIProperties.UIPropertiesDetails"
	displayBlock="true">
	<Dialog title="Edit Node Details" contentWidth="40%">
		<l:VerticalLayout class="sapUiContentPadding" width="100%">
			<l:content>				
			<HBox>
			<!-- This label holds the ID of the node that is being edited. The data is stored back to the node with this ID -->
				<Label text="{fieldPropertiesDetails>/NodeEditDetails/nodeId}" visible="false"/>
				<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
					<Label text="Entity" labelFor="idPropertiesCBoxEntities"/>
					<ComboBox id="idPropertiesCBoxEntities" width="100%"
						selectedKey="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedEntity}" 
						items="{ path: 'fieldPropertiesDetails>/NodeEditDetails/nodeEditEntityList', sorter: { path: 'UsmdEntity' } }"
						selectionChange=".NodeEditModel.onNodeEditEntitySelectionChange" >
						<c:ListItem key="{fieldPropertiesDetails>UsmdEntity}"
							text="{= ${fieldPropertiesDetails>UsmdEntity}.concat(${fieldPropertiesDetails>Txtlg}==='' ? '' : ' - '.concat(${fieldPropertiesDetails>Txtlg}))}" />
					</ComboBox>
				</VBox>
		
				<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
					<Label text="Attribute" labelFor="idPropertiesCBoxAttributes"/>
					<ComboBox id="idPropertiesCBoxAttributes" width="100%"
						selectedKey="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedAttribute}" 
						items="{ path: 'fieldPropertiesDetails>/NodeEditDetails/nodeEditAttributeList', length:2000, sorter: { path: 'UsmdAttribute' } }"
						selectionChange=".NodeEditModel.onNodeEditAttributeSelectionChange" >
						<c:ListItem key="{fieldPropertiesDetails>UsmdAttribute}"
							text="{fieldPropertiesDetails>UsmdAttribute} - {fieldPropertiesDetails>Txtlg}"
							additionalText="{fieldPropertiesDetails>Txtlg}"/>
					</ComboBox>
				</VBox>
			</HBox>
				
			<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
				<Label text="Comparision" labelFor="idPropertiesSelectComparisionOp"/>
				<Select id="idPropertiesSelectComparisionOp"
					selectedKey="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator}"
					change=".NodeEditModel.onNodeEditComparisionChange"
					items="{ path: 'fieldPropertiesDetails>/NodeEditDetails/nodeEditComparatorList' }" >
					<c:ListItem
						text="{fieldPropertiesDetails>id} - {fieldPropertiesDetails>name}"
						additionalText="{fieldPropertiesDetails>constant}"
						key="{fieldPropertiesDetails>id}"/>
				</Select>
			</VBox>
		
			<HBox>
				<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd"
					visible="{= ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementType} === 'Q'}">
					<Label text="Dimension" labelFor="idDimension"/>
					<ComboBox id="idDimensionUIProp"
						selectedKey="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedDimension}" 
						items="{ path: 'fieldPropertiesDetails>/NodeEditDetails/dimensionsList', sorter: { path: 'Dimension' } }"
						selectionChange=".NodeEditModel.onNodeEditDimensionChange" >
						<c:ListItem key="{fieldPropertiesDetails>Dimension}"
							text="{= ${fieldPropertiesDetails>Dimension}.concat(${fieldPropertiesDetails>Text}==='' ? '' : ' - '.concat(${fieldPropertiesDetails>Text}))}" />
					</ComboBox>
				</VBox>
				
				<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd"
					visible="{= ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementType} === 'Q' || ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementType} === 'A'}">
					<Label text="Unit" labelFor="idUnit"/>
					<ComboBox id="idUnit"
						selectedKey="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedUnit}" 
						items="{ path: 'fieldPropertiesDetails>/NodeEditDetails/UnitsList', length: 500, sorter: { path: 'Unit' } }">
						<c:ListItem key="{fieldPropertiesDetails>Unit}"
							text="{= ${fieldPropertiesDetails>Unit}.concat(${fieldPropertiesDetails>Text}==='' ? '' : ' - '.concat(${fieldPropertiesDetails>Text}))}" />
					</ComboBox>
				</VBox>			
		
				<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
					<Label
						visible="{= ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'BT' &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'NB'}"
						text="Value" labelFor="idInputValue"/>
					<Label 
						visible="{= ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} === 'BT' || ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} === 'NB' }"
						text="From Value" labelFor="idPropertiesInputFromValue"/>
					<!-- Task 11217 - Value Conversion and Data Check for Single value rules and properties -->
					<!-- Bug 12379 - Not able to enter pattern when CP or NP selected for attribute value if it is dropdown -->
					<Input id="idPropertiesInputFromValue" width="100%"
						visible="{= ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} === 'CP' || ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} === 'NP' || ( ${fieldPropertiesDetails>/NodeEditDetails/nodeEditFromValueList}.length === 0 &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementType} !== 'P' ) }"
						value="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/inputFromValue}" 
						placeholder="Enter Value ..."
						change=".NodeEditModel.onChangeValue"
						maxLength="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementLength}">
					</Input>
					<Input id="idPropertiesInputFromValueSugg" width="100%"
						visible="{= ( ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'CP' &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'NP' &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList} ) ? ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList}.length !== 0 &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementType} !== 'P' : false}"
						value="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/inputFromValue}" 
						placeholder="Enter Value ..." liveChange=".NodeEditModel.onLiveChangeFromValue"
						change=".NodeEditModel.onChangeValue"
						autocomplete="false"
						showSuggestion="true"
						suggestionItems="{fieldPropertiesDetails>/NodeEditDetails/nodeEditFromValueList}"
						startSuggestion="0"
						valueState="Information"
						valueStateText="Type to refresh list">
						<suggestionItems>
							<c:Item
								key="{fieldPropertiesDetails>Key}"
								text="{= ${fieldPropertiesDetails>Key}.concat(${fieldPropertiesDetails>Ddtext}==='' ? '' : ' - '.concat(${fieldPropertiesDetails>Ddtext}))}"
							/>
						</suggestionItems>
					</Input>
					<!-- Bug 12379 - Not able to enter pattern when CP or NP selected for attribute value if it is dropdown -->
					<!-- <ComboBox id="idPropertiesComboFromValue" 
						visible="{= ( ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'CP' &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'NP' &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList} ) ? ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList}.length !== 0 &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementType} !== 'P' : false}" 
						width="100%" selectedKey="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/inputFromValue}"
						items="{path: 'fieldPropertiesDetails>/NodeEditDetails/nodeEditFromValueList', length: '2000' }">
						<c:ListItem key="{fieldPropertiesDetails>Key}" text="{= ${fieldPropertiesDetails>Key}.concat(${fieldPropertiesDetails>Ddtext}==='' ? '' : ' - '.concat(${fieldPropertiesDetails>Ddtext}))}"/>
					</ComboBox> -->
					<!-- Bug 12379 - Not able to enter pattern when CP or NP selected for attribute value if it is dropdown -->
					<DatePicker id="idPropertiesDatePFromValue" width="100%"
						visible="{= ( ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'CP' &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'NP' &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList} ) ? ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList}.length === 0 &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementType} === 'P' : false}"
						value="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/inputFromValue}" 
						placeholder="Enter Date ..." change=".NodeEditModel.onChangeDate"
						displayFormat="M/d/yyyy"	valueFormat="yyyyMMdd"
					/>
				</VBox>
		
				<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd"
					visible="{= ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} === 'BT' || ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/selectedComparator} === 'NB'}">
					<Label text="To Value" labelFor="idPropertiesInputToValue"/>
					<!-- Task 11217 - Value Conversion and Data Check for Single value rules and properties -->
						<!-- visible="{= ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList}.length === 0 &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementType} !== 'P'}" -->
					<Input id="idPropertiesInputToValue" width="100%"
						visible="{= ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList}.length === 0 &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementType} !== 'P'}"
						value="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/inputToValue}" 
						placeholder="Enter Value ..."
						change=".NodeEditModel.onChangeValue"
						maxLength="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementLength}">
					</Input>
					<Input id="idPropertiesInputToValueSugg" width="100%"
						visible="{= ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList} ? ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList}.length !== 0 &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementType} !== 'P' : false}"
						value="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/inputToValue}" 
						placeholder="Enter Value ..." liveChange=".NodeEditModel.onLiveChangeToValue"
						change=".NodeEditModel.onChangeValue"
						autocomplete="false"
						showSuggestion="true"
						suggestionItems="{fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList}"
						startSuggestion="0"
						valueState="Information"
						valueStateText="Type to refresh list">
						<suggestionItems>
							<c:Item
								key="{fieldPropertiesDetails>Key}"
								text="{= ${fieldPropertiesDetails>Key}.concat(${fieldPropertiesDetails>Ddtext}==='' ? '' : ' - '.concat(${fieldPropertiesDetails>Ddtext}))}"
							/>
						</suggestionItems>
					</Input>
					<!-- <ComboBox id="idPropertiesComboToValue" 
						visible="{= ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList} ? ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList}.length !== 0 &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementType} !== 'P' : false}" 
						width="100%" selectedKey="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/inputToValue}"
						items="{path: 'fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList', length: '2000'}">
						<c:ListItem key="{fieldPropertiesDetails>Key}" text="{= ${fieldPropertiesDetails>Key}.concat(${fieldPropertiesDetails>Ddtext}==='' ? '' : ' - '.concat(${fieldPropertiesDetails>Ddtext}))}"/>
					</ComboBox> -->
					<DatePicker id="idPropertiesDatePToValue" width="100%"
						visible="{= ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList} ? ${fieldPropertiesDetails>/NodeEditDetails/nodeEditToValueList}.length === 0 &amp;&amp; ${fieldPropertiesDetails>/NodeEditDetails/nodeEditData/ElementType} === 'P' : false}"
						value="{fieldPropertiesDetails>/NodeEditDetails/nodeEditData/inputToValue}" 
						placeholder="Enter Date ..." change=".NodeEditModel.onChangeDate"
						displayFormat="M/d/yyyy"	valueFormat="yyyyMMdd"
					/>
				</VBox>
			</HBox>
			</l:content>
		</l:VerticalLayout>

		<beginButton> 
			<Button 
				id="save"
				text="Save"
				press=".NodeEditModel.onNodeEditSave"/>
		</beginButton>
		<endButton>
			<Button text="Cancel" press=".NodeEditModel.onNodeEditCancel" id="cancel"/>
		</endButton>

	</Dialog>
	
</c:FragmentDefinition>