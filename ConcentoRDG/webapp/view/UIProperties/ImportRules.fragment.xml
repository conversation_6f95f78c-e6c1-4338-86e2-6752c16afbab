<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:customdata="sap.ui.core.CustomData"
	xmlns:c="sap.ui.core" 
	xmlns:m="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true">
	<m:Dialog title="Import Field Properties">
		<m:VBox class="sapUiSmallMarginTop sapUiSmallMarginBegin sapUiSmallMarginEnd">
			<m:HBox class="sapUiSmallMarginBottom">
				<m:VBox class="sapUiSmallMarginEnd">
					<m:Label text="Change Request Type"/>
					<!-- Bug 12554 - Limited CR types available while copy -->
					<m:ComboBox 
						id="idCbImportUIPropertiesType"
						items="{ path: 'fieldPropertiesDetails>/importUIProperties/changeRequestList', length: 2000 }"
						selectedKey="{fieldPropertiesDetails>/importUIProperties/selectedCRType}"
						change=".onSelectCRType">
						<c:ListItem
							additionalText="{fieldPropertiesDetails>UsmdModel}"
							key="{fieldPropertiesDetails>UsmdCreqType}" 
							text="{fieldPropertiesDetails>UsmdCreqType} - {fieldPropertiesDetails>Txtmi}"/>
					</m:ComboBox>
				</m:VBox>
			</m:HBox>
			
			<c:ComponentContainer id="importUIPropertiesList" name="dmr.components.SearchList" async="false"
				componentCreated="onImportUIPropertiesListCreated"/>
		</m:VBox>
		<m:beginButton>
			<m:Button 
				text="Save" press=".onImportUiPropertiesSave" id="idSaveImportUIProperties" enabled="{= ${fieldPropertiesDetails>/importAllowed}}"/>
		</m:beginButton>
		<m:endButton>
			<m:Button text="Cancel" press=".onImportUiPropertiesCancel" id="idCancelImportUIProperties"/>
		</m:endButton>
	</m:Dialog>
</c:FragmentDefinition>