<c:FragmentDefinition xmlns:mvc="sap.ui.core.mvc" xmlns:u="sap.ui.unified" xmlns:c="sap.ui.core" xmlns="sap.m" xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" xmlns:html="http://www.w3.org/1999/xhtml" controllerName="dmr.components" displayBlock="true">
	<Dialog title="Select Attribute">
		<l:VerticalLayout class="sapUiContentPadding" width="100%">
			<l:content>
				<!-- This label holds the ID of the node that is being edited. The data is stored back to the node with this ID -->
				<Label text="{fieldPropertiesDetails>/SelectAttributeDialog/AttributeType}" visible="false"/>
				<VBox>
					<Label text="Entity" labelFor="idCBoxEntities" required="true"/>
					<ComboBox id="idCBoxEntities" width="100%" selectedKey="{fieldPropertiesDetails>/SelectAttributeDialog/selected/entity}"
						items="{ path: 'fieldPropertiesDetails>/SelectAttributeDialog/entityList', sorter: { path: 'UsmdEntity' }, length: 2000}"
						enabled="{= ${fieldPropertiesDetails>/SelectAttributeDialog/attributeType} === 'Driving'}"
						selectionChange=".SelectAttributeDialog.onEntitySelectionChange">
						<c:ListItem key="{fieldPropertiesDetails>UsmdEntity}"
							text="{= ${fieldPropertiesDetails>UsmdEntity}.concat(${fieldPropertiesDetails>Txtlg}==='' ? '' : ' - '.concat(${fieldPropertiesDetails>Txtlg}))}"/>
					</ComboBox>
				</VBox>
				<VBox>
					<Label text="Attribute" labelFor="idCBoxAttributes" required="true"/>
					<RadioButtonGroup id="rbValueOrLength" columns="2"
						visible="{= ${fieldPropertiesDetails>/SelectAttributeDialog/attributeType} === 'Driving'}"
						selectedIndex="{fieldPropertiesDetails>/SelectAttributeDialog/selected/type}"
						select=".SelectAttributeDialog.onSelectAttrType">
						<buttons>
							<RadioButton text="Value"/>
							<RadioButton text="Length"/>
						</buttons>
					</RadioButtonGroup>
					<!-- Task 11998 - Change ComboBox to MultiComboBox for multiple selection of attributes -->
					<RadioButtonGroup id="rbForAllAttributes" columns="2"
						visible="{= ${fieldPropertiesDetails>/SelectAttributeDialog/attributeType} === 'Deriving'}"
						selectedIndex="{fieldPropertiesDetails>/SelectAttributeDialog/selected/forAllAttributes}"
						select=".SelectAttributeDialog.onSelectForAllAttributes">
						<buttons>
							<RadioButton text="Attributes"/>
							<RadioButton text="For All Attributes"/>
						</buttons>
					</RadioButtonGroup>
					<!-- Task 11998 - Change ComboBox to MultiComboBox for multiple selection of attributes -->
					<MultiComboBox id="idCBoxAttributes" width="300px"
						visible="{= ${fieldPropertiesDetails>/SelectAttributeDialog/selected/forAllAttributes} === 0}"
						selectedKeys="{fieldPropertiesDetails>/SelectAttributeDialog/selected/attributes}" showSelectAll="true"
						items="{ path: 'fieldPropertiesDetails>/SelectAttributeDialog/attributeListFiltered', length: 2000, sorter: { path: 'UsmdAttribute' } }" >
						<c:Item key="{fieldPropertiesDetails>UsmdAttribute}"
							text="{fieldPropertiesDetails>UsmdAttribute} - {fieldPropertiesDetails>Txtlg}"/>
					</MultiComboBox>
				</VBox>
			</l:content>
		</l:VerticalLayout>
		<beginButton>
			<Button text="Add Column" press=".SelectAttributeDialog.onAddColumnConfirm" id="addColumn"/>
		</beginButton>
		<endButton>
			<Button text="Cancel" press=".SelectAttributeDialog.onAddColumnCancel" id="cancel"/>
		</endButton>
	</Dialog>
</c:FragmentDefinition>