<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml" 
	xmlns:sc="sap.suite.ui.commons"
	xmlns:n="sap.suite.ui.commons.networkgraph"
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.UIProperties.UIPropertiesDetails">
	
	<f:SimpleForm 
		id="idValueChangeUIPropertyForm"
		editable="{= ${fieldPropertiesDetails>/singleValueUIDetails/editForm} === true}">
	<f:toolbar>
		<Toolbar class="transparentBar">
			<Label text="Single Value UI Property"/>
		 	<ToolbarSpacer/>
		 	
		 	<Button 
				id="btnDeleteProperty" 
				text="Delete UI Property" 
				tooltip="Delete UI Property" 
				iconFirst="false" icon="sap-icon://delete" type="Transparent"
				enabled="{= ${fieldPropertiesDetails>/singleValueUIDetails/editForm} === true}"
				press=".SingleValueUIDetailsDialog.onPropertyDeletePressed"/>
			<ToolbarSpacer/>
		 	
			<Label text="Property Active"></Label>
			<Switch 
				name="propertyEnabledSwitch"
				type="AcceptReject" 
				state="{fieldPropertiesDetails>/singleValueUIDetails/propertyEnabled}"
				enabled="{= ${fieldPropertiesDetails>/singleValueUIDetails/editForm} === true}"
				change=".SingleValueUIDetailsDialog.onPropertyEnableChanged">
			</Switch>	
			<ToolbarSpacer/>
		 	<Button 
		 		visible="{= ${fieldPropertiesDetails>/singleValueUIDetails/editForm} !== true}"
		 		text="Edit" icon="sap-icon://edit"
		 		press=".SingleValueUIDetailsDialog.onPressEditPropertyCheckFlow" enabled="{= !${fieldPropertiesDetails>/lockedActions}.includes('E')}"/>
		 	<Button 
		 		visible="{= ${fieldPropertiesDetails>/singleValueUIDetails/editForm} === true}"
		 		text="Save" tooltip="Save UI Property" icon="sap-icon://filter-fields"
		 		press=".SingleValueUIDetailsDialog.onSaveUIProperty" enabled="{= !${fieldPropertiesDetails>/lockedActions}.includes('S')}"/>
		</Toolbar>
	</f:toolbar>
	<f:content>
	<VBox class="sapUiContentPadding" width="100%">
		<!-- Task 12487 - Add User Rule Name to Rules/Properties -->
		<VBox class="sapUiSmallMarginTop sapUiSmallMarginBottom">
			<Label text="Rule Name" labelFor="idSingleValueUIPropertyRuleName"/>
			<Input 
				id="idSingleValueUIPropertyRuleName" width="25%"
				enabled="{= ${fieldPropertiesDetails>/singleValueUIDetails/editForm} === true}"
				value="{fieldPropertiesDetails>/singleValueUIDetails/userRuleName}">
			</Input>
		</VBox>
		<HBox>
			<VBox class="sapUiSmallMarginEnd">
				<Label text="Attribute" labelFor="idSingleValueUIPropertyAttribute"/>
				<!-- Bug 12317 - Attribute field not populating after importing -->
				<ComboBox 
					id="idSingleValueUIPropertyAttribute" 
					enabled="{= ${fieldPropertiesDetails>/singleValueUIDetails/editForm} === true &amp;&amp; ${fieldPropertiesDetails>/singleValueUIDetails/editAttribute} !== false}"
					selectedKey="{fieldPropertiesDetails>/singleValueUIDetails/selectedAttributeName}"
					items="{ path: 'fieldPropertiesDetails>/singleValueUIDetails/attributeList', length: 2000, sorter: { path: 'UsmdAttribute' } }" 
					width="100%" change=".SingleValueUIDetailsDialog.onChangeAttribute">
					<c:ListItem key="{fieldPropertiesDetails>UsmdAttribute}"
						text="{fieldPropertiesDetails>UsmdAttribute} - {fieldPropertiesDetails>Txtlg}"
						additionalText="{fieldPropertiesDetails>Txtlg}"/>
				</ComboBox>
			</VBox>
			<VBox>
				<Label text="Check expression" labelFor="idSingleValueUIPropertyExpression"/>
				<HBox class="sapUiSmallMarginEnd">
					<!-- Task 10260 - Provide a better User interface for the SIngle value derivation (Check Expression) -->
					<ComboBox 
						id="idSingleValueUIPropertyExpression"
						class="sapUiSmallMarginEnd"
						selectedKey="{fieldPropertiesDetails>/singleValueUIDetails/checkExpressionType}"
						value="{fieldPropertiesDetails>/singleValueUIDetails/checkExpression}"
						valueState="{fieldPropertiesDetails>/singleValueUIDetails/checkExpressionValueState}"
						valueStateText="{fieldPropertiesDetails>/singleValueUIDetails/checkExpressionValueStateText}"
						enabled="{= ${fieldPropertiesDetails>/singleValueUIDetails/editForm} === true &amp;&amp; ${fieldPropertiesDetails>/singleValueUIDetails/selectedAttributeName} !== undefined}"
						change=".SingleValueUIDetailsDialog.onChangeCheckExpression">
						<items>
							<c:Item key="1" text="&lt;1&gt; or &lt;2&gt;"/>
							<c:Item key="2" text="&lt;1&gt; and &lt;2&gt;"/>
							<c:Item key="3" text="&lt;1&gt; or &lt;2&gt; or &lt;3&gt;"/>
							<c:Item key="4" text="(&lt;1&gt; and &lt;2&gt;) or &lt;3&gt;"/>
							<c:Item key="5" text="(&lt;1&gt; or &lt;2&gt;) and &lt;3&gt;"/>
							<c:Item key="6" text="&lt;1&gt; and &lt;2&gt; and &lt;3&gt;"/>
							<!-- Task 10260 - Provide a better User interface for the SIngle value derivation (Check Expression) -->
							<c:Item key="99" text="User-defined"/>
						</items>
					</ComboBox>
					<Button 
						id="idPropertyCheckGenerateFlow"
						enabled="{= ${fieldPropertiesDetails>/singleValueUIDetails/checkExpressionType} !== undefined}"
						visible="{= ${fieldPropertiesDetails>/singleValueUIDetails/editForm} === true}"
						icon="sap-icon://process" tooltip="Generate Flow" 
						press=".SingleValueUIDetailsDialog.onPressGeneratePropertyCheckFlow"/>
				</HBox>
			</VBox>
		</HBox>

		<VBox>
			<sc:ProcessFlow 
				id="idPropertyCheckProcessFlow" 
				showLabels="true" scrollable="false" foldedCorners="true" wheelZoomable="false" 
				nodePress=".SingleValueUIDetailsDialog.onNodePressPropertyCheckProcessFlow"
				nodes="{/nodes}" lanes="{/lanes}">
				<sc:nodes>
					<sc:ProcessFlowNode laneId="{lane}" nodeId="{id}" title="{title}" titleAbbreviation="{titleAbbreviation}"
						children="{path:'children', formatter:'.SingleValueUIDetailsDialog.formatConnectionLabels'}" 
						state="{state}" stateText="{stateText}" texts="{texts}"
						highlighted="{highlighted}" focused="{focused}" type="{type}"/>
				</sc:nodes>
				<sc:lanes>
					<sc:ProcessFlowLaneHeader laneId="{id}" iconSrc="{icon}" text="{label}" position="{position}"/>
				</sc:lanes>
			</sc:ProcessFlow>
		</VBox>
		<!-- <HBox>
			<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd"
			visible="{= ${fieldPropertiesDetails>/singleValueUIDetails/ElementType} === 'Q'}">
				<Label text="Dimension" labelFor="idUIPropertiesDimension"/>
				<ComboBox id="idUIPropertiesDimension" placeholder="Enter Dimension..." enabled="{= ${fieldPropertiesDetails>/singleValueUIDetails/editForm} === true}"
					selectedKey="{fieldPropertiesDetails>/singleValueUIDetails/selectedDimension}" 
					items="{ path: 'fieldPropertiesDetails>/singleValueUIDetails/dimensionsList', sorter: { path: 'Dimension' } }"
					selectionChange=".SingleValueUIDetailsDialog.onUIPropertiesDimensionChange">
					<c:ListItem key="{fieldPropertiesDetails>Dimension}"
						text="{= ${fieldPropertiesDetails>Dimension}.concat(${fieldPropertiesDetails>Text}==='' ? '' : ' - '.concat(${fieldPropertiesDetails>Text}))}" />
				</ComboBox>
			</VBox>
			
			<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd"
			visible="{= ${fieldPropertiesDetails>/singleValueUIDetails/ElementType} === 'Q' || ${fieldPropertiesDetails>/singleValueUIDetails/ElementType} === 'A'}">
				<Label text="Unit" labelFor="idUIPropertiesUnit"/>
				<ComboBox id="idUIPropertiesUnit" placeholder="Enter Unit..." enabled="{= ${fieldPropertiesDetails>/singleValueUIDetails/editForm} === true}"
					selectedKey="{fieldPropertiesDetails>/singleValueUIDetails/selectedUnit}" 
					items="{ path: 'fieldPropertiesDetails>/singleValueUIDetails/UnitsList', length: '500', sorter: { path: 'Unit' } }">
					<c:ListItem key="{fieldPropertiesDetails>Unit}"
						text="{= ${fieldPropertiesDetails>Unit}.concat(${fieldPropertiesDetails>Text}==='' ? '' : ' - '.concat(${fieldPropertiesDetails>Text}))}" />
				</ComboBox>
			</VBox>
		</HBox>	 -->
		<VBox class="sapUiSmallMarginTop sapUiSmallMarginBottom">
			<Label text="Workflow Step" labelFor="idUIPropertyWFStep"/>
			<Select 
				id="idUIPropertyWFStep" forceSelection="false"
				enabled="{= ${fieldPropertiesDetails>/singleValueUIDetails/editForm} === true}"
				selectedKey="{fieldPropertiesDetails>/singleValueUIDetails/wfStatus}"
				items="{ path: 'fieldPropertiesDetails>/singleValueUIDetails/wfStatusList'}">
				<c:ListItem key="{fieldPropertiesDetails>UsmdCreqStep}"
					text="{= ${fieldPropertiesDetails>UsmdCreqStep}.concat(${fieldPropertiesDetails>Txtmi}==='' ? '' : ' - '.concat(${fieldPropertiesDetails>Txtmi}))}" />
			</Select>
		</VBox>
		<HBox wrap="Wrap" alignContent="SpaceBetween" fitContainer="true">
			<VBox class="sapUiSmallMarginEnd">
				<Label text="UI Property" labelFor="idUIPropertyStatus" required="true"/>
				<Select 
						id="idUIPropertyStatus" forceSelection="false"
						enabled="{= ${fieldPropertiesDetails>/singleValueUIDetails/editForm} === true}"
						selectedKey="{fieldPropertiesDetails>/singleValueUIDetails/statusUIProperty}"
						items="{ path: 'fieldPropertiesDetails>/singleValueUIDetails/statusList'}">
						<c:ListItem key="{fieldPropertiesDetails>statusKey}"
							text="{fieldPropertiesDetails>statusKey} - {fieldPropertiesDetails>statusText}"
							additionalText="{fieldPropertiesDetails>statusText}"/>
				</Select>
			</VBox>
		</HBox>		
	</VBox>
	</f:content>
	</f:SimpleForm>

</c:FragmentDefinition>