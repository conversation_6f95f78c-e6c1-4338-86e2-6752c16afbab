<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:customdata="sap.ui.core.CustomData"
	xmlns:c="sap.ui.core" 
	xmlns:m="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true">
	<m:Dialog id="dialogSelectEntities" busyIndicatorDelay="0" title="Select Entities">
		<m:List
            id="EntitiesList"
            mode="MultiSelect"
            includeItemInSelection="true"
            items="{path:'FieldPropertyConfigModel>/entByStepList'}">
            <m:headerToolbar>
                <m:OverflowToolbar>
                    <m:content>
                        <m:SearchField id="selEntity" width="100%" showSearchButton="false" placeholder="Select Entities" ariaDescribedBy="Select Entities"
							ariaLabelledBy="selEntity" liveChange=".onSearchEntityList"/>
                    </m:content>
                </m:OverflowToolbar>
            </m:headerToolbar>
            <m:StandardListItem
                title="{FieldPropertyConfigModel>UsmdEntity}"
                description="{FieldPropertyConfigModel>Txtlg}" />
        </m:List>
		<m:beginButton>
			<m:Button 
				text="Save" press=".onSelectEntitiesSave" enabled="{= ${viewInterface>/editBusSystems/Bussystem} !== ''}" id="idSaveSelectEntities"/>
		</m:beginButton>
		<m:endButton>
			<m:Button text="Cancel" press=".onSelectEntitiesCancel" id="idCancelSelectEntities"/>
		</m:endButton>
	</m:Dialog>
</c:FragmentDefinition>