<mvc:View 
	xmlns:c="sap.ui.core" 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.UIProperties.UIProperties" 
	xmlns:html="http://www.w3.org/1999/xhtml">
	<Page title="Field Properties Configuration" titleLevel="H1" showNavButton="true" navButtonPress="onBackPress" class="sapUiContentPadding">
		<headerContent>
			<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.shared.MainMenu"/>
		</headerContent>
		<content>
			<Bar design="SubHeader">
				<contentLeft class="transparentBar">
					<Label text="Change Request Type: " labelFor="idChangeRequestComboBox" class="sapUiTinyMarginEnd"/>
					<ComboBox 
						id="idChangeRequestComboBox"
						items="{path:'fieldPropertiesModel>/changeRequestList'}"  
						selectedKey="{fieldPropertiesModel>/selectedCRType}"
						change=".onSelectCRType">
						<!--changeReqs-->
						<c:ListItem
							additionalText="{fieldPropertiesModel>UsmdModel}"
							key="{fieldPropertiesModel>UsmdCreqType}" 
							text="{fieldPropertiesModel>UsmdCreqType} - {fieldPropertiesModel>Txtmi}"/>
					</ComboBox>
					<Label text="Select Rule Type: " labelFor="idRuleTypeSelect" class="sapUiTinyMarginEnd"/>
					<Select 
						enabled="{= !(${fieldPropertiesModel>/selectedCRType_})}"
						id="idRuleTypeSelect"
						selectedKey="ByEntity"
						change=".onSelectRuleType"
						forceSelection="true">
						<!--changeReqs-->
						<c:ListItem
							key="ByEntity" 
							text="By Entity"/>
						<c:ListItem
							key="ByCRStepType" 
							text="By CR Step Type"/>
					</Select>
					<!--enabled="{= !(${fieldPropertiesModel>/selectedCRType})}"-->
				</contentLeft>
			</Bar>
			<l:Splitter id="splitterByEntity" height="95%" resize="onResizeScreen">
				<l:Splitter>
					<VBox id="idFieldPropertiesEntitiesList">
						<layoutData>
							<l:SplitterLayoutData size="50%" resizable="false"/>
						</layoutData>
						<!-- Entity List is inserted here by the controller -->
					</VBox>
					<VBox  id="idFieldPropertiesContainer">
						<layoutData>
							<l:SplitterLayoutData size="50%" resizable="false"/>
						</layoutData>
						<!-- Existing UI Properties List is inserted here by the controller -->
					</VBox>
				</l:Splitter>
				<VBox id="idFieldPropertiesDetails">
					<layoutData>
						<l:SplitterLayoutData size="70%" resizable="false"/>
					</layoutData>
					<items>
						<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.UIProperties.UIPropertiesDetails"/>
					</items>
				</VBox>
			</l:Splitter>
			<l:Splitter id="splitterByCRStep" height="95%" resize="onResizeScreen">
				<l:Splitter>
					<VBox id="idStepsList">
						<layoutData>
							<l:SplitterLayoutData size="100%" resizable="false"/>
						</layoutData>
						<!-- Entity List is inserted here by the controller -->
					</VBox>
				</l:Splitter>
				<VBox id="idStepDetails">
					<layoutData>
						<l:SplitterLayoutData size="80%" resizable="false"/>
					</layoutData>
					<items>
						<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.UIProperties.FieldPropertyConfig"/>
					</items>
				</VBox>
			</l:Splitter>
		</content>
	</Page>
</mvc:View>