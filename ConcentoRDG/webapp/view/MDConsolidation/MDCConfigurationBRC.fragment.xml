<c:FragmentDefinition
	xmlns="sap.m" 
    xmlns:mvc="sap.ui.core.mvc"
	xmlns:c="sap.ui.core" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.f"
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true">

    <Dialog title="Manage Configurations">
        <IconTabBar id="MainTabBarBRC" class="sapUiResponsiveContentPadding" selectedKey="orderBRC" expandable="false" select=".MDCConfigurationBRC.onTabChange">
            <items>
                <IconTabFilter text="Specify Order of Rules for Best Record Calculation" key="orderBRC">
                    <Label class="requiredInfo sapUiSmallMarginEnd sapUiSmallMarginTop" id="idInfoRequired" text="* Denotes Required Field"/>

                    <GenericTag text="Configuration" status="None" design="StatusIconHidden" class="sapUiSmallMarginTop sapUiSmallMarginBottom sapUiSmallMarginBegin"/>
                    <l:Grid containerQuery="true" defaultSpan="XL12 L12 M12 S12">
                        <HBox>
                            <VBox class="sapUiSmallMarginEnd" width="30%">
                                <Label labelFor="ConfigIdInput" required="true" text="ID"/>
                                <Input id="ConfigIdInput" width="100%" editable="{= ${mdconsolidationModel>/configData/OperationType} === 'C' ? true : false }" 
                                    maxLength="10"
                                    liveChange=".MDCConfigurationBRC.onLiveChangeConfiguration"
                                    change=".MDCConfigurationBRC.onLiveChangeConfiguration"
                                    valueLiveUpdate="true"
                                    value="{mdconsolidationModel>/configData/ConfigId}"
                                    valueState="{mdconsolidationModel>/configData/ConfigIdValueState}"
                                    valueStateText="{mdconsolidationModel>/configData/ConfigIdValueStateText}"/>
                            </VBox>
                            <VBox class="sapUiSmallMarginEnd" width="30%">
                                <Label labelFor="DescriptionInput" required="true" text="Description"/>
                                <Input id="DescriptionInput" width="100%" editable="true"
                                    liveChange=".MDCConfigurationBRC.onLiveChangeDescription"
                                    change=".MDCConfigurationBRC.onLiveChangeDescription"
                                    valueLiveUpdate="true"
                                    value="{mdconsolidationModel>/configData/Description}"
                                    valueState="{mdconsolidationModel>/configData/DescriptionValueState}"
                                    valueStateText="{mdconsolidationModel>/configData/DescriptionValueStateText}"/>
                            </VBox>
                        </HBox>
                        <HBox>
                            <VBox class="sapUiSmallMarginEnd" width="30%">
                                <Label labelFor="Parallel" required="false" text="Number of Parallel Processes"/>
                                <Input id="Parallel" width="100%" editable="true" value="{mdconsolidationModel>/configData/Parallel}" change=".MDCConfigurationBRC.onChangeParallel" 
                                liveChange=".MDCConfigurationBRC.onChangeParallel" valueState="{mdconsolidationModel>/configData/ParallelValueState}" 
                                valueStateText="{mdconsolidationModel>/configData/ParallelValueStateText}"/>
                            </VBox>
                            <VBox class="sapUiSmallMarginEnd" width="30%">
                                <Label labelFor="QueuePrefix" required="false" text="Queue Prefix"/>
                                <Input id="QueuePrefix" width="100%" editable="true" value="{mdconsolidationModel>/configData/QueuePrefix}"/>
                            </VBox>
                        </HBox>
                        <IconTabBar id="iconTabBarBRCOrderRules" class="sapUiResponsiveContentPadding" selectedKey="OSS" expandable="false">
                            <items>
                                <IconTabFilter text="Order of Source Systems" key="OSS">
                                    <l:Grid containerQuery="true" defaultSpan="XL12 L12 M12 S12">
                                        <Table id="idOSSTable"
                                            inset="false"
                                            sticky="HeaderToolbar,ColumnHeaders"
                                            width="100%"
                                            items="{mdconsolidationModel>/configData/BRCORDER2BRCSOURCESNAV}"
                                            mode="Delete"
                                            delete=".MDCConfigurationBRC.deleteOSSRow">
                                            <headerToolbar>
                                                <Toolbar>
                                                    <Label text="Order of Source Systems"/>
                                                    <ToolbarSpacer/>
                                                    <Button icon="sap-icon://add" text="ADD" id="addSourceSystems" press=".MDCConfigurationBRC.onAddSourceSystems"/>
                                                </Toolbar>
                                            </headerToolbar>
                                            <columns>
                                                <Column width="10%" hAlign="Center">
                                                    <Label design="Bold" text="Seq. No" wrapping="true" />
                                                </Column>
                                                <Column width="20%">
                                                    <Label design="Bold" text="Source System" required="true" wrapping="true" />
                                                </Column>
                                                <Column width="20%">
                                                    <Label design="Bold" text="Table" required="true" wrapping="true" />
                                                </Column>
                                                <Column width="50%">
                                                    <Label design="Bold" text="Description" wrapping="true" />
                                                </Column>
                                            </columns>
                                            <items>
                                                <ColumnListItem>
                                                    <cells>
                                                        <Text id="stepNoOSSText" text="{mdconsolidationModel>SeqNo}" width="100%"/>
                                                        <Input id="sourceSystemOSSInput" width="100%" editable="true" value="{mdconsolidationModel>SourceSystem}" liveChange=".MDCConfigurationBRC.onLiveChangeSourceSystemOSSInput"
                                                        change=".MDCConfigurationBRC.onLiveChangeSourceSystemOSSInput" valueState="{mdconsolidationModel>SourceSystemSelectValueState}" 
                                                        valueStateText="{mdconsolidationModel>SourceSystemSelectStateText}"/>
                                                        <Select id="tableOSSSelect" width="100%" editable="true" forceSelection="false" items="{path: 'mdconsolidationModel>/configData/BRCORDERTABLE', templateShareable:true}"
                                                        valueState="{mdconsolidationModel>TableSelectValueState}" valueStateText="{mdconsolidationModel>TableSelectValueStateText}" selectedKey="{mdconsolidationModel>Table}"
                                                        change=".MDCConfigurationBRC.onChangeBRCTable">
                                                            <c:Item key="{mdconsolidationModel>TableName}" text="{mdconsolidationModel>TableName}"/>
                                                        </Select>
                                                        <Input id="descriptionOSSInput" value="{mdconsolidationModel>TableDescription}" width="100%" editable="false"/>
                                                    </cells>
                                                </ColumnListItem>
                                            </items>
                                        </Table>
                                    </l:Grid>
                                </IconTabFilter>
                                <IconTabFilter text="Order of Rules for Tables" key="ORT">
                                    <l:Grid containerQuery="true" defaultSpan="XL12 L12 M12 S12">
                                        <Table id="idORTTable"
                                            inset="false"
                                            sticky="HeaderToolbar,ColumnHeaders"
                                            items="{mdconsolidationModel>/configData/BRCORDER2BRCTABLESNAV}"
                                            width="100%"
                                            mode="Delete"
                                            delete=".MDCConfigurationBRC.deleteORTRow">
                                            <headerToolbar>
                                                <Toolbar>
                                                    <Label text="Order of Rules for Tables"/>
                                                    <ToolbarSpacer/>
                                                    <Button icon="sap-icon://add" text="ADD" id="addRulesforTables" press=".MDCConfigurationBRC.onAddRulesforTables"/>
                                                </Toolbar>
                                            </headerToolbar>
                                            <columns>
                                                <Column width="10%" hAlign="Center">
                                                    <Label design="Bold" text="Seq. No" wrapping="true" />
                                                </Column>
                                                <Column width="20%">
                                                    <Label design="Bold" text="Table" required="true" wrapping="true" />
                                                </Column>
                                                <Column width="20%">
                                                    <Label design="Bold" text="Description" wrapping="true" />
                                                </Column>
                                                <Column width="50%">
                                                    <Label design="Bold" text="Rule ID" required="true" wrapping="true" />
                                                </Column>
                                            </columns>
                                            <items>
                                                <ColumnListItem>
                                                    <cells>
                                                        <Text id="stepNoORTText" text="{mdconsolidationModel>SeqNo}" width="100%"/>
                                                        <Select id="tableORTSelect" width="100%" editable="true" forceSelection="false" items="{path: 'mdconsolidationModel>/configData/BRCORDERTABLE', templateShareable:true}"
                                                        valueState="{mdconsolidationModel>TableSelectValueState}" valueStateText="{mdconsolidationModel>TableSelectValueStateText}" selectedKey="{mdconsolidationModel>Table}"
                                                        change=".MDCConfigurationBRC.onChangeBRCTable">
                                                            <c:Item key="{mdconsolidationModel>TableName}" text="{mdconsolidationModel>TableName}"/>
                                                        </Select>
                                                        <Input id="descriptionORTInput" value="{mdconsolidationModel>TableDescription}" width="100%" editable="false"/>
                                                        <Select id="ruleORTInput" width="100%" editable="true" forceSelection="false" items="{path: 'mdconsolidationModel>/configData/BRCTableRules', templateShareable:true}"
                                                        valueState="{mdconsolidationModel>RuleIdValueState}" valueStateText="{mdconsolidationModel>RuleIdValueStateText}" selectedKey="{mdconsolidationModel>RuleId}" 
                                                        change=".MDCConfigurationBRC.onChangeRuleID">
                                                            <c:Item key="{mdconsolidationModel>RuleId}" text="{mdconsolidationModel>RuleId}"/>
                                                        </Select>
                                                    </cells>
                                                </ColumnListItem>
                                            </items>
                                        </Table>
                                    </l:Grid>
                                </IconTabFilter>
                                <IconTabFilter text="Order of Rules for Fields" key="ORF">
                                    <l:Grid containerQuery="true" defaultSpan="XL12 L12 M12 S12">
                                        <Table id="idORFTable"
                                                inset="false"
                                                sticky="HeaderToolbar,ColumnHeaders"
                                                items="{mdconsolidationModel>/configData/BRCORDER2BRCFIELDSNAV}"
                                                width="100%"
                                                mode="Delete"
                                                delete=".MDCConfigurationBRC.deleteORFRow">
                                            <headerToolbar>
                                                <Toolbar>
                                                    <Label text="Order of Rules for Fields"/>
                                                    <ToolbarSpacer/>
                                                    <Button icon="sap-icon://add" text="ADD" id="addRulesforFields" press=".MDCConfigurationBRC.onAddRulesforFields"/>
                                                </Toolbar>
                                            </headerToolbar>
                                            <columns>
                                                <Column width="8%" hAlign="Center">
                                                    <Label design="Bold" text="Seq. No" wrapping="true" />
                                                </Column>
                                                <Column width="15%">
                                                    <Label design="Bold" text="Table" required="true" wrapping="true" />
                                                </Column>
                                                <Column width="15%">
                                                    <Label design="Bold" text="Description" wrapping="true" />
                                                </Column>
                                                <Column width="15%">
                                                    <Label design="Bold" text="Fields" required="true" wrapping="true" />
                                                </Column>
                                                <Column width="15%">
                                                    <Label design="Bold" text="Description" wrapping="true" />
                                                </Column>
                                                <Column width="32%">
                                                    <Label design="Bold" text="Rule ID" required="true" wrapping="true" />
                                                </Column>
                                            </columns>
                                            <items>
                                                <ColumnListItem>
                                                    <cells>
                                                        <Text id="stepNoORFText" text="{mdconsolidationModel>SeqNo}" width="100%"/>
                                                        <Select id="tableORFSelect" width="100%" editable="true" forceSelection="false" items="{path: 'mdconsolidationModel>/configData/BRCORDERTABLEFIELDS', templateShareable:true}"
                                                        valueState="{mdconsolidationModel>TableSelectValueState}" valueStateText="{mdconsolidationModel>TableSelectValueStateText}" selectedKey="{mdconsolidationModel>Table}" 
                                                        change=".MDCConfigurationBRC.onChangeBRCTable">
                                                            <c:Item key="{mdconsolidationModel>TableName}" text="{mdconsolidationModel>TableName}"/>
                                                        </Select>
                                                        <Input id="tableDescriptionORFInput" value="{mdconsolidationModel>TableDescription}" width="100%" editable="false"/>

                                                        <Select id="FieldsORFSelect" width="100%" editable="true" forceSelection="false" items="{path: 'mdconsolidationModel>BRCORDERFIELD', templateShareable:true}"
                                                        valueState="{mdconsolidationModel>FieldNameValueState}" valueStateText="{mdconsolidationModel>FieldNameValueStateText}" selectedKey="{mdconsolidationModel>Field}"
                                                        change=".MDCConfigurationBRC.onChangeBRCField">
                                                            <c:Item key="{mdconsolidationModel>Fieldname}" text="{mdconsolidationModel>Fieldname}"/>
                                                        </Select>

                                                        <Input id="fieldsDescriptionORFInput" value="{mdconsolidationModel>FieldDescription}" width="100%" editable="false"/>

                                                        <Select id="ruleORFSelect" width="100%" editable="true" forceSelection="false" items="{path: 'mdconsolidationModel>/configData/BRCFieldRules', templateShareable:true}"
                                                        valueState="{mdconsolidationModel>RuleIdValueState}" valueStateText="{mdconsolidationModel>RuleIdValueStateText}" selectedKey="{mdconsolidationModel>RuleId}" 
                                                        change=".MDCConfigurationBRC.onChangeRuleID">
                                                            <c:Item key="{mdconsolidationModel>RuleId}" text="{mdconsolidationModel>RuleId}"/>
                                                        </Select>
                                                    </cells>
                                                </ColumnListItem>
                                            </items>
                                        </Table>
                                    </l:Grid>
                                </IconTabFilter>
                            </items>
                        </IconTabBar>
                    </l:Grid>
                </IconTabFilter>
                <IconTabFilter text="Specify Rules for Best Record Calculation" key="rulesBRC">
                    <l:Grid containerQuery="true" defaultSpan="XL12 L12 M12 S12">
                        <Table id="idRulesBRCTable"
                            inset="false"
                            sticky="HeaderToolbar,ColumnHeaders"
                            items="{mdconsolidationModel>/configData/BRCRules}"
                            width="100%"
                            mode="Delete"
                            delete=".MDCConfigurationBRC.deleteRulesBRCRow">
                            <headerToolbar>
                                <Toolbar>
                                    <Label text="Specify Rules for Best Record Calculation"/>
                                    <ToolbarSpacer/>
                                    <Button icon="sap-icon://add" text="ADD" id="addRulesforBRC" press=".MDCConfigurationBRC.onAddRulesforBRC"/>
                                </Toolbar>
                            </headerToolbar>
                            <columns>
                                <Column width="26%">
                                    <Label design="Bold" text="Rule ID" required="true" wrapping="true" />
                                </Column>
                                <Column width="26%">
                                    <Label design="Bold" text="Description" required="true" wrapping="true" />
                                </Column>
                                <Column width="28%">
                                    <Label design="Bold" text="Label" required="true" wrapping="true" />
                                </Column>
                                <Column width="10%" hAlign="Center">
                                    <Label design="Bold" text="Table" wrapping="true" />
                                </Column>
                                <Column width="10%" hAlign="Center">
                                    <Label design="Bold" text="Fields" wrapping="true" />
                                </Column>
                            </columns>
                            <items>
                                <ColumnListItem>
                                    <cells>
                                        <Input id="ruleRulesforBRCTableInput" value="{mdconsolidationModel>RuleId}" width="100%" editable="true" liveChange=".MDCConfigurationBRC.onLiveChangeRuleForBRCTableInput"
                                            change=".MDCConfigurationBRC.onLiveChangeRuleForBRCTableInput" valueState="{mdconsolidationModel>RuleIdValueState}" valueStateText="{mdconsolidationModel>RuleIdValueStateText}" 
                                            enabled="{mdconsolidationModel>ebRule}"/>
                                        <Input id="descriptionRulesforBRCTableInput" value="{mdconsolidationModel>Description}" width="100%" editable="true"  liveChange=".MDCConfigurationBRC.onLiveChangeDescriptionForBRCTableInput"
                                            change=".MDCConfigurationBRC.onLiveChangeDescriptionForBRCTableInput" valueState="{mdconsolidationModel>DescriptionValueState}" valueStateText="{mdconsolidationModel>DescriptionValueStateText}"
                                            enabled="{= ${mdconsolidationModel>mainRule} ? false : true}"/>
                                        <Input id="labelRulesforBRCTableInput" value="{mdconsolidationModel>Label}" width="100%" editable="true"  liveChange=".MDCConfigurationBRC.onLiveChangeLabelForBRCTableInput"
                                            change=".MDCConfigurationBRC.onLiveChangeLabelForBRCTableInput" valueState="{mdconsolidationModel>LabelValueState}" valueStateText="{mdconsolidationModel>LabelValueStateText}"
                                            enabled="{= ${mdconsolidationModel>mainRule} ? false : true}"/>
                                        <CheckBox id="checkboxRulesforBRCTable" selected="{mdconsolidationModel>ForTable}" editable="true" select=".MDCConfigurationBRC.onCheckboxSelect" enabled="{= ${mdconsolidationModel>mainRule} ? false : true}"/>
                                        <CheckBox id="checkboxRulesforBRCField" selected="{mdconsolidationModel>ForField}" editable="true" select=".MDCConfigurationBRC.onCheckboxSelect" enabled="{= ${mdconsolidationModel>mainRule} ? false : true}"/>
                                    </cells>
                                </ColumnListItem>
                            </items>
                        </Table>
                    </l:Grid>
                </IconTabFilter>
            </items>
        </IconTabBar>
	<MessageStrip
		type="Warning"
		text="Note: there are unsave changes."
		class="sapUiSmallMarginBeginEnd sapUiSmallMarginTopBottom" 
        visible="{mdconsolidationModel>/configData/visChangesText}"
        />
        <buttons>
            <Button id="BtnDelete" icon="sap-icon://delete" text="Delete Configuration" press=".MDCConfigurationBRC.onClickDelete" visible="{mdconsolidationModel>/configData/visDeleteButton}"/>
            <!-- Bug 13013 - change the text of the button to "save" -->
            <Button id="BtnSave" icon="sap-icon://save" text="{= ${mdconsolidationModel>/configData/OperationType} === 'C' ? 'Save' : 'Update' }" press=".MDCConfigurationBRC.onClickSave"/> 
            <Button id="BtnClose" icon="sap-icon://decline" text="Close" press=".MDCConfigurationBRC.onClickClose"/>
        </buttons>
	</Dialog>
</c:FragmentDefinition>