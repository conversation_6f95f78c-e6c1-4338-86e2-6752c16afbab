<c:FragmentDefinition
	xmlns:mvc="sap.ui.core.mvc"
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true"
>
	<Dialog title="Create Authorization Group">
		<l:VerticalLayout class="sapUiContentPadding" width="100%">
			<l:content>
				<VBox>
					<Label required="true" text="Authorization Group"/>
                    <Input value="{mdconsolidationModel>/AuthGroupFragment/AuthGroup}" liveChange="onLiveChangeAuthGroup" valueLiveUpdate="true"
                        valueState="{mdconsolidationModel>/AuthGroupFragment/authGroupState}" maxLength="10"
                        valueStateText="{mdconsolidationModel>/AuthGroupFragment/authGroupStateText}"/>
				</VBox>
				<VBox>
					<Label required="true" text="Description"></Label>
					<Input value="{mdconsolidationModel>/AuthGroupFragment/Description}" valueLiveUpdate="true" maxLength="40"
                        valueState="{= ${mdconsolidationModel>/AuthGroupFragment/Description} ? 'None' : 'Error' }" valueStateText="Description is mandatory"/>
				</VBox>
			</l:content>
		</l:VerticalLayout>
		
		<beginButton>
			<Button 
				text="Create" 
				press="onClickAuthGroupSave" id="BtnAuthGroupCreate"/>
		</beginButton>
		<endButton>
			<Button
				text="Cancel"
				press="onClickAuthGroupCancel" id="BtnAuthGroupCancel"/>
		</endButton>
	
	</Dialog>
</c:FragmentDefinition>