<c:FragmentDefinition
	xmlns="sap.m" 
    xmlns:mvc="sap.ui.core.mvc"
	xmlns:c="sap.ui.core" 
	xmlns:l="sap.ui.layout"
    xmlns:t="sap.ui.table"
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true">

    <Dialog title="Manage Configurations" contentWidth="1300px">
        <Label class="requiredInfo sapUiSmallMarginEnd sapUiSmallMarginTop" id="idInfoRequired" text="* Denotes Required Field"/>

        <GenericTag text="Configuration" status="None" design="StatusIconHidden" class="sapUiSmallMarginTop sapUiSmallMarginBottom sapUiSmallMarginBegin"/>
        <l:Grid containerQuery="true" defaultSpan="XL12 L12 M12 S12">
            <HBox>
                <VBox class="sapUiSmallMarginEnd" width="20%">
                    <Label labelFor="ConfigIdInput" required="true" text="ID"/>
                    <Input id="ConfigIdInput" width="100%" editable="{= ${mdconsolidationModel>/configData/OperationType} === 'C' ? true : false }" 
                        maxLength="10"
                        liveChange=".MDCConfigurationMTC.onLiveChangeConfiguration"
                        change=".MDCConfigurationMTC.onLiveChangeConfiguration"
                        placeholder="Add Configuration ID"
                        valueLiveUpdate="true"
                        value="{mdconsolidationModel>/configData/ConfigId}"
                        valueState="{mdconsolidationModel>/configData/ConfigIdValueState}"
                        valueStateText="{mdconsolidationModel>/configData/ConfigIdValueStateText}"/>
                </VBox>
                <VBox class="sapUiSmallMarginEnd" width="30%">
                    <Label labelFor="DescriptionInput" required="true" text="Description"/>
                    <Input id="DescriptionInput" width="100%" 
                        liveChange=".MDCConfigurationMTC.onLiveChangeDescription"
                        change=".MDCConfigurationMTC.onLiveChangeDescription"
                        placeholder="Add Description"
                        valueLiveUpdate="true"
                        value="{mdconsolidationModel>/configData/Description}"
                        valueState="{mdconsolidationModel>/configData/DescriptionValueState}"
                        valueStateText="{mdconsolidationModel>/configData/DescriptionValueStateText}"/>
                </VBox>
                <VBox class="sapUiSmallMarginEnd" width="20%">
                    <Label labelFor="boType" text="BO Type"/>
                    <Select id="boType" width="100%" editable="true" selectedKey="{mdconsolidationModel>/configData/BoType}" enabled="false">
                        <c:Item key="147" text="147 - Business Partner"/>
                        <c:Item key="1405" text="1405 - Business Partner Relationship"/>
                        <c:Item key="194" text="194 - Material"/>
                    </Select>
                </VBox>
            </HBox>
            <HBox>
                <VBox class="sapUiSmallMarginEnd" width="10%">
                    <Label labelFor="includeClassCheckbox" required="false" wrapping="true" text="Include Classification"/>
                    <CheckBox id="includeClassCheckbox" selected="{mdconsolidationModel>/configData/UseClassification}" editable="true" select=".MDCConfigurationMTC.onConfigurationChange"/>
                </VBox>
                <VBox class="sapUiSmallMarginEnd" width="10%">
                    <Label labelFor="duplicateDefaultCheckbox" required="false" wrapping="true" text="Duplicate Check Default"/>
                    <CheckBox id="duplicateDefaultCheckbox" selected="{mdconsolidationModel>/configData/DuplCheckDflt}" editable="true" select=".MDCConfigurationMTC.onConfigurationChange"/>
                </VBox>
                <VBox class="sapUiSmallMarginEnd" width="20%">
                    <Label labelFor="scoreSelect" required="true" text="Score Selection"/>
                    <Select id="scoreSelect" width="100%" editable="true" forceSelection="true" selectedKey="{mdconsolidationModel>/configData/ScoreSelection}" change=".MDCConfigurationMTC.onConfigurationChange" liveChange=".MDCConfigurationMTC.onConfigurationChange">
                        <c:Item key="HIGHESTSCORE" text="Highest Score"/>
                        <c:Item key="FIRSTRULE" text="First Rule"/>
                    </Select>
                </VBox>
                <VBox class="sapUiSmallMarginEnd" width="20%">
                    <Label labelFor="usageSelect" required="true" text="Usage"/>
                    <Select id="usageSelect" width="100%" editable="true" forceSelection="true" selectedKey="{mdconsolidationModel>/configData/MTCUsage}" change=".MDCConfigurationMTC.onConfigurationChange" liveChange=".MDCConfigurationMTC.onConfigurationChange">
                        <c:Item key="" text="Consolidation"/>
                        <c:Item key="1" text="Duplicate Check"/>
                        <c:Item key="2" text="Consolidation and Duplicate Check"/>

                    </Select>
                </VBox>
                <VBox class="sapUiSmallMarginEnd" width="20%">
                    <Label labelFor="approvalScoreInput" required="true" text="Approval Score"/>
                    <!-- Bug 13448: added value state -->
                    <StepInput id="approvalScoreInput" value="{mdconsolidationModel>/configData/ApprScore}" min="0.00" max="1.00" step="0.01" displayValuePrecision="2" width="100%" change=".MDCConfigurationMTC.onApprovalScoreChange" 
                    valueState="{mdconsolidationModel>/configData/ApprScoreValueState}"/> 
                </VBox>
            </HBox>

            <HBox>
                <Button class="sapUiSmallMarginEnd" id="BtnCancelConf" icon="sap-icon://decline" text="Reset Configuration Changes" visible="{mdconsolidationModel>/configData/VisCancelConf}" 
                press=".MDCConfigurationMTC.onCancelConfPress" /> 
            </HBox>


            <l:ResponsiveSplitter defaultPane="default" height="480px">
            <!-- Panel container of the tree table -->
                <l:PaneContainer>
                    <l:SplitPane requiredParentWidth="200" id="default">
                        <l:layoutData>
                            <l:SplitterLayoutData size="auto" resizable="false" />
                        </l:layoutData>
                        <Panel headerText="Rules" height="100%">
                            <t:TreeTable id="ruleTreeTable" width="100%" selectionMode="Single" selectionBehavior="RowOnly" rowSelectionChange=".MDCConfigurationMTC.onRowClick"
                                enableSelectAll="false" ariaLabelledBy="title" alternateRowColors="true" rows="{path:'mdconsolidationModel>/configData/children', parameters:{ arrayNames:['children']}}"
                                useGroupMode="false" visibleRowCountMode="Fixed">
                                <t:extension>
                                    <OverflowToolbar>
                                        <Button icon="sap-icon://add" text="Add Rule" id="addRuleButton" press=".MDCConfigurationMTC.onAddRulePress" enabled="{mdconsolidationModel>/configData/EbAddRuleBtn}"/>
                                        <ToolbarSpacer/>
                                        <Button icon="sap-icon://add" text="{mdconsolidationModel>/configData/AddBtnText}" id="addButton" press=".MDCConfigurationMTC.onAddPress" 
                                        enabled="{mdconsolidationModel>/configData/EbAddBtn}"/>
                                    </OverflowToolbar>
                                </t:extension>
                                <t:rowSettingsTemplate>
                                    <t:RowSettings highlight="{mdconsolidationModel>RowStatus}"/>
                                </t:rowSettingsTemplate>
                                <t:columns>
                                    <t:Column>
                                        <t:label>
                                            <HBox>
                                                <Label text="Rules" wrapping="true"/>
                                            </HBox>
                                        </t:label>
                                        <t:template>
                                            <Label text="{mdconsolidationModel>Name}" />
                                        </t:template>
                                    </t:Column>
                                </t:columns>
                            </t:TreeTable>
                        </Panel>
                    </l:SplitPane>
                <!-- Panel container of the forms -->
                    <l:PaneContainer>
                        <l:SplitPane requiredParentWidth="200">
                            <l:layoutData>
                                <l:SplitterLayoutData size="auto" />
                            </l:layoutData>
                            <Panel headerText="{mdconsolidationModel>/configData/PanelText}" height="100%" id="MTCFormPanel">
                                <VBox alignItems="End">
                                    <HBox>
                                        <Button class="sapUiSmallMarginEnd" icon="sap-icon://decline" text="{mdconsolidationModel>/configData/CancelRuleBtnText}" id="cancelRuleButton" press=".MDCConfigurationMTC.onCancelPress" visible="{mdconsolidationModel>/configData/VisCancelRuleBtn}"/>
                                        <Button class="sapUiSmallMarginEnd" icon="sap-icon://delete" text="Delete {mdconsolidationModel>/configData/PanelText}" id="deleteRuleButton" press=".MDCConfigurationMTC.onClickDeleteRule" visible="{mdconsolidationModel>/configData/VisDeleteRuleBtn}"/>
                                    </HBox>
                                </VBox>
                                <VBox id="MTCFormView">
                                    <html:div>
                                        <html:center>
                                        <html:br></html:br>
                                        <c:Icon src="sap-icon://hint" size="5em" color="blue" class="sapUiContentPadding"/>
                                        <html:br></html:br>
                                        <html:h4 style="font-size:1.4em; width:70%">
                                            Please select a Row from the treetable to view/edit the content.
                                        </html:h4>
                                        </html:center>
                                    </html:div>
                                </VBox>
                            </Panel>
                        </l:SplitPane>
                    </l:PaneContainer>
                </l:PaneContainer>
            </l:ResponsiveSplitter>
        </l:Grid>
        <buttons>
            <Button id="BtnDelete" icon="sap-icon://delete" text="Delete Configuration" press=".MDCConfigurationMTC.deleteConfiguration" visible="{= ${mdconsolidationModel>/configData/OperationType} !== 'C' }"/>
            <Button id="BtnSaveNew" icon="sap-icon://save" text="{= ${mdconsolidationModel>/configData/OperationType} === 'C' ? 'Save Configuration' : 'Save Changes'}" enabled="{mdconsolidationModel>/configData/EbSaveBtn}" press=".MDCConfigurationMTC.saveConfiguration" /> 
            <Button id="BtnClose" icon="sap-icon://decline" text="Close" press=".MDCConfigurationMTC.onClickClose"/>
        </buttons>
	</Dialog>
</c:FragmentDefinition>