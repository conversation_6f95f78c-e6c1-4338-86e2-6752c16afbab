<!-- Task 12350 - Display/Edit/Delete/Add Adapter service -->
<c:FragmentDefinition
	xmlns="sap.m" 
	xmlns:c="sap.ui.core" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:core="sap.ui.core"
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true">

	<Dialog title="Manage Adapters">
		<!-- Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog -->
		<Label class="requiredInfo sapUiSmallMarginEnd sapUiSmallMarginTop" id="idInfoRequired" text="* Denotes Required Field"/>

		<l:VerticalLayout class="sapUiContentPadding" width="100%">
			<l:content>
				<VBox>
					<Label required="true" text="BO Type"></Label>
					<Select id="BOType" width="100%" editable="false"
						items="{path:'mdconsolidationModel>/arrBoTypeList'}"
						selectedKey="{mdconsolidationModel>/MDCAdapterModel/BOType}">
						<core:Item key="{mdconsolidationModel>BoType}" text="{mdconsolidationModel>BoType} - {mdconsolidationModel>Description}"/>
					</Select>
				</VBox>
				<VBox>
					<Label required="true" text="Step Type"></Label>
					<Select id="StepType" width="100%" editable="false"
						items="{path:'mdconsolidationModel>/arrStepTypeList'}"
						selectedKey="{mdconsolidationModel>/MDCAdapterModel/StepType}">
						<core:Item key="{mdconsolidationModel>StepName}" text="{mdconsolidationModel>StepName} - {mdconsolidationModel>Description}"/>
					</Select>
				</VBox>
                <VBox>
					<Label required="true" text="Adapter"></Label>
					<Select id="Adapter" width="100%" editable="true"
						change=".MDCAdapter.onChangeAdapter"
						items="{path:'mdconsolidationModel>/MDCAdapterModel/AdapterList'}"
						selectedKey="{mdconsolidationModel>/MDCAdapterModel/Adapter}"
						valueState="{mdconsolidationModel>/MDCAdapterModel/AdapterValueState}"
						valueStateText="{mdconsolidationModel>/MDCAdapterModel/AdapterValueStateText}">
						<core:Item key="{mdconsolidationModel>Adapter}" text="{mdconsolidationModel>Adapter} - {mdconsolidationModel>Description}"/>
					</Select>
				</VBox>
                <VBox>
					<Label required="true" text="Description"></Label>
					<Input id="Description" width="100%" editable="true"
						liveChange=".MDCAdapter.onLiveChangeDescription"
						valueLiveUpdate="true"
					    value="{mdconsolidationModel>/MDCAdapterModel/Description}"
                        valueState="{mdconsolidationModel>/MDCAdapterModel/DescriptionValueState}"
                        valueStateText="{mdconsolidationModel>/MDCAdapterModel/DescriptionValueStateText}"/>
				</VBox>
				<!-- Bug 12404 - Remove fields based on version related properties -->
                <VBox visible="{= ${mdconsolidationModel>/ShowProcessUsage} === true}">
					<Label required="false" text="Usage"></Label>
					<Select id="Usage" width="100%" editable="true"
						items="{path:'mdconsolidationModel>/MDCAdapterModel/UsageList'}"
						selectedKey="{mdconsolidationModel>/MDCAdapterModel/Usage}">
						<core:Item key="{mdconsolidationModel>UsageName}" text="{mdconsolidationModel>Description}"/>
					</Select>
				</VBox>
			</l:content>
		</l:VerticalLayout>

		<buttons>
			<Button id="BtnDelete" icon="sap-icon://delete" text="Delete" press=".MDCAdapter.onClickDelete" visible="{= ${mdconsolidationModel>/MDCAdapterModel/OperationType} !== 'C' }"/>
			<Button id="BtnSave" icon="sap-icon://save" text="{= ${mdconsolidationModel>/MDCAdapterModel/OperationType} === 'C' ? 'Create' : 'Update' }" press=".MDCAdapter.onClickSave"/>
			<Button id="BtnClose" icon="sap-icon://decline" text="Close" press=".MDCAdapter.onClickClose"/>
        </buttons>
	</Dialog>

</c:FragmentDefinition>