<c:FragmentDefinition 
    xmlns="sap.m" 
	xmlns:c="sap.ui.core" 
	xmlns:l="sap.ui.layout"
    controllerName=""
	displayBlock="true">

	<Dialog title="Manage Configurations">
        <Label class="requiredInfo sapUiSmallMarginEnd sapUiSmallMarginTop" id="idInfoRequired" text="* Denotes Required Field"/>

        <GenericTag text="Configuration" status="None" design="StatusIconHidden" class="sapUiSmallMarginTop sapUiSmallMarginBottom sapUiSmallMarginBegin"/>
		<l:Grid containerQuery="true" defaultSpan="XL10 L10 M10 S10">
            <VBox>
                <Label labelFor="ConfigIdInput" required="true" text="ID"/>
                <Input id="ConfigIdInput" width="100%" editable="{= ${mdconsolidationModel>/configData/OperationType} === 'C' ? true : false }" 
                    maxLength="10"
                    liveChange=".MDCConfigurationEVA.onLiveChangeConfiguration"
                    valueLiveUpdate="true"
                    value="{mdconsolidationModel>/configData/ConfigId}"
                    valueState="{mdconsolidationModel>/configData/ConfigIdValueState}"
                    valueStateText="{mdconsolidationModel>/configData/ConfigIdValueStateText}"/>
            </VBox>
        </l:Grid>
		<l:Grid containerQuery="true" defaultSpan="XL10 L10 M10 S10">
            <VBox>
            <GenericTag text="Maintenance View MDC_CONFIG_EVA" status="None" design="StatusIconHidden" class="sapUiSmallMarginTop sapUiSmallMarginBottom"/>
                <VBox>
                    <Label labelFor="DescriptionInput" required="true" text="Description"/>
                    <Input id="DescriptionInput" width="100%" editable="true"
                        liveChange=".MDCConfigurationEVA.onLiveChangeDescription"
                        valueLiveUpdate="true"
                        value="{mdconsolidationModel>/configData/Description}"
                        valueState="{mdconsolidationModel>/configData/DescriptionValueState}"
                        valueStateText="{mdconsolidationModel>/configData/DescriptionValueStateText}"/>
                </VBox>
                <VBox class="sapUiSmallMarginTop">
                    <Label labelFor="ProcessNumberInput" text="No of Processes"/>
                    <Input id="ProcessNumberInput" width="100%" editable="true" type="Number"
                        value="{mdconsolidationModel>/configData/Parallel}"/>
                </VBox>
                <VBox class="sapUiSmallMarginTop">
                    <Label labelFor="PackageSizeInput" text="Package Size"/>
                    <Input id="PackageSizeInput" width="100%" editable="true" type="Number"
                        value="{mdconsolidationModel>/configData/PackageSize}"/>
                </VBox>
                <VBox class="sapUiSmallMarginTop">
                    <Label labelFor="QueuePrefixInput" text="Queue Prefix"/>
                    <Input id="QueuePrefixInput" width="100%" editable="true"
                        value="{mdconsolidationModel>/configData/QueuePrefix}"/>
                </VBox>
            </VBox>
        </l:Grid>
		<l:Grid containerQuery="true" defaultSpan="XL10 L10 M10 S10">
            <VBox>
            <GenericTag text="Evaluation Configuration" status="None" design="StatusIconHidden" class="sapUiSmallMarginTop sapUiSmallMarginBottom"/>
                <VBox>
                    <Label labelFor="ProvideScoreCheckbox" required="false" text="Provide Scores for Dimensions"/>
                    <CheckBox id="ProvideScoreCheckbox" textAlign="Left" editable="true"
                        select=".MDCConfigurationEVA.onSelectProvideScoreCheckbox"
                        selected="{mdconsolidationModel>/configData/ProvideScores}"/>
                </VBox>
            <GenericTag text="Previous Configuration" status="None" design="StatusIconHidden" class="sapUiSmallMarginTop sapUiSmallMarginBottom"/>
                <HBox>
                    <VBox>
                        <Label labelFor="DeleteResultsCheckbox" required="false" text="Delete Results"/>
                        <CheckBox id="DeleteResultsCheckbox" textAlign="Left" editable="true"
                            select=".MDCConfigurationEVA.onSelectDeleteResultsCheckbox"
                            selected="{mdconsolidationModel>/configData/DeleteResults}"/>

                        <Label labelFor="EvaluationResultsInput" text="Evaluation with Retained Results"/>
                        <Input id="EvaluationResultsInput" width="100%" editable="true" type="Number"
                            value="{mdconsolidationModel>/configData/EvaluationResults}"/>
                    </VBox>
                    <VBox class="sapUiSmallMarginBegin">
                        <Label labelFor="DeleteScoresCheckbox" required="false" text="Delete Scores"/>
                        <CheckBox id="DeleteScoresCheckbox" textAlign="Left" editable="true"
                            select=".MDCConfigurationEVA.onSelectDeleteScoresCheckbox"
                            selected="{mdconsolidationModel>/configData/DeleteScores}"/>

                        <Label labelFor="EvaluationScoresInput" text="Evaluation with Retained Scores"/>
                        <Input id="EvaluationScoresInput" width="100%" editable="true" type="Number"
                            value="{mdconsolidationModel>/configData/EvaluationScores}"/>
                    </VBox>
                </HBox>
            </VBox>
        </l:Grid>

        <buttons>
            <Button id="BtnDelete" icon="sap-icon://delete" text="Delete" press=".MDCConfigurationEVA.onClickDelete" visible="{= ${mdconsolidationModel>/configData/OperationType} !== 'C' }"/>
            <Button id="BtnSave" icon="sap-icon://save" text="{= ${mdconsolidationModel>/configData/OperationType} === 'C' ? 'Create' : 'Update' }" press=".MDCConfigurationEVA.onClickSave"/> 
            <Button id="BtnClose" icon="sap-icon://decline" text="Close" press=".MDCConfigurationEVA.onClickClose"/>
        </buttons>  
	</Dialog>

</c:FragmentDefinition>