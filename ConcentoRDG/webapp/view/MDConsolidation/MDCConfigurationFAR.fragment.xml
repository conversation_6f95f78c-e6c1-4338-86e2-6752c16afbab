<!-- Task 12351 - Display/Edit/Delete/Add Configurations -->
<c:FragmentDefinition 
    xmlns="sap.m" 
    xmlns:mvc="sap.ui.core.mvc"
	xmlns:c="sap.ui.core" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
    controllerName=""
	displayBlock="true">

	<Dialog title="Manage Configurations">
        <Label class="requiredInfo sapUiSmallMarginEnd sapUiSmallMarginTop" id="idInfoRequired" text="* Denotes Required Field"/>

        <GenericTag text="Configuration" status="None" design="StatusIconHidden" class="sapUiSmallMarginTop sapUiSmallMarginBottom sapUiSmallMarginBegin"/>
		<l:Grid containerQuery="true" defaultSpan="XL4 L3 M4 S4">
            <VBox>
                <Label labelFor="ConfigIdInput" required="true" text="ID"/>
                <Input id="ConfigIdInput" width="100%" editable="{= ${mdconsolidationModel>/configData/OperationType} === 'C' ? true : false }" 
                    maxLength="10"
                    liveChange=".MDCConfigurationFAR.onLiveChangeConfiguration"
                    valueLiveUpdate="true"
                    value="{mdconsolidationModel>/configData/ConfigId}"
                    valueState="{mdconsolidationModel>/configData/ConfigIdValueState}"
                    valueStateText="{mdconsolidationModel>/configData/ConfigIdValueStateText}"/>
            </VBox>
            <VBox>
                <Label labelFor="DescriptionInput" required="true" text="Description"/>
                <Input id="DescriptionInput" width="100%" editable="true"
                    liveChange=".MDCConfigurationFAR.onLiveChangeDescription"
                    valueLiveUpdate="true"
                    value="{mdconsolidationModel>/configData/Description}"
                    valueState="{mdconsolidationModel>/configData/DescValueState}"
                    valueStateText="{mdconsolidationModel>/configData/DescValueStateText}"/>
            </VBox>
        </l:Grid>
		<l:Grid containerQuery="true" defaultSpan="XL8 L6 M8 S8">
            <VBox>
                <Label labelFor="ProcessStepTypeSelect" required="true" text="Predecessor Process Step Type"/>
                <Select id="ProcessStepTypeSelect" width="100%" editable="true" forceSelection="false"
                    change=".MDCConfigurationFAR.onChangeProcessStepType"
                    selectedKey="{mdconsolidationModel>/configData/StepType}"
                    valueState="{mdconsolidationModel>/configData/PredecessorProcessValueState}"
                    valueStateText="{mdconsolidationModel>/configData/PredecessorProcessValueStateText}">
                        <c:Item key="MTC" text="Matching"/>
                        <c:Item key="BRC" text="Best Record Calculation"/>
                        <c:Item key="VAL" text="Validation"/>
                </Select>
            </VBox>
        </l:Grid>

        <GenericTag id="ProcessStepTypeTag" status="None" design="StatusIconHidden" class="sapUiSmallMarginTop sapUiSmallMarginBottom sapUiSmallMarginBegin sapUiSmallMarginEnd" 
                    visible="false"/>
		<VBox id="ProcessStepTypeView" visible="{= ${mdconsolidationModel>/configData/StepType} ? true : false}">
        </VBox>

        <buttons>
            <Button id="BtnDelete" icon="sap-icon://delete" text="Delete" press=".MDCConfigurationFAR.onClickDelete" visible="{= ${mdconsolidationModel>/configData/OperationType} !== 'C' }"/>
            <Button id="BtnSave" icon="sap-icon://save" text="{= ${mdconsolidationModel>/configData/OperationType} === 'C' ? 'Create' : 'Update' }" press=".MDCConfigurationFAR.onClickSave"/> 
            <Button id="BtnClose" icon="sap-icon://decline" text="Close" press=".MDCConfigurationFAR.onClickClose"/>
        </buttons>  
	</Dialog>

</c:FragmentDefinition>