<mvc:View xmlns:core="sap.ui.core" xmlns:mvc="sap.ui.core.mvc" xmlns="sap.m" xmlns:l="sap.ui.layout" xmlns:f="sap.ui.layout.form"
	xmlns:n="sap.suite.ui.commons.networkgraph" xmlns:layout="sap.suite.ui.commons.networkgraph.layout"
	xmlns:html="http://www.w3.org/1999/xhtml" controllerName="dmr.mdg.supernova.SupernovaFJ.controller.MDConsolidation.MDConsolidation" id="idMDConsolidation">
	<Page title="Manage Master Data Consolidation" titleLevel="H1" showNavButton="true" navButtonPress="onBackPress" class="sapUiContentPadding">
		<headerContent>
			<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.shared.MainMenu"/>
		</headerContent>
		<content>
			<core:ComponentContainer id="mdcTransportPackageSelectDialog" name="dmr.components.TransportPackage" async="true"
							componentCreated="onTransportPackageDialogCreated"/>
			<l:Splitter >
				<VBox id="layout1">
					<layoutData>
						<l:SplitterLayoutData size="20%" resizable="false"/>
					</layoutData>
					<core:ComponentContainer
						id="mdconsolidationSearchList"
						name="dmr.components.SearchList"
						async="false"
						componentCreated="onMDConsolidationSearchListCreated"/>
				</VBox>
				<VBox id="layout0" class="sapUiNoContentPadding">
					<Panel id="idMDConsolidationPanel" headerText="Process Template" height="auto" width="100%" expandable="true" expanded="false">
						<Label class="requiredInfo sapUiSmallMarginEnd" id="idInfoRequired" text="* Denotes Required Field"/>
						<GenericTag text="Process Template Details" status="None" design="StatusIconHidden" class="sapUiSmallMarginBottom"/>
						<l:Grid containerQuery="true" defaultSpan="XL6 L6 M6 S6">
                            <VBox>
								<Label text="Process Template" required="true" labelFor="processTemplate"/>
								<Input editable="{= ${mdconsolidationModel>/processTemplateType} === 'new' }" id="processTemplate" value="{mdconsolidationModel>/selectedProcessTemplate/ProcessTemplate}"
									liveChange="onLiveChangeProcessTemplate" valueLiveUpdate="true" maxLength="10" placeholder="Process Template Name"
									valueState="{mdconsolidationModel>/templateValueState}" valueStateText="{mdconsolidationModel>/templateStateText}"/>
							</VBox>	
							<VBox>
								<Label text="Process Template Description" required="true" labelFor="processTemplateDesc"/>
								<Input editable="{= ${mdconsolidationModel>/processTemplateType} === 'new' || ${mdconsolidationModel>/processTemplateType} === 'edit' }" id="processTemplateDesc" 
									liveChange="onLiveChangeDescription" value="{mdconsolidationModel>/selectedProcessTemplate/Description}" placeholder="Process Template Description"
									valueState="{mdconsolidationModel>/selectedProcessTemplate/DescriptionValueState}" valueStateText="{mdconsolidationModel>/selectedProcessTemplate/DescriptionValueStateText}"/>
							</VBox>
							<VBox>
								<Label text="BO Type" labelFor="boType" required="true"/>
								<Select forceSelection="false" items="{path:'mdconsolidationModel>/arrBoTypeList'}" id="boType"
									editable="{= ${mdconsolidationModel>/processTemplateType} === 'new' || ${mdconsolidationModel>/processTemplateType} === 'edit' }" change="onChangeBoType" width="100%"
									selectedKey="{mdconsolidationModel>/selectedProcessTemplate/BoType}" 
									valueState="{mdconsolidationModel>/selectedProcessTemplate/BoTypeValueState}" valueStateText="{mdconsolidationModel>/selectedProcessTemplate/BoTypeValueStateText}">
									<core:Item key="{mdconsolidationModel>BoType}" text="{mdconsolidationModel>BoType} - {mdconsolidationModel>Description}"/>
								</Select>
							</VBox>
							<VBox>
								<Label text="Process Goal" labelFor="processGoal" required="true"/>
								<Select forceSelection="false" items="{path:'mdconsolidationModel>/arrProcessGoalsList'}" id="processGoal" width="100%"
									editable="{= ${mdconsolidationModel>/processTemplateType} === 'new' || ${mdconsolidationModel>/processTemplateType} === 'edit' }"
									selectedKey="{mdconsolidationModel>/selectedProcessTemplate/ProcessGoal}" change="onChangeProcessGoal"
									valueState="{mdconsolidationModel>/selectedProcessTemplate/ProcessGoalValueState}" valueStateText="{mdconsolidationModel>/selectedProcessTemplate/ProcessGoalValueStateText}">
									<core:Item key="{mdconsolidationModel>Name}" text="{mdconsolidationModel>Name} - {mdconsolidationModel>Description}"/>
								</Select>
							</VBox>
							<HBox>
								<VBox class="sapUiLargeMarginEnd">
									<CheckBox id="defaultTemplate" selected="{mdconsolidationModel>/selectedProcessTemplate/DefaultTemplate}"
										text="Default Template" 
										editable="{= ( ${mdconsolidationModel>/processTemplateType} === 'new' || ${mdconsolidationModel>/processTemplateType} === 'edit' ) &amp;&amp; ${mdconsolidationModel>/oEnableProperties/DefaultTemplate}}"/>
								</VBox>
								<VBox class="sapUiLargeMarginBegin">
									<CheckBox id="deleteSource" selected="{mdconsolidationModel>/selectedProcessTemplate/DeleteSource}"
										text="Delete Source" 
										editable="{= ( ${mdconsolidationModel>/processTemplateType} === 'new' || ${mdconsolidationModel>/processTemplateType} === 'edit' ) &amp;&amp; ${mdconsolidationModel>/oEnableProperties/DeleteSource}}"/>
								</VBox>
							</HBox>
							<VBox>
								<!-- Bug 12364 - Required information for process templates -->
								<Label text="Strategy" labelFor="strategy" required="{= ${mdconsolidationModel>/selectedProcessTemplate/ProcessGoal} === 'A' ? true : false }" />
								<Select forceSelection="false" items="{path:'mdconsolidationModel>/arrStrategyList'}" id="strategy" width="100%"
									editable="{= ( ${mdconsolidationModel>/processTemplateType} === 'new' || ${mdconsolidationModel>/processTemplateType} === 'edit' ) &amp;&amp; ${mdconsolidationModel>/oEnableProperties/Strategy}}"
									selectedKey="{mdconsolidationModel>/selectedProcessTemplate/Strategy}" change="onChangeStrategy"
									valueState="{mdconsolidationModel>/selectedProcessTemplate/StrategyValueState}" valueStateText="{mdconsolidationModel>/selectedProcessTemplate/StrategyValueStateText}">
									<core:Item key="{mdconsolidationModel>StratName}" text="{mdconsolidationModel>StratName} - {mdconsolidationModel>Description}"/>
								</Select>
							</VBox>
							<HBox class="sapUiSmallMarginEnd">
								<ToolbarSpacer/>
								<Button class="sapUiSmallMarginEnd" tooltip="Save Process Template"
									visible="{= ${mdconsolidationModel>/processTemplateType} === 'new' || ${mdconsolidationModel>/processTemplateType} === 'edit' }"
									enabled="false"
									iconFirst="true" icon="sap-icon://save" text="Save" id="saveProcessTemplate" press="onSaveProcessTemplate"/>
								<Button class="sapUiSmallMarginEnd" tooltip="Delete Process Template"
									visible="{= ${mdconsolidationModel>/processTemplateType} === 'edit' }"
									enabled="{formatter: '.DeleteButtonEnabled', parts: ['mdconsolidationModel>/selectedProcessTemplate/ProcessTemplate']}"
									iconFirst="true" icon="sap-icon://delete" text="Delete" id="deleteProcessTemplate" press="onDeleteProcessTemplate"/>
							</HBox>
						</l:Grid>
						<!-- Bug 12496 - Process Goal B not allowed to have steps -->
						<VBox class="sapUiSmallMarginBegin sapUiSmallMarginBottom" visible="{= ${mdconsolidationModel>/selectedProcessTemplate/ProcessGoal} !== 'B' ? true : false }">
							<Table id="idProcessTemplateStepTable" alternateRowColors="true" contextualWidth="Auto" mode="SingleSelectMaster" autoPopinMode="true"
								items="{path: 'mdconsolidationModel>/selectedProcessTemplate/TEMPLATE2STEPNAV/results'}">
								<headerToolbar>
									<OverflowToolbar>
										<Label text="Process Template Step" />
										<ToolbarSpacer/>
										<Button icon="sap-icon://add" text="ADD" id="addProcessTemplateStep" press="onAddProcessTemplateStep" enabled="{mdconsolidationModel>/selectedProcessTemplate/ebAddBtn}"/>
										<Button icon="sap-icon://delete" text="DELETE" id="deleteProcessTemplateStep" press="onDeleteProcessTemplateStep" enabled="{mdconsolidationModel>/selectedProcessTemplate/ebDelBtn}"/>
									</OverflowToolbar>
								</headerToolbar>
								<columns>
									<Column importance="High" width="5%">
										<Label design="Bold" text="Step" required="true" wrapping="true"/>
									</Column>
									<Column importance="High" width="15%">
										<Label design="Bold" text="Step Type" required="true" wrapping="true"/>
									</Column>
									<Column importance="High" width="15%">
										<Label design="Bold" text="Description" required="true" wrapping="true"/>
									</Column>
									<!-- Task 12350 - Dispaly/Edit/Delete/Add Adapter service -->
									<Column importance="None" width="16%">
										<Label design="Bold" text="Adapter" wrapping="true"/>
									</Column>
									<Column importance="None" width="4%">
										<Label design="Bold" text="" wrapping="true"/>
									</Column>
									<!-- Task 12351 - Display/Edit/Delete/Add Configurations -->
									<Column importance="None" width="11%">
										<Label design="Bold" text="Config Id" wrapping="true"/>
									</Column>
									<Column importance="None" width="4%">
										<Label design="Bold" text="" wrapping="true"/>
									</Column>
									<Column importance="None" width="5%">
										<Label design="Bold" text="Check Pt" wrapping="true"/>
									</Column>
									<Column importance="None" width="10%">
										<Label design="Bold" text="Action Control" wrapping="true"/>
									</Column>
									<!-- Bug 12404 - Remove fields based on version related properties -->
									<Column importance="None" width="15%" visible="{= ${mdconsolidationModel>/ShowAuthGroup} === true}">
										<Label design="Bold" text="Authorization Group" wrapping="true"/>
									</Column>
								</columns>
								<items>
									<ColumnListItem>
										<cells>
											<Text id="stepNo" text="{mdconsolidationModel>StepNo}" width="100%"/>
											<Select id="stepType" forceSelection="false" items="{path:'mdconsolidationModel>/arrStepTypeList', templateShareable:true}"
												editable="{= ${mdconsolidationModel>/processTemplateType} === 'new' || ${mdconsolidationModel>/processTemplateType} === 'edit' }"
												valueState="{mdconsolidationModel>processTempTypeValueState}" valueStateText="{mdconsolidationModel>processTempTypeValueStateText}"
												selectedKey="{mdconsolidationModel>StepType}" change="onChangeStepType" width="100%">
												<core:Item key="{mdconsolidationModel>StepName}" text="{mdconsolidationModel>StepName} - {mdconsolidationModel>Description}"/>
											</Select>
											<Input id="stepDescription" value="{mdconsolidationModel>StepDescription}" valueState="{mdconsolidationModel>StepDescValueState}" 
													valueStateText="{mdconsolidationModel>StepDescValueStateText}" maxLength="30" width="100%" liveChange="onLiveChangeDescStep"/>
											<!-- change the force selection to true so the corresponding field is selected -->
											<!-- Bug 13446: change the force selection to false and added resetOnMissingKey to avoid selecting a wrong default value -->
											<Select forceSelection="false" items="{path:'mdconsolidationModel>arrAdapterList', templateShareable:true}" id="stepdapter"
												editable="{= ${mdconsolidationModel>/processTemplateType} === 'new' || ${mdconsolidationModel>/processTemplateType} === 'edit' }"
												selectedKey="{mdconsolidationModel>Adapter}" change="onChangeAdapter" width="100%" resetOnMissingKey="true">
												<core:Item key="{mdconsolidationModel>AdapterName}" text="{mdconsolidationModel>AdapterName} - {mdconsolidationModel>Description}"/>
											</Select>
											<!-- Task 12350 - Dispaly/Edit/Delete/Add Adapter service -->
											<Button icon="sap-icon://edit" text="" tooltip="View Details" id="stepAdapterInfo" press="onClickViewAdapterDetails" />
											<!-- change the force selection to true so the corresponding field is selected -->
											<!-- Bug 13216 - variable on enabled change to "ebConfig" -->
											<!-- Bug 13446: change the force selection to false and added resetOnMissingKey to avoid selecting a wrong default value -->
											<Select id="confidId" forceSelection="false" items="{path:'mdconsolidationModel>arrConfigList', templateShareable:true}"
												editable="{= ${mdconsolidationModel>/processTemplateType} === 'new' || ${mdconsolidationModel>/processTemplateType} === 'edit' }"
												enabled="{= ${mdconsolidationModel>Adapter} !== '' &amp;&amp; ${mdconsolidationModel>ebConfig}}"
												selectedKey="{mdconsolidationModel>ConfigId}" change="onChangeConfiguration" width="100%" resetOnMissingKey="true">
												<core:Item key="{mdconsolidationModel>Id}" text="{mdconsolidationModel>Id} - {mdconsolidationModel>Description}"/>
											</Select>
											<!-- Task 12351 - Display/Edit/Delete/Add Configurations -->
											<Button icon="sap-icon://edit" text="" tooltip="View Details" id="confidIdInfo" press="onClickViewConfigurationDetails" />
											<CheckBox id="checkPt" selected="{mdconsolidationModel>CheckPt}" editable="{= ${mdconsolidationModel>/processTemplateType} === 'new' || ${mdconsolidationModel>/processTemplateType} === 'edit' }"/>
											<!-- change the force selection to true so the corresponding field is selected -->
											<Select id="stepActionCtrl" forceSelection="true" items="{path:'mdconsolidationModel>/arrActionList', templateShareable:false}"
												editable="{= ${mdconsolidationModel>/processTemplateType} === 'new' || ${mdconsolidationModel>/processTemplateType} === 'edit' }"
												selectedKey="{mdconsolidationModel>ActionCtrl}" width="100%">
												<core:Item key="{mdconsolidationModel>Name}" text="{mdconsolidationModel>Name} - {mdconsolidationModel>Description}"/>
											</Select>
											<!-- Bug 12404 - Remove fields based on version related properties -->
											<!-- change the force selection to true so the corresponding field is selected -->
											<Select id="idAuthGroup" forceSelection="true" items="{path:'mdconsolidationModel>/arrAuthGroupList', templateShareable:false}"
												visible="{= ${mdconsolidationModel>/ShowAuthGroup} === true}"
												editable="{= ${mdconsolidationModel>/processTemplateType} === 'new' || ${mdconsolidationModel>/processTemplateType} === 'edit' }"
												selectedKey="{mdconsolidationModel>AuthGroup}" width="100%" change="onChangeAuthGroup">
												<core:Item key="{mdconsolidationModel>AuthName}" text="{mdconsolidationModel>AuthName} - {mdconsolidationModel>Description}"/>
											</Select>
										</cells>
									</ColumnListItem>
								</items>
							</Table>
						</VBox>
					</Panel>
				</VBox>
			</l:Splitter>
		</content>
	</Page>
</mvc:View>