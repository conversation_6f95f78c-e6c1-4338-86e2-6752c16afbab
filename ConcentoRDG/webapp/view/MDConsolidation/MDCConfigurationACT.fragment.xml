<!-- Task 12351 - Display/Edit/Delete/Add Configurations -->
<c:FragmentDefinition
	xmlns="sap.m" 
    xmlns:mvc="sap.ui.core.mvc"
	xmlns:c="sap.ui.core" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true">

	<Dialog title="Manage Configurations">
        <!-- Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog -->
        <Label class="requiredInfo sapUiSmallMarginEnd sapUiSmallMarginTop" id="idInfoRequired" text="* Denotes Required Field"/>

        <GenericTag text="Configuration" status="None" design="StatusIconHidden" class="sapUiSmallMarginTop sapUiSmallMarginBottom sapUiSmallMarginBegin"/>
		<l:Grid containerQuery="true" defaultSpan="XL4 L4 M4 S4">
            <VBox>
                <Label labelFor="ConfigId" required="true" text="ID"></Label>
                <Input id="ConfigId" width="100%" editable="{= ${mdconsolidationModel>/configData/OperationType} === 'C' ? true : false }" 
                    maxLength="10"
                    liveChange=".MDCConfigurationACT.onLiveChangeConfiguration"
					valueLiveUpdate="true"
                    value="{mdconsolidationModel>/configData/ConfigId}"
                    valueState="{mdconsolidationModel>/configData/ConfigIdValueState}"
                    valueStateText="{mdconsolidationModel>/configData/ConfigIdValueStateText}"/>
            </VBox>
            <VBox>
                <Label labelFor="Desc" required="true" text="Description"></Label>
                <Input id="Desc" width="100%" editable="true"
                    liveChange=".MDCConfigurationACT.onLiveChangeDescription"
					valueLiveUpdate="true"
                    value="{mdconsolidationModel>/configData/Desc}"
                    valueState="{mdconsolidationModel>/configData/DescValueState}"
                    valueStateText="{mdconsolidationModel>/configData/DescValueStateText}"/>
            </VBox>
        </l:Grid>

        <GenericTag text="Configurations for Activation" status="None" design="StatusIconHidden" class="sapUiSmallMarginBottom sapUiSmallMarginBegin"/>
		<l:Grid containerQuery="true" defaultSpan="XL4 L4 M4 S4">
            <VBox>
                <Label labelFor="ParallelProcs" required="false" text="Number of Parallel Processes"></Label>
                <Input id="ParallelProcs" width="100%" editable="true" value="{mdconsolidationModel>/configData/ParallelProcs}"/>
            </VBox>
            <VBox>
                <Label labelFor="QueuePrefix" required="false" text="Queue Prefix"></Label>
                <Input id="QueuePrefix" width="100%" editable="true" value="{mdconsolidationModel>/configData/QueuePrefix}"/>
            </VBox>
        </l:Grid>

        <GenericTag text="New Records" status="None" design="StatusIconHidden" class="sapUiSmallMarginBottom sapUiSmallMarginBegin"/>
		<l:Grid containerQuery="true" defaultSpan="XL4 L4 M4 S4">
            <VBox>
                <Label labelFor="NewTarget" required="false" text="Target for Correct Records"></Label>
                <Select id="NewTarget" width="100%" editable="true"
                    change=".MDCConfigurationACT.onChangeTarget"
                    items="{path:'mdconsolidationModel>/configData/NewTargetList'}"
                    selectedKey="{mdconsolidationModel>/configData/NewTarget}">
                    <c:Item key="{mdconsolidationModel>ID}" text="{mdconsolidationModel>Description}"/>
                </Select>
            </VBox>
            <VBox>
                <Label labelFor="NewCrType" required="{= ( ${mdconsolidationModel>/configData/NewTarget} === '2' || ${mdconsolidationModel>/configData/NewTarget} === '3' ) ? true : false }" text="CR Type"
               ></Label>
                <!-- Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog -->
                <Select id="NewCrType" width="100%" 
                    editable="{= ( ${mdconsolidationModel>/configData/NewTarget} === '2' || ${mdconsolidationModel>/configData/NewTarget} === '3' ) ? true : false }"
                    change=".MDCConfigurationACT.onChangeCrType"
                    items="{path:'mdconsolidationModel>/configData/NewCrTypeList'}"
                    selectedKey="{mdconsolidationModel>/configData/NewCrType}"
                    valueState="{mdconsolidationModel>/configData/NewCrTypeValueState}"
                   
                    valueStateText="{mdconsolidationModel>/configData/NewCrTypeValueStateText}">
                    <c:Item key="{mdconsolidationModel>CrType}" text="{mdconsolidationModel>CrType} - {mdconsolidationModel>CrDesc}"/>
                </Select>
            </VBox>
            <VBox>
                <Label labelFor="NewCrMaxNoRec" required="false" text="Max. Rec."></Label>
                <Input id="NewCrMaxNoRec" width="100%" 
                    value="{mdconsolidationModel>/configData/NewCrMaxNoRec}"
                   
                    editable="{= ( ${mdconsolidationModel>/configData/NewTarget} === '2' || ${mdconsolidationModel>/configData/NewTarget} === '3' ) ? true : false }"/>
            </VBox>
            <VBox>
                <Label labelFor="NewTargetErr" required="false" text="Target for Incorrect Records"></Label>
                <Select id="NewTargetErr" width="100%" editable="true"
                    change=".MDCConfigurationACT.onChangeTarget"
                    items="{path:'mdconsolidationModel>/configData/NewTargetErrList'}"
                    selectedKey="{mdconsolidationModel>/configData/NewTargetErr}"
                   >
                    <c:Item key="{mdconsolidationModel>ID}" text="{mdconsolidationModel>Description}"/>
                </Select>
            </VBox>
            <VBox>
                <Label labelFor="NewCrTypeErr" required="{= ( ${mdconsolidationModel>/configData/NewTargetErr} === '2' || ${mdconsolidationModel>/configData/NewTargetErr} === '3' ) ? true : false }" text="CR Type"
               ></Label>
                <!-- Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog -->
                <Select id="NewCrTypeErr" width="100%" 
                    editable="{= ( ${mdconsolidationModel>/configData/NewTargetErr} === '2' || ${mdconsolidationModel>/configData/NewTargetErr} === '3' ) ? true : false }"
                    change=".MDCConfigurationACT.onChangeCrType"
                    items="{path:'mdconsolidationModel>/configData/NewCrTypeErrList'}"
                   
                    selectedKey="{mdconsolidationModel>/configData/NewCrTypeErr}"
                    valueState="{mdconsolidationModel>/configData/NewCrTypeErrValueState}"
                    valueStateText="{mdconsolidationModel>/configData/NewCrTypeErrValueStateText}">
                    <c:Item key="{mdconsolidationModel>CrType}" text="{mdconsolidationModel>CrType} - {mdconsolidationModel>CrDesc}"/>
                </Select>
            </VBox>
            <VBox>
                <Label labelFor="NewCrMaxNoRecErr" required="false" text="Max. Rec."></Label>
                <Input id="NewCrMaxNoRecErr" width="100%"
                    value="{mdconsolidationModel>/configData/NewCrMaxNoRecErr}"
                    editable="{= ( ${mdconsolidationModel>/configData/NewTargetErr} === '2' || ${mdconsolidationModel>/configData/NewTargetErr} === '3' ) ? true : false }"/>
            </VBox>
        </l:Grid>

        <GenericTag text="Updated Records" status="None" design="StatusIconHidden" class="sapUiSmallMarginBottom sapUiSmallMarginBegin"/>
		<l:Grid containerQuery="true" defaultSpan="XL4 L4 M4 S4">
            <VBox>
                <Label labelFor="UpdTarget" required="false" text="Target for Correct Records"></Label>
                <Select id="UpdTarget" width="100%" editable="true"
                    change=".MDCConfigurationACT.onChangeTarget"
                    items="{path:'mdconsolidationModel>/configData/UpdTargetList'}"
                    selectedKey="{mdconsolidationModel>/configData/UpdTarget}">
                    <c:Item key="{mdconsolidationModel>ID}" text="{mdconsolidationModel>Description}"/>
                </Select>
            </VBox>
            <VBox>
                <Label labelFor="UpdCrType" required="{= ( ${mdconsolidationModel>/configData/UpdTarget} === '2' || ${mdconsolidationModel>/configData/UpdTarget} === '3' ) ? true : false }" text="CR Type"
               ></Label>
                <!-- Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog -->
                <Select id="UpdCrType" width="100%" 
                    editable="{= ( ${mdconsolidationModel>/configData/UpdTarget} === '2' || ${mdconsolidationModel>/configData/UpdTarget} === '3' ) ? true : false }"
                    change=".MDCConfigurationACT.onChangeCrType"
                    items="{path:'mdconsolidationModel>/configData/UpdCrTypeList'}"
                    selectedKey="{mdconsolidationModel>/configData/UpdCrType}"
                    valueState="{mdconsolidationModel>/configData/UpdCrTypeValueState}"
                    valueStateText="{mdconsolidationModel>/configData/UpdCrTypeValueStateText}">
                    <c:Item key="{mdconsolidationModel>CrType}" text="{mdconsolidationModel>CrType} - {mdconsolidationModel>CrDesc}"/>
                </Select>
            </VBox>
            <VBox>
                <Label labelFor="UpdCrMaxNoRec" required="false" text="Max. Rec."></Label>
                <Input id="UpdCrMaxNoRec" width="100%" 
                    value="{mdconsolidationModel>/configData/UpdCrMaxNoRec}"
                    editable="{= ( ${mdconsolidationModel>/configData/UpdTarget} === '2' || ${mdconsolidationModel>/configData/UpdTarget} === '3' ) ? true : false }"/>
            </VBox>
            <VBox>
                <Label labelFor="UpdTargetErr" required="false" text="Target for Incorrect Records"></Label>
                <Select id="UpdTargetErr" width="100%" editable="true"
                    change=".MDCConfigurationACT.onChangeTarget"
                    items="{path:'mdconsolidationModel>/configData/UpdTargetErrList'}"
                    selectedKey="{mdconsolidationModel>/configData/UpdTargetErr}">
                    <c:Item key="{mdconsolidationModel>ID}" text="{mdconsolidationModel>Description}"/>
                </Select>
            </VBox>
            <VBox>
                <Label labelFor="UpdCrTypeErr" required="{= ( ${mdconsolidationModel>/configData/UpdTargetErr} === '2' || ${mdconsolidationModel>/configData/UpdTargetErr} === '3' ) ? true : false }" 
                text="CR Type"></Label>
                <!-- Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog -->
                <Select id="UpdCrTypeErr" width="100%" 
                    editable="{= ( ${mdconsolidationModel>/configData/UpdTargetErr} === '2' || ${mdconsolidationModel>/configData/UpdTargetErr} === '3' ) ? true : false }"
                    change=".MDCConfigurationACT.onChangeCrType"
                    items="{path:'mdconsolidationModel>/configData/UpdCrTypeErrList'}"
                    selectedKey="{mdconsolidationModel>/configData/UpdCrTypeErr}"
                    valueState="{mdconsolidationModel>/configData/UpdCrTypeErrValueState}"
                    valueStateText="{mdconsolidationModel>/configData/UpdCrTypeErrValueStateText}">
                    <c:Item key="{mdconsolidationModel>CrType}" text="{mdconsolidationModel>CrType} - {mdconsolidationModel>CrDesc}"/>
                </Select>
            </VBox>
            <VBox>
                <Label labelFor="UpdCrMaxNoRecErr" required="false" text="Max. Rec."></Label>
                <Input id="UpdCrMaxNoRecErr" width="100%"
                    value="{mdconsolidationModel>/configData/UpdCrMaxNoRecErr}"
                    editable="{= ( ${mdconsolidationModel>/configData/UpdTargetErr} === '2' || ${mdconsolidationModel>/configData/UpdTargetErr} === '3' ) ? true : false }"/>
            </VBox>
        </l:Grid>

        <GenericTag text="Match Groups" status="None" design="StatusIconHidden" class="sapUiSmallMarginBottom sapUiSmallMarginBegin"/>
		<l:Grid containerQuery="true" defaultSpan="XL4 L4 M4 S4">
            <VBox>
                <Label labelFor="MtcTarget" required="false" text="Target for Correct Match Groups"></Label>
                <Select id="MtcTarget" width="100%" editable="true"
                    change=".MDCConfigurationACT.onChangeTarget"
                    items="{path:'mdconsolidationModel>/configData/MtcTargetList'}"
                    selectedKey="{mdconsolidationModel>/configData/MtcTarget}">
                    <c:Item key="{mdconsolidationModel>ID}" text="{mdconsolidationModel>Description}"/>
                </Select>
            </VBox>
            <VBox>
                <Label labelFor="MtcCrType" required="{= ( ${mdconsolidationModel>/configData/MtcTarget} === '2' || ${mdconsolidationModel>/configData/MtcTarget} === '3' ) ? true : false }" text="CR Type"></Label>
                <!-- Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog -->
                <Select id="MtcCrType" width="100%" 
                    editable="{= ( ${mdconsolidationModel>/configData/MtcTarget} === '2' || ${mdconsolidationModel>/configData/MtcTarget} === '3' ) ? true : false }"
                    change=".MDCConfigurationACT.onChangeCrType"
                    items="{path:'mdconsolidationModel>/configData/MtcCrTypeList'}"
                    selectedKey="{mdconsolidationModel>/configData/MtcCrType}"
                    valueState="{mdconsolidationModel>/configData/MtcCrTypeValueState}"
                    valueStateText="{mdconsolidationModel>/configData/MtcCrTypeValueStateText}">
                    <c:Item key="{mdconsolidationModel>CrType}" text="{mdconsolidationModel>CrType} - {mdconsolidationModel>CrDesc}"/>
                </Select>
            </VBox>
            <VBox>
                <Label labelFor="MtcCrMaxNoRec" required="false" text="Max. Rec."></Label>
                <Input id="MtcCrMaxNoRec" width="100%"
                    value="{mdconsolidationModel>/configData/MtcCrMaxNoRec}"
                    editable="{= ( ${mdconsolidationModel>/configData/MtcTarget} === '2' || ${mdconsolidationModel>/configData/MtcTarget} === '3' ) ? true : false }"/>
            </VBox>
            <VBox>
                <Label labelFor="MtcTargetErr" required="false" text="Target for Incorrect Match Groups"></Label>
                <Select id="MtcTargetErr" width="100%" editable="true"
                    change=".MDCConfigurationACT.onChangeTarget"
                    items="{path:'mdconsolidationModel>/configData/MtcTargetErrList'}"
                    selectedKey="{mdconsolidationModel>/configData/MtcTargetErr}">
                    <c:Item key="{mdconsolidationModel>ID}" text="{mdconsolidationModel>Description}"/>
                </Select>
            </VBox>
            <VBox>
                <Label labelFor="MtcCrTypeErr" required="{= ( ${mdconsolidationModel>/configData/MtcTargetErr} === '2' || ${mdconsolidationModel>/configData/MtcTargetErr} === '3' ) ? true : false }" text="CR Type"></Label>
                <!-- Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog -->
                <Select id="MtcCrTypeErr" width="100%" 
                    editable="{= ( ${mdconsolidationModel>/configData/MtcTargetErr} === '2' || ${mdconsolidationModel>/configData/MtcTargetErr} === '3' ) ? true : false }"
                    change=".MDCConfigurationACT.onChangeCrType"
                    items="{path:'mdconsolidationModel>/configData/MtcCrTypeErrList'}"
                    selectedKey="{mdconsolidationModel>/configData/MtcCrTypeErr}"
                    valueState="{mdconsolidationModel>/configData/MtcCrTypeErrValueState}"
                    valueStateText="{mdconsolidationModel>/configData/MtcCrTypeErrValueStateText}">
                    <c:Item key="{mdconsolidationModel>CrType}" text="{mdconsolidationModel>CrType} - {mdconsolidationModel>CrDesc}"/>
                </Select>    
            </VBox>
            <VBox>
                <Label labelFor="MtcCrMaxNoRecErr" required="false" text="Max. Rec."></Label>
                <Input id="MtcCrMaxNoRecErr" width="100%" 
                    value="{mdconsolidationModel>/configData/MtcCrMaxNoRecErr}"
                    editable="{= ( ${mdconsolidationModel>/configData/MtcTargetErr} === '2' || ${mdconsolidationModel>/configData/MtcTargetErr} === '3' ) ? true : false }"/>
            </VBox>
        </l:Grid>

        <GenericTag text="Replication" status="None" design="StatusIconHidden" class="sapUiSmallMarginBottom sapUiSmallMarginBegin"/>
		<l:Grid containerQuery="true" defaultSpan="XL4 L4 M4 S4">
            <VBox>
                <Label labelFor="Replication" required="false" text="Call DRF After Activation"></Label>
                <CheckBox id="Replication" textAlign="Left" 
                    editable="false"
                    select=".MDCConfigurationACT.onSelectReplication" 
                    selected="{= ${mdconsolidationModel>/configData/Replication}}"/>
            </VBox>
        </l:Grid>

        <GenericTag text="Validation" status="None" design="StatusIconHidden" class="sapUiSmallMarginBottom sapUiSmallMarginBegin"/>
        <l:Grid containerQuery="true" defaultSpan="XL4 L4 M4 S4">
            <VBox>
                <Label labelFor="DQRuleValidations" required="false" text="Apply Validation Rules"></Label>
                <CheckBox id="DQRuleValidations" textAlign="Left" 
                    editable="{= ${mdconsolidationModel>/selectedProcessTemplate/BoType} !== '1405' ? true : false }"
                    select=".MDCConfigurationACT.onSelectDQRuleValidations" 
                    selected="{= ${mdconsolidationModel>/configData/DQRuleValidations}}"/>
            </VBox>
            <VBox>
                <Label labelFor="MDGValidations" required="false" text="Execute Change Request Checks"></Label>
                <CheckBox id="MDGValidations" textAlign="Left" select=".MDCConfigurationACT.onSelectMDGValidations" selected="{= ${mdconsolidationModel>/configData/MDGValidations}}"/>
            </VBox>
            <VBox>
                <Label labelFor="BRFPlusValidations" required="false" text="Execute BRF Plus Checks"></Label>
                <CheckBox id="BRFPlusValidations" textAlign="Left" select=".MDCConfigurationACT.onSelectBRFPlusValidations" selected="{= ${mdconsolidationModel>/configData/BRFPlusValidations}}"/>
            </VBox>
        </l:Grid>

        <GenericTag text="Retention Time" status="None" design="StatusIconHidden" class="sapUiSmallMarginBottom sapUiSmallMarginBegin"/>
        <l:Grid containerQuery="true" defaultSpan="XL4 L4 M4 S4">
            <VBox>
                <Label labelFor="RetentionDays" required="false" text="Days"></Label>
                <Input id="RetentionDays" width="100%" editable="true" value="{mdconsolidationModel>/configData/RetentionDays}"/>
            </VBox>
            <VBox>
                <Label labelFor="RetentionHours" required="false" text="Hours"></Label>
                <Input id="RetentionHours" width="100%" editable="true" value="{mdconsolidationModel>/configData/RetentionHours}"/>
            </VBox>
        </l:Grid>

        <!-- Bug 12404 - Remove fields based on version related properties -->
        <GenericTag text="Step Scheduling" status="None" design="StatusIconHidden" class="sapUiSmallMarginBottom sapUiSmallMarginBegin"
            visible="{= ${mdconsolidationModel>/ShowScheduleThreshold} === true}"/>
        <l:Grid containerQuery="true" defaultSpan="XL4 L4 M4 S4"
            visible="{= ${mdconsolidationModel>/ShowScheduleThreshold} === true}">
            <VBox visible="{= ${mdconsolidationModel>/ShowScheduleThreshold} === true}">
                <Label labelFor="ScheduleThreshold" required="false" text="Minimum Number of Records"></Label>
                <Input id="ScheduleThreshold" width="100%" editable="true" value="{mdconsolidationModel>/configData/ScheduleThreshold}"/>
            </VBox>
        </l:Grid>

        <buttons>
            <Button id="BtnDelete" icon="sap-icon://delete" text="Delete" press=".MDCConfigurationACT.onClickDelete" visible="{= ${mdconsolidationModel>/configData/OperationType} !== 'C' }"/>
            <Button id="BtnSave" icon="sap-icon://save" text="{= ${mdconsolidationModel>/configData/OperationType} === 'C' ? 'Create' : 'Update' }" press=".MDCConfigurationACT.onClickSave"/> 
            <Button id="BtnClose" icon="sap-icon://decline" text="Close" press=".MDCConfigurationACT.onClickClose"/>
        </buttons>
	</Dialog>

</c:FragmentDefinition>