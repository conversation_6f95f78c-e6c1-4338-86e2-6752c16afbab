<c:FragmentDefinition 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m"
	displayBlock="true">
	<Dialog title="{i18n>businessRules.importFragment.title}">
		<VBox class="sapUiSmallMarginTop sapUiSmallMarginBegin sapUiSmallMarginEnd">
			<Label text="{i18n>businessRules.common.dataModel.title}" labelFor="idCbImportDataModel"/>
			<ComboBox 
				id="idCbImportDataModel"
				items="{ path: 'BusinessRules>/DataModelsList', length: 2000, filters: [{path: 'DataModelId', operator: 'NE', value1: ''}] }"
				change=".onChangeDataModelImport"
				width="100%"
				selectedKey="{BusinessRules>/importRules/selectedDataModel}"
				required="true">
				
				<c:ListItem
					key="{BusinessRules>DataModelId}" 
					text="{BusinessRules>DataModelId} - {BusinessRules>DataModelDescription}"/>
			</ComboBox>

			<Label class="sapUiSmallMarginTop" text="{i18n>businessRules.importFragment.fromCrType.title}" labelFor="idCbImportFromCRType"/>
			<ComboBox 
				id="idCbImportFromCRType"
				items="{ path: 'BusinessRules>/importRules/fromChangeRequestList', length: 2000 }"
				change=".onSelectImportFromCRType"
				width="100%"
				selectedKey="{BusinessRules>/importRules/selectedFromCRType}"
				required="true">
				
				<c:ListItem
					additionalText="{BusinessRules>UsmdModel}"
					key="{BusinessRules>UsmdCreqType}" 
					text="{BusinessRules>UsmdCreqType} - {BusinessRules>Txtmi}"/>
			</ComboBox>

			<VBox visible="{= ${BusinessRules>/RulesBy} === 1}">
				<Label class="sapUiSmallMarginTop" text="{i18n>businessRules.importFragment.fromStepType.title}" labelFor="idCbImportFromCRStep"/>
				<ComboBox 
					id="idCbImportFromCRStep"
					items="{ path: 'BusinessRules>/importRules/fromCRStepsList', length: 2000 }"
					width="100%"
					selectedKey="{BusinessRules>/importRules/selectedFromCRStep}"
					required="true">
					
					<c:ListItem
						key="{BusinessRules>UsmdCreqStep}" 
						text="{BusinessRules>UsmdCreqStep} - {BusinessRules>Txtmi}"/>
				</ComboBox>
			</VBox>

			<Label class="sapUiSmallMarginTop" text="{i18n>businessRules.importFragment.toCrType.title}" labelFor="idCbImportToCRType"/>
			<ComboBox 
				id="idCbImportToCRType"
				items="{ path: 'BusinessRules>/importRules/toChangeRequestList', length: 2000 }"
				change=".onSelectImportToCRType"
				width="100%"
				selectedKey="{BusinessRules>/importRules/selectedToCRType}"
				required="true">
				
				<c:ListItem
					additionalText="{BusinessRules>UsmdModel}"
					key="{BusinessRules>UsmdCreqType}" 
					text="{BusinessRules>UsmdCreqType} - {BusinessRules>Txtmi}"/>
			</ComboBox>

			<VBox visible="{= ${BusinessRules>/RulesBy} === 1}">
				<Label class="sapUiSmallMarginTop" text="{i18n>businessRules.importFragment.toStepType.title}" labelFor="idCbImportToCRStep"/>
				<ComboBox 
					id="idCbImportToCRStep"
					items="{ path: 'BusinessRules>/importRules/toCRStepsList', length: 2000 }"
					width="100%"
					selectedKey="{BusinessRules>/importRules/selectedToCRStep}"
					required="true">
					
					<c:ListItem
						additionalText="{BusinessRules>UsmdModel}"
						key="{BusinessRules>UsmdCreqStep}" 
						text="{BusinessRules>UsmdCreqStep} - {BusinessRules>Txtmi}"/>
				</ComboBox>
			</VBox>

			<VBox visible="{= ${BusinessRules>/RulesBy} === 0}">
				<Label class="sapUiLargeMarginTop" text="{i18n>businessRules.importFragment.selectRuleTypes.title}"/>
				<MultiComboBox 
					id="idMultiCbImportRuleType" showSelectAll="true"
					selectionChange=".onSelectImportRuleType"
					selectedKeys="{BusinessRules>/importRules/selectedRuleTypes}"
					enabled="{= ${BusinessRules>/importRules/selectedToCRType} ? true : false}"
					width="100%">
					<c:ListItem
						key="Mandatory" 
						text="Mandatory"/>
					<c:ListItem
						key="Single Value Validation" 
						text="Single Value Validation"/>
					<c:ListItem
						key="Single Value Derivation" 
						text="Single Value Derivation"/>
					<c:ListItem
						key="Multi Value Validation" 
						text="Multi Value Validation"/>
					<c:ListItem
						key="Multi Value Derivation" 
						text="Multi Value Derivation"/>
				</MultiComboBox>
				
				<c:ComponentContainer id="importRulesList" name="dmr.components.SearchList" async="false"
					componentCreated="onImportRulesListCreated"/>
			</VBox>
		</VBox>
		<beginButton>
			<Button 
				text="{i18n>common.button.save.title}" press=".onImportRulesSave" 
				enabled="{parts: [
							'BusinessRules>/RulesBy',
							'BusinessRules>/importRules/selectedFromCRType',
							'BusinessRules>/importRules/selectedFromCRStep',
							'BusinessRules>/importRules/selectedToCRType',
							'BusinessRules>/importRules/selectedToCRStep',
							'BusinessRules>/importAllowed'
						],
						formatter:'.isImportSaveEnabled'
					}"
				id="idSaveImportRules"/>
		</beginButton>
		<endButton>
			<Button text="{i18n>common.button.cancel.title}" press=".onImportRulesCancel" id="idCancelImportRules"/>
		</endButton>
	</Dialog>
</c:FragmentDefinition>