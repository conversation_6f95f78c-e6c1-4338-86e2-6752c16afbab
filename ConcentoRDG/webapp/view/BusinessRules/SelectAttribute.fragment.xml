<c:FragmentDefinition 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	controllerName="dmr.components"
	displayBlock="true">
	<Dialog title="Select Attribute">
		<l:VerticalLayout class="sapUiContentPadding" width="100%">
			<l:content>
				<!-- This label holds the ID of the node that is being edited. The data is stored back to the node with this ID -->
				<Label text="{attributeSelectionModel>/attributeType}" visible="false"/>
				<VBox>
					<Label text="Entity" labelFor="idCBoxEntities" required="true"/>
					<ComboBox id="idCBoxEntities" width="100%"
						selectedKey="{attributeSelectionModel>/selectedEntity}" 
						enabled="{= ${attributeSelectionModel>/attributeType} === 'Driving'}"
						items="{ path: 'attributeSelectionModel>/entityList', length: 2000 }"
						selectionChange=".onEntitySelectionChange" >
						<c:ListItem key="{attributeSelectionModel>UsmdEntity}"
							 text="{= ${attributeSelectionModel>UsmdEntity}.concat(${attributeSelectionModel>Txtlg}==='' ? '' : ' - '.concat(${attributeSelectionModel>Txtlg}))}" />
					</ComboBox>
				</VBox>
		
				<VBox>
					<Label text="Attribute" labelFor="idCBoxAttributes" required="true"/>
					<RadioButtonGroup id="rbValueOrLength" columns="2"
						visible="{= ${attributeSelectionModel>/attributeType} === 'Driving'}"
						selectedIndex="{attributeSelectionModel>/selectedType}">
						<buttons>
							<RadioButton text="Value"/>
							<RadioButton text="Length"/>
						</buttons>
					</RadioButtonGroup>
					<!-- Task 11998 - Change ComboBox to MultiComboBox for multiple selection of attributes -->
					<MultiComboBox id="idCBoxAttributes" width="300px"
						selectedKeys="{attributeSelectionModel>/selectedAttributes}" showSelectAll="true"
						items="{ path: 'attributeSelectionModel>/attributeListFiltered', length: 2000, sorter: { path: 'UsmdAttribute' } }" >
						<c:Item key="{attributeSelectionModel>UsmdAttribute}"
							text="{attributeSelectionModel>UsmdAttribute} - {attributeSelectionModel>Txtlg}"/>
					</MultiComboBox>
				</VBox>
			</l:content>
		</l:VerticalLayout>

		<beginButton> 
			<Button 
				text="Add Attributes" 
				press=".onAddAttributesConfirm" id="addAttribute"/>
		</beginButton>
		<endButton>
			<Button text="Cancel" press=".onCancelAddDerivingAttribute" id="cancel"/>
		</endButton>

	</Dialog>
	
</c:FragmentDefinition>