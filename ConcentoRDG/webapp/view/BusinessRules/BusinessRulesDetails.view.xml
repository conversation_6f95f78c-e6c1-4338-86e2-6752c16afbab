<mvc:View 
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.BusinessRules.BusinessRulesDetails"
	xmlns:mvc="sap.ui.core.mvc"
	xmlns:c="sap.ui.core"
	xmlns="sap.m"
	xmlns:forms="sap.ui.layout.form"
	xmlns:f="sap.f"
	xmlns:grid="sap.ui.layout.cssgrid">

	<Page 
		title=""
		titleLevel="H1"
		showNavButton="true"
		navButtonPress="onBackPress">

		<headerContent>
			<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.shared.MainMenu"/>
		</headerContent>

		<content>
			<f:DynamicPage headerExpanded="true" preserveHeaderStateOnScroll="false" toggleHeaderOnTitleClick="true">
				<f:title>
					<f:DynamicPageTitle busy="{= ${BusinessRulesDetails>/isBusy} === true}">
						<f:breadcrumbs>
							<Breadcrumbs>
								<Link text="Home" press=".onClickLinkHome"/>
								<Link text="Business Rules" press=".onClickLinkBusinessRules"/>
								<Link text="{BusinessRulesDetails>/selectedBusinessRule/BusinessRuleName}" press=".onClickLinkThisPage" />
							</Breadcrumbs>
						</f:breadcrumbs>
						<f:heading>
							<Title text="{= ${BusinessRulesDetails>/selectedBusinessRule/UserRuleName}.length > 0 ? ${BusinessRulesDetails>/selectedBusinessRule/UserRuleName} : ${BusinessRulesDetails>/selectedBusinessRule/BusinessRuleName}}"/>
						</f:heading>
						<f:actions>
							<Button 
								text="Save" 
								icon="sap-icon://save" 
								type="Accept" 
								visible="{= ${BusinessRulesDetails>/FragmentToLoad} !== 'Mandatory'}" 
								press="onRuleSavePressed" 
								enabled="{= (!${BusinessRulesDetails>/newRule} &amp;&amp; ${BusinessRulesDetails>/lockedActions}.includes('S')) 
											|| (${BusinessRulesDetails>/newRule} &amp;&amp; ${BusinessRulesDetails>/lockedActions}.includes('E')) 
											|| ${BusinessRulesDetails>/lockedActions} === ''}"/>
							<Button 
								text="Delete" 
								icon="sap-icon://delete" 
								type="Reject" 
								press="onRuleDeletePressed" 
								visible="{= ${BusinessRulesDetails>/FragmentToLoad} !== 'CRStepTypeProps' &amp;&amp; !${BusinessRulesDetails>/newRule}}"
								enabled="{= !${BusinessRulesDetails>/lockedActions}.includes('D')}"/>
						</f:actions>
					</f:DynamicPageTitle>
				</f:title>
				
				<f:header>
					<f:DynamicPageHeader pinnable="false" busy="{= ${BusinessRulesDetails>/isBusy} === true}">
						<f:content>
							<forms:SimpleForm editable="true" layout="ColumnLayout">
								<forms:content>
									<Label text="Data Model"/>
									<!-- Bug 12802 - Event "selectionChange" was changed to "change" event to avoid clean the field -->
									<ComboBox
										id="cmbDataModel"
										required="true"
										enabled="{= ${BusinessRulesDetails>/newRule} &amp;&amp; (!${BusinessRulesDetails>/drivingAttributes} 
													|| ${BusinessRulesDetails>/drivingAttributes}.length === 0) 
													&amp;&amp; (!${BusinessRulesDetails>/derivingAttributes} 
													|| ${BusinessRulesDetails>/derivingAttributes}.length === 0)}"
										items="{ path: 'BusinessRulesDetails>/DataModelsList', length: 2000 }"
										selectedKey="{BusinessRulesDetails>/selectedBusinessRule/DataModelId}"
										change=".onChangeDataModel">
										<c:ListItem
											key="{BusinessRulesDetails>DataModelId}" 
											text="{BusinessRulesDetails>DataModelId} - {BusinessRulesDetails>DataModelDescription}"/>
									</ComboBox>
									
									<Label text="CR Type"/>
									<!-- Bug 12723 - Added a contition to lock the field if there is not a Data Model selected -->
									<!-- Bug 12802 - Event "selectionChange" was changed to "change" event to avoid clean the field -->
									<ComboBox
										id="cmbCRType"
										required="true"
										enabled="{= ${BusinessRulesDetails>/newRule} &amp;&amp; (!${BusinessRulesDetails>/drivingAttributes} 
													|| ${BusinessRulesDetails>/drivingAttributes}.length === 0) 
													&amp;&amp; (!${BusinessRulesDetails>/derivingAttributes} 
													|| ${BusinessRulesDetails>/derivingAttributes}.length === 0)  
													&amp;&amp; ${BusinessRulesDetails>/selectedBusinessRule/DataModelId} !== ''}"
										items="{ path: 'BusinessRulesDetails>/CRTypesList', length: 2000 }"  
										selectedKey="{BusinessRulesDetails>/selectedBusinessRule/CRTypeId}"
										change=".onChangeCRType">
										<c:ListItem
											key="{BusinessRulesDetails>CRTypeId}" 
											text="{BusinessRulesDetails>CRTypeId} - {BusinessRulesDetails>CRTypeDescription}"/>
									</ComboBox>
									
									<Label text="Entity" visible="{= ${BusinessRulesDetails>/FragmentToLoad} !== 'CRStepTypeProps'}"/>
									<!-- Bug 12723 - Added a contition to lock the field if there is not a CR Type selected selected -->
									<!-- Bug 12763 - Event "selectionChange" was changed to "change" event to avoid call the method before finishing the selection -->
									<ComboBox
										id="cmbEntity"
										visible="{= ${BusinessRulesDetails>/FragmentToLoad} !== 'CRStepTypeProps'}"
										required="true"
										enabled="{= ${BusinessRulesDetails>/newRule} &amp;&amp; (!${BusinessRulesDetails>/drivingAttributes} 
													|| ${BusinessRulesDetails>/drivingAttributes}.length === 0) 
													&amp;&amp; (!${BusinessRulesDetails>/derivingAttributes} 
													|| ${BusinessRulesDetails>/derivingAttributes}.length === 0) 
													&amp;&amp; ${BusinessRulesDetails>/selectedBusinessRule/DataModelId} !== '' 
													&amp;&amp; ${BusinessRulesDetails>/selectedBusinessRule/CRTypeId} !== ''}"
										items="{ path: 'BusinessRulesDetails>/EntitiesList', length: 2000 }"  
										selectedKey="{BusinessRulesDetails>/selectedBusinessRule/EntityId}"
										change=".onChangeEntity">
										<c:ListItem
											key="{BusinessRulesDetails>EntityId}" 
											text="{BusinessRulesDetails>EntityId} - {BusinessRulesDetails>EntityDescription}"/>
									</ComboBox>
									
									<Label text="Rule Type" visible="{= ${BusinessRulesDetails>/FragmentToLoad} !== 'CRStepTypeProps'}"/>
									<ComboBox
										id="cmbRuleType"
										visible="{= ${BusinessRulesDetails>/FragmentToLoad} !== 'CRStepTypeProps'}"
										required="true"
										enabled="false"
										items="{ path: 'BusinessRulesDetails>/RuleTypesList', length: 2000 }"  
										selectedKey="{BusinessRulesDetails>/selectedBusinessRule/RuleTypeId}">
										<c:ListItem
											key="{BusinessRulesDetails>RuleTypeId}" 
											text="{BusinessRulesDetails>RuleTypeId} - {BusinessRulesDetails>RuleTypeDescription}"/>
									</ComboBox>

									<Label text="CR Step Type" visible="{= ${BusinessRulesDetails>/FragmentToLoad} === 'CRStepTypeProps'}"/>
									<ComboBox
										id="cmbCRStepType"
										visible="{= ${BusinessRulesDetails>/FragmentToLoad} === 'CRStepTypeProps'}"
										required="true"
										enabled="{= ${BusinessRulesDetails>/newRule}}"
										items="{ path: 'BusinessRulesDetails>/CRStepsList', length: 2000 }"  
										selectedKey="{BusinessRulesDetails>/selectedBusinessRule/CRStep}"
										change=".onChangeCRStepType">
										<c:ListItem
											key="{BusinessRulesDetails>UsmdCreqStep}" 
											text="{BusinessRulesDetails>UsmdCreqStep} - {BusinessRulesDetails>Txtmi}"/>
									</ComboBox>
								</forms:content>
							</forms:SimpleForm>
							<HBox width="100%" justifyContent="SpaceAround" >
								<CheckBox 
									enabled="false" 
									visible="{= ${BusinessRulesDetails>/selectedBusinessRule/RuleTypeId} === '5'}"
									selected="{= ${BusinessRulesDetails>/selectedBusinessRule/RuleExpression/crossentity} === 'X' || ${BusinessRulesDetails>/selectedBusinessRule/RuleExpression/crossentity} === 'N'}" 
									text="{BusinessRulesDetails>/selectedBusinessRule/RuleExpression/crossentityDescription}" 
								/>
								<HBox alignItems="Center" visible="{= ${BusinessRulesDetails>/FragmentToLoad} !== 'Mandatory' &amp;&amp; ${BusinessRulesDetails>/FragmentToLoad} !== 'CRStepTypeProps'}">
									<Label labelFor="userBusinessRuleName" text="Rule Name: " />
									<Input id="userBusinessRuleName" value="{BusinessRulesDetails>/selectedBusinessRule/UserRuleName}" class="sapUiSmallMarginBegin" />
								</HBox>
							</HBox>
						</f:content>
					</f:DynamicPageHeader>
				</f:header>
				<f:content>
					<Page showHeader="false">
						<VBox visible="{= ${BusinessRulesDetails>/FragmentToLoad} === 'CRStepTypeProps'}">
							<c:Fragment fragmentName="dmr.mdg.supernova.SupernovaFJ.view.BusinessRules.CRStepTypeProps" type="XML" />
						</VBox>
						<VBox visible="{= ${BusinessRulesDetails>/FragmentToLoad} === 'Mandatory'}">
							<c:Fragment fragmentName="dmr.mdg.supernova.SupernovaFJ.view.BusinessRules.MandatoryRule" type="XML" />
						</VBox>
						<VBox visible="{= ${BusinessRulesDetails>/FragmentToLoad} === 'ValueCheck'}">
							<Toolbar class="toolbarBackground">
								<ToolbarSpacer/>
									<Label text="WF__STEP:" />
									<Select items="{BusinessRulesDetails>/WorkflowStepsList}" change=".onChangeStep" selectedKey="{BusinessRulesDetails>/selectedRule/colValue}">
										<c:Item key="{BusinessRulesDetails>UsmdCreqStep}" text="{BusinessRulesDetails>UsmdCreqStep} - {BusinessRulesDetails>Txtmi}"/>
									</Select>
								<ToolbarSpacer/>
							</Toolbar>
							<c:Fragment id="SingleValueRuleFragment" fragmentName="dmr.mdg.supernova.SupernovaFJ.view.BusinessRules.SingleValueRule" type="XML" />
						</VBox>
						<SplitContainer
							id="SplitContDemo"
							initialDetail="detail"
							initialMaster="master"
							visible="{= ${BusinessRulesDetails>/FragmentToLoad} !== 'CRStepTypeProps' 
										&amp;&amp; ${BusinessRulesDetails>/FragmentToLoad} !== 'Mandatory' 	
										&amp;&amp; ${BusinessRulesDetails>/FragmentToLoad} !== 'ValueCheck'}">
							<masterPages>
								<Page
									id="master"
									title="Rules">
									<!-- Bug 12781 - Changed property to read the filter items -->
									<ComboBox width="100%" items="{BusinessRulesDetails>/WorkflowStepsListForFilter}" change=".onChangeStepFilter">
										<c:Item key="{BusinessRulesDetails>UsmdCreqStep}" text="{BusinessRulesDetails>UsmdCreqStep} - {BusinessRulesDetails>Txtmi}"/>
									</ComboBox>
									<List id="rulesListId" items="{BusinessRulesDetails>/rulesByStep}" mode="SingleSelectMaster" rememberSelections="false" selectionChange="onPressRuleByStep">
										<StandardListItem title="Rule {BusinessRulesDetails>rowNum}" description="{BusinessRulesDetails>colValue} - {BusinessRulesDetails>description}" type="Active" />
									</List>
									
									<c:ComponentContainer id="transportPackageSelectDialog" name="dmr.components.TransportPackage" async="true"
										componentCreated="onTransportPackageDialogCreated"/>
									<c:ComponentContainer id="BADISelectDialog" name="dmr.components.BADISelect" async="true"
										componentCreated="onBADISelectionDialogCreated"/>
									<footer>
										<OverflowToolbar>
											<ToolbarSpacer/>
											<Button type="Transparent" icon="sap-icon://add"
													tooltip="Add Rule" 
													press="onAddStepRule" 
											/>
											<Button type="Transparent" icon="sap-icon://delete" 
													enabled="{= ${BusinessRulesDetails>/rulesByStep}.length > 1 &amp;&amp; ${BusinessRulesDetails>/selectedRulesNumber} ? true : false}" 
													tooltip="Delete Rule" 
													press="onDeleteStepRule" 
											/>
										</OverflowToolbar>
									</footer>
								</Page>
							</masterPages>
							<detailPages>
							<!-- Fragment shown will be changed based on the rule type selected by user in screen 1 -->
							<!-- Add Multiple VBox. Manage visibility based on selected rule type  -->
								<Page
									id="detail"
									title="Details"
									showNavButton="{= !${device>/system/desktop} }"
									navButtonPress=".onPressDetailBack">
									<VBox visible="{= ${BusinessRulesDetails>/FragmentToLoad} === 'Unidentified' || ${BusinessRulesDetails>/selectedRulesNumber} ? false : true}">
										<c:Fragment fragmentName="dmr.mdg.supernova.SupernovaFJ.view.BusinessRules.Unidentified" type="XML" />
									</VBox>
									<VBox class="sapUiTinyMarginEnd" visible="{= ${BusinessRulesDetails>/FragmentToLoad} === 'Init' &amp;&amp; ${BusinessRulesDetails>/selectedRulesNumber} ? true : false}">
										<Toolbar class="toolbarBackground">
											<ToolbarSpacer/>
												<Label text="WF__STEP:" />
												<Select items="{BusinessRulesDetails>/WorkflowStepsList}" change=".onChangeStep" selectedKey="{BusinessRulesDetails>/selectedRule/colValue}">
													<c:Item key="{BusinessRulesDetails>UsmdCreqStep}" text="{BusinessRulesDetails>UsmdCreqStep} - {BusinessRulesDetails>Txtmi}"/>
												</Select>
											<ToolbarSpacer/>
										</Toolbar>
										<f:GridList
											id="gridListDriving"
											class="sapUiSmallMargin"
											items="{BusinessRulesDetails>/drivingAttributes}">
											<f:customLayout>
												<grid:GridBoxLayout boxWidth="22rem" />
											</f:customLayout>
											<f:headerToolbar>
												<OverflowToolbar>
													<Title text="Driving Attributes"/>
													<ToolbarSpacer />
													<Button id="btnAddDriving" 
														enabled="{= ${BusinessRulesDetails>/WorkflowStepsList}.length > 0}"
														tooltip="Add Attribute" 
														icon="sap-icon://add" 
														press="onAddAttribute" 
														class="sapUiTinyMarginEnd"/>
												</OverflowToolbar>
											</f:headerToolbar>
											<f:GridListItem>
												<VBox class="sapUiTinyMargin">
													<HBox width="100%" justifyContent="SpaceBetween" alignItems="Center">
														<!-- Bug 12909 - Added specific width to the title to respect wrapping property -->
														<Title text="{BusinessRulesDetails>colName}" wrapping="true" width="17rem" />
														<Button type="Transparent" icon="sap-icon://decline" tooltip="Delete Attribute" press="onDeleteAttribute($event, 'Driving')" />
													</HBox>
													<HBox width="100%" id="gridHBoxDriving">
														<Select selectedKey="{BusinessRulesDetails>operator}" change="onOperatorChange">
															<c:Item key="EQ" text="=" />
															<c:Item key="NE" text="!=" />
															<c:Item key="GT" text="&gt;" />
															<c:Item key="GE" text="&gt;=" />
															<c:Item key="LT" text="&lt;" />
															<c:Item key="LE" text="&lt;=" />
															<c:Item key="CP" text="CP" />
															<c:Item key="NP" text="NP" />
														</Select>
														<VBox justifyContent="End" alignItems="End">
															<DatePicker
																value="{parts:['BusinessRulesDetails>colValue', 'BusinessRulesDetails>currentDateCheck'], formatter:'.datePickerFormatter'}"
																displayFormat="M/d/yyyy"
																valueFormat="yyyyMMdd"
																visible="{= ${BusinessRulesDetails>attrdatatype} === 'DATS'}"
																enabled="{BusinessRulesDetails>ebDatePicker}"
																change=".changeDatePickerValue"
															/>
															<CheckBox
																selected="{BusinessRulesDetails>currentDateCheck}"
																visible="{= ${BusinessRulesDetails>attrdatatype} === 'DATS'}"
																select=".onCurrentDateSelect"
																text="Current Date"
															/>
														</VBox>
													</HBox>
												</VBox>
											</f:GridListItem>
										</f:GridList>

										<f:GridList
											id="gridListDeriving"
											class="sapUiSmallMargin"
											items="{BusinessRulesDetails>/derivingAttributes}"
											visible="{= ${BusinessRulesDetails>/selectedBusinessRule/RuleTypeId} === '5'}">
											<f:customLayout>
												<grid:GridBoxLayout boxWidth="22rem" />
											</f:customLayout>
											<f:headerToolbar>
												<OverflowToolbar>
													<Title text="Deriving Attributes"/>
													<ToolbarSpacer />
													<Button id="btnAddDeriving"
														enabled="{= ${BusinessRulesDetails>/WorkflowStepsList}.length > 0}"
														tooltip="Add Attribute" 
														icon="sap-icon://add" 
														press="onAddAttribute" 
														class="sapUiTinyMarginEnd"/>
												</OverflowToolbar>
											</f:headerToolbar>
											<f:GridListItem>

											</f:GridListItem>
										</f:GridList>

										<f:GridList
											id="gridListValidation"
											class="sapUiSmallMargin"
											items="{ path: 'BusinessRulesDetails>/validationMessage', templateShareable: true}"
											visible="{= ${BusinessRulesDetails>/selectedBusinessRule/RuleTypeId} === '4'}">
											<f:customLayout>
												<grid:GridBoxLayout boxWidth="22rem" />
											</f:customLayout>
											<f:headerToolbar>
												<OverflowToolbar>
													<Title text="Validation Message"/>
												</OverflowToolbar>
											</f:headerToolbar>
											<f:GridListItem >
												<VBox class="sapUiSmallMargin">
													<!-- Bug 12909 - Added specific width to the title to respect wrapping property -->
													<Title 
														wrapping="true" width="17rem"
														text="{= ${BusinessRulesDetails>colName} === 'MSGTY'  ? 'MESSAGE TYPE' 
																												: ${BusinessRulesDetails>colName} === 'MSGID' 
																												? 'MESSAGE CLASS' 
																												: 'MESSAGE NUMBER'}" 
													/>
													<HBox>
														<Input
															value="{BusinessRulesDetails>colValue}"
															visible="{= ${BusinessRulesDetails>colName} !== 'MSGTY'}"
															liveChange="onLiveChangeMessageValue"
															showSuggestion="true"
															autocomplete="false"
															valueState="{= ${BusinessRulesDetails>validValue} ? 'Information' : 'Warning'}"
															valueStateText="{= ${BusinessRulesDetails>validValue} ? 'Type to refresh list' : 'The selected value is not in the list'}"
															valueLiveUpdate="true"
															change="changeInputValue">
															<layoutData>
																<FlexItemData growFactor="4" />
															</layoutData>
														</Input>
														<ComboBox
															items="{ path: 'BusinessRulesDetails>/selectedBusinessRule/lists/MessageTypeValues', templateShareable: true}"
															selectedKey="{BusinessRulesDetails>colValue}"
															visible="{= ${BusinessRulesDetails>colName} === 'MSGTY'}"
															width="100%"
														>
															<c:Item key="{BusinessRulesDetails>Etype}" text="{BusinessRulesDetails>Etype} - {BusinessRulesDetails>Etext}" />
															<layoutData>
																<FlexItemData growFactor="4" />
															</layoutData>
														</ComboBox>
													</HBox>
												</VBox>
											</f:GridListItem>
										</f:GridList>
									</VBox>
								</Page>
							</detailPages>
						</SplitContainer>
					</Page>
				</f:content>
			</f:DynamicPage>		
		</content>
	</Page>
</mvc:View>