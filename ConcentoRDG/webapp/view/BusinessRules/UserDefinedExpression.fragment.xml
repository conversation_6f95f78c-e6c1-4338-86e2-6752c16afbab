<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	c:require="{UserDefinedExpressionDialog: 'dmr/mdg/supernova/SupernovaFJ/controller/BusinessRules/UserDefinedExpression'}"
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.BusinessRules.BusinessRulesDetails"
	displayBlock="true">
	
        <Dialog title="User-Defined Expression" contentWidth="40%">
                <VBox class="sapUiSmallMargin" justifyContent="Center">
                        <HBox alignItems="Center" alignContent="SpaceAround" width="100%">
                                <Button id="btnOpenParenthesis" text="(" press="UserDefinedExpressionDialog.onClickOpenParenthesis" class="sapUiSmallMarginEnd"/>
                                <Button id="btnCloseParenthesis" text=")" press="UserDefinedExpressionDialog.onClickCloseParenthesis" class="sapUiSmallMarginEnd"/>
                                <Button id="btnNewAttribute" text="New Attribute" press="UserDefinedExpressionDialog.onClickNewAttribute" class="sapUiSmallMarginEnd"/>
                                <Button id="btnAndOperator" text="AND" press="UserDefinedExpressionDialog.onClickAndOperator" class="sapUiSmallMarginEnd"/>
                                <Button id="btnOrOperator" text="OR" press="UserDefinedExpressionDialog.onClickOrOperator"/>
                        </HBox>

                        <TextArea id="txtUserDefinedExpression" width="100%"
                                growing="true" growingMaxLines="1"
                                enabled="{formatter: '.UserDefinedExpressionDialog.checkExpressionState', parts: ['BusinessRulesDetails>/userDefinedExpression']}"
                                value="{BusinessRulesDetails>/userDefinedExpression}" 
                                placeholder="Click the buttons above to build your expression..."
                                showValueStateMessage="{= ${BusinessRulesDetails>/userDefinedExpressionValueState} !== 'None'}"
                                valueState="{BusinessRulesDetails>/userDefinedExpressionValueState}"
                                valueStateText="{BusinessRulesDetails>/userDefinedExpressionValueStateText}"/>
                </VBox>

                <beginButton>
                        <Button id="btnSave" text="Save"
                        enabled="{= ${BusinessRulesDetails>/userDefinedExpressionValueState} !== 'Error'}" press="UserDefinedExpressionDialog.onClickSave"/>
                </beginButton>
                <endButton>
                        <Button id="btnCancel" text="Cancel" press="UserDefinedExpressionDialog.onClickCancel"/>
                </endButton>
	</Dialog>
	
</c:FragmentDefinition>