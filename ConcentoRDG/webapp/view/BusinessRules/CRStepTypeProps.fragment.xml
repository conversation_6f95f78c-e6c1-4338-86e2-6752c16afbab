<c:FragmentDefinition 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.BusinessRules.BusinessRulesDetails"
	c:require="{CRStepTypePropsDialog: 'dmr/mdg/supernova/SupernovaFJ/controller/BusinessRules/CRStepTypeProps'}"
	displayBlock="true">
	<VBox height="100%">

		<Table id="idCRStepPropsTable"
			alternateRowColors="true"
			items="{BusinessRulesDetails>/crStepTypeProps/CRStepPropsList}"
			sticky="HeaderToolbar,InfoToolbar">
			<headerToolbar>
				<OverflowToolbar class="transparentBara">						
					<ToolbarSpacer/>
					<Title id="TblTitle" text="CR Type Properties" />
					<ToolbarSpacer/>
					
				</OverflowToolbar>
			</headerToolbar>
			<infoToolbar>
				<OverflowToolbar class="transparentBar">
					<ToolbarSpacer/>
					
					<Button id="btnAdd" tooltip="Add CR Type Properties " 
						iconFirst="true" text="ADD"  icon="sap-icon://add" type="Default"
						press="CRStepTypePropsDialog.addCRTypeProps "/>
					
				</OverflowToolbar>
			</infoToolbar>
			<columns>
				<Column><Label text="Checks and Enrichment Spots" /></Column>
				<Column hAlign="Center"><Label text="Message Output" /></Column>
				<Column hAlign="Center" width="5rem"><Label text="Relevant" /></Column>
				<Column hAlign="Center"><Label text="Execution" /></Column>
			</columns>
			<items>
				<ColumnListItem>
					<cells>
						<Text text="{BusinessRulesDetails>Enrich} {BusinessRulesDetails>EnrichmentDescription}"/>
						<!-- Bug 12954 - Added correct path for selectedKey property -->
						<Select items="{path: 'BusinessRulesDetails>/crStepTypeProps/MessageOutputList', templateShareable: true}" selectedKey="{BusinessRulesDetails>Messageoutput}">
							<c:Item text="{BusinessRulesDetails>MessageOutputDescription}" key="{BusinessRulesDetails>Name}" />
						</Select>
						<CheckBox select="CRStepTypePropsDialog.onSelectRelevant" selected="{= ${BusinessRulesDetails>Relevant} === 'X'}"/>
						<Select items="{path: 'BusinessRulesDetails>/crStepTypeProps/ExecutionList', templateShareable: true}" selectedKey="{BusinessRulesDetails>Execution}">
							<c:Item text="{BusinessRulesDetails>ExecutionDescription}" key="{BusinessRulesDetails>Name}" />
						</Select>
					</cells>
				</ColumnListItem>
			</items>
		</Table>

	</VBox>
</c:FragmentDefinition>