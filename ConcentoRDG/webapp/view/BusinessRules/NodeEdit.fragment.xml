<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	c:require="{NodeEditDialog: 'dmr/mdg/supernova/SupernovaFJ/controller/BusinessRules/NodeEdit'}"
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.BusinessRules.BusinessRulesDetails"
	displayBlock="true">
	<Dialog title="Edit Node Details" contentWidth="40%">
		<l:VerticalLayout class="sapUiContentPadding" width="100%">
			<l:content>
				<!-- This label holds the ID of the node that is being edited. The data is stored back to the node with this ID -->
				
				<!--11890 - Error in creating single validation/derivation business rules for more than two entities-->
				<!--brought the radio button outside the Hbox-->
				<VBox>
					<Label text="Compare Against"/>
					<RadioButtonGroup id="rbGroupValueType" columns="2" 
					selectedIndex="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedValueType}"
					select = "NodeEditDialog.onAttrList">
						<buttons>
							<RadioButton id="rbValue" text="Value"/>
							<RadioButton id="rbAttribute" text="Attribute"/>
						</buttons>
					</RadioButtonGroup>
				</VBox>

				<!--11890 - Error in creating single validation/derivation business rules for more than two entities-->
				<!--Inserted enabled with a validation based on the attribute radio button-->
				<HBox>
					<Label text="{BusinessRulesDetails>/NodeEditDetails/nodeId}" visible="false"/>
						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd" >
							<Label text="Entity" labelFor="idCBoxEntities"/>
							<ComboBox id="idCBoxEntities" width="100%"
								enabled ="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedValueType} === 0}"
								selectedKey="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedEntity}" 
								items="{ path: 'BusinessRulesDetails>/NodeEditDetails/nodeEditEntityList' }"
								selectionChange="NodeEditDialog.onNodeEditEntitySelectionChange" >
								<c:ListItem key="{BusinessRulesDetails>UsmdEntity}"
									text="{= ${BusinessRulesDetails>UsmdEntity}.concat(${BusinessRulesDetails>Txtlg}==='' ? '' : ' - '.concat(${BusinessRulesDetails>Txtlg}))}" />
							</ComboBox>
						</VBox>

						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
							<Label text="Attribute" labelFor="idCBoxAttributes"/>
							<ComboBox id="idCBoxAttributes" width="100%"
								selectedKey="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedAttribute}" 
								items="{ path: 'BusinessRulesDetails>/NodeEditDetails/nodeEditAttributeList', length:2000, sorter: { path: 'UsmdAttribute' } }"
								selectionChange="NodeEditDialog.onNodeEditAttributeSelectionChange" >
								<c:ListItem key="{BusinessRulesDetails>UsmdAttribute}"
									text="{BusinessRulesDetails>UsmdAttribute} - {BusinessRulesDetails>Txtlg}"
									additionalText="{BusinessRulesDetails>Txtlg}"/>
							</ComboBox>
						</VBox>
				</HBox>
					
				<VBox visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedValueType} === 0 || ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedValueType} === 1}"
					class="sapUiSmallMarginBottom">
					
					<VBox visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedValueType} === 0}">
						<Label text="Comparision" labelFor="idSelectComparisionOp"/>
						<Select id="idSelectComparisionOp" 
							selectedKey="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator}" 
							items="{ path: 'BusinessRulesDetails>/NodeEditDetails/nodeEditComparatorList' }"
							change="NodeEditDialog.onNodeEditComparisonChange">
							<c:ListItem
								text="{BusinessRulesDetails>id} - {BusinessRulesDetails>name}"
								additionalText="{BusinessRulesDetails>constant}"
								key="{BusinessRulesDetails>id}"/>
						</Select>
					</VBox>
					
					<VBox visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedValueType} === 1}">
						<Label text="Comparision" labelFor="idSelectComparisionAttribute"/>
						<Select id="idSelectComparisionAttribute" 
							selectedKey="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedAttributeComparator}" 
							items="{ path: 'BusinessRulesDetails>/NodeEditDetails/nodeEditAttributeComparatorList' }" 
							change="NodeEditDialog.onNodeEditComparisonChange">
							<c:ListItem
								text="{BusinessRulesDetails>id} - {BusinessRulesDetails>name}"
								additionalText="{BusinessRulesDetails>constant}"
								key="{BusinessRulesDetails>id}"/>
						</Select>
					</VBox>
					
					
					<HBox>
						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd"
							visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementType} === 'Q'}">
							<Label text="Dimension" labelFor="idDimension"/>
							<ComboBox id="idDimension"
								selectedKey="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedDimension}" 
								items="{ path: 'BusinessRulesDetails>/NodeEditDetails/dimensionsList', sorter: { path: 'Dimension' } }"
								selectionChange="NodeEditDialog.onNodeEditDimensionChange" >
								<c:ListItem key="{BusinessRulesDetails>Dimension}"
									text="{= ${BusinessRulesDetails>Dimension}.concat(${BusinessRulesDetails>Text}==='' ? '' : ' - '.concat(${BusinessRulesDetails>Text}))}" />
							</ComboBox>
						</VBox>
						
						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd"
							visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementType} === 'Q' || ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementType} === 'A'}">
							<Label text="Unit" labelFor="idUnit"/>
							<ComboBox id="idUnit"
								selectedKey="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedUnit}" 
								items="{ path: 'BusinessRulesDetails>/NodeEditDetails/UnitsList', length: 500, sorter: { path: 'Unit' } }">
								<c:ListItem key="{BusinessRulesDetails>Unit}"
									text="{= ${BusinessRulesDetails>Unit}.concat(${BusinessRulesDetails>Text}==='' ? '' : ' - '.concat(${BusinessRulesDetails>Text}))}" />
							</ComboBox>
						</VBox>
						
						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd"
							visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} === 'BT' || ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} === 'NB'}">
							<Label text="From Value" labelFor="idInputFromValue"/>
							<!-- Task 11217 - Value Conversion and Data Check for Single value rules and properties -->
							<!-- visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditFromValueList}.length === 0 &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementType} !== 'P'}"  -->
							<Input id="idInputFromValue" 
								visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditFromValueList}.length === 0 &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementType} !== 'P'}"
								width="100%"
								value="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/inputFromValueData}" 
								placeholder="Enter Value ..."
								change="NodeEditDialog.onChangeValue"
								maxLength="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementLength}">
							</Input>
							<Input id="idInputFromValueSugg" 
								visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList} ? ${BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList}.length !== 0 &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementType} !== 'P' : false}"
								width="100%"
								value="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/inputFromValueData}" 
								placeholder="Enter Value ..." liveChange="NodeEditDialog.onLiveChangeFromValue"
								change="NodeEditDialog.onChangeValue"
								autocomplete="false"
								showSuggestion="true"
								suggestionItems="{BusinessRulesDetails>/NodeEditDetails/nodeEditFromValueList}"
								startSuggestion="0"
								valueState="Information"
								valueStateText="Type to refresh list">
								<suggestionItems>
									<c:Item
										key="{BusinessRulesDetails>Key}"
										text="{= ${BusinessRulesDetails>Key}.concat(${BusinessRulesDetails>Ddtext}==='' ? '' : ' - '.concat(${BusinessRulesDetails>Ddtext}))}"
									/>
								</suggestionItems>
							</Input>
							<HBox>
								<DatePicker id="idDatePFromValue" width="100%"
									visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList} ? ${BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList}.length === 0 &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementType} === 'P' : false}"
									value="{parts:['BusinessRulesDetails>/NodeEditDetails/nodeEditData/inputFromValueData', 'BusinessRulesDetails>/NodeEditDetails/nodeEditData/currentDateCheckFromValue'], formatter:'.datePickerFormatter'}"
									valueStateText="Enter correct format" 
									enabled="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/ebDatePickerFromValue}"
									placeholder="Enter Date ..." change="NodeEditDialog.onChangeDate"
									displayFormat="M/d/yyyy"	valueFormat="yyyyMMdd"
								/>
								<CheckBox
									id="idCurrentDateCheckFromValue"
									selected="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/currentDateCheckFromValue}"
									visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList} ? ${BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList}.length === 0 &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementType} === 'P' : false}"
									select="NodeEditDialog.onCurrentDateSelect"
									text="Current Date"
								/>
							</HBox>
						</VBox>
				
						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
							<Label
								visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedValueType} === 0 &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'BT' &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'NB'}"
								text="Value" labelFor="idInputToValue"/>
							<Label 
								visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedValueType} === 0 &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} === 'BT' || ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} === 'NB' }"
								text="To Value" labelFor="idInputToValue"/>
							<!-- Task 11217 - Value Conversion and Data Check for Single value rules and properties -->
							<!-- Bug 12379 - Not able to enter pattern when CP or NP selected for attribute value if it is dropdown -->
							<Input id="idInputToValue" width="100%"
								visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} === 'CP' || ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} === 'NP' || ( ${BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList}.length === 0 &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementType} !== 'P' &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedValueType} === 0 ) }"
								value="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/inputToValueData}" 
								placeholder="Enter Value ..."
								change="NodeEditDialog.onChangeValue"
								maxLength="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementLength}">
							</Input>
							<Input id="idInputToValueSugg" width="100%"
								visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'CP' &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'NP' ? ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedValueType} === 0 &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList} ? ${BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList}.length !== 0 &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementType} !== 'P' : false : false }"
								value="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/inputToValueData}" 
								placeholder="Enter Value ..." liveChange="NodeEditDialog.onLiveChangeToValue"
								change="NodeEditDialog.onChangeValue"
								autocomplete="false"
								showSuggestion="true"
								suggestionItems="{BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList}"
								startSuggestion="0"
								valueState="Information"
								valueStateText="Type to refresh list">
								<suggestionItems>
									<c:Item
										key="{BusinessRulesDetails>Key}"
										text="{= ${BusinessRulesDetails>Key}.concat(${BusinessRulesDetails>Ddtext}==='' ? '' : ' - '.concat(${BusinessRulesDetails>Ddtext}))}"
									/>
								</suggestionItems>
							</Input>
							<HBox>
								<DatePicker id="idDatePToValue" width="100%"
									visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'CP' &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'NP' ? ${BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList} ? ${BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList}.length === 0 &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementType} === 'P' : false : false }"
									value="{parts:['BusinessRulesDetails>/NodeEditDetails/nodeEditData/inputToValueData', 'BusinessRulesDetails>/NodeEditDetails/nodeEditData/currentDateCheckToValue'], formatter:'.datePickerFormatter'}"
									valueStateText="Enter correct format" enabled="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementType} === 'P' &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ebDatePickerToValue}}"
									placeholder="Enter Date ..." change="NodeEditDialog.onChangeDate"
									displayFormat="M/d/yyyy"	valueFormat="yyyyMMdd"
								/>
								<CheckBox
									id="idCurrentDateCheckToValue"
									selected="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/currentDateCheckToValue}"
									visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'CP' &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedComparator} !== 'NP' ? ${BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList} ? ${BusinessRulesDetails>/NodeEditDetails/nodeEditToValueList}.length === 0 &amp;&amp; ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/ElementType} === 'P' : false : false }"
									select="NodeEditDialog.onCurrentDateSelect"
									text="Current Date"
								/>
							</HBox>
						</VBox>
					</HBox>
					
					<HBox visible="{= ${BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedValueType} === 1}">
						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
							<Label text="Entity" labelFor="idValueTypeEntity"/>
							<ComboBox id="idValueTypeEntity" width="100%"
								selectedKey="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedEntityValue}" 
								items="{ path: 'BusinessRulesDetails>/NodeEditDetails/nodeEditEntityList', sorter: { path: 'UsmdEntity' } }"
								selectionChange="NodeEditDialog.onNodeEditEntitySelectionChange" >
								<c:ListItem key="{BusinessRulesDetails>UsmdEntity}"
									text="{= ${BusinessRulesDetails>UsmdEntity}.concat(${BusinessRulesDetails>Txtlg}==='' ? '' : ' - '.concat(${BusinessRulesDetails>Txtlg}))}" />
							</ComboBox>
						</VBox>
				
						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
							<Label text="Attribute" labelFor="idValueTypeAttribute"/>
							<ComboBox id="idValueTypeAttribute" width="100%"
								selectedKey="{BusinessRulesDetails>/NodeEditDetails/nodeEditData/selectedAttributeValue}" 
								items="{ path: 'BusinessRulesDetails>/NodeEditDetails/nodeEditAttributeValueList', length:2000, sorter: { path: 'UsmdAttribute' } }">
								<c:ListItem key="{BusinessRulesDetails>UsmdAttribute}"
									text="{BusinessRulesDetails>UsmdAttribute} - {BusinessRulesDetails>Txtlg}"
									additionalText="{BusinessRulesDetails>Txtlg}"/>
							</ComboBox>
						</VBox>
					</HBox>
				</VBox>
			</l:content>
		</l:VerticalLayout>

		<beginButton> 
			<Button 
				id="save"
				text="Save"
				press="NodeEditDialog.onNodeEditSave"/>
		</beginButton>
		<endButton>
			<Button text="Cancel" press="NodeEditDialog.onNodeEditCancel" id="cancel"/>
		</endButton>

	</Dialog>
	
</c:FragmentDefinition>