<mvc:View 
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.BusinessRules.BusinessRules"
	xmlns:mvc="sap.ui.core.mvc"
	xmlns:c="sap.ui.core"
	xmlns="sap.m"
	xmlns:f="sap.f"
	xmlns:fb="sap.ui.comp.filterbar"
	xmlns:svm="sap.ui.comp.smartvariants">

	<Page 
		title=""
		titleLevel="H1"
		showNavButton="true"
		navButtonPress="onBackPress">

		<headerContent>
			<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.shared.MainMenu"/>
		</headerContent>

		<content>
			
			<f:DynamicPage>
				
				<f:title>
					<f:DynamicPageTitle>
						<f:breadcrumbs>
							<Breadcrumbs>
								<Link text="{i18n>common.home.title}" press=".onClickLinkHome"/>
								<Link text="{i18n>businessRules.title}" press=".onClickLinkThisPage"/>
							</Breadcrumbs>
						</f:breadcrumbs>
						<f:heading>
							<Title text="{i18n>businessRules.title}"/>
						</f:heading>
						<f:expandedContent>
							<Label text="{i18n>businessRules.listPage.title}"/>
						</f:expandedContent>
					</f:DynamicPageTitle>
				</f:title>
			
				<f:header>
					<f:DynamicPageHeader pinnable="false">
						
						<f:content>
							<RadioButtonGroup id="rbRulesBy" columns="2" selectedIndex="{BusinessRules>/RulesBy}" select=".onSelectRulesBy">
								<RadioButton text="{i18n>businessRules.listPage.findBy.Entity}" />
								<RadioButton text="{i18n>businessRules.listPage.findBy.stepType}" />
							</RadioButtonGroup>
							<fb:FilterBar id="filterbar" persistencyKey="myPersKey" useToolbar="false" search=".onSearch" filterContainerWidth="12rem" defaultSpan="XL12 L12 M12 S12" showFilterConfiguration="false">
								<fb:filterGroupItems>
									
									<fb:FilterGroupItem name="fgiDataModelFilter" label="{i18n>businessRules.common.dataModel.title}" groupName="FiltersGroup" visibleInFilterBar="true">
										<fb:control>
											<ComboBox
												id="cmbDataModelFilter"
												items="{ path: 'BusinessRules>/DataModelsList', length: 2000 }"  
												selectedKey="{BusinessRules>/DataModelFilter}"
												change=".onChangeDataModelFilter">
												<c:ListItem
													key="{BusinessRules>DataModelId}" 
													text="{BusinessRules>DataModelId} - {BusinessRules>DataModelDescription}"/>
											</ComboBox>
										</fb:control>
									</fb:FilterGroupItem>
									
									<fb:FilterGroupItem name="fgiCRTypeFilter" label="{i18n>businessRules.common.CRType.title}" groupName="FiltersGroup" visibleInFilterBar="true">
										<fb:control>
											<ComboBox
												id="cmbCRTypeFilter"
												items="{ path: 'BusinessRules>/CRTypesList', length: 2000 }"  
												selectedKey="{BusinessRules>/CRTypeFilter}"
												change=".onChangeCRTypeFilter">
												<c:ListItem
													key="{BusinessRules>CRTypeId}" 
													text="{BusinessRules>CRTypeId} - {BusinessRules>CRTypeDescription}"/>
											</ComboBox>
										</fb:control>
									</fb:FilterGroupItem>
									
									<fb:FilterGroupItem name="fgiEntityFilter" label="{i18n>businessRules.common.entity.title}" groupName="FiltersGroup" visibleInFilterBar="true" visible="{= ${BusinessRules>/RulesBy} === 0}">
										<fb:control>
											<ComboBox
												id="cmbEntityFilter"
												items="{ path: 'BusinessRules>/EntitiesList', length: 2000 }"  
												selectedKey="{BusinessRules>/EntityFilter}">
												<c:ListItem
													key="{BusinessRules>EntityId}" 
													text="{BusinessRules>EntityId} - {BusinessRules>EntityDescription}"/>
											</ComboBox>
										</fb:control>
									</fb:FilterGroupItem>

									<fb:FilterGroupItem name="fgiRuleTypeFilter" label="{i18n>businessRules.common.ruleType.title}" groupName="FiltersGroup" visibleInFilterBar="true" visible="{= ${BusinessRules>/RulesBy} === 0}">
										<fb:control>
											<ComboBox
												id="cmbRuleTypeFilter"
												items="{ path: 'BusinessRules>/RuleTypesList', length: 2000 }"  
												selectedKey="{BusinessRules>/RuleTypeFilter}">
												<c:ListItem
													key="{BusinessRules>RuleTypeId}" 
													text="{BusinessRules>RuleTypeId} - {BusinessRules>RuleTypeDescription}"/>
											</ComboBox>
										</fb:control>
									</fb:FilterGroupItem>

									<!-- AI Generated CheckBox -->
									<fb:FilterGroupItem name="fgiAICheckbox" groupName="FiltersGroup" visibleInFilterBar="true" visible="{= ${BusinessRules>/RulesBy} === 0}">
										<fb:control>
											<CheckBox
												id="cbAIGenerated"
												text="AI Generated"
												selected="{BusinessRules>/IsAIGenerated}"/>
										</fb:control>
									</fb:FilterGroupItem>

									<fb:FilterGroupItem name="fgiStepTypeFilter" label="{i18n>businessRules.common.crStepType.title}" groupName="FiltersGroup" visibleInFilterBar="true" visible="{= ${BusinessRules>/RulesBy} === 1}">
										<fb:control>
											<ComboBox
												id="cmbStepTypeFilter"
												items="{ path: 'BusinessRules>/StepTypesList', length: 2000 }"  
												selectedKey="{BusinessRules>/StepTypeFilter}">
												<c:ListItem
													key="{BusinessRules>StepTypeId}" 
													text="{BusinessRules>StepTypeId} - {BusinessRules>StepTypeDescription}"/>
											</ComboBox>
										</fb:control>
									</fb:FilterGroupItem>

								</fb:filterGroupItems>
							</fb:FilterBar>
						</f:content>

					</f:DynamicPageHeader>
				</f:header>

				<f:content>
					<!-- Bug 12722 - Adding the growing property to show the complete list -->
					<VBox>
						<Table 
							id="tblBusinessRules"
							items="{ path: 'BusinessRules>/SearchResults' }"
							growing="true"
							growingThreshold="50">
							<headerToolbar>
								<OverflowToolbar>
									<Title text="{i18n>businessRules.listPage.results.title}"/>
									<SearchField width="15rem" placeholder="Search by rule name" search="onSearchRuleByName" visible="{= typeof ${BusinessRules>/SearchResults} === 'object' &amp;&amp; ${BusinessRules>/SearchResults}.length > 0}" />
									<ToolbarSpacer/>
									<Button
										id="btnImport"
										text="{i18n>businessRules.listPage.import.text}"
										tooltip="{i18n>businessRules.listPage.import.tooltip}"
										press=".onClickImportRules"/>
									<Button
										id="btnAddCRStepTypeRule"
										icon="sap-icon://add"
										iconFirst="false"
										text="{i18n>businessRules.listPage.new.text}"
										type="Accept"
										tooltip="{i18n>businessRules.listPage.new.tooltip}"
										press=".onClickAddBusinessRule"
										visible="{= ${BusinessRules>/RulesBy} === 1}"/>
										
									<MenuButton
										id="menuBtnBussinesRules"
										text="{i18n>businessRules.listPage.new.text}"
										type="Accept"
										tooltip="{i18n>businessRules.listPage.new.tooltip}"
										buttonMode="Regular" useDefaultActionOnly="true"
										visible="{= ${BusinessRules>/RulesBy} === 0}">
										<menu>
											<Menu itemSelected="onEntityMenuAction">
												<MenuItem 
													id="idNewMandatoryRuleMenuItem"
													key="1" 
													text="{i18n>businessRules.common.ruleType.mandatory}" icon="sap-icon://flag" press=".onClickAddBusinessRule" />
												<MenuItem
													id="idNewValueCheckRuleMenuItem" 
													key="2"
													text="{i18n>businessRules.common.ruleType.valueCheck}" icon="sap-icon://filter-fields" press=".onClickAddBusinessRule"/>
												<MenuItem 
													id="idNewDerivationRuleMenuItem" 
													key="3"
													text="{i18n>businessRules.common.ruleType.derivationRule}" icon="sap-icon://simulate" press=".onClickAddBusinessRule" />
												<MenuItem
													id="idNewMultiValueCheckRuleMenuItem" 
													key="4"
													text="{i18n>businessRules.common.ruleType.multiValueCheck}" icon="sap-icon://filter-fields" press=".onClickAddBusinessRule"/>
												<MenuItem 
													id="idNewDerivationValuesRuleMenuItem" 
													key="5"
													text="{i18n>businessRules.common.ruleType.multiValueDerivation}" icon="sap-icon://simulate" press=".onClickAddBusinessRule" />
												
											</Menu>
										</menu>
									</MenuButton>
								</OverflowToolbar>
							</headerToolbar>
							
							<columns>
								<Column>
									<Text text="{i18n>businessRules.common.dataModel.title}" />
								</Column>
								<Column>
									<Text text="{i18n>businessRules.common.CRType.title}" />
								</Column>
								<Column visible="{= ${BusinessRules>/RulesBy} === 0}">
									<Text text="{i18n>businessRules.common.entity.title}" />
								</Column>
								<Column visible="{= ${BusinessRules>/RulesBy} === 0}">
									<Text text="{i18n>businessRules.common.ruleType.title}" />
								</Column>
								<Column visible="{= ${BusinessRules>/RulesBy} === 0}">
									<Text text="{i18n>businessRules.listPage.column.businessRule}" />
								</Column>
								<Column visible="{= ${BusinessRules>/RulesBy} === 1}">
									<Text text="{i18n>businessRules.common.crStepType.title}" />
								</Column>
								<Column hAlign="Center" width="8em">
									<Text text="Ai" />
								</Column>
								<Column hAlign="Center" width="8em">
									<Text text="Active" />
								</Column>
								<Column hAlign="Center" width="4em">
									<Text text="" />
								</Column>
								<Column hAlign="Center" width="4em">
									<Text text="" />
								</Column>
							</columns>

							<items>
								<ColumnListItem>
									<cells>
										<VBox>
											<Label text="{BusinessRules>DataModelId}"/>
											<Label text="{BusinessRules>DataModelDescription}"/>
										</VBox>
										<VBox>
											<Label text="{BusinessRules>CRTypeId}"/>
											<Label text="{BusinessRules>CRTypeDescription}"/>
										</VBox>
										<VBox visible="{= ${BusinessRules>/RulesBy} === 0}">
											<Label text="{BusinessRules>EntityId}"/>
											<Label text="{BusinessRules>EntityDescription}"/>
										</VBox>
										<VBox visible="{= ${BusinessRules>/RulesBy} === 0}">
											<Label text="{BusinessRules>RuleTypeId}"/>
											<Label text="{BusinessRules>RuleTypeDescription}"/>
										</VBox>
										<VBox visible="{= ${BusinessRules>/RulesBy} === 0}">
											<Label text="{= ${BusinessRules>UserRuleName}.length > 0 ? ${BusinessRules>UserRuleName} : ${BusinessRules>BusinessRuleName}}" tooltip="{BusinessRules>BusinessRuleName}" />
										</VBox>
										<VBox visible="{= ${BusinessRules>/RulesBy} === 1}">
											<Label text="{BusinessRules>CRStep}" />
											<Label text="{BusinessRules>CRStepDescription}"/>
										</VBox>
										<Button 
											icon="img/RDGAI-2.png" 
											iconDensityAware="false"
											enabled="false" 
											visible="{BusinessRules>AiGenerated}"
											tooltip="Ai Generated Rule"/>
										<Switch visible="{= (${BusinessRules>RuleTypeId} === '1' || ${BusinessRules>RuleTypeId} === '2' || ${BusinessRules>RuleTypeId} === '3') &amp;&amp; (${BusinessRules>AiGenerated} ? false : true)}"
											customTextOn="Yes" customTextOff="No" state="{BusinessRules>IsActive}" change=".onStatusChange">
											<customData>
												<c:CustomData key="showProgress" value="true"/>
											</customData>	
										</Switch>
										<Button
											icon="sap-icon://delete"
											tooltip="Delete Rule"
											press=".onRuleDeletePressed"
										/>
										<Button 
											id="btnEditBusinessRule"
											icon="sap-icon://feeder-arrow"
											tooltip="Show Details"
											press=".onClickEditBusinessRule"/>
									</cells>
								</ColumnListItem>
							</items>
						</Table>
						<c:ComponentContainer id="transportPackageSelectDialog" name="dmr.components.TransportPackage" async="true"
										componentCreated="onTransportPackageDialogCreated"/>
					</VBox>
				</f:content>
			</f:DynamicPage>
		
		</content>
	</Page>

</mvc:View>