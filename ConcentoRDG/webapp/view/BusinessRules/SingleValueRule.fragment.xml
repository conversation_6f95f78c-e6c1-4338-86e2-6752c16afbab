<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml" 
	xmlns:sc="sap.suite.ui.commons"
	xmlns:n="sap.suite.ui.commons.networkgraph" 
    c:require="{SingleValueRuleDialog: 'dmr/mdg/supernova/SupernovaFJ/controller/BusinessRules/SingleValueRule'}"
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.BusinessRules.BusinessRulesDetails"
	displayBlock="true">

	<Panel
		id="idValueChangeBusinessRuleForm">
	<headerToolbar>
		<Toolbar class="transparentBar">
			<Label text="Value Check Rule" visible="{= ${BusinessRulesDetails>/selectedBusinessRule/RuleTypeId} === '2'}"/>
			<Label text="Derivation Rule" visible="{= ${BusinessRulesDetails>/selectedBusinessRule/RuleTypeId} === '3'}"/>
		 	<ToolbarSpacer/>
		 	
			<ToolbarSpacer/>
		 	
			<Label text="Rule Active"></Label>
			<Switch 
				name="ruleEnabledSwitch"
				id="ruleEnabledSwitch"
				type="AcceptReject" 
				state="{BusinessRulesDetails>/valueCheck/ruleEnabled}"
				enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}"
				change="SingleValueRuleDialog.onRuleEnableChanged">
			</Switch>	
			<ToolbarSpacer/>
		 	<Button 
		 		visible="{= ${BusinessRulesDetails>/valueCheck/editForm} !== true}"
		 		text="Edit" icon="sap-icon://edit"
		 		press="SingleValueRuleDialog.onPressEditValueCheckFlow"/>
		</Toolbar>
	</headerToolbar>
	<content>
	<VBox class="sapUiContentPadding" alignItems="Center" >
	<!-- Task 12487 - Add User Rule Name to Rules/Properties. Content moved to parent page. -->
		<HBox>
			<VBox class="sapUiSmallMarginEnd">
				<Label text="Attribute" labelFor="idValueCheckSelectAttribute"/>
				<ComboBox 
					id="idValueCheckSelectAttribute"  
					enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}" 
					selectedKey="{BusinessRulesDetails>/valueCheck/selectedAttributeName}"
					items="{ path: 'BusinessRulesDetails>/valueCheck/attributeList', length:2000, sorter: { path: 'UsmdAttribute' } }" 
					width="100%" change="SingleValueRuleDialog.onChangeAttribute">
					<c:ListItem key="{BusinessRulesDetails>UsmdAttribute}"
						text="{BusinessRulesDetails>UsmdAttribute} - {BusinessRulesDetails>Txtlg}"
						additionalText="{BusinessRulesDetails>Txtlg}"/>
				</ComboBox>
			</VBox>
			<VBox>
				<Label text="Check expression" labelFor="idValueCheckSelectExpression"/>
				<HBox class="sapUiSmallMarginEnd">
					<!-- Task 10260 - Provide a better User interface for the SIngle value derivation (Check Expression) -->
					<ComboBox 
						id="idValueCheckSelectExpression"
						class="sapUiSmallMarginEnd"
						selectedKey="{BusinessRulesDetails>/valueCheck/checkExpressionType}"
						value="{BusinessRulesDetails>/valueCheck/checkExpression}"
						valueState="{BusinessRulesDetails>/valueCheck/checkExpressionValueState}"
						valueStateText="{BusinessRulesDetails>/valueCheck/checkExpressionValueStateText}"
						enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true &amp;&amp; ${BusinessRulesDetails>/valueCheck/selectedAttributeName} !== undefined}"
						change="SingleValueRuleDialog.onChangeCheckExpression">
						<items>
							<c:Item key="1" text="&lt;1&gt; or &lt;2&gt;"/>
							<c:Item key="2" text="&lt;1&gt; and &lt;2&gt;"/>
							<c:Item key="3" text="&lt;1&gt; or &lt;2&gt; or &lt;3&gt;"/>
							<c:Item key="4" text="(&lt;1&gt; and &lt;2&gt;) or &lt;3&gt;"/>
							<c:Item key="5" text="(&lt;1&gt; or &lt;2&gt;) and &lt;3&gt;"/>
							<c:Item key="6" text="&lt;1&gt; and &lt;2&gt; and &lt;3&gt;"/>
							<!-- Task 10260 - Provide a better User interface for the SIngle value derivation (Check Expression) -->
							<c:Item key="99" text="User-defined"/>
						</items>
					</ComboBox>
					<Button 
						id="idValueCheckGenerateFlow"
						enabled="{= ${BusinessRulesDetails>/valueCheck/checkExpressionType} !== undefined}"
						visible="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}"
						icon="sap-icon://process" tooltip="Generate Flow" 
						press="SingleValueRuleDialog.onPressGenerateValueCheckFlow"/>
				</HBox>
			</VBox>
		</HBox>

		<VBox width="{= ${BusinessRulesDetails>/valueCheck/processFlowNodesCount} > 5?'100%':''}" >
			<sc:ProcessFlow 
				id="idValueCheckProcessFlow" 
				showLabels="true" scrollable="true" foldedCorners="true" wheelZoomable="true" 
				nodePress="SingleValueRuleDialog.onNodePressValueCheckProcessFlow"
				nodes="{/nodes}" lanes="{/lanes}">
				<!-- labelPress="onLabelPress" -->
				<sc:nodes>
					<sc:ProcessFlowNode laneId="{lane}" nodeId="{id}" title="{title}" titleAbbreviation="{titleAbbreviation}"
						children="{path:'children', formatter:'SingleValueRuleDialog.formatConnectionLabels'}" 
						state="{state}" stateText="{stateText}" texts="{texts}"
						highlighted="{highlighted}" focused="{focused}" type="{type}"/>
				</sc:nodes>
				<sc:lanes>
					<sc:ProcessFlowLaneHeader laneId="{id}" iconSrc="{icon}" text="{label}" position="{position}"/>
				</sc:lanes>
			</sc:ProcessFlow>
		</VBox>
		
		<Panel  visible="{= ${BusinessRulesDetails>/selectedBusinessRule/RuleTypeId} === '2'}">
			<VBox>
				<Label text="Message Type" labelFor="idValueCheckMessageTypeRadioBtn"/>
				<RadioButtonGroup 
					selectedIndex="{BusinessRulesDetails>/valueCheck/messageType}"
					enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}"
					id="idValueCheckMessageTypeRadioBtn" columns="3" 
					class="sapUiMediumMarginBottom">
					<RadioButton text="Information" selected="true" />
					<RadioButton text="Warning" />
					<RadioButton text="Error" />
				</RadioButtonGroup>
			</VBox>
			<VBox>
				<Label text="Message" labelFor="idValueCheckMessageRadioBtn"/>
				<RadioButtonGroup 
					selectedIndex="{BusinessRulesDetails>/valueCheck/message}"
					enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}"
					id="idValueCheckMessageRadioBtn" columns="2"
					class="sapUiMediumMarginBottom">
					<RadioButton text="Message Class" />
					<RadioButton text="Custom Message" />
				</RadioButtonGroup>
			</VBox>
			<HBox  wrap="Wrap" alignContent="SpaceBetween" alignItems="End" fitContainer="true">
				<VBox class="sapUiSmallMarginEnd" 
				visible="{= ${BusinessRulesDetails>/valueCheck/messageType} !== -1 &amp;&amp; ${BusinessRulesDetails>/valueCheck/message} === 0}">
					<Label text="Message Class" labelFor="idValueCheckMessageClass"/>
					<Input 
						value="{BusinessRulesDetails>/valueCheck/messageClassValue}" 
						id="idValueCheckMessageClass" showSuggestion="true" 
						required="true"
						valueState="{= ${BusinessRulesDetails>/valueCheck/messageClassValue}.length > 0 ? 'None' : 'Error'}"
						valueStateText="The field cannot be empty"
						liveChange="SingleValueRuleDialog.onLiveChangeMessageClass"
						suggestionItems="{BusinessRulesDetails>/valueCheck/messageClassList}" 
						suggest="SingleValueRuleDialog.onSuggest"
						enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}"
						startSuggestion="1" filterSuggests="false" placeholder="Enter Message Class..."
						suggestionItemSelected="SingleValueRuleDialog.onInputSuggestionSelectedMessageClass"
						selectedKey="{BusinessRulesDetails>/valueCheck/messageClassValueKey}">
						<suggestionItems>
							<c:Item key="{BusinessRulesDetails>Arbgb}" text="{BusinessRulesDetails>Arbgb} - {BusinessRulesDetails>Stext}"/>
						</suggestionItems>
					</Input>
				</VBox>
				<VBox 
					visible="{= ${BusinessRulesDetails>/valueCheck/messageType} !== -1 &amp;&amp; ${BusinessRulesDetails>/valueCheck/message} === 0 }">
					<Label text="Message Text" labelFor="idValueCheckMessageTextCombo"/>
					<ComboBox 
						id="idValueCheckMessageTextCombo" 
						required="true"
						valueState="{= ${BusinessRulesDetails>/valueCheck/messageTextKey}.length > 0 ? 'None' : 'Error'}"
						valueStateText="The field cannot be empty"
						enabled="{= ${BusinessRulesDetails>/valueCheck/messageClassValueSelected} === true  &amp;&amp; ${BusinessRulesDetails>/valueCheck/editForm} === true}"
						selectedKey="{BusinessRulesDetails>/valueCheck/messageTextKey}"
						value="{BusinessRulesDetails>/valueCheck/messageText}"
						items="{ path: 'BusinessRulesDetails>/valueCheck/messageTextList', length: 2000, sorter: { path: 'Msgnr' } }" 
						width="100%"
						selectionChange="SingleValueRuleDialog.changeInputValueMess">
						<c:Item key="{BusinessRulesDetails>Msgnr}" text="{BusinessRulesDetails>Msgnr} - {BusinessRulesDetails>Text}"/>
					</ComboBox>
				</VBox>
		
				<VBox class="sapUiSmallMarginEnd"
					visible="{formatter: 'SingleValueRuleDialog.showPlaceHolderComponent', parts: ['BusinessRulesDetails>/valueCheck/messageText']}" >
					<c:ComponentContainer
							id="idButtonTrueFormulaMess"
							name="dmr.components.FormulaEdit"
							propagateModel="true"
							settings="{
								formulaType: 'PLACEHOLDER', 
								ruleNumber: '', 
								colName: '{BusinessRulesDetails>/valueCheck/messageTextKey}',
								value: '{BusinessRulesDetails>/valueCheck/messageText}',
								dataModel: '{BusinessRulesDetails>/selectedBusinessRule/DataModelId}',
								crType: '{BusinessRulesDetails>/selectedBusinessRule/CRTypeId}',
								entity: '{BusinessRulesDetails>/selectedBusinessRule/EntityId}',
								restrictEntitySelection: true,
								isCrossEntity: ' ',
								tableStruct: '{BusinessRulesDetails>/valueCheck/messageTextKeyPlaceholderTbl}'
							}"
							async="false"
							componentCreated="SingleValueRuleDialog.onFormulaComponentMess"/>
				</VBox>	

				<VBox class="sapUiSmallMarginEnd"
					visible="{= ${BusinessRulesDetails>/valueCheck/messageType} !== -1 &amp;&amp; ${BusinessRulesDetails>/valueCheck/message} === 1 }">
					<Label text="Custom Message" labelFor="idValueCheckCustomeMessageInput"/>
					<Input 
						id="idValueCheckCustomeMessageInput"
						required="true"
						valueState="{= ${BusinessRulesDetails>/valueCheck/customeMessageInputValue}.length > 0 ? 'None' : 'Error'}"
						valueStateText="The field cannot be empty"
						value="{BusinessRulesDetails>/valueCheck/customeMessageInputValue}" 
						enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}"
						placeholder="Enter Custom Message..." 
						maxLength="255"
						width="800px"/>
				</VBox>
			</HBox>
		</Panel>

		<Panel  visible="{= ${BusinessRulesDetails>/selectedBusinessRule/RuleTypeId} === '3'}">
			<HBox alignContent="SpaceBetween" fitContainer="true" width="100%">
				<VBox class="sapUiLargeMarginEnd" >
					<Label text="When condition is TRUE"/>
					
					<RadioButtonGroup id="rbGroupValueTrue" columns="3"
						enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}"
						selectedIndex="{BusinessRulesDetails>/valueCheck/conditionTrue/derivationRbTrue}"
						select="SingleValueRuleDialog.onResetConditionFields">
						<buttons>
							<RadioButton id="rbValueTrue" text="Value"/>
							<RadioButton id="rbAttributeTrue" text="Attribute"/>
						</buttons>
					</RadioButtonGroup>
					
					<HBox visible="{= ${BusinessRulesDetails>/valueCheck/conditionTrue/derivationRbTrue} === 0}">
						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd"
							visible="{= ${BusinessRulesDetails>/valueCheck/ElementType} === 'Q'}">
							<Label text="Dimension" labelFor="idDerivationDimensionTrue"/>
							<ComboBox id="idDerivationDimensionTrue" placeholder="Enter Dimension..." enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}"
								selectedKey="{BusinessRulesDetails>/valueCheck/conditionTrue/selectedDimension}" 
								items="{ path: 'BusinessRulesDetails>/valueCheck/dimensionsList', sorter: { path: 'Dimension' } }"
								selectionChange="SingleValueRuleDialog.onDerivationDimensionChange">
								<c:ListItem key="{BusinessRulesDetails>Dimension}"
									text="{= ${BusinessRulesDetails>Dimension}.concat(${BusinessRulesDetails>Text}==='' ? '' : ' - '.concat(${BusinessRulesDetails>Text}))}" />
							</ComboBox>
						</VBox>
						
						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd"
							visible="{= ${BusinessRulesDetails>/valueCheck/ElementType} === 'Q' || ${BusinessRulesDetails>/valueCheck/ElementType} === 'A'}">
							<Label text="Unit" labelFor="idDerivationUnitTrue"/>
							<ComboBox id="idDerivationUnitTrue" placeholder="Enter Unit..." enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}"
								selectedKey="{BusinessRulesDetails>/valueCheck/conditionTrue/selectedUnit}" 
								items="{ path: 'BusinessRulesDetails>/valueCheck/conditionTrue/UnitsList', length: 500, sorter: { path: 'Unit' } }">
								<c:ListItem key="{BusinessRulesDetails>Unit}"
									text="{= ${BusinessRulesDetails>Unit}.concat(${BusinessRulesDetails>Text}==='' ? '' : ' - '.concat(${BusinessRulesDetails>Text}))}" />
							</ComboBox>
						</VBox>
						
						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
							<Label text="Value" labelFor="idDerivationCheckValueTrue"/>
							<!-- Task 11217 - Value Conversion and Data Check for Single value rules and properties -->
							<!-- Fix 12563 - binding expression added for valueState property -->
							<Input
								id="idDerivationCheckValueTrue"
								visible="{= ${BusinessRulesDetails>/valueCheck/ElementType} !== 'P'}" 
								enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}" 
								value="{BusinessRulesDetails>/valueCheck/conditionTrue/derivationCheckTrue}"
								maxLength="{= ${BusinessRulesDetails>/valueCheck/conditionTrue/TrueValueList}.length > 0 ? 0 : ${BusinessRulesDetails>/valueCheck/attributeLength}}"
								placeholder="Enter Value..." liveChange="SingleValueRuleDialog.onLiveChangeTrueValue"
								autocomplete="false"
								change="SingleValueRuleDialog.onChangeValue"
								showSuggestion="true" suggestionItems="{BusinessRulesDetails>/valueCheck/conditionTrue/TrueValueList}"
								startSuggestion="0"
								valueState="{= ${BusinessRulesDetails>/valueCheck/conditionTrue/TrueValueList}.length !== 0 ? 'Information' : 'None'}"
								valueStateText="Type to refresh list">
								<suggestionItems>
									<c:Item key="{BusinessRulesDetails>Key}"
										text="{= ${BusinessRulesDetails>Key}.concat(${BusinessRulesDetails>Ddtext}==='' ? '' : ' - '.concat(${BusinessRulesDetails>Ddtext}))}"/>
								</suggestionItems>
							</Input>
							<DatePicker id="idDPDerivationCheckValueTrue"
								visible="{= ${BusinessRulesDetails>/valueCheck/ElementType} === 'P'}" 
								enabled="{= ${BusinessRulesDetails>/valueCheck/conditionTrue/ebDatePicker} ? ${BusinessRulesDetails>/valueCheck/editForm} ? true : false : false}" 
								value="{parts:['BusinessRulesDetails>/valueCheck/conditionTrue/derivationCheckTrue', 'BusinessRulesDetails>/valueCheck/conditionTrue/currentDateCheck'], formatter:'.datePickerFormatter'}"
								change="SingleValueRuleDialog.changeDatePickerValue"
								placeholder="Enter Date..."
								displayFormat="M/d/yyyy"
								valueFormat="yyyyMMdd"/>
							<CheckBox
									id="idCurrentCheckBoxTrue"
									selected="{BusinessRulesDetails>/valueCheck/conditionTrue/currentDateCheck}"
									visible="{= ${BusinessRulesDetails>/valueCheck/ElementType} === 'P'}"
									select="SingleValueRuleDialog.onCurrentDateSelect"
									text="Current Date"/>
						</VBox>
					</HBox>
					
					<HBox class="sapUiSmallMarginEnd"
						visible="{= ${BusinessRulesDetails>/valueCheck/conditionTrue/derivationRbTrue} === 1}" >
						<c:ComponentContainer
							id="idButtonTrueFormulaMessDerivation"
							name="dmr.components.FormulaEdit"
							propagateModel="true"
							settings="{
								formulaType: 'FORMULA', 
								formulaTypeSelected: '{BusinessRulesDetails>/valueCheck/conditionTrue/formulaDetails/selectedType}',
								formulaText: '{BusinessRulesDetails>/valueCheck/conditionTrue/formulaDetails/formulaText}',
								ruleNumber: '', 
								colName: '{BusinessRulesDetails>/valueCheck/conditionTrue/derivationCheckTrue}',
								value: '{BusinessRulesDetails>/valueCheck/conditionTrue/derivationCheckTrue}',
								dataModel: '{BusinessRulesDetails>/selectedBusinessRule/DataModelId}',
								crType: '{BusinessRulesDetails>/selectedBusinessRule/CRTypeId}',
								entity: '{BusinessRulesDetails>/selectedBusinessRule/EntityId}',
								attribute: '{BusinessRulesDetails>/valueCheck/selectedAttributeName}',
								restrictEntitySelection: true,
								isCrossEntity: ' ',
								delimiter: '{BusinessRulesDetails>/valueCheck/conditionTrue/formulaDetails/delimiter}',
								tableStruct: '{BusinessRulesDetails>/valueCheck/conditionTrue/formulaDetails/tableDetails}'
							}"
							async="false"
							componentCreated="SingleValueRuleDialog.onFormulaComponentMess"/>
					</HBox>	

				</VBox>
				
				<VBox class="sapUiLargeMarginBegin" >
					<Label text="When condition is FALSE"/>
					
					<RadioButtonGroup id="rbGroupValueFalse" columns="3" 
						enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}"
						selectedIndex="{BusinessRulesDetails>/valueCheck/conditionFalse/derivationRbFalse}"
						select="SingleValueRuleDialog.onResetConditionFields">
						<buttons>
							<RadioButton text="Value"/>
							<RadioButton text="Attribute"/>
							<!-- Task 11803 - Remove false condition for rule if false condition is blank -->
							<RadioButton text="No Action"/>
						</buttons>
					</RadioButtonGroup>
					
					<HBox visible="{= ${BusinessRulesDetails>/valueCheck/conditionFalse/derivationRbFalse} === 0}">
						
						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd"
						visible="{= ${BusinessRulesDetails>/valueCheck/ElementType} === 'Q'}">
							<Label text="Dimension" labelFor="idDerivationDimensionFalse"/>
							<ComboBox id="idDerivationDimensionFalse" placeholder="Enter Dimension..." enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}"
								selectedKey="{BusinessRulesDetails>/valueCheck/conditionFalse/selectedDimension}" 
								items="{ path: 'BusinessRulesDetails>/valueCheck/dimensionsList', sorter: { path: 'Dimension' } }"
								selectionChange="SingleValueRuleDialog.onDerivationDimensionChange">
								<c:ListItem key="{BusinessRulesDetails>Dimension}"
									text="{= ${BusinessRulesDetails>Dimension}.concat(${BusinessRulesDetails>Text}==='' ? '' : ' - '.concat(${BusinessRulesDetails>Text}))}" />
							</ComboBox>
						</VBox>
						
						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd"
						visible="{= ${BusinessRulesDetails>/valueCheck/ElementType} === 'Q' || ${BusinessRulesDetails>/valueCheck/ElementType} === 'A'}">
							<Label text="Unit" labelFor="idDerivationUnitFalse"/>
							<ComboBox id="idDerivationUnitFalse" placeholder="Enter Unit..." enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}"
								selectedKey="{BusinessRulesDetails>/valueCheck/conditionFalse/selectedUnit}" 
								items="{ path: 'BusinessRulesDetails>/valueCheck/conditionFalse/UnitsList', length: 500, sorter: { path: 'Unit' } }">
								<c:ListItem key="{BusinessRulesDetails>Unit}"
									text="{= ${BusinessRulesDetails>Unit}.concat(${BusinessRulesDetails>Text}==='' ? '' : ' - '.concat(${BusinessRulesDetails>Text}))}" />
							</ComboBox>
						</VBox>
						<VBox class="sapUiSmallMarginTop sapUiSmallMarginEnd">
							<Label text="Value" labelFor="idDerivationCheckValueFalse"/>
							<!-- Task 11217 - Value Conversion and Data Check for Single value rules and properties -->
							<!-- Fix 12563 - binding expression added for valueState property -->
							<Input 
								id="idDerivationCheckValueFalse" 
								visible="{= ${BusinessRulesDetails>/valueCheck/ElementType} !== 'P'}" 
								enabled="{= ${BusinessRulesDetails>/valueCheck/editForm} === true}" 
								value="{BusinessRulesDetails>/valueCheck/conditionFalse/derivationCheckFalse}"
								maxLength="{= ${BusinessRulesDetails>/valueCheck/conditionFalse/FalseValueList}.length > 0 ? 0 : ${BusinessRulesDetails>/valueCheck/attributeLength}}"
								placeholder="Enter Value..." liveChange="SingleValueRuleDialog.onLiveChangeFalseValue"
								autocomplete="false"
								change="SingleValueRuleDialog.onChangeValue"
								showSuggestion="true" suggestionItems="{BusinessRulesDetails>/valueCheck/conditionFalse/FalseValueList}"
								startSuggestion="0"
								valueState="{= ${BusinessRulesDetails>/valueCheck/conditionFalse/FalseValueList}.length !== 0 ? 'Information' : 'None'}"
								valueStateText="Type to refresh list">
								<suggestionItems>
									<c:Item key="{BusinessRulesDetails>Key}"
										text="{= ${BusinessRulesDetails>Key}.concat(${BusinessRulesDetails>Ddtext}==='' ? '' : ' - '.concat(${BusinessRulesDetails>Ddtext}))}"/>
								</suggestionItems>
							</Input>
							<DatePicker id="idDPDerivationCheckValueFalse"
								visible="{= ${BusinessRulesDetails>/valueCheck/ElementType} === 'P'}" 
								enabled="{= ${BusinessRulesDetails>/valueCheck/conditionFalse/ebDatePicker} ? ${BusinessRulesDetails>/valueCheck/editForm} ? true : false : false}" 
								value="{parts:['BusinessRulesDetails>/valueCheck/conditionFalse/derivationCheckFalse', 'BusinessRulesDetails>/valueCheck/conditionFalse/currentDateCheck'], formatter:'.datePickerFormatter'}"
								change="SingleValueRuleDialog.changeDatePickerValue"
								placeholder="Enter Date..."
								displayFormat="M/d/yyyy"
								valueFormat="yyyyMMdd"/>
							<CheckBox
								id="idCurrentCheckBoxFalse"
								selected="{BusinessRulesDetails>/valueCheck/conditionFalse/currentDateCheck}"
								visible="{= ${BusinessRulesDetails>/valueCheck/ElementType} === 'P'}"
								select="SingleValueRuleDialog.onCurrentDateSelect"
								text="Current Date"/>
						</VBox>
					</HBox>
                   
					<HBox class="sapUiSmallMarginEnd"
						visible="{= ${BusinessRulesDetails>/valueCheck/conditionFalse/derivationRbFalse} === 1}" >
						<c:ComponentContainer
							id="idButtonFalseFormulaMessDerivation"
							name="dmr.components.FormulaEdit"
							propagateModel="true"
							settings="{
								formulaType: 'FORMULA', 
								formulaTypeSelected: '{BusinessRulesDetails>/valueCheck/conditionFalse/formulaDetails/selectedType}',
								formulaText: '{BusinessRulesDetails>/valueCheck/conditionFalse/formulaDetails/formulaText}',
								ruleNumber: '', 
								colName: '{BusinessRulesDetails>/valueCheck/conditionFalse/derivationCheckFalse}',
								value: '{BusinessRulesDetails>/valueCheck/conditionFalse/derivationCheckFalse}',
								dataModel: '{BusinessRulesDetails>/selectedBusinessRule/DataModelId}',
								crType: '{BusinessRulesDetails>/selectedBusinessRule/CRTypeId}',
								entity: '{BusinessRulesDetails>/selectedBusinessRule/EntityId}',
								attribute: '{BusinessRulesDetails>/valueCheck/selectedAttributeName}',
								restrictEntitySelection: true,
								isCrossEntity: ' ',
								delimiter: '{BusinessRulesDetails>/valueCheck/conditionFalse/formulaDetails/delimiter}',
								tableStruct: '{BusinessRulesDetails>/valueCheck/conditionFalse/formulaDetails/tableDetails}'
							}"
							async="false"
							componentCreated="SingleValueRuleDialog.onFormulaComponentMess"/>
					</HBox>	

				</VBox>
			</HBox>
		</Panel>
	</VBox>
	</content>
	</Panel>
</c:FragmentDefinition>