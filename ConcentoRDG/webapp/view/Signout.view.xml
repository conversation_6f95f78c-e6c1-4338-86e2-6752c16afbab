<mvc:View xmlns:table="sap.ui.table" xmlns:mvc="sap.ui.core.mvc" xmlns:u="sap.ui.unified" xmlns:c="sap.ui.core" xmlns="sap.m"
	xmlns:html="http://www.w3.org/1999/xhtml" controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Signout" displayBlock="true">
	<App id="app">
		<pages>
			<Page id="dashboard" showFooter="false" showHeader="true" showSubHeader="false" class="sapUiContentPadding dmrBackGroundImage">
				<headerContent>
					<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.shared.MainMenuLogOff"/>
				</headerContent>
				<FlexBox wrap="Wrap" width="100%" height="70%" class="sapUiSmallMargin" alignItems="Center" justifyContent="Center">
					<items>
						<html:h4 style="color:white;font-size:1.4em;text-align:center" visible="{= ${/loggedOut} === true}">
							<html:br>You have been signed out of the application.
							</html:br>
							Click on RDG logo to return.
						</html:h4>
					</items>
				</FlexBox>
			</Page>
		</pages>
	</App>
</mvc:View>