<mvc:View xmlns:mvc="sap.ui.core.mvc" xmlns:u="sap.ui.unified" xmlns:c="sap.ui.core" xmlns:m="sap.m"
	xmlns:html="http://www.w3.org/1999/xhtml" controllerName="dmr.mdg.supernova.controller.SupernovaFJ.DataModel.EntryDetails" displayBlock="true">
	<m:Page showNavButton="false" class="sapUiContentPadding" title="Entry Details" id="modelEdit">
		<m:content>
			<m:Title id="path"/>
			<m:RadioButtonGroup id="EntryGroup"/>
			<m:Button text="Insert" press="onInsertItem" id="insert"/>
		</m:content>
	</m:Page>
</mvc:View>