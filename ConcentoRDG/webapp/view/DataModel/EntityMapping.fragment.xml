<c:FragmentDefinition xmlns:mvc="sap.ui.core.mvc" xmlns:u="sap.ui.unified" xmlns:c="sap.ui.core" xmlns="sap.m" xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" xmlns:html="http://www.w3.org/1999/xhtml" displayBlock="true" xmlns:smartTable="sap.ui.comp.smarttable" xmlns:smartField="sap.ui.comp.smartfield" xmlns:smartForm="sap.ui.comp.smartform">
	<Dialog title="Entity Mapping">
		<l:VerticalLayout class="sapUiContentPadding">
			<l:content>
				<Table id="idEntityMappingTable" mode="SingleSelectMaster" alternateRowColors="true" items="{path:'entityMappingModel>/smtMappingList'}"
					sticky="HeaderToolbar,InfoToolbar,ColumnHeaders" ariaLabelledBy="title" selectionChange ="rowSelectionChange">
					<headerToolbar>
						<OverflowToolbar>
							<Title id="TblTitle" text="Entity Mapping Table"/>
							<ToolbarSpacer/>
						</OverflowToolbar>
					</headerToolbar>
					<columns>
						<Column width="10em" hAlign="Center" vAlign="Middle">
							<Label text="Modified Entity" wrapping="true"/>
						</Column>
						<Column width="10em" hAlign="Center" vAlign="Middle">
							<Label text="Application" wrapping="true"/>
						</Column>
						<Column width="10em" hAlign="Center" vAlign="Middle">
							<Label text="Mapping Step" wrapping="true"/>
						</Column>
						<Column width="10em" hAlign="Center" vAlign="Middle">
							<Label text="Source" wrapping="true"/>
						</Column>
						<Column width="10em" hAlign="Center" vAlign="Middle">
							<Label text="Target" wrapping="true"/>
						</Column>
						<Column width="10em" hAlign="Center" vAlign="Middle">
							<Label text="Structure to extend" wrapping="true"/>
						</Column>
						<Column width="10em" hAlign="Center" vAlign="Middle">
							<Label text="Status" wrapping="true"/>
						</Column>
						<Column width="3em" hAlign="Center" vAlign="Middle">
							<c:Icon src="sap-icon://edit" width="100%" tooltip="Edit"/>
						</Column>
					</columns>
					<items>
						<ColumnListItem type="Active" class="dmrTableRow">
							<cells class="sapMListTblCell dmrTableRow">
								<Label text="{entityMappingModel>Usmdentity}" wrapping="true" textAlign="Begin"/>
								<Label width="100%" text="{entityMappingModel>Application}" wrapping="true"/>
								<ComboBox id="idMappingStep" width="100%" selectedKey ="{entityMappingModel>selectedMappingStep}"
									editable = "{= ${entityMappingModel>editable} === true}"
									selectionChange="mappingStepSelectionChange" tooltip="{entityMappingModel>selectedMappingStep}"
									items="{path: 'entityMappingModel>MappingSteps/results', templateShareable: false}">
									<c:Item key="{entityMappingModel>Mappingstep}" text="{entityMappingModel>Mappingstep}"/>
								</ComboBox>
								<Label text="{entityMappingModel>SourceStructure}" wrapping="true"></Label>
								<Label text="{entityMappingModel>TargetStructure}" wrapping="true"></Label>
								<ComboBox id="idStructure" width="100%" selectedKey="{entityMappingModel>selectedStructure}"
								editable = "{= ${entityMappingModel>editable} === true}" tooltip="{entityMappingModel>selectedStructure}"
								items="{path: 'entityMappingModel>IncludeStructures', templateShareable: false}">
									<c:Item key="{entityMappingModel>Include}" text="{entityMappingModel>Include}"/>
								</ComboBox>
								<Link text="{entityMappingModel>status}" enabled="{= ${entityMappingModel>editable} === true}" press=".onPresStatus" />
								<Button icon="sap-icon://combine" tooltip="Create SMT" type="Transparent"
									enabled="{
											parts: [
												{path:'entityMappingModel>selectedMappingStep'},
												{path:'entityMappingModel>selectedStructure'},
												{path:'entityMappingModel>status'}
											], 
											formatter:'.isMappingButtonEnabled'
										}"
									press=".onPressCreateSMTForRow"/>
							</cells>
						</ColumnListItem>
					</items>
				</Table>
			</l:content>
		</l:VerticalLayout>
		<beginButton>
			<Button text="Done" enabled="{= ${entityMappingModel>/bEnableDone}}" press=".closeMappingPage" id="endMapping"/>
		</beginButton>
	</Dialog>
</c:FragmentDefinition>