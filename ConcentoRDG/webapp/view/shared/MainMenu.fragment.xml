<core:FragmentDefinition xmlns="sap.m" xmlns:core="sap.ui.core" controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Home">
	<Menu items="{ path: 'mainMenuModel>/menuItems'}">
		<items>
			<MenuItem 
				text="{mainMenuModel>title}{= ${mainMenuModel>beta} === true? ' [Beta]': ''}" 
				visible="{mainMenuModel>enabled}"
				key="{mainMenuModel>target}"
				icon="sap-icon://{mainMenuModel>icon}"
				press="onMenuItemPressed" />
		</items>
	</Menu>
</core:FragmentDefinition>