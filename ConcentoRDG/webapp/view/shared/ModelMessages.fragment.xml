<c:FragmentDefinition xmlns:mvc="sap.ui.core.mvc" xmlns:u="sap.ui.unified" xmlns:c="sap.ui.core" xmlns:m="sap.m" xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" xmlns:html="http://www.w3.org/1999/xhtml" displayBlock="true">
	<m:Dialog title= "Messages issued">
		<l:VerticalLayout class="sapUiContentPadding" width="100%">
			<l:content>
				<m:Table inset="false" items="{ path: 'messageData>/messages'}" growing="true" noDataText= "No Messages" >
					<m:headerToolbar></m:headerToolbar>
					<m:infoToolbar></m:infoToolbar>
					<m:columns>
						<m:Column width="10%">
							<m:Text text="Status"/>
						</m:Column>
					</m:columns>
					<m:items>
						<m:ColumnListItem>
							<m:cells>
								<m:GenericTag  design="Full" 
									status="{path:'messageData>MessageType', formatter:'.ModelMessages.messageStatus'}"  
									class="sapUiSmallMarginBottom">
									<m:ObjectNumber 
										state="None"  
										emphasized="{path:'messageData>MessageType', formatter:'.ModelMessages.messageEmphasis'}" 
										number="{= ${messageData>Msgno} === undefined || ${messageData>Msgno} === '' ? ${messageData>Message} : ${messageData>Message} + ' [Message No.: ' + ${messageData>Msgno} + ']'}"/>
								</m:GenericTag> 
							</m:cells>
						</m:ColumnListItem>
					</m:items>
				</m:Table>
			</l:content>
		</l:VerticalLayout>
		<m:beginButton>
			<m:Button text="Close" press=".ModelMessages.closeMessageDialog"/>
		</m:beginButton>
	</m:Dialog>
</c:FragmentDefinition>