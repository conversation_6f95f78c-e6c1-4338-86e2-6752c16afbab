<c:FragmentDefinition xmlns:mvc="sap.ui.core.mvc" xmlns:u="sap.ui.unified" xmlns:c="sap.ui.core" xmlns:m="sap.m" xmlns:l="sap.ui.layout"
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Home"
	xmlns:f="sap.ui.layout.form" xmlns:html="http://www.w3.org/1999/xhtml">

	<m:Dialog title= "{i18n>systemInformation.title}" > 	
		<f:SimpleForm
			editable="true"
			layout="ResponsiveGridLayout"
			labelSpanXL="4"
			labelSpanL="3"
			labelSpanM="4"
			labelSpanS="12"
			adjustLabelSpan="false"
			emptySpanXL="0"
			emptySpanL="4"
			emptySpanM="0"
			emptySpanS="0"
			columnsXL="2"
			columnsL="1"
			columnsM="1"
			singleContainerFullSize="false"
			ariaLabelledBy="Title1" >
			<f:content>
				<m:Label text="{i18n>systemInformation.IDLabel}" labelFor="systemId"/>
				<m:Input enabled="false" id="systemId" value="{mainMenuModel>/sysysid}"></m:Input>                
				
				<m:Label text="{i18n>systemInformation.clientLabel}" labelFor="clientId"/>
				<m:Input enabled="false" id="clientId" value="{mainMenuModel>/symandt}"></m:Input>				
							
				<m:Label text="{i18n>systemInformation.userNameLabel}" labelFor="systemUserName"/>
				<m:Input enabled="false" id="systemUserName" value="{mainMenuModel>/syuname}"></m:Input>
			</f:content>
		</f:SimpleForm>          
		<m:beginButton>
			<m:Button text="{i18n>common.closeButtonText}" press=".closeSystemInfoDialog"/>
		</m:beginButton> 
	</m:Dialog>
	
</c:FragmentDefinition>