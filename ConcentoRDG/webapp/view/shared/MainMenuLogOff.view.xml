<core:View xmlns="sap.m" xmlns:core="sap.ui.core" controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Home">
	<FlexBox alignItems="Center" justifyContent="End" fitContainer="true">
		<items>
			<FlexBox alignItems="Center" justifyContent="Start" visible="{mainMenuModel>/isHomePage}" renderType="Bare">
				<items>
					<Image height="2em" src="img/logo.svg" class="sapUiTinyMarginBegin"/>
				</items>
			</FlexBox>
			<HBox width="100%" id="hbox0"/>
			<Image height="3em" src="img/RDG_Horizontal.png" press="onHomeButtonPress" alt="RDG"/>
			<Button id="openMenu" icon="sap-icon://menu2" enabled="false" iconFirst="false" tooltip="Open Application Menu" press="onMenuOpenPress"/>
		</items>
	</FlexBox>
</core:View>