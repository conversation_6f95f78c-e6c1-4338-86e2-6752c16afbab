<mvc:View xmlns="sap.m" xmlns:mvc="sap.ui.core.mvc" xmlns:c="sap.ui.core" controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Home">
	<FlexBox alignItems="Center" justifyContent="End" fitContainer="true">
		<items>
			<FlexBox alignItems="Center" justifyContent="Start" visible="{mainMenuModel>/isHomePage}"  renderType="Bare">
				<items>
					<Image height="2em" src="img/logo.svg" alt="syniti.com" class="sapUiTinyMarginBegin" tooltip="Syniti" />
				</items>
			</FlexBox>
			<HBox width="100%" id="hbox0"/>
			<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.shared.rdgAI"/>
			<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.Notifications.Notifications"/>
			<Image height="3em" src="img/RDG_Horizontal.png" press="onHomeButtonPress" alt="RDG" tooltip="RDG"/>
			<Button id="openMenu" icon="sap-icon://menu2" iconFirst="false" tooltip="Open Application Menu" press="onMenuOpenPress"/>
		</items>
	</FlexBox>
</mvc:View>