<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:customdata="sap.ui.core.CustomData"
	xmlns:c="sap.ui.core" 
	xmlns:m="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true">
	<m:Dialog title="Business System">
		<l:VerticalLayout width="100%" class="sapUiContentPadding">
			<l:content>
				<l:Grid containerQuery="true" defaultSpan="XL2 L4">
					<m:VBox class="sapUiTinyMarginEnd">
						<m:Label labelFor="idBusSystem" required="true" text="Business System"/>
						<m:Input
							id="idBusSystem"
							value="{viewInterface>/editBusSystems/Bussystem}" valueLiveUpdate="true"
							valueState="{= ${viewInterface>/editBusSystems/Bussystem} ? 'None' : 'Error' }"
							valueStateText="Business System must not be empty."
							change=".BusinessSystems.onBusSystemChange"
							showSuggestion="{= ${viewInterface>/arrBusinessSystem}.length !== 0}" startSuggestion="1"
							suggestionItems="{viewInterface>/arrBusinessSystem}" filterSuggests="false">
							<m:suggestionItems>
								<c:Item text="{viewInterface>Bussystem}"/>
							</m:suggestionItems>
						</m:Input>
					</m:VBox>
					
					<m:VBox class="sapUiTinyMarginEnd">
						<m:Label labelFor="idLogSystem" text="Logical System"/>
						<m:Select forceSelection="false" id="idLogSystem"
							selectedKey="{viewInterface>/editBusSystems/Logsys}" 
							change=".BusinessSystems.onLogicalSystemChange" items="{path:'viewInterface>/SearchHelps/arrLogicalSystem', templateShareable:false }" >
							<c:Item key="{viewInterface>Logsys}" 
										text="{= ${viewInterface>Logsys}.concat(${viewInterface>Description}==='' ? '' : ' - '.concat(${viewInterface>Description}))}"/>
						</m:Select>
					</m:VBox>
					
					<m:VBox class="sapUiTinyMarginEnd">
						<m:Label labelFor="idRfcdest" text="RFC Destination"/>
						<m:Select forceSelection="false" id="idRfcdest" width="215px"
							selectedKey="{viewInterface>/editBusSystems/Rfcdest}" items="{path:'viewInterface>/SearchHelps/arrRfcDestination', templateShareable:false }" >
							<c:Item key="{viewInterface>Rfc}" 
										text="{= ${viewInterface>Rfc}.concat(${viewInterface>Description}==='' ? '' : ' - '.concat(${viewInterface>Description}))}"/>
						</m:Select>
					</m:VBox>
					
					<m:VBox class="sapUiTinyMarginEnd">
						<m:Label labelFor="idFilePath" text="Logical File Path"/>
						<!-- Bug 11292 - Logical file path to be changed from input to select box  -->
						<m:Select forceSelection="false" id="idFilePath" selectedKey="{viewInterface>/editBusSystems/Filepath}"
							items="{path:'viewInterface>/SearchHelps/arrLogicalFilePath', templateShareable:false, length: 1500 }" >
							<c:Item key="{viewInterface>LogicalPath}" 
										text="{= ${viewInterface>LogicalPath}.concat(${viewInterface>Description}==='' ? '' : ' - '.concat(${viewInterface>Description}))}"/>
						</m:Select>
					</m:VBox>
					
					<m:VBox class="sapUiTinyMarginEnd">
						<m:Label labelFor="idStorageServ" text="Download to PS"/>
						<m:CheckBox id="idStorageServ" textAlign="Left" 
						selected="{viewInterface>/editBusSystems/Defstorageserv}"/>
					</m:VBox>
					
					<m:VBox class="sapUiTinyMarginEnd">
						<m:Label labelFor="idDisabled" text="Disabled for Replication"/>
						<m:CheckBox id="idDisabled" textAlign="Left" 
						selected="{viewInterface>/editBusSystems/Disabled}"/>
					</m:VBox>
					
					<m:VBox class="sapUiTinyMarginEnd">
						<m:Label labelFor="idUnicode" text="Unicode"/>
						<m:CheckBox id="idUnicode" textAlign="Left" 
						selected="{viewInterface>/editBusSystems/Unicodesystem}"/>
					</m:VBox>
					
					<m:VBox class="sapUiTinyMarginEnd">
						<m:Label labelFor="idUnicodePage" text="Unicode Code Page"/>
						<m:Select forceSelection="false" id="idUnicodePage" enabled="{= ${viewInterface>/editBusSystems/Unicodesystem} === true}"
							selectedKey="{viewInterface>/editBusSystems/Unicodecodepag}"
							items="{path:'viewInterface>/SearchHelps/arrUnicode', templateShareable:false, length: 1000 }" >
							<c:Item key="{viewInterface>Cpcodepage}" 
										text="{= ${viewInterface>Cpcodepage}.concat(${viewInterface>Cpcomment}==='' ? '' : ' - '.concat(${viewInterface>Cpcomment}))}"/>
						</m:Select>
					</m:VBox>
				</l:Grid>

				
				<m:ScrollContainer horizontal="true" vertical="true">
					<m:Table id="idBusObjectTable" alternateRowColors="true" items="{path:'viewInterface>/editBusSystems/arrBusObject'}" 
					sticky="HeaderToolbar,InfoToolbar,ColumnHeaders" mode="Delete" delete=".BusinessSystems.onDeleteBusObject" fixedLayout="false">
						<m:headerToolbar>
							<m:OverflowToolbar>
								<m:Title class="sapUiLargeMarginEnd" id="idTblBusObject" text="Business Objects"/>
								<m:ToolbarSpacer/>
								<m:Button icon="sap-icon://add" text="ADD" id="addBusObject" press=".BusinessSystems.onAddBusinessObject"/>
							</m:OverflowToolbar>
						</m:headerToolbar>
						<m:columns>
							<m:Column hAlign="Center" vAlign="Middle">
								<m:Label text="Business Object Type" required="true" wrapping="false" />
							</m:Column>
							<m:Column  hAlign="Center" vAlign="Middle" >
								<m:Label text="System Filter" wrapping="true" />
							</m:Column>
							<m:Column  hAlign="Center" vAlign="Middle" >
								<m:Label text="Output Mode" wrapping="true" />
							</m:Column>
							<m:Column hAlign="Center" vAlign="Middle">
								<m:Label text="Comm. Channel" required="true" wrapping="true" />
							</m:Column>
							<m:Column  hAlign="Center" vAlign="Middle">
								<m:Label text="Key Harmonization" wrapping="true" />
							</m:Column>
							<m:Column  hAlign="Center" vAlign="Middle">
								<m:Label text="Key Mapping" wrapping="true" />
							</m:Column>
							<m:Column  hAlign="Center" vAlign="Middle">
								<m:Label text="Storage" wrapping="true" />
							</m:Column>
							<m:Column hAlign="Center" vAlign="Middle">
								<m:Label text="Time Dependency" wrapping="true" />
							</m:Column>
							<m:Column  hAlign="Center" vAlign="Middle">
								<m:Label text="MDC Create Template" wrapping="true" />
							</m:Column>
							<m:Column  hAlign="Center" vAlign="Middle">
								<m:Label text="MDC Change Template" wrapping="true" />
							</m:Column>
							<m:Column  hAlign="Center" vAlign="Middle">
								<m:Label text="MDC Reliable" wrapping="true" />
							</m:Column>
						</m:columns>
						<m:items>
							<m:ColumnListItem  type="Active" class="dmrTableRow">
								<m:cells class="sapMListTblCell dmrTableRow" >
									<m:ComboBox id="idBoType" selectedKey="{viewInterface>Busobject}" 
										items="{path:'viewInterface>/arrBusinessObjects', templateShareable:false, length: 2000}"
										change=".BusinessSystems.onChangeBusObject" valueStateText="Business Object Type must not be empty" 
										valueState="{= !${viewInterface>Busobject} ? 'Error' : 'None'}">
										<c:Item 
											key="{viewInterface>Busobject}"
											text="{= ${viewInterface>Busobject}.concat(${viewInterface>Description}==='' ? '' : ' - '.concat(${viewInterface>Description}))}"/>	
									</m:ComboBox>
								</m:cells>
								<m:cells class="sapMListTblCell dmrTableRow" >
									<m:CheckBox id="idSysFilter" textAlign="Left" 
										selected="{viewInterface>Sysfilterstat}"/>
								</m:cells>
								<m:cells class="sapMListTblCell dmrTableRow" >
									<m:Select forceSelection="false" items="{path:'viewInterface>/SearchHelps/arrOutputMode', templateShareable:false }" 
										id="idOutputMode" selectedKey="{viewInterface>Mdgoutputmode}">
										<c:Item key="{viewInterface>Outputmode}" 
												text="{= (${viewInterface>Outputmode}=== 'X' ? '' : ${viewInterface>Outputmode}).concat(${viewInterface>Description}==='' ? '' : ' - '.concat(${viewInterface>Description}))}"/>
									</m:Select>
								</m:cells>
								<m:cells class="sapMListTblCell dmrTableRow" >
									<m:Select forceSelection="false" items="{path:'viewInterface>/SearchHelps/arrCommChannel', templateShareable:false }" 
										id="idCommChannel" selectedKey="{viewInterface>Commchannel}"
										valueStateText="Communication Channel must not be empty" 
										valueState="{= !${viewInterface>Commchannel} ? 'Error' : 'None'}">
										<c:Item key="{viewInterface>Commchannel}" 
												text="{= ${viewInterface>Commchannel}.concat(${viewInterface>Description}==='' ? '' : ' - '.concat(${viewInterface>Description}))}"/>
									</m:Select>
								</m:cells>
								<m:cells class="sapMListTblCell dmrTableRow" >
									<m:Select forceSelection="false" items="{path:'viewInterface>/SearchHelps/arrKeyHarm', templateShareable:false }" 
										id="idKeyharm" selectedKey="{viewInterface>Keyharm}">
										<c:Item key="{viewInterface>Keyharmvalue}"
										text="{= (${viewInterface>Keyharmvalue}=== 'X' ? '' : ${viewInterface>Keyharmvalue}).concat(${viewInterface>Description}==='' ? '' : ' - '.concat(${viewInterface>Description}))}"/>
									</m:Select>
								</m:cells>
								<m:cells class="sapMListTblCell dmrTableRow" >
									<m:CheckBox id="idKeyMap" textAlign="Left" 
										selected="{viewInterface>Keymap}"/>
								</m:cells>
								<m:cells class="sapMListTblCell dmrTableRow" >
									<m:Select forceSelection="false" items="{path:'viewInterface>/SearchHelps/arrStorageLocation', templateShareable:false }" 
										id="idStorage" selectedKey="{viewInterface>Storageloc}">
										<c:Item key="{viewInterface>Storagevalue}"
											text="{= (${viewInterface>Storagevalue}=== 'X' ? '' : ${viewInterface>Storagevalue}).concat(${viewInterface>Description}==='' ? '' : ' - '.concat(${viewInterface>Description}))}"/>
									</m:Select>
								</m:cells>
								<m:cells class="sapMListTblCell dmrTableRow" >
									<m:Select forceSelection="false" items="{path:'viewInterface>/SearchHelps/arrTimeDependency', templateShareable:false }" 
										id="idTimeDep" selectedKey="{viewInterface>Timedependency}">
										<c:Item key="{viewInterface>Timedepvalue}"
											text="{= (${viewInterface>Timedepvalue}=== 'X' ? '' : ${viewInterface>Timedepvalue}).concat(${viewInterface>Description}==='' ? '' : ' - '.concat(${viewInterface>Description}))}"/>
									</m:Select>
								</m:cells>
								
								<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Select forceSelection="false" items="{path:'viewInterface>arrTemplate', templateShareable:false }" 
											id="idCrTemplate" selectedKey="{viewInterface>Mdctemplatecreate}">
											<c:Item key="{viewInterface>Type}"
													text="{= ${viewInterface>Type}.concat(${viewInterface>Descr}==='' ? '' : ' - '.concat(${viewInterface>Descr}))}"/>
										</m:Select>
								</m:cells>
								<m:cells class="sapMListTblCell dmrTableRow" >
									<m:Select forceSelection="false" items="{path:'viewInterface>arrTemplate', templateShareable:false }" 
										id="idChTemplate" selectedKey="{viewInterface>Mdctemplatechange}">
										<c:Item key="{viewInterface>Type}"
												text="{= ${viewInterface>Type}.concat(${viewInterface>Descr}==='' ? '' : ' - '.concat(${viewInterface>Descr}))}"/>
									</m:Select>
								</m:cells>
								<m:cells class="sapMListTblCell dmrTableRow" >
									<m:CheckBox id="idReliable" textAlign="Left" 
										selected="{viewInterface>Mdcprocessreliable}"/>
								</m:cells>
							</m:ColumnListItem>
						</m:items>
					</m:Table>
				</m:ScrollContainer>
			</l:content>
		</l:VerticalLayout>
		<m:beginButton>
			<m:Button 
				text="Save" press=".BusinessSystems.onBusSystemsSave" enabled="{= ${viewInterface>/editBusSystems/Bussystem} !== ''}" id="idSaveBusSystem"/>
		</m:beginButton>
		<m:endButton>
			<m:Button text="Cancel" press=".BusinessSystems.onBusSystemsCancel" id="idCancelBusSystem"/>
		</m:endButton>
	</m:Dialog>
</c:FragmentDefinition>