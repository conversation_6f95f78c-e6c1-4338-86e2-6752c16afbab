<c:FragmentDefinition
	xmlns:mvc="sap.ui.core.mvc"
	xmlns:c="sap.ui.core" 
	xmlns="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true"
>
	<Dialog title="Create Logical System">
		<l:VerticalLayout class="sapUiContentPadding" width="100%">
			<l:content>
				<VBox>
				<VBox>
					<Label required="true" text="Logical System"/>
					<HBox>
						<Input value="{viewInterface>/LogicalSysDetailsFragment/LogicalSys}"
							valueState="{= ${viewInterface>/LogicalSysDetailsFragment/LogicalSys} ? 'None' : 'Error' }"
							valueStateText="Logical system should not be empty"
							/>
					</HBox>
				</VBox>
				<VBox>
					<Label text="Description"></Label>
					<Input value="{viewInterface>/LogicalSysDetailsFragment/Desc}"/>
				</VBox>
				</VBox>
			</l:content>
		</l:VerticalLayout>
		
		<beginButton>
			<Button 
				text="Create" enabled= "{= ${viewInterface>/LogicalSysDetailsFragment/LogicalSys} ? true : false }"
				press=".LogicalSystemCreate.onCreateLogicalSystem" id="BtnLogicalSysCreate"/>
		</beginButton>
		<endButton>
			<Button
				text="Cancel"
				press=".LogicalSystemCreate.onCancelLogicalSystem" id="BtnLogicalSysCancel"/>
		</endButton>
	
	</Dialog>
</c:FragmentDefinition>