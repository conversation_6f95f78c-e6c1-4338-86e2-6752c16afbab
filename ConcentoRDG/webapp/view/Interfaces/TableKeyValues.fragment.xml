<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns:customdata="sap.ui.core.CustomData"
	xmlns:c="sap.ui.core" 
	xmlns:m="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true">
	<m:Dialog title="Key Values">
		<l:VerticalLayout width="100%" class="sapUiContentPadding">
			<l:content>
				<m:List id="TableKeyValuesList" sticky="HeaderToolbar"
					items="{path: 'viewInterface>/mappedValue/arrKeyValueList'}">
					<m:CustomListItem>
							<m:VBox class="sapUiTinyMarginBegin">
								<m:Label class="sapUiTinyMarginTop" text="{viewInterface>Text}" required="true"/>
								<m:HBox>
									<!-- Feature 11655 - Set Mapping Key from Mapping Table -->
									<!-- ComboBox visible when dropdown list is available and user has not selected mapping from mapping tree table -->
									<!-- Input Box visible when dropdown list is not available or user has selected mapping from mapping tree table -->
									<!-- Input Box disabled when value is * or mapping is selected from mapping tree table -->
									<m:ComboBox width="100%" id="idComboBoxKeyField"
										selectedKey="{viewInterface>Value}" 
										visible="{formatter: '.TableKeyValues.isComboBoxKeyFieldVisible', parts: ['viewInterface>arrKeyValues', 'viewInterface>oMappingKeyInfo', 'viewInterface>Kind']}"
										items="{path: 'viewInterface>arrKeyValues', templateShareable:false, length: 2000}" enabled="{= ${viewInterface>Value} !== '*'}">
										<c:Item key="{viewInterface>Key}"
												text="{= ${viewInterface>Key}.concat(${viewInterface>Ddtext}.length > 0 ? ' - ': '').concat(${viewInterface>Ddtext})}"/>
									</m:ComboBox>

									<m:Input width="100%" id="idInputKeyField"
										valueLiveUpdate="true" enabled="{= ${viewInterface>Value} !== '*' &amp;&amp; ${viewInterface>oMappingKeyInfo} === undefined}"
										tooltip="{formatter: '.TableKeyValues.addMappingInputInformation', parts: ['viewInterface>oMappingKeyInfo']}"
										value="{viewInterface>Value}" 
										visible="{formatter: '.TableKeyValues.isInputKeyFieldVisible', parts: ['viewInterface>arrKeyValues', 'viewInterface>oMappingKeyInfo', 'viewInterface>Kind']}">
									</m:Input>

									<!-- Task 11511 - Add Date and Time Picker for relevant key fields -->
									<m:DatePicker id="idDateKeyField" value="{viewInterface>Value}" valueFormat="ddMMyyyy"
										visible="{formatter: '.TableKeyValues.isDateKeyFieldVisible', parts: ['viewInterface>oMappingKeyInfo', 'viewInterface>Kind']}"/>

									<m:TimePicker id="idTimeKeyField" value="{viewInterface>Value}" maskMode="On" valueFormat="HHmmSS"
										visible="{formatter: '.TableKeyValues.isTimeKeyFieldVisible', parts: ['viewInterface>oMappingKeyInfo', 'viewInterface>Kind']}"/>
									
									<!-- Feature 11655 - Set Mapping Key from Mapping Table -->
									<!-- Set Mapping Button added to allow user to select mapping from mapping tree table -->
									<m:Button class="sapUiTinyMarginBegin" id="idButtonSetMappingKey" iconFirst="true" icon="sap-icon://key" tooltip="Set Mapping"  
										visible="{= ${viewInterface>/selectedInterface/Commchannel} === '1' &amp;&amp; ${viewInterface>Value} !== '*'}" press=".TableKeyValues.onClickSetMappingKey"/>
									
									<!-- Feature 11655 - Set Mapping Key from Mapping Table -->
									<!-- Clear Mapping Button added to allow user to clear selected mapping from mapping tree table -->
									<!-- Clear Mapping Button visible only when user has selected mapping from mapping taable -->
									<m:Button class="sapUiTinyMarginBegin" id="idButtonClearMappingKey" iconFirst="true" icon="sap-icon://decline" tooltip="Clear Mapping" visible="{= ${viewInterface>oMappingKeyInfo} !== undefined}" 
										press=".TableKeyValues.onClickClearMappingKey"/>
								</m:HBox>
							</m:VBox>
					</m:CustomListItem>
				</m:List>
			</l:content>
		</l:VerticalLayout>
		<m:beginButton>
			<m:Button 
				text="Save" press=".TableKeyValues.onKeyValueSave" id="idSaveKeyValues"/>
		</m:beginButton>
		<m:endButton>
			<m:Button text="Cancel" press=".TableKeyValues.onKeyValueCancel" id="idCancelKeyValues"/>
		</m:endButton>
	</m:Dialog>
</c:FragmentDefinition>