<mvc:View xmlns:mvc="sap.ui.core.mvc" xmlns:u="sap.ui.unified" xmlns:l="sap.ui.layout" xmlns:c="sap.ui.core" xmlns:m="sap.m"
	xmlns:f="sap.ui.layout.form" xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Interfaces.IdocExtensionWizard" displayBlock="true">
	<m:Page showNavButton="true" showHeader="true" navButtonPress="onBackPress" title="Extension Details" id="idocExtensionWizardPage">
		<m:headerContent>
			<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.shared.MainMenu"/>
		</m:headerContent>
		<m:content>
			<!-- Create an invisible component container for the Transport Selection Dialog -->
			<c:ComponentContainer id="iDocExtTransportPackageDialog" name="dmr.components.TransportPackage" async="true" componentCreated="onTransportPackageSelectionDialogCreated"/>
			<m:Wizard id="idocExtensionWizard" enableBranching="true" class="sapUiResponsivePadding--header sapUiResponsivePadding--content">
				<m:WizardStep id="SelectExtensionTypeStep" title="Select Extension Type" validated="false" nextStep="CreateExtensionStep" subsequentSteps="CreateExtensionStep, CreateEditSegmentStep">
					<m:RadioButtonGroup id="rbGroupSelectExtensionType" selectedIndex="-1" select="onWizardSelectExtensionType">
						<m:buttons>
							<m:RadioButton id="rbCreateNew" text="New" enabled="{= ${idocExtensionWizardDetails>/idocAction} === 'addExtension'}"/>
							<m:RadioButton id="rbCreateCopy" text="Copy" enabled="{= ${idocExtensionWizardDetails>/idocAction} === 'addExtension'}" />
							<m:RadioButton id="rbCreateSuccessor" text="Successor" enabled="{= ${idocExtensionWizardDetails>/idocAction} === 'addExtension'}"/>
						</m:buttons>					
					</m:RadioButtonGroup>
				</m:WizardStep>
				<!-- Bug 11159 - Extension "COPY" option doesn't work -->
				<m:WizardStep id="CreateExtensionStep" validated="false" activate="onActivateStep2">
					<l:VerticalLayout id="wizardE" width="100%" class="gridWrapper">
						<m:GenericTag text="Extension Details" status="None" design="StatusIconHidden" class="sapUiSmallMarginBottom"/>
						<l:Grid containerQuery="true" defaultSpan="XL2 L4">
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Extension Name" labelFor="extnName"/>
								<m:Input id="extnName" required="true" liveChange="onLiveChangeExtension" enabled="{= ${idocExtensionWizardDetails>/idocAction} === 'addExtension'}" placeholder="Enter extension name..." maxLength="8"
									valueState="{= ${idocExtensionWizardDetails>/Extension/extensionNameValueState}}"
									valueStateText="{= ${idocExtensionWizardDetails>/Extension/extensionNameValueStateText}}"
									value="{idocExtensionWizardDetails>/Extension/extensionName}"></m:Input>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd" visible="{= ${idocExtensionWizardDetails>/sSelectedExtnType} === 'COPY'}">
								<m:Label text="Copy From Extension" labelFor="copyFromExtn" required="false"/>
								<m:Select valueStateText="Must select copy from extension" forceSelection="false" id="copyFromExtn"
									selectedKey="{idocExtensionWizardDetails>/Extension/Firsttyp}" valueState="{= ${idocExtensionWizardDetails>/Extension/Firsttyp} ? 'None' : 'Error' }"
									enabled="true" items="{ path: 'idocExtensionWizardDetails>/extensionList' }">
									<c:Item key="{idocExtensionWizardDetails>Idocext}" text="{idocExtensionWizardDetails>Idocext}"/>
								</m:Select>
							</m:VBox>
							<m:VBox class="sapUip0SmallMarginEnd" visible="{= ${idocExtensionWizardDetails>/sSelectedExtnType} === 'SUCCESSOR'}">
								<m:Label text="Successor" labelFor="successor"/>
								<m:Select valueStateText="Must select successor" forceSelection="false" id="extnSucc"
									selectedKey="{idocExtensionWizardDetails>/Extension/Pretyp}" valueState="{= ${idocExtensionWizardDetails>/Extension/Pretyp} ? 'None' : 'Error' }"
									enabled="true" items="{ path: 'idocExtensionWizardDetails>/extensionList' }">
									<c:Item key="{idocExtensionWizardDetails>Idocext}" text="{idocExtensionWizardDetails>Idocext}"/>
								</m:Select>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Description" labelFor="extnDesc"/>
								<m:Input valueStateText="Description must not be empty" value="{idocExtensionWizardDetails>/Extension/extensionDescription}"
									valueState="{= ${idocExtensionWizardDetails>/Extension/extensionDescription} ? 'None' : 'Error' }" enabled="{= ${idocExtensionWizardDetails>/idocAction} === 'addExtension'}" required="true" id="extnDesc" maxLength="40"
									placeholder="Enter description..." liveChange="onActivateCreateExtensionStep"/>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd" visible="{= ${idocExtensionWizardDetails>/sSelectedExtnType} === 'NEW' || ${idocExtensionWizardDetails>/sSelectedExtnType} === 'COPY'}">
								<m:Label text="Basic Type" labelFor="basicType" required="false"/>
								<m:Input value="{idocExtensionWizardDetails>/Extension/basicType}" id="basicType" showSuggestion="true"
									suggest="onLiveChangeBasicType" valueStateText="Basic Type must not be empty"
									valueState="{= ${idocExtensionWizardDetails>/Extension/basicType} ? 'None' : 'Error' }" required="true"
									enabled="false" suggestionItems="{idocExtensionWizardDetails>/basicTypeSuggestions}" startSuggestion="1" filterSuggests="false"
									placeholder="Enter Basic Type..." liveChange="onActivateCreateExtensionStep">
									<m:suggestionItems>
										<c:Item text="{idocExtensionWizardDetails>Idoctyp}"/>
									</m:suggestionItems>
								</m:Input>
							</m:VBox>
						</l:Grid>
					</l:VerticalLayout>
				</m:WizardStep>
				<m:WizardStep id="CreateEditSegmentStep" title="Create/Edit Segment Step" validated="false">
					<l:VerticalLayout width="100%" class="sapUiContentPadding">
						<l:content>
							<l:Grid containerQuery="true" defaultSpan="XL2 L4">
								<m:VBox class="sapUiSmallMarginEnd" visible="{= ${idocExtensionWizardDetails>/idocAction} === 'addSegment' || ${idocExtensionWizardDetails>/idocAction} === 'addExtension'}">
									<m:Label text="Parent Segment" labelFor="Parseg"/>
									<!-- Bug 11159 - Extension "COPY" option doesn't work -->
									<m:Select
										id="Parseg"
										items="{ path: 'idocExtensionWizardDetails>/ParentSegmentSuggestions' }"
										selectedKey="{idocExtensionWizardDetails>/ParentSegment/Parseg}"
										forceSelection="false"
										valueState="{formatter: '.ParentSegmentValueState', parts: ['idocExtensionWizardDetails>/sSelectedExtnType', 'idocExtensionWizardDetails>/ParentSegment/Parseg']}"
										valueStateText="Parent Segment must not be empty"
										enabled="{formatter: '.ParentSegmentEnabled', parts: ['idocExtensionWizardDetails>/idocAction', 'idocExtensionWizardDetails>/sSelectedExtnType']}"
										change="onChangeParentSegment">
											<c:Item key="{idocExtensionWizardDetails>Segmenttype}" text="{idocExtensionWizardDetails>Segmenttype}"/>
									</m:Select>
								</m:VBox>
							</l:Grid>
							<l:Grid containerQuery="true" defaultSpan="XL2 L4">
								<m:VBox class="sapUiTinyMarginEnd">
									<m:Label text="Segment Type" labelFor="idSegmentType"/>
									<!-- Bug 11159 - Extension "COPY" option doesn't work -->
									<m:Input 
										id="idSegmentType"
										placeholder="Enter segment name..."
										maxLength="30"
										value="{idocExtensionWizardDetails>/Segment/Segtyp}"
										showSuggestion="true"
										suggestionItems="{idocExtensionWizardDetails>/segmentSuggestions}"
										startSuggestion="1"
										filterSuggests="false"
										valueState="{= ${idocExtensionWizardDetails>/Segment/SegTypeValueState}}"
										valueStateText="{= ${idocExtensionWizardDetails>/Segment/SegTypeValueStateText}}"
										required="{formatter: '.SegmentTypeRequired', parts: ['idocExtensionWizardDetails>/sSelectedExtnType']}"
										enabled="{formatter: '.SegmentTypeEnabled', parts: ['idocExtensionWizardDetails>/idocAction', 'idocExtensionWizardDetails>/sSelectedExtnType']}"
										suggest="onLiveChangeSegmentType"
										change="onSelectSegmentType">
										<m:suggestionItems>
											<c:Item text="{idocExtensionWizardDetails>Tabname}"/>
										</m:suggestionItems>
									</m:Input>
								</m:VBox>
								<m:VBox class="sapUiSmallMarginEnd">
									<m:Label text="Reference Table" labelFor="refSegTable"/>
									<!-- Bug 11212 - Reference Table not mandatory for Edit Segment -->
									<!-- Bug 11159 - Extension "COPY" option doesn't work -->
									<m:Input 
										id="refSegTable"
										placeholder="Enter Reference Table..."
										maxLength="40"
										value="{idocExtensionWizardDetails>/Segment/Descrp}"
										showSuggestion="true"
										suggestionItems="{idocExtensionWizardDetails>/refTableSuggestions}" 
										startSuggestion="1"
										filterSuggests="false"
										valueState="{= ${idocExtensionWizardDetails>/Segment/refTableValueState}}" 
										valueStateText="{= ${idocExtensionWizardDetails>/Segment/refTableValueStateText}}"
										required="{formatter: '.ReferenceTableRequired', parts: ['idocExtensionWizardDetails>/sSelectedExtnType']}"
										enabled="{formatter: '.ReferenceTableEnabled', parts: ['idocExtensionWizardDetails>/sSelectedExtnType']}"
										liveChange="onChangeRefTable"
										change="onChangeRefTable">
										<m:suggestionItems>
											<c:Item text="{idocExtensionWizardDetails>Tabname}"/>
										</m:suggestionItems>
									</m:Input>
								</m:VBox>
								<m:VBox class="sapUiTinyMarginEnd">
									<m:Label labelFor="idQualSeg" text="Qualified Segment"/>
									<!-- Bug 11159 - Extension "COPY" option doesn't work -->
									<m:CheckBox 
										id="idQualSeg"
										selected="{idocExtensionWizardDetails>/Segment/Qualifier}"
										enabled="{formatter: '.QualifiedSegmentEnabled', parts: ['idocExtensionWizardDetails>/sSelectedExtnType']}"
										textAlign="Left"/>
								</m:VBox>
							</l:Grid>
							<l:Grid containerQuery="true" defaultSpan="XL2 L4">
								<m:VBox class="sapUiSmallMarginEnd">
									<m:Label text="Minimum Number" labelFor="minNumber"/>
									<!-- Bug 11159 - Extension "COPY" option doesn't work -->
									<m:StepInput
										id="minNumber"
										min="0"
										value="{idocExtensionWizardDetails>/Segment/Occmin}"
										valueState="{formatter: '.MinimumMaximumNumbersValueState', parts: ['idocExtensionWizardDetails>/Segment/Occmin', 'idocExtensionWizardDetails>/Segment/Occmax']}"
										valueStateText="Minimum Number should be lower than or equals to Maximum Number"
										validationMode="LiveChange"
										enabled="{formatter: '.MinimumMaximumNumbersEnabled', parts: ['idocExtensionWizardDetails>/sSelectedExtnType']}"
										change="enableSave"
										width="100px"/>
								</m:VBox>
								<m:VBox class="sapUiSmallMarginEnd">
									<m:Label text="Maximum Number" labelFor="maxNumber"/>
									<!-- Bug 11159 - Extension "COPY" option doesn't work -->
									<m:StepInput 
										id="maxNumber"
										min="1"
										value="{idocExtensionWizardDetails>/Segment/Occmax}" 
										valueState="{formatter: '.MinimumMaximumNumbersValueState', parts: ['idocExtensionWizardDetails>/Segment/Occmin', 'idocExtensionWizardDetails>/Segment/Occmax']}"
										valueStateText="Maximum Number should be greater than or equals to Minimum Number"
										validationMode="LiveChange"
										enabled="{formatter: '.MinimumMaximumNumbersEnabled', parts: ['idocExtensionWizardDetails>/sSelectedExtnType']}"
										change="enableSave"
										width="100px"/>
								</m:VBox>
							</l:Grid>
							<m:ScrollContainer horizontal="true" vertical="true">
								<m:Table id="idSegmentFieldsTable" alternateRowColors="true" items="{path:'idocExtensionWizardDetails>/Segment/segmentFieldList'}" 
								sticky="HeaderToolbar,InfoToolbar,ColumnHeaders" mode="Delete" delete="onDeleteSegmentField" fixedLayout="false">
									<m:headerToolbar>
										<m:OverflowToolbar>
											<m:Title class="sapUiLargeMarginEnd" id="idSegmentFields" text="Fields in Segment"/>
											<m:ToolbarSpacer/>
											<!-- Bug 11159 - Extension "COPY" option doesn't work -->
											<m:Button 
												id="addField"
												icon="sap-icon://add"
												text="ADD"
												enabled="{formatter: '.AddFieldEnabled', parts: ['idocExtensionWizardDetails>/sSelectedExtnType']}"
												press="onAddSegmentField"/>
										</m:OverflowToolbar>
									</m:headerToolbar>
									<m:columns>
										<m:Column hAlign="Center" vAlign="Middle">
											<m:Label text="Field Name" wrapping="false" labelFor="segFieldName"/>
										</m:Column>
										<m:Column  hAlign="Center" vAlign="Middle" >
											<m:Label text="Data Element" wrapping="true" labelFor="entSegElement"/>
										</m:Column>
										<m:Column  hAlign="Center" vAlign="Middle" >
											<m:Label text="ISO Code?" wrapping="true" />
										</m:Column>
										<m:Column  hAlign="Center" vAlign="Middle" >
											<m:Label text="Exp Len" wrapping="true" />
										</m:Column>
									</m:columns>
									<m:items>
										<m:ColumnListItem  type="Active" class="dmrTableRow">
											<m:cells class="sapMListTblCell dmrTableRow" >
												<!-- Bug 11213 - Make Data Element mandatory for new Segment Fields -->
												<m:Input value="{idocExtensionWizardDetails>Fieldname}" required="true" liveChange="onChangeField" id="segFieldName" placeholder="Enter a name..." valueState="{= ${idocExtensionWizardDetails>Fieldname} ? 'None' : 'Error' }" valueStateText="Field Name is mandatory" />
											</m:cells>
											<m:cells class="sapMListTblCell dmrTableRow" >
												<!-- Bug 11213 - Make Data Element mandatory for new Segment Fields -->
												<m:Input value="{idocExtensionWizardDetails>Rollname}" required="true" change="onChangeDataElement" liveChange="onLiveChangeDataElement" id="entSegElement" placeholder="Enter element name..." showSuggestion="true" 
												suggestionItems="{path: 'idocExtensionWizardDetails>/DataElementSuggestions', templateShareable: false}" startSuggestion="1" filterSuggests="false"
												maxLength="30" valueState="{= ${idocExtensionWizardDetails>Rollname} ? 'None' : 'Error' }" valueStateText="Data Element is mandatory">
												<m:suggestionItems>
													<c:Item text="{idocExtensionWizardDetails>Rollname}"/>
												</m:suggestionItems>
												</m:Input>
											</m:cells>
											<m:cells class="sapMListTblCell dmrTableRow" >
												<m:CheckBox id="idIsocode" textAlign="Left" 
													selected="{idocExtensionWizardDetails>Isocode}"/>
											</m:cells>
											<m:cells class="sapMListTblCell dmrTableRow" >
												<m:Input value="{idocExtensionWizardDetails>Expleng}" enabled="false" id="expLen" type="Number" placeholder="Enter a Number ..."/>
											</m:cells>
										</m:ColumnListItem>
									</m:items>
								</m:Table>
							</m:ScrollContainer>
						</l:content>
					</l:VerticalLayout>
				</m:WizardStep>
			</m:Wizard>
		</m:content>
		<m:footer>
			<m:Bar>
				<m:contentRight>
					<m:Button text="Save" press=".onSegmentSave" enabled="{= ${idocExtensionWizardDetails>/bEnableSave}}" id="idSaveSegment"/>
					<m:Button text="Cancel" press=".onWizardCancel" id="idWizardCancel"/>
				</m:contentRight>
			</m:Bar>
		</m:footer>
	</m:Page>
</mvc:View>