<mvc:View xmlns:core="sap.ui.core" xmlns:mvc="sap.ui.core.mvc" xmlns:l="sap.ui.layout" xmlns:f="sap.ui.layout.form"
	xmlns:n="sap.suite.ui.commons.networkgraph" xmlns:layout="sap.suite.ui.commons.networkgraph.layout" xmlns="sap.ui.table"
	xmlns:u="sap.ui.unified" xmlns:m="sap.m" controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Interfaces.Interfaces"
	xmlns:html="http://www.w3.org/1999/xhtml" class="sapUiContentPadding">
	<m:Page title="Interfaces" titleLevel="H1" showNavButton="true" navButtonPress="onBackPress" enableScrolling="false">
		<m:headerContent>
			<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.shared.MainMenu" />
		</m:headerContent>
		<m:content>
			<!-- Create an invisible component container for the Transport Selection Dialog START -->
		            <core:ComponentContainer id="idTransportPackageSelection" name="dmr.components.TransportPackage" async="true"
                componentCreated="onTransportPackageDialogCreated" />
			<!-- Create an invisible component container for the Package Selection Dialog END -->
			<l:Splitter >
				<m:VBox id="layout1">
					<m:layoutData>
						<l:SplitterLayoutData size="25%" resizable="false"/>
					</m:layoutData>
					<core:ComponentContainer id="interfaceSearchList" name="dmr.components.SearchList" async="false"
						componentCreated="onInterfaceSearchListCreated"/>
				</m:VBox>
				<m:VBox fitContainer="true" height="91vh">
					<m:Panel id="idInterfacePanel" headerText="Interface Details" height="auto" width="100%" expandable="true" expanded="false">
						<m:Label class="requiredInfo sapUiSmallMarginEnd" id="idInInfoRequired" text="* Denotes Required Field"/>
						<f:SimpleForm id="form" editable="true" layout="ResponsiveGridLayout" labelSpanL="3" labelSpanM="3">
							<m:Label xmlns="sap.m" text="Replication Model" labelFor="idRepModel"/>
							<m:Input enabled="{= ${viewInterface>/interface} === 'new' }" id="idRepModel" value="{viewInterface>/selectedInterface/UsmdInterface}"
								required="true" maxLength="10" valueStateText="Interface must not be empty"
								valueState="{= !${viewInterface>/selectedInterface/UsmdInterface} ? 'Error' : 'None'}" valueLiveUpdate="true" change="onInterfaceChange"
								placeholder="Enter Replication Model Name..." fieldWidth="30%" showSuggestion="true" suggestionItems="{viewInterface>/RepModels}"
								startSuggestion="0" filterSuggests="false">
								<m:suggestionItems>
									<core:Item text="{viewInterface>Appl}"/>
								</m:suggestionItems>
							</m:Input>
							<m:Label xmlns="sap.m" text="Description" labelFor="interfaceDesc"/>
							<!-- Bug 11139 - Interface Configuration Create and Change Processes -->
							<!-- New business logic to determine if Description field should be enabled -->
							<m:Input enabled="{formatter: '.DescriptionEnabled', parts: ['viewInterface>/interface']}" id="interfaceDesc"
								maxLength="60" valueLiveUpdate="true" change="onDescriptionChange" valueStateText="Description must not be empty for new Replication model"
								valueState="{viewInterface>/selectedInterface/interfaceDescValueState}" value="{viewInterface>/selectedInterface/UsmdInterfaceText}"
								required="{= ${viewInterface>/newInterface}}" fieldWidth="30%"/>
							<!-- Bug 11139 - Interface Configuration Create and Change Processes -->
							<!-- Adding Active as a new field to the form -->
							<m:Label xmlns="sap.m" labelFor="active" text="Active"/>
							<m:Switch id="active" name="active" type="AcceptReject" 
								enabled="{formatter: '.ActiveEnabled', parts: ['viewInterface>/interface']}"
								state="{= ${viewInterface>/selectedInterface/Active} === 'Active' ? true : false}" 
								change=".onActiveChange"/>
							<m:Label text="Outbound Implementation" labelFor="interfaceOutImpl" required="true"/>
							<m:HBox>
								<m:ComboBox id="interfaceOutImpl" enabled="{= ${viewInterface>/interface} === 'new' }"
									selectedKey="{viewInterface>/selectedInterface/Implementation}" change="onChangeOutboundImpl"
									valueState="{viewInterface>/selectedInterface/interfaceOutbValueState}" required="true"
									valueStateText="{viewInterface>/selectedInterface/interfaceOutbValueStateText}"
									items="{ path: 'viewInterface>/SearchHelps/arrOutboundImplemenations' }">
									<core:Item key="{viewInterface>ServImp}" text="{viewInterface>ServImp} - {viewInterface>Description}"/>
								</m:ComboBox>
								<m:Button tooltip="Clear Selection" type="Transparent"
									visible="{= ${viewInterface>/interface} === 'new' &amp;&amp; ${viewInterface>/selectedInterface/Implementation} !== ''}" iconFirst="true"
									icon="sap-icon://decline" id="clearOutbImpl" press="onClearOutbImpl"/>
							</m:HBox>
							<m:Label text="Communication Channel" labelFor="interfaceCommChannel"/>
							<m:Select forceSelection="false" items="{path:'viewInterface>/SearchHelps/arrCommChannel'}" id="interfaceCommChannel" enabled="false"
								required="true" selectedKey="{viewInterface>/selectedInterface/Commchannel}">
								<core:Item key="{viewInterface>Commchannel}" text="{viewInterface>Commchannel} - {viewInterface>Description}"/>
							</m:Select>
							<m:Label text="Data Model" labelFor="dataModel" required="true"/>
							<m:HBox>
								<m:Select forceSelection="false" items="{path:'dataModel>/results'}" id="dataModel" required="true"
									valueStateText="Data Model must not be empty" valueState="{= !${viewInterface>/selectedInterface/UsmdModel} ? 'Error' : 'None'}"
									enabled="{= ${viewInterface>/interface} !== undefined &amp;&amp; ${viewInterface>/interface} !== 'existing'}" change="onChangeDataModel"
									selectedKey="{viewInterface>/selectedInterface/UsmdModel}">
									<core:Item key="{dataModel>Model}" text="{dataModel>Model} - {dataModel>Desc}"/>
								</m:Select>
								<m:Button tooltip="Clear Selection" type="Transparent" visible="{= ${viewInterface>/selectedInterface/UsmdModel} !== ''}"
									enabled="{= ${viewInterface>/interface} !== undefined &amp;&amp; ${viewInterface>/interface} !== 'existing'}" iconFirst="true"
									icon="sap-icon://decline" id="clearIntDataModel" press="onClearIntDataModel"/>
							</m:HBox>
							<m:Label text="Sequence" labelFor="interfaceSequence"/>
							<m:Input enabled="{= ${viewInterface>/interface} !== undefined &amp;&amp; ${viewInterface>/interface} !== 'existing'}"
								value="{viewInterface>/selectedInterface/Sequence}" id="interfaceSequence" fieldWidth="30%"
								valueState="{= RegExp('^[0-9]+$').test(${viewInterface>/selectedInterface/Sequence}) || ${viewInterface>/selectedInterface/Sequence} === '' ? 'None' : 'Error' }"
								valueStateText="Sequence should contain only numbers" maxLength="2" valueLiveUpdate="true"/>
							<m:Label text="Filter Time" labelFor="interfaceFilterTime"/>
							<m:HBox>
								<!-- Bug 11138 - Filter Time Select Box can be given by the user only if in built Config Filter Time property for Outbound Implementation is set-->
								<m:Select forceSelection="false" items="{path:'viewInterface>/SearchHelps/arrDomainRanges'}" id="interfaceFilterTime"	
									enabled="{= ${viewInterface>/interface} !== undefined &amp;&amp; ${viewInterface>/interface} !== 'existing' &amp;&amp; ${viewInterface>/selectedInterface/ConfigTp} !== '' }"
									selectedKey="{viewInterface>/selectedInterface/Filtertp}">
									<core:Item key="{viewInterface>Domainranges}" text="{viewInterface>Domainranges} - {viewInterface>Description}"/>
								</m:Select>
								<m:Button tooltip="Clear Selection" type="Transparent" visible="{= ${viewInterface>/selectedInterface/ConfigTp} !== '' &amp;&amp; ${viewInterface>/selectedInterface/Filtertp} !== ''}"
									enabled="{= ${viewInterface>/interface} !== undefined &amp;&amp; ${viewInterface>/interface} !== 'existing'}" iconFirst="true"
									icon="sap-icon://decline" id="clearFilterTime" press="onClearFilterTime"/>
							</m:HBox>
							<m:Label text="Business System" labelFor="interfaceBusSystem"/>
							<m:MultiComboBox id="interfaceBusSystem" required="true" selectionChange="onBusinessSystemChange"
								valueStateText="Business System must not be empty" valueState="{viewInterface>/selectedInterface/interfaceBusisyValueState}"
								enabled="{= ${viewInterface>/interface} !== 'existing' &amp;&amp; ${viewInterface>/interface} !== undefined &amp;&amp; ${viewInterface>/arrBusinessSystem}.length !== 0 }"
								selectedKeys="{viewInterface>/selectedInterface/selectedBusinessSystems}"
								items="{ path: 'viewInterface>/arrBusinessSystem', sorter: { path: 'Bussystem' } }">
								<core:Item key="{viewInterface>Bussystem}" text="{viewInterface>Bussystem}"/>
							</m:MultiComboBox>
							<m:HBox>
								<m:Button class="sapUiTinyMarginEnd"
									enabled="{= ${viewInterface>/interface} !== undefined &amp;&amp; ${viewInterface>/interface} !== 'existing'}" text="Manage Business System"
									id="manageBussSystem" press="onManageBussinessSystem"/>
							</m:HBox>
							<m:Label text="Parameters" labelFor="interfaceParams"/>
							<m:Table id="idParamTable" alternateRowColors="true" width="100%" mode="{viewInterface>/parameterTableMode}" delete="onDeleteParameter"
								items="{path: 'viewInterface>/selectedInterface/arrParametersTable'}">
								<m:headerToolbar>
									<m:OverflowToolbar>
										<m:ToolbarSpacer/>
										<m:Button enabled="{= ${viewInterface>/interface} !== undefined &amp;&amp; ${viewInterface>/interface} !== 'existing'}"
											icon="sap-icon://add" text="ADD" id="addParameter" press="onAddParameter"/>
									</m:OverflowToolbar>
								</m:headerToolbar>
								<m:columns>
									<m:Column>
										<m:Label design="Bold" text="Parameter" wrapping="true"/>
									</m:Column>
									<m:Column>
										<m:Label design="Bold" text="Mandatory" wrapping="true"/>
									</m:Column>
									<m:Column>
										<m:Label design="Bold" text="Value" wrapping="true"/>
									</m:Column>
								</m:columns>
								<m:items>
									<m:ColumnListItem>
										<m:cells>
											<m:Select forceSelection="false" id="idParameters"
												enabled="{= ${viewInterface>/interface} !== undefined &amp;&amp; ${viewInterface>/interface} !== 'existing'}"
												selectedKey="{viewInterface>Outbparameter}" items="{path:'viewInterface>/arrParameters', templateShareable:false}"
												change="onChangeParameter">
												<core:Item key="{viewInterface>Outbparameter}" text="{viewInterface>Outbparameter}"/>
											</m:Select>
											<m:Switch name="mandatorySwitch" type="AcceptReject" state="{viewInterface>Mandatory}" enabled="false"></m:Switch>
											<!-- Bug 11149 - Outbound Parameter values are duplicated in RDG -->
											<m:Select 
												id="idValues"
												items="{ path:'viewInterface>arrValues', templateShareable:false }"
												selectedKey="{viewInterface>Value}"
												forceSelection="false"
												enabled="{= ${viewInterface>/interface} !== undefined &amp;&amp; ${viewInterface>/interface} !== 'existing'}">
												<core:Item key="{viewInterface>Value}" text="{viewInterface>Value}"/>
											</m:Select>
										</m:cells>
									</m:ColumnListItem>
								</m:items>
							</m:Table>
							<m:Label text="Variants" labelFor="interfaceVariants"/>
							<!-- Bug 11139 - Interface Configuration Create and Change Processes -->
							<!-- New business logic to determine if Variants field should be enabled -->
							<m:MultiComboBox id="interfaceVariants"
								enabled="{formatter: '.VariantsEnabled', parts: ['viewInterface>/interface', 'viewInterface>/selectedInterface/Commchannel', 'viewInterface>/arrVariants']}"
								selectedKeys="{viewInterface>/selectedInterface/selectedVariants}"
								items="{ path: 'viewInterface>/arrVariants', sorter: { path: 'Usmdtrvari' } }">
								<core:Item key="{viewInterface>Usmdtrvari}" text="{viewInterface>Usmdtrvari}"/>
							</m:MultiComboBox>
							<m:HBox>
								<m:Button tooltip="Save Interface" visible="{= ${viewInterface>/interface} === 'new' || ${viewInterface>/interface} === 'edit'}"
									enabled="{formatter: '.createNewInterfaceEnabled', parts: ['viewInterface>/selectedInterface/UsmdInterface', 'viewInterface>/selectedInterface/Implementation', 'viewInterface>/selectedInterface/Commchannel', 'viewInterface>/selectedInterface/UsmdModel', 'viewInterface>/selectedInterface/selectedBusinessSystems','viewInterface>/selectedInterface/UsmdInterfaceText','viewInterface>/newInterface','viewInterface>/selectedInterface/interfaceOutbValueState','viewInterface>/selectedInterface/Sequence']}"
									iconFirst="true" icon="sap-icon://save" text="Save" id="save" press="onSaveInterface"/>
								<m:Button tooltip="Edit Interface" visible="{= ${viewInterface>/interface} === 'existing'}" iconFirst="true" icon="sap-icon://edit"
									text="Edit" id="edit" press="onEditInterface"/>
								<m:Button class="sapUiTinyMarginBegin" tooltip="Delete Interface" visible="{= ${viewInterface>/interface} === 'existing' || ${viewInterface>/interface} === 'edit'}" iconFirst="true" icon="sap-icon://delete"
									text="Delete" id="delete" press="onDeleteInterface"/>
							</m:HBox>
						</f:SimpleForm>
					</m:Panel>
					<f:SimpleForm id="idformBusSystem" editable="true" layout="ResponsiveGridLayout" emptySpanL="5" emptySpanM="5">
						<m:Label text="Business System for Mapping" labelFor="idIdocBusSystem"/>
						<m:Select id="idIdocBusSystem" forceSelection="false" change="onChangeMappingBusinessSystem"
							visible="{= ${viewInterface>/interface} === 'existing'}" enabled="{= ${viewInterface>/isBusSystemForMappingDisabled} !== true}"
							valueStateText="No Business Systems have been assigned to the Interface"
							valueState="{= !${viewInterface>/selectedInterface/selectedBusinessSystems}.length > '0' ? 'Error' : 'None'}"
							selectedKey="{viewInterface>/selectedBusSystemForMapping}" items="{ path: 'viewInterface>/selectedInterface/selectedBusinessSystems' }">
							<core:Item key="{viewInterface>}" text="{viewInterface>}"/>
						</m:Select>
					</f:SimpleForm>
					<m:Panel id="idIdocPanel" class="sapUiNoContentPadding" headerText="Mapping Details" height="80vh" width="100%" expandable="true"
						expanded="false">
						<TreeTable id="IdocTree" width="100%" rowSelectionChange="onTreeRowSelected" selectionMode="Single" selectionBehavior="RowOnly"
							enableSelectAll="false" ariaLabelledBy="title" alternateRowColors="true" collapseRecursive="true"
							rows="{path:'/children', parameters:{ arrayNames: ['children'], numberOfExpandedLevels: 1 }}" groupHeaderProperty="Name"
							useGroupMode="false" visibleRowCountMode="Fixed">
							<!-- Bug 11958 - Allow to make transformations only interface is active -->
							<!-- Made changes so the IDoc and edit mapping are only enabled when interface is active -->
							<extension>
								<m:OverflowToolbar>
									<m:Button visible="{= ${viewInterface>/sSaveValidationError} === true}" text="{viewInterface>/sValidationText}"
										press="onSaveValidationFilterChange"/>
									<m:ToolbarSpacer/>
									<m:Button visible="{= ${viewInterface>/selectedInterface/Idocextension} === '' &amp;&amp; ${viewInterface>/editIdoc} === true}"
										icon="sap-icon://add" text="Add Extension" id="addExtensionDetails" press="onExtensionPress" enabled="{= ${/children} !== undefined}"/>
									<m:Button visible="{= ${viewInterface>/selectedInterface/Idocextension} !=='' &amp;&amp; ${viewInterface>/editIdoc} === true}"
										icon="sap-icon://add" text="Add Segment" id="addSegment" press="onExtensionPress" enabled="{= ${/children} !== undefined}"/>
									<m:Button visible="{= ${viewInterface>/selectedInterface/Idocextension} !=='' &amp;&amp; ${viewInterface>/editIdoc} === true}"
										icon="sap-icon://edit" text="Edit Segment" id="editSegment" press="onExtensionPress"/>
									<!-- Task 10877 - Deletion of extensions/segments in Mapping Tree - UI5 -->
									<m:Button visible="{= ${viewInterface>/selectedInterface/Idocextension} !=='' &amp;&amp; ${viewInterface>/editIdoc} === true}"
										icon="sap-icon://delete" text="Delete Segment" id="deleteSegment" press="onExtensionPress"/>
									<!-- Task 10877 - Deletion of extensions/segments in Mapping Tree - UI5 -->
									<m:Button visible="{= ${viewInterface>/selectedInterface/Idocextension} !=='' &amp;&amp; ${viewInterface>/editIdoc} === true}"
										icon="sap-icon://delete" text="Delete Extension" id="deleteExtension" press="onExtensionPress"/>
									<m:Button visible="{= !${viewInterface>/enableMappingDetailsTable} &amp;&amp; ${viewInterface>/selectedInterface/Commchannel} === '2' &amp;&amp; ${viewInterface>/editMode} === false}" 
										icon="sap-icon://edit" text="Edit IDOC" id="editIdocDetails" press="onEditIdocDetails" enabled="{= ${/children} !== undefined &amp;&amp; ${viewInterface>/selectedInterface/Active} === 'Active'}"/>
									
									<!-- Task 12294 - Add all options for enhancement and open new Proxy Extension screen -->
									<m:Button visible="{= ${viewInterface>/selectedInterface/Commchannel} === '1' &amp;&amp; ${viewInterface>/editMode} === false &amp;&amp; ${viewInterface>/enableProxyDetails} === false}" enabled="{= ${/children} !== undefined &amp;&amp; ${viewInterface>/selectedInterface/Active} === 'Active'}" 
										icon="sap-icon://edit" text="Edit Proxy" id="editProxy" press="onEditProxy" />

									<m:Button visible="{= ${viewInterface>/enableProxyDetails} === true }" icon="sap-icon://add" text="Add" id="addProxyElement" press="onAddProxyPress"/>
									<m:Button visible="{= ${viewInterface>/enableProxyDetails} === true }" icon="sap-icon://edit" text="Edit" id="editProxyElement" press="onEditProxyPress"/>
									<m:Button visible="{= ${viewInterface>/enableProxyDetails} === true }" icon="sap-icon://delete" text="Delete" id="deleteProxyElement" press="onDeleteProxyPress"/>
									<!-- end Task 12294 -->
									
									<m:Button visible="{= !${viewInterface>/enableMappingDetailsTable} &amp;&amp; ${viewInterface>/editMode} === false}" icon="sap-icon://edit"
										text="Edit Mapping" id="editMappingDetails" press="onEditMappingDetails" enabled="{= ${/children} !== undefined &amp;&amp; ${viewInterface>/selectedInterface/Active} === 'Active'}"/>
									<m:Button visible="{= ${viewInterface>/enableMappingDetailsTable} === true }" icon="sap-icon://save" text="Save" id="saveMappingDetails"
										press="onSaveMappingDetails"/>
									<m:Button visible="{= ${viewInterface>/editMode} === true}" icon="sap-icon://decline" text="Cancel" id="cancelDetails"
										press="onCancelDetails"/>
								</m:OverflowToolbar>
							</extension>
							<extension>
								<m:OverflowToolbar class="transparentBar" visible="{= ${/children} !== undefined}">
									<m:HBox class="sapUiSmallMarginEnd">
										<m:Input value="{viewInterface>/sSelectedSegmentForFilter}" placeholder="Enter Segment" id="idSegmentInput" />
									</m:HBox>
									<m:HBox class="sapUiSmallMarginEnd">
										<m:Input value="{viewInterface>/sSelectedFieldForFilter}" placeholder="Enter Field" id="enterField" />
									</m:HBox>
									<m:HBox class="sapUiSmallMarginEnd">
										<m:ComboBox selectedKey="{viewInterface>/sSelectedTransformationForFilter}"
										id="idTransformationFilterSelect">
											<m:items>
												<core:Item key="" text=""/>
												<core:Item key="B" text="Bypass" enabled="{= ${viewInterface>/selectedInterface/Commchannel} === '2'}"/>
												<core:Item key="M" text="Mapped Value"/>
												<core:Item key="F" text="Fixed Value"/>
											</m:items>
										</m:ComboBox>
										
										<m:Button class="sapUiTinyMarginBegin" iconFirst="true" tooltip="Filter"
										icon="sap-icon://filter" press="onClickFilter" id="filterButton"/>
										<m:Button class="sapUiTinyMarginBegin" iconFirst="true" tooltip="Clear Filter"
										icon="sap-icon://clear-filter" press="onClearFilter" id="clearFilter" />
									</m:HBox>
								</m:OverflowToolbar>
							</extension>
							<columns>
								<Column hAlign="Center" width="20%" >
									<label>
										<m:HBox>
											<m:Label text="Segment" wrapping="true"/>
										</m:HBox>
									</label>
									<template>
										<m:ObjectStatus text="{Segment}{Fieldname}"
											state="{formatter: '.FieldState', parts: ['SEGSTRUCT2ATTRIBNAV/tableValueState', 'SEGSTRUCT2ATTRIBNAV/tableFieldValueState', 'SEGSTRUCT2ATTRIBNAV/fixedValueState']}"
											inverted="{formatter: '.FieldInverted', parts: ['SEGSTRUCT2ATTRIBNAV/tableValueState', 'SEGSTRUCT2ATTRIBNAV/tableFieldValueState', 'SEGSTRUCT2ATTRIBNAV/fixedValueState']}"/>
									</template>
								</Column>
								<Column hAlign="Center" width="20%">
									<label>
										<m:Label text="Transformation" wrapping="true"/>
									</label>
									<template>
										<m:Select selectedKey="{Transformation}" forceSelection="false" id="idTransformationSelect"
											enabled="{= ${viewInterface>/enableMappingDetailsTable} === true}" visible="{= ${Transformation} !== undefined }"
											change="onTransformationChange">
											<m:items>
												<core:Item key="" text=""/>
												<core:Item key="B" text="Bypass" enabled="{= ${viewInterface>/selectedInterface/Commchannel} === '2'}"/>
												<core:Item key="M" text="Mapped Value"/>
												<core:Item key="F" text="Fixed Value"/>
											</m:items>
										</m:Select>
									</template>
								</Column>
								<Column hAlign="Center" width="20%">
									<label>
										<m:Label text="Table" wrapping="true"/>
									</label>
									<template>
										<m:HBox>
											<m:Input value="{Table}" id="idMappingTable" valueLiveUpdate="true" change="onSelectTable" liveChange="onLiveChangeTable"
												suggestionItems="{path: 'viewInterface>/TableSuggestions', templateShareable:true }" showSuggestion="true" startSuggestion="1"
												filterSuggests="false" visible="{= ${Table} !== undefined }" valueStateText="{SEGSTRUCT2ATTRIBNAV/tableValueStateText}"
												valueState="{= ${SEGSTRUCT2ATTRIBNAV/tableValueState} === 'X' ? 'Warning' : 'None'}"
												enabled="{= ${Transformation} === 'M' &amp;&amp; ${viewInterface>/enableMappingDetailsTable} === true &amp;&amp; ${Segext} !== 'X'}">
												<m:suggestionItems>
													<core:Item text="{viewInterface>Tabname} {viewInterface>Tabledesc}"/>
												</m:suggestionItems>
											</m:Input>
											<m:Button iconFirst="true" icon="sap-icon://key" visible="{= ${Transformation} === 'M' &amp;&amp; ${Keyvalue} !== '' }"
												enabled="{= ${Transformation} === 'M' &amp;&amp; ${viewInterface>/enableMappingDetailsTable} === true}" class="roundButton"
												tooltip="{Keyvalue}" press=".onKeyValuePress"/>
										</m:HBox>
									</template>
								</Column>
								<Column hAlign="Center" width="20%">
									<label>
										<m:Label text="Table Field" wrapping="true"/>
									</label>
									<template>
										<m:Input value="{Tablefield}" valueLiveUpdate="true"
											suggestionItems="{path: 'viewInterface>/TablefieldSuggestions', templateShareable:true }" liveChange="onLiveChangeTableField"
											change="onSelectTableField" showSuggestion="true" startSuggestion="1" filterSuggests="false" visible="{= ${Tablefield} !== undefined }"
											valueStateText="{SEGSTRUCT2ATTRIBNAV/tableFieldValueStateText}"
											valueState="{= ${SEGSTRUCT2ATTRIBNAV/tableFieldValueState} === 'X' ? 'Warning' : 'None'}"
											enabled="{= ${Transformation} === 'M' &amp;&amp; ${viewInterface>/enableMappingDetailsTable} === true }">
											<m:suggestionItems>
												<core:Item text="{viewInterface>Fieldname}"/>
											</m:suggestionItems>
										</m:Input>
									</template>
								</Column>
								<Column hAlign="Center" width="20%">
									<label>
										<m:Label text="Fixed Value" wrapping="true"/>
									</label>
									<template>
										<!-- Bug 11539 - Fixed Value Input is shown for segments in Mapping Table -->
										<m:HBox>
											<!-- Bug 11175 - Able to save mapping without table field for mapped transformation, which results in replication error -->
											<m:Input id="idFixedValue" value="{Fixedvalue}"
												enabled="{= ${Transformation} === 'F' &amp;&amp; ${viewInterface>/enableMappingDetailsTable} === true}" change=".onFixedValueLiveChange" liveChange="onFixedValueLiveChange"
												type="{formatter: '.FixedValueType', parts: ['SEGSTRUCT2ATTRIBNAV/Datatype']}"
												visible="{= ${Fixedvalue} !== undefined &amp;&amp; ${SEGSTRUCT2ATTRIBNAV/Datatype} !== 'DATS' &amp;&amp; ${SEGSTRUCT2ATTRIBNAV/Datatype} !== 'DATN' &amp;&amp; ${SEGSTRUCT2ATTRIBNAV/Datatype} !== 'TIMS' &amp;&amp; ${SEGSTRUCT2ATTRIBNAV/Datatype} !== 'TIMN'}"
												showSuggestion="{= ${SEGSTRUCT2ATTRIBNAV/Fixvalues} === 'X'}" startSuggestion="0"
												suggestionItems="{path: 'SEGSTRUCT2ATTRIBNAV/SEGSTRUCTATTRIB2FIXVALUESNAV/results', templateShareable:false}"
												valueState="{= ${SEGSTRUCT2ATTRIBNAV/fixedValueState} === 'X' ? 'Error' : 'None'}"
												valueStateText="{SEGSTRUCT2ATTRIBNAV/fixedValueStateText}"
												filterSuggests="{= ${SEGSTRUCT2ATTRIBNAV/SEGSTRUCTATTRIB2FIXVALUESNAV/results}.length > 100}">
												<m:suggestionItems>
													<core:Item key="{Value}" text="{= ${Value}.concat(${Descrp}.length > 0 ? ' - '.concat(${Descrp}) : '')}"/>
												</m:suggestionItems>
											</m:Input>
											<!-- Bug 12159 - Unable to save "Fixed Value" Transformation for 'Date' Fields - SOA Mapping -->
											<m:DatePicker id="idFixedValueDate" value="{Fixedvalue}" valueFormat="ddMMyyyy" displayFormat="M/d/yyyy" change=".onFixedValueDateTimeChange"
												enabled="{= ${Transformation} === 'F' &amp;&amp; ${viewInterface>/enableMappingDetailsTable} === true}" 
												visible="{= ${Fixedvalue} !== undefined &amp;&amp; ${SEGSTRUCT2ATTRIBNAV/Datatype} === 'DATS' || ${SEGSTRUCT2ATTRIBNAV/Datatype} === 'DATN'}"/>
											<!-- Bug 12159 - Unable to save "Fixed Value" Transformation for 'Date' Fields - SOA Mapping -->
											<m:TimePicker id="idFixedValueTime" value="{Fixedvalue}" maskMode="On" valueFormat="HHmmSS" change=".onFixedValueDateTimeChange"
												enabled="{= ${Transformation} === 'F' &amp;&amp; ${viewInterface>/enableMappingDetailsTable} === true }"
												visible="{= ${Fixedvalue} !== undefined &amp;&amp; ${SEGSTRUCT2ATTRIBNAV/Datatype} === 'TIMS' || ${SEGSTRUCT2ATTRIBNAV/Datatype} === 'TIMN'}"/>
										</m:HBox>
									</template>
								</Column>
							</columns>
						</TreeTable>
					</m:Panel>
				</m:VBox>
			</l:Splitter>
		</m:content>
	</m:Page>
</mvc:View>