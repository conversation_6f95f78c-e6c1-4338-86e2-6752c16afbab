<c:FragmentDefinition 
	xmlns:mvc="sap.ui.core.mvc" 
	xmlns:u="sap.ui.unified" 
	xmlns="sap.ui.table"
	xmlns:customdata="sap.ui.core.CustomData"
	xmlns:c="sap.ui.core" 
	xmlns:m="sap.m" 
	xmlns:l="sap.ui.layout"
	xmlns:f="sap.ui.layout.form" 
	xmlns:html="http://www.w3.org/1999/xhtml"
	controllerName="" 
	displayBlock="true">
	<m:Dialog title="Set Mapping Key">
		<l:VerticalLayout class="sapUiContentPadding">
			<l:content>
				<TreeTable id="MappingKeyValueTree" selectionMode="Single" selectionBehavior="RowOnly"
					enableSelectAll="false" ariaLabelledBy="mappingTree" alternateRowColors="true" collapseRecursive="true"
					rows="{path:'/children', parameters:{ arrayNames: ['children'], numberOfExpandedLevels: 1 }}" groupHeaderProperty="Name"
					useGroupMode="false" visibleRowCountMode="Fixed">
					<columns>
						<Column hAlign="Center" >
							<label>
								<m:HBox>
									<m:Label text="Segment" wrapping="true"/>
								</m:HBox>
							</label>
							<template>
								<m:ObjectStatus text="{Segment}{Fieldname}"/>
							</template>
						</Column>
					</columns>
				</TreeTable>
			</l:content>
		</l:VerticalLayout>
		<m:beginButton>
			<m:Button 
				text="Save" press=".SetMappingKeyValue.onMappingKeyValueSave" id="idSaveKeyValues"/>
		</m:beginButton>
		<m:endButton>
			<m:Button text="Cancel" press=".SetMappingKeyValue.onMappingKeyValueCancel" id="idCancelKeyValues"/>
		</m:endButton>
	</m:Dialog>
</c:FragmentDefinition>