<mvc:View xmlns:mvc="sap.ui.core.mvc" xmlns:u="sap.ui.unified" xmlns:l="sap.ui.layout" xmlns:c="sap.ui.core" xmlns:m="sap.m"
	xmlns:f="sap.ui.layout.form" xmlns:html="http://www.w3.org/1999/xhtml" 
	controllerName="dmr.mdg.supernova.SupernovaFJ.controller.Interfaces.ProxyEnhancementWizard" displayBlock="true">
	<m:Page showNavButton="true" showHeader="true" navButtonPress="onWizardCancel" title="Proxy Enhancement" id="ProxyEnhancementWizardPage">
		<m:headerContent>
			<mvc:XMLView viewName="dmr.mdg.supernova.SupernovaFJ.view.shared.MainMenu"/>
		</m:headerContent>
		<m:content>
			<!-- Create an invisible component container for the Transport Selection Dialog -->
			<c:ComponentContainer id="proxyEnhanceTransportPackageDialog" name="dmr.components.TransportPackage" async="true" componentCreated="onTransportPackageSelectionDialogCreated"/>

			<m:Wizard id="proxyEnhancementWizard" enableBranching="true" class="sapUiResponsivePadding--header sapUiResponsivePadding--content">
				<m:WizardStep id="selectEnhancementTypeStep" title="Select Enhancement Type" validated="{= ${proxyEnhancementModel>/parSegment}==='Edit' ? true : false}" 
							nextStep="createEnhancement">
					<m:RadioButtonGroup selectedIndex="-1" select="onEnhancementTypeSelect">
						<m:buttons>
							<m:RadioButton text="Add Element" id="rbElement"/>
							<m:RadioButton text="Add Attribute" id="rbAttribute"/>
						</m:buttons>					
					</m:RadioButtonGroup>
				</m:WizardStep>
				<m:WizardStep id="createEnhancement" title="Create/Select Enhancement" validated="{= ${proxyEnhancementModel>/parSegment}==='Edit' ? true : false}"  nextStep="createAttribute" subsequentSteps="createElement, createAttribute">
							<m:Label class="requiredInfo sapUiSmallMarginEnd" id="entInfoRequired" text="* Denotes Required Field"/>
					<l:VerticalLayout width="100%" class="gridWrapper">
						<l:Grid containerQuery="true" defaultSpan="XL2 L4">
							<m:VBox class="sapUiSmallMarginEnd">

								<m:Label text="Enhancement Name" labelFor="enhanceName" required="true"/>
								<m:Input id="enhanceName" placeholder="Enter Enhancement Name..." value="{proxyEnhancementModel>/enhancementData/sEnhancementName}" 
								required="true" editable="true" valueState="{proxyEnhancementModel>/enhancementData/enhancementNameState}" 
								valueStateText="{proxyEnhancementModel>/enhancementData/enhancementNameStateText}" liveChange="onEnhanceNameLiveChange" enabled="{= ${proxyEnhancementModel>/parSegment}!=='Add' ? false : true}">

								</m:Input>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Enhancement Description" labelFor="enhanceDesc"/>
								<!-- Bug 12518 - Mapping Details name length limit on Interface-->
								<!-- Bug 12584 - Missing ID's on Proxy Extension -->
								<m:Input id="enhanceDesc" placeholder="Enter Enhancement Description..." value="{proxyEnhancementModel>/enhancementData/sEnhancementDescription}" maxLength="100" editable="true"/>
								<!-- End Bug 12584 - Missing ID's on Proxy Extension -->
								<!-- End Bug 12518 - Mapping Details name length limit on Interface-->
							</m:VBox>
						</l:Grid>

					</l:VerticalLayout>
				</m:WizardStep>
				<m:WizardStep id="createElement" title="Create/Edit Element" validated="false" nextStep="createAttribute">
					<l:VerticalLayout width="100%" class="gridWrapper">
						<l:Grid containerQuery="true" defaultSpan="XL2 L4">
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Name" required="true"/>
								<m:Input placeholder="Enter Element..." value="{proxyEnhancementModel>/elementData/ElementName}" valueState="{proxyEnhancementModel>/elementData/elementNameState}" 
											valueStateText="{proxyEnhancementModel>/elementData/elementNameStateText}" liveChange="onElementNameChange" id="ElementName" enabled="{= ${proxyEnhancementModel>/parSegment}==='Edit' ? false : true}" />
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Element Prefix"/>
								<m:Input value="{proxyEnhancementModel>/elementData/ElementPrefix}" enabled="false"/>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Default Value"/>
								<!-- Bug 12584 - Missing ID's on Proxy Extension -->
								<m:Input id="elDefaultValue" placeholder="Enter Default Value..." value="{proxyEnhancementModel>/elementData/DefaultValue}"/>
								<!-- End Bug 12584 - Missing ID's on Proxy Extension -->
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Min Occurs" required="true"/>
								<m:Input placeholder="0" value="{proxyEnhancementModel>/elementData/MinOccurs}" enabled="false" id="MinOccurs"/>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Max Occurs" required="true"/>
									<m:Select forceSelection="false" selectedKey="{proxyEnhancementModel>/elementData/MaxOccurs}" valueState="{proxyEnhancementModel>/elementData/maxOccursState}" 
											valueStateText="{proxyEnhancementModel>/elementData/maxOccursStateText}"  change="onMaxOccursChange"  liveChange="onMaxOccursChange" id="MaxOccurs">
										<c:Item key="unbounded" text="unbounded"/>
										<c:Item key="1" text="1"/>
									</m:Select>
							</m:VBox>
							<m:VBox class="sapUiMediumMarginEnd sapUiLargeMarginBottom">
								<m:HBox>
								<m:CheckBox text="Deletable" enabled="false"/>
								<m:CheckBox text="Nillable" enabled="false"/>
								</m:HBox>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="XSDType" required="true"/>
								<m:Select forceSelection="false" items="{path:'proxyEnhancementModel>/abapData' , templateShareable:false}" 
								selectedKey="{proxyEnhancementModel>/elementData/XSDType}" change="onChangeXSDType" liveChange="onChangeXSDType" required="true" id="xsdElement">
									<c:Item key="{proxyEnhancementModel>XSDType}" text="{proxyEnhancementModel>XSDType}"/>
								</m:Select>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="ABAP Type" required="true"/>
								<m:Select forceSelection="false" items="{path:'proxyEnhancementModel>/elementData/arrAbapTypes' , templateShareable:false}" selectedKey="{proxyEnhancementModel>/elementData/ABAPType}" 
											change="onABAPTypeChange" liveChange="onABAPTypeChange" valueState="{proxyEnhancementModel>/elementData/abapTypeState}" 
											valueStateText="{proxyEnhancementModel>/elementData/abapTypeStateText}" id="elAbapType">
									<c:Item key="{proxyEnhancementModel>ABAPType}" text="{proxyEnhancementModel>ABAPType}"/>
								</m:Select>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Pattern"/>
								<m:Input placeholder="Pattern..." value="{proxyEnhancementModel>/elementData/Pattern}" enabled="{= ${proxyEnhancementModel>/elementData/EbPattern}==='X'}" id="Pattern-element" />
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Length"/>
								<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>/elementData/Length}" enabled="{= ${proxyEnhancementModel>/elementData/EbLength}==='X'}" liveChange="filterElementData"
								valueState="{proxyEnhancementModel>/elementData/lengthState}" valueStateText="{proxyEnhancementModel>/elementData/lengthStateText}" id="Length-element"/>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Max Length"/>
								<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>/elementData/MaxLength}" enabled="{= ${proxyEnhancementModel>/elementData/EbMaxLength}==='X'}" liveChange="onMinMaxLiveChange" 
								valueState="{proxyEnhancementModel>/elementData/MaxLengthState}" valueStateText="{proxyEnhancementModel>/elementData/MaxLengthStateText}" id="MaxLength-Element"/>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Min Length"/>
								<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>/elementData/MinLength}" enabled="{= ${proxyEnhancementModel>/elementData/EbMinLength}==='X'}" liveChange="onMinMaxLiveChange" 
								valueState="{proxyEnhancementModel>/elementData/MinLengthState}" valueStateText="{proxyEnhancementModel>/elementData/MinLengthStateText}" id="MinLength-Element"/>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Max Incl."/>
								<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>/elementData/MaxIncl}" enabled="{= ${proxyEnhancementModel>/elementData/EbMaxIncl}==='X'}" liveChange="onMinMaxLiveChange" 
								valueState="{proxyEnhancementModel>/elementData/MaxInclState}" valueStateText="{proxyEnhancementModel>/elementData/MaxInclStateText}" id="MaxIncl-Element"/>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Min Incl."/>
								<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>/elementData/MinIncl}" enabled="{= ${proxyEnhancementModel>/elementData/EbMinIncl}==='X'}" liveChange="onMinMaxLiveChange" 
								valueState="{proxyEnhancementModel>/elementData/MinInclState}" valueStateText="{proxyEnhancementModel>/elementData/MinInclStateText}" id="MinIncl-Element"/>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Max Excl."/>
								<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>/elementData/MaxExcl}" enabled="{= ${proxyEnhancementModel>/elementData/EbMaxExcl}==='X'}" liveChange="onMinMaxLiveChange" 
								valueState="{proxyEnhancementModel>/elementData/MaxExclState}" valueStateText="{proxyEnhancementModel>/elementData/MaxExclStateText}" id="MaxExcl-Element"/>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Min Excl."/>
								<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>/elementData/MinExcl}" enabled="{= ${proxyEnhancementModel>/elementData/EbMinExcl}==='X'}" liveChange="onMinMaxLiveChange" 
								valueState="{proxyEnhancementModel>/elementData/MinExclState}" valueStateText="{proxyEnhancementModel>/elementData/MinExclStateText}" id="MinExcl-Element"/>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Total Digits"/>
								<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>/elementData/TotalDigits}" enabled="{= ${proxyEnhancementModel>/elementData/EbTotalDigits}==='X'}" liveChange="onTotalDigitsLiveChange" 
								id="TotalDigits-Element"/>
							</m:VBox>
							<m:VBox class="sapUiSmallMarginEnd">
								<m:Label text="Fraction Digits"/>
								<m:Input type="Number" placeholder="000000" value="{proxyEnhancementModel>/elementData/FractionDigits}" enabled="{= ${proxyEnhancementModel>/elementData/EbFractionDigits}==='X'}" liveChange="onFractionDigitsLiveChange" 
								valueState="{proxyEnhancementModel>/elementData/fractionDigitsState}" valueStateText="{proxyEnhancementModel>/elementData/fractionDigitsStateText}" id="FractionDigits-Element"/>
							</m:VBox>
							
						</l:Grid>
					</l:VerticalLayout>
				</m:WizardStep>
				<m:WizardStep id="createAttribute" title="Create/Edit Attribute" validated="false" activate="true">
					<m:ScrollContainer vertical="true" height="100%">
						<m:Table width="100%" sticky="ColumnHeaders" autoPopinMode="true" popinLayout="GridLarge" 
						contextualWidth="Auto" alternateRowColors="true" items="{path: 'proxyEnhancementModel>/attributeData'}" id="attrTable">
							<m:headerToolbar>
								<m:OverflowToolbar>
									<m:ToolbarSpacer/>
									<m:Button icon="sap-icon://add" text="ADD" press="onAddAttribute" id="addAttribute"/>
								</m:OverflowToolbar>
							</m:headerToolbar>
							<m:columns>
								<m:Column>
									<m:Label design="Bold" text="Name" wrapping="true" required="true"/>
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="Default Value" wrapping="true"/>
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="Optional" wrapping="true"/>
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="XSD Type" wrapping="true" required="true"/>
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="ABAP Type" wrapping="true" required="true" />
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="Pattern" wrapping="true"/>
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="Length" wrapping="true"/>
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="Max Length" wrapping="true"/>
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="Min Length" wrapping="true"/>
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="Max Incl." wrapping="true"/>
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="Min Incl." wrapping="true"/>
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="Max Excl." wrapping="true"/>
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="Min Excl." wrapping="true"/>
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="Total Digits" wrapping="true"/>
								</m:Column>
								<m:Column>
									<m:Label design="Bold" text="Fraction Digits" wrapping="true"/>
								</m:Column>
							</m:columns>
							<m:items>
								<m:ColumnListItem>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<!-- Bug 12518 - Mapping Details name length limit on Interface-->
										<!-- Bug 12584 - Missing ID's on Proxy Extension -->
										<m:Input id="attrName" placeholder="Enter Attribute name..." value="{proxyEnhancementModel>AttrName}" valueState="{proxyEnhancementModel>attrNameValueState}" 
										valueStateText="{proxyEnhancementModel>attrNameValueStateText}" required="true" liveChange="onAttrNameLiveChange" enabled="{proxyEnhancementModel>EbName}" maxLength="30" />
										<!-- End Bug 12584 - Missing ID's on Proxy Extension -->
										<!-- End Bug 12518 - Mapping Details name length limit on Interface-->
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Input id="attrDefaultValue" placeholder="Enter Default Value..." value="{proxyEnhancementModel>DefaultValue}" />
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:CheckBox selected="true" enabled="false"/>
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Select forceSelection="false" items="{path:'proxyEnhancementModel>/abapData' , templateShareable:false}" selectedKey="{proxyEnhancementModel>XSDType}" 
										change="onChangeXSDType" liveChange="onChangeXSDType" required="true" id="xsdAttribute">
											<c:Item key="{proxyEnhancementModel>XSDType}" text="{proxyEnhancementModel>XSDType}"/>
										</m:Select>
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Select forceSelection="false" items="{path:'proxyEnhancementModel>arrAbapTypes' , templateShareable:false}" selectedKey="{proxyEnhancementModel>ABAPType}" 
										change="onABAPTypeChange" liveChange="onABAPTypeChange" valueState="{proxyEnhancementModel>abapTypeState}" 
										valueStateText="{proxyEnhancementModel>abapTypeStateText}" id="attrAbapType">
											<c:Item key="{proxyEnhancementModel>ABAPType}" text="{proxyEnhancementModel>ABAPType}"/>
										</m:Select>
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Input placeholder="Enter Pattern..." value="{proxyEnhancementModel>Pattern}" id="Pattern" enabled="{= ${proxyEnhancementModel>EbPattern}==='X'}"/>
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>Length}" id="Length" enabled="{= ${proxyEnhancementModel>EbLength}==='X'}" liveChange="filterAttributeData"
										valueState="{proxyEnhancementModel>lengthState}" valueStateText="{proxyEnhancementModel>lengthStateText}"/>
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>MaxLength}" id="MaxLength-Attribute" enabled="{= ${proxyEnhancementModel>EbMaxLength}==='X'}" liveChange="onMinMaxLiveChange"
										valueState="{proxyEnhancementModel>MaxLengthState}" valueStateText="{proxyEnhancementModel>MaxLengthStateText}"/>
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>MinLength}" id="MinLength-Attribute" enabled="{= ${proxyEnhancementModel>EbMinLength}==='X'}" liveChange="onMinMaxLiveChange" 
										valueState="{proxyEnhancementModel>MinLengthState}" valueStateText="{proxyEnhancementModel>MinLengthStateText}" />
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>MaxIncl}" id="MaxIncl-Attribute" enabled="{= ${proxyEnhancementModel>EbMaxIncl}==='X'}" liveChange="onMinMaxLiveChange"
										valueState="{proxyEnhancementModel>MaxInclState}" valueStateText="{proxyEnhancementModel>MaxInclStateText}"/>
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>MinIncl}" id="MinIncl-Attribute" enabled="{= ${proxyEnhancementModel>EbMinIncl}==='X'}" liveChange="onMinMaxLiveChange"
										valueState="{proxyEnhancementModel>MinInclState}" valueStateText="{proxyEnhancementModel>MinInclStateText}"/>
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>MaxExcl}" id="MaxExcl-Attribute" enabled="{= ${proxyEnhancementModel>EbMaxExcl}==='X'}" liveChange="onMinMaxLiveChange"
										valueState="{proxyEnhancementModel>MaxExclState}" valueStateText="{proxyEnhancementModel>MaxExclStateText}"/>
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>MinExcl}" id="MinExcl-Attribute" enabled="{= ${proxyEnhancementModel>EbMinExcl}==='X'}" liveChange="onMinMaxLiveChange"
										valueState="{proxyEnhancementModel>MinExclState}" valueStateText="{proxyEnhancementModel>MinExclStateText}"/>
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Input type="Number" placeholder="0" value="{proxyEnhancementModel>TotalDigits}" id="TotalDigits-Attribute" enabled="{= ${proxyEnhancementModel>EbTotalDigits}==='X'}" liveChange="onTotalDigitsLiveChange"/>
									</m:cells>
									<m:cells class="sapMListTblCell dmrTableRow" >
										<m:Input type="Number" placeholder="000000" value="{proxyEnhancementModel>FractionDigits}" id="FractionDigits-Attribute" enabled="{= ${proxyEnhancementModel>EbFractionDigits}==='X'}" liveChange="onFractionDigitsLiveChange"
										valueState="{proxyEnhancementModel>fractionDigitsState}" valueStateText="{proxyEnhancementModel>fractionDigitsStateText}"/>
									</m:cells>
								</m:ColumnListItem>
							</m:items>
						</m:Table>
					</m:ScrollContainer>
					
				</m:WizardStep>
			</m:Wizard>
		</m:content>
		<m:footer>
			<m:Bar>
				<m:contentRight>
					<!-- Bug 12584 - Missing ID's on Proxy Extension -->
					<m:Button id="btnSave" text="Save" press=".onProxySave" enabled="{proxyEnhancementModel>/EbSaveButton}"/>
					<m:Button id="btnCancel" text="Cancel" press=".onWizardCancel"/>
					<!-- End Bug 12584 - Missing ID's on Proxy Extension -->
				</m:contentRight>
			</m:Bar>
		</m:footer>
	</m:Page>
</mvc:View>