{"dataModel": [{"name": "BP", "desc": "Business Partner", "entity": [{"name": "FKKVK", "desc": "Contract Account", "entity": [{"desc": "Contract Account", "hryCol": "FKKVKTEST", "fld_type": "Entity Type Itself", "storage": "True", "element": "MDG_FICA_VKONT", "ref_Ent": ""}, {"name": "APPLK", "desc": "Application Area", "fld_type": "Attribute", "storage": "False", "element": "MDG_FICA_APPLK", "ref_Ent": ""}, {"name": "FKK_LOEVM", "desc": "Deletion Flag", "fld_type": "Attribute", "storage": "False", "element": "MDG_FICA_LOEVM", "ref_Ent": ""}, {"name": "TXTMI", "desc": "Description (Medium Text)", "fld_type": "Attribute", "storage": "False", "element": "USMD_TXTMI", "ref_Ent": ""}, {"name": "VKBEZ_UPP", "desc": "Contract Acct Name", "fld_type": "Attribute", "storage": "False", "element": "MDG_FICA_VKBEZ_UPP", "ref_Ent": ""}, {"name": "VKONA", "desc": "CtrAcct In Legacy Sys", "fld_type": "Attribute", "storage": "False", "element": "MDG_FICA_VKONA", "ref_Ent": ""}, {"name": "VKTYP", "desc": "Contract Acct Categ", "fld_type": "Attribute", "storage": "False", "element": "MDG_FICA_VKTYP", "ref_Ent": ""}, {"name": "FKKVKTD", "desc": "Contract Account (Time Dependence)", "entity": [{"name": "FKKVK", "desc": "Contract Account", "fld_type": "Leading Entity Type", "storage": "True", "element": "MDG_FICA_VKONT", "ref_Ent": ""}, {"name": "FKK_VALDT", "desc": "Validity Date", "fld_type": "Qualifying Entity Type", "storage": "False", "element": "MDG_FICA_VALDT", "ref_Ent": ""}, {"name": "FKKTAXEX", "desc": "Contract Account (Time Dependence)", "entity": [{"name": "FKKVK", "desc": "Contract Account", "fld_type": "Leading Entity Type", "storage": "True", "element": "MDG_FICA_VKONT", "ref_Ent": ""}, {"name": "FKK_EXDFR", "desc": "Tax Exemption Valid From", "fld_type": "Qualifying Entity Type", "storage": "False", "element": "MDG_FICA_EXDFR", "ref_Ent": ""}]}]}]}]}], "storage": [{"key": "1", "value": "Changeable via Change Request; Generated Database Tables"}, {"key": "2", "value": "Changeable w/o Change Request; Generated Check/Text Tables"}, {"key": "3", "value": "Not Changeable via MDG; No Generated Tables"}, {"key": "4", "value": "Changeable via Other Entity Type; Generated Database Tables"}], "validity": [{"key": "E", "value": "Edition"}, {"key": " ", "value": "No Edition"}], "keyHandling": [{"key": " ", "value": "Key Cannot Be Changed; No Internal Key Assignment"}, {"key": "I", "value": "Internal Key Assignment Only"}, {"key": "C", "value": "Key Can Be Changed; No Internal Key Assignment"}, {"key": "B", "value": "Key Can Be Changed; Internal Key Assignment Possible"}]}