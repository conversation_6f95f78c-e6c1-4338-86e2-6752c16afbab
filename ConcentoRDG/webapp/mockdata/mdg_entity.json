{"BP": [{"name": "BP", "description": "Business Partner", "property": [], "isFolder": "X", "relation": ""}, {"name": "FKKVK", "description": "Contract Account", "property": [{"description": "Contract Account", "fieldKind": "Entity Type Itself", "hryCol": "FKKVK", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKONT"}, {"description": "Application Area", "fieldKind": "Attribute", "hryCol": "APPLK", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_APPLK"}, {"description": "Deletion Flag", "fieldKind": "Attribute", "hryCol": "FKK_LOEVM", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_LOEVM"}, {"description": "Description (medium text)", "fieldKind": "Attribute", "hryCol": "TXTMI", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTMI"}, {"description": "Contract Acct Name", "fieldKind": "Attribute", "hryCol": "VKBEZ_UPP", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKBEZ_UPPERCASE"}, {"description": "CtrAcct in LegacySys", "fieldKind": "Attribute", "hryCol": "VKONA", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKONA"}, {"description": "Contract Acct Categ.", "fieldKind": "Attribute", "hryCol": "VKTYP", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKTYP"}, {"description": "Contract Account (Time Dependence)", "fieldKind": "Attribute", "hryCol": "FKKVKTD", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKTYP"}, {"description": "Tax Exemptions (Contract Account)", "fieldKind": "Attribute", "hryCol": "FKKTAXEX", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKTYP"}], "isFolder": "X", "relation": "BP"}, {"name": "FKKVKTD", "property": [{"description": "Contract Account", "fieldKind": "Leading Entity Type", "hryCol": "FKKVK", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKONT"}, {"description": "Validity Date", "fieldKind": "Qualifying Entity Type", "hryCol": "FKK_VALDT", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VALDT"}, {"description": "Application Area", "fieldKind": "Attribute", "hryCol": "TD_APPLK", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_APPLK"}, {"description": "Deletion Flag", "fieldKind": "Attribute", "hryCol": "TD_LOEVM", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_LOEVM"}, {"description": "Contract Acct Name", "fieldKind": "Attribute", "hryCol": "TD_VKBEZ", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKBEZ"}, {"description": "CtrAcct in LegacySys", "fieldKind": "Attribute", "hryCol": "TD_VKONA", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKONA"}, {"description": "Contract Acct Categ.", "fieldKind": "Attribute", "hryCol": "TD_VKTYP", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKTYP"}, {"description": "Contract Account Partner-Specific (Contract Account)", "fieldKind": "Attribute", "hryCol": "FKKVKP", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKTYP"}], "isFolder": "X", "relation": "FKKVK"}, {"name": "FKKVKP", "property": [{"description": "Contract Account", "fieldKind": "Leading Entity Type", "hryCol": "FKKVK", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKONT"}, {"description": "Validity Date", "fieldKind": "Leading Entity Type", "hryCol": "FKK_VALDT", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VALDT"}, {"description": "Business Partner ID", "fieldKind": "Qualifying Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Bank Details ID-Outg", "fieldKind": "Attribute", "hryCol": "ABVTY", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_ABVTY"}, {"description": "Business Partner ID", "fieldKind": "Attribute", "hryCol": "ABWMA", "isMainEt": "", "refEt": "BP_HEADER", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Business Partner ID", "fieldKind": "Attribute", "hryCol": "ABWRA", "isMainEt": "", "refEt": "BP_HEADER", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Business Partner ID", "fieldKind": "Attribute", "hryCol": "ABWRE", "isMainEt": "", "refEt": "BP_HEADER", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Business Partner ID", "fieldKind": "Attribute", "hryCol": "ABWRH", "isMainEt": "", "refEt": "BP_HEADER", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Collective Bill Acct", "fieldKind": "Attribute", "hryCol": "ABWVK", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_ABWVK"}, {"description": "Address Number", "fieldKind": "Attribute", "hryCol": "ADR_CORRD", "isMainEt": "", "refEt": "ADDRNO", "rollname": "AD_ADDRNUM"}, {"description": "Clearing Restriction", "fieldKind": "Attribute", "hryCol": "AUGRS_DEF", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_AUGRS_DEF"}, {"description": "Outgoing Payt Method", "fieldKind": "Attribute", "hryCol": "AZAWE", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_AZAWE"}, {"description": "Predecessor", "fieldKind": "Attribute", "hryCol": "BPCL_SUCC", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_BPCL_SUCC"}, {"description": "Lock Valid From", "fieldKind": "Attribute", "hryCol": "BPL_FDATE", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_FDATE"}, {"description": "Lock Valid To", "fieldKind": "Attribute", "hryCol": "BPL_TDATE", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_TDATE"}, {"description": "Dun./Payt Lock", "fieldKind": "Attribute", "hryCol": "BPL_XADDL", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_BPL_XADDL"}, {"description": "Posting/Clearing Lock", "fieldKind": "Attribute", "hryCol": "BPL_XPOCL", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_BPL_XPOCL"}, {"description": "ID Type 1", "fieldKind": "Attribute", "hryCol": "BPTAXTYP1", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_TAXTYPE1"}, {"description": "ID Type 2", "fieldKind": "Attribute", "hryCol": "BPTAXTYP2", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_TAXTYPE2"}, {"description": "Business Place", "fieldKind": "Attribute", "hryCol": "BUPLA", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_BUPLA"}, {"description": "Pymt Card ID Incomg", "fieldKind": "Attribute", "hryCol": "CCARD_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_CCID_IN"}, {"description": "Payment Card ID Out.", "fieldKind": "Attribute", "hryCol": "CCARD_OUT", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_CCID_OUT"}, {"description": "Master Data Group", "fieldKind": "Attribute", "hryCol": "CMGRP", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_CMGRP"}, {"description": "Correspondence V<PERSON>t", "fieldKind": "Attribute", "hryCol": "COPRC", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_COPRC"}, {"description": "CorrespDunningProc.", "fieldKind": "Attribute", "hryCol": "CORR_MAHN", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_CORR_MAHN"}, {"description": "County code", "fieldKind": "Attribute", "hryCol": "COUNTY", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_COUNC"}, {"description": "Business Partner ID", "fieldKind": "Attribute", "hryCol": "CPERS", "isMainEt": "", "refEt": "BP_HEADER", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Direct Debit Limit", "fieldKind": "Attribute", "hryCol": "DDLAM", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_DDLAM"}, {"description": "<PERSON><PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "DDLCU", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_DDLCU"}, {"description": "Number of Months", "fieldKind": "Attribute", "hryCol": "DDLNM", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_DDLNM"}, {"description": "Rolling", "fieldKind": "Attribute", "hryCol": "DDLXG", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_DDLXG"}, {"description": "Business Partner ID", "fieldKind": "Attribute", "hryCol": "DEF_REC", "isMainEt": "", "refEt": "BP_HEADER", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Activity", "fieldKind": "Attribute", "hryCol": "DEF_REC_I", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_DEF_REC_I"}, {"description": "Bank Det.ID Incoming", "fieldKind": "Attribute", "hryCol": "EBVTY", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_EBVTY"}, {"description": "Own Bank Details", "fieldKind": "Attribute", "hryCol": "EIGBV", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_EIGBV"}, {"description": "Reference Number", "fieldKind": "Attribute", "hryCol": "EXVKO", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_EXVKO"}, {"description": "Incom. Payt Method", "fieldKind": "Attribute", "hryCol": "EZAWE", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_EZAWE"}, {"description": "Planning Group", "fieldKind": "Attribute", "hryCol": "FDGRP", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_FDGRP"}, {"description": "Cash Mgmt Extra Days", "fieldKind": "Attribute", "hryCol": "FDZTG", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_FDZTG"}, {"description": "Address Number", "fieldKind": "Attribute", "hryCol": "FKK_ADRJD", "isMainEt": "", "refEt": "ADDRNO", "rollname": "AD_ADDRNUM"}, {"description": "Address Number", "fieldKind": "Attribute", "hryCol": "FKK_ADRMA", "isMainEt": "", "refEt": "ADDRNO", "rollname": "AD_ADDRNUM"}, {"description": "Address Number", "fieldKind": "Attribute", "hryCol": "FKK_ADRNB", "isMainEt": "", "refEt": "ADDRNO", "rollname": "AD_ADDRNUM"}, {"description": "Address Number", "fieldKind": "Attribute", "hryCol": "FKK_ADRRA", "isMainEt": "", "refEt": "ADDRNO", "rollname": "AD_ADDRNUM"}, {"description": "Address Number", "fieldKind": "Attribute", "hryCol": "FKK_ADRRE", "isMainEt": "", "refEt": "ADDRNO", "rollname": "AD_ADDRNUM"}, {"description": "Address Number", "fieldKind": "Attribute", "hryCol": "FKK_ADRRH", "isMainEt": "", "refEt": "ADDRNO", "rollname": "AD_ADDRNUM"}, {"description": "Authorization Group", "fieldKind": "Attribute", "hryCol": "FKK_BEGRU", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_BEGRU"}, {"description": "Tax Type", "fieldKind": "Attribute", "hryCol": "FKK_FITYP", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_FITYP"}, {"description": "Deletion Flag", "fieldKind": "Attribute", "hryCol": "FKK_LVRMK", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_LVRMK"}, {"description": "Dunning Grouping", "fieldKind": "Attribute", "hryCol": "FKK_MGRUP", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_MGRUP"}, {"description": "<PERSON>id <PERSON>", "fieldKind": "Attribute", "hryCol": "FKK_QSZDT", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_QSZDT"}, {"description": "Exemption Number", "fieldKind": "Attribute", "hryCol": "FKK_QSZNR", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_QSZNR"}, {"description": "Tolerance Group", "fieldKind": "Attribute", "hryCol": "FKK_TOGRU", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_TOGRU"}, {"description": "Trading Partner", "fieldKind": "Attribute", "hryCol": "FKK_VBUND", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VBUND"}, {"description": "Business Partner ID", "fieldKind": "Attribute", "hryCol": "GPARV", "isMainEt": "", "refEt": "BP_HEADER", "rollname": "BU_BUSINESSPARTNER"}, {"description": "GUID", "fieldKind": "Attribute", "hryCol": "GUID", "isMainEt": "", "refEt": "", "rollname": "CHAR32"}, {"description": "Interest Key", "fieldKind": "Attribute", "hryCol": "IKEY", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_IKEY"}, {"description": "Invoicing Category", "fieldKind": "Attribute", "hryCol": "INV_CATEG", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_INV_CATEG"}, {"description": "Inv. Target Currency", "fieldKind": "Attribute", "hryCol": "INV_CURR", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_INV_CURR"}, {"description": "Billing Cycle", "fieldKind": "Attribute", "hryCol": "INV_CYCLE", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_INV_CYCLE"}, {"description": "Specified Day", "fieldKind": "Attribute", "hryCol": "INV_CYC_D", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_INV_CYC_D"}, {"description": "Start Date", "fieldKind": "Attribute", "hryCol": "INV_CYC_S", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_INV_CYC_S"}, {"description": "Alt. Form Determin.", "fieldKind": "Attribute", "hryCol": "INV_FORM", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_INV_FORM"}, {"description": "Scheduling", "fieldKind": "Attribute", "hryCol": "INV_SCHED", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_INV_SCHED"}, {"description": "Destination country", "fieldKind": "Attribute", "hryCol": "LANDL", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_LANDL"}, {"description": "Dunning Procedure", "fieldKind": "Attribute", "hryCol": "MAHNV", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_MAHNV"}, {"description": "Mandate Reference", "fieldKind": "Attribute", "hryCol": "MNDID", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_MNDID"}, {"description": "Company Code Group", "fieldKind": "Attribute", "hryCol": "OPBUK", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_OPBUK"}, {"description": "Business Partner ID", "fieldKind": "Attribute", "hryCol": "PERSR", "isMainEt": "", "refEt": "BP_HEADER", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Region", "fieldKind": "Attribute", "hryCol": "PROVINCE", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_REGIO"}, {"description": "W/Htax Code O.Payts", "fieldKind": "Attribute", "hryCol": "QSSKZ_A", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_QSSKZ_A"}, {"description": "W/Htax Code I.Payts", "fieldKind": "Attribute", "hryCol": "QSSKZ_E", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_QSSKZ_E"}, {"description": "Standard CompanyCode", "fieldKind": "Attribute", "hryCol": "STDBK", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_STDBK"}, {"description": "Process. Lock To", "fieldKind": "Attribute", "hryCol": "STOPD", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_STOPD"}, {"description": "Proc. Lock Reason", "fieldKind": "Attribute", "hryCol": "STOPG", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_STOPG"}, {"description": "Collection Strategy", "fieldKind": "Attribute", "hryCol": "STRAT", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_STRAT"}, {"description": "Items Transferred", "fieldKind": "Attribute", "hryCol": "UEBTR", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_UEBTR"}, {"description": "Clearing Category", "fieldKind": "Attribute", "hryCol": "VERTYP", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VERTYP"}, {"description": "Account Used for Pyt", "fieldKind": "Attribute", "hryCol": "VKONV", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKONV"}, {"description": "Contract Acct Rel.", "fieldKind": "Attribute", "hryCol": "VKPBZ", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKPBZ"}, {"description": "Line Items", "fieldKind": "Attribute", "hryCol": "VWNZA", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VWNZA"}, {"description": "Correspondence for Relationship Contract Account<->Partner", "fieldKind": "Attribute", "hryCol": "FKKVKCORR", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VWNZA"}, {"description": "Texts (Contract Account)", "fieldKind": "Attribute", "hryCol": "FKKTXT", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VWNZA"}, {"description": "Business Locks (Contract Account)", "fieldKind": "Attribute", "hryCol": "FKKLOCKS", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VWNZA"}], "isFolder": "X", "relation": "FKKVKTD"}, {"name": "FKKVKCORR", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Contract Account", "fieldKind": "Leading Entity Type", "hryCol": "FKKVK", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKONT"}, {"description": "Validity Date", "fieldKind": "Leading Entity Type", "hryCol": "FKK_VALDT", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VALDT"}, {"description": "Internal Counter", "fieldKind": "Qualifying Entity Type", "hryCol": "FKK_IDENT", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_IDENTNUMBER"}, {"description": "Address Number", "fieldKind": "Attribute", "hryCol": "ADR_CORR", "isMainEt": "", "refEt": "ADDRNO", "rollname": "AD_ADDRNUM"}, {"description": "Correspondence Roles", "fieldKind": "Attribute", "hryCol": "CORR_ROLE", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_CORR_ROLE"}, {"description": "Correspondence Type", "fieldKind": "Attribute", "hryCol": "COTYP", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_COTYP"}, {"description": "Activity", "fieldKind": "Attribute", "hryCol": "IND_RECEI", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_IND_RECEI"}, {"description": "Business Partner ID", "fieldKind": "Attribute", "hryCol": "RECEIVER", "isMainEt": "", "refEt": "BP_HEADER", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Alt./Add. Recipient", "fieldKind": "Attribute", "hryCol": "SUBSTITUT", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_SUBSTITUTE"}, {"description": "Address Selected", "fieldKind": "Attribute", "hryCol": "XADR_CORR", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_ADRNR_CORR_SET"}], "isFolder": "X", "relation": "FKKVKP"}, {"name": "FKKTXT", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Contract Account", "fieldKind": "Leading Entity Type", "hryCol": "FKKVK", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKONT"}, {"description": "Validity Date", "fieldKind": "Leading Entity Type", "hryCol": "FKK_VALDT", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VALDT"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "FKK_LANGU", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Text-ID", "fieldKind": "Qualifying Entity Type", "hryCol": "FKK_TDID", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_TDID"}, {"description": "Long Text", "fieldKind": "Attribute", "hryCol": "FKK_LTEXT", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_STRING"}], "isFolder": "X", "relation": "FKKVKP"}, {"name": "FKKLOCKS", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Contract Account", "fieldKind": "Leading Entity Type", "hryCol": "FKKVK", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKONT"}, {"description": "Validity Date", "fieldKind": "Leading Entity Type", "hryCol": "FKK_VALDT", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VALDT"}, {"description": "Lock Valid From", "fieldKind": "Qualifying Entity Type", "hryCol": "FKK_FDATE", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_FDATE"}, {"description": "Lock Reason", "fieldKind": "Qualifying Entity Type", "hryCol": "FKK_LOCKR", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_LOCKR"}, {"description": "Process", "fieldKind": "Qualifying Entity Type", "hryCol": "FKK_PROID", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_PROID"}, {"description": "Lock Valid To", "fieldKind": "Qualifying Entity Type", "hryCol": "FKK_TDATE", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_TDATE"}, {"description": "Dummy", "fieldKind": "Attribute", "hryCol": "FKK_DUMMY", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_DUMMY"}], "isFolder": "X", "relation": "FKKVKP"}, {"name": "FKKTAXEX", "property": [{"description": "Contract Account", "fieldKind": "Leading Entity Type", "hryCol": "FKKVK", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_VKONT"}, {"description": "Tax Exemption Valid From", "fieldKind": "Qualifying Entity Type", "hryCol": "FKK_EXDFR", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_EXDFR"}, {"description": "Condition Type", "fieldKind": "Qualifying Entity Type", "hryCol": "FKK_KSCHL", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_KSCHL"}, {"description": "Tax Code", "fieldKind": "Qualifying Entity Type", "hryCol": "FKK_MWSKZ", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_MWSKZ"}, {"description": "Exempted Until", "fieldKind": "Attribute", "hryCol": "EXDTO", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_EXDTO"}, {"description": "Licence Number", "fieldKind": "Attribute", "hryCol": "EXNUM", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_EXNUM"}, {"description": "Exempt Amount Rate", "fieldKind": "Attribute", "hryCol": "EXRAT", "isMainEt": "", "refEt": "", "rollname": "MDG_FICA_EXRAT"}], "isFolder": "X", "relation": "FKKVK"}, {"name": "BP_SUBHRY", "property": [{"description": "Structure Node", "fieldKind": "Entity Type Itself", "hryCol": "BP_SUBHRY", "isMainEt": "", "refEt": "", "rollname": "BU_SUBHIERARCHY"}, {"description": "Description (long text)", "fieldKind": "Attribute", "hryCol": "TXTLG", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}], "isFolder": "X", "relation": "BP"}, {"name": "BP_REL", "property": [{"description": "Relationship Cat.", "fieldKind": "Entity Type Itself", "hryCol": "BP_REL", "isMainEt": "", "refEt": "", "rollname": "BU_RELTYP"}, {"description": "Business partner 1", "fieldKind": "Key Attribute", "hryCol": "PARTNER1", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER1"}, {"description": "Business Partner 2", "fieldKind": "Key Attribute", "hryCol": "PARTNER2", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER2"}, {"description": "Standard", "fieldKind": "Attribute", "hryCol": "XDFREL", "isMainEt": "", "refEt": "", "rollname": "BU_XDFREL"}, {"description": "BP relationship contact person general data", "fieldKind": "Attribute", "hryCol": "BP_CPGEN", "isMainEt": "", "refEt": "", "rollname": "BU_XDFREL"}], "isFolder": "X", "relation": "BP"}, {"name": "BP_CPGEN", "property": [{"description": "Relationship Cat.", "fieldKind": "Leading Entity Type", "hryCol": "BP_REL", "isMainEt": "", "refEt": "", "rollname": "BU_RELTYP"}, {"description": "Business partner 1", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER1", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER1"}, {"description": "Business Partner 2", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER2", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER2"}, {"description": "Department", "fieldKind": "Attribute", "hryCol": "DPRTM", "isMainEt": "", "refEt": "", "rollname": "BU_ABTNR"}, {"description": "Authority", "fieldKind": "Attribute", "hryCol": "PAAUTH", "isMainEt": "", "refEt": "", "rollname": "BU_PAAUTH"}, {"description": "Function", "fieldKind": "Attribute", "hryCol": "PAFKT", "isMainEt": "", "refEt": "", "rollname": "BU_PAFKT"}, {"description": "Note", "fieldKind": "Attribute", "hryCol": "PAREM", "isMainEt": "", "refEt": "", "rollname": "BU_PAREM"}, {"description": "VIP", "fieldKind": "Attribute", "hryCol": "PAVIP", "isMainEt": "", "refEt": "", "rollname": "BU_PAVIP"}, {"description": "BP contact person workplace address", "fieldKind": "Attribute", "hryCol": "BP_WPAD", "isMainEt": "", "refEt": "", "rollname": "BU_PAVIP"}], "isFolder": "X", "relation": "BP_REL"}, {"name": "BP_WPAD", "property": [{"description": "Relationship Cat.", "fieldKind": "Leading Entity Type", "hryCol": "BP_REL", "isMainEt": "", "refEt": "", "rollname": "BU_RELTYP"}, {"description": "Address Number", "fieldKind": "Qualifying Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Business partner 1", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER1", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER1"}, {"description": "Business Partner 2", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER2", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER2"}, {"description": "Building code", "fieldKind": "Attribute", "hryCol": "BLDNG_P", "isMainEt": "", "refEt": "", "rollname": "AD_BLDNG_P"}, {"description": "Comm. Method", "fieldKind": "Attribute", "hryCol": "COMM_TYPE", "isMainEt": "", "refEt": "", "rollname": "AD_COMM"}, {"description": "Department", "fieldKind": "Attribute", "hryCol": "DPRTMNT", "isMainEt": "", "refEt": "", "rollname": "AD_DPRTMNT"}, {"description": "Floor", "fieldKind": "Attribute", "hryCol": "FLOOR", "isMainEt": "", "refEt": "", "rollname": "AD_FLOOR"}, {"description": "Function", "fieldKind": "Attribute", "hryCol": "FUNCTION", "isMainEt": "", "refEt": "", "rollname": "AD_FNCTN"}, {"description": "Internal mail", "fieldKind": "Attribute", "hryCol": "IH_MAIL", "isMainEt": "", "refEt": "", "rollname": "AD_IH_MAIL"}, {"description": "Short name", "fieldKind": "Attribute", "hryCol": "INITS_SIG", "isMainEt": "", "refEt": "", "rollname": "AD_ID_CODE"}, {"description": "Room Number", "fieldKind": "Attribute", "hryCol": "ROOM_NO", "isMainEt": "", "refEt": "", "rollname": "AD_ROOMNUM"}, {"description": "Stand. address", "fieldKind": "Attribute", "hryCol": "XDFADR", "isMainEt": "", "refEt": "", "rollname": "BU_XDFADR"}, {"description": "Workplace Address: Internet Address", "fieldKind": "Attribute", "hryCol": "WP_URL", "isMainEt": "", "refEt": "", "rollname": "BU_XDFADR"}, {"description": "Workplace Address: Telephone Number", "fieldKind": "Attribute", "hryCol": "WP_TEL", "isMainEt": "", "refEt": "", "rollname": "BU_XDFADR"}, {"description": "Workplace Address: International Versions", "fieldKind": "Attribute", "hryCol": "WP_POSTAL", "isMainEt": "", "refEt": "", "rollname": "BU_XDFADR"}, {"description": "Workplace Address: Fax Number", "fieldKind": "Attribute", "hryCol": "WP_FAX", "isMainEt": "", "refEt": "", "rollname": "BU_XDFADR"}, {"description": "Workplace Address: E-Mail Address", "fieldKind": "Attribute", "hryCol": "WP_EMAIL", "isMainEt": "", "refEt": "", "rollname": "BU_XDFADR"}], "isFolder": "X", "relation": "BP_CPGEN"}, {"name": "WP_URL", "property": [{"description": "Address Number", "fieldKind": "Leading Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Relationship Cat.", "fieldKind": "Leading Entity Type", "hryCol": "BP_REL", "isMainEt": "", "refEt": "", "rollname": "BU_RELTYP"}, {"description": "Sequence Number", "fieldKind": "Qualifying Entity Type", "hryCol": "AD_CONSNO", "isMainEt": "", "refEt": "", "rollname": "AD_CONSNUM"}, {"description": "Business partner 1", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER1", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER1"}, {"description": "Business Partner 2", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER2", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER2"}, {"description": "URI", "fieldKind": "Attribute", "hryCol": "U_ADDRESS", "isMainEt": "", "refEt": "", "rollname": "AD_URISCR"}, {"description": "Standard No.", "fieldKind": "Attribute", "hryCol": "U_FLGDEFT", "isMainEt": "", "refEt": "", "rollname": "AD_FLGDFNR"}, {"description": "Do Not Use Communication Number", "fieldKind": "Attribute", "hryCol": "U_FLGNOUS", "isMainEt": "", "refEt": "", "rollname": "AD_FLNOUSE"}, {"description": "URI address", "fieldKind": "Attribute", "hryCol": "U_SEARCH", "isMainEt": "", "refEt": "", "rollname": "AD_URI2"}, {"description": "URI type", "fieldKind": "Attribute", "hryCol": "U_TYPE", "isMainEt": "", "refEt": "", "rollname": "AD_URITYPE"}], "isFolder": "X", "relation": "BP_WPAD"}, {"name": "WP_TEL", "property": [{"description": "Address Number", "fieldKind": "Leading Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Relationship Cat.", "fieldKind": "Leading Entity Type", "hryCol": "BP_REL", "isMainEt": "", "refEt": "", "rollname": "BU_RELTYP"}, {"description": "Sequence Number", "fieldKind": "Qualifying Entity Type", "hryCol": "AD_CONSNO", "isMainEt": "", "refEt": "", "rollname": "AD_CONSNUM"}, {"description": "Business partner 1", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER1", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER1"}, {"description": "Business Partner 2", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER2", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER2"}, {"description": "Country", "fieldKind": "Attribute", "hryCol": "T_COUNTRY", "isMainEt": "", "refEt": "", "rollname": "AD_COMCTRY"}, {"description": "Extension", "fieldKind": "Attribute", "hryCol": "T_EXTENS", "isMainEt": "", "refEt": "", "rollname": "AD_TLXTNS"}, {"description": "Standard No.", "fieldKind": "Attribute", "hryCol": "T_FLGDEFT", "isMainEt": "", "refEt": "", "rollname": "AD_FLGDFNR"}, {"description": "Mobile phone", "fieldKind": "Attribute", "hryCol": "T_FLGMOB", "isMainEt": "", "refEt": "", "rollname": "AD_FLGMOB"}, {"description": "Do Not Use Communication Number", "fieldKind": "Attribute", "hryCol": "T_FLGNOUS", "isMainEt": "", "refEt": "", "rollname": "AD_FLNOUSE"}, {"description": "SMS-Enab.", "fieldKind": "Attribute", "hryCol": "T_FLGSMS", "isMainEt": "", "refEt": "", "rollname": "AD_FLGSMS"}, {"description": "Caller number", "fieldKind": "Attribute", "hryCol": "T_NR_CALL", "isMainEt": "", "refEt": "", "rollname": "AD_TELNRCL"}, {"description": "Telephone number", "fieldKind": "Attribute", "hryCol": "T_NR_LONG", "isMainEt": "", "refEt": "", "rollname": "AD_TELNRLG"}, {"description": "Telephone", "fieldKind": "Attribute", "hryCol": "T_NUMBER", "isMainEt": "", "refEt": "", "rollname": "AD_TLNMBR"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "T_VALID_F", "isMainEt": "", "refEt": "", "rollname": "BU_DATFROM"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "T_VALID_T", "isMainEt": "", "refEt": "", "rollname": "BU_DATTO"}], "isFolder": "X", "relation": "BP_WPAD"}, {"name": "WP_POSTAL", "property": [{"description": "Address Number", "fieldKind": "Leading Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Relationship Cat.", "fieldKind": "Leading Entity Type", "hryCol": "BP_REL", "isMainEt": "", "refEt": "", "rollname": "BU_RELTYP"}, {"description": "Address Version", "fieldKind": "Qualifying Entity Type", "hryCol": "AD_NATION", "isMainEt": "", "refEt": "", "rollname": "AD_NATION"}, {"description": "Business partner 1", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER1", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER1"}, {"description": "Business Partner 2", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER2", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER2"}, {"description": "Building code", "fieldKind": "Attribute", "hryCol": "WP_BLDNGP", "isMainEt": "", "refEt": "", "rollname": "AD_BLDNG_P"}, {"description": "Comm. Method", "fieldKind": "Attribute", "hryCol": "WP_COMMTP", "isMainEt": "", "refEt": "", "rollname": "AD_COMM"}, {"description": "Department", "fieldKind": "Attribute", "hryCol": "WP_DPRTMN", "isMainEt": "", "refEt": "", "rollname": "AD_DPRTMNT"}, {"description": "Floor", "fieldKind": "Attribute", "hryCol": "WP_FLOOR", "isMainEt": "", "refEt": "", "rollname": "AD_FLOOR"}, {"description": "Function", "fieldKind": "Attribute", "hryCol": "WP_FNCTN", "isMainEt": "", "refEt": "", "rollname": "AD_FNCTN"}, {"description": "Internal mail", "fieldKind": "Attribute", "hryCol": "WP_IHMAIL", "isMainEt": "", "refEt": "", "rollname": "AD_IH_MAIL"}, {"description": "Short name", "fieldKind": "Attribute", "hryCol": "WP_INITSG", "isMainEt": "", "refEt": "", "rollname": "AD_ID_CODE"}, {"description": "Room Number", "fieldKind": "Attribute", "hryCol": "WP_ROOMNO", "isMainEt": "", "refEt": "", "rollname": "AD_ROOMNUM"}, {"description": "Stand. address", "fieldKind": "Attribute", "hryCol": "WP_XDFADR", "isMainEt": "", "refEt": "", "rollname": "BU_XDFADR"}], "isFolder": "X", "relation": "BP_WPAD"}, {"name": "WP_FAX", "property": [{"description": "Address Number", "fieldKind": "Leading Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Relationship Cat.", "fieldKind": "Leading Entity Type", "hryCol": "BP_REL", "isMainEt": "", "refEt": "", "rollname": "BU_RELTYP"}, {"description": "Sequence Number", "fieldKind": "Qualifying Entity Type", "hryCol": "AD_CONSNO", "isMainEt": "", "refEt": "", "rollname": "AD_CONSNUM"}, {"description": "Business partner 1", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER1", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER1"}, {"description": "Business Partner 2", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER2", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER2"}, {"description": "Country", "fieldKind": "Attribute", "hryCol": "F_COUNTRY", "isMainEt": "", "refEt": "", "rollname": "AD_COMCTRY"}, {"description": "Extension", "fieldKind": "Attribute", "hryCol": "F_EXTENS", "isMainEt": "", "refEt": "", "rollname": "AD_FXXTNS"}, {"description": "Standard No.", "fieldKind": "Attribute", "hryCol": "F_FLGDEFT", "isMainEt": "", "refEt": "", "rollname": "AD_FLGDFNR"}, {"description": "Do Not Use Communication Number", "fieldKind": "Attribute", "hryCol": "F_FLGNOUS", "isMainEt": "", "refEt": "", "rollname": "AD_FLNOUSE"}, {"description": "Sender number", "fieldKind": "Attribute", "hryCol": "F_NR_CALL", "isMainEt": "", "refEt": "", "rollname": "AD_FAXNRCL"}, {"description": "Fax number", "fieldKind": "Attribute", "hryCol": "F_NR_LONG", "isMainEt": "", "refEt": "", "rollname": "AD_FXNRLNG"}, {"description": "Fax", "fieldKind": "Attribute", "hryCol": "F_NUMBER", "isMainEt": "", "refEt": "", "rollname": "AD_FXNMBR"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "F_VALID_F", "isMainEt": "", "refEt": "", "rollname": "BU_DATFROM"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "F_VALID_T", "isMainEt": "", "refEt": "", "rollname": "BU_DATTO"}], "isFolder": "X", "relation": "BP_WPAD"}, {"name": "WP_EMAIL", "property": [{"description": "Address Number", "fieldKind": "Leading Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Relationship Cat.", "fieldKind": "Leading Entity Type", "hryCol": "BP_REL", "isMainEt": "", "refEt": "", "rollname": "BU_RELTYP"}, {"description": "Sequence Number", "fieldKind": "Qualifying Entity Type", "hryCol": "AD_CONSNO", "isMainEt": "", "refEt": "", "rollname": "AD_CONSNUM"}, {"description": "Business partner 1", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER1", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER1"}, {"description": "Business Partner 2", "fieldKind": "Inherited Key Attribute", "hryCol": "PARTNER2", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNER2"}, {"description": "E-Mail Address", "fieldKind": "Attribute", "hryCol": "E_ADDRESS", "isMainEt": "", "refEt": "", "rollname": "AD_SMTPADR"}, {"description": "Standard No.", "fieldKind": "Attribute", "hryCol": "E_FLGDEFT", "isMainEt": "", "refEt": "", "rollname": "AD_FLGDFNR"}, {"description": "Do Not Use Communication Number", "fieldKind": "Attribute", "hryCol": "E_FLGNOUS", "isMainEt": "", "refEt": "", "rollname": "AD_FLNOUSE"}, {"description": "E-Mail Address", "fieldKind": "Attribute", "hryCol": "E_SEARCH", "isMainEt": "", "refEt": "", "rollname": "AD_SMTPAD2"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "E_VALID_F", "isMainEt": "", "refEt": "", "rollname": "BU_DATFROM"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "E_VALID_T", "isMainEt": "", "refEt": "", "rollname": "BU_DATTO"}], "isFolder": "X", "relation": "BP_WPAD"}, {"name": "BP_HRCHY", "property": [{"description": "BPartner Hierarchy", "fieldKind": "Entity Type Itself", "hryCol": "BP_HRCHY", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER_HIERARCHY"}, {"description": "Description (long text)", "fieldKind": "Attribute", "hryCol": "TXTLG", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}], "isFolder": "X", "relation": "BP"}, {"name": "BP_HEADER", "property": [{"description": "Business Partner ID", "fieldKind": "Entity Type Itself", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "BP_GUID", "fieldKind": "Attribute", "hryCol": "BP_GUID", "isMainEt": "", "refEt": "", "rollname": "CHAR32"}, {"description": "Grouping", "fieldKind": "Attribute", "hryCol": "BU_GROUP", "isMainEt": "", "refEt": "", "rollname": "BU_GROUP"}, {"description": "BP Category", "fieldKind": "Attribute", "hryCol": "BU_TYPE", "isMainEt": "", "refEt": "", "rollname": "BU_TYPE"}, {"description": "Description (long text)", "fieldKind": "Attribute", "hryCol": "TXTLG", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}, {"description": "", "fieldKind": "Attribute", "hryCol": "ZEXTRAINF", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}, {"description": "Tax Numbers", "fieldKind": "Attribute", "hryCol": "BP_TAXNUM", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}, {"description": "Role", "fieldKind": "Attribute", "hryCol": "BP_ROLE", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}, {"description": "Multiple Assignment of Customer/Supplier", "fieldKind": "Attribute", "hryCol": "BP_MLT_AS", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}, {"description": "Industry", "fieldKind": "Attribute", "hryCol": "BP_INDSTR", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}, {"description": "Identification Numbers", "fieldKind": "Attribute", "hryCol": "BP_IDNUM", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}, {"description": "Central Data", "fieldKind": "Attribute", "hryCol": "BP_CENTRL", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}, {"description": "Payment Cards", "fieldKind": "Attribute", "hryCol": "BP_CCDTL", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}, {"description": "Bank Details", "fieldKind": "Attribute", "hryCol": "BP_BKDTL", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}, {"description": "Addresses", "fieldKind": "Attribute", "hryCol": "BP_ADDR", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}, {"description": "Address", "fieldKind": "Attribute", "hryCol": "ADDRESS", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTLG"}], "isFolder": "X", "relation": "BP"}, {"name": "ZEXTRAINF", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Material", "fieldKind": "Qualifying Entity Type", "hryCol": "ZPARTNUM", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Blocked", "fieldKind": "Attribute", "hryCol": "ZZDELBLOC", "isMainEt": "", "refEt": "", "rollname": "BLOCKED"}], "isFolder": "X", "relation": "BP_HEADER"}, {"name": "BP_TAXNUM", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Tax Number Category", "fieldKind": "Qualifying Entity Type", "hryCol": "BP_TX_TYP", "isMainEt": "", "refEt": "", "rollname": "BPTAXTYPE"}, {"description": "Tax number", "fieldKind": "Attribute", "hryCol": "TAXNUM", "isMainEt": "", "refEt": "", "rollname": "BPTAXNUM"}, {"description": "Tax Number", "fieldKind": "Attribute", "hryCol": "TAXNUMXL", "isMainEt": "", "refEt": "", "rollname": "BPTAXNUMXL"}], "isFolder": "X", "relation": "BP_HEADER"}, {"name": "BP_ROLE", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "BP Role", "fieldKind": "Qualifying Entity Type", "hryCol": "BP_ROL_ID", "isMainEt": "", "refEt": "", "rollname": "BU_PARTNERROLE"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "ROL_VFROM", "isMainEt": "", "refEt": "", "rollname": "BU_VALID_FROM_DATS"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "ROL_VTO", "isMainEt": "", "refEt": "", "rollname": "BU_VALID_TO_DATS"}], "isFolder": "X", "relation": "BP_HEADER"}, {"name": "BP_MLT_AS", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Assignment ID", "fieldKind": "Qualifying Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Assignment Type", "fieldKind": "Attribute", "hryCol": "AS_TYPE", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_TYPE"}, {"description": "Object ID", "fieldKind": "Attribute", "hryCol": "OBJECT_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_OBJECT_ID"}, {"description": "Reason", "fieldKind": "Attribute", "hryCol": "REASON_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_REASON_ID"}, {"description": "Indicator", "fieldKind": "Attribute", "hryCol": "STANDARD", "isMainEt": "", "refEt": "", "rollname": "FLAG"}, {"description": "General Data (Supplier)", "fieldKind": "Attribute", "hryCol": "BP_VENGEN", "isMainEt": "", "refEt": "", "rollname": "FLAG"}, {"description": "Multiple Addresses of Customer/Supplier", "fieldKind": "Attribute", "hryCol": "BP_MLT_AD", "isMainEt": "", "refEt": "", "rollname": "FLAG"}, {"description": "", "fieldKind": "Attribute", "hryCol": "BP_CUSGEN", "isMainEt": "", "refEt": "", "rollname": "FLAG"}], "isFolder": "X", "relation": "BP_HEADER"}, {"name": "BP_VENGEN", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Social Ins. Code", "fieldKind": "Attribute", "hryCol": "ACTSS", "isMainEt": "", "refEt": "", "rollname": "J_1AACTSS"}, {"description": "Train station", "fieldKind": "Attribute", "hryCol": "BAHNS", "isMainEt": "", "refEt": "", "rollname": "BAHNS"}, {"description": "ServAgntProcGrp", "fieldKind": "Attribute", "hryCol": "DLGRP", "isMainEt": "", "refEt": "", "rollname": "DLGRP"}, {"description": "DME indicator", "fieldKind": "Attribute", "hryCol": "DTAMS", "isMainEt": "", "refEt": "", "rollname": "DTAMS"}, {"description": "Ext. manufacturer", "fieldKind": "Attribute", "hryCol": "EMNFR", "isMainEt": "", "refEt": "", "rollname": "EMNFR"}, {"description": "PBC/POR number", "fieldKind": "Attribute", "hryCol": "ESRNR", "isMainEt": "", "refEt": "", "rollname": "ESRNR"}, {"description": "Fiscal address", "fieldKind": "Attribute", "hryCol": "FISKN", "isMainEt": "", "refEt": "", "rollname": "FISKN_K"}, {"description": "Tax Office Responsible", "fieldKind": "Attribute", "hryCol": "FISKU", "isMainEt": "", "refEt": "", "rollname": "FISKU"}, {"description": "Tax split", "fieldKind": "Attribute", "hryCol": "IPISP", "isMainEt": "", "refEt": "", "rollname": "J_1BINDEQU"}, {"description": "Group key", "fieldKind": "Attribute", "hryCol": "KONZS", "isMainEt": "", "refEt": "", "rollname": "KONZS"}, {"description": "Information Number", "fieldKind": "Attribute", "hryCol": "KRAUS", "isMainEt": "", "refEt": "", "rollname": "KRAUS_CM"}, {"description": "Account group", "fieldKind": "Attribute", "hryCol": "KTOKK", "isMainEt": "", "refEt": "", "rollname": "KTOKK"}, {"description": "<PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "LIFNR", "isMainEt": "", "refEt": "", "rollname": "LIFNR"}, {"description": "Alternative Payee", "fieldKind": "Attribute", "hryCol": "LNRZA", "isMainEt": "", "refEt": "", "rollname": "LNRZA"}, {"description": "Central deletion flag", "fieldKind": "Attribute", "hryCol": "LOEVM", "isMainEt": "", "refEt": "", "rollname": "LOEVM_X"}, {"description": "Vendor sub-range relevant", "fieldKind": "Attribute", "hryCol": "LTSNA", "isMainEt": "", "refEt": "", "rollname": "LTSNA"}, {"description": "Central del.block", "fieldKind": "Attribute", "hryCol": "NODEL_A", "isMainEt": "", "refEt": "", "rollname": "NODEL_X"}, {"description": "Factory Calendar", "fieldKind": "Attribute", "hryCol": "PLKAL", "isMainEt": "", "refEt": "", "rollname": "FABKL"}, {"description": "Relevant for POD", "fieldKind": "Attribute", "hryCol": "PODKZB", "isMainEt": "", "refEt": "", "rollname": "PODKZB"}, {"description": "Actual QM System", "fieldKind": "Attribute", "hryCol": "QSSYS", "isMainEt": "", "refEt": "", "rollname": "QSSYS_IST"}, {"description": "QM System Valid To", "fieldKind": "Attribute", "hryCol": "QSSYSDAT", "isMainEt": "", "refEt": "", "rollname": "QQSSYSDAT"}, {"description": "Authorization object", "fieldKind": "Attribute", "hryCol": "REF_BPVEN", "isMainEt": "", "refEt": "BROBJ", "rollname": "BROBJ"}, {"description": "Authorization Group", "fieldKind": "Attribute", "hryCol": "REF_VENGE", "isMainEt": "", "refEt": "BEGRU", "rollname": "BRGRU"}, {"description": "Payment Method", "fieldKind": "Attribute", "hryCol": "REF_VNGEN", "isMainEt": "", "refEt": "PYMT_METH", "rollname": "DZLSCH"}, {"description": "Social Insurance", "fieldKind": "Attribute", "hryCol": "REGSS", "isMainEt": "", "refEt": "", "rollname": "J_1AREGSS"}, {"description": "Last External Review", "fieldKind": "Attribute", "hryCol": "REVDB", "isMainEt": "", "refEt": "", "rollname": "REVDB_CM"}, {"description": "Instruction key", "fieldKind": "Attribute", "hryCol": "RF_VENDGN", "isMainEt": "", "refEt": "DTAWS", "rollname": "DTAWS"}, {"description": "Country Key", "fieldKind": "Attribute", "hryCol": "RF_VENGEN", "isMainEt": "", "refEt": "LAND1", "rollname": "LAND1"}, {"description": "SCAC", "fieldKind": "Attribute", "hryCol": "SCACD", "isMainEt": "", "refEt": "", "rollname": "SCACD"}, {"description": "Carrier freight grp", "fieldKind": "Attribute", "hryCol": "SFRGR", "isMainEt": "", "refEt": "", "rollname": "SFRGR"}, {"description": "Central purchasing block", "fieldKind": "Attribute", "hryCol": "SPERM", "isMainEt": "", "refEt": "", "rollname": "SPERM_X"}, {"description": "Block Function", "fieldKind": "Attribute", "hryCol": "SPERQ", "isMainEt": "", "refEt": "", "rollname": "QSPERRFKT"}, {"description": "Central posting block", "fieldKind": "Attribute", "hryCol": "SPERR", "isMainEt": "", "refEt": "", "rollname": "SPERB_X"}, {"description": "Tax Number at Responsible Tax Authority", "fieldKind": "Attribute", "hryCol": "STENR", "isMainEt": "", "refEt": "", "rollname": "STENR"}, {"description": "Stat. grp, service agent", "fieldKind": "Attribute", "hryCol": "STGDL", "isMainEt": "", "refEt": "", "rollname": "STGDL"}, {"description": "Sales equalizatn tax", "fieldKind": "Attribute", "hryCol": "STKZA", "isMainEt": "", "refEt": "", "rollname": "STKZA"}, {"description": "Liable for VAT", "fieldKind": "Attribute", "hryCol": "STKZU", "isMainEt": "", "refEt": "", "rollname": "STKZU"}, {"description": "Tax base", "fieldKind": "Attribute", "hryCol": "TAXBS", "isMainEt": "", "refEt": "", "rollname": "TAXBS_MDG_BP"}, {"description": "Company ID", "fieldKind": "Attribute", "hryCol": "VBUND", "isMainEt": "", "refEt": "", "rollname": "VBUND"}, {"description": "Tax type", "fieldKind": "Attribute", "hryCol": "VEN_FITYP", "isMainEt": "", "refEt": "", "rollname": "J_1AFITP_D"}, {"description": "Type of Business", "fieldKind": "Attribute", "hryCol": "VEN_KFBUS", "isMainEt": "", "refEt": "", "rollname": "GESTYP"}, {"description": "Type of Industry", "fieldKind": "Attribute", "hryCol": "VEN_KFIND", "isMainEt": "", "refEt": "", "rollname": "INDTYP"}, {"description": "Name of Representative", "fieldKind": "Attribute", "hryCol": "VEN_KFREP", "isMainEt": "", "refEt": "", "rollname": "REPRES"}, {"description": "Customer", "fieldKind": "Attribute", "hryCol": "VEN_KUNNR", "isMainEt": "", "refEt": "", "rollname": "KUNNR"}, {"description": "Plant level relevant", "fieldKind": "Attribute", "hryCol": "WERKR", "isMainEt": "", "refEt": "", "rollname": "WERKR"}, {"description": "Plant", "fieldKind": "Attribute", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_EXT"}, {"description": "Payee in Document", "fieldKind": "Attribute", "hryCol": "XZEMP", "isMainEt": "", "refEt": "", "rollname": "XZEMP"}, {"description": "<PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "ZTEST", "isMainEt": "", "refEt": "", "rollname": "LIFNR"}, {"description": "Trade Expiration Date", "fieldKind": "Attribute", "hryCol": "ZZEXPDATE", "isMainEt": "", "refEt": "", "rollname": "ZZ_EXPD"}, {"description": "Trading Number", "fieldKind": "Attribute", "hryCol": "ZZLICENS", "isMainEt": "", "refEt": "", "rollname": "ZZ_LICENSE"}, {"description": "Name 1", "fieldKind": "Attribute", "hryCol": "ZZVENNOTE", "isMainEt": "", "refEt": "", "rollname": "NAME1"}, {"description": "Texts (Supplier General Data)", "fieldKind": "Attribute", "hryCol": "VENGENTXT", "isMainEt": "", "refEt": "", "rollname": "NAME1"}, {"description": "Supplier: Characteristic Valuation (Classification)", "fieldKind": "Attribute", "hryCol": "BP_VENVAL", "isMainEt": "", "refEt": "", "rollname": "NAME1"}, {"description": "Supplier Subrange", "fieldKind": "Attribute", "hryCol": "BP_VENSUB", "isMainEt": "", "refEt": "", "rollname": "NAME1"}, {"description": "Basic Data for Document Link", "fieldKind": "Attribute", "hryCol": "BP_VENDDB", "isMainEt": "", "refEt": "", "rollname": "NAME1"}, {"description": "Supplier: Class Assignment (Classification)", "fieldKind": "Attribute", "hryCol": "BP_VENCLA", "isMainEt": "", "refEt": "", "rollname": "NAME1"}, {"description": "Tax Groupings (Suppliers)", "fieldKind": "Attribute", "hryCol": "BP_TAXGRP", "isMainEt": "", "refEt": "", "rollname": "NAME1"}, {"description": "Purchasing Organization", "fieldKind": "Attribute", "hryCol": "BP_PORG", "isMainEt": "", "refEt": "", "rollname": "NAME1"}, {"description": "Company Code", "fieldKind": "Attribute", "hryCol": "BP_COMPNY", "isMainEt": "", "refEt": "", "rollname": "NAME1"}], "isFolder": "X", "relation": "BP_MLT_AS"}, {"name": "VENGENTXT", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "VENLANGU", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Text-ID", "fieldKind": "Qualifying Entity Type", "hryCol": "VEN_TDID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_TDID"}, {"description": "Longtext", "fieldKind": "Attribute", "hryCol": "VEN_LTEXT", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_STRING"}], "isFolder": "X", "relation": "BP_VENGEN"}, {"name": "BP_VENVAL", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Characteristic ID", "fieldKind": "Qualifying Entity Type", "hryCol": "CHARID", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_CLF_CHARID"}, {"description": "Class Type", "fieldKind": "Qualifying Entity Type", "hryCol": "CLASSTYPE", "isMainEt": "", "refEt": "", "rollname": "KLASSENART"}, {"description": "Int. counter", "fieldKind": "Qualifying Entity Type", "hryCol": "ECOCNTR", "isMainEt": "", "refEt": "", "rollname": "ADZHL"}, {"description": "Counter", "fieldKind": "Qualifying Entity Type", "hryCol": "VALCNT", "isMainEt": "", "refEt": "", "rollname": "WZAEHL"}, {"description": "Author", "fieldKind": "Attribute", "hryCol": "VEN_ATAUT", "isMainEt": "", "refEt": "", "rollname": "ATAUT"}, {"description": "Alternative unit", "fieldKind": "Attribute", "hryCol": "VEN_ATAW1", "isMainEt": "", "refEt": "", "rollname": "ATAWE"}, {"description": "Alternative unit", "fieldKind": "Attribute", "hryCol": "VEN_ATAWE", "isMainEt": "", "refEt": "", "rollname": "ATAWE"}, {"description": "Code", "fieldKind": "Attribute", "hryCol": "VEN_ATCOD", "isMainEt": "", "refEt": "", "rollname": "ATCOD"}, {"description": "Value to", "fieldKind": "Attribute", "hryCol": "VEN_ATFLB", "isMainEt": "", "refEt": "", "rollname": "ATFLB"}, {"description": "Value from", "fieldKind": "Attribute", "hryCol": "VEN_ATFLV", "isMainEt": "", "refEt": "", "rollname": "ATFLV"}, {"description": "Field length 10", "fieldKind": "Attribute", "hryCol": "VEN_ATIMB", "isMainEt": "", "refEt": "", "rollname": "NUMC10"}, {"description": "Position", "fieldKind": "Attribute", "hryCol": "VEN_ATSRT", "isMainEt": "", "refEt": "", "rollname": "ATSRT"}, {"description": "Characteristic Value", "fieldKind": "Attribute", "hryCol": "VEN_ATWRT", "isMainEt": "", "refEt": "", "rollname": "ATWRT"}, {"description": "Instance counter", "fieldKind": "Attribute", "hryCol": "VEN_ATZIS", "isMainEt": "", "refEt": "", "rollname": "ATZIS"}, {"description": "Deletion Indicator", "fieldKind": "Attribute", "hryCol": "VEN_VADEL", "isMainEt": "", "refEt": "", "rollname": "LKENZ"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "VEN_VADVF", "isMainEt": "", "refEt": "", "rollname": "DATUV"}, {"description": "Comp. type", "fieldKind": "Attribute", "hryCol": "VEN_VGLAR", "isMainEt": "", "refEt": "", "rollname": "ATVGLART"}], "isFolder": "X", "relation": "BP_VENGEN"}, {"name": "BP_VENSUB", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "<PERSON><PERSON><PERSON>", "fieldKind": "Qualifying Entity Type", "hryCol": "LTSNR", "isMainEt": "", "refEt": "", "rollname": "LTSNR_MDG"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "VEN_LANGU", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "SSR Description", "fieldKind": "Attribute", "hryCol": "LTSBZ", "isMainEt": "", "refEt": "", "rollname": "BEZEILTS"}], "isFolder": "X", "relation": "BP_VENGEN"}, {"name": "BP_VENDDB", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Document Type", "fieldKind": "Qualifying Entity Type", "hryCol": "VEN_DOKAR", "isMainEt": "", "refEt": "", "rollname": "DOKAR"}, {"description": "Document", "fieldKind": "Qualifying Entity Type", "hryCol": "VEN_DOKNR", "isMainEt": "", "refEt": "", "rollname": "DOKNR"}, {"description": "Document part", "fieldKind": "Qualifying Entity Type", "hryCol": "VEN_DOKTL", "isMainEt": "", "refEt": "", "rollname": "DOKTL_D"}, {"description": "Document version", "fieldKind": "Qualifying Entity Type", "hryCol": "VEN_DOKVR", "isMainEt": "", "refEt": "", "rollname": "DOKVR"}, {"description": "CAD indicator", "fieldKind": "Attribute", "hryCol": "VEN_CAD_P", "isMainEt": "", "refEt": "", "rollname": "CAD_POS"}, {"description": "Newest version", "fieldKind": "Attribute", "hryCol": "VEN_NWSTV", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_BP_NEWESTVER"}], "isFolder": "X", "relation": "BP_VENGEN"}, {"name": "BP_VENCLA", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Class", "fieldKind": "Qualifying Entity Type", "hryCol": "CLASS", "isMainEt": "", "refEt": "", "rollname": "KLASSE_D"}, {"description": "Class Type", "fieldKind": "Qualifying Entity Type", "hryCol": "CLASSTYPE", "isMainEt": "", "refEt": "", "rollname": "KLASSENART"}, {"description": "Int. counter", "fieldKind": "Qualifying Entity Type", "hryCol": "ECOCNTR", "isMainEt": "", "refEt": "", "rollname": "ADZHL"}, {"description": "Deletion Indicator", "fieldKind": "Attribute", "hryCol": "VEN_CLDEL", "isMainEt": "", "refEt": "", "rollname": "LKENZ"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "VEN_CLDVF", "isMainEt": "", "refEt": "", "rollname": "DATUV"}, {"description": "Internal class no.", "fieldKind": "Attribute", "hryCol": "VEN_CLINT", "isMainEt": "", "refEt": "", "rollname": "CLINT"}, {"description": "Status", "fieldKind": "Attribute", "hryCol": "VEN_CLSTA", "isMainEt": "", "refEt": "", "rollname": "CLSTATUS"}], "isFolder": "X", "relation": "BP_VENGEN"}, {"name": "BP_TAXGRP", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Account type", "fieldKind": "Qualifying Entity Type", "hryCol": "KOART", "isMainEt": "", "refEt": "", "rollname": "KOART"}, {"description": "Category Indicator for Tax", "fieldKind": "Qualifying Entity Type", "hryCol": "TAXGR", "isMainEt": "", "refEt": "", "rollname": "TAXGR"}, {"description": "Coll.auth. until", "fieldKind": "Attribute", "hryCol": "AFTDT", "isMainEt": "", "refEt": "", "rollname": "J_1AAGENTT"}, {"description": "Coll.auth.from", "fieldKind": "Attribute", "hryCol": "AGTDF", "isMainEt": "", "refEt": "", "rollname": "J_1AAGENTF"}], "isFolder": "X", "relation": "BP_VENGEN"}, {"name": "BP_PORG", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Purch. organization", "fieldKind": "Qualifying Entity Type", "hryCol": "PRCH_ORG", "isMainEt": "", "refEt": "", "rollname": "EKORG"}, {"description": "Settlement Mgmt.", "fieldKind": "Attribute", "hryCol": "AGREL", "isMainEt": "", "refEt": "", "rollname": "AGREL"}, {"description": "Doc. Index Active", "fieldKind": "Attribute", "hryCol": "BLIND", "isMainEt": "", "refEt": "", "rollname": "BLIND"}, {"description": "Subseq. sett. index", "fieldKind": "Attribute", "hryCol": "BOIND", "isMainEt": "", "refEt": "", "rollname": "BOIND"}, {"description": "Subseq. settlement", "fieldKind": "Attribute", "hryCol": "BOLRE", "isMainEt": "", "refEt": "", "rollname": "BOLRE"}, {"description": "LB restriction profile", "fieldKind": "Attribute", "hryCol": "BOPNR", "isMainEt": "", "refEt": "", "rollname": "BOPNR"}, {"description": "Confirmation Control", "fieldKind": "Attribute", "hryCol": "BSTAE", "isMainEt": "", "refEt": "", "rollname": "BSTAE"}, {"description": "Acc. with supplier", "fieldKind": "Attribute", "hryCol": "EIKTO_M", "isMainEt": "", "refEt": "", "rollname": "EIKTO_M"}, {"description": "Purchasing Group", "fieldKind": "Attribute", "hryCol": "EKGRP", "isMainEt": "", "refEt": "", "rollname": "EKGRP"}, {"description": "Incoterms", "fieldKind": "Attribute", "hryCol": "INCO1", "isMainEt": "", "refEt": "", "rollname": "INCO1"}, {"description": "Incoterms (Part 2)", "fieldKind": "Attribute", "hryCol": "INCO2", "isMainEt": "", "refEt": "", "rollname": "INCO2"}, {"description": "<PERSON><PERSON><PERSON>, Supplier", "fieldKind": "Attribute", "hryCol": "KALSK", "isMainEt": "", "refEt": "", "rollname": "KALSK"}, {"description": "Acknowledgment Reqd", "fieldKind": "Attribute", "hryCol": "KZABS", "isMainEt": "", "refEt": "", "rollname": "KZABS"}, {"description": "Automatic PO", "fieldKind": "Attribute", "hryCol": "KZAUT", "isMainEt": "", "refEt": "", "rollname": "KZAUT"}, {"description": "Service-Based Invoice Verification", "fieldKind": "Attribute", "hryCol": "LEBRE", "isMainEt": "", "refEt": "", "rollname": "LEBRE"}, {"description": "ABC indicator", "fieldKind": "Attribute", "hryCol": "LFABC", "isMainEt": "", "refEt": "", "rollname": "LFABC"}, {"description": "Order entry: supplier", "fieldKind": "Attribute", "hryCol": "LIBES", "isMainEt": "", "refEt": "", "rollname": "LIBES"}, {"description": "Supplier price marking", "fieldKind": "Attribute", "hryCol": "LIPRE", "isMainEt": "", "refEt": "", "rollname": "LIPRE"}, {"description": "Rack jobbing", "fieldKind": "Attribute", "hryCol": "LISER", "isMainEt": "", "refEt": "", "rollname": "LISER"}, {"description": "Delete flag for purchasing organization", "fieldKind": "Attribute", "hryCol": "LOEVM_M", "isMainEt": "", "refEt": "", "rollname": "LOEVM_M"}, {"description": "Pricing Date Control", "fieldKind": "Attribute", "hryCol": "MEPRF", "isMainEt": "", "refEt": "", "rollname": "MEPRF"}, {"description": "Minimum order value", "fieldKind": "Attribute", "hryCol": "MINBW", "isMainEt": "", "refEt": "", "rollname": "MINBW"}, {"description": "Qualifying for DKd", "fieldKind": "Attribute", "hryCol": "NRGEW", "isMainEt": "", "refEt": "", "rollname": "NRGEW_MDG"}, {"description": "PROACT control prof.", "fieldKind": "Attribute", "hryCol": "PAPRF", "isMainEt": "", "refEt": "", "rollname": "WVMI_PAPRF"}, {"description": "Planned Deliv. Time", "fieldKind": "Attribute", "hryCol": "PLIFZ", "isMainEt": "", "refEt": "", "rollname": "PLIFZ"}, {"description": "Price determination", "fieldKind": "Attribute", "hryCol": "PRFRE", "isMainEt": "", "refEt": "", "rollname": "PRFRE_LH"}, {"description": "Rounding Profile", "fieldKind": "Attribute", "hryCol": "REF_BPOR1", "isMainEt": "", "refEt": "RDPRF", "rollname": "RDPRF"}, {"description": "Level", "fieldKind": "Attribute", "hryCol": "REF_BPOR2", "isMainEt": "", "refEt": "RDZAE", "rollname": "RDZAE"}, {"description": "Country Key", "fieldKind": "Attribute", "hryCol": "REF_BPORG", "isMainEt": "", "refEt": "LAND1", "rollname": "LAND1"}, {"description": "Plant", "fieldKind": "Attribute", "hryCol": "REF_BPPOG", "isMainEt": "", "refEt": "WERKS_D", "rollname": "WERKS_D"}, {"description": "Mode of Transport", "fieldKind": "Attribute", "hryCol": "REF_BPPOR", "isMainEt": "", "refEt": "EXPVZ", "rollname": "EXPVZ"}, {"description": "Planning Calendar", "fieldKind": "Attribute", "hryCol": "REF_BPPRG", "isMainEt": "", "refEt": "MRPPP", "rollname": "MRPPP_W"}, {"description": "Customs office", "fieldKind": "Attribute", "hryCol": "REF_BPRG", "isMainEt": "", "refEt": "DZOLLA", "rollname": "DZOLLS"}, {"description": "Unit of Measure Grp", "fieldKind": "Attribute", "hryCol": "RF_BPORG", "isMainEt": "", "refEt": "MEGRU", "rollname": "MEGRU"}, {"description": "Planning cycle", "fieldKind": "Attribute", "hryCol": "RF_BPPORG", "isMainEt": "", "refEt": "LFRHY", "rollname": "LFRHY"}, {"description": "Alternative Unit of Measure", "fieldKind": "Attribute", "hryCol": "RF_BPPRG", "isMainEt": "", "refEt": "MEINS", "rollname": "LRMEI"}, {"description": "Sort criterion", "fieldKind": "Attribute", "hryCol": "SKRIT", "isMainEt": "", "refEt": "", "rollname": "SKRIT"}, {"description": "Purch. block for purchasing organization", "fieldKind": "Attribute", "hryCol": "SPERM_P", "isMainEt": "", "refEt": "", "rollname": "SPERM_M"}, {"description": "B.vol.comp./ag.nec.", "fieldKind": "Attribute", "hryCol": "UMSAE", "isMainEt": "", "refEt": "", "rollname": "UMSAE"}, {"description": "Supplier Service Level", "fieldKind": "Attribute", "hryCol": "VENSL", "isMainEt": "", "refEt": "", "rollname": "VENSL"}, {"description": "Salesperson", "fieldKind": "Attribute", "hryCol": "VERKF", "isMainEt": "", "refEt": "", "rollname": "EVERK"}, {"description": "Shipping Conditions", "fieldKind": "Attribute", "hryCol": "VSBED", "isMainEt": "", "refEt": "", "rollname": "VSBED"}, {"description": "Order currency", "fieldKind": "Attribute", "hryCol": "WAERS", "isMainEt": "", "refEt": "", "rollname": "BSTWA"}, {"description": "GR-Based Inv. Verif.", "fieldKind": "Attribute", "hryCol": "WEBRE", "isMainEt": "", "refEt": "", "rollname": "WEBRE"}, {"description": "Aut. ev. GRSetmt.Ret", "fieldKind": "Attribute", "hryCol": "XERSR", "isMainEt": "", "refEt": "", "rollname": "XERSR"}, {"description": "Eval. receipt sett.", "fieldKind": "Attribute", "hryCol": "XERSY", "isMainEt": "", "refEt": "", "rollname": "XERSY"}, {"description": "Revaluation", "fieldKind": "Attribute", "hryCol": "XNBWY", "isMainEt": "", "refEt": "", "rollname": "XNBWY"}, {"description": "Payment terms", "fieldKind": "Attribute", "hryCol": "ZTERM_M", "isMainEt": "", "refEt": "", "rollname": "DZTERM"}, {"description": "Texts (Supplier Purchasing Organization)", "fieldKind": "Attribute", "hryCol": "VENPOTXT", "isMainEt": "", "refEt": "", "rollname": "DZTERM"}, {"description": "Supplier: Partner Functions", "fieldKind": "Attribute", "hryCol": "BP_VENFCN", "isMainEt": "", "refEt": "", "rollname": "DZTERM"}, {"description": "Purchasing Data", "fieldKind": "Attribute", "hryCol": "BP_PORG2", "isMainEt": "", "refEt": "", "rollname": "DZTERM"}], "isFolder": "X", "relation": "BP_VENGEN"}, {"name": "VENPOTXT", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Purch. organization", "fieldKind": "Leading Entity Type", "hryCol": "PRCH_ORG", "isMainEt": "", "refEt": "", "rollname": "EKORG"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "VENLANGU", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Text-ID", "fieldKind": "Qualifying Entity Type", "hryCol": "VEN_TDID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_TDID"}, {"description": "Longtext", "fieldKind": "Attribute", "hryCol": "VPO_LTEXT", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_STRING"}], "isFolder": "X", "relation": "BP_PORG"}, {"name": "BP_VENFCN", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Purch. organization", "fieldKind": "Leading Entity Type", "hryCol": "PRCH_ORG", "isMainEt": "", "refEt": "", "rollname": "EKORG"}, {"description": "<PERSON><PERSON><PERSON>", "fieldKind": "Qualifying Entity Type", "hryCol": "LTSNR", "isMainEt": "", "refEt": "", "rollname": "LTSNR_MDG"}, {"description": "Partner Function", "fieldKind": "Qualifying Entity Type", "hryCol": "PARVW", "isMainEt": "", "refEt": "", "rollname": "PARVW"}, {"description": "Partner counter", "fieldKind": "Qualifying Entity Type", "hryCol": "PARZA", "isMainEt": "", "refEt": "", "rollname": "PARZA"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS_D", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Default Partner", "fieldKind": "Attribute", "hryCol": "DEFPA", "isMainEt": "", "refEt": "", "rollname": "DEFPA"}, {"description": "Ref. to other vendor", "fieldKind": "Attribute", "hryCol": "LIFN2", "isMainEt": "", "refEt": "", "rollname": "LIFN2"}, {"description": "Contact Person", "fieldKind": "Attribute", "hryCol": "PARNR", "isMainEt": "", "refEt": "", "rollname": "PARNR"}, {"description": "Reflexive Partner Functions", "fieldKind": "Attribute", "hryCol": "REFLEXIVE", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_PFNC_REFLEXIVE"}, {"description": "Assignment ID", "fieldKind": "Attribute", "hryCol": "VPF_ASGID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "VPF_GUID", "fieldKind": "Attribute", "hryCol": "VPF_GUID", "isMainEt": "", "refEt": "", "rollname": "CHAR32"}, {"description": "Personnel Number", "fieldKind": "Attribute", "hryCol": "VPF_PERNR", "isMainEt": "", "refEt": "", "rollname": "PERNR_D"}], "isFolder": "X", "relation": "BP_PORG"}, {"name": "BP_PORG2", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Purch. organization", "fieldKind": "Leading Entity Type", "hryCol": "PRCH_ORG", "isMainEt": "", "refEt": "", "rollname": "EKORG"}, {"description": "<PERSON><PERSON><PERSON>", "fieldKind": "Qualifying Entity Type", "hryCol": "LTSNR", "isMainEt": "", "refEt": "", "rollname": "LTSNR_MDG"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS_D", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Subseq. Settlement", "fieldKind": "Attribute", "hryCol": "BOLRE_LM2", "isMainEt": "", "refEt": "", "rollname": "BOLRE_UNUSED"}, {"description": "LB restriction profile", "fieldKind": "Attribute", "hryCol": "BOPNR_LM2", "isMainEt": "", "refEt": "", "rollname": "BOPNR"}, {"description": "Confirmation Control", "fieldKind": "Attribute", "hryCol": "BSTAE_LM2", "isMainEt": "", "refEt": "", "rollname": "BSTAE"}, {"description": "MRP Controller", "fieldKind": "Attribute", "hryCol": "DISPO_LM2", "isMainEt": "", "refEt": "DISPO", "rollname": "DISPO"}, {"description": "Purchasing Group", "fieldKind": "Attribute", "hryCol": "EKGRP_LM2", "isMainEt": "", "refEt": "", "rollname": "EKGRP"}, {"description": "Mode of Transport", "fieldKind": "Attribute", "hryCol": "EXPVZ_LM2", "isMainEt": "", "refEt": "EXPVZ", "rollname": "EXPVZ"}, {"description": "Incoterms", "fieldKind": "Attribute", "hryCol": "INCO1_LM2", "isMainEt": "", "refEt": "", "rollname": "INCO1"}, {"description": "Incoterms (Part 2)", "fieldKind": "Attribute", "hryCol": "INCO2_LM2", "isMainEt": "", "refEt": "", "rollname": "INCO2"}, {"description": "<PERSON><PERSON><PERSON>, Supplier", "fieldKind": "Attribute", "hryCol": "KALSK_LM2", "isMainEt": "", "refEt": "", "rollname": "KALSK"}, {"description": "Acknowledgment Reqd", "fieldKind": "Attribute", "hryCol": "KZABS_LM2", "isMainEt": "", "refEt": "", "rollname": "KZABS"}, {"description": "Automatic PO", "fieldKind": "Attribute", "hryCol": "KZAUT_LM2", "isMainEt": "", "refEt": "", "rollname": "KZAUT"}, {"description": "Country Key", "fieldKind": "Attribute", "hryCol": "LAND_LM2", "isMainEt": "", "refEt": "LAND1", "rollname": "LAND1"}, {"description": "Service-Based Invoice Verification", "fieldKind": "Attribute", "hryCol": "LEBRE_LM2", "isMainEt": "", "refEt": "", "rollname": "LEBRE"}, {"description": "ABC indicator", "fieldKind": "Attribute", "hryCol": "LFABC_LM2", "isMainEt": "", "refEt": "", "rollname": "LFABC"}, {"description": "Planning cycle", "fieldKind": "Attribute", "hryCol": "LFRHY_LM2", "isMainEt": "", "refEt": "LFRHY", "rollname": "LFRHY"}, {"description": "Order entry: supplier", "fieldKind": "Attribute", "hryCol": "LIBES_LM2", "isMainEt": "", "refEt": "", "rollname": "LIBES"}, {"description": "Supplier price marking", "fieldKind": "Attribute", "hryCol": "LIPRE_LM2", "isMainEt": "", "refEt": "", "rollname": "LIPRE"}, {"description": "Rack jobbing", "fieldKind": "Attribute", "hryCol": "LISER_LM2", "isMainEt": "", "refEt": "", "rollname": "LISER"}, {"description": "Delete flag for purchasing organization", "fieldKind": "Attribute", "hryCol": "LOEVM_LM2", "isMainEt": "", "refEt": "", "rollname": "LOEVM_M"}, {"description": "Unit of Measure Grp", "fieldKind": "Attribute", "hryCol": "MEGRU_LM2", "isMainEt": "", "refEt": "MEGRU", "rollname": "MEGRU"}, {"description": "Alternative Unit of Measure", "fieldKind": "Attribute", "hryCol": "MEINS_LM2", "isMainEt": "", "refEt": "MEINS", "rollname": "LRMEI"}, {"description": "Pricing Date Control", "fieldKind": "Attribute", "hryCol": "MEPRF_LM2", "isMainEt": "", "refEt": "", "rollname": "MEPRF"}, {"description": "Minimum order value", "fieldKind": "Attribute", "hryCol": "MINBW_LM2", "isMainEt": "", "refEt": "", "rollname": "MINBW"}, {"description": "Planning Calendar", "fieldKind": "Attribute", "hryCol": "MRPPP_LM2", "isMainEt": "", "refEt": "MRPPP", "rollname": "MRPPP_W"}, {"description": "PROACT control prof.", "fieldKind": "Attribute", "hryCol": "PAPRF_LM2", "isMainEt": "", "refEt": "", "rollname": "WVMI_PAPRF"}, {"description": "Planned Deliv. Time", "fieldKind": "Attribute", "hryCol": "PLIFZ_LM2", "isMainEt": "", "refEt": "", "rollname": "PLIFZ"}, {"description": "Rounding Profile", "fieldKind": "Attribute", "hryCol": "RDPRF_LM2", "isMainEt": "", "refEt": "RDPRF", "rollname": "RDPRF"}, {"description": "Level", "fieldKind": "Attribute", "hryCol": "RDZAE_LM2", "isMainEt": "", "refEt": "RDZAE", "rollname": "RDZAE"}, {"description": "Purch. block for purchasing organization", "fieldKind": "Attribute", "hryCol": "SPERM_LM2", "isMainEt": "", "refEt": "", "rollname": "SPERM_M"}, {"description": "Bus. Vol. Comp. Nec.", "fieldKind": "Attribute", "hryCol": "UMSAE_LM2", "isMainEt": "", "refEt": "", "rollname": "UMSAE_UNUSED"}, {"description": "Salesperson", "fieldKind": "Attribute", "hryCol": "VERKF_LM2", "isMainEt": "", "refEt": "", "rollname": "EVERK"}, {"description": "Order currency", "fieldKind": "Attribute", "hryCol": "WAERS_LM2", "isMainEt": "", "refEt": "", "rollname": "BSTWA"}, {"description": "GR-Based Inv. Verif.", "fieldKind": "Attribute", "hryCol": "WEBRE_LM2", "isMainEt": "", "refEt": "", "rollname": "WEBRE"}, {"description": "Aut. ev. GRSetmt.Ret", "fieldKind": "Attribute", "hryCol": "XERSR_LM2", "isMainEt": "", "refEt": "", "rollname": "XERSR"}, {"description": "Eval. receipt sett.", "fieldKind": "Attribute", "hryCol": "XERSY_LM2", "isMainEt": "", "refEt": "", "rollname": "XERSY"}, {"description": "Revaluation", "fieldKind": "Attribute", "hryCol": "XNBWY_LM2", "isMainEt": "", "refEt": "", "rollname": "XNBWY"}, {"description": "Customs office", "fieldKind": "Attribute", "hryCol": "ZOLLA", "isMainEt": "", "refEt": "DZOLLA", "rollname": "DZOLLS"}, {"description": "Payment terms", "fieldKind": "Attribute", "hryCol": "ZTERM_LM2", "isMainEt": "", "refEt": "", "rollname": "DZTERM"}], "isFolder": "X", "relation": "BP_PORG"}, {"name": "BP_COMPNY", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Company Code", "fieldKind": "Qualifying Entity Type", "hryCol": "COMPANY", "isMainEt": "", "refEt": "", "rollname": "BUKRS"}, {"description": "Previous Account No.", "fieldKind": "Attribute", "hryCol": "ALTKN", "isMainEt": "", "refEt": "", "rollname": "ALTKN"}, {"description": "Authorization Group", "fieldKind": "Attribute", "hryCol": "BEGRU_B", "isMainEt": "", "refEt": "BEGRU", "rollname": "BRGRU"}, {"description": "Subsidy Indicator", "fieldKind": "Attribute", "hryCol": "BLNKZ", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_BLNKZ"}, {"description": "Authorization object", "fieldKind": "Attribute", "hryCol": "BRGRU_SCC", "isMainEt": "", "refEt": "BROBJ", "rollname": "BROBJ"}, {"description": "Certification Date", "fieldKind": "Attribute", "hryCol": "CERDT", "isMainEt": "", "refEt": "", "rollname": "CERDT"}, {"description": "Date of Last Interest Calc.", "fieldKind": "Attribute", "hryCol": "DATLZ", "isMainEt": "", "refEt": "", "rollname": "DATLZ"}, {"description": "Account with vendor", "fieldKind": "Attribute", "hryCol": "EIKTO", "isMainEt": "", "refEt": "", "rollname": "EIKTO_K"}, {"description": "Planning Group", "fieldKind": "Attribute", "hryCol": "FDGRV", "isMainEt": "", "refEt": "", "rollname": "FDGRV"}, {"description": "Release Group", "fieldKind": "Attribute", "hryCol": "FRGRP", "isMainEt": "", "refEt": "", "rollname": "FRGRP"}, {"description": "Credit Memo Pyt Term", "fieldKind": "Attribute", "hryCol": "GUZTE", "isMainEt": "", "refEt": "", "rollname": "GUZTE"}, {"description": "<PERSON><PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "HWAERS", "isMainEt": "", "refEt": "", "rollname": "WAERS"}, {"description": "Clrk's internet add.", "fieldKind": "Attribute", "hryCol": "INTAD", "isMainEt": "", "refEt": "", "rollname": "INTAD"}, {"description": "Check Cashing Time", "fieldKind": "Attribute", "hryCol": "KULTG", "isMainEt": "", "refEt": "", "rollname": "KULTG"}, {"description": "Account <PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "KVERM", "isMainEt": "", "refEt": "", "rollname": "KVERM"}, {"description": "Alternative payee", "fieldKind": "Attribute", "hryCol": "LNRZB", "isMainEt": "", "refEt": "", "rollname": "LNRZB"}, {"description": "Head office", "fieldKind": "Attribute", "hryCol": "LNRZE", "isMainEt": "", "refEt": "", "rollname": "LNRZE"}, {"description": "Deletion flag for company code", "fieldKind": "Attribute", "hryCol": "LOEVM_B", "isMainEt": "", "refEt": "", "rollname": "LOEVM_B"}, {"description": "Grouping Key", "fieldKind": "Attribute", "hryCol": "MGRUP", "isMainEt": "", "refEt": "", "rollname": "MGRUP"}, {"description": "Minority Indicator", "fieldKind": "Attribute", "hryCol": "MINDK", "isMainEt": "", "refEt": "", "rollname": "MINDK"}, {"description": "CoCd deletion block", "fieldKind": "Attribute", "hryCol": "NODEL", "isMainEt": "", "refEt": "", "rollname": "NODEL_B"}, {"description": "Personnel Number", "fieldKind": "Attribute", "hryCol": "PERNR", "isMainEt": "", "refEt": "", "rollname": "PERNR_D"}, {"description": "Exemption Authority", "fieldKind": "Attribute", "hryCol": "QSBGR", "isMainEt": "", "refEt": "", "rollname": "QSBGR"}, {"description": "<PERSON>id <PERSON>", "fieldKind": "Attribute", "hryCol": "QSZDT", "isMainEt": "", "refEt": "", "rollname": "QSZDT"}, {"description": "Exemption Number", "fieldKind": "Attribute", "hryCol": "QSZNR", "isMainEt": "", "refEt": "", "rollname": "QSZNR"}, {"description": "Reconciliation acct", "fieldKind": "Attribute", "hryCol": "REF_BPCMP", "isMainEt": "", "refEt": "AKONT", "rollname": "AKONT"}, {"description": "Chart of Accounts", "fieldKind": "Attribute", "hryCol": "REF_BPCOM", "isMainEt": "", "refEt": "KTOPL", "rollname": "KTOPL"}, {"description": "Clerk Abbreviation", "fieldKind": "Attribute", "hryCol": "REF_BPCOY", "isMainEt": "", "refEt": "BUSAB", "rollname": "BUSAB"}, {"description": "Tolerance Group", "fieldKind": "Attribute", "hryCol": "REF_BPCPY", "isMainEt": "", "refEt": "TOGRU", "rollname": "TOGRU"}, {"description": "Check Double Invoice", "fieldKind": "Attribute", "hryCol": "REPRF", "isMainEt": "", "refEt": "", "rollname": "REPRF"}, {"description": "Withholding Tax Code", "fieldKind": "Attribute", "hryCol": "RF_BPCMPN", "isMainEt": "", "refEt": "QSSKZ", "rollname": "QSSKZ"}, {"description": "Country Key", "fieldKind": "Attribute", "hryCol": "RF_BPCMPY", "isMainEt": "", "refEt": "LAND1", "rollname": "LAND1"}, {"description": "House bank", "fieldKind": "Attribute", "hryCol": "RF_BPCOMP", "isMainEt": "", "refEt": "HBKID", "rollname": "HBKID"}, {"description": "WTax Country", "fieldKind": "Attribute", "hryCol": "RF_CMPANY", "isMainEt": "", "refEt": "QLAND", "rollname": "QLAND"}, {"description": "Tolerance Group", "fieldKind": "Attribute", "hryCol": "RF_COMANY", "isMainEt": "", "refEt": "TOGRR", "rollname": "TOGRR"}, {"description": "Recipient Type", "fieldKind": "Attribute", "hryCol": "RF_COPANY", "isMainEt": "", "refEt": "QSREC", "rollname": "QSREC"}, {"description": "Posting block for company code", "fieldKind": "Attribute", "hryCol": "SPERR_B", "isMainEt": "", "refEt": "", "rollname": "SPERB_B"}, {"description": "Acct.clerks tel.no.", "fieldKind": "Attribute", "hryCol": "TLFNS", "isMainEt": "", "refEt": "", "rollname": "TLFNS"}, {"description": "Acctg clerk's fax", "fieldKind": "Attribute", "hryCol": "TLFXS", "isMainEt": "", "refEt": "", "rollname": "TLFXS"}, {"description": "Pmt meth. supplement", "fieldKind": "Attribute", "hryCol": "UZAWE", "isMainEt": "", "refEt": "", "rollname": "UZAWE"}, {"description": "Interest indicator", "fieldKind": "Attribute", "hryCol": "VZSKZ", "isMainEt": "", "refEt": "", "rollname": "VZSKZ"}, {"description": "Bill/Ex. Limit", "fieldKind": "Attribute", "hryCol": "WEBTR", "isMainEt": "", "refEt": "", "rollname": "WEBTR"}, {"description": "Account Statement", "fieldKind": "Attribute", "hryCol": "XAUSZ", "isMainEt": "", "refEt": "", "rollname": "XAUSZ"}, {"description": "Local Processing", "fieldKind": "Attribute", "hryCol": "XDEZV", "isMainEt": "", "refEt": "", "rollname": "XDEZV"}, {"description": "Pmnt advice by EDI", "fieldKind": "Attribute", "hryCol": "XEDIP", "isMainEt": "", "refEt": "", "rollname": "XEDIP"}, {"description": "Individual Payment", "fieldKind": "Attribute", "hryCol": "XPORE", "isMainEt": "", "refEt": "", "rollname": "XPORE"}, {"description": "Clearing with cust.", "fieldKind": "Attribute", "hryCol": "XVERR", "isMainEt": "", "refEt": "", "rollname": "XVERR_LFB1"}, {"description": "Payment Block", "fieldKind": "Attribute", "hryCol": "ZAHLS", "isMainEt": "", "refEt": "", "rollname": "DZAHLS"}, {"description": "Grouping key", "fieldKind": "Attribute", "hryCol": "ZGRUP", "isMainEt": "", "refEt": "", "rollname": "DZGRUP"}, {"description": "Key Date of Last Int. Calc.", "fieldKind": "Attribute", "hryCol": "ZINDT", "isMainEt": "", "refEt": "", "rollname": "DZINDT"}, {"description": "Interest Calc. Frequency", "fieldKind": "Attribute", "hryCol": "ZINRT", "isMainEt": "", "refEt": "", "rollname": "DZINRT"}, {"description": "Clerk at vendor", "fieldKind": "Attribute", "hryCol": "ZSABE", "isMainEt": "", "refEt": "", "rollname": "DZSABE_K"}, {"description": "Payment terms", "fieldKind": "Attribute", "hryCol": "ZTERM", "isMainEt": "", "refEt": "", "rollname": "DZTERM"}, {"description": "Sort key", "fieldKind": "Attribute", "hryCol": "ZUAWA", "isMainEt": "", "refEt": "", "rollname": "DZUAWA"}, {"description": "Payment Methods", "fieldKind": "Attribute", "hryCol": "ZWELS", "isMainEt": "", "refEt": "", "rollname": "DZWELS"}, {"description": "Texts (Supplier Company Code Data)", "fieldKind": "Attribute", "hryCol": "VENCCTXT", "isMainEt": "", "refEt": "", "rollname": "DZWELS"}, {"description": "Extended Withholding Tax", "fieldKind": "Attribute", "hryCol": "BP_WHTAX", "isMainEt": "", "refEt": "", "rollname": "DZWELS"}, {"description": "Dunning Data", "fieldKind": "Attribute", "hryCol": "BP_DUNN", "isMainEt": "", "refEt": "", "rollname": "DZWELS"}], "isFolder": "X", "relation": "BP_VENGEN"}, {"name": "VENCCTXT", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Company Code", "fieldKind": "Leading Entity Type", "hryCol": "COMPANY", "isMainEt": "", "refEt": "", "rollname": "BUKRS"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "VENLANGU", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Text-ID", "fieldKind": "Qualifying Entity Type", "hryCol": "VEN_TDID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_TDID"}, {"description": "Longtext", "fieldKind": "Attribute", "hryCol": "VCC_LTEXT", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_STRING"}], "isFolder": "X", "relation": "BP_COMPNY"}, {"name": "BP_WHTAX", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Company Code", "fieldKind": "Leading Entity Type", "hryCol": "COMPANY", "isMainEt": "", "refEt": "", "rollname": "BUKRS"}, {"description": "Country Key", "fieldKind": "Qualifying Entity Type", "hryCol": "LAND_1", "isMainEt": "", "refEt": "", "rollname": "LAND1"}, {"description": "Withholding Tax Type", "fieldKind": "Qualifying Entity Type", "hryCol": "WITHT", "isMainEt": "", "refEt": "", "rollname": "WITHT"}, {"description": "Recipient Type", "fieldKind": "Attribute", "hryCol": "QSREC_WT", "isMainEt": "", "refEt": "WT_QSREC", "rollname": "WT_QSREC"}, {"description": "Exemption Start Date", "fieldKind": "Attribute", "hryCol": "WT_EXDF", "isMainEt": "", "refEt": "", "rollname": "WT_EXDF"}, {"description": "Exemption End Date", "fieldKind": "Attribute", "hryCol": "WT_EXDT", "isMainEt": "", "refEt": "", "rollname": "WT_EXDT"}, {"description": "Exemption Certificate No.", "fieldKind": "Attribute", "hryCol": "WT_EXNR", "isMainEt": "", "refEt": "", "rollname": "WT_EXNR"}, {"description": "Exemption Rate", "fieldKind": "Attribute", "hryCol": "WT_EXRT", "isMainEt": "", "refEt": "", "rollname": "WT_EXRT"}, {"description": "Subject to w/tax", "fieldKind": "Attribute", "hryCol": "WT_SUBJCT", "isMainEt": "", "refEt": "", "rollname": "WT_SUBJCT"}, {"description": "Withholding Tax Code", "fieldKind": "Attribute", "hryCol": "WT_WITHCD", "isMainEt": "", "refEt": "WT_WITHCD", "rollname": "WT_WITHCD"}, {"description": "Exemption Reason", "fieldKind": "Attribute", "hryCol": "WT_WTEXRS", "isMainEt": "", "refEt": "WT_WTEXRS", "rollname": "WT_WTEXRS"}, {"description": "W/tax identification no.", "fieldKind": "Attribute", "hryCol": "WT_WTSTCD", "isMainEt": "", "refEt": "", "rollname": "WT_WTSTCD"}], "isFolder": "X", "relation": "BP_COMPNY"}, {"name": "BP_DUNN", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Company Code", "fieldKind": "Leading Entity Type", "hryCol": "COMPANY", "isMainEt": "", "refEt": "", "rollname": "BUKRS"}, {"description": "Dunning Area", "fieldKind": "Qualifying Entity Type", "hryCol": "MABER", "isMainEt": "", "refEt": "", "rollname": "MABER"}, {"description": "Clerk Abbreviation", "fieldKind": "Attribute", "hryCol": "BUSAB", "isMainEt": "", "refEt": "BUSAB", "rollname": "BUSAB"}, {"description": "Legal Dunn.Proc.From", "fieldKind": "Attribute", "hryCol": "GMVDT", "isMainEt": "", "refEt": "", "rollname": "GMVDT"}, {"description": "Dunn.recipient", "fieldKind": "Attribute", "hryCol": "LFRMA", "isMainEt": "", "refEt": "", "rollname": "LFRMA"}, {"description": "Last Dunned", "fieldKind": "Attribute", "hryCol": "MADAT", "isMainEt": "", "refEt": "", "rollname": "MADAT"}, {"description": "Dunning Procedure", "fieldKind": "Attribute", "hryCol": "MAHNA", "isMainEt": "", "refEt": "", "rollname": "MAHNA"}, {"description": "Dunning Level", "fieldKind": "Attribute", "hryCol": "MAHNS", "isMainEt": "", "refEt": "", "rollname": "MAHNS_D"}, {"description": "Dunning Block", "fieldKind": "Attribute", "hryCol": "MANSP", "isMainEt": "", "refEt": "", "rollname": "MANSP"}], "isFolder": "X", "relation": "BP_COMPNY"}, {"name": "BP_MLT_AD", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Address Number", "fieldKind": "Qualifying Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "ADR_DUMMY", "fieldKind": "Attribute", "hryCol": "ADR_DUMMY", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_DUMMY"}], "isFolder": "X", "relation": "BP_MLT_AS"}, {"name": "BP_CUSGEN", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Central order block", "fieldKind": "Attribute", "hryCol": "AUFSD", "isMainEt": "", "refEt": "AUFSD", "rollname": "AUFSD_X"}, {"description": "Express station", "fieldKind": "Attribute", "hryCol": "BAHNE", "isMainEt": "", "refEt": "", "rollname": "BAHNE"}, {"description": "Authorization Group", "fieldKind": "Attribute", "hryCol": "BEGRU", "isMainEt": "", "refEt": "BEGRU", "rollname": "BRGRU"}, {"description": "Industry code 1", "fieldKind": "Attribute", "hryCol": "BRAN1", "isMainEt": "", "refEt": "", "rollname": "BRAN1_D"}, {"description": "Industry code 2", "fieldKind": "Attribute", "hryCol": "BRAN2", "isMainEt": "", "refEt": "", "rollname": "BRAN2"}, {"description": "Industry code 3", "fieldKind": "Attribute", "hryCol": "BRAN3", "isMainEt": "", "refEt": "", "rollname": "BRAN3"}, {"description": "Industry code 4", "fieldKind": "Attribute", "hryCol": "BRAN4", "isMainEt": "", "refEt": "", "rollname": "BRAN4"}, {"description": "Industry code 5", "fieldKind": "Attribute", "hryCol": "BRAN5", "isMainEt": "", "refEt": "", "rollname": "BRAN5"}, {"description": "Authorization object", "fieldKind": "Attribute", "hryCol": "BROBJ", "isMainEt": "", "refEt": "BROBJ", "rollname": "BROBJ"}, {"description": "Central sales block", "fieldKind": "Attribute", "hryCol": "CASSD", "isMainEt": "", "refEt": "", "rollname": "CASSD_X"}, {"description": "Biochemical warfare", "fieldKind": "Attribute", "hryCol": "CCC01", "isMainEt": "", "refEt": "", "rollname": "CCC01"}, {"description": "Nuclear nonprolif.", "fieldKind": "Attribute", "hryCol": "CCC02", "isMainEt": "", "refEt": "", "rollname": "CCC02"}, {"description": "National security", "fieldKind": "Attribute", "hryCol": "CCC03", "isMainEt": "", "refEt": "", "rollname": "CCC03"}, {"description": "Missile technology", "fieldKind": "Attribute", "hryCol": "CCC04", "isMainEt": "", "refEt": "", "rollname": "CCC04"}, {"description": "Cust. CFOP category", "fieldKind": "Attribute", "hryCol": "CFOPC", "isMainEt": "", "refEt": "", "rollname": "J_1BINDUS1"}, {"description": "City code", "fieldKind": "Attribute", "hryCol": "CITYC", "isMainEt": "", "refEt": "CITYC", "rollname": "CITYC"}, {"description": "Non-milit. use", "fieldKind": "Attribute", "hryCol": "CIVVE", "isMainEt": "", "refEt": "", "rollname": "CIVVE"}, {"description": "County Code", "fieldKind": "Attribute", "hryCol": "COUNC", "isMainEt": "", "refEt": "COUNC", "rollname": "COUNC"}, {"description": "Country Key", "fieldKind": "Attribute", "hryCol": "COUNTRY", "isMainEt": "", "refEt": "COUNTRY", "rollname": "LAND1"}, {"description": "Train station", "fieldKind": "Attribute", "hryCol": "CUS_BAHNS", "isMainEt": "", "refEt": "", "rollname": "BAHNS"}, {"description": "DME indicator", "fieldKind": "Attribute", "hryCol": "CUS_DTAMS", "isMainEt": "", "refEt": "", "rollname": "DTAMS"}, {"description": "Fiscal address", "fieldKind": "Attribute", "hryCol": "CUS_FISKN", "isMainEt": "", "refEt": "", "rollname": "FISKN_D"}, {"description": "Group key", "fieldKind": "Attribute", "hryCol": "CUS_KONZS", "isMainEt": "", "refEt": "", "rollname": "KONZS"}, {"description": "Language Key", "fieldKind": "Attribute", "hryCol": "CUS_LANGU", "isMainEt": "", "refEt": "CUS_LANGU", "rollname": "SPRAST"}, {"description": "<PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "CUS_LIFNR", "isMainEt": "", "refEt": "", "rollname": "LIFNR"}, {"description": "Central deletion flag", "fieldKind": "Attribute", "hryCol": "CUS_LOEVM", "isMainEt": "", "refEt": "", "rollname": "LOEVM_X"}, {"description": "Central del.block", "fieldKind": "Attribute", "hryCol": "CUS_NODEL", "isMainEt": "", "refEt": "", "rollname": "NODEL_X"}, {"description": "Central posting block", "fieldKind": "Attribute", "hryCol": "CUS_SPERR", "isMainEt": "", "refEt": "", "rollname": "SPERB_X"}, {"description": "Sales equalizatn tax", "fieldKind": "Attribute", "hryCol": "CUS_STKZA", "isMainEt": "", "refEt": "", "rollname": "STKZA"}, {"description": "Liable for VAT", "fieldKind": "Attribute", "hryCol": "CUS_STKZU", "isMainEt": "", "refEt": "", "rollname": "STKZU"}, {"description": "Trading partner", "fieldKind": "Attribute", "hryCol": "CUS_VBUND", "isMainEt": "", "refEt": "", "rollname": "RASSC"}, {"description": "Alt.payer in doc?", "fieldKind": "Attribute", "hryCol": "CUS_XZEMP", "isMainEt": "", "refEt": "", "rollname": "XREGU"}, {"description": "Instruction key", "fieldKind": "Attribute", "hryCol": "DTAWS", "isMainEt": "", "refEt": "DTAWS", "rollname": "DTAWS"}, {"description": "Central billing block", "fieldKind": "Attribute", "hryCol": "FAKSD", "isMainEt": "", "refEt": "", "rollname": "FAKSD_X"}, {"description": "Tax type", "fieldKind": "Attribute", "hryCol": "FITYP", "isMainEt": "", "refEt": "", "rollname": "J_1AFITP_D"}, {"description": "Year no. given for", "fieldKind": "Attribute", "hryCol": "JMJAH", "isMainEt": "", "refEt": "", "rollname": "JMJAH"}, {"description": "Employee", "fieldKind": "Attribute", "hryCol": "JMZAH", "isMainEt": "", "refEt": "", "rollname": "JMZAH"}, {"description": "Name of Representative", "fieldKind": "Attribute", "hryCol": "J_1KFREPR", "isMainEt": "", "refEt": "", "rollname": "REPRES"}, {"description": "Type of Business", "fieldKind": "Attribute", "hryCol": "J_1KFTBUS", "isMainEt": "", "refEt": "", "rollname": "GESTYP"}, {"description": "Type of Industry", "fieldKind": "Attribute", "hryCol": "J_1KFTIND", "isMainEt": "", "refEt": "", "rollname": "INDTYP"}, {"description": "Attribute 1", "fieldKind": "Attribute", "hryCol": "KATR1", "isMainEt": "", "refEt": "", "rollname": "KATR1"}, {"description": "Attribute 10", "fieldKind": "Attribute", "hryCol": "KATR10", "isMainEt": "", "refEt": "", "rollname": "KATR10"}, {"description": "Attribute 2", "fieldKind": "Attribute", "hryCol": "KATR2", "isMainEt": "", "refEt": "", "rollname": "KATR2"}, {"description": "Attribute 3", "fieldKind": "Attribute", "hryCol": "KATR3", "isMainEt": "", "refEt": "", "rollname": "KATR3"}, {"description": "Attribute 4", "fieldKind": "Attribute", "hryCol": "KATR4", "isMainEt": "", "refEt": "", "rollname": "KATR4"}, {"description": "Attribute 5", "fieldKind": "Attribute", "hryCol": "KATR5", "isMainEt": "", "refEt": "", "rollname": "KATR5"}, {"description": "Attribute 6", "fieldKind": "Attribute", "hryCol": "KATR6", "isMainEt": "", "refEt": "", "rollname": "KATR6"}, {"description": "Attribute 7", "fieldKind": "Attribute", "hryCol": "KATR7", "isMainEt": "", "refEt": "", "rollname": "KATR7"}, {"description": "Attribute 8", "fieldKind": "Attribute", "hryCol": "KATR8", "isMainEt": "", "refEt": "", "rollname": "KATR8"}, {"description": "Attribute 9", "fieldKind": "Attribute", "hryCol": "KATR9", "isMainEt": "", "refEt": "", "rollname": "KATR9"}, {"description": "Condition group 1", "fieldKind": "Attribute", "hryCol": "KDKG1", "isMainEt": "", "refEt": "", "rollname": "KDKG1"}, {"description": "Condition group 2", "fieldKind": "Attribute", "hryCol": "KDKG2", "isMainEt": "", "refEt": "", "rollname": "KDKG2"}, {"description": "Condition group 3", "fieldKind": "Attribute", "hryCol": "KDKG3", "isMainEt": "", "refEt": "", "rollname": "KDKG3"}, {"description": "Condition group 4", "fieldKind": "Attribute", "hryCol": "KDKG4", "isMainEt": "", "refEt": "", "rollname": "KDKG4"}, {"description": "Condition group 5", "fieldKind": "Attribute", "hryCol": "KDKG5", "isMainEt": "", "refEt": "", "rollname": "KDKG5"}, {"description": "Alternative payer", "fieldKind": "Attribute", "hryCol": "KNRZA", "isMainEt": "", "refEt": "", "rollname": "KNRZA"}, {"description": "Account group", "fieldKind": "Attribute", "hryCol": "KTOKD", "isMainEt": "", "refEt": "", "rollname": "KTOKD"}, {"description": "Customer classific.", "fieldKind": "Attribute", "hryCol": "KUKLA", "isMainEt": "", "refEt": "", "rollname": "KUKLA"}, {"description": "Customer", "fieldKind": "Attribute", "hryCol": "KUNNR", "isMainEt": "", "refEt": "", "rollname": "KUNNR"}, {"description": "Country Key", "fieldKind": "Attribute", "hryCol": "LAND1", "isMainEt": "", "refEt": "LAND1", "rollname": "LAND1"}, {"description": "Central delivery block", "fieldKind": "Attribute", "hryCol": "LIFSD", "isMainEt": "", "refEt": "", "rollname": "LIFSD_X"}, {"description": "Location code", "fieldKind": "Attribute", "hryCol": "LOCCO", "isMainEt": "", "refEt": "", "rollname": "LOCCO"}, {"description": "Military use", "fieldKind": "Attribute", "hryCol": "MILVE", "isMainEt": "", "refEt": "", "rollname": "MILVE"}, {"description": "Nielsen indicator", "fieldKind": "Attribute", "hryCol": "NIELS", "isMainEt": "", "refEt": "", "rollname": "NIELS"}, {"description": "Fiscal Year Variant", "fieldKind": "Attribute", "hryCol": "PERIV", "isMainEt": "", "refEt": "", "rollname": "PERIV"}, {"description": "Payment Method", "fieldKind": "Attribute", "hryCol": "PYMT_METH", "isMainEt": "", "refEt": "PYMT_METH", "rollname": "DZLSCH"}, {"description": "Region", "fieldKind": "Attribute", "hryCol": "REGION", "isMainEt": "", "refEt": "REGION", "rollname": "REGIO"}, {"description": "Sales year", "fieldKind": "Attribute", "hryCol": "UMJAH", "isMainEt": "", "refEt": "", "rollname": "UMJAH"}, {"description": "Annual sales", "fieldKind": "Attribute", "hryCol": "UMSA1", "isMainEt": "", "refEt": "", "rollname": "UMSA1"}, {"description": "Currency of sales", "fieldKind": "Attribute", "hryCol": "UWAER", "isMainEt": "", "refEt": "", "rollname": "UWAER"}, {"description": "ICMS-exempt", "fieldKind": "Attribute", "hryCol": "XICMS", "isMainEt": "", "refEt": "", "rollname": "J_1BTCICMS"}, {"description": "SubTrib group", "fieldKind": "Attribute", "hryCol": "XSUBT", "isMainEt": "", "refEt": "", "rollname": "J_1BTCST"}, {"description": "IPI-exempt", "fieldKind": "Attribute", "hryCol": "XXIPI", "isMainEt": "", "refEt": "", "rollname": "J_1BTCIPI"}, {"description": "Texts (Customer General Data)", "fieldKind": "Attribute", "hryCol": "CUSGENTXT", "isMainEt": "", "refEt": "", "rollname": "J_1BTCIPI"}, {"description": "Sales Data (Customer)", "fieldKind": "Attribute", "hryCol": "BP_SALES", "isMainEt": "", "refEt": "", "rollname": "J_1BTCIPI"}, {"description": "Customer Company Code Data", "fieldKind": "Attribute", "hryCol": "BP_CUS_CC", "isMainEt": "", "refEt": "", "rollname": "J_1BTCIPI"}, {"description": "Customer: Characteristic Valuation (Classification)", "fieldKind": "Attribute", "hryCol": "BP_CUSVAL", "isMainEt": "", "refEt": "", "rollname": "J_1BTCIPI"}, {"description": "Unloading Points for Customer", "fieldKind": "Attribute", "hryCol": "BP_CUSULP", "isMainEt": "", "refEt": "", "rollname": "J_1BTCIPI"}, {"description": "Tax Classification for Customer", "fieldKind": "Attribute", "hryCol": "BP_CUSTAX", "isMainEt": "", "refEt": "", "rollname": "J_1BTCIPI"}, {"description": "Basic Data for Document Link", "fieldKind": "Attribute", "hryCol": "BP_CUSDDB", "isMainEt": "", "refEt": "", "rollname": "J_1BTCIPI"}, {"description": "Customer: Class Assignment (Classification)", "fieldKind": "Attribute", "hryCol": "BP_CUSCLA", "isMainEt": "", "refEt": "", "rollname": "J_1BTCIPI"}], "isFolder": "X", "relation": "BP_MLT_AS"}, {"name": "CUSGENTXT", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "CUSLANGU", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Text-ID", "fieldKind": "Qualifying Entity Type", "hryCol": "CUS_TDID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_TDID"}, {"description": "Longtext", "fieldKind": "Attribute", "hryCol": "CUS_LTEXT", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_STRING"}], "isFolder": "X", "relation": "BP_CUSGEN"}, {"name": "BP_SALES", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Division", "fieldKind": "Qualifying Entity Type", "hryCol": "SPART", "isMainEt": "", "refEt": "", "rollname": "SPART"}, {"description": "Sales Organization", "fieldKind": "Qualifying Entity Type", "hryCol": "VKORG", "isMainEt": "", "refEt": "", "rollname": "VKORG"}, {"description": "Distribution Channel", "fieldKind": "Qualifying Entity Type", "hryCol": "VTWEG", "isMainEt": "", "refEt": "", "rollname": "VTWEG"}, {"description": "Max.Part.Deliveries", "fieldKind": "Attribute", "hryCol": "ANTLF", "isMainEt": "", "refEt": "", "rollname": "ANTLF"}, {"description": "Complete Delivery", "fieldKind": "Attribute", "hryCol": "AUTLF", "isMainEt": "", "refEt": "", "rollname": "AUTLF"}, {"description": "Order Probability", "fieldKind": "Attribute", "hryCol": "AWAHR", "isMainEt": "", "refEt": "", "rollname": "AWAHR"}, {"description": "Rebate", "fieldKind": "Attribute", "hryCol": "BOKRE", "isMainEt": "", "refEt": "", "rollname": "BOKRE"}, {"description": "Sales District", "fieldKind": "Attribute", "hryCol": "BZIRK", "isMainEt": "", "refEt": "", "rollname": "BZIRK"}, {"description": "Batch split allowed", "fieldKind": "Attribute", "hryCol": "CHSPL", "isMainEt": "", "refEt": "", "rollname": "CHSPL"}, {"description": "Settlement Mgmt.", "fieldKind": "Attribute", "hryCol": "CSD_AGREL", "isMainEt": "", "refEt": "", "rollname": "AGREL"}, {"description": "Central order block", "fieldKind": "Attribute", "hryCol": "CSD_AUFSD", "isMainEt": "", "refEt": "AUFSD", "rollname": "AUFSD_X"}, {"description": "Authorization Group", "fieldKind": "Attribute", "hryCol": "CSD_BEGRU", "isMainEt": "", "refEt": "BEGRU", "rollname": "BRGRU"}, {"description": "Doc. Index Active", "fieldKind": "Attribute", "hryCol": "CSD_BLIND", "isMainEt": "", "refEt": "", "rollname": "BLIND"}, {"description": "Authorization object", "fieldKind": "Attribute", "hryCol": "CSD_BROBJ", "isMainEt": "", "refEt": "BROBJ", "rollname": "BROBJ"}, {"description": "Sales Block for Sales Area", "fieldKind": "Attribute", "hryCol": "CSD_CASSD", "isMainEt": "", "refEt": "", "rollname": "CASSD_V"}, {"description": "Account at customer", "fieldKind": "Attribute", "hryCol": "CSD_EIKTO", "isMainEt": "", "refEt": "", "rollname": "EIKTO"}, {"description": "Billing block for sales area", "fieldKind": "Attribute", "hryCol": "CSD_FAKSD", "isMainEt": "", "refEt": "", "rollname": "FAKSD_V"}, {"description": "Incoterms", "fieldKind": "Attribute", "hryCol": "CSD_INCO1", "isMainEt": "", "refEt": "", "rollname": "INCO1"}, {"description": "Incoterms (Part 2)", "fieldKind": "Attribute", "hryCol": "CSD_INCO2", "isMainEt": "", "refEt": "", "rollname": "INCO2"}, {"description": "Language Key", "fieldKind": "Attribute", "hryCol": "CSD_LANGU", "isMainEt": "", "refEt": "CUS_LANGU", "rollname": "SPRAST"}, {"description": "Delivery block for sales area", "fieldKind": "Attribute", "hryCol": "CSD_LIFSD", "isMainEt": "", "refEt": "", "rollname": "LIFSD_V"}, {"description": "Del. indicator for sales area", "fieldKind": "Attribute", "hryCol": "CSD_LOEVM", "isMainEt": "", "refEt": "", "rollname": "LOEVM_V"}, {"description": "Unit of Measure Grp", "fieldKind": "Attribute", "hryCol": "CSD_MEGRU", "isMainEt": "", "refEt": "MEGRU", "rollname": "MEGRU"}, {"description": "Alternative Unit of Measure", "fieldKind": "Attribute", "hryCol": "CSD_MEINS", "isMainEt": "", "refEt": "MEINS", "rollname": "LRMEI"}, {"description": "Price determination", "fieldKind": "Attribute", "hryCol": "CSD_PRFRE", "isMainEt": "", "refEt": "", "rollname": "PRFRE"}, {"description": "Shipping Conditions", "fieldKind": "Attribute", "hryCol": "CSD_VSBED", "isMainEt": "", "refEt": "", "rollname": "VSBED"}, {"description": "<PERSON><PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "CSD_WAERS", "isMainEt": "", "refEt": "", "rollname": "WAERS_V02D"}, {"description": "Payment terms", "fieldKind": "Attribute", "hryCol": "CSD_ZTERM", "isMainEt": "", "refEt": "", "rollname": "DZTERM"}, {"description": "Paymt guarant. proc.", "fieldKind": "Attribute", "hryCol": "KABSS", "isMainEt": "", "refEt": "", "rollname": "KABSSCH_CM"}, {"description": "Cust.Pric.Procedure", "fieldKind": "Attribute", "hryCol": "KALKS", "isMainEt": "", "refEt": "", "rollname": "KALKS"}, {"description": "Customer Group", "fieldKind": "Attribute", "hryCol": "KDGRP", "isMainEt": "", "refEt": "", "rollname": "KDGRP"}, {"description": "Credit control area", "fieldKind": "Attribute", "hryCol": "KKBER", "isMainEt": "", "refEt": "", "rollname": "KKBER"}, {"description": "ABC classification", "fieldKind": "Attribute", "hryCol": "KLABC", "isMainEt": "", "refEt": "", "rollname": "KLABC"}, {"description": "Price Group", "fieldKind": "Attribute", "hryCol": "KONDA", "isMainEt": "", "refEt": "", "rollname": "KONDA"}, {"description": "Acct Assmt Grp Cust.", "fieldKind": "Attribute", "hryCol": "KTGRD", "isMainEt": "", "refEt": "", "rollname": "KTGRD"}, {"description": "Exchange Rate Type", "fieldKind": "Attribute", "hryCol": "KURST", "isMainEt": "", "refEt": "", "rollname": "KURST"}, {"description": "Customer Group 1", "fieldKind": "Attribute", "hryCol": "KVGR1", "isMainEt": "", "refEt": "", "rollname": "KVGR1"}, {"description": "Customer Group 2", "fieldKind": "Attribute", "hryCol": "KVGR2", "isMainEt": "", "refEt": "", "rollname": "KVGR2"}, {"description": "Customer Group 3", "fieldKind": "Attribute", "hryCol": "KVGR3", "isMainEt": "", "refEt": "", "rollname": "KVGR3"}, {"description": "Customer Group 4", "fieldKind": "Attribute", "hryCol": "KVGR4", "isMainEt": "", "refEt": "", "rollname": "KVGR4"}, {"description": "Customer Group 5", "fieldKind": "Attribute", "hryCol": "KVGR5", "isMainEt": "", "refEt": "", "rollname": "KVGR5"}, {"description": "Order Combination", "fieldKind": "Attribute", "hryCol": "KZAZU", "isMainEt": "", "refEt": "", "rollname": "KZAZU_D"}, {"description": "Part.dlv./item", "fieldKind": "Attribute", "hryCol": "KZTLF", "isMainEt": "", "refEt": "", "rollname": "KZTLF"}, {"description": "Delivery Priority", "fieldKind": "Attribute", "hryCol": "LPRIO", "isMainEt": "", "refEt": "", "rollname": "LPRIO"}, {"description": "Man. Invoice Maint.", "fieldKind": "Attribute", "hryCol": "MRNKZ", "isMainEt": "", "refEt": "", "rollname": "MRNKZ"}, {"description": "Invoicing Dates", "fieldKind": "Attribute", "hryCol": "PERFK", "isMainEt": "", "refEt": "", "rollname": "PERFK"}, {"description": "Invoice List Sched.", "fieldKind": "Attribute", "hryCol": "PERRL", "isMainEt": "", "refEt": "", "rollname": "PERRL"}, {"description": "Price List Type", "fieldKind": "Attribute", "hryCol": "PLTYP", "isMainEt": "", "refEt": "", "rollname": "PLTYP"}, {"description": "Relevant for POD", "fieldKind": "Attribute", "hryCol": "PODKZ", "isMainEt": "", "refEt": "", "rollname": "PODKZ"}, {"description": "POD timeframe", "fieldKind": "Attribute", "hryCol": "PODTG", "isMainEt": "", "refEt": "", "rollname": "PODTG"}, {"description": "PP customer proced.", "fieldKind": "Attribute", "hryCol": "PVKSM", "isMainEt": "", "refEt": "", "rollname": "PVKSM"}, {"description": "Switch off rounding", "fieldKind": "Attribute", "hryCol": "RDOFF", "isMainEt": "", "refEt": "", "rollname": "RDOFF"}, {"description": "Unlimited Tolerance", "fieldKind": "Attribute", "hryCol": "UEBTK", "isMainEt": "", "refEt": "", "rollname": "UEBTK_V"}, {"description": "Overdeliv. Tolerance", "fieldKind": "Attribute", "hryCol": "UEBTO", "isMainEt": "", "refEt": "", "rollname": "UEBTO"}, {"description": "Under<PERSON>. Tolerance", "fieldKind": "Attribute", "hryCol": "UNTTO", "isMainEt": "", "refEt": "", "rollname": "UNTTO"}, {"description": "Customer Stats.Group", "fieldKind": "Attribute", "hryCol": "VERSG", "isMainEt": "", "refEt": "", "rollname": "STGKU"}, {"description": "Sales office", "fieldKind": "Attribute", "hryCol": "VKBUR", "isMainEt": "", "refEt": "", "rollname": "VKBUR"}, {"description": "Sales group", "fieldKind": "Attribute", "hryCol": "VKGRP", "isMainEt": "", "refEt": "", "rollname": "VKGRP"}, {"description": "Item proposal", "fieldKind": "Attribute", "hryCol": "VSORT", "isMainEt": "", "refEt": "", "rollname": "VSORT"}, {"description": "Delivering Plant", "fieldKind": "Attribute", "hryCol": "VWERK", "isMainEt": "", "refEt": "", "rollname": "DWERK_EXT"}, {"description": "Texts (Customer Sales Data)", "fieldKind": "Attribute", "hryCol": "CUSSALTXT", "isMainEt": "", "refEt": "", "rollname": "DWERK_EXT"}, {"description": "Customer: Partner Function", "fieldKind": "Attribute", "hryCol": "BP_CUSFCN", "isMainEt": "", "refEt": "", "rollname": "DWERK_EXT"}], "isFolder": "X", "relation": "BP_CUSGEN"}, {"name": "CUSSALTXT", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Division", "fieldKind": "Leading Entity Type", "hryCol": "SPART", "isMainEt": "", "refEt": "", "rollname": "SPART"}, {"description": "Sales Organization", "fieldKind": "Leading Entity Type", "hryCol": "VKORG", "isMainEt": "", "refEt": "", "rollname": "VKORG"}, {"description": "Distribution Channel", "fieldKind": "Leading Entity Type", "hryCol": "VTWEG", "isMainEt": "", "refEt": "", "rollname": "VTWEG"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "CUSLANGU", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Text-ID", "fieldKind": "Qualifying Entity Type", "hryCol": "CUS_TDID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_TDID"}, {"description": "Longtext", "fieldKind": "Attribute", "hryCol": "CSD_LTEXT", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_STRING"}], "isFolder": "X", "relation": "BP_SALES"}, {"name": "BP_CUSFCN", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Division", "fieldKind": "Leading Entity Type", "hryCol": "SPART", "isMainEt": "", "refEt": "", "rollname": "SPART"}, {"description": "Sales Organization", "fieldKind": "Leading Entity Type", "hryCol": "VKORG", "isMainEt": "", "refEt": "", "rollname": "VKORG"}, {"description": "Distribution Channel", "fieldKind": "Leading Entity Type", "hryCol": "VTWEG", "isMainEt": "", "refEt": "", "rollname": "VTWEG"}, {"description": "Partner Function", "fieldKind": "Qualifying Entity Type", "hryCol": "PARVW", "isMainEt": "", "refEt": "", "rollname": "PARVW"}, {"description": "Partner counter", "fieldKind": "Qualifying Entity Type", "hryCol": "PARZA", "isMainEt": "", "refEt": "", "rollname": "PARZA"}, {"description": "Assignment ID", "fieldKind": "Attribute", "hryCol": "CPF_ASGID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Default Partner", "fieldKind": "Attribute", "hryCol": "CPF_DEFPA", "isMainEt": "", "refEt": "", "rollname": "DEFPA"}, {"description": "CPF_GUID", "fieldKind": "Attribute", "hryCol": "CPF_GUID", "isMainEt": "", "refEt": "", "rollname": "CHAR32"}, {"description": "<PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "CPF_LIFNR", "isMainEt": "", "refEt": "", "rollname": "LIFNR"}, {"description": "Contact Person", "fieldKind": "Attribute", "hryCol": "CPF_PARNR", "isMainEt": "", "refEt": "", "rollname": "PARNR"}, {"description": "Personnel Number", "fieldKind": "Attribute", "hryCol": "CPF_PERNR", "isMainEt": "", "refEt": "", "rollname": "PERNR_D"}, {"description": "Reflexive Partner Functions", "fieldKind": "Attribute", "hryCol": "CPF_REFLX", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_PFNC_REFLEXIVE"}, {"description": "Partner description", "fieldKind": "Attribute", "hryCol": "KNREF", "isMainEt": "", "refEt": "", "rollname": "KNREF"}, {"description": "Customer", "fieldKind": "Attribute", "hryCol": "KUNN2", "isMainEt": "", "refEt": "", "rollname": "KUNN2"}], "isFolder": "X", "relation": "BP_SALES"}, {"name": "BP_CUS_CC", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Company Code", "fieldKind": "Qualifying Entity Type", "hryCol": "COMPANY", "isMainEt": "", "refEt": "", "rollname": "BUKRS"}, {"description": "Reconciliation acct", "fieldKind": "Attribute", "hryCol": "CUS_AKONT", "isMainEt": "", "refEt": "AKONT", "rollname": "AKONT"}, {"description": "Previous Account No.", "fieldKind": "Attribute", "hryCol": "CUS_ALTKN", "isMainEt": "", "refEt": "", "rollname": "ALTKN"}, {"description": "Authorization Group", "fieldKind": "Attribute", "hryCol": "CUS_BEGRU", "isMainEt": "", "refEt": "BEGRU", "rollname": "BRGRU"}, {"description": "Subsidy Indicator", "fieldKind": "Attribute", "hryCol": "CUS_BLNKZ", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_BLNKZ"}, {"description": "Authorization object", "fieldKind": "Attribute", "hryCol": "CUS_BROBJ", "isMainEt": "", "refEt": "BROBJ", "rollname": "BROBJ"}, {"description": "Clerk Abbreviation", "fieldKind": "Attribute", "hryCol": "CUS_BUSAB", "isMainEt": "", "refEt": "BUSAB", "rollname": "BUSAB"}, {"description": "Valuation Area", "fieldKind": "Attribute", "hryCol": "CUS_BWBER", "isMainEt": "", "refEt": "BWBER", "rollname": "BWBER"}, {"description": "AR Pledging Ind.", "fieldKind": "Attribute", "hryCol": "CUS_CESSN", "isMainEt": "", "refEt": "CESSION", "rollname": "CESSION_KZ"}, {"description": "Date of Last Interest Calc.", "fieldKind": "Attribute", "hryCol": "CUS_DATLZ", "isMainEt": "", "refEt": "", "rollname": "DATLZ"}, {"description": "Account at customer", "fieldKind": "Attribute", "hryCol": "CUS_EIKTO", "isMainEt": "", "refEt": "", "rollname": "EIKTO_D"}, {"description": "Planning Group", "fieldKind": "Attribute", "hryCol": "CUS_FDGRV", "isMainEt": "", "refEt": "", "rollname": "FDGRV"}, {"description": "Release Group", "fieldKind": "Attribute", "hryCol": "CUS_FRGRP", "isMainEt": "", "refEt": "", "rollname": "FRGRP"}, {"description": "Future", "fieldKind": "Attribute", "hryCol": "CUS_FUTUR", "isMainEt": "", "refEt": "FUTURE", "rollname": "FFUTURE"}, {"description": "Credit Memo Pyt Term", "fieldKind": "Attribute", "hryCol": "CUS_GUZTE", "isMainEt": "", "refEt": "", "rollname": "GUZTE"}, {"description": "House bank", "fieldKind": "Attribute", "hryCol": "CUS_HBKID", "isMainEt": "", "refEt": "HBKID", "rollname": "HBKID"}, {"description": "<PERSON><PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "CUS_HWAER", "isMainEt": "", "refEt": "", "rollname": "WAERS"}, {"description": "Clrk's internet add.", "fieldKind": "Attribute", "hryCol": "CUS_INTAD", "isMainEt": "", "refEt": "", "rollname": "INTAD"}, {"description": "Chart of Accounts", "fieldKind": "Attribute", "hryCol": "CUS_KTOPL", "isMainEt": "", "refEt": "KTOPL", "rollname": "KTOPL"}, {"description": "Check Cashing Time", "fieldKind": "Attribute", "hryCol": "CUS_KULTG", "isMainEt": "", "refEt": "", "rollname": "KULTG"}, {"description": "Account <PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "CUS_KVERM", "isMainEt": "", "refEt": "", "rollname": "KVERM"}, {"description": "Country Key", "fieldKind": "Attribute", "hryCol": "CUS_LAND1", "isMainEt": "", "refEt": "LAND1", "rollname": "LAND1"}, {"description": "Deletion flag for company code", "fieldKind": "Attribute", "hryCol": "CUS_LOEVB", "isMainEt": "", "refEt": "", "rollname": "LOEVM_B"}, {"description": "Grouping Key", "fieldKind": "Attribute", "hryCol": "CUS_MGRUP", "isMainEt": "", "refEt": "", "rollname": "MGRUP"}, {"description": "CoCd deletion block", "fieldKind": "Attribute", "hryCol": "CUS_NDELC", "isMainEt": "", "refEt": "", "rollname": "NODEL_B"}, {"description": "Days Overdue", "fieldKind": "Attribute", "hryCol": "CUS_OVDUE", "isMainEt": "", "refEt": "OVDUE", "rollname": "OVDUE"}, {"description": "Personnel Number", "fieldKind": "Attribute", "hryCol": "CUS_PERNR", "isMainEt": "", "refEt": "", "rollname": "PERNR_D"}, {"description": "Posting block for company code", "fieldKind": "Attribute", "hryCol": "CUS_SPERB", "isMainEt": "", "refEt": "", "rollname": "SPERB_B"}, {"description": "Acct.clerks tel.no.", "fieldKind": "Attribute", "hryCol": "CUS_TLFNS", "isMainEt": "", "refEt": "", "rollname": "TLFNS"}, {"description": "Acctg clerk's fax", "fieldKind": "Attribute", "hryCol": "CUS_TLFXS", "isMainEt": "", "refEt": "", "rollname": "TLFXS"}, {"description": "Tolerance Group", "fieldKind": "Attribute", "hryCol": "CUS_TOGRU", "isMainEt": "", "refEt": "TOGRU", "rollname": "TOGRU"}, {"description": "Pmt meth. supplement", "fieldKind": "Attribute", "hryCol": "CUS_UZAWE", "isMainEt": "", "refEt": "", "rollname": "UZAWE"}, {"description": "Interest indicator", "fieldKind": "Attribute", "hryCol": "CUS_VZSKZ", "isMainEt": "", "refEt": "", "rollname": "VZSKZ"}, {"description": "Bill/Ex. Limit", "fieldKind": "Attribute", "hryCol": "CUS_WEBTR", "isMainEt": "", "refEt": "", "rollname": "WEBTR"}, {"description": "Account Statement", "fieldKind": "Attribute", "hryCol": "CUS_XAUSZ", "isMainEt": "", "refEt": "", "rollname": "XAUSZ"}, {"description": "Local Processing", "fieldKind": "Attribute", "hryCol": "CUS_XDEZV", "isMainEt": "", "refEt": "", "rollname": "XDEZV"}, {"description": "Pmnt advice by EDI", "fieldKind": "Attribute", "hryCol": "CUS_XEDIP", "isMainEt": "", "refEt": "", "rollname": "XEDIP"}, {"description": "Individual Payment", "fieldKind": "Attribute", "hryCol": "CUS_XPORE", "isMainEt": "", "refEt": "", "rollname": "XPORE"}, {"description": "Clearing with vendor", "fieldKind": "Attribute", "hryCol": "CUS_XVERR", "isMainEt": "", "refEt": "", "rollname": "XVERR_KNB1"}, {"description": "Payment Block", "fieldKind": "Attribute", "hryCol": "CUS_ZAHLS", "isMainEt": "", "refEt": "", "rollname": "DZAHLS"}, {"description": "Grouping key", "fieldKind": "Attribute", "hryCol": "CUS_ZGRUP", "isMainEt": "", "refEt": "", "rollname": "DZGRUP"}, {"description": "Key Date of Last Int. Calc.", "fieldKind": "Attribute", "hryCol": "CUS_ZINDT", "isMainEt": "", "refEt": "", "rollname": "DZINDT"}, {"description": "Interest Calc. Frequency", "fieldKind": "Attribute", "hryCol": "CUS_ZINRT", "isMainEt": "", "refEt": "", "rollname": "DZINRT"}, {"description": "User at customer", "fieldKind": "Attribute", "hryCol": "CUS_ZSABE", "isMainEt": "", "refEt": "", "rollname": "DZSABE_D"}, {"description": "Payment terms", "fieldKind": "Attribute", "hryCol": "CUS_ZTERM", "isMainEt": "", "refEt": "", "rollname": "DZTERM"}, {"description": "Sort key", "fieldKind": "Attribute", "hryCol": "CUS_ZUAWA", "isMainEt": "", "refEt": "", "rollname": "DZUAWA"}, {"description": "Payment Methods", "fieldKind": "Attribute", "hryCol": "CUS_ZWELS", "isMainEt": "", "refEt": "", "rollname": "DZWELS"}, {"description": "Buying Group", "fieldKind": "Attribute", "hryCol": "EKVBD", "isMainEt": "", "refEt": "", "rollname": "EKVBD"}, {"description": "Alternative payer", "fieldKind": "Attribute", "hryCol": "KNRZB", "isMainEt": "", "refEt": "", "rollname": "KNRZB"}, {"description": "Head office", "fieldKind": "Attribute", "hryCol": "KNRZE", "isMainEt": "", "refEt": "", "rollname": "KNRZE"}, {"description": "Lockbox", "fieldKind": "Attribute", "hryCol": "LOCKB", "isMainEt": "", "refEt": "LOCKB", "rollname": "LOCKB"}, {"description": "Coll.Invoice Variant", "fieldKind": "Attribute", "hryCol": "PERKZ", "isMainEt": "", "refEt": "", "rollname": "PERKZ_KNB1"}, {"description": "Next payee", "fieldKind": "Attribute", "hryCol": "REMIT", "isMainEt": "", "refEt": "", "rollname": "REMIT"}, {"description": "Selection Rule", "fieldKind": "Attribute", "hryCol": "SREGL", "isMainEt": "", "refEt": "", "rollname": "SREGL"}, {"description": "Known/Negotiat.Leave", "fieldKind": "Attribute", "hryCol": "URLID", "isMainEt": "", "refEt": "", "rollname": "URLID"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "VERDT", "isMainEt": "", "refEt": "", "rollname": "VERDT"}, {"description": "Amount Insured", "fieldKind": "Attribute", "hryCol": "VLIBB", "isMainEt": "", "refEt": "", "rollname": "VLIBB"}, {"description": "Institution Number", "fieldKind": "Attribute", "hryCol": "VRBKZ", "isMainEt": "", "refEt": "", "rollname": "VRBKZ"}, {"description": "Reason Code Conv.", "fieldKind": "Attribute", "hryCol": "VRSDG", "isMainEt": "", "refEt": "", "rollname": "VRSDG"}, {"description": "Policy Number", "fieldKind": "Attribute", "hryCol": "VRSNR", "isMainEt": "", "refEt": "", "rollname": "VRSNR"}, {"description": "Deductible", "fieldKind": "Attribute", "hryCol": "VRSPR", "isMainEt": "", "refEt": "", "rollname": "VRSPR"}, {"description": "Lead Months", "fieldKind": "Attribute", "hryCol": "VRSZL", "isMainEt": "", "refEt": "", "rollname": "VRSZL"}, {"description": "B/Ex. Charges Terms", "fieldKind": "Attribute", "hryCol": "WAKON", "isMainEt": "", "refEt": "", "rollname": "WAKON"}, {"description": "Value Adjustment", "fieldKind": "Attribute", "hryCol": "WBRSL", "isMainEt": "", "refEt": "WBRSL", "rollname": "WBRSL"}, {"description": "Record pmnt history", "fieldKind": "Attribute", "hryCol": "XZVER", "isMainEt": "", "refEt": "", "rollname": "XZVER"}, {"description": "Accounting", "fieldKind": "Attribute", "hryCol": "ZAMIB", "isMainEt": "", "refEt": "", "rollname": "DZAMIB"}, {"description": "Customer (with CI)", "fieldKind": "Attribute", "hryCol": "ZAMIM", "isMainEt": "", "refEt": "", "rollname": "DZAMIM"}, {"description": "Customer (W/o CI)", "fieldKind": "Attribute", "hryCol": "ZAMIO", "isMainEt": "", "refEt": "", "rollname": "DZAMIO"}, {"description": "Legal Department", "fieldKind": "Attribute", "hryCol": "ZAMIR", "isMainEt": "", "refEt": "", "rollname": "DZAMIR"}, {"description": "Sales", "fieldKind": "Attribute", "hryCol": "ZAMIV", "isMainEt": "", "refEt": "", "rollname": "DZAMIV"}, {"description": "Texts (Customer Company Code Data)", "fieldKind": "Attribute", "hryCol": "CUSCCTXT", "isMainEt": "", "refEt": "", "rollname": "DZAMIV"}, {"description": "Customer: Extended Withholding Tax", "fieldKind": "Attribute", "hryCol": "BP_CUSWHT", "isMainEt": "", "refEt": "", "rollname": "DZAMIV"}, {"description": "Dunning Data (Customer)", "fieldKind": "Attribute", "hryCol": "BP_CUSDUN", "isMainEt": "", "refEt": "", "rollname": "DZAMIV"}], "isFolder": "X", "relation": "BP_CUSGEN"}, {"name": "CUSCCTXT", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Company Code", "fieldKind": "Leading Entity Type", "hryCol": "COMPANY", "isMainEt": "", "refEt": "", "rollname": "BUKRS"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "CUSLANGU", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Text-ID", "fieldKind": "Qualifying Entity Type", "hryCol": "CUS_TDID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_TDID"}, {"description": "Longtext", "fieldKind": "Attribute", "hryCol": "CCC_LTEXT", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_STRING"}], "isFolder": "X", "relation": "BP_CUS_CC"}, {"name": "BP_CUSWHT", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Company Code", "fieldKind": "Leading Entity Type", "hryCol": "COMPANY", "isMainEt": "", "refEt": "", "rollname": "BUKRS"}, {"description": "Withholding Tax Type", "fieldKind": "Qualifying Entity Type", "hryCol": "WITHT", "isMainEt": "", "refEt": "", "rollname": "WITHT"}, {"description": "Withholding Tax Agent", "fieldKind": "Attribute", "hryCol": "CWT_AGENT", "isMainEt": "", "refEt": "", "rollname": "WT_WTAGT"}, {"description": "W/Tax Obligated Frm", "fieldKind": "Attribute", "hryCol": "CWT_AGTDF", "isMainEt": "", "refEt": "", "rollname": "WT_AGTDF"}, {"description": "Oblig.to W/Tax Until", "fieldKind": "Attribute", "hryCol": "CWT_AGTDT", "isMainEt": "", "refEt": "", "rollname": "WT_AGTDT"}, {"description": "Exemption Start Date", "fieldKind": "Attribute", "hryCol": "CWT_EXDF", "isMainEt": "", "refEt": "", "rollname": "WT_EXDF"}, {"description": "Exemption End Date", "fieldKind": "Attribute", "hryCol": "CWT_EXDT", "isMainEt": "", "refEt": "", "rollname": "WT_EXDT"}, {"description": "Exemption Certificate No.", "fieldKind": "Attribute", "hryCol": "CWT_EXNR", "isMainEt": "", "refEt": "", "rollname": "WT_EXNR"}, {"description": "Exemption Rate", "fieldKind": "Attribute", "hryCol": "CWT_EXRT", "isMainEt": "", "refEt": "", "rollname": "WT_EXRT"}, {"description": "Withholding Tax Code", "fieldKind": "Attribute", "hryCol": "CWT_WITHC", "isMainEt": "", "refEt": "WT_WITHCD", "rollname": "WT_WITHCD"}, {"description": "Exemption Reason", "fieldKind": "Attribute", "hryCol": "CWT_WTEXR", "isMainEt": "", "refEt": "", "rollname": "MDG_WT_WTEXRS"}, {"description": "W/tax identification no.", "fieldKind": "Attribute", "hryCol": "CWT_WTSTC", "isMainEt": "", "refEt": "", "rollname": "WT_WTSTCD"}], "isFolder": "X", "relation": "BP_CUS_CC"}, {"name": "BP_CUSDUN", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Company Code", "fieldKind": "Leading Entity Type", "hryCol": "COMPANY", "isMainEt": "", "refEt": "", "rollname": "BUKRS"}, {"description": "Dunning Area", "fieldKind": "Qualifying Entity Type", "hryCol": "MABER", "isMainEt": "", "refEt": "", "rollname": "MABER"}, {"description": "Clerk Abbreviation", "fieldKind": "Attribute", "hryCol": "CDD_BUSAB", "isMainEt": "", "refEt": "BUSAB", "rollname": "BUSAB"}, {"description": "Legal Dunn.Proc.From", "fieldKind": "Attribute", "hryCol": "CDD_GMVDT", "isMainEt": "", "refEt": "", "rollname": "GMVDT"}, {"description": "Last Dunned", "fieldKind": "Attribute", "hryCol": "CDD_MADAT", "isMainEt": "", "refEt": "", "rollname": "MADAT"}, {"description": "Dunning Procedure", "fieldKind": "Attribute", "hryCol": "CDD_MAHNA", "isMainEt": "", "refEt": "", "rollname": "MAHNA"}, {"description": "Dunning Level", "fieldKind": "Attribute", "hryCol": "CDD_MAHNS", "isMainEt": "", "refEt": "", "rollname": "MAHNS_D"}, {"description": "Dunning Block", "fieldKind": "Attribute", "hryCol": "CDD_MANSP", "isMainEt": "", "refEt": "", "rollname": "MANSP"}, {"description": "Dunning Recipient", "fieldKind": "Attribute", "hryCol": "KNRMA", "isMainEt": "", "refEt": "", "rollname": "KNRMA"}], "isFolder": "X", "relation": "BP_CUS_CC"}, {"name": "BP_CUSVAL", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Characteristic ID", "fieldKind": "Qualifying Entity Type", "hryCol": "CHARID", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_CLF_CHARID"}, {"description": "Class Type", "fieldKind": "Qualifying Entity Type", "hryCol": "CLASSTYPE", "isMainEt": "", "refEt": "", "rollname": "KLASSENART"}, {"description": "Int. counter", "fieldKind": "Qualifying Entity Type", "hryCol": "ECOCNTR", "isMainEt": "", "refEt": "", "rollname": "ADZHL"}, {"description": "Counter", "fieldKind": "Qualifying Entity Type", "hryCol": "VALCNT", "isMainEt": "", "refEt": "", "rollname": "WZAEHL"}, {"description": "Author", "fieldKind": "Attribute", "hryCol": "CUS_ATAUT", "isMainEt": "", "refEt": "", "rollname": "ATAUT"}, {"description": "Alternative unit", "fieldKind": "Attribute", "hryCol": "CUS_ATAW1", "isMainEt": "", "refEt": "", "rollname": "ATAWE"}, {"description": "Alternative unit", "fieldKind": "Attribute", "hryCol": "CUS_ATAWE", "isMainEt": "", "refEt": "", "rollname": "ATAWE"}, {"description": "Code", "fieldKind": "Attribute", "hryCol": "CUS_ATCOD", "isMainEt": "", "refEt": "", "rollname": "ATCOD"}, {"description": "Value to", "fieldKind": "Attribute", "hryCol": "CUS_ATFLB", "isMainEt": "", "refEt": "", "rollname": "ATFLB"}, {"description": "Value from", "fieldKind": "Attribute", "hryCol": "CUS_ATFLV", "isMainEt": "", "refEt": "", "rollname": "ATFLV"}, {"description": "Field length 10", "fieldKind": "Attribute", "hryCol": "CUS_ATIMB", "isMainEt": "", "refEt": "", "rollname": "NUMC10"}, {"description": "Position", "fieldKind": "Attribute", "hryCol": "CUS_ATSRT", "isMainEt": "", "refEt": "", "rollname": "ATSRT"}, {"description": "Characteristic Value", "fieldKind": "Attribute", "hryCol": "CUS_ATWRT", "isMainEt": "", "refEt": "", "rollname": "ATWRT"}, {"description": "Instance counter", "fieldKind": "Attribute", "hryCol": "CUS_ATZIS", "isMainEt": "", "refEt": "", "rollname": "ATZIS"}, {"description": "Deletion Indicator", "fieldKind": "Attribute", "hryCol": "CUS_VADEL", "isMainEt": "", "refEt": "", "rollname": "LKENZ"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "CUS_VADVF", "isMainEt": "", "refEt": "", "rollname": "DATUV"}, {"description": "Comp. type", "fieldKind": "Attribute", "hryCol": "CUS_VGLAR", "isMainEt": "", "refEt": "", "rollname": "ATVGLART"}], "isFolder": "X", "relation": "BP_CUSGEN"}, {"name": "BP_CUSULP", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Unloading Point", "fieldKind": "Qualifying Entity Type", "hryCol": "ABLAD", "isMainEt": "", "refEt": "", "rollname": "ABLAD"}, {"description": "Default unloading pt", "fieldKind": "Attribute", "hryCol": "DEFAB", "isMainEt": "", "refEt": "", "rollname": "DEFAB"}, {"description": "Tuesday", "fieldKind": "Attribute", "hryCol": "DIAB1", "isMainEt": "", "refEt": "", "rollname": "WADIAB1"}, {"description": "Tuesday", "fieldKind": "Attribute", "hryCol": "DIAB2", "isMainEt": "", "refEt": "", "rollname": "WADIAB2"}, {"description": "Tuesday", "fieldKind": "Attribute", "hryCol": "DIBI1", "isMainEt": "", "refEt": "", "rollname": "WADIBI1"}, {"description": "Tuesday", "fieldKind": "Attribute", "hryCol": "DIBI2", "isMainEt": "", "refEt": "", "rollname": "WADIBI2"}, {"description": "Thursday", "fieldKind": "Attribute", "hryCol": "DOAB1", "isMainEt": "", "refEt": "", "rollname": "WADOAB1"}, {"description": "Thursday", "fieldKind": "Attribute", "hryCol": "DOAB2", "isMainEt": "", "refEt": "", "rollname": "WADOAB2"}, {"description": "Thursday", "fieldKind": "Attribute", "hryCol": "DOBI1", "isMainEt": "", "refEt": "", "rollname": "WADOBI1"}, {"description": "Thursday", "fieldKind": "Attribute", "hryCol": "DOBI2", "isMainEt": "", "refEt": "", "rollname": "WADOBI2"}, {"description": "Friday", "fieldKind": "Attribute", "hryCol": "FRAB1", "isMainEt": "", "refEt": "", "rollname": "WAFRAB1"}, {"description": "Friday", "fieldKind": "Attribute", "hryCol": "FRAB2", "isMainEt": "", "refEt": "", "rollname": "WAFRAB2"}, {"description": "Friday", "fieldKind": "Attribute", "hryCol": "FRBI1", "isMainEt": "", "refEt": "", "rollname": "WAFRBI1"}, {"description": "Friday", "fieldKind": "Attribute", "hryCol": "FRBI2", "isMainEt": "", "refEt": "", "rollname": "WAFRBI2"}, {"description": "Cust.fact.calendar", "fieldKind": "Attribute", "hryCol": "KNFAK", "isMainEt": "", "refEt": "", "rollname": "KNKAL"}, {"description": "Wednesday", "fieldKind": "Attribute", "hryCol": "MIAB1", "isMainEt": "", "refEt": "", "rollname": "WAMIAB1"}, {"description": "Wednesday", "fieldKind": "Attribute", "hryCol": "MIAB2", "isMainEt": "", "refEt": "", "rollname": "WAMIAB2"}, {"description": "Wednesday", "fieldKind": "Attribute", "hryCol": "MIBI1", "isMainEt": "", "refEt": "", "rollname": "WAMIBI1"}, {"description": "Wednesday", "fieldKind": "Attribute", "hryCol": "MIBI2", "isMainEt": "", "refEt": "", "rollname": "WAMIBI2"}, {"description": "Monday", "fieldKind": "Attribute", "hryCol": "MOAB1", "isMainEt": "", "refEt": "", "rollname": "WAMOAB1"}, {"description": "Monday", "fieldKind": "Attribute", "hryCol": "MOAB2", "isMainEt": "", "refEt": "", "rollname": "WAMOAB2"}, {"description": "Monday", "fieldKind": "Attribute", "hryCol": "MOBI1", "isMainEt": "", "refEt": "", "rollname": "WAMOBI1"}, {"description": "Monday", "fieldKind": "Attribute", "hryCol": "MOBI2", "isMainEt": "", "refEt": "", "rollname": "WAMOBI2"}, {"description": "Saturday", "fieldKind": "Attribute", "hryCol": "SAAB1", "isMainEt": "", "refEt": "", "rollname": "WASAAB1"}, {"description": "Saturday", "fieldKind": "Attribute", "hryCol": "SAAB2", "isMainEt": "", "refEt": "", "rollname": "WASAAB2"}, {"description": "Saturday", "fieldKind": "Attribute", "hryCol": "SABI1", "isMainEt": "", "refEt": "", "rollname": "WASABI1"}, {"description": "Saturday", "fieldKind": "Attribute", "hryCol": "SABI2", "isMainEt": "", "refEt": "", "rollname": "WASABI2"}, {"description": "Sunday", "fieldKind": "Attribute", "hryCol": "SOAB1", "isMainEt": "", "refEt": "", "rollname": "WASOAB1"}, {"description": "Sunday", "fieldKind": "Attribute", "hryCol": "SOAB2", "isMainEt": "", "refEt": "", "rollname": "WASOAB2"}, {"description": "Sunday", "fieldKind": "Attribute", "hryCol": "SOBI1", "isMainEt": "", "refEt": "", "rollname": "WASOBI1"}, {"description": "Sunday", "fieldKind": "Attribute", "hryCol": "SOBI2", "isMainEt": "", "refEt": "", "rollname": "WASOBI2"}, {"description": "Goods receiving hrs", "fieldKind": "Attribute", "hryCol": "WANID", "isMainEt": "", "refEt": "", "rollname": "WANID"}], "isFolder": "X", "relation": "BP_CUSGEN"}, {"name": "BP_CUSTAX", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Country", "fieldKind": "Qualifying Entity Type", "hryCol": "ALAND", "isMainEt": "", "refEt": "", "rollname": "ALAND"}, {"description": "Tax category", "fieldKind": "Qualifying Entity Type", "hryCol": "TATYP", "isMainEt": "", "refEt": "", "rollname": "TATYP"}, {"description": "Tax classification", "fieldKind": "Attribute", "hryCol": "TAXKD", "isMainEt": "", "refEt": "TAXKD", "rollname": "TAKLD"}], "isFolder": "X", "relation": "BP_CUSGEN"}, {"name": "BP_CUSDDB", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Document Type", "fieldKind": "Qualifying Entity Type", "hryCol": "CUS_DOKAR", "isMainEt": "", "refEt": "", "rollname": "DOKAR"}, {"description": "Document", "fieldKind": "Qualifying Entity Type", "hryCol": "CUS_DOKNR", "isMainEt": "", "refEt": "", "rollname": "DOKNR"}, {"description": "Document part", "fieldKind": "Qualifying Entity Type", "hryCol": "CUS_DOKTL", "isMainEt": "", "refEt": "", "rollname": "DOKTL_D"}, {"description": "Document version", "fieldKind": "Qualifying Entity Type", "hryCol": "CUS_DOKVR", "isMainEt": "", "refEt": "", "rollname": "DOKVR"}, {"description": "CAD indicator", "fieldKind": "Attribute", "hryCol": "CUS_CAD_P", "isMainEt": "", "refEt": "", "rollname": "CAD_POS"}, {"description": "Newest version", "fieldKind": "Attribute", "hryCol": "CUS_NWSTV", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_BP_NEWESTVER"}], "isFolder": "X", "relation": "BP_CUSGEN"}, {"name": "BP_CUSCLA", "property": [{"description": "Assignment ID", "fieldKind": "Leading Entity Type", "hryCol": "ASSGNM_ID", "isMainEt": "", "refEt": "", "rollname": "MDG_BP_ASSIGNMENT_ID"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Class", "fieldKind": "Qualifying Entity Type", "hryCol": "CLASS", "isMainEt": "", "refEt": "", "rollname": "KLASSE_D"}, {"description": "Class Type", "fieldKind": "Qualifying Entity Type", "hryCol": "CLASSTYPE", "isMainEt": "", "refEt": "", "rollname": "KLASSENART"}, {"description": "Int. counter", "fieldKind": "Qualifying Entity Type", "hryCol": "ECOCNTR", "isMainEt": "", "refEt": "", "rollname": "ADZHL"}, {"description": "Deletion Indicator", "fieldKind": "Attribute", "hryCol": "CUS_CLDEL", "isMainEt": "", "refEt": "", "rollname": "LKENZ"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "CUS_CLDVF", "isMainEt": "", "refEt": "", "rollname": "DATUV"}, {"description": "Internal class no.", "fieldKind": "Attribute", "hryCol": "CUS_CLINT", "isMainEt": "", "refEt": "", "rollname": "CLINT"}, {"description": "Status", "fieldKind": "Attribute", "hryCol": "CUS_CLSTA", "isMainEt": "", "refEt": "", "rollname": "CLSTATUS"}], "isFolder": "X", "relation": "BP_CUSGEN"}, {"name": "BP_INDSTR", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Industry", "fieldKind": "Qualifying Entity Type", "hryCol": "BP_INDSCT", "isMainEt": "", "refEt": "", "rollname": "BU_IND_SECTOR"}, {"description": "Industry System", "fieldKind": "Qualifying Entity Type", "hryCol": "BP_INDSYS", "isMainEt": "", "refEt": "", "rollname": "BU_ISTYPE"}, {"description": "Standard Industry", "fieldKind": "Attribute", "hryCol": "ISDEF", "isMainEt": "", "refEt": "", "rollname": "BU_ISDEF"}], "isFolder": "X", "relation": "BP_HEADER"}, {"name": "BP_IDNUM", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Identification Type", "fieldKind": "Qualifying Entity Type", "hryCol": "BP_IDTYPE", "isMainEt": "", "refEt": "", "rollname": "BU_ID_TYPE"}, {"description": "ID number", "fieldKind": "Qualifying Entity Type", "hryCol": "BP_ID_NUM", "isMainEt": "", "refEt": "", "rollname": "BU_ID_NUMBER"}, {"description": "Entry date", "fieldKind": "Attribute", "hryCol": "ENTRY_DAT", "isMainEt": "", "refEt": "", "rollname": "BU_ID_ENTRY_DATE"}, {"description": "<PERSON>id from", "fieldKind": "Attribute", "hryCol": "ID_VFROM", "isMainEt": "", "refEt": "", "rollname": "BU_ID_VALID_DATE_FROM"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "ID_VTO", "isMainEt": "", "refEt": "", "rollname": "BU_ID_VALID_DATE_TO"}, {"description": "Responsible Institn", "fieldKind": "Attribute", "hryCol": "INSTITUTE", "isMainEt": "", "refEt": "", "rollname": "BU_ID_INSTITUTE"}, {"description": "Region", "fieldKind": "Attribute", "hryCol": "REFIDNUMB", "isMainEt": "", "refEt": "REGION", "rollname": "REGIO"}, {"description": "Country Key", "fieldKind": "Attribute", "hryCol": "REF_IDNUM", "isMainEt": "", "refEt": "COUNTRY", "rollname": "LAND1"}], "isFolder": "X", "relation": "BP_HEADER"}, {"name": "BP_CENTRL", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Date of Birth", "fieldKind": "Attribute", "hryCol": "BIRTHDT", "isMainEt": "", "refEt": "", "rollname": "BU_BIRTHDT"}, {"description": "Birthplace", "fieldKind": "Attribute", "hryCol": "BIRTHPL", "isMainEt": "", "refEt": "", "rollname": "BU_BIRTHPL"}, {"description": "External BP Number", "fieldKind": "Attribute", "hryCol": "BPEXT", "isMainEt": "", "refEt": "", "rollname": "BU_BPEXT"}, {"description": "BP Type", "fieldKind": "Attribute", "hryCol": "BPKIND", "isMainEt": "", "refEt": "", "rollname": "BU_BPKIND"}, {"description": "Language", "fieldKind": "Attribute", "hryCol": "BU_LANGU", "isMainEt": "", "refEt": "", "rollname": "BU_LANGU"}, {"description": "Search term 1", "fieldKind": "Attribute", "hryCol": "BU_SORT1", "isMainEt": "", "refEt": "", "rollname": "BU_SORT1"}, {"description": "Search term 2", "fieldKind": "Attribute", "hryCol": "BU_SORT2", "isMainEt": "", "refEt": "", "rollname": "BU_SORT2"}, {"description": "Country of Origin", "fieldKind": "Attribute", "hryCol": "CNDSC", "isMainEt": "", "refEt": "", "rollname": "BU_CNDSC"}, {"description": "Contact", "fieldKind": "Attribute", "hryCol": "CONTACT", "isMainEt": "", "refEt": "", "rollname": "BU_CONTACT"}, {"description": "Death date", "fieldKind": "Attribute", "hryCol": "DEATHDT", "isMainEt": "", "refEt": "", "rollname": "BU_DEATHDT"}, {"description": "Employer", "fieldKind": "Attribute", "hryCol": "EMPLO", "isMainEt": "", "refEt": "", "rollname": "BU_EMPLO"}, {"description": "Date founded", "fieldKind": "Attribute", "hryCol": "FOUND_DAT", "isMainEt": "", "refEt": "", "rollname": "BU_FOUND_DAT"}, {"description": "Sex", "fieldKind": "Attribute", "hryCol": "GENDER", "isMainEt": "", "refEt": "", "rollname": "BU_SEXID"}, {"description": "Initials", "fieldKind": "Attribute", "hryCol": "INITIALSB", "isMainEt": "", "refEt": "", "rollname": "AD_INITS"}, {"description": "Occupation", "fieldKind": "Attribute", "hryCol": "JOBGR", "isMainEt": "", "refEt": "", "rollname": "BU_JOBGR"}, {"description": "Legal entity", "fieldKind": "Attribute", "hryCol": "LEGAL_ORG", "isMainEt": "", "refEt": "", "rollname": "BU_LEGAL_ORG"}, {"description": "Legal form", "fieldKind": "Attribute", "hryCol": "LGAL_ENTY", "isMainEt": "", "refEt": "", "rollname": "BU_LEGENTY"}, {"description": "Correspondence lang.", "fieldKind": "Attribute", "hryCol": "LNGU_CORR", "isMainEt": "", "refEt": "", "rollname": "BU_LANGU_CORR"}, {"description": "Int. location no. 1", "fieldKind": "Attribute", "hryCol": "LOCATION1", "isMainEt": "", "refEt": "", "rollname": "BBBNR"}, {"description": "Int. location no. 2", "fieldKind": "Attribute", "hryCol": "LOCATION2", "isMainEt": "", "refEt": "", "rollname": "BBSNR"}, {"description": "Check digit", "fieldKind": "Attribute", "hryCol": "LOCATION3", "isMainEt": "", "refEt": "", "rollname": "BUBKZ"}, {"description": "Liquidation date", "fieldKind": "Attribute", "hryCol": "LQUID_DAT", "isMainEt": "", "refEt": "", "rollname": "BU_LIQUID_DAT"}, {"description": "Marital Status", "fieldKind": "Attribute", "hryCol": "MARST", "isMainEt": "", "refEt": "", "rollname": "BU_MARST"}, {"description": "Name 1/last name", "fieldKind": "Attribute", "hryCol": "MCNAME1", "isMainEt": "", "refEt": "", "rollname": "BU_MCNAME1"}, {"description": "Name 2/First name", "fieldKind": "Attribute", "hryCol": "MCNAME2", "isMainEt": "", "refEt": "", "rollname": "BU_MCNAME2"}, {"description": "First Group Name", "fieldKind": "Attribute", "hryCol": "NAME_GRP1", "isMainEt": "", "refEt": "", "rollname": "BU_GR_NAME1"}, {"description": "Second Group Name", "fieldKind": "Attribute", "hryCol": "NAME_GRP2", "isMainEt": "", "refEt": "", "rollname": "BU_GR_NAME2"}, {"description": "Last name", "fieldKind": "Attribute", "hryCol": "NAME_LAST", "isMainEt": "", "refEt": "", "rollname": "BU_NAMEP_L"}, {"description": "Other Last Name", "fieldKind": "Attribute", "hryCol": "NAME_LST2", "isMainEt": "", "refEt": "", "rollname": "BU_NAMEPL2"}, {"description": "First Name of Organization", "fieldKind": "Attribute", "hryCol": "NAME_ORG1", "isMainEt": "", "refEt": "", "rollname": "BU_ORG_NAME1"}, {"description": "Second Name of Organization", "fieldKind": "Attribute", "hryCol": "NAME_ORG2", "isMainEt": "", "refEt": "", "rollname": "BU_ORG_NAME2"}, {"description": "Third Name of Organization", "fieldKind": "Attribute", "hryCol": "NAME_ORG3", "isMainEt": "", "refEt": "", "rollname": "BU_ORG_NAME3"}, {"description": "Fourth Name of Organization", "fieldKind": "Attribute", "hryCol": "NAME_ORG4", "isMainEt": "", "refEt": "", "rollname": "BU_ORG_NAME4"}, {"description": "First name", "fieldKind": "Attribute", "hryCol": "NAM_FIRST", "isMainEt": "", "refEt": "", "rollname": "BU_NAMEP_F"}, {"description": "Name at Birth", "fieldKind": "Attribute", "hryCol": "NAM_LAST2", "isMainEt": "", "refEt": "", "rollname": "BU_BIRTHNM"}, {"description": "Middle Name", "fieldKind": "Attribute", "hryCol": "NANMIDDLE", "isMainEt": "", "refEt": "", "rollname": "BU_NAMEMID"}, {"description": "Nationality", "fieldKind": "Attribute", "hryCol": "NATIO", "isMainEt": "", "refEt": "", "rollname": "BU_NATIO"}, {"description": "Natural Person", "fieldKind": "Attribute", "hryCol": "NATPERS", "isMainEt": "", "refEt": "", "rollname": "BU_NATURAL_PERSON"}, {"description": "Known As", "fieldKind": "Attribute", "hryCol": "NICK_NAME", "isMainEt": "", "refEt": "", "rollname": "BU_NICKNAM"}, {"description": "Full Name", "fieldKind": "Attribute", "hryCol": "NME1_TEXT", "isMainEt": "", "refEt": "", "rollname": "BU_NAME1TX"}, {"description": "Not Released", "fieldKind": "Attribute", "hryCol": "NTRLEASED", "isMainEt": "", "refEt": "", "rollname": "BU_XNOT_RELEASED"}, {"description": "Authorization Group Object", "fieldKind": "Attribute", "hryCol": "OBJHASBP", "isMainEt": "", "refEt": "AUOBJ", "rollname": "BU_AUOBJ"}, {"description": "1st Name Prefix", "fieldKind": "Attribute", "hryCol": "PREFIX1_B", "isMainEt": "", "refEt": "", "rollname": "BU_AD_PREFIX1"}, {"description": "2nd Name Prefix", "fieldKind": "Attribute", "hryCol": "PREFIX2_B", "isMainEt": "", "refEt": "", "rollname": "BU_AD_PREFIX2"}, {"description": "Print Format", "fieldKind": "Attribute", "hryCol": "PRINT_MOD", "isMainEt": "", "refEt": "", "rollname": "BU_PRINT_MODE"}, {"description": "Partner group type", "fieldKind": "Attribute", "hryCol": "PRTGRPTYP", "isMainEt": "", "refEt": "", "rollname": "BU_GRPTYP"}, {"description": "Name Format", "fieldKind": "Attribute", "hryCol": "REFCENTRL", "isMainEt": "", "refEt": "NAMFORMAT", "rollname": "AD_FORMAT"}, {"description": "Authorization Group", "fieldKind": "Attribute", "hryCol": "REF_CEN", "isMainEt": "", "refEt": "AUGRP", "rollname": "BU_AUGRP"}, {"description": "Country Key", "fieldKind": "Attribute", "hryCol": "REF_CENTR", "isMainEt": "", "refEt": "NAMCNTRY", "rollname": "LAND1"}, {"description": "Data Origin", "fieldKind": "Attribute", "hryCol": "SOURCE", "isMainEt": "", "refEt": "", "rollname": "BU_SOURCE"}, {"description": "Title", "fieldKind": "Attribute", "hryCol": "TITLE_BP", "isMainEt": "", "refEt": "", "rollname": "AD_TITLE"}, {"description": "Salutation", "fieldKind": "Attribute", "hryCol": "TITLE_LET", "isMainEt": "", "refEt": "", "rollname": "BU_TITLE_LET"}, {"description": "First academic Title", "fieldKind": "Attribute", "hryCol": "TTLE_ACA1", "isMainEt": "", "refEt": "", "rollname": "BU_AD_TITLE1"}, {"description": "2nd academic title", "fieldKind": "Attribute", "hryCol": "TTLE_ACA2", "isMainEt": "", "refEt": "", "rollname": "AD_TITLE2"}, {"description": "Name Supplement", "fieldKind": "Attribute", "hryCol": "TTLE_ROYL", "isMainEt": "", "refEt": "", "rollname": "AD_TITLES"}, {"description": "Central Block", "fieldKind": "Attribute", "hryCol": "XBLCK", "isMainEt": "", "refEt": "", "rollname": "BU_XBLCK"}, {"description": "Archiving Flag", "fieldKind": "Attribute", "hryCol": "XDELE", "isMainEt": "", "refEt": "", "rollname": "BU_XDELE"}, {"description": "Trade Expiration Date", "fieldKind": "Attribute", "hryCol": "ZZEXPD", "isMainEt": "", "refEt": "", "rollname": "ZZ_EXPD"}, {"description": "Trading Number", "fieldKind": "Attribute", "hryCol": "ZZLICENSE", "isMainEt": "", "refEt": "", "rollname": "ZZ_LICENSE"}, {"description": "ZZTEST_1", "fieldKind": "Attribute", "hryCol": "ZZTEST_1", "isMainEt": "", "refEt": "", "rollname": "CHAR1"}], "isFolder": "X", "relation": "BP_HEADER"}, {"name": "BP_CCDTL", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Payment Card ID", "fieldKind": "Qualifying Entity Type", "hryCol": "CARD_ID", "isMainEt": "", "refEt": "", "rollname": "BU_CCID"}, {"description": "CARD_GUID", "fieldKind": "Attribute", "hryCol": "CARD_GUID", "isMainEt": "", "refEt": "", "rollname": "BU_CARD_GUID_MAP"}, {"description": "Description", "fieldKind": "Attribute", "hryCol": "CCACCNAME", "isMainEt": "", "refEt": "", "rollname": "BU_CREDITCNAME"}, {"description": "Standard Card", "fieldKind": "Attribute", "hryCol": "CCDEF", "isMainEt": "", "refEt": "", "rollname": "BU_CCDEF"}, {"description": "Payment Card Type", "fieldKind": "Attribute", "hryCol": "CCINS", "isMainEt": "", "refEt": "", "rollname": "CC_INSTITUTE"}, {"description": "Card Number", "fieldKind": "Attribute", "hryCol": "CCNUM", "isMainEt": "", "refEt": "", "rollname": "BU_CCNUM"}], "isFolder": "X", "relation": "BP_HEADER"}, {"name": "BP_BKDTL", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Bank details ID", "fieldKind": "Qualifying Entity Type", "hryCol": "BANK_ID", "isMainEt": "", "refEt": "", "rollname": "BU_BKVID"}, {"description": "Account Name", "fieldKind": "Attribute", "hryCol": "ACCNAME", "isMainEt": "", "refEt": "", "rollname": "BU_BANKACCNAME"}, {"description": "Bank number", "fieldKind": "Attribute", "hryCol": "BANKL", "isMainEt": "", "refEt": "", "rollname": "BANKL"}, {"description": "Bank Account", "fieldKind": "Attribute", "hryCol": "BANKN", "isMainEt": "", "refEt": "", "rollname": "BANKN"}, {"description": "Bank Country", "fieldKind": "Attribute", "hryCol": "BANKS", "isMainEt": "", "refEt": "", "rollname": "BANKS"}, {"description": "Extern.bank dtls ID", "fieldKind": "Attribute", "hryCol": "BKEXT", "isMainEt": "", "refEt": "", "rollname": "BU_BKEXT"}, {"description": "Bank control key", "fieldKind": "Attribute", "hryCol": "BKONT", "isMainEt": "", "refEt": "", "rollname": "BKONT"}, {"description": "Reference details", "fieldKind": "Attribute", "hryCol": "BKREF", "isMainEt": "", "refEt": "", "rollname": "BKREF"}, {"description": "IBAN", "fieldKind": "Attribute", "hryCol": "IBAN", "isMainEt": "", "refEt": "", "rollname": "IBAN"}, {"description": "Account Holder", "fieldKind": "Attribute", "hryCol": "KOINH", "isMainEt": "", "refEt": "", "rollname": "BU_KOINH"}, {"description": "Move On", "fieldKind": "Attribute", "hryCol": "MOVE_DAT", "isMainEt": "", "refEt": "", "rollname": "BU_MOVEDAT"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "VAL_FROM", "isMainEt": "", "refEt": "", "rollname": "BU_DATFROM"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "VAL_TO", "isMainEt": "", "refEt": "", "rollname": "BU_DATTO"}, {"description": "Collection authorization", "fieldKind": "Attribute", "hryCol": "XEZER", "isMainEt": "", "refEt": "", "rollname": "XEZER"}], "isFolder": "X", "relation": "BP_HEADER"}, {"name": "BP_ADDR", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Address Number", "fieldKind": "Qualifying Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "UUID in Char. Format", "fieldKind": "Attribute", "hryCol": "ADDR_GUID", "isMainEt": "", "refEt": "", "rollname": "ADDR_UUID_CHAR"}, {"description": "External Address No.", "fieldKind": "Attribute", "hryCol": "ADEXT", "isMainEt": "", "refEt": "", "rollname": "BU_ADEXT"}, {"description": "Move On", "fieldKind": "Attribute", "hryCol": "MOVE_DATE", "isMainEt": "", "refEt": "", "rollname": "BU_MOVEDAT"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "VALID_FRM", "isMainEt": "", "refEt": "", "rollname": "BU_DATFROM"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "VALID_TO", "isMainEt": "", "refEt": "", "rollname": "BU_DATTO"}, {"description": "Address Usage", "fieldKind": "Attribute", "hryCol": "BP_ADDUSG", "isMainEt": "", "refEt": "", "rollname": "BU_DATTO"}], "isFolder": "X", "relation": "BP_HEADER"}, {"name": "BP_ADDUSG", "property": [{"description": "Address Number", "fieldKind": "Leading Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Address Type", "fieldKind": "Qualifying Entity Type", "hryCol": "BP_ADRKND", "isMainEt": "", "refEt": "", "rollname": "BU_ADRKIND"}, {"description": "Standard Usage", "fieldKind": "Attribute", "hryCol": "XDFADU", "isMainEt": "", "refEt": "", "rollname": "BU_XDFADU"}], "isFolder": "X", "relation": "BP_ADDR"}, {"name": "ADDRESS", "property": [{"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Address Number", "fieldKind": "Qualifying Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Address Type", "fieldKind": "Attribute", "hryCol": "TYPE", "isMainEt": "", "refEt": "", "rollname": "AD_ADRTYPE"}, {"description": "Internet Address", "fieldKind": "Attribute", "hryCol": "AD_URL", "isMainEt": "", "refEt": "", "rollname": "AD_ADRTYPE"}, {"description": "Telephone Number", "fieldKind": "Attribute", "hryCol": "AD_TEL", "isMainEt": "", "refEt": "", "rollname": "AD_ADRTYPE"}, {"description": "Physical Address", "fieldKind": "Attribute", "hryCol": "AD_POSTAL", "isMainEt": "", "refEt": "", "rollname": "AD_ADRTYPE"}, {"description": "Names of Persons", "fieldKind": "Attribute", "hryCol": "AD_NAME_P", "isMainEt": "", "refEt": "", "rollname": "AD_ADRTYPE"}, {"description": "Names of Organizations", "fieldKind": "Attribute", "hryCol": "AD_NAME_O", "isMainEt": "", "refEt": "", "rollname": "AD_ADRTYPE"}, {"description": "Fax Number", "fieldKind": "Attribute", "hryCol": "AD_FAX", "isMainEt": "", "refEt": "", "rollname": "AD_ADRTYPE"}, {"description": "E-Mail Address", "fieldKind": "Attribute", "hryCol": "AD_EMAIL", "isMainEt": "", "refEt": "", "rollname": "AD_ADRTYPE"}], "isFolder": "X", "relation": "BP_HEADER"}, {"name": "AD_URL", "property": [{"description": "Address Number", "fieldKind": "Leading Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Sequence Number", "fieldKind": "Qualifying Entity Type", "hryCol": "AD_CONSNO", "isMainEt": "", "refEt": "", "rollname": "AD_CONSNUM"}, {"description": "URI", "fieldKind": "Attribute", "hryCol": "U_ADDRESS", "isMainEt": "", "refEt": "", "rollname": "AD_URISCR"}, {"description": "Standard No.", "fieldKind": "Attribute", "hryCol": "U_FLGDEFT", "isMainEt": "", "refEt": "", "rollname": "AD_FLGDFNR"}, {"description": "Do Not Use Communication Number", "fieldKind": "Attribute", "hryCol": "U_FLGNOUS", "isMainEt": "", "refEt": "", "rollname": "AD_FLNOUSE"}, {"description": "URI address", "fieldKind": "Attribute", "hryCol": "U_SEARCH", "isMainEt": "", "refEt": "", "rollname": "AD_URI2"}, {"description": "URI type", "fieldKind": "Attribute", "hryCol": "U_TYPE", "isMainEt": "", "refEt": "", "rollname": "AD_URITYPE"}], "isFolder": "X", "relation": "ADDRESS"}, {"name": "AD_TEL", "property": [{"description": "Address Number", "fieldKind": "Leading Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Sequence Number", "fieldKind": "Qualifying Entity Type", "hryCol": "AD_CONSNO", "isMainEt": "", "refEt": "", "rollname": "AD_CONSNUM"}, {"description": "Country", "fieldKind": "Attribute", "hryCol": "T_COUNTRY", "isMainEt": "", "refEt": "", "rollname": "AD_COMCTRY"}, {"description": "Extension", "fieldKind": "Attribute", "hryCol": "T_EXTENS", "isMainEt": "", "refEt": "", "rollname": "AD_TLXTNS"}, {"description": "Standard No.", "fieldKind": "Attribute", "hryCol": "T_FLGDEFT", "isMainEt": "", "refEt": "", "rollname": "AD_FLGDFNR"}, {"description": "Mobile phone", "fieldKind": "Attribute", "hryCol": "T_FLGMOB", "isMainEt": "", "refEt": "", "rollname": "AD_FLGMOB"}, {"description": "Do Not Use Communication Number", "fieldKind": "Attribute", "hryCol": "T_FLGNOUS", "isMainEt": "", "refEt": "", "rollname": "AD_FLNOUSE"}, {"description": "SMS-Enab.", "fieldKind": "Attribute", "hryCol": "T_FLGSMS", "isMainEt": "", "refEt": "", "rollname": "AD_FLGSMS"}, {"description": "Caller number", "fieldKind": "Attribute", "hryCol": "T_NR_CALL", "isMainEt": "", "refEt": "", "rollname": "AD_TELNRCL"}, {"description": "Telephone number", "fieldKind": "Attribute", "hryCol": "T_NR_LONG", "isMainEt": "", "refEt": "", "rollname": "AD_TELNRLG"}, {"description": "Telephone", "fieldKind": "Attribute", "hryCol": "T_NUMBER", "isMainEt": "", "refEt": "", "rollname": "AD_TLNMBR"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "T_VALID_F", "isMainEt": "", "refEt": "", "rollname": "BU_DATFROM"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "T_VALID_T", "isMainEt": "", "refEt": "", "rollname": "BU_DATTO"}], "isFolder": "X", "relation": "ADDRESS"}, {"name": "AD_POSTAL", "property": [{"description": "Address Number", "fieldKind": "Leading Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Address Version", "fieldKind": "Qualifying Entity Type", "hryCol": "AD_NATION", "isMainEt": "", "refEt": "", "rollname": "AD_NATION"}, {"description": "Building Code", "fieldKind": "Attribute", "hryCol": "BUILDING", "isMainEt": "", "refEt": "", "rollname": "AD_BLDNG"}, {"description": "Test stat./City file", "fieldKind": "Attribute", "hryCol": "CHCKSTATU", "isMainEt": "", "refEt": "", "rollname": "AD_CHECKST"}, {"description": "City", "fieldKind": "Attribute", "hryCol": "CITY1", "isMainEt": "", "refEt": "", "rollname": "BS_UI_AD_CITY1"}, {"description": "c/o", "fieldKind": "Attribute", "hryCol": "CO_NAME", "isMainEt": "", "refEt": "", "rollname": "AD_NAME_CO"}, {"description": "City Code", "fieldKind": "Attribute", "hryCol": "CTY_CODE", "isMainEt": "", "refEt": "CITY_CODE", "rollname": "AD_CITYNUM"}, {"description": "Comm. Method", "fieldKind": "Attribute", "hryCol": "DEFLTCOMM", "isMainEt": "", "refEt": "", "rollname": "AD_COMM"}, {"description": "District", "fieldKind": "Attribute", "hryCol": "DISTRICT", "isMainEt": "", "refEt": "", "rollname": "AD_CITY2"}, {"description": "Number of Delivery Service", "fieldKind": "Attribute", "hryCol": "DLVSRV_NR", "isMainEt": "", "refEt": "", "rollname": "AD_DELIVERY_SERVICE_NUMBER"}, {"description": "Type of Delivry Service", "fieldKind": "Attribute", "hryCol": "DLVSRV_TY", "isMainEt": "", "refEt": "", "rollname": "AD_DELIVERY_SERVICE_TYPE"}, {"description": "Undeliverable", "fieldKind": "Attribute", "hryCol": "DONTUSE_P", "isMainEt": "", "refEt": "", "rollname": "AD_NO_USEP"}, {"description": "Undeliverable", "fieldKind": "Attribute", "hryCol": "DONTUSE_S", "isMainEt": "", "refEt": "", "rollname": "AD_NO_USES"}, {"description": "FLAGGROUP", "fieldKind": "Attribute", "hryCol": "FLAGGROUP", "isMainEt": "", "refEt": "", "rollname": "AD_FLGGP"}, {"description": "Floor", "fieldKind": "Attribute", "hryCol": "FLOOR", "isMainEt": "", "refEt": "", "rollname": "AD_FLOOR"}, {"description": "Different City", "fieldKind": "Attribute", "hryCol": "HOME_CITY", "isMainEt": "", "refEt": "", "rollname": "AD_CITY3"}, {"description": "House Number", "fieldKind": "Attribute", "hryCol": "HOUSE_NR1", "isMainEt": "", "refEt": "", "rollname": "AD_HSNM1"}, {"description": "Supplement", "fieldKind": "Attribute", "hryCol": "HOUSE_NR2", "isMainEt": "", "refEt": "", "rollname": "AD_HSNM2"}, {"description": "Language Key", "fieldKind": "Attribute", "hryCol": "LANGU_COM", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Street 5", "fieldKind": "Attribute", "hryCol": "LOCATION", "isMainEt": "", "refEt": "", "rollname": "AD_LCTN"}, {"description": "City", "fieldKind": "Attribute", "hryCol": "MC_CITY1", "isMainEt": "", "refEt": "", "rollname": "AD_MC_CITY"}, {"description": "Street", "fieldKind": "Attribute", "hryCol": "MC_STREET", "isMainEt": "", "refEt": "", "rollname": "AD_MC_STRT"}, {"description": "personal address", "fieldKind": "Attribute", "hryCol": "PERS_ADDR", "isMainEt": "", "refEt": "", "rollname": "AD_PRSADDR"}, {"description": "PO box country", "fieldKind": "Attribute", "hryCol": "POBOX_CTY", "isMainEt": "", "refEt": "", "rollname": "AD_POBXCTY"}, {"description": "PO Box Lobby", "fieldKind": "Attribute", "hryCol": "POBOX_LBY", "isMainEt": "", "refEt": "", "rollname": "AD_PO_BOX_LBY"}, {"description": "PO Box City", "fieldKind": "Attribute", "hryCol": "POBOX_LOC", "isMainEt": "", "refEt": "", "rollname": "AD_POBXLOC"}, {"description": "PO Box w/o No.", "fieldKind": "Attribute", "hryCol": "POBOX_NUM", "isMainEt": "", "refEt": "", "rollname": "AD_POBXNUM"}, {"description": "Postal Code", "fieldKind": "Attribute", "hryCol": "POST_COD1", "isMainEt": "", "refEt": "", "rollname": "AD_PSTCD1"}, {"description": "PO Box Postal Code", "fieldKind": "Attribute", "hryCol": "POST_COD2", "isMainEt": "", "refEt": "", "rollname": "AD_PSTCD2"}, {"description": "Company Postal Code", "fieldKind": "Attribute", "hryCol": "POST_COD3", "isMainEt": "", "refEt": "", "rollname": "AD_PSTCD3"}, {"description": "PO Box", "fieldKind": "Attribute", "hryCol": "PO_BOX", "isMainEt": "", "refEt": "", "rollname": "AD_POBX"}, {"description": "Country Key", "fieldKind": "Attribute", "hryCol": "REF_POSTA", "isMainEt": "", "refEt": "COUNTRY", "rollname": "LAND1"}, {"description": "City Code", "fieldKind": "Attribute", "hryCol": "REF_PSTAL", "isMainEt": "", "refEt": "CITYH_COD", "rollname": "AD_CITYNUM"}, {"description": "Structure Group", "fieldKind": "Attribute", "hryCol": "REGIOGROU", "isMainEt": "", "refEt": "", "rollname": "REGIOGROUP"}, {"description": "Region", "fieldKind": "Attribute", "hryCol": "RFE_POST", "isMainEt": "", "refEt": "REGION", "rollname": "REGIO"}, {"description": "City Code", "fieldKind": "Attribute", "hryCol": "RFE_POSTA", "isMainEt": "", "refEt": "CITY_COD2", "rollname": "AD_CIT2NUM"}, {"description": "Transportation Zone", "fieldKind": "Attribute", "hryCol": "RFE_POSTL", "isMainEt": "", "refEt": "TRANSPZON", "rollname": "LZONE"}, {"description": "PO Box Region", "fieldKind": "Attribute", "hryCol": "RFE_PSTAL", "isMainEt": "", "refEt": "POBOX_REG", "rollname": "AD_POBXREG"}, {"description": "District", "fieldKind": "Attribute", "hryCol": "RF_POSTAL", "isMainEt": "", "refEt": "CITYP_COD", "rollname": "AD_CITYPNM"}, {"description": "Room Number", "fieldKind": "Attribute", "hryCol": "ROOMNUM", "isMainEt": "", "refEt": "", "rollname": "AD_ROOMNUM"}, {"description": "Search Term 1", "fieldKind": "Attribute", "hryCol": "SORT1_AD", "isMainEt": "", "refEt": "", "rollname": "AD_SORT1"}, {"description": "Search Term 2", "fieldKind": "Attribute", "hryCol": "SORT2_AD", "isMainEt": "", "refEt": "", "rollname": "AD_SORT2"}, {"description": "Street", "fieldKind": "Attribute", "hryCol": "STREET", "isMainEt": "", "refEt": "", "rollname": "BS_UI_AD_STREET"}, {"description": "Street 2", "fieldKind": "Attribute", "hryCol": "STRSUPPL1", "isMainEt": "", "refEt": "", "rollname": "AD_STRSPP1"}, {"description": "Street 3", "fieldKind": "Attribute", "hryCol": "STRSUPPL2", "isMainEt": "", "refEt": "", "rollname": "AD_STRSPP2"}, {"description": "Street 4", "fieldKind": "Attribute", "hryCol": "STRSUPPL3", "isMainEt": "", "refEt": "", "rollname": "AD_STRSPP3"}, {"description": "Tax Jurisdiction", "fieldKind": "Attribute", "hryCol": "TAXJURCOD", "isMainEt": "", "refEt": "", "rollname": "AD_TXJCD"}, {"description": "Time zone", "fieldKind": "Attribute", "hryCol": "TIME_ZONE", "isMainEt": "", "refEt": "", "rollname": "AD_TZONE"}, {"description": "ZZ_FLAG", "fieldKind": "Attribute", "hryCol": "ZZ_FLAG", "isMainEt": "", "refEt": "", "rollname": "CHAR1"}], "isFolder": "X", "relation": "ADDRESS"}, {"name": "AD_NAME_P", "property": [{"description": "Address Number", "fieldKind": "Leading Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Address Version", "fieldKind": "Qualifying Entity Type", "hryCol": "AD_NATION", "isMainEt": "", "refEt": "", "rollname": "AD_NATION"}, {"description": "Converted", "fieldKind": "Attribute", "hryCol": "CONVERTED", "isMainEt": "", "refEt": "", "rollname": "AD_NAMCONV"}, {"description": "Initials", "fieldKind": "Attribute", "hryCol": "INITIALS", "isMainEt": "", "refEt": "", "rollname": "AD_INITS"}, {"description": "Language Key", "fieldKind": "Attribute", "hryCol": "LANGU_P", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Name at Birth", "fieldKind": "Attribute", "hryCol": "NAME2_P", "isMainEt": "", "refEt": "", "rollname": "AD_NAME2_P"}, {"description": "First name", "fieldKind": "Attribute", "hryCol": "NAMEFIRST", "isMainEt": "", "refEt": "", "rollname": "AD_NAMEFIR"}, {"description": "Last name", "fieldKind": "Attribute", "hryCol": "NAMELAST", "isMainEt": "", "refEt": "", "rollname": "AD_NAMELAS"}, {"description": "Second Family Name", "fieldKind": "Attribute", "hryCol": "NAMELAST2", "isMainEt": "", "refEt": "", "rollname": "AD_NAMLAS2"}, {"description": "Second First Name", "fieldKind": "Attribute", "hryCol": "NAMEMIDDL", "isMainEt": "", "refEt": "", "rollname": "AD_NAMEMID"}, {"description": "Full Name", "fieldKind": "Attribute", "hryCol": "NAMETXT_P", "isMainEt": "", "refEt": "", "rollname": "AD_NAMTEXT"}, {"description": "c/o", "fieldKind": "Attribute", "hryCol": "NAME_CO_P", "isMainEt": "", "refEt": "", "rollname": "AD_NAME_CO"}, {"description": "Nickname/name used", "fieldKind": "Attribute", "hryCol": "NICKNAME", "isMainEt": "", "refEt": "", "rollname": "AD_NICKNAM"}, {"description": "Prefix 1", "fieldKind": "Attribute", "hryCol": "PREFIX1", "isMainEt": "", "refEt": "", "rollname": "AD_PREFIX"}, {"description": "2nd name prefix", "fieldKind": "Attribute", "hryCol": "PREFIX2", "isMainEt": "", "refEt": "", "rollname": "AD_PREFIX2"}, {"description": "Country Key", "fieldKind": "Attribute", "hryCol": "REFNAMEP", "isMainEt": "", "refEt": "NAMCNTRY", "rollname": "LAND1"}, {"description": "Name Format", "fieldKind": "Attribute", "hryCol": "REFNAMEP1", "isMainEt": "", "refEt": "NAMFORMAT", "rollname": "AD_FORMAT"}, {"description": "Search Term 1", "fieldKind": "Attribute", "hryCol": "SORT1", "isMainEt": "", "refEt": "", "rollname": "AD_SORT1"}, {"description": "Search Term 2", "fieldKind": "Attribute", "hryCol": "SORT2", "isMainEt": "", "refEt": "", "rollname": "AD_SORT2"}, {"description": "2nd academic title", "fieldKind": "Attribute", "hryCol": "TITLEACA2", "isMainEt": "", "refEt": "", "rollname": "AD_TITLE2"}, {"description": "Academic Title 1", "fieldKind": "Attribute", "hryCol": "TITLE_ACA", "isMainEt": "", "refEt": "", "rollname": "AD_TITLE1"}, {"description": "Title", "fieldKind": "Attribute", "hryCol": "TITLE_P", "isMainEt": "", "refEt": "", "rollname": "AD_TITLE"}, {"description": "Name Supplement", "fieldKind": "Attribute", "hryCol": "TITLE_SPP", "isMainEt": "", "refEt": "", "rollname": "AD_TITLES"}], "isFolder": "X", "relation": "ADDRESS"}, {"name": "AD_NAME_O", "property": [{"description": "Address Number", "fieldKind": "Leading Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Address Version", "fieldKind": "Qualifying Entity Type", "hryCol": "AD_NATION", "isMainEt": "", "refEt": "", "rollname": "AD_NATION"}, {"description": "Company name", "fieldKind": "Attribute", "hryCol": "MC_NAME1", "isMainEt": "", "refEt": "", "rollname": "AD_MC_NAME"}, {"description": "Name", "fieldKind": "Attribute", "hryCol": "NAME1", "isMainEt": "", "refEt": "", "rollname": "AD_NAME1"}, {"description": "Name 2", "fieldKind": "Attribute", "hryCol": "NAME2", "isMainEt": "", "refEt": "", "rollname": "AD_NAME2"}, {"description": "Name 3", "fieldKind": "Attribute", "hryCol": "NAME3", "isMainEt": "", "refEt": "", "rollname": "AD_NAME3"}, {"description": "Name 4", "fieldKind": "Attribute", "hryCol": "NAME4", "isMainEt": "", "refEt": "", "rollname": "AD_NAME4"}, {"description": "c/o", "fieldKind": "Attribute", "hryCol": "NAME_CO", "isMainEt": "", "refEt": "", "rollname": "AD_NAME_CO"}, {"description": "Converted Name", "fieldKind": "Attribute", "hryCol": "NAME_TEXT", "isMainEt": "", "refEt": "", "rollname": "AD_NAMETXT"}, {"description": "Title", "fieldKind": "Attribute", "hryCol": "TITLE", "isMainEt": "", "refEt": "", "rollname": "AD_TITLE"}], "isFolder": "X", "relation": "ADDRESS"}, {"name": "AD_FAX", "property": [{"description": "Address Number", "fieldKind": "Leading Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Sequence Number", "fieldKind": "Qualifying Entity Type", "hryCol": "AD_CONSNO", "isMainEt": "", "refEt": "", "rollname": "AD_CONSNUM"}, {"description": "Country", "fieldKind": "Attribute", "hryCol": "F_COUNTRY", "isMainEt": "", "refEt": "", "rollname": "AD_COMCTRY"}, {"description": "Extension", "fieldKind": "Attribute", "hryCol": "F_EXTENS", "isMainEt": "", "refEt": "", "rollname": "AD_FXXTNS"}, {"description": "Standard No.", "fieldKind": "Attribute", "hryCol": "F_FLGDEFT", "isMainEt": "", "refEt": "", "rollname": "AD_FLGDFNR"}, {"description": "Do Not Use Communication Number", "fieldKind": "Attribute", "hryCol": "F_FLGNOUS", "isMainEt": "", "refEt": "", "rollname": "AD_FLNOUSE"}, {"description": "Sender number", "fieldKind": "Attribute", "hryCol": "F_NR_CALL", "isMainEt": "", "refEt": "", "rollname": "AD_FAXNRCL"}, {"description": "Fax number", "fieldKind": "Attribute", "hryCol": "F_NR_LONG", "isMainEt": "", "refEt": "", "rollname": "AD_FXNRLNG"}, {"description": "Fax", "fieldKind": "Attribute", "hryCol": "F_NUMBER", "isMainEt": "", "refEt": "", "rollname": "AD_FXNMBR"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "F_VALID_F", "isMainEt": "", "refEt": "", "rollname": "BU_DATFROM"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "F_VALID_T", "isMainEt": "", "refEt": "", "rollname": "BU_DATTO"}], "isFolder": "X", "relation": "ADDRESS"}, {"name": "AD_EMAIL", "property": [{"description": "Address Number", "fieldKind": "Leading Entity Type", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}, {"description": "Business Partner ID", "fieldKind": "Leading Entity Type", "hryCol": "BP_HEADER", "isMainEt": "", "refEt": "", "rollname": "BU_BUSINESSPARTNER"}, {"description": "Sequence Number", "fieldKind": "Qualifying Entity Type", "hryCol": "AD_CONSNO", "isMainEt": "", "refEt": "", "rollname": "AD_CONSNUM"}, {"description": "E-Mail Address", "fieldKind": "Attribute", "hryCol": "E_ADDRESS", "isMainEt": "", "refEt": "", "rollname": "AD_SMTPADR"}, {"description": "Standard No.", "fieldKind": "Attribute", "hryCol": "E_FLGDEFT", "isMainEt": "", "refEt": "", "rollname": "AD_FLGDFNR"}, {"description": "Do Not Use Communication Number", "fieldKind": "Attribute", "hryCol": "E_FLGNOUS", "isMainEt": "", "refEt": "", "rollname": "AD_FLNOUSE"}, {"description": "E-Mail Address", "fieldKind": "Attribute", "hryCol": "E_SEARCH", "isMainEt": "", "refEt": "", "rollname": "AD_SMTPAD2"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "E_VALID_F", "isMainEt": "", "refEt": "", "rollname": "BU_DATFROM"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "E_VALID_T", "isMainEt": "", "refEt": "", "rollname": "BU_DATTO"}], "isFolder": "X", "relation": "ADDRESS"}, {"name": "ADDRNO", "property": [{"description": "Address Number", "fieldKind": "Entity Type Itself", "hryCol": "ADDRNO", "isMainEt": "", "refEt": "", "rollname": "AD_ADDRNUM"}], "isFolder": "X", "relation": "BP"}], "MM": [{"name": "MM", "description": "Materials", "property": [], "isFolder": "X", "relation": ""}, {"name": "MKALBASIC", "property": [{"description": "Production Version", "fieldKind": "Entity Type Itself", "hryCol": "MKALBASIC", "isMainEt": "", "refEt": "", "rollname": "VERID"}, {"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Leading Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "<PERSON>id from", "fieldKind": "Attribute", "hryCol": "ADATUMKAL", "isMainEt": "", "refEt": "", "rollname": "ADATM"}, {"description": "Receiving Location", "fieldKind": "Attribute", "hryCol": "ALOMKALBA", "isMainEt": "", "refEt": "ALORTMKAL", "rollname": "ALORT"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "BDATUMKAL", "isMainEt": "", "refEt": "", "rollname": "BDATM"}, {"description": "Maximum Lot Size", "fieldKind": "Attribute", "hryCol": "BSTMAMKAL", "isMainEt": "", "refEt": "", "rollname": "SA_LOSBS"}, {"description": "Minimum Lot Size", "fieldKind": "Attribute", "hryCol": "BSTMIMKAL", "isMainEt": "", "refEt": "", "rollname": "SA_LOSVN"}, {"description": "Apportionment Struct", "fieldKind": "Attribute", "hryCol": "CSPMKALBA", "isMainEt": "", "refEt": "CSPLTMKAL", "rollname": "CSPLIT"}, {"description": "Issue stor. location", "fieldKind": "Attribute", "hryCol": "ELPMKALBA", "isMainEt": "", "refEt": "ELPROMKAL", "rollname": "SA_ELPRO"}, {"description": "Other header mat.", "fieldKind": "Attribute", "hryCol": "MATKOMKAL", "isMainEt": "", "refEt": "", "rollname": "MKAL_MATKO"}, {"description": "Production Line", "fieldKind": "Attribute", "hryCol": "MDV01MKAL", "isMainEt": "", "refEt": "", "rollname": "SA_LINE1"}, {"description": "Planning ID", "fieldKind": "Attribute", "hryCol": "MDVMKALBA", "isMainEt": "", "refEt": "MDV02MKAL", "rollname": "MDV"}, {"description": "Production Version Lock", "fieldKind": "Attribute", "hryCol": "MKSPMKAL", "isMainEt": "", "refEt": "", "rollname": "MKSP"}, {"description": "Group Counter rate-based planning", "fieldKind": "Attribute", "hryCol": "PLNALG", "isMainEt": "", "refEt": "PLNAL_G", "rollname": "MDG_BS_MAT_PLNAL_G"}, {"description": "Group Counter rough-cut planning", "fieldKind": "Attribute", "hryCol": "PLNALM", "isMainEt": "", "refEt": "PLNAL_M", "rollname": "MDG_BS_MAT_PLNAL_M"}, {"description": "Group Counter detailed planning", "fieldKind": "Attribute", "hryCol": "PLNALP", "isMainEt": "", "refEt": "PLNAL_P", "rollname": "MDG_BS_MAT_PLNAL_P"}, {"description": "Key for Task List Group rate-based plg", "fieldKind": "Attribute", "hryCol": "PLNNRG", "isMainEt": "", "refEt": "PLNNR_G", "rollname": "MDG_BS_MAT_PLNNR_G"}, {"description": "Key for Task List Group rough-cut plg", "fieldKind": "Attribute", "hryCol": "PLNNRM", "isMainEt": "", "refEt": "PLNNR_M", "rollname": "MDG_BS_MAT_PLNNR_M"}, {"description": "Key for Task List Group detailed plg", "fieldKind": "Attribute", "hryCol": "PLNNRP", "isMainEt": "", "refEt": "PLNNR_P", "rollname": "MDG_BS_MAT_PLNNR_P"}, {"description": "Task List Type rate-based  planning", "fieldKind": "Attribute", "hryCol": "PLNTYG", "isMainEt": "", "refEt": "PLNTY_G", "rollname": "MDG_BS_MAT_PLNTY_G"}, {"description": "Task List Type rough-cut  planning", "fieldKind": "Attribute", "hryCol": "PLNTYM", "isMainEt": "", "refEt": "PLNTY_M", "rollname": "MDG_BS_MAT_PLNTY_M"}, {"description": "Task List Type detailed  planning", "fieldKind": "Attribute", "hryCol": "PLNTYP", "isMainEt": "", "refEt": "PLNTY_P", "rollname": "MDG_BS_MAT_PLNTY_P"}, {"description": "Date of last check of prod.version", "fieldKind": "Attribute", "hryCol": "PRDATMKAL", "isMainEt": "", "refEt": "", "rollname": "MKPRDAT"}, {"description": "Default Supply Area", "fieldKind": "Attribute", "hryCol": "PRVMKALBA", "isMainEt": "", "refEt": "PRVBEMKAL", "rollname": "SA_PRVBE"}, {"description": "REM Allowed", "fieldKind": "Attribute", "hryCol": "SERKZMKAL", "isMainEt": "", "refEt": "", "rollname": "SA_VERSI"}, {"description": "Alternative BOM", "fieldKind": "Attribute", "hryCol": "STAMKALBA", "isMainEt": "", "refEt": "STLALMKAL", "rollname": "STALT"}, {"description": "BOM Usage", "fieldKind": "Attribute", "hryCol": "STLANMKAL", "isMainEt": "", "refEt": "", "rollname": "STLAN"}, {"description": "Production Version Text", "fieldKind": "Attribute", "hryCol": "TEXT1MKAL", "isMainEt": "", "refEt": "", "rollname": "VERS_TEXT"}, {"description": "OB Reference Materl", "fieldKind": "Attribute", "hryCol": "UCMATMKAL", "isMainEt": "", "refEt": "", "rollname": "VBOB_OB_RFMAT"}, {"description": "Distribution Key", "fieldKind": "Attribute", "hryCol": "VERMKALBA", "isMainEt": "", "refEt": "VERTOMKAL", "rollname": "SA_VERTO"}], "isFolder": "X", "relation": "MM"}, {"name": "MATERIAL", "property": [{"description": "Material", "fieldKind": "Entity Type Itself", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Document change no.", "fieldKind": "Attribute", "hryCol": "AESZN", "isMainEt": "", "refEt": "", "rollname": "AESZN"}, {"description": "Authorization Group", "fieldKind": "Attribute", "hryCol": "BEGRU", "isMainEt": "", "refEt": "", "rollname": "BEGRU"}, {"description": "Old material number", "fieldKind": "Attribute", "hryCol": "BISMT", "isMainEt": "", "refEt": "", "rollname": "BISMT"}, {"description": "Number of sheets", "fieldKind": "Attribute", "hryCol": "BLANZ", "isMainEt": "", "refEt": "", "rollname": "BLANZ"}, {"description": "Page number", "fieldKind": "Attribute", "hryCol": "BLATT", "isMainEt": "", "refEt": "", "rollname": "BLATT"}, {"description": "<PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "BREITMARA", "isMainEt": "", "refEt": "", "rollname": "BREIT"}, {"description": "Gross weight", "fieldKind": "Attribute", "hryCol": "BRGEWMARA", "isMainEt": "", "refEt": "", "rollname": "BRGEW"}, {"description": "CAD Indicator", "fieldKind": "Attribute", "hryCol": "CADKZ", "isMainEt": "", "refEt": "", "rollname": "CADKZ"}, {"description": "EAN/UPC", "fieldKind": "Attribute", "hryCol": "EAN_MARA", "isMainEt": "", "refEt": "EAN", "rollname": "EAN11"}, {"description": "Ext. Material Group", "fieldKind": "Attribute", "hryCol": "EXTWG", "isMainEt": "", "refEt": "", "rollname": "EXTWG"}, {"description": "Prod./insp. memo", "fieldKind": "Attribute", "hryCol": "FERTH", "isMainEt": "", "refEt": "", "rollname": "FERTH"}, {"description": "Page format", "fieldKind": "Attribute", "hryCol": "FORMT", "isMainEt": "", "refEt": "", "rollname": "FORMT"}, {"description": "Weight unit", "fieldKind": "Attribute", "hryCol": "GEWEI_MAT", "isMainEt": "", "refEt": "", "rollname": "GEWEI"}, {"description": "Size/dimensions", "fieldKind": "Attribute", "hryCol": "GROES", "isMainEt": "", "refEt": "", "rollname": "GROES"}, {"description": "EAN Variant", "fieldKind": "Attribute", "hryCol": "GTIN_VAR1", "isMainEt": "", "refEt": "", "rollname": "GTIN_VARIANT"}, {"description": "Relevant for HS", "fieldKind": "Attribute", "hryCol": "HAZMAT", "isMainEt": "", "refEt": "", "rollname": "CIFHAZMAT"}, {"description": "Height", "fieldKind": "Attribute", "hryCol": "HOEHEMARA", "isMainEt": "", "refEt": "", "rollname": "HOEHE"}, {"description": "Highly viscous", "fieldKind": "Attribute", "hryCol": "IHIVI", "isMainEt": "", "refEt": "", "rollname": "ADGE_IHIVI"}, {"description": "In bulk/liquid", "fieldKind": "Attribute", "hryCol": "ILOOS", "isMainEt": "", "refEt": "", "rollname": "ADGE_ILOOS"}, {"description": "Product allocation", "fieldKind": "Attribute", "hryCol": "KOSCH", "isMainEt": "", "refEt": "", "rollname": "KOSCH"}, {"description": "Assign effect. vals", "fieldKind": "Attribute", "hryCol": "KZEFF", "isMainEt": "", "refEt": "", "rollname": "CC_MTEFF"}, {"description": "Material is configurable", "fieldKind": "Attribute", "hryCol": "KZKFG", "isMainEt": "", "refEt": "", "rollname": "KZKFG"}, {"description": "Environmentally rlvt", "fieldKind": "Attribute", "hryCol": "KZUMW", "isMainEt": "", "refEt": "", "rollname": "KZUMW"}, {"description": "Lab/Office", "fieldKind": "Attribute", "hryCol": "LABOR", "isMainEt": "", "refEt": "", "rollname": "LABOR"}, {"description": "Length", "fieldKind": "Attribute", "hryCol": "LAENGMARA", "isMainEt": "", "refEt": "", "rollname": "LAENG"}, {"description": "DF at client level", "fieldKind": "Attribute", "hryCol": "LVORM_MAT", "isMainEt": "", "refEt": "", "rollname": "LVOMA"}, {"description": "Matl Grp Pack.Matls", "fieldKind": "Attribute", "hryCol": "MAGRVMARA", "isMainEt": "", "refEt": "", "rollname": "MAGRV"}, {"description": "Material is locked", "fieldKind": "Attribute", "hryCol": "MATFI", "isMainEt": "", "refEt": "", "rollname": "MATFI"}, {"description": "Material Group", "fieldKind": "Attribute", "hryCol": "MATKL", "isMainEt": "", "refEt": "", "rollname": "MATKL"}, {"description": "Material Number", "fieldKind": "Attribute", "hryCol": "MATNR_EXT", "isMainEt": "", "refEt": "", "rollname": "MATNR_EXT"}, {"description": "Industry Sector", "fieldKind": "Attribute", "hryCol": "MBRSH", "isMainEt": "", "refEt": "", "rollname": "MBRSH"}, {"description": "Unit of Dimension", "fieldKind": "Attribute", "hryCol": "MEABMMARA", "isMainEt": "", "refEt": "", "rollname": "MEABM"}, {"description": "Medium", "fieldKind": "Attribute", "hryCol": "MEDIUM", "isMainEt": "", "refEt": "", "rollname": "MEDIU"}, {"description": "Base Unit of Measure", "fieldKind": "Attribute", "hryCol": "MEINS", "isMainEt": "", "refEt": "", "rollname": "MEINS"}, {"description": "X-Plant Matl Status", "fieldKind": "Attribute", "hryCol": "MSTAE", "isMainEt": "", "refEt": "", "rollname": "MSTAE"}, {"description": "<PERSON>id from", "fieldKind": "Attribute", "hryCol": "MSTDE", "isMainEt": "", "refEt": "", "rollname": "MSTDE"}, {"description": "Material type", "fieldKind": "Attribute", "hryCol": "MTART", "isMainEt": "", "refEt": "", "rollname": "MTART"}, {"description": "Gen. item cat. grp", "fieldKind": "Attribute", "hryCol": "MTPOSMARA", "isMainEt": "", "refEt": "", "rollname": "MTPOS_MARA"}, {"description": "Industry Std Desc.", "fieldKind": "Attribute", "hryCol": "NORMT", "isMainEt": "", "refEt": "", "rollname": "NORMT"}, {"description": "Qual.f.<PERSON>sDis.", "fieldKind": "Attribute", "hryCol": "NRFHG", "isMainEt": "", "refEt": "", "rollname": "NRFHG"}, {"description": "Net weight", "fieldKind": "Attribute", "hryCol": "NTGEW", "isMainEt": "", "refEt": "", "rollname": "NTGEW"}, {"description": "EAN category", "fieldKind": "Attribute", "hryCol": "NUMTP1", "isMainEt": "", "refEt": "", "rollname": "NUMTP"}, {"description": "Product hierarchy", "fieldKind": "Attribute", "hryCol": "PRDHA", "isMainEt": "", "refEt": "", "rollname": "PRODH_D"}, {"description": "DG indicator profile", "fieldKind": "Attribute", "hryCol": "PROFL", "isMainEt": "", "refEt": "", "rollname": "ADGE_PROFL"}, {"description": "Reference matl for packing", "fieldKind": "Attribute", "hryCol": "RMATP", "isMainEt": "", "refEt": "", "rollname": "PL_RMATP"}, {"description": "Cross-plant CM", "fieldKind": "Attribute", "hryCol": "SATNR", "isMainEt": "", "refEt": "", "rollname": "SATNR"}, {"description": "Serial No. Profile", "fieldKind": "Attribute", "hryCol": "SERIAL", "isMainEt": "", "refEt": "", "rollname": "MDG_CIFSERIAL"}, {"description": "Serialization level", "fieldKind": "Attribute", "hryCol": "SERLV", "isMainEt": "", "refEt": "", "rollname": "SERLV"}, {"description": "Division", "fieldKind": "Attribute", "hryCol": "SPART", "isMainEt": "", "refEt": "", "rollname": "SPART"}, {"description": "Description (medium text)", "fieldKind": "Attribute", "hryCol": "TXTMI", "isMainEt": "", "refEt": "", "rollname": "USMD_TXTMI"}, {"description": "Volume unit", "fieldKind": "Attribute", "hryCol": "VOLEHMARA", "isMainEt": "", "refEt": "", "rollname": "VOLEH"}, {"description": "Volume", "fieldKind": "Attribute", "hryCol": "VOLUMMARA", "isMainEt": "", "refEt": "", "rollname": "VOLUM"}, {"description": "Basic material", "fieldKind": "Attribute", "hryCol": "WRKST", "isMainEt": "", "refEt": "", "rollname": "WRKST"}, {"description": "Batch management", "fieldKind": "Attribute", "hryCol": "XCHPFMARA", "isMainEt": "", "refEt": "", "rollname": "XCHPF"}, {"description": "Appr.<PERSON><PERSON> Recd Req.", "fieldKind": "Attribute", "hryCol": "XGCHPMARA", "isMainEt": "", "refEt": "", "rollname": "XGCHP"}, {"description": "Document type", "fieldKind": "Attribute", "hryCol": "ZEIAR", "isMainEt": "", "refEt": "", "rollname": "DZEIAR"}, {"description": "Page format", "fieldKind": "Attribute", "hryCol": "ZEIFO", "isMainEt": "", "refEt": "", "rollname": "DZEIFO"}, {"description": "Document", "fieldKind": "Attribute", "hryCol": "ZEINR", "isMainEt": "", "refEt": "", "rollname": "DZEINR"}, {"description": "Doc. Version", "fieldKind": "Attribute", "hryCol": "ZEIVR", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Characteristic Valuation (Classification)", "fieldKind": "Attribute", "hryCol": "VALUATION", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Units of Measure for Material", "fieldKind": "Attribute", "hryCol": "UNITOFMSR", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material Sales Text", "fieldKind": "Attribute", "hryCol": "SALESTXT", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Parameters for Inspection Type", "fieldKind": "Attribute", "hryCol": "QMATBASIC", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material Quality Inspection Text", "fieldKind": "Attribute", "hryCol": "QINSPTXT", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material Purchasing Text", "fieldKind": "Attribute", "hryCol": "PURCHTXT", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Sales Data", "fieldKind": "Attribute", "hryCol": "MVKESALES", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Sales Grouping", "fieldKind": "Attribute", "hryCol": "MVKEGRPNG", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material MRP Text", "fieldKind": "Attribute", "hryCol": "MRPTXT", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material Data for Product Group", "fieldKind": "Attribute", "hryCol": "MPGDPRODG", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material Storage Type Data", "fieldKind": "Attribute", "hryCol": "MLGTSTOR", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material Warehouse Management Data", "fieldKind": "Attribute", "hryCol": "MLGNSTOR", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Tax Classification for Sales", "fieldKind": "Attribute", "hryCol": "MLANSALES", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Tax Classification for Purchasing", "fieldKind": "Attribute", "hryCol": "MLANPURCH", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "International Article Numbers (EANs) for Material", "fieldKind": "Attribute", "hryCol": "MEAN_GTIN", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "MRP Area Basic Data", "fieldKind": "Attribute", "hryCol": "MDMABASIC", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material Valuation Data", "fieldKind": "Attribute", "hryCol": "MBEWVALUA", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material Costing Data", "fieldKind": "Attribute", "hryCol": "MBEWCSTNG", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Storage Location General Data for Material", "fieldKind": "Attribute", "hryCol": "MARDSTOR", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Storage Location MRP Data for Material", "fieldKind": "Attribute", "hryCol": "MARDMRP", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data Work Scheduling", "fieldKind": "Attribute", "hryCol": "MARCWRKSD", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data Storage", "fieldKind": "Attribute", "hryCol": "MARCSTORE", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data Sales", "fieldKind": "Attribute", "hryCol": "MARCSALES", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data Quality Management", "fieldKind": "Attribute", "hryCol": "MARCQTMNG", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data Purchasing", "fieldKind": "Attribute", "hryCol": "MARCPURCH", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data MRP Stock Planning (View Procurement)", "fieldKind": "Attribute", "hryCol": "MARCMRPSP", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data MRP Production Planning (View Material)", "fieldKind": "Attribute", "hryCol": "MARCMRPPP", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data MRP Misc (View Manufacturing)", "fieldKind": "Attribute", "hryCol": "MARCMRPMI", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data MRP Lot Size (View Lot Size)", "fieldKind": "Attribute", "hryCol": "MARCMRPLS", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data MRP Forecast (View Planning)", "fieldKind": "Attribute", "hryCol": "MARCMRPFC", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data Forecast Parameters", "fieldKind": "Attribute", "hryCol": "MARCFRPAR", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data Foreign Trade", "fieldKind": "Attribute", "hryCol": "MARCFRGTR", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data Forecasting", "fieldKind": "Attribute", "hryCol": "MARCFRCST", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data Costing", "fieldKind": "Attribute", "hryCol": "MARCCSTNG", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data Basic Data", "fieldKind": "Attribute", "hryCol": "MARCBASIC", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Plant Data ATP", "fieldKind": "Attribute", "hryCol": "MARCATP", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material Storage Data", "fieldKind": "Attribute", "hryCol": "MARASTOR", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material Service Parts Management", "fieldKind": "Attribute", "hryCol": "MARASPM", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material Sales Data", "fieldKind": "Attribute", "hryCol": "MARASALES", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material Quality Data", "fieldKind": "Attribute", "hryCol": "MARAQTMNG", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Material Purchasing Data", "fieldKind": "Attribute", "hryCol": "MARAPURCH", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Internal Comment", "fieldKind": "Attribute", "hryCol": "INTCMNT", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Class Assignment (Classification)", "fieldKind": "Attribute", "hryCol": "CLASSASGN", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}, {"description": "Basic Data Text", "fieldKind": "Attribute", "hryCol": "BSCDATTXT", "isMainEt": "", "refEt": "", "rollname": "DZEIVR"}], "isFolder": "X", "relation": "MM"}, {"name": "VALUATION", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Change Number", "fieldKind": "Qualifying Entity Type", "hryCol": "CHANGENO", "isMainEt": "", "refEt": "", "rollname": "AENNR"}, {"description": "Characteristic ID", "fieldKind": "Qualifying Entity Type", "hryCol": "CHARID", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_CLF_CHARID"}, {"description": "Class Type", "fieldKind": "Qualifying Entity Type", "hryCol": "CLASSTYPE", "isMainEt": "", "refEt": "", "rollname": "KLASSENART"}, {"description": "Int. counter", "fieldKind": "Qualifying Entity Type", "hryCol": "ECOCNTR", "isMainEt": "", "refEt": "", "rollname": "ADZHL"}, {"description": "UUID", "fieldKind": "Qualifying Entity Type", "hryCol": "GUID", "isMainEt": "", "refEt": "", "rollname": "SYSUUID_C"}, {"description": "Counter", "fieldKind": "Qualifying Entity Type", "hryCol": "VALCNT", "isMainEt": "", "refEt": "", "rollname": "WZAEHL"}, {"description": "Author", "fieldKind": "Attribute", "hryCol": "ATAUT", "isMainEt": "", "refEt": "", "rollname": "ATAUT"}, {"description": "Alternative unit", "fieldKind": "Attribute", "hryCol": "ATAW1", "isMainEt": "", "refEt": "", "rollname": "ATAWE"}, {"description": "Alternative unit", "fieldKind": "Attribute", "hryCol": "ATAWE", "isMainEt": "", "refEt": "", "rollname": "ATAWE"}, {"description": "Code", "fieldKind": "Attribute", "hryCol": "ATCOD", "isMainEt": "", "refEt": "", "rollname": "ATCOD"}, {"description": "Value to", "fieldKind": "Attribute", "hryCol": "ATFLB", "isMainEt": "", "refEt": "", "rollname": "ATFLB"}, {"description": "Value from", "fieldKind": "Attribute", "hryCol": "ATFLV", "isMainEt": "", "refEt": "", "rollname": "ATFLV"}, {"description": "Internal char no.", "fieldKind": "Attribute", "hryCol": "ATIMB", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_CLF_ATIMB"}, {"description": "Position", "fieldKind": "Attribute", "hryCol": "ATSRT", "isMainEt": "", "refEt": "", "rollname": "ATSRT"}, {"description": "Comp. type", "fieldKind": "Attribute", "hryCol": "ATVGLART", "isMainEt": "", "refEt": "", "rollname": "ATVGLART"}, {"description": "Characteristic Value", "fieldKind": "Attribute", "hryCol": "ATWRT", "isMainEt": "", "refEt": "", "rollname": "ATWRT"}, {"description": "Instance counter", "fieldKind": "Attribute", "hryCol": "ATZIS", "isMainEt": "", "refEt": "", "rollname": "ATZIS"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "DATUV_VAL", "isMainEt": "", "refEt": "", "rollname": "DATUV"}, {"description": "Deletion Indicator", "fieldKind": "Attribute", "hryCol": "LKENZ_VAL", "isMainEt": "", "refEt": "", "rollname": "LKENZ"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "UNITOFMSR", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Display Unit/Measure", "fieldKind": "Qualifying Entity Type", "hryCol": "QTEUNIT", "isMainEt": "", "refEt": "", "rollname": "MEINH"}, {"description": "<PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "BREIT", "isMainEt": "", "refEt": "", "rollname": "BREIT"}, {"description": "Gross weight", "fieldKind": "Attribute", "hryCol": "BRGEW", "isMainEt": "", "refEt": "", "rollname": "BRGEW"}, {"description": "Capacity Usage", "fieldKind": "Attribute", "hryCol": "CAPAUSE", "isMainEt": "", "refEt": "", "rollname": "CIFCAPAUSE"}, {"description": "EAN/UPC", "fieldKind": "Attribute", "hryCol": "EAN_MARM", "isMainEt": "", "refEt": "EAN", "rollname": "EAN11"}, {"description": "Weight unit", "fieldKind": "Attribute", "hryCol": "GEWEI", "isMainEt": "", "refEt": "", "rollname": "GEWEI"}, {"description": "EAN Variant", "fieldKind": "Attribute", "hryCol": "GTIN_VAR2", "isMainEt": "", "refEt": "", "rollname": "GTIN_VARIANT"}, {"description": "Height", "fieldKind": "Attribute", "hryCol": "HOEHE", "isMainEt": "", "refEt": "", "rollname": "HOEHE"}, {"description": "Length", "fieldKind": "Attribute", "hryCol": "LAENG", "isMainEt": "", "refEt": "", "rollname": "LAENG"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "MAX_STACK", "isMainEt": "", "refEt": "", "rollname": "CIFMXSTACK"}, {"description": "Unit of Dimension", "fieldKind": "Attribute", "hryCol": "MEABM", "isMainEt": "", "refEt": "", "rollname": "MEABM"}, {"description": "Rem.Vol.After Nestng", "fieldKind": "Attribute", "hryCol": "NEST_FTR", "isMainEt": "", "refEt": "", "rollname": "CIFNESTFTR"}, {"description": "EAN category", "fieldKind": "Attribute", "hryCol": "NUMTP2", "isMainEt": "", "refEt": "", "rollname": "NUMTP"}, {"description": "UoM Category", "fieldKind": "Attribute", "hryCol": "TY2TQ", "isMainEt": "", "refEt": "", "rollname": "DE_EWMTY2TQ"}, {"description": "Denominator", "fieldKind": "Attribute", "hryCol": "UMREN", "isMainEt": "", "refEt": "", "rollname": "UMREN"}, {"description": "Counter", "fieldKind": "Attribute", "hryCol": "UMREZ", "isMainEt": "", "refEt": "", "rollname": "UMREZ"}, {"description": "Volume unit", "fieldKind": "Attribute", "hryCol": "VOLEH", "isMainEt": "", "refEt": "", "rollname": "VOLEH"}, {"description": "Volume", "fieldKind": "Attribute", "hryCol": "VOLUM", "isMainEt": "", "refEt": "", "rollname": "VOLUM"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "SALESTXT", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "LANGUCODE", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Sales Organization", "fieldKind": "Qualifying Entity Type", "hryCol": "VKORG", "isMainEt": "", "refEt": "", "rollname": "VKORG"}, {"description": "Distribution Channel", "fieldKind": "Qualifying Entity Type", "hryCol": "VTWEG", "isMainEt": "", "refEt": "", "rollname": "VTWEG"}, {"description": "Sales Text", "fieldKind": "Attribute", "hryCol": "TXTSALES", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_MAT_SALESSTRG"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "QMATBASIC", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Inspection Type", "fieldKind": "Qualifying Entity Type", "hryCol": "ART", "isMainEt": "", "refEt": "", "rollname": "QPART"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Inspection for HU", "fieldKind": "Attribute", "hryCol": "AFR", "isMainEt": "", "refEt": "", "rollname": "QAFR"}, {"description": "InspType-Matl active", "fieldKind": "Attribute", "hryCol": "AKTIV", "isMainEt": "", "refEt": "", "rollname": "QMATAKTIV"}, {"description": "PreferredInsTyp", "fieldKind": "Attribute", "hryCol": "APA", "isMainEt": "", "refEt": "", "rollname": "QAPA"}, {"description": "Automatic assignment", "fieldKind": "Attribute", "hryCol": "APP", "isMainEt": "", "refEt": "", "rollname": "QAPP_D"}, {"description": "QM Order", "fieldKind": "Attribute", "hryCol": "AUFNR_CO", "isMainEt": "", "refEt": "", "rollname": "QAUFNR_CO"}, {"description": "Automatic UD", "fieldKind": "Attribute", "hryCol": "AVE", "isMainEt": "", "refEt": "", "rollname": "QAUTO_VE"}, {"description": "Control insLot", "fieldKind": "Attribute", "hryCol": "CHG", "isMainEt": "", "refEt": "", "rollname": "Q1WELOS"}, {"description": "Insp. by configuratn", "fieldKind": "Attribute", "hryCol": "CONF", "isMainEt": "", "refEt": "", "rollname": "QCONF"}, {"description": "Skips allowed", "fieldKind": "Attribute", "hryCol": "DYN", "isMainEt": "", "refEt": "", "rollname": "QDYN"}, {"description": "Modification Rule", "fieldKind": "Attribute", "hryCol": "DYNREGEL", "isMainEt": "", "refEt": "", "rollname": "QDYNREGEL"}, {"description": "Serial numbers poss.", "fieldKind": "Attribute", "hryCol": "EIN", "isMainEt": "", "refEt": "", "rollname": "QEINZ"}, {"description": "100% inspection", "fieldKind": "Attribute", "hryCol": "HPZ", "isMainEt": "", "refEt": "", "rollname": "QHPZ"}, {"description": "Post to insp. stock", "fieldKind": "Attribute", "hryCol": "INSMK_Q", "isMainEt": "", "refEt": "", "rollname": "QINSMK"}, {"description": "Individual QM order", "fieldKind": "Attribute", "hryCol": "KZPRFKOST", "isMainEt": "", "refEt": "", "rollname": "QKZPRFKOST"}, {"description": "Check Chars", "fieldKind": "Attribute", "hryCol": "MER", "isMainEt": "", "refEt": "", "rollname": "QMERK"}, {"description": "Manual sample entry", "fieldKind": "Attribute", "hryCol": "MPB", "isMainEt": "", "refEt": "", "rollname": "QMPB"}, {"description": "Avg. insp. duration", "fieldKind": "Attribute", "hryCol": "MPDAU", "isMainEt": "", "refEt": "", "rollname": "QMPDAU"}, {"description": "Manual sample calc.", "fieldKind": "Attribute", "hryCol": "MST", "isMainEt": "", "refEt": "", "rollname": "QMST"}, {"description": "Multiple Specs", "fieldKind": "Attribute", "hryCol": "MS_FLAG", "isMainEt": "", "refEt": "", "rollname": "Q_MS_FLG"}, {"description": "Insp. with task list", "fieldKind": "Attribute", "hryCol": "PPL", "isMainEt": "", "refEt": "", "rollname": "QPPL"}, {"description": "Q-Score Procedure", "fieldKind": "Attribute", "hryCol": "QKZVERF", "isMainEt": "", "refEt": "", "rollname": "QKZVERF"}, {"description": "Allowed scrap share", "fieldKind": "Attribute", "hryCol": "QPMAT", "isMainEt": "", "refEt": "", "rollname": "QAUSSCHUSS"}, {"description": "Insp. with Mat Spec.", "fieldKind": "Attribute", "hryCol": "SPEZUEBER", "isMainEt": "", "refEt": "", "rollname": "QSPEZUEBER"}, {"description": "Insp. percentage", "fieldKind": "Attribute", "hryCol": "SPROZ", "isMainEt": "", "refEt": "", "rollname": "QSPROZ"}, {"description": "Sampling Procedure", "fieldKind": "Attribute", "hryCol": "STICHPRVE", "isMainEt": "", "refEt": "", "rollname": "QSTPRVER"}, {"description": "Inspect by batch", "fieldKind": "Attribute", "hryCol": "TLS", "isMainEt": "", "refEt": "", "rollname": "QCHARG"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "QINSPTXT", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "LANGUCODE", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Quality Text", "fieldKind": "Attribute", "hryCol": "TXTQINSP", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_MAT_QINSPSTRG"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "PURCHTXT", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "LANGUCODE", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Purchasing Text", "fieldKind": "Attribute", "hryCol": "TXTPURCH", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_MAT_PURCHSTRG"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MVKESALES", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Sales Organization", "fieldKind": "Qualifying Entity Type", "hryCol": "VKORG", "isMainEt": "", "refEt": "", "rollname": "VKORG"}, {"description": "Distribution Channel", "fieldKind": "Qualifying Entity Type", "hryCol": "VTWEG", "isMainEt": "", "refEt": "", "rollname": "VTWEG"}, {"description": "Minimum order qty", "fieldKind": "Attribute", "hryCol": "AUMNG", "isMainEt": "", "refEt": "", "rollname": "MINAU"}, {"description": "Min. MtO quantity", "fieldKind": "Attribute", "hryCol": "EFMNG", "isMainEt": "", "refEt": "", "rollname": "MINEF"}, {"description": "Minimum Delivery Qty", "fieldKind": "Attribute", "hryCol": "LFMNG", "isMainEt": "", "refEt": "", "rollname": "MINLF"}, {"description": "Unit of Measure Grp", "fieldKind": "Attribute", "hryCol": "MEGSALES", "isMainEt": "", "refEt": "MEGRU", "rollname": "MEGRU"}, {"description": "DF distr. chain lvl", "fieldKind": "Attribute", "hryCol": "MVKELVORM", "isMainEt": "", "refEt": "", "rollname": "LVOVK"}, {"description": "Product attribute 1", "fieldKind": "Attribute", "hryCol": "PRAT1", "isMainEt": "", "refEt": "", "rollname": "PRAT1"}, {"description": "Product attribute 2", "fieldKind": "Attribute", "hryCol": "PRAT2", "isMainEt": "", "refEt": "", "rollname": "PRAT2"}, {"description": "Product attribute 3", "fieldKind": "Attribute", "hryCol": "PRAT3", "isMainEt": "", "refEt": "", "rollname": "PRAT3"}, {"description": "Product attribute 4", "fieldKind": "Attribute", "hryCol": "PRAT4", "isMainEt": "", "refEt": "", "rollname": "PRAT4"}, {"description": "Product attribute 5", "fieldKind": "Attribute", "hryCol": "PRAT5", "isMainEt": "", "refEt": "", "rollname": "PRAT5"}, {"description": "Product attribute 6", "fieldKind": "Attribute", "hryCol": "PRAT6", "isMainEt": "", "refEt": "", "rollname": "PRAT6"}, {"description": "Product attribute 7", "fieldKind": "Attribute", "hryCol": "PRAT7", "isMainEt": "", "refEt": "", "rollname": "PRAT7"}, {"description": "Product attribute 8", "fieldKind": "Attribute", "hryCol": "PRAT8", "isMainEt": "", "refEt": "", "rollname": "PRAT8"}, {"description": "Product attribute 9", "fieldKind": "Attribute", "hryCol": "PRAT9", "isMainEt": "", "refEt": "", "rollname": "PRAT9"}, {"description": "Product attribute 10", "fieldKind": "Attribute", "hryCol": "PRATA", "isMainEt": "", "refEt": "", "rollname": "PRATA"}, {"description": "Rounding Profile", "fieldKind": "Attribute", "hryCol": "RDPSALES", "isMainEt": "", "refEt": "RDPRFMVKE", "rollname": "RDPRF"}, {"description": "Unit of measure", "fieldKind": "Attribute", "hryCol": "SCHME", "isMainEt": "", "refEt": "", "rollname": "SCHME"}, {"description": "<PERSON><PERSON> for Split<PERSON> <PERSON>uo<PERSON>", "fieldKind": "Attribute", "hryCol": "SCMNG", "isMainEt": "", "refEt": "", "rollname": "SCMNG"}, {"description": "Cash Discount", "fieldKind": "Attribute", "hryCol": "SKTOF", "isMainEt": "", "refEt": "", "rollname": "SKTOF"}, {"description": "Sales unit not var.", "fieldKind": "Attribute", "hryCol": "VAVME", "isMainEt": "", "refEt": "", "rollname": "VAVME"}, {"description": "DChain-spec. status", "fieldKind": "Attribute", "hryCol": "VMSTA", "isMainEt": "", "refEt": "", "rollname": "VMSTA"}, {"description": "<PERSON>id from", "fieldKind": "Attribute", "hryCol": "VMSTD", "isMainEt": "", "refEt": "", "rollname": "VMSTD"}, {"description": "Sales unit", "fieldKind": "Attribute", "hryCol": "VRKME", "isMainEt": "", "refEt": "", "rollname": "VRKME"}, {"description": "Delivering Plant", "fieldKind": "Attribute", "hryCol": "WRKSALES", "isMainEt": "", "refEt": "DWERK", "rollname": "DWERK_EXT"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MVKEGRPNG", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Sales Organization", "fieldKind": "Qualifying Entity Type", "hryCol": "VKORG", "isMainEt": "", "refEt": "", "rollname": "VKORG"}, {"description": "Distribution Channel", "fieldKind": "Qualifying Entity Type", "hryCol": "VTWEG", "isMainEt": "", "refEt": "", "rollname": "VTWEG"}, {"description": "Volume Rebate Group", "fieldKind": "Attribute", "hryCol": "BONUS", "isMainEt": "", "refEt": "", "rollname": "BONUS"}, {"description": "Material Price Grp", "fieldKind": "Attribute", "hryCol": "KONDM", "isMainEt": "", "refEt": "", "rollname": "KONDM"}, {"description": "Acct Assmt Grp Mat.", "fieldKind": "Attribute", "hryCol": "KTGRM", "isMainEt": "", "refEt": "", "rollname": "KTGRM"}, {"description": "Item category group", "fieldKind": "Attribute", "hryCol": "MTPOS", "isMainEt": "", "refEt": "", "rollname": "MTPOS"}, {"description": "Material Group 1", "fieldKind": "Attribute", "hryCol": "MVGR1", "isMainEt": "", "refEt": "", "rollname": "MVGR1"}, {"description": "Material Group 2", "fieldKind": "Attribute", "hryCol": "MVGR2", "isMainEt": "", "refEt": "", "rollname": "MVGR2"}, {"description": "Material Group 3", "fieldKind": "Attribute", "hryCol": "MVGR3", "isMainEt": "", "refEt": "", "rollname": "MVGR3"}, {"description": "Material Group 4", "fieldKind": "Attribute", "hryCol": "MVGR4", "isMainEt": "", "refEt": "", "rollname": "MVGR4"}, {"description": "Material Group 5", "fieldKind": "Attribute", "hryCol": "MVGR5", "isMainEt": "", "refEt": "", "rollname": "MVGR5"}, {"description": "Pricing Re<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "PMATN", "isMainEt": "", "refEt": "", "rollname": "PMATN"}, {"description": "Product hierarchy", "fieldKind": "Attribute", "hryCol": "PRODH", "isMainEt": "", "refEt": "", "rollname": "PRODH_D"}, {"description": "Commission Group", "fieldKind": "Attribute", "hryCol": "PROVG", "isMainEt": "", "refEt": "", "rollname": "PROVG"}, {"description": "Matl statistics grp", "fieldKind": "Attribute", "hryCol": "VERSG", "isMainEt": "", "refEt": "", "rollname": "STGMA"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MRPTXT", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Plant Text", "fieldKind": "Attribute", "hryCol": "TXTMRP", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_MAT_MRPSTRG"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MPGDPRODG", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Planning material", "fieldKind": "Attribute", "hryCol": "PRGPRODG", "isMainEt": "", "refEt": "PRGRP", "rollname": "VPMAT"}, {"description": "Plant", "fieldKind": "Attribute", "hryCol": "PRWPRODG", "isMainEt": "", "refEt": "PRWRK", "rollname": "WERKS_D"}, {"description": "Plng conv. factor", "fieldKind": "Attribute", "hryCol": "UMREF", "isMainEt": "", "refEt": "", "rollname": "VPREF"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MLGTSTOR", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Warehouse Number", "fieldKind": "Qualifying Entity Type", "hryCol": "LGNUM", "isMainEt": "", "refEt": "", "rollname": "LGNUM"}, {"description": "Storage Type", "fieldKind": "Qualifying Entity Type", "hryCol": "LGTYP", "isMainEt": "", "refEt": "", "rollname": "LGTYP"}, {"description": "Picking Area", "fieldKind": "Attribute", "hryCol": "KOBMLGTST", "isMainEt": "", "refEt": "KOBER", "rollname": "KOBER"}, {"description": "Storage Bin", "fieldKind": "Attribute", "hryCol": "LGPMLGTST", "isMainEt": "", "refEt": "LGPLA", "rollname": "LGPLA"}, {"description": "Maximum bin quantity", "fieldKind": "Attribute", "hryCol": "LPMAX", "isMainEt": "", "refEt": "", "rollname": "MLGT_LPMAX"}, {"description": "Minimum bin quantity", "fieldKind": "Attribute", "hryCol": "LPMIN", "isMainEt": "", "refEt": "", "rollname": "MLGT_LPMIN"}, {"description": "Del. flag: stge type", "fieldKind": "Attribute", "hryCol": "LVORMMLGT", "isMainEt": "", "refEt": "", "rollname": "LVOLT"}, {"description": "Control quantity", "fieldKind": "Attribute", "hryCol": "MAMNG", "isMainEt": "", "refEt": "", "rollname": "MLGT_MAMNG"}, {"description": "Replenishment qty", "fieldKind": "Attribute", "hryCol": "NSMNG", "isMainEt": "", "refEt": "", "rollname": "MLGT_NSMNG"}, {"description": "Rounding qty", "fieldKind": "Attribute", "hryCol": "RDMNG", "isMainEt": "", "refEt": "", "rollname": "MLGT_RDMNG"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MLGNSTOR", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Warehouse Number", "fieldKind": "Qualifying Entity Type", "hryCol": "LGNUM", "isMainEt": "", "refEt": "", "rollname": "LGNUM"}, {"description": "Cap.consumption unit", "fieldKind": "Attribute", "hryCol": "BEZME", "isMainEt": "", "refEt": "", "rollname": "MLGN_BEZME"}, {"description": "Bulk storage", "fieldKind": "Attribute", "hryCol": "BLOMLGNST", "isMainEt": "", "refEt": "BLOCK", "rollname": "LVS_BLOCK2"}, {"description": "Special movement", "fieldKind": "Attribute", "hryCol": "BSSMLGNST", "isMainEt": "", "refEt": "BSSKZ", "rollname": "LVS_BSSKZ"}, {"description": "Message to inv. mgmt", "fieldKind": "Attribute", "hryCol": "KZMBF", "isMainEt": "", "refEt": "", "rollname": "KZMBF"}, {"description": "Allow addn to stock", "fieldKind": "Attribute", "hryCol": "KZZUL", "isMainEt": "", "refEt": "", "rollname": "MLGN_KZZUL"}, {"description": "2-step picking", "fieldKind": "Attribute", "hryCol": "L2SKR", "isMainEt": "", "refEt": "", "rollname": "MLGN_L2SKR"}, {"description": "Storage unit type", "fieldKind": "Attribute", "hryCol": "LE1MLGNST", "isMainEt": "", "refEt": "LETY1", "rollname": "LVS_LETYP1"}, {"description": "Storage unit type", "fieldKind": "Attribute", "hryCol": "LE2MLGNST", "isMainEt": "", "refEt": "LETY2", "rollname": "LVS_LETYP2"}, {"description": "Storage unit type", "fieldKind": "Attribute", "hryCol": "LE3MLGNST", "isMainEt": "", "refEt": "LETY3", "rollname": "LVS_LETYP3"}, {"description": "Storage Section Ind.", "fieldKind": "Attribute", "hryCol": "LGBMLGNST", "isMainEt": "", "refEt": "LGBKZ", "rollname": "LVS_LGBKZ"}, {"description": "Unit of measure", "fieldKind": "Attribute", "hryCol": "LHME1", "isMainEt": "", "refEt": "", "rollname": "LHMEH1"}, {"description": "Unit of measure", "fieldKind": "Attribute", "hryCol": "LHME2", "isMainEt": "", "refEt": "", "rollname": "LHMEH2"}, {"description": "Unit of measure", "fieldKind": "Attribute", "hryCol": "LHME3", "isMainEt": "", "refEt": "", "rollname": "LHMEH3"}, {"description": "Loading equip. qty", "fieldKind": "Attribute", "hryCol": "LHMG1", "isMainEt": "", "refEt": "", "rollname": "LVS_LHMNG1"}, {"description": "Loading equip. qty", "fieldKind": "Attribute", "hryCol": "LHMG2", "isMainEt": "", "refEt": "", "rollname": "LVS_LHMNG2"}, {"description": "Loading equip. qty", "fieldKind": "Attribute", "hryCol": "LHMG3", "isMainEt": "", "refEt": "", "rollname": "LVS_LHMNG3"}, {"description": "Stock removal", "fieldKind": "Attribute", "hryCol": "LTAMLGNST", "isMainEt": "", "refEt": "LTKZA", "rollname": "MLGN_LTKZA"}, {"description": "Stock placement", "fieldKind": "Attribute", "hryCol": "LTEMLGNST", "isMainEt": "", "refEt": "LTKZE", "rollname": "MLGN_LTKZE"}, {"description": "Del.flag:warehse no.", "fieldKind": "Attribute", "hryCol": "LVORMMLGN", "isMainEt": "", "refEt": "", "rollname": "LVOLN"}, {"description": "WM unit", "fieldKind": "Attribute", "hryCol": "LVSME", "isMainEt": "", "refEt": "", "rollname": "LVS_LVSME"}, {"description": "Capacity usage", "fieldKind": "Attribute", "hryCol": "MKAPV", "isMainEt": "", "refEt": "", "rollname": "MLGN_MKAPV"}, {"description": "Picking storage type", "fieldKind": "Attribute", "hryCol": "PLKMLGNST", "isMainEt": "", "refEt": "PLKPT", "rollname": "MLGN_PLKPT"}, {"description": "Proposed <PERSON><PERSON><PERSON> from mat.", "fieldKind": "Attribute", "hryCol": "VOMEM", "isMainEt": "", "refEt": "", "rollname": "MLGN_VOMEM"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MLANSALES", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Country", "fieldKind": "Qualifying Entity Type", "hryCol": "ALAND", "isMainEt": "", "refEt": "", "rollname": "ALAND"}, {"description": "Tax category", "fieldKind": "Qualifying Entity Type", "hryCol": "TATYP", "isMainEt": "", "refEt": "", "rollname": "TATYP"}, {"description": "Tax classification", "fieldKind": "Attribute", "hryCol": "TAXSALTAX", "isMainEt": "", "refEt": "TAXKM", "rollname": "TAXKM"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MLANPURCH", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Country", "fieldKind": "Qualifying Entity Type", "hryCol": "ALAND", "isMainEt": "", "refEt": "", "rollname": "ALAND"}, {"description": "Tax ind. f. material", "fieldKind": "Attribute", "hryCol": "TAIPURTAX", "isMainEt": "", "refEt": "TAXIM", "rollname": "MDG_MAT_TAXIM1"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MEAN_GTIN", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "EAN/UPC", "fieldKind": "Qualifying Entity Type", "hryCol": "EAN", "isMainEt": "", "refEt": "", "rollname": "EAN11"}, {"description": "Display Unit/Measure", "fieldKind": "Qualifying Entity Type", "hryCol": "QTEUNIT", "isMainEt": "", "refEt": "", "rollname": "MEINH"}, {"description": "EAN category", "fieldKind": "Attribute", "hryCol": "EANTP_MEA", "isMainEt": "", "refEt": "", "rollname": "NUMTP"}, {"description": "Main EAN", "fieldKind": "Attribute", "hryCol": "HPEAN", "isMainEt": "", "refEt": "", "rollname": "HPEAN"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MDMABASIC", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "MRP Area", "fieldKind": "Qualifying Entity Type", "hryCol": "BERID", "isMainEt": "", "refEt": "", "rollname": "BERID"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "MRP dep.requirements", "fieldKind": "Attribute", "hryCol": "AHDISMDMA", "isMainEt": "", "refEt": "", "rollname": "AHDIS"}, {"description": "Relevant to APO", "fieldKind": "Attribute", "hryCol": "APOKZMDMA", "isMainEt": "", "refEt": "", "rollname": "APOKZ"}, {"description": "Assembly scrap (%)", "fieldKind": "Attribute", "hryCol": "AUSSSMDMA", "isMainEt": "", "refEt": "", "rollname": "AUSSS"}, {"description": "Fixed lot size", "fieldKind": "Attribute", "hryCol": "BSTFEMDMA", "isMainEt": "", "refEt": "", "rollname": "BSTFE"}, {"description": "Maximum Lot Size", "fieldKind": "Attribute", "hryCol": "BSTMAMDMA", "isMainEt": "", "refEt": "", "rollname": "BSTMA"}, {"description": "Minimum Lot Size", "fieldKind": "Attribute", "hryCol": "BSTMIMDMA", "isMainEt": "", "refEt": "", "rollname": "BSTMI"}, {"description": "Rounding value", "fieldKind": "Attribute", "hryCol": "BSTRFMDMA", "isMainEt": "", "refEt": "", "rollname": "BSTRF"}, {"description": "MRP Group", "fieldKind": "Attribute", "hryCol": "DGRMDMA", "isMainEt": "", "refEt": "DISGR", "rollname": "DISGR"}, {"description": "Lot Sizing Procedure", "fieldKind": "Attribute", "hryCol": "DISLSMDMA", "isMainEt": "", "refEt": "", "rollname": "DISLS"}, {"description": "MRP Controller", "fieldKind": "Attribute", "hryCol": "DISMDMA", "isMainEt": "", "refEt": "DISPO", "rollname": "DISPO"}, {"description": "MRP Type", "fieldKind": "Attribute", "hryCol": "DISMMMDMA", "isMainEt": "", "refEt": "", "rollname": "DISMM"}, {"description": "Safety stock", "fieldKind": "Attribute", "hryCol": "EISBEMDMA", "isMainEt": "", "refEt": "", "rollname": "EISBE"}, {"description": "Planning time fence", "fieldKind": "Attribute", "hryCol": "FXHORMDMA", "isMainEt": "", "refEt": "", "rollname": "FXHOR"}, {"description": "Storage Costs Code", "fieldKind": "Attribute", "hryCol": "LAGMDMA", "isMainEt": "", "refEt": "LAGPR", "rollname": "LAGPR"}, {"description": "Planning cycle", "fieldKind": "Attribute", "hryCol": "LFRMDMA", "isMainEt": "", "refEt": "LFRHY", "rollname": "LFRHY"}, {"description": "Storage loc. for EP", "fieldKind": "Attribute", "hryCol": "LGFMDMA", "isMainEt": "", "refEt": "LGFSB", "rollname": "LGFSB"}, {"description": "Prod. stor. location", "fieldKind": "Attribute", "hryCol": "LGPMDMA", "isMainEt": "", "refEt": "LGPRO", "rollname": "LGPRO"}, {"description": "Service level (%)", "fieldKind": "Attribute", "hryCol": "LGRADMDMA", "isMainEt": "", "refEt": "", "rollname": "LGRAD"}, {"description": "Deletion Indicator", "fieldKind": "Attribute", "hryCol": "LOEKZMDMA", "isMainEt": "", "refEt": "", "rollname": "LVORM"}, {"description": "LS-Independent Costs", "fieldKind": "Attribute", "hryCol": "LOSFXMDMA", "isMainEt": "", "refEt": "", "rollname": "LOSFX"}, {"description": "Maximum Stock Level", "fieldKind": "Attribute", "hryCol": "MABSTMDMA", "isMainEt": "", "refEt": "", "rollname": "MABST"}, {"description": "Reorder Point", "fieldKind": "Attribute", "hryCol": "MINBEMDMA", "isMainEt": "", "refEt": "", "rollname": "MINBE"}, {"description": "Planning Calendar", "fieldKind": "Attribute", "hryCol": "MRPMDMA", "isMainEt": "", "refEt": "MRPPP", "rollname": "MRPPP"}, {"description": "Planned Deliv. Time", "fieldKind": "Attribute", "hryCol": "PLIFZMDMA", "isMainEt": "", "refEt": "", "rollname": "PLIFZ"}, {"description": "Consider Planned Delivery Time", "fieldKind": "Attribute", "hryCol": "PLIFZX", "isMainEt": "", "refEt": "", "rollname": "MD_PLIFZ_DB"}, {"description": "Rounding Profile", "fieldKind": "Attribute", "hryCol": "RDPMDMA", "isMainEt": "", "refEt": "RDPRF", "rollname": "RDPRF"}, {"description": "Coverage profile", "fieldKind": "Attribute", "hryCol": "RWPMDMA", "isMainEt": "", "refEt": "RWPRO", "rollname": "RWPRO"}, {"description": "Safety time ind.", "fieldKind": "Attribute", "hryCol": "SHFLGMDMA", "isMainEt": "", "refEt": "", "rollname": "SHFLG"}, {"description": "STime period profile", "fieldKind": "Attribute", "hryCol": "SHPMDMA", "isMainEt": "", "refEt": "SHPRO", "rollname": "SHPRO"}, {"description": "Safety time/act.cov.", "fieldKind": "Attribute", "hryCol": "SHZETMDMA", "isMainEt": "", "refEt": "", "rollname": "SHZET"}, {"description": "Special procurement", "fieldKind": "Attribute", "hryCol": "SOBMDMA", "isMainEt": "", "refEt": "SOBSL", "rollname": "SOBSL"}, {"description": "Takt time", "fieldKind": "Attribute", "hryCol": "TAKZTMDMA", "isMainEt": "", "refEt": "", "rollname": "TAKZT"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MBEWVALUA", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Valuation area", "fieldKind": "Qualifying Entity Type", "hryCol": "BWKEY", "isMainEt": "", "refEt": "", "rollname": "BWKEY"}, {"description": "Valuation Type", "fieldKind": "Qualifying Entity Type", "hryCol": "BWTAR", "isMainEt": "", "refEt": "", "rollname": "BWTAR_D"}, {"description": "Valuation Class", "fieldKind": "Attribute", "hryCol": "BKLAS", "isMainEt": "", "refEt": "", "rollname": "BKLAS"}, {"description": "Valuation Category", "fieldKind": "Attribute", "hryCol": "BWTTY", "isMainEt": "", "refEt": "", "rollname": "BWTTY_D"}, {"description": "VC: Sales order stk", "fieldKind": "Attribute", "hryCol": "EKLAS", "isMainEt": "", "refEt": "", "rollname": "EKLAS"}, {"description": "Planned price date 1", "fieldKind": "Attribute", "hryCol": "FZPLD1", "isMainEt": "", "refEt": "", "rollname": "DZPLD1"}, {"description": "Planned price date 2", "fieldKind": "Attribute", "hryCol": "FZPLD2", "isMainEt": "", "refEt": "", "rollname": "DZPLD2"}, {"description": "Planned price date 3", "fieldKind": "Attribute", "hryCol": "FZPLD3", "isMainEt": "", "refEt": "", "rollname": "DZPLD3"}, {"description": "Planned price 1", "fieldKind": "Attribute", "hryCol": "FZPLP1", "isMainEt": "", "refEt": "", "rollname": "DZPLP1"}, {"description": "Planned price 2", "fieldKind": "Attribute", "hryCol": "FZPLP2", "isMainEt": "", "refEt": "", "rollname": "DZPLP2"}, {"description": "Planned price 3", "fieldKind": "Attribute", "hryCol": "FZPLP3", "isMainEt": "", "refEt": "", "rollname": "DZPLP3"}, {"description": "Future planned price", "fieldKind": "Attribute", "hryCol": "FZPLPR", "isMainEt": "", "refEt": "", "rollname": "DZPLPR"}, {"description": "Del. flag val. type", "fieldKind": "Attribute", "hryCol": "MBEWLVORM", "isMainEt": "", "refEt": "", "rollname": "LVOBA"}, {"description": "Material Price Determination: Control", "fieldKind": "Attribute", "hryCol": "MBEWMLAST", "isMainEt": "", "refEt": "", "rollname": "CK_ML_ABST"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "MBEWMLMAA", "isMainEt": "", "refEt": "", "rollname": "CK_ML_MAAC"}, {"description": "Material origin", "fieldKind": "Attribute", "hryCol": "MTORG", "isMainEt": "", "refEt": "", "rollname": "J_1BMATORG"}, {"description": "Material Usage", "fieldKind": "Attribute", "hryCol": "MTUSE", "isMainEt": "", "refEt": "", "rollname": "J_1BMATUSE"}, {"description": "Produced in-house", "fieldKind": "Attribute", "hryCol": "OWNPR", "isMainEt": "", "refEt": "", "rollname": "J_1BOWNPRO"}, {"description": "Price unit", "fieldKind": "Attribute", "hryCol": "PEINH", "isMainEt": "", "refEt": "", "rollname": "PEINH"}, {"description": "Proj. stk val. class", "fieldKind": "Attribute", "hryCol": "QKLAS", "isMainEt": "", "refEt": "", "rollname": "QKLAS"}, {"description": "Standard price", "fieldKind": "Attribute", "hryCol": "STPRS", "isMainEt": "", "refEt": "", "rollname": "STPRS"}, {"description": "Previous price", "fieldKind": "Attribute", "hryCol": "STPRV", "isMainEt": "", "refEt": "", "rollname": "STPRV"}, {"description": "Moving price", "fieldKind": "Attribute", "hryCol": "VERPR", "isMainEt": "", "refEt": "", "rollname": "VERPR"}, {"description": "Price control", "fieldKind": "Attribute", "hryCol": "VPRSV", "isMainEt": "", "refEt": "", "rollname": "VPRSV"}, {"description": "Material Ledger: Period Totals Records Values", "fieldKind": "Attribute", "hryCol": "MBEWMLVAL", "isMainEt": "", "refEt": "", "rollname": "VPRSV"}, {"description": "Material Ledger: Prices", "fieldKind": "Attribute", "hryCol": "MBEWMLAC", "isMainEt": "", "refEt": "", "rollname": "VPRSV"}, {"description": "Material Accounting Data", "fieldKind": "Attribute", "hryCol": "MBEWACTNG", "isMainEt": "", "refEt": "", "rollname": "VPRSV"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MBEWMLVAL", "property": [{"description": "Valuation area", "fieldKind": "Leading Entity Type", "hryCol": "BWKEY", "isMainEt": "", "refEt": "", "rollname": "BWKEY"}, {"description": "Valuation Type", "fieldKind": "Leading Entity Type", "hryCol": "BWTAR", "isMainEt": "", "refEt": "", "rollname": "BWTAR_D"}, {"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Crcy type/val.view", "fieldKind": "Qualifying Entity Type", "hryCol": "CURTP", "isMainEt": "", "refEt": "", "rollname": "CURTP"}, {"description": "Price unit", "fieldKind": "Attribute", "hryCol": "PEINHMLVL", "isMainEt": "", "refEt": "", "rollname": "PEINH"}, {"description": "Periodic Unit Price", "fieldKind": "Attribute", "hryCol": "PVPRS", "isMainEt": "", "refEt": "", "rollname": "CK_PVPRS_1"}, {"description": "Standard Price", "fieldKind": "Attribute", "hryCol": "STPRSMLVL", "isMainEt": "", "refEt": "", "rollname": "CK_STPRS_1"}, {"description": "Price control", "fieldKind": "Attribute", "hryCol": "VPRSVMLVL", "isMainEt": "", "refEt": "", "rollname": "VPRSV"}], "isFolder": "X", "relation": "MBEWVALUA"}, {"name": "MBEWMLAC", "property": [{"description": "Valuation area", "fieldKind": "Leading Entity Type", "hryCol": "BWKEY", "isMainEt": "", "refEt": "", "rollname": "BWKEY"}, {"description": "Valuation Type", "fieldKind": "Leading Entity Type", "hryCol": "BWTAR", "isMainEt": "", "refEt": "", "rollname": "BWTAR_D"}, {"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Crcy type/val.view", "fieldKind": "Qualifying Entity Type", "hryCol": "CURTP", "isMainEt": "", "refEt": "", "rollname": "CURTP"}, {"description": "Price unit", "fieldKind": "Attribute", "hryCol": "PEINHMLAC", "isMainEt": "", "refEt": "", "rollname": "PEINH"}, {"description": "Future price", "fieldKind": "Attribute", "hryCol": "ZKPRS", "isMainEt": "", "refEt": "", "rollname": "DZKPRS"}, {"description": "Price Valid From", "fieldKind": "Attribute", "hryCol": "ZPRSDAT", "isMainEt": "", "refEt": "", "rollname": "CK_ZPRSDAT"}], "isFolder": "X", "relation": "MBEWVALUA"}, {"name": "MBEWACTNG", "property": [{"description": "Valuation area", "fieldKind": "Leading Entity Type", "hryCol": "BWKEY", "isMainEt": "", "refEt": "", "rollname": "BWKEY"}, {"description": "Valuation Type", "fieldKind": "Leading Entity Type", "hryCol": "BWTAR", "isMainEt": "", "refEt": "", "rollname": "BWTAR_D"}, {"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Devaluation ind.", "fieldKind": "Attribute", "hryCol": "ABWKZ", "isMainEt": "", "refEt": "", "rollname": "ABWKZ"}, {"description": "Price unit", "fieldKind": "Attribute", "hryCol": "BWPEI", "isMainEt": "", "refEt": "", "rollname": "BWPEI"}, {"description": "Commercial price 2", "fieldKind": "Attribute", "hryCol": "BWPH1", "isMainEt": "", "refEt": "", "rollname": "BWPH1"}, {"description": "Commercial price 1", "fieldKind": "Attribute", "hryCol": "BWPRH", "isMainEt": "", "refEt": "", "rollname": "BWPRH"}, {"description": "Tax price 1", "fieldKind": "Attribute", "hryCol": "BWPRS", "isMainEt": "", "refEt": "", "rollname": "BWPRS"}, {"description": "Tax price 2", "fieldKind": "Attribute", "hryCol": "BWPS1", "isMainEt": "", "refEt": "", "rollname": "BWPS1"}, {"description": "LIFO/FIFO-relevant", "fieldKind": "Attribute", "hryCol": "FXLIFO", "isMainEt": "", "refEt": "", "rollname": "XLIFO"}, {"description": "<PERSON>id from", "fieldKind": "Attribute", "hryCol": "FZKDAT", "isMainEt": "", "refEt": "", "rollname": "DZKDAT"}, {"description": "Future price", "fieldKind": "Attribute", "hryCol": "FZKPRS", "isMainEt": "", "refEt": "", "rollname": "DZKPRS"}, {"description": "Obsolete", "fieldKind": "Attribute", "hryCol": "MLAST", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_MAT_MLAST_OBSOLETE"}, {"description": "Obsolete", "fieldKind": "Attribute", "hryCol": "MLMAA", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_MAT_MLMAA_OBSOLETE"}, {"description": "LIFO pool", "fieldKind": "Attribute", "hryCol": "MYPACTNG", "isMainEt": "", "refEt": "MYPOL", "rollname": "MYPOOL"}, {"description": "Commercial price 3", "fieldKind": "Attribute", "hryCol": "VJBWH", "isMainEt": "", "refEt": "", "rollname": "VJBWH"}, {"description": "Tax price 3", "fieldKind": "Attribute", "hryCol": "VJBWS", "isMainEt": "", "refEt": "", "rollname": "VJBWS"}], "isFolder": "X", "relation": "MBEWVALUA"}, {"name": "MBEWCSTNG", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Valuation area", "fieldKind": "Qualifying Entity Type", "hryCol": "BWKEY", "isMainEt": "", "refEt": "", "rollname": "BWKEY"}, {"description": "With Qty Structure", "fieldKind": "Attribute", "hryCol": "EKALR", "isMainEt": "", "refEt": "", "rollname": "CK_EKALREL"}, {"description": "Material origin", "fieldKind": "Attribute", "hryCol": "HKMAT", "isMainEt": "", "refEt": "", "rollname": "HKMAT"}, {"description": "Origin group", "fieldKind": "Attribute", "hryCol": "HRKCSTNG", "isMainEt": "", "refEt": "HRKFT", "rollname": "HRKFT"}, {"description": "Overhead Group", "fieldKind": "Attribute", "hryCol": "KOSCSTNG", "isMainEt": "", "refEt": "KOSGR", "rollname": "CK_KOSGR"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARDSTOR", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Storage location", "fieldKind": "Qualifying Entity Type", "hryCol": "LGORT", "isMainEt": "", "refEt": "", "rollname": "LGORT_D"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Storage Bin", "fieldKind": "Attribute", "hryCol": "LGPBE", "isMainEt": "", "refEt": "", "rollname": "LGPBE"}, {"description": "DF stor. loc. level", "fieldKind": "Attribute", "hryCol": "LVORMMARD", "isMainEt": "", "refEt": "", "rollname": "LVOLG"}, {"description": "Picking area", "fieldKind": "Attribute", "hryCol": "LWMMARDST", "isMainEt": "", "refEt": "LWMKB", "rollname": "MARD_KOBER"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARDMRP", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Storage location", "fieldKind": "Qualifying Entity Type", "hryCol": "LGORT", "isMainEt": "", "refEt": "", "rollname": "LGORT_D"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "SLoc MRP indicator", "fieldKind": "Attribute", "hryCol": "DISKZ", "isMainEt": "", "refEt": "", "rollname": "DISKZ"}, {"description": "Replenishment qty", "fieldKind": "Attribute", "hryCol": "LBSTF", "isMainEt": "", "refEt": "", "rollname": "LBSTF"}, {"description": "Reorder point", "fieldKind": "Attribute", "hryCol": "LMINB", "isMainEt": "", "refEt": "", "rollname": "LMINB"}, {"description": "Spec.proc.type: SLoc", "fieldKind": "Attribute", "hryCol": "LSOMARDMR", "isMainEt": "", "refEt": "LSOBS", "rollname": "LSOBS"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCWRKSD", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Base quantity", "fieldKind": "Attribute", "hryCol": "BASMG", "isMainEt": "", "refEt": "", "rollname": "BASMG"}, {"description": "Processing time", "fieldKind": "Attribute", "hryCol": "BEARZ", "isMainEt": "", "refEt": "", "rollname": "BEARZ"}, {"description": "In-house production", "fieldKind": "Attribute", "hryCol": "DZEIT", "isMainEt": "", "refEt": "", "rollname": "DZEIT"}, {"description": "Production Supervisor", "fieldKind": "Attribute", "hryCol": "FEVMARCWR", "isMainEt": "", "refEt": "FEVOR", "rollname": "FEVOR"}, {"description": "Production unit", "fieldKind": "Attribute", "hryCol": "FRTME", "isMainEt": "", "refEt": "", "rollname": "FRTME"}, {"description": "Withdr.from prod.bin", "fieldKind": "Attribute", "hryCol": "KZPRO", "isMainEt": "", "refEt": "", "rollname": "KZPRO"}, {"description": "Material Grouping", "fieldKind": "Attribute", "hryCol": "MATGR", "isMainEt": "", "refEt": "", "rollname": "MATNRGROUP"}, {"description": "Change profile", "fieldKind": "Attribute", "hryCol": "OCMPF", "isMainEt": "", "refEt": "", "rollname": "OCM_PROFILE"}, {"description": "Setup time", "fieldKind": "Attribute", "hryCol": "RUEZT", "isMainEt": "", "refEt": "", "rollname": "RUEZT"}, {"description": "Production Scheduling Profile", "fieldKind": "Attribute", "hryCol": "SFCMARCWR", "isMainEt": "", "refEt": "SFCPF", "rollname": "CO_PRODPRF"}, {"description": "Interoperation", "fieldKind": "Attribute", "hryCol": "TRANZ", "isMainEt": "", "refEt": "", "rollname": "TRANZ"}, {"description": "Unltd Overdelivery", "fieldKind": "Attribute", "hryCol": "UEETK", "isMainEt": "", "refEt": "", "rollname": "UEETK"}, {"description": "Overdely tolerance", "fieldKind": "Attribute", "hryCol": "UEETO", "isMainEt": "", "refEt": "", "rollname": "UEETO"}, {"description": "Underdely tolerance", "fieldKind": "Attribute", "hryCol": "UNETO", "isMainEt": "", "refEt": "", "rollname": "UNETO"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCSTORE", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "CC Phys. Inv. Ind.", "fieldKind": "Attribute", "hryCol": "ABCMARCST", "isMainEt": "", "refEt": "ABCIN", "rollname": "ABCIN"}, {"description": "Unit of issue", "fieldKind": "Attribute", "hryCol": "AUSME", "isMainEt": "", "refEt": "", "rollname": "AUSME"}, {"description": "CC indicator fixed", "fieldKind": "Attribute", "hryCol": "CCFIX", "isMainEt": "", "refEt": "", "rollname": "CCFIX"}, {"description": "Time unit", "fieldKind": "Attribute", "hryCol": "LZEIH", "isMainEt": "", "refEt": "", "rollname": "LZEIH"}, {"description": "Max. Storage Period", "fieldKind": "Attribute", "hryCol": "MAXLZ", "isMainEt": "", "refEt": "", "rollname": "MAXLZ"}, {"description": "Putaway/Stock Removal Strategy", "fieldKind": "Attribute", "hryCol": "ROTATION", "isMainEt": "", "refEt": "", "rollname": "ROTATION_DATE"}, {"description": "Indicator for Original Batch Management", "fieldKind": "Attribute", "hryCol": "UCHKZ", "isMainEt": "", "refEt": "", "rollname": "UCHKZ"}, {"description": "Orig. Batch Material", "fieldKind": "Attribute", "hryCol": "UCMAT", "isMainEt": "", "refEt": "", "rollname": "UCMAT"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCSALES", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Replacement Part", "fieldKind": "Attribute", "hryCol": "ATPKZ", "isMainEt": "", "refEt": "", "rollname": "ATPKZ"}, {"description": "Loading Group", "fieldKind": "Attribute", "hryCol": "LADGR", "isMainEt": "", "refEt": "", "rollname": "LADGR"}, {"description": "Material freight grp", "fieldKind": "Attribute", "hryCol": "MFRGR", "isMainEt": "", "refEt": "", "rollname": "MFRGR"}, {"description": "Base quantity", "fieldKind": "Attribute", "hryCol": "VBAMG", "isMainEt": "", "refEt": "", "rollname": "VBAMG"}, {"description": "Processing time", "fieldKind": "Attribute", "hryCol": "VBEAZ", "isMainEt": "", "refEt": "", "rollname": "VBEAZ"}, {"description": "Setup time", "fieldKind": "Attribute", "hryCol": "VRVEZ", "isMainEt": "", "refEt": "", "rollname": "VRVEZ"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCQTMNG", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Documentation reqd", "fieldKind": "Attribute", "hryCol": "KZDKZ", "isMainEt": "", "refEt": "", "rollname": "KZDKZ"}, {"description": "Inspection interval", "fieldKind": "Attribute", "hryCol": "PRFRQ", "isMainEt": "", "refEt": "", "rollname": "PRFRQ"}, {"description": "QM material auth.", "fieldKind": "Attribute", "hryCol": "QMATA", "isMainEt": "", "refEt": "", "rollname": "QMATAUTH"}, {"description": "Inspection setup", "fieldKind": "Attribute", "hryCol": "QMATV", "isMainEt": "", "refEt": "", "rollname": "QMATV"}, {"description": "Target QM system", "fieldKind": "Attribute", "hryCol": "QSSYS", "isMainEt": "", "refEt": "", "rollname": "QSSYS_SOLL"}, {"description": "Certificate Type", "fieldKind": "Attribute", "hryCol": "QZGTP", "isMainEt": "", "refEt": "", "rollname": "QZGTYP"}, {"description": "QM Control Key", "fieldKind": "Attribute", "hryCol": "SSQSS", "isMainEt": "", "refEt": "", "rollname": "QSSPUR"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCPURCH", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Purchasing Group", "fieldKind": "Attribute", "hryCol": "EKGRP", "isMainEt": "", "refEt": "EKGRP_X", "rollname": "EKGRP"}, {"description": "Post to insp. stock", "fieldKind": "Attribute", "hryCol": "INSMK", "isMainEt": "", "refEt": "", "rollname": "INSMK_MAT"}, {"description": "Automatic PO", "fieldKind": "Attribute", "hryCol": "KAUTB", "isMainEt": "", "refEt": "", "rollname": "KAUTB"}, {"description": "Source list", "fieldKind": "Attribute", "hryCol": "KORDB", "isMainEt": "", "refEt": "", "rollname": "KORDB"}, {"description": "Critical Part", "fieldKind": "Attribute", "hryCol": "KZKRI", "isMainEt": "", "refEt": "", "rollname": "KZKRI"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCMRPSP", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Procurement type", "fieldKind": "Attribute", "hryCol": "BESKZ", "isMainEt": "", "refEt": "", "rollname": "BESKZ"}, {"description": "Stock determ. group", "fieldKind": "Attribute", "hryCol": "EPRMRPSP", "isMainEt": "", "refEt": "EPRIO", "rollname": "BF_GROUP"}, {"description": "JIT Sched. Indicator", "fieldKind": "Attribute", "hryCol": "FABKZ", "isMainEt": "", "refEt": "", "rollname": "FABKZ"}, {"description": "Batch entry", "fieldKind": "Attribute", "hryCol": "KZECH", "isMainEt": "", "refEt": "", "rollname": "KZECH"}, {"description": "Storage loc. for EP", "fieldKind": "Attribute", "hryCol": "LGFMRPSP", "isMainEt": "", "refEt": "LGFSB", "rollname": "LGFSB"}, {"description": "Prod. stor. location", "fieldKind": "Attribute", "hryCol": "LGPMRPSP", "isMainEt": "", "refEt": "LGPRO", "rollname": "LGPRO"}, {"description": "Planning Calendar", "fieldKind": "Attribute", "hryCol": "MRPMRPSP", "isMainEt": "", "refEt": "MRPPP", "rollname": "MRPPP"}, {"description": "Planned Deliv. Time", "fieldKind": "Attribute", "hryCol": "PLIFZ", "isMainEt": "", "refEt": "", "rollname": "PLIFZ"}, {"description": "Backflush", "fieldKind": "Attribute", "hryCol": "RGEKZ", "isMainEt": "", "refEt": "", "rollname": "RGEKM"}, {"description": "Bulk material", "fieldKind": "Attribute", "hryCol": "SCHGT", "isMainEt": "", "refEt": "", "rollname": "SCHGT"}, {"description": "Special procurement", "fieldKind": "Attribute", "hryCol": "SOBMRPSP", "isMainEt": "", "refEt": "SOBSL", "rollname": "SOBSL"}, {"description": "Quota arr. usage", "fieldKind": "Attribute", "hryCol": "USEQU", "isMainEt": "", "refEt": "", "rollname": "USEQU"}, {"description": "Default supply area", "fieldKind": "Attribute", "hryCol": "VSPMRPSP", "isMainEt": "", "refEt": "VSPVB", "rollname": "VSPVB"}, {"description": "GR processing time", "fieldKind": "Attribute", "hryCol": "WEBAZ", "isMainEt": "", "refEt": "", "rollname": "WEBAZ"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCMRPPP", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "MRP Group", "fieldKind": "Attribute", "hryCol": "DGRMRPPP", "isMainEt": "", "refEt": "DISGR", "rollname": "DISGR"}, {"description": "MRP Type", "fieldKind": "Attribute", "hryCol": "DISMM", "isMainEt": "", "refEt": "", "rollname": "DISMM"}, {"description": "MRP Controller", "fieldKind": "Attribute", "hryCol": "DISMRPPP", "isMainEt": "", "refEt": "DISPO", "rollname": "DISPO"}, {"description": "MRP profile", "fieldKind": "Attribute", "hryCol": "DISPR", "isMainEt": "", "refEt": "", "rollname": "DISPR"}, {"description": "Sched<PERSON>gin Key", "fieldKind": "Attribute", "hryCol": "FHOMRPPP", "isMainEt": "", "refEt": "FHORI", "rollname": "FHORI"}, {"description": "Planning time fence", "fieldKind": "Attribute", "hryCol": "FXHOR", "isMainEt": "", "refEt": "", "rollname": "FXHOR"}, {"description": "Planning cycle", "fieldKind": "Attribute", "hryCol": "LFRMRPPP", "isMainEt": "", "refEt": "LFRHY", "rollname": "LFRHY"}, {"description": "ABC Indicator", "fieldKind": "Attribute", "hryCol": "MAABC", "isMainEt": "", "refEt": "", "rollname": "MAABC"}, {"description": "Reorder Point", "fieldKind": "Attribute", "hryCol": "MINBE", "isMainEt": "", "refEt": "", "rollname": "MINBE"}, {"description": "Advanced Planning", "fieldKind": "Attribute", "hryCol": "PPSKZ", "isMainEt": "", "refEt": "", "rollname": "PPSKZ"}, {"description": "GR Handlg Cap<PERSON> Consumptn", "fieldKind": "Attribute", "hryCol": "SCM_CCHP", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/SNPCONHAP"}, {"description": "GI Handlg Cap. Bucket Consumpn", "fieldKind": "Attribute", "hryCol": "SCM_CHPO", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/SNPCONHAP_OUT"}, {"description": "Fixed Pegging", "fieldKind": "Attribute", "hryCol": "SCM_FXPPS", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/DM_FIXPEG_PROD_SETTING"}, {"description": "Product Alerts", "fieldKind": "Attribute", "hryCol": "SCM_GETAL", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/GET_ALERTS_FOR_PROD"}, {"description": "GI Processing Time", "fieldKind": "Attribute", "hryCol": "SCM_GIPRT", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/GIPRT"}, {"description": "GR Processing Time", "fieldKind": "Attribute", "hryCol": "SCM_GRPRT", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/GRPRT"}, {"description": "Heuristic", "fieldKind": "Attribute", "hryCol": "SCM_HEUID", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/PROD_HEUR_ID"}, {"description": "UoM: HandlCap in GR", "fieldKind": "Attribute", "hryCol": "SCM_HUNIT", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/HUNIT"}, {"description": "UoM: HandlCap in GI", "fieldKind": "Attribute", "hryCol": "SCM_HUOT", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/HUNIT_OUT"}, {"description": "Lot Size Unit", "fieldKind": "Attribute", "hryCol": "SCM_LSUOM", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/LSUOM"}, {"description": "Loc.-Dep. Matur.Time", "fieldKind": "Attribute", "hryCol": "SCM_MADUR", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/MATURITY_DUR_L"}, {"description": "Pegg.<PERSON>", "fieldKind": "Attribute", "hryCol": "SCM_PFALE", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/DM_PEGGING_FUTUR_ALERT"}, {"description": "Planning Package", "fieldKind": "Attribute", "hryCol": "SCM_PKGID", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/PROD_HEUR_PACKID"}, {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "SCM_PPALE", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/DM_PEGGING_PAST_ALERT"}, {"description": "Pegging Strategy", "fieldKind": "Attribute", "hryCol": "SCM_PSTRA", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/PEG_STRATEGY"}, {"description": "Avo<PERSON>s", "fieldKind": "Attribute", "hryCol": "SCM_PWALE", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/PEG_WO_ALERT_FIRST"}, {"description": "Replen. Lead Time", "fieldKind": "Attribute", "hryCol": "SCM_RELDT", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/RELDT"}, {"description": "Network name", "fieldKind": "Attribute", "hryCol": "SCM_RESNN", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/RESNET_NETNAME"}, {"description": "Reorder Days' Supply", "fieldKind": "Attribute", "hryCol": "SCM_RODUR", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/REORD_DUR"}, {"description": "PP Plng Procedure", "fieldKind": "Attribute", "hryCol": "SCM_RRPTP", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/PPS_PLANNING_TYPE"}, {"description": "Planning Group", "fieldKind": "Attribute", "hryCol": "SCM_RSGRP", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/RRP_SEL_GROUP"}, {"description": "Prod. Storage Costs", "fieldKind": "Attribute", "hryCol": "SCM_SCOST", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/SCOST"}, {"description": "Loc.-<PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "SCM_SLDUR", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/SHELF_LIFE_DUR_L"}, {"description": "Loc.-<PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "SCM_SLLOC", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/SHELF_LIFE_LOC_FLAG"}, {"description": "<PERSON><PERSON>(Loc)", "fieldKind": "Attribute", "hryCol": "SCM_SLRMN", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/SHELF_LIFE_REQ_MIN_L"}, {"description": "<PERSON><PERSON>(Loc)", "fieldKind": "Attribute", "hryCol": "SCM_SLRMX", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/SHELF_LIFE_REQ_MAX_L"}, {"description": "Safety Stock Penalty", "fieldKind": "Attribute", "hryCol": "SCM_SSPEN", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/SSPEN"}, {"description": "Requirement Strategy", "fieldKind": "Attribute", "hryCol": "SCM_STRA1", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/STRA1"}, {"description": "Target Days' Supply", "fieldKind": "Attribute", "hryCol": "SCM_TGDUR", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/TARGET_DUR"}, {"description": "Plan Explosion", "fieldKind": "Attribute", "hryCol": "SCM_WHBOM", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/WHATBOM"}, {"description": "ConfigurableMaterial", "fieldKind": "Attribute", "hryCol": "STDPD", "isMainEt": "", "refEt": "", "rollname": "STDPD"}, {"description": "Per.Lot Sz.Plng Cal.", "fieldKind": "Attribute", "hryCol": "TSIDMRPPP", "isMainEt": "", "refEt": "SCM_TSID", "rollname": "/SAPAPO/LOT_TSTRID"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCMRPMI", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "MRP dep.requirements", "fieldKind": "Attribute", "hryCol": "AHDIS", "isMainEt": "", "refEt": "", "rollname": "AHDIS"}, {"description": "Selection method", "fieldKind": "Attribute", "hryCol": "ALTSL", "isMainEt": "", "refEt": "", "rollname": "ALTSL"}, {"description": "Effective-Out Date", "fieldKind": "Attribute", "hryCol": "AUSDT", "isMainEt": "", "refEt": "", "rollname": "AUSDT"}, {"description": "Component Scrap (%)", "fieldKind": "Attribute", "hryCol": "KAUSF", "isMainEt": "", "refEt": "", "rollname": "KAUSF"}, {"description": "Discontinuation ind.", "fieldKind": "Attribute", "hryCol": "KZAUS", "isMainEt": "", "refEt": "", "rollname": "KZAUS"}, {"description": "Requirements group", "fieldKind": "Attribute", "hryCol": "KZBED", "isMainEt": "", "refEt": "", "rollname": "KZBED"}, {"description": "Action control", "fieldKind": "Attribute", "hryCol": "MDAMRPMI", "isMainEt": "", "refEt": "MDACH", "rollname": "MDACH"}, {"description": "Follow-Up Material", "fieldKind": "Attribute", "hryCol": "NFMAT", "isMainEt": "", "refEt": "", "rollname": "NFMAT"}, {"description": "Repet. Manufacturing", "fieldKind": "Attribute", "hryCol": "SAUFT", "isMainEt": "", "refEt": "", "rollname": "SA_SAUFT"}, {"description": "Individual/coll.", "fieldKind": "Attribute", "hryCol": "SBDKZ", "isMainEt": "", "refEt": "", "rollname": "SBDKZ"}, {"description": "REM Profile", "fieldKind": "Attribute", "hryCol": "SFEPR", "isMainEt": "", "refEt": "", "rollname": "SFEPR"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCMRPLS", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Assembly scrap (%)", "fieldKind": "Attribute", "hryCol": "AUSSS", "isMainEt": "", "refEt": "", "rollname": "AUSSS"}, {"description": "Fixed lot size", "fieldKind": "Attribute", "hryCol": "BSTFE", "isMainEt": "", "refEt": "", "rollname": "BSTFE"}, {"description": "Maximum Lot Size", "fieldKind": "Attribute", "hryCol": "BSTMA", "isMainEt": "", "refEt": "", "rollname": "BSTMA"}, {"description": "Minimum Lot Size", "fieldKind": "Attribute", "hryCol": "BSTMI", "isMainEt": "", "refEt": "", "rollname": "BSTMI"}, {"description": "Rounding value", "fieldKind": "Attribute", "hryCol": "BSTRF", "isMainEt": "", "refEt": "", "rollname": "BSTRF"}, {"description": "Lot Sizing Procedure", "fieldKind": "Attribute", "hryCol": "DISLS", "isMainEt": "", "refEt": "", "rollname": "DISLS"}, {"description": "Safety stock", "fieldKind": "Attribute", "hryCol": "EISBE", "isMainEt": "", "refEt": "", "rollname": "EISBE"}, {"description": "Min safety stock", "fieldKind": "Attribute", "hryCol": "EISLO", "isMainEt": "", "refEt": "", "rollname": "EISLO"}, {"description": "Storage Costs Code", "fieldKind": "Attribute", "hryCol": "LAGMRPLS", "isMainEt": "", "refEt": "LAGPR", "rollname": "LAGPR"}, {"description": "Service level (%)", "fieldKind": "Attribute", "hryCol": "LGRAD", "isMainEt": "", "refEt": "", "rollname": "LGRAD"}, {"description": "LS-Independent Costs", "fieldKind": "Attribute", "hryCol": "LOSFX", "isMainEt": "", "refEt": "", "rollname": "LOSFX"}, {"description": "Maximum Stock Level", "fieldKind": "Attribute", "hryCol": "MABST", "isMainEt": "", "refEt": "", "rollname": "MABST"}, {"description": "Unit of Measure Grp", "fieldKind": "Attribute", "hryCol": "MEGMRPLS", "isMainEt": "", "refEt": "MEGRU", "rollname": "MEGRU"}, {"description": "Rounding Profile", "fieldKind": "Attribute", "hryCol": "RDPMRPLS", "isMainEt": "", "refEt": "RDPRF", "rollname": "RDPRF"}, {"description": "Coverage profile", "fieldKind": "Attribute", "hryCol": "RWPMRPLS", "isMainEt": "", "refEt": "RWPRO", "rollname": "RWPRO"}, {"description": "Safety time ind.", "fieldKind": "Attribute", "hryCol": "SHFLG", "isMainEt": "", "refEt": "", "rollname": "SHFLG"}, {"description": "STime period profile", "fieldKind": "Attribute", "hryCol": "SHPMRPLS", "isMainEt": "", "refEt": "SHPRO", "rollname": "SHPRO"}, {"description": "Safety time/act.cov.", "fieldKind": "Attribute", "hryCol": "SHZET", "isMainEt": "", "refEt": "", "rollname": "SHZET"}, {"description": "Takt time", "fieldKind": "Attribute", "hryCol": "TAKZT", "isMainEt": "", "refEt": "", "rollname": "TAKZT"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCMRPFC", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Splitting indicator", "fieldKind": "Attribute", "hryCol": "AUFMRPFC", "isMainEt": "", "refEt": "AUFTL", "rollname": "AUFTL"}, {"description": "Mixed MRP", "fieldKind": "Attribute", "hryCol": "MISKZ", "isMainEt": "", "refEt": "", "rollname": "MISKZ"}, {"description": "Fiscal Year Variant", "fieldKind": "Attribute", "hryCol": "PERIV", "isMainEt": "", "refEt": "", "rollname": "PERIV"}, {"description": "Period Indicator", "fieldKind": "Attribute", "hryCol": "PERMRPFC", "isMainEt": "", "refEt": "PERKZ", "rollname": "PERKZ"}, {"description": "Planning Strategy Group", "fieldKind": "Attribute", "hryCol": "STRGR", "isMainEt": "", "refEt": "", "rollname": "STRGR"}, {"description": "Bwd consumption per.", "fieldKind": "Attribute", "hryCol": "VINT1", "isMainEt": "", "refEt": "", "rollname": "VINT1"}, {"description": "Fwd consumption per.", "fieldKind": "Attribute", "hryCol": "VINT2", "isMainEt": "", "refEt": "", "rollname": "VINT2"}, {"description": "Consumption mode", "fieldKind": "Attribute", "hryCol": "VRMOD", "isMainEt": "", "refEt": "", "rollname": "VRMOD"}, {"description": "Tot. repl. lead time", "fieldKind": "Attribute", "hryCol": "WZEIT", "isMainEt": "", "refEt": "", "rollname": "WZEIT"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCFRPAR", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Alpha factor", "fieldKind": "Attribute", "hryCol": "ALPHA", "isMainEt": "", "refEt": "", "rollname": "ALPHA"}, {"description": "Forecast periods", "fieldKind": "Attribute", "hryCol": "ANZPR", "isMainEt": "", "refEt": "", "rollname": "ANZPR"}, {"description": "Beta factor", "fieldKind": "Attribute", "hryCol": "BETA1", "isMainEt": "", "refEt": "", "rollname": "BETA1"}, {"description": "Delta factor", "fieldKind": "Attribute", "hryCol": "DELTA", "isMainEt": "", "refEt": "", "rollname": "DELTA"}, {"description": "Fixed periods", "fieldKind": "Attribute", "hryCol": "FIMON", "isMainEt": "", "refEt": "", "rollname": "FIMON"}, {"description": "Gamma factor", "fieldKind": "Attribute", "hryCol": "GAMMA", "isMainEt": "", "refEt": "", "rollname": "GAMMA"}, {"description": "Weighting group", "fieldKind": "Attribute", "hryCol": "GEWMARCPA", "isMainEt": "", "refEt": "GEWGR", "rollname": "GEWGR"}, {"description": "Initialization", "fieldKind": "Attribute", "hryCol": "KZINI", "isMainEt": "", "refEt": "", "rollname": "KZINI"}, {"description": "Param.optimization", "fieldKind": "Attribute", "hryCol": "KZPAR", "isMainEt": "", "refEt": "", "rollname": "KZPAR"}, {"description": "Selection procedure", "fieldKind": "Attribute", "hryCol": "MODAV", "isMainEt": "", "refEt": "", "rollname": "MODAV"}, {"description": "Model selection", "fieldKind": "Attribute", "hryCol": "MODAW", "isMainEt": "", "refEt": "", "rollname": "MODAW"}, {"description": "Optimization level", "fieldKind": "Attribute", "hryCol": "OPGRA", "isMainEt": "", "refEt": "", "rollname": "OPGRA"}, {"description": "Historical periods", "fieldKind": "Attribute", "hryCol": "PERAN", "isMainEt": "", "refEt": "", "rollname": "PERAN"}, {"description": "Initialization pds", "fieldKind": "Attribute", "hryCol": "PERIN", "isMainEt": "", "refEt": "", "rollname": "PERIN"}, {"description": "Periods per season", "fieldKind": "Attribute", "hryCol": "PERIO", "isMainEt": "", "refEt": "", "rollname": "PERIO"}, {"description": "Last forecast", "fieldKind": "Attribute", "hryCol": "PRDAT", "isMainEt": "", "refEt": "", "rollname": "PRDAT"}, {"description": "Forecast model", "fieldKind": "Attribute", "hryCol": "PRMOD", "isMainEt": "", "refEt": "", "rollname": "PRMOD"}, {"description": "Tracking limit", "fieldKind": "Attribute", "hryCol": "SIGGR", "isMainEt": "", "refEt": "", "rollname": "SIGGR"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCFRGTR", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "CAS number (pharm.)", "fieldKind": "Attribute", "hryCol": "CASMARCFT", "isMainEt": "", "refEt": "CASNR", "rollname": "CASNR"}, {"description": "Commodity code unit", "fieldKind": "Attribute", "hryCol": "EXPME", "isMainEt": "", "refEt": "", "rollname": "EXPME"}, {"description": "PRODCOM no.", "fieldKind": "Attribute", "hryCol": "GPNMARCFT", "isMainEt": "", "refEt": "GPNUM", "rollname": "GPNUM"}, {"description": "Country of origin", "fieldKind": "Attribute", "hryCol": "HERKL", "isMainEt": "", "refEt": "HERKL_X", "rollname": "HERKL"}, {"description": "Region of origin", "fieldKind": "Attribute", "hryCol": "HERMARCFT", "isMainEt": "", "refEt": "HERKR", "rollname": "HERKR"}, {"description": "Mat. CFOP category", "fieldKind": "Attribute", "hryCol": "INDUS", "isMainEt": "", "refEt": "", "rollname": "J_1BINDUS3"}, {"description": "Military goods", "fieldKind": "Attribute", "hryCol": "ITARK", "isMainEt": "", "refEt": "", "rollname": "ITARK"}, {"description": "CAP prod. group", "fieldKind": "Attribute", "hryCol": "MOGMARCFT", "isMainEt": "", "refEt": "MOGRU", "rollname": "MOGRU"}, {"description": "CAP product list no.", "fieldKind": "Attribute", "hryCol": "MOWMARCFT", "isMainEt": "", "refEt": "MOWNR", "rollname": "MOWNR"}, {"description": "Intrastat Group", "fieldKind": "Attribute", "hryCol": "MTVER", "isMainEt": "", "refEt": "", "rollname": "MTVER"}, {"description": "Customs Preference", "fieldKind": "Attribute", "hryCol": "PREFE", "isMainEt": "", "refEt": "", "rollname": "PREFE"}, {"description": "ExemptionCertificate", "fieldKind": "Attribute", "hryCol": "PRENC", "isMainEt": "", "refEt": "", "rollname": "PRENC"}, {"description": "Iss.date of ex.cert.", "fieldKind": "Attribute", "hryCol": "PREND", "isMainEt": "", "refEt": "", "rollname": "PREND"}, {"description": "Vendor decl. code", "fieldKind": "Attribute", "hryCol": "PRENE", "isMainEt": "", "refEt": "", "rollname": "PRENE"}, {"description": "Vendor decl. date", "fieldKind": "Attribute", "hryCol": "PRENG", "isMainEt": "", "refEt": "", "rollname": "PRENG"}, {"description": "Exemption cert. no.", "fieldKind": "Attribute", "hryCol": "PRENO", "isMainEt": "", "refEt": "", "rollname": "PRENN"}, {"description": "Commodity Code", "fieldKind": "Attribute", "hryCol": "STAMARCFT", "isMainEt": "", "refEt": "STAWN", "rollname": "STAWN"}, {"description": "Control code", "fieldKind": "Attribute", "hryCol": "STEMARCFT", "isMainEt": "", "refEt": "STEUC", "rollname": "STEUC"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCFRCST", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Reset automatically", "fieldKind": "Attribute", "hryCol": "AUTRU", "isMainEt": "", "refEt": "", "rollname": "AUTRU"}, {"description": "Correction factors", "fieldKind": "Attribute", "hryCol": "KZKFK", "isMainEt": "", "refEt": "", "rollname": "KZKFK"}, {"description": "Date to", "fieldKind": "Attribute", "hryCol": "VRBDT", "isMainEt": "", "refEt": "", "rollname": "VRBDT"}, {"description": "Multiplier", "fieldKind": "Attribute", "hryCol": "VRBFK", "isMainEt": "", "refEt": "", "rollname": "VRBFK"}, {"description": "RefMatl: consumption", "fieldKind": "Attribute", "hryCol": "VRBMT", "isMainEt": "", "refEt": "", "rollname": "VRBMT"}, {"description": "RefPlant:consumption", "fieldKind": "Attribute", "hryCol": "VRBWK", "isMainEt": "", "refEt": "", "rollname": "VRBWK"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCCSTNG", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Group Counter", "fieldKind": "Attribute", "hryCol": "APLAL", "isMainEt": "", "refEt": "", "rollname": "PLNAL"}, {"description": "Varian<PERSON> Key", "fieldKind": "Attribute", "hryCol": "AWSMARCCS", "isMainEt": "", "refEt": "AWSLS", "rollname": "AWSLS"}, {"description": "Production Version", "fieldKind": "Attribute", "hryCol": "FVIDK", "isMainEt": "", "refEt": "MKALBASIC", "rollname": "CK_VERID"}, {"description": "Fixed-Price Co-Product", "fieldKind": "Attribute", "hryCol": "FXPRU", "isMainEt": "", "refEt": "", "rollname": "CK_FIXPRKU"}, {"description": "Co-product", "fieldKind": "Attribute", "hryCol": "KZKUP", "isMainEt": "", "refEt": "", "rollname": "KZKUPMAT"}, {"description": "Costing Lot Size", "fieldKind": "Attribute", "hryCol": "LOSGR", "isMainEt": "", "refEt": "", "rollname": "CK_LOSGR"}, {"description": "Do Not Cost", "fieldKind": "Attribute", "hryCol": "NCOST", "isMainEt": "", "refEt": "", "rollname": "CK_NO_COSTING"}, {"description": "Group", "fieldKind": "Attribute", "hryCol": "PLNNR", "isMainEt": "", "refEt": "", "rollname": "PLNNR"}, {"description": "Task List Type", "fieldKind": "Attribute", "hryCol": "PLNTY", "isMainEt": "", "refEt": "", "rollname": "PLNTY"}, {"description": "SpecProcurem Costing", "fieldKind": "Attribute", "hryCol": "SOBMARCCS", "isMainEt": "", "refEt": "SOBSK", "rollname": "CK_SOBSL"}, {"description": "Alternative BOM", "fieldKind": "Attribute", "hryCol": "STLAL", "isMainEt": "", "refEt": "", "rollname": "STLAL"}, {"description": "BOM Usage", "fieldKind": "Attribute", "hryCol": "STLAN", "isMainEt": "", "refEt": "", "rollname": "STLAN"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCBASIC", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Distr. profile", "fieldKind": "Attribute", "hryCol": "FPRFM", "isMainEt": "", "refEt": "", "rollname": "FPRFM"}, {"description": "IUID-Relevant", "fieldKind": "Attribute", "hryCol": "IUID_RELE", "isMainEt": "", "refEt": "", "rollname": "IUID_RELEVANT"}, {"description": "IUID Type", "fieldKind": "Attribute", "hryCol": "IUID_TYPE", "isMainEt": "", "refEt": "", "rollname": "IUID_TYPE"}, {"description": "Log. handling group", "fieldKind": "Attribute", "hryCol": "LOGGR", "isMainEt": "", "refEt": "", "rollname": "LOGGR"}, {"description": "DF at plant level", "fieldKind": "Attribute", "hryCol": "MARCLVORM", "isMainEt": "", "refEt": "", "rollname": "LVOWK"}, {"description": "Plant-sp.matl status", "fieldKind": "Attribute", "hryCol": "MMSTA", "isMainEt": "", "refEt": "", "rollname": "MMSTA"}, {"description": "<PERSON>id from", "fieldKind": "Attribute", "hryCol": "MMSTD", "isMainEt": "", "refEt": "", "rollname": "MMSTD"}, {"description": "Profit Center", "fieldKind": "Attribute", "hryCol": "PRCMARCBA", "isMainEt": "", "refEt": "PRCTR", "rollname": "PRCTR"}, {"description": "Serial No. Profile", "fieldKind": "Attribute", "hryCol": "SERNP", "isMainEt": "", "refEt": "", "rollname": "SERAIL"}, {"description": "External Allocation of UII", "fieldKind": "Attribute", "hryCol": "UID_IEA", "isMainEt": "", "refEt": "", "rollname": "UID_IEA"}, {"description": "Batch management", "fieldKind": "Attribute", "hryCol": "XCHPF", "isMainEt": "", "refEt": "", "rollname": "XCHPF"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARCATP", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Plant", "fieldKind": "Qualifying Entity Type", "hryCol": "WERKS", "isMainEt": "", "refEt": "", "rollname": "WERKS_D"}, {"description": "Cross-project matl", "fieldKind": "Attribute", "hryCol": "KZPSP", "isMainEt": "", "refEt": "", "rollname": "KZPSP"}, {"description": "Availability check", "fieldKind": "Attribute", "hryCol": "MTVFP", "isMainEt": "", "refEt": "", "rollname": "MTVFP"}, {"description": "Neg. stocks in plant", "fieldKind": "Attribute", "hryCol": "XMCNG", "isMainEt": "", "refEt": "", "rollname": "XMCNG"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARASTOR", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Container reqmts", "fieldKind": "Attribute", "hryCol": "BEHVO", "isMainEt": "", "refEt": "", "rollname": "BEHVO"}, {"description": "Label type", "fieldKind": "Attribute", "hryCol": "ETIAR", "isMainEt": "", "refEt": "", "rollname": "ETIAR"}, {"description": "Label form", "fieldKind": "Attribute", "hryCol": "ETIFO", "isMainEt": "", "refEt": "", "rollname": "ETIFO"}, {"description": "Period Ind. for SLED", "fieldKind": "Attribute", "hryCol": "IPRKZ", "isMainEt": "", "refEt": "", "rollname": "DATTP"}, {"description": "Total shelf life", "fieldKind": "Attribute", "hryCol": "MHDHB", "isMainEt": "", "refEt": "", "rollname": "MHDHB"}, {"description": "Storage percentage", "fieldKind": "Attribute", "hryCol": "MHDLP", "isMainEt": "", "refEt": "", "rollname": "MHDLP"}, {"description": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "MHDRZ", "isMainEt": "", "refEt": "", "rollname": "MHDRZ"}, {"description": "Storage conditions", "fieldKind": "Attribute", "hryCol": "RAUBE", "isMainEt": "", "refEt": "", "rollname": "RAUBE"}, {"description": "Rounding rule SLED", "fieldKind": "Attribute", "hryCol": "RDMHD", "isMainEt": "", "refEt": "", "rollname": "RDMHD"}, {"description": "Maturation Time", "fieldKind": "Attribute", "hryCol": "SCMM_MDUR", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/MATURITY_DUR"}, {"description": "Reqd <PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "SCMM_SLRM", "isMainEt": "", "refEt": "", "rollname": "/SAPAPO/SHELF_LIFE_REQ_MAX"}, {"description": "Haz. material number", "fieldKind": "Attribute", "hryCol": "STOMARAST", "isMainEt": "", "refEt": "STOFF", "rollname": "STOFF"}, {"description": "Temp. conditions", "fieldKind": "Attribute", "hryCol": "TEMPB", "isMainEt": "", "refEt": "", "rollname": "TEMPB"}, {"description": "Number of GR slips", "fieldKind": "Attribute", "hryCol": "WESCH", "isMainEt": "", "refEt": "", "rollname": "WESCH"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARASPM", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Adjustment Profile", "fieldKind": "Attribute", "hryCol": "ADPROF", "isMainEt": "", "refEt": "", "rollname": "CIFADPROF"}, {"description": "Handling Indicator", "fieldKind": "Attribute", "hryCol": "HNDLCODE", "isMainEt": "", "refEt": "", "rollname": "MDG_CIFHDLCODE"}, {"description": "Handling Unit Type", "fieldKind": "Attribute", "hryCol": "HUTYP", "isMainEt": "", "refEt": "", "rollname": "MDG_CIFHUTYP"}, {"description": "Standard HU Type", "fieldKind": "Attribute", "hryCol": "HUTYP_DFL", "isMainEt": "", "refEt": "", "rollname": "MDG_CIFHUTYPDF"}, {"description": "Max. Pack. Width", "fieldKind": "Attribute", "hryCol": "MAXB", "isMainEt": "", "refEt": "", "rollname": "CIFMAXB"}, {"description": "Maximum Capacity", "fieldKind": "Attribute", "hryCol": "MAXC", "isMainEt": "", "refEt": "", "rollname": "CIFMAXC"}, {"description": "Overcapacity Toler.", "fieldKind": "Attribute", "hryCol": "MAXC_TOL", "isMainEt": "", "refEt": "", "rollname": "CIFMAXCTOL"}, {"description": "Unit of Measurement", "fieldKind": "Attribute", "hryCol": "MAXDIM_UO", "isMainEt": "", "refEt": "", "rollname": "CIFMAXDUOM"}, {"description": "Max. Pack. Height", "fieldKind": "Attribute", "hryCol": "MAXH", "isMainEt": "", "refEt": "", "rollname": "CIFMAXH"}, {"description": "Max. Pack. Length", "fieldKind": "Attribute", "hryCol": "MAXL", "isMainEt": "", "refEt": "", "rollname": "CIFMAXL"}, {"description": "Pilferable", "fieldKind": "Attribute", "hryCol": "PILFERABL", "isMainEt": "", "refEt": "", "rollname": "CIFPILFRBL"}, {"description": "Form Name", "fieldKind": "Attribute", "hryCol": "PS_SMARTF", "isMainEt": "", "refEt": "", "rollname": "CIFPSSFNAME"}, {"description": "Quality Inspec. Grp", "fieldKind": "Attribute", "hryCol": "QGRP", "isMainEt": "", "refEt": "", "rollname": "MDG_CIFQGRP"}, {"description": "Quarant. Per.", "fieldKind": "Attribute", "hryCol": "QQTIME", "isMainEt": "", "refEt": "", "rollname": "CIFQQTIME"}, {"description": "Time Unit", "fieldKind": "Attribute", "hryCol": "QQTIMEUOM", "isMainEt": "", "refEt": "", "rollname": "CIFQQTIMEUOM"}, {"description": "Preferred Unit of Measure", "fieldKind": "Attribute", "hryCol": "SCM_PUOM", "isMainEt": "", "refEt": "", "rollname": "/SCWM/DE_PUOM"}, {"description": "Var<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "TARE_VAR", "isMainEt": "", "refEt": "", "rollname": "CIFTAREVAR"}, {"description": "WH Material Group", "fieldKind": "Attribute", "hryCol": "WHMATGR", "isMainEt": "", "refEt": "", "rollname": "MDG_CIFWHMATGR"}, {"description": "WH Storage Condition", "fieldKind": "Attribute", "hryCol": "WHSTC", "isMainEt": "", "refEt": "", "rollname": "MDG_CIFWHSTC"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARASALES", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Unit of weight", "fieldKind": "Attribute", "hryCol": "ERGEI", "isMainEt": "", "refEt": "", "rollname": "ERGEI"}, {"description": "Allowed pkg weight", "fieldKind": "Attribute", "hryCol": "ERGEW", "isMainEt": "", "refEt": "", "rollname": "ERGEW"}, {"description": "Volume unit", "fieldKind": "Attribute", "hryCol": "ERVOE", "isMainEt": "", "refEt": "", "rollname": "ERVOE"}, {"description": "Allowed pkg volume", "fieldKind": "Attribute", "hryCol": "ERVOL", "isMainEt": "", "refEt": "", "rollname": "ERVOL"}, {"description": "Maximum level", "fieldKind": "Attribute", "hryCol": "FUELG", "isMainEt": "", "refEt": "", "rollname": "FUELG"}, {"description": "Excess wt tolerance", "fieldKind": "Attribute", "hryCol": "GEWTO", "isMainEt": "", "refEt": "", "rollname": "GEWTO"}, {"description": "Customer", "fieldKind": "Attribute", "hryCol": "KUNNR", "isMainEt": "", "refEt": "", "rollname": "KUNNR"}, {"description": "Closed", "fieldKind": "Attribute", "hryCol": "KZGVH", "isMainEt": "", "refEt": "", "rollname": "KZGVH"}, {"description": "Material freight grp", "fieldKind": "Attribute", "hryCol": "MFRGRMARA", "isMainEt": "", "refEt": "", "rollname": "MFRGR"}, {"description": "X-distr.chain status", "fieldKind": "Attribute", "hryCol": "MSTAV", "isMainEt": "", "refEt": "", "rollname": "MSTAV"}, {"description": "<PERSON>id from", "fieldKind": "Attribute", "hryCol": "MSTDV", "isMainEt": "", "refEt": "", "rollname": "MSTDV"}, {"description": "Stackability factor", "fieldKind": "Attribute", "hryCol": "STFAK", "isMainEt": "", "refEt": "", "rollname": "STFAK"}, {"description": "Transportation Group", "fieldKind": "Attribute", "hryCol": "TRAGR", "isMainEt": "", "refEt": "", "rollname": "TRAGR"}, {"description": "Packaging matl type", "fieldKind": "Attribute", "hryCol": "VHART", "isMainEt": "", "refEt": "", "rollname": "VHART"}, {"description": "Excess volume tol.", "fieldKind": "Attribute", "hryCol": "VOLTO", "isMainEt": "", "refEt": "", "rollname": "VOLTO"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARAQTMNG", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "QM proc. active", "fieldKind": "Attribute", "hryCol": "QMPUR", "isMainEt": "", "refEt": "", "rollname": "QMPUR"}, {"description": "Catalog Profile", "fieldKind": "Attribute", "hryCol": "RBNRM", "isMainEt": "", "refEt": "", "rollname": "RBNR"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MARAPURCH", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Int. material number", "fieldKind": "Attribute", "hryCol": "BMATN", "isMainEt": "", "refEt": "", "rollname": "MPMAT"}, {"description": "Order Unit", "fieldKind": "Attribute", "hryCol": "BSTME", "isMainEt": "", "refEt": "", "rollname": "BSTME"}, {"description": "Purchasing value key", "fieldKind": "Attribute", "hryCol": "EKWSL", "isMainEt": "", "refEt": "", "rollname": "EKWSL"}, {"description": "Manufacturer", "fieldKind": "Attribute", "hryCol": "MFRNR", "isMainEt": "", "refEt": "", "rollname": "MFRNR"}, {"description": "Manufacturer Part No.", "fieldKind": "Attribute", "hryCol": "MFRPN", "isMainEt": "", "refEt": "", "rollname": "MFRPN"}, {"description": "Mfr part profile", "fieldKind": "Attribute", "hryCol": "MPROF", "isMainEt": "", "refEt": "", "rollname": "MPROF"}, {"description": "Variable Purchase Order Unit", "fieldKind": "Attribute", "hryCol": "VABME", "isMainEt": "", "refEt": "", "rollname": "VABME"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "INTCMNT", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "LANGUCODE", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Comment", "fieldKind": "Attribute", "hryCol": "NOTEINTCM", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_MAT_INTCMSTRG"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "CLASSASGN", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Change Number", "fieldKind": "Qualifying Entity Type", "hryCol": "CHANGENO", "isMainEt": "", "refEt": "", "rollname": "AENNR"}, {"description": "Class", "fieldKind": "Qualifying Entity Type", "hryCol": "CLASS", "isMainEt": "", "refEt": "", "rollname": "KLASSE_D"}, {"description": "Class Type", "fieldKind": "Qualifying Entity Type", "hryCol": "CLASSTYPE", "isMainEt": "", "refEt": "", "rollname": "KLASSENART"}, {"description": "Int. counter", "fieldKind": "Qualifying Entity Type", "hryCol": "ECOCNTR", "isMainEt": "", "refEt": "", "rollname": "ADZHL"}, {"description": "UUID", "fieldKind": "Qualifying Entity Type", "hryCol": "GUID", "isMainEt": "", "refEt": "", "rollname": "SYSUUID_C"}, {"description": "Internal class no.", "fieldKind": "Attribute", "hryCol": "CLINT", "isMainEt": "", "refEt": "", "rollname": "CLINT"}, {"description": "Status", "fieldKind": "Attribute", "hryCol": "CLSTATUS", "isMainEt": "", "refEt": "", "rollname": "CLSTATUS"}, {"description": "<PERSON><PERSON>", "fieldKind": "Attribute", "hryCol": "DATUV_CLA", "isMainEt": "", "refEt": "", "rollname": "DATUV"}, {"description": "Deletion Indicator", "fieldKind": "Attribute", "hryCol": "LKENZ_CLA", "isMainEt": "", "refEt": "", "rollname": "LKENZ"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "BSCDATTXT", "property": [{"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "LANGUCODE", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Basic Text", "fieldKind": "Attribute", "hryCol": "NOTEBSCDA", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_MAT_BSCDASTRG"}], "isFolder": "X", "relation": "MATERIAL"}, {"name": "MATCHGMNG", "property": [{"description": "Change Management", "fieldKind": "Entity Type Itself", "hryCol": "MATCHGMNG", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_MAT_CRNO"}, {"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Change Number", "fieldKind": "Attribute", "hryCol": "ECOCHGMNG", "isMainEt": "", "refEt": "CHGMNGNO", "rollname": "MDG_BS_MAT_CHGMNG_AENNR"}, {"description": "Revision Level", "fieldKind": "Attribute", "hryCol": "REVCHGMNG", "isMainEt": "", "refEt": "REVLV", "rollname": "CC_REVLV"}], "isFolder": "X", "relation": "MM"}, {"name": "DRADBASIC", "property": [{"description": "Document Type", "fieldKind": "Leading Entity Type", "hryCol": "DOKAR", "isMainEt": "", "refEt": "", "rollname": "DOKAR"}, {"description": "Document", "fieldKind": "Leading Entity Type", "hryCol": "DOKNR", "isMainEt": "", "refEt": "", "rollname": "DOKNR"}, {"description": "Document part", "fieldKind": "Leading Entity Type", "hryCol": "DOKTL", "isMainEt": "", "refEt": "", "rollname": "DOKTL_D"}, {"description": "Document version", "fieldKind": "Leading Entity Type", "hryCol": "DOKVR", "isMainEt": "", "refEt": "", "rollname": "DOKVR"}, {"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "CAD indicator", "fieldKind": "Attribute", "hryCol": "CAD_POS", "isMainEt": "", "refEt": "", "rollname": "CAD_POS"}, {"description": "Newest version", "fieldKind": "Attribute", "hryCol": "NEWESTVER", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_MAT_NEWESTVER"}, {"description": "Document Link Text", "fieldKind": "Attribute", "hryCol": "DRADTXT", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_MAT_NEWESTVER"}], "isFolder": "X", "relation": "MM"}, {"name": "DRADTXT", "property": [{"description": "Document Type", "fieldKind": "Leading Entity Type", "hryCol": "DOKAR", "isMainEt": "", "refEt": "", "rollname": "DOKAR"}, {"description": "Document", "fieldKind": "Leading Entity Type", "hryCol": "DOKNR", "isMainEt": "", "refEt": "", "rollname": "DOKNR"}, {"description": "Document part", "fieldKind": "Leading Entity Type", "hryCol": "DOKTL", "isMainEt": "", "refEt": "", "rollname": "DOKTL_D"}, {"description": "Document version", "fieldKind": "Leading Entity Type", "hryCol": "DOKVR", "isMainEt": "", "refEt": "", "rollname": "DOKVR"}, {"description": "Material", "fieldKind": "Leading Entity Type", "hryCol": "MATERIAL", "isMainEt": "", "refEt": "", "rollname": "MATNR"}, {"description": "Language Key", "fieldKind": "Qualifying Entity Type", "hryCol": "LANGUCODE", "isMainEt": "", "refEt": "", "rollname": "SPRAST"}, {"description": "Document Link Text", "fieldKind": "Attribute", "hryCol": "TXTDRAD", "isMainEt": "", "refEt": "", "rollname": "MDG_BS_MAT_DRADSTRG"}], "isFolder": "X", "relation": "DRADBASIC"}]}