{"dataModel": [{"name": "BP", "desc": "Business Partner", "node": true, "entity": [{"name": "FKKVK", "desc": "Contract Account", "node": true, "entity": [{"name": "FKKVK", "amount": 16.99, "currency": "EUR", "size": "S"}, {"name": "Short Black Dress", "amount": 47.99, "currency": "EUR", "size": "M"}, {"name": "Long Blue Dinner Dress", "amount": 103.99, "currency": "USD", "size": "L"}, {"name": "FKKVKTD", "desc": "Contract Account (Time Dependence)", "node": true, "entity": [{"name": "FKKVK", "amount": 16.99, "currency": "EUR", "size": "S"}, {"name": "Short Black Dress", "amount": 47.99, "currency": "EUR", "size": "M"}, {"name": "Long Blue Dinner Dress", "amount": 103.99, "currency": "USD", "size": "L"}]}]}, {"name": "AD_POSTAL", "desc": "ADDRESS", "node": true, "entity": [{"name": "USMD_OBS_TAT", "desc": "USMD_OBS_TAT", "node": true, "entity": [{"name": "Dresses", "entity": [{"name": "Casual <PERSON> Dress", "amount": 16.99, "currency": "EUR", "size": "S"}, {"name": "Short Black Dress", "amount": 47.99, "currency": "EUR", "size": "M"}, {"name": "Long Blue Dinner Dress", "amount": 103.99, "currency": "USD", "size": "L"}]}, {"name": "Tops", "node": true, "entity": [{"name": "Printed Shirt", "amount": 24.99, "currency": "USD", "size": "M"}, {"name": "Tank Top", "amount": 14.99, "currency": "USD", "size": "S"}]}, {"name": "<PERSON>ts", "node": true, "entity": [{"name": "Red Pant", "amount": 32.99, "currency": "USD", "size": "M"}, {"name": "<PERSON><PERSON>", "amount": 44.99, "currency": "USD", "size": "S"}, {"name": "Black Jeans", "amount": 99.99, "currency": "USD", "size": "XS"}, {"name": "Relaxed <PERSON><PERSON>", "amount": 56.99, "currency": "USD", "size": "L"}]}, {"name": "Skirts", "node": true, "entity": [{"name": "Striped Skirt", "amount": 24.99, "currency": "USD", "size": "M"}, {"name": "Black Skirt", "amount": 44.99, "currency": "USD", "size": "S"}]}]}, {"name": "Jewelry", "node": true, "entity": [{"name": "Necklace", "amount": 16.99, "currency": "USD"}, {"name": "Bracelet", "amount": 47.99, "currency": "USD"}, {"name": "Gold Ring", "amount": 399.99, "currency": "USD"}]}, {"name": "Handbags", "node": true, "entity": [{"name": "Little Black Bag", "amount": 16.99, "currency": "USD", "size": "S"}, {"name": "Grey Shopper", "amount": 47.99, "currency": "USD", "size": "M"}]}, {"name": "Shoes", "node": true, "entity": [{"name": "Pumps", "amount": 89.99, "currency": "USD"}, {"name": "Sport Shoes", "amount": 47.99, "currency": "USD"}, {"name": "Boots", "amount": 103.99, "currency": "USD"}]}]}, {"name": "Men", "node": true, "entity": [{"name": "Clothing", "node": true, "entity": [{"name": "Shirts", "entity": [{"name": "Black T-shirt", "amount": 9.99, "currency": "USD", "size": "XL"}, {"name": "Polo T-shirt", "amount": 47.99, "currency": "USD", "size": "M"}, {"name": "White Shirt", "amount": 103.99, "currency": "USD", "size": "L"}]}, {"name": "<PERSON>ts", "node": true, "entity": [{"name": "Blue Jeans", "amount": 78.99, "currency": "USD", "size": "M"}, {"name": "<PERSON><PERSON><PERSON>", "amount": 54.99, "currency": "USD", "size": "S"}]}, {"name": "Shorts", "node": true, "entity": [{"name": "Trouser Short", "amount": 62.99, "currency": "USD", "size": "M"}, {"name": "<PERSON>", "amount": 44.99, "currency": "USD", "size": "S"}]}]}, {"name": "Accessories", "node": true, "entity": [{"name": "Tie", "amount": 36.99, "currency": "USD"}, {"name": "Wallet", "amount": 47.99, "currency": "USD"}, {"name": "Sunglasses", "amount": 199.99, "currency": "USD"}]}, {"name": "Shoes", "node": true, "entity": [{"name": "Fashion Sneaker", "amount": 89.99, "currency": "USD"}, {"name": "Sport Shoe", "amount": 47.99, "currency": "USD"}, {"name": "Boots", "amount": 103.99, "currency": "USD"}]}]}, {"name": "Girls", "node": true, "entity": [{"name": "Clothing", "node": true, "entity": [{"name": "Shirts", "entity": [{"name": "Red T-shirt", "amount": 16.99, "currency": "USD", "size": "S"}, {"name": "Tunic Top", "amount": 47.99, "currency": "USD", "size": "M"}, {"name": "<PERSON><PERSON> Sweater", "amount": 103.99, "currency": "USD", "size": "L"}]}, {"name": "<PERSON>ts", "node": true, "entity": [{"name": "Blue Jeans", "amount": 24.99, "currency": "USD", "size": "M"}, {"name": "Red Pant", "amount": 54.99, "currency": "USD", "size": "S"}]}, {"name": "Shorts", "node": true, "entity": [{"name": "<PERSON><PERSON>", "amount": 32.99, "currency": "USD", "size": "M"}, {"name": "Sport Short", "amount": 14.99, "currency": "USD", "size": "S"}]}]}, {"name": "Accessories", "node": true, "entity": [{"name": "Necklace", "amount": 26.99, "currency": "USD"}, {"name": "Gloves", "amount": 7.99, "currency": "USD"}, {"name": "<PERSON><PERSON>", "amount": 12.99, "currency": "USD"}]}, {"name": "Shoes", "node": true, "entity": [{"name": "Sport Shoes", "amount": 39.99, "currency": "USD"}, {"name": "Boots", "amount": 87.99, "currency": "USD"}, {"name": "Sandals", "amount": 63.99, "currency": "USD"}]}]}, {"name": "Boys", "node": true, "entity": [{"name": "Clothing", "node": true, "entity": [{"name": "Shirts", "entity": [{"name": "Black T-shirt with Print", "amount": 16.99, "currency": "USD", "size": "S"}, {"name": "Blue Shirt", "amount": 47.99, "currency": "USD", "size": "M"}, {"name": "<PERSON> Sweater", "amount": 63.99, "currency": "USD", "size": "L"}]}, {"name": "<PERSON>ts", "node": true, "entity": [{"name": "Blue Jeans", "amount": 44.99, "currency": "USD", "size": "M"}, {"name": "<PERSON>", "amount": 89.99, "currency": "USD", "size": "S"}]}, {"name": "Shorts", "node": true, "entity": [{"name": "Sport Short", "amount": 32.99, "currency": "USD", "size": "M"}, {"name": "<PERSON><PERSON>", "amount": 99.99, "currency": "USD", "size": "XS"}, {"name": "<PERSON>", "amount": 56.99, "currency": "USD", "size": "L"}]}]}, {"name": "Accessories", "node": true, "entity": [{"name": "Sunglasses", "amount": 36.99, "currency": "USD"}, {"name": "<PERSON><PERSON>", "amount": 17.99, "currency": "USD"}, {"name": "<PERSON><PERSON><PERSON>", "amount": 15.99, "currency": "USD"}]}, {"name": "Shoes", "node": true, "entity": [{"name": "<PERSON><PERSON><PERSON>", "amount": 89.99, "currency": "USD"}, {"name": "Sport Shoe", "amount": 47.99, "currency": "USD"}, {"name": "Boots", "amount": 103.99, "currency": "USD"}]}]}]}, {"name": "M2", "desc": "Model 2", "node": true}, {"name": "M3", "desc": "Model 3", "node": true}], "sizes": [{"key": "XS", "value": "Extra Small"}, {"key": "S", "value": "Small"}, {"key": "M", "value": "Medium"}, {"key": "L", "value": "Large"}]}