<edmx:Edmx xmlns:edmx="http://schemas.microsoft.com/ado/2007/06/edmx"
	xmlns:m="http://schemas.microsoft.com/ado/2007/08/dataservices/metadata" xmlns:sap="http://www.sap.com/Protocols/SAPData" Version="1.0">
	<edmx:DataServices m:DataServiceVersion="2.0">
		<Schema xmlns="http://schemas.microsoft.com/ado/2008/09/edm" Namespace="Z_SEARCHHELP_TRANSPORT_REQUEST_SRV" xml:lang="en"
			sap:schema-version="1">
			<EntityType Name="ZzSearchhelpTr" sap:content-version="1">
				<Key><PropertyRef Name="As4user"/></Key><Property Name="As4user" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Owner" sap:creatable="false"
					sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Strkorr" Type="Edm.String" Nullable="false" MaxLength="20" sap:unicode="false" sap:label="Parent Request"
					sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="As4text" Type="Edm.String" Nullable="false" MaxLength="60" sap:unicode="false" sap:label="Short Description"
					sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType>
			<EntityContainer Name="Z_SEARCHHELP_TRANSPORT_REQUEST_SRV_Entities" m:IsDefaultEntityContainer="true" sap:supported-formats="atom json xlsx"><EntitySet Name="ZzSearchhelpTrSet" EntityType="Z_SEARCHHELP_TRANSPORT_REQUEST_SRV.ZzSearchhelpTr" sap:creatable="false"
				sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/></EntityContainer><atom:link xmlns:atom="http://www.w3.org/2005/Atom" rel="self" href="./sap/Z_SEARCHHELP_TRANSPORT_REQUEST_SRV/$metadata"/><atom:link xmlns:atom="http://www.w3.org/2005/Atom" rel="latest-version" href="./sap/Z_SEARCHHELP_TRANSPORT_REQUEST_SRV/$metadata"/></Schema>
	</edmx:DataServices>
</edmx:Edmx>