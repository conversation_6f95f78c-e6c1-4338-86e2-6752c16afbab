<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="1.0" xmlns:edmx="http://schemas.microsoft.com/ado/2007/06/edmx"
	xmlns:m="http://schemas.microsoft.com/ado/2007/08/dataservices/metadata" xmlns:sap="http://www.sap.com/Protocols/SAPData">
	<edmx:DataServices m:DataServiceVersion="2.0">
		<Schema Namespace="Z_DATAMODEL1_SRV" xml:lang="en" sap:schema-version="1" xmlns="http://schemas.microsoft.com/ado/2008/09/edm">
			<EntityType Name="Z_DATAMODELDM" sap:content-version="1">
				<Key><PropertyRef Name="Model"/></Key><Property Name="Model" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Data Model" sap:creatable="false"
					sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Desc" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Description (medium)"
					sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Aarea" Type="Edm.String" Nullable="false" MaxLength="10" sap:unicode="false" sap:label="Active Area" sap:creatable="false"
					sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Prefix" Type="Edm.String" Nullable="false" MaxLength="10" sap:unicode="false" sap:label="Prefix/Namespace"
					sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Package" Type="Edm.String" Nullable="false" MaxLength="30" sap:unicode="false" sap:label="Package" sap:creatable="false"
					sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Uname" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="User Name" sap:creatable="false"
					sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Cdate" Type="Edm.String" Nullable="false" sap:unicode="false" sap:label="Changed on" sap:creatable="false"
					sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Time" Type="Edm.String" Nullable="false" sap:unicode="false" sap:label="Time" sap:creatable="false" sap:updatable="false"
					sap:sortable="false" sap:filterable="false"/><Property Name="Version" Type="Edm.String" Nullable="false" MaxLength="1" sap:unicode="false" sap:label="Active Version"
					sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType>
			<EntityContainer Name="Z_DATAMODEL1_SRV_Entities" m:IsDefaultEntityContainer="true" sap:supported-formats="atom json xlsx"><EntitySet Name="Z_DATAMODELDMSet" EntityType="Z_DATAMODEL1_SRV.Z_DATAMODELDM" sap:creatable="false" sap:updatable="false"
				sap:deletable="false" sap:pageable="false" sap:content-version="1"/></EntityContainer><atom:link rel="self"
				href="https://webidecp-h3dea9a6e.dispatcher.us3.hana.ondemand.com/destinations/Supernova/sap/opu/odata/sap/Z_DATAMODEL1_SRV/$metadata"
				xmlns:atom="http://www.w3.org/2005/Atom"/><atom:link rel="latest-version"
				href="https://webidecp-h3dea9a6e.dispatcher.us3.hana.ondemand.com/destinations/Supernova/sap/opu/odata/sap/Z_DATAMODEL1_SRV/$metadata"
				xmlns:atom="http://www.w3.org/2005/Atom"/></Schema>
	</edmx:DataServices>
</edmx:Edmx>