{"operands": [{"group": "1", "desc": "Ordinal comparisons", "operators": [{"name": "is equal to", "constant": "GC_OPTION_EQUAL", "id": "EQ"}, {"name": "is not equal to", "constant": "GC_OPTION_NOT_EQUAL", "id": "NE"}, {"name": "is less than", "constant": "GC_OPTION_LESS", "id": "LT"}, {"name": "is less than or equal to", "constant": "GC_OPTION_LESS_EQUAL", "id": "LE"}, {"name": "is greater than", "constant": "GC_OPTION_GREATER", "id": "GT"}, {"name": "is greater than or equal to", "constant": "GC_OPTION_GREATER_EQUAL", "id": "GE"}, {"name": "is between", "constant": "GC_OPTION_BETWEEN", "id": "BT"}, {"name": "is not between", "constant": "GC_OPTION_NOT_BETWEEN", "id": "NB"}, {"name": "matches pattern", "constant": "GC_OPTION_CONTAINS_PATTERN", "id": "CP"}, {"name": "does not match pattern", "constant": "GC_OPTION_NOT_CONTAINS_PATTERN", "id": "NP"}]}, {"group": "2", "desc": "String comparisons", "operators": [{"name": "contains any", "constant": "GC_OPTION_CONTAINS_ANY", "id": "CA"}, {"name": "does not contain any", "constant": "GC_OPTION_CONTAINS_NOT_ANY", "id": "NA"}, {"name": "contains only", "constant": "GC_OPTION_CONTAINS_ONLY", "id": "CO"}, {"name": "does not contain only", "constant": "GC_OPTION_CONTAINS_NOT_ONLY", "id": "CN"}, {"name": "contains string", "constant": "GC_OPTION_CONTAINS_STRING", "id": "CS"}, {"name": "does not contain string", "constant": "GC_OPTION_CONTAINS_NO_STRING", "id": "NS"}, {"name": "matches pattern", "constant": "GC_OPTION_CONTAINS_PATTERN", "id": "CP"}, {"name": "does not match pattern", "constant": "GC_OPTION_NOT_CONTAINS_PATTERN", "id": "NP"}, {"name": "starts with", "constant": "GC_OPTION_STARTS_WITH", "id": "SW"}, {"name": "ends with", "constant": "GC_OPTION_ENDS_WITH", "id": "EW"}]}], "logicGate": [{"id": "and"}, {"id": "or"}, {"id": "not and"}, {"id": "not or"}], "CR_OBMAIN": [{"UsmdCreqObmain": "", "UsmdCreqObmainTxt": "No Processing"}, {"UsmdCreqObmain": "L", "UsmdCreqObmainTxt": "Change of Object List"}, {"UsmdCreqObmain": "O", "UsmdCreqObmainTxt": "Execution of Changes"}], "ApprType": [{"key": " ", "text": ""}, {"key": "AG", "text": "AG - Security Role"}, {"key": "US", "text": "US - User"}, {"key": "SU", "text": "SU - Special User"}]}