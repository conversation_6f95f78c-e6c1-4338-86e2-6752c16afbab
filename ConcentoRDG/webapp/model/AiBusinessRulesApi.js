sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService"
], function (ModelGetLists, DMRDataService) {
	"use strict";
    let AiBusinessRulesApi = {};
	this.AIREPONSESIZE = 100;
	let parentThis = this;

	/**
	 * Get the list of rules based on the search criteria and return to the AI 
	 * @param {*} oController 
	 * @param {Object} oFilterInfo 
	 * 	{
	 *		modelName   : 'aaa',
	 *		entityName  : 'bbb',
	 *		crType      : "CRXXX",
	 *		ruleType    : RULE_TYPE,
	 * 		offset 		: 0
	 * 	}
	 * @param {Function} fAICallback
	 */
    AiBusinessRulesApi.getBusinessRulesByEntity = function(oController, oFilterInfo, fAICallback){

       // Check if this is a repeat request for additional data from a previous call
	   let oCacheInfo = undefined;
	   if(oFilterInfo.offset > 0){
		 oCacheInfo = parentThis._getAiResponseDataCache(oController, "BRF_REPORT");
	   }

	   // If a cache exists and the request matches, return the next set of data 
	   if(oCacheInfo && oCacheInfo.modelName === oFilterInfo.modelName && oCacheInfo.entityName === oFilterInfo.entityName &&
		  oCacheInfo.crType === oFilterInfo.crType && oCacheInfo.ruleType === oFilterInfo.ruleType){
			 // return AIREPONSESIZE results 
			 let arrData = oCacheInfo.data;
			 let arrElementsToSend = arrData.splice(0, parentThis.AIREPONSESIZE);

			 // Store the remaining info
			 oCacheInfo.data = arrData;
			 if(arrElementsToSend.length === 0){
			   oCacheInfo = undefined;
			 }
			 parentThis._setAiResponseDataCache(oController, "BRF_REPORT", oCacheInfo);

			 // Respond with the data
			 fAICallback({ status: "success", data: arrElementsToSend });

			 return;
	   	}

	   	// Get the business rules list and return to AI 
				//sModelName, sEntityName, sCRType, sRuleType,
		let sRuleTypeNumber = parentThis._RuleTypeTextToNum(oFilterInfo.ruleType);
		let promise = ModelGetLists.getBusinessRuleList(
			oController, oFilterInfo.modelName, oFilterInfo.entityName, oFilterInfo.crType, sRuleTypeNumber);
		promise
   		.then(function(result){

		 	// return the top AIREPONSESIZE results 
		 	let arrElementsToSend = result.splice(0, parentThis.AIREPONSESIZE);

			// If all elements could not be sent, store the info
			parentThis._setAiResponseDataCache(oController, "BRF_REPORT", {
		   		modelName: oFilterInfo.modelName, entityName: oFilterInfo.entityName, crType: oFilterInfo.crType, 
		   		ruleType: oFilterInfo.ruleType, data: result
		 	});  

		 	// Respond with the data
		 	fAICallback({ status: "success", data: arrElementsToSend });
	   	})
	   	.catch(function(error){
			fAICallback({ status: "error", data: error });
	   	});

		return promise;
    };

	/**
	 * Get the details of the specified rule
	 * @param {*} oController 
	 * @param {Object} oFilterInfo 
	 * 	{
	 * 		ruleName {String}: AAA_A__AAAA 
	 * 	}
	 * @param {Function} fAICallback
	 */
	 AiBusinessRulesApi.getBusinessRuleDetailsByEntity = function(oController, oFilterInfo, fAICallback){

		let promise = ModelGetLists.getBusinessRuleDetails(oController, oFilterInfo.ruleName);
		promise.then(function(result){
			if(result.length === 0){
				throw new Error("No rule found");
			}

			// Remove unwanted content from the result
			delete result[0].__metadata; // Remove meta data that could leak server info

			// Parse the stringified json for ease of use in AI
			if(result[0].RuleExpr){ 
				result[0].RuleExpr = JSON.parse(result[0].RuleExpr);
			}
			fAICallback({ status: "success", data: result[0] });
		})
		.catch(function(error){
			fAICallback({ status: "error", data: error.message });
		});
 
		 return promise;
	 };

	AiBusinessRulesApi.createMandatoryRules = async function(oController, oPayload, fAICallback){

		let apiController = this;

		(new DMRDataService(
		oController,
		"CREATEBRF",
		"/MODELSet",
		undefined,
		"/", // Root of the received data
		"Mandatory Rule Save"
		))	.showBusyIndicator(false)
		.saveData(
			false,
			oPayload,
			null, {
				success: {
					fCallback: function (oParams, oResponseData) {

						let sMessageType = oResponseData.modeltomessageNav.results[0].MessageType;
						let sStatus = "";
						let oResponse = {};

						if(sMessageType === "S"){
							
							sStatus = "Success";
							let arrConditions = [];
	
							for (let i = 0; i < oResponseData.modeltobrfNav.results[0].brftoattrNav.results.length; i++) {
								const oAttribute = oResponseData.modeltobrfNav.results[0].brftoattrNav.results[i];
								let oConditionDetails = {
									attribute: oAttribute.UsmdAttribute,
									mandatory: oAttribute.Mandt
								};
	
								arrConditions.push(oConditionDetails);
							}

							oResponse = {
								changeRequest: oResponseData.UsmdCreqType,
								dataModel: oResponseData.UsmdModel,
								ruleType: "Mandatory",
								ruleName: oResponseData.UserRuleName,
								entity: oResponseData.modeltobrfNav.results[0].UsmdEntity,
								conditions: [...arrConditions]
							};
							
						}  else {
							sStatus = "Error";
							oResponse = apiController.messageErrorHandle(oResponseData.modeltobrfNav.results);
						}
						

						fAICallback({
							status: sStatus,
							data: oResponse
						});
					},
				},
				error: {
					fCallback: function (oParams, oResponseData) {
						let oError = JSON.parse(oResponseData.responseText);

						fAICallback({
							status: "Error",
							message: oError.error.message.value
						});
					},
				},
			}
		);
	};

	AiBusinessRulesApi.createSingleValueValidation = function(oController, oPayload, fAICallback){
		this.createSingleValueRule(oController, oPayload, fAICallback, "BRFVALCHECK");
	 };

	 AiBusinessRulesApi.createSingleValueDerivation = function(oController, oPayload, fAICallback){
		this.createSingleValueRule(oController, oPayload, fAICallback, "BRFENRICH");
	 };

	 AiBusinessRulesApi.messageErrorHandle = function(arrMessage) {
		let arrErrorMessages = [];

		for (let i = 0; i <  arrMessage.length; i++) {
		  const oMessage =  arrMessage[i];
		  arrErrorMessages[i] = oMessage.Message;
		}

		return {
			message: arrErrorMessages.join(", ")
		};
	 };

	AiBusinessRulesApi.createSingleValueRule = function(oController, oPayload, fAICallback, sService){

		let apiController = this;
		let sRuleType;

		if(sService === "BRFVALCHECK"){
			sRuleType = "Validation";
		} else {
			sRuleType = "Derivation";
		}

		(new DMRDataService(
			oController,
			sService,
			"/MODELSet",
			undefined,
			"/", // Root of the received data
			"Single Value " + sRuleType +" Rule Save"
		))	
		.showBusyIndicator(false)
		.saveData(
			false,
			oPayload,
			null, 
			{
				success: {
					fCallback: function (oParams, oResponseData) {

						let sMessageType = oResponseData.MODELTOMESSAGE.results[0].MessageType;
						let sStatus = "";
						let oResponse = {};

						if(sMessageType === "S"){
							sStatus = "Success";
							let arrConditions = [];
	
							for (let i = 0; i < oResponseData.MODELTOENTITY.results[0].ENTITYTOATTR.results[0].ATTRTOCONDITION.results.length; i++) {
								const oCondition = oResponseData.MODELTOENTITY.results[0].ENTITYTOATTR.results[0].ATTRTOCONDITION.results[i];
								let	oConditionDetails = {
										entity: oCondition.UsmdEntity,
										attribute: oCondition.UsmdAttribute,
										operator: oCondition.Operator,
										value: oCondition.Value,
										toValue: oCondition.ToValue
									};
	
								arrConditions.push(oConditionDetails);
							}

							oResponse = {
								changeRequest: oResponseData.UsmdCreqType,
								dataModel: oResponseData.UsmdModel,
								ruleType: "Single Value " + sRuleType,
								ruleName: oResponseData.MODELTOENTITY.results[0].ENTITYTOATTR.results[0].UserRuleName,
								entity: oResponseData.MODELTOENTITY.results[0].UsmdEntity,
								conditions: [...arrConditions]
							};
						} else {
							sStatus = "Error";
							oResponse = apiController.messageErrorHandle(oResponseData.MODELTOMESSAGE.results);
						}

						fAICallback({
							status: sStatus,
							data: oResponse
						});
					},
				},
				error: {
					fCallback: function (oParams, oResponseData) {
						let oError = JSON.parse(oResponseData.responseText);

						fAICallback({
							status: "Error",
							message: oError.error.message.value
						});
					},
				},
			}
		);
	};

	AiBusinessRulesApi.addDataToPayload = function(oController, oPayload){
		let promise = new Promise(async (resolve, reject)=>{
			try{
				for(let i = 0; i < oPayload.MODELTODRIVING.length; i++){
					let oDriving = oPayload.MODELTODRIVING[i];
					
					if(oDriving.UsmdEntity !== "WF"){
						let sAttribute = oDriving.UsmdAttribute.split("__LENGTH")[0];
						let arrAttributesList = await ModelGetLists.getEntityAttributeList(oController, oPayload.UsmdModel, oDriving.UsmdEntity, sAttribute);
						let oAttrData = arrAttributesList[0];
						if (!oAttrData || !oAttrData.DATATYPE || !oAttrData.Length || !oAttrData.Decimals || !oAttrData.Kind) {
							reject("Attribute Not found: " + oDriving.UsmdEntity + " -> " + sAttribute);
							return;
						}
						oDriving.Attrdatatype = oAttrData.DATATYPE;
						oDriving.Lenghttype = oAttrData.Length;
						oDriving.Decimals = oAttrData.Decimals;
						oDriving.Kind = oAttrData.Kind;
					}
				}
				resolve(oPayload);
			}
			catch (err){
				reject(err);
			}
		});

		return promise;
	};

	AiBusinessRulesApi.createMultiValueValidation = function(oController, oPayload, fAICallback){
		this.createMultiValueRule(oController, oPayload, fAICallback, "YGW_ZEUS_BRF_VALIDATION_MODEL");
	 };

	 AiBusinessRulesApi.createMultiValueDerivation = function(oController, oPayload, fAICallback){
		this.createMultiValueRule(oController, oPayload, fAICallback, "YGW_ZEUS_BRF_DERIVE_MODEL");
	 };


	AiBusinessRulesApi.createMultiValueRule = function(oController, oPayload, fAICallback, sService){
		let sRuleType;
		let apiController = this;

		if(sService === "YGW_ZEUS_BRF_VALIDATION_MODEL"){
			sRuleType = "Validation";
		} else {
			sRuleType = "Derivation";
		}

		(new DMRDataService(
			oController,
			sService,
			"/MODELSet",
			undefined,
			"/", // Root of the received data
			"Multi Value " + sRuleType + " Rule Save"
		))	
		.showBusyIndicator(false)
		.saveData(
			false,
			oPayload,
			null, 
			{
				success: {
					fCallback: function (oParams, oResponseData) {

						let sMessageType = oResponseData.MODELTOMESSAGE.results[0].MessageType;
						let sStatus = "";
						let oResponse = {};

						if(sMessageType === "S"){
							sStatus = "Success";

							oResponse = {
								changeRequest: oResponseData.UsmdCreqType,
								dataModel: oResponseData.UsmdModel,
								ruleType: "Multi Value " + sRuleType,
								ruleName: oResponseData.UserRuleName,
								entity: oResponseData.UsmdEntity,
								conditions: [{"attribute" : "Multi"}]
							};
						} else {
							sStatus = "Error";
							oResponse = apiController.messageErrorHandle(oResponseData.MODELTOMESSAGE.results);
						}

						fAICallback({
							status: sStatus,
							data: oResponse
						});
					},
				},
				error: {
					fCallback: function (oParams, oResponseData) {
						let oError = JSON.parse(oResponseData.responseText);

						fAICallback({
							status: "Error",
							message: oError.error.message.value
						});
					},
				},
			}
		);
	};

    // Get and set functions to temporarily store the Information for AI function calls.
    this._getAiResponseDataCache = function(oController, sDataCacheKey) {
		let oAiResponseCache = oController.getView().getModel().getProperty("/AiResponseDataCache");
		let cache = oAiResponseCache? oAiResponseCache[sDataCacheKey]: undefined;
		return cache;
	};
  
	this._setAiResponseDataCache = function(oController, sDataCacheKey, dataCache){
		let oModel = oController.getView().getModel();
		let oCacheData = oModel.getProperty("/AiResponseDataCache");
		if(!oCacheData){
			oCacheData = {};
		}
		oCacheData[sDataCacheKey] = dataCache;
		oModel.setProperty("/AiResponseDataCache", oCacheData);
	};
  

	this._RuleTypeTextToNum = function(sRuleType){
		// If no rule type was provided, nothing to map.
		if(!sRuleType){
			return undefined;
		}

		let sRuleTypeCaps = sRuleType.toUpperCase();

		const oRuleTypeMapping = {
			"MANDATORY": 1,
			"SINGLEVALUEVALIDATION" : 2, 
			"SINGLEVALUEDERIVATION" : 3, 
			"MULTIVALUEVALIDATION" : 4, 
			"MULTIVALUEDERIVATION" : 5 
		};

		let returnVal = oRuleTypeMapping[sRuleTypeCaps];

		return returnVal;
	};

	return AiBusinessRulesApi;
});