sap.ui.define([
	"sap/ui/base/Object",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService"
], function (Object, DMRDataService) {
	"use strict";
	return Object.extend("dmr.mdg.supernova.SupernovaFJ.model.DmAction", {
		constructor: function (oController) {
			this.Controller = oController;
			this.srvDmAction =
				new DMRDataService(
					oController,
					"DMACTION",
					"/ACTIONSet",
					"DataModelName", // Set a Dummy name in the constructor. Reset later
					"/", // Set to root, reset later if necessary 
					"Data Model Action" // Set a default name, reset later  
				);
		},
		_invokeService: function (bUpdate, oData, oDataTemplate, fCallbackFunc, oCallbackParams) {
			let parentObject = this;
			
			this.srvDmAction
			.setToastMessage(this.sToastMessage)
			.saveData(
				bUpdate,
				oData,
				oDataTemplate, {
					success: {
						fCallback: function (oParams, oResponseData) {
							if (fCallbackFunc) {
								fCallbackFunc(oCallbackParams, oResponseData);
							}
						},
						oParam: parentObject.Controller
					}
				}
			);
			this.sToastMessage = null;
		},

		enQueue: function (sDataModel, fCallback, oEnQueueParams) {
			let oData = {
				UsmdModel: sDataModel,
				Action: "E"
			};

			this._invokeService(false, oData, null, fCallback, oEnQueueParams);
		},

		deQueue: function (sDataModel, fCallback, oEnQueueParams) {
			let oData = {
				UsmdModel: sDataModel,
				Action: "D"
			};

			this._invokeService(false, oData, null, fCallback, oEnQueueParams);
		},

		check: function (sDataModel, oData, fCallback, oEnQueueParams) {
			this._invokeService(false, oData, null, fCallback, oEnQueueParams);
		},

		saveDataModel: function (oData, fCallback, oEnQueueParams) {
			this._invokeService(false, oData, null, fCallback, oEnQueueParams);
		},
		
		setToastMessage: function(sToastMessage) {
			this.sToastMessage = sToastMessage;
			return this;
		}

		// E (enqueue) - When the user clicks edit model, the data model will be enqueued (locked) so others will not be able to perform any edits simultaneously.
		// C (Check)  - When the user created or updated an attribute and performs the check operation, the data model will be checked and displays if any warnings or error messages are present. The check operation is done even if the user hasn’t saved the data model.
		// M (Edit/Modify datamodel to add or update attribute of an entity in data model) - When the action is M, the user can add new or update an attribute. For now, if there are any errors, the changes done by the user will be reverted and error messages will be displayed
		// S (Save&Transport objects) - When the user perform save action after making necessary edits to the data model, the data model will be saved and all the objects (view cluster and tables) will be added to the TR given by the user. Error messages will be returned if there’s any failure in adding the objects to the Tr.
		// A(Activation)  - Activation is performed after saving the data model. Any error or warning messages will be returned if there are any errors and warnings while activating.
		// D (dequeue)  - Once all the necessary changes are made, we will dequeue the data model.
	});
});