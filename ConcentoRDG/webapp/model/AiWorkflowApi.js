sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
    "dmr/mdg/supernova/SupernovaFJ/libs/DataService"
], function (ModelGetLists, DMRDataService) {
	"use strict";
    let AiWorkflowApi = {};

    AiWorkflowApi.getWorkflowDetails = function(oController, fAICallback, oParameters){
        let that = this; 
        let oWorkFlowDetails = {};

        // get the workflow info
        that._getWorkflowInfo(oController, oParameters.CrType)
        .then(function(oWorkflowInfo){
            oWorkFlowDetails.oWorkflowInfo = oWorkflowInfo;
            return that._getWorkflowDetails(oController, oParameters.CrType);
        })
        .then(function(arrWorkflowData){
            oWorkFlowDetails.arrWorkflowData = arrWorkflowData;
            fAICallback({
                status: "success",
                data: oWorkFlowDetails
            });
        })
        .catch(function(oError){
            // return the response to the caller
            fAICallback({
                status: "error",
                data: oError.message
            });
        });
    };

    AiWorkflowApi._getWorkflowInfo = function(oController, sCrType){
        let promiseChangeRequestData = 
            ModelGetLists.getChangeRequestData(oController, sCrType);
        
        let responsePromise = promiseChangeRequestData.then(oChangeRequestData => {
            
            let oChangeRequestInfo = {};
            oChangeRequestInfo.UsmdModel = oChangeRequestData.UsmdModel;
            oChangeRequestInfo.UsmdEdtnType = oChangeRequestData.UsmdEdtnType;
            oChangeRequestInfo.UsmdEntity = oChangeRequestData.UsmdEntityMain;
            oChangeRequestInfo.leadingEntities = [];
            if(oChangeRequestData.CrequestToEntityNav.results.length > 0) {
                oChangeRequestData.CrequestToEntityNav.results.forEach(oLeadingEntity => {
                    oChangeRequestInfo.leadingEntities.push({
                        UsmdEntity: oLeadingEntity.UsmdEntity
                    });
                });
            }

            return oChangeRequestInfo;
        });

        return responsePromise;
    };

    /**
     * Retrieves the Workflow detail based on the CR Name provided.
     * The API returns a promise that resolves to the recessary details. 
     * 
     * @param {*} oController 
     * @param {*} oParameters // Information needed to filter the data 
     * @returns {Array} Change Request Lists in a promise
     */
    AiWorkflowApi._getWorkflowDetails = function(oController, sCrType){
        let sUrlParameters = {
            "$filter": "CrType eq '" + sCrType + "'",
            "$format": "json",
            "$expand": "wfToPrstepNav,FOLLOWUPCRNAV,BADIIMPL"
        };

        let promise = new Promise(function(resolve, reject) {
            // Call the workflow service and retrieve the data for the graph
            (new DMRDataService(
                oController,
                "WORKFLOW",
                "/WFDETAILSSet",
                "CreateWF",
                "/", // Root of the received data
                "Create WF Decision Table"
            ))
            .showBusyIndicator(false)
            .getData({
                    success: {
                        fCallback: function (oParams, oData) {

                            if (oData.results.length <= 0) {
                                return;
                            }

                            // Check to see if the data returned has DynamicWorkflow Information
                            let arrWorkflowData = oData.results;

                            // If the Dynamic Workflow Results are available, parse the content and use the same for 
                            if (oData.results[0].DynWfExpr) {
                                /**
                                 * Copy the data after updating the naming conventions. The target format is as below (same as when not using Dynamic Data)
                                 *	{
                                    *		approverType -> ApproverType
                                    *		approverValue -> ApproverVal
                                    *		crStep -> CrStep
                                    *		crType -> CrType
                                    *		crStepName -> StepType
                                    *		prStepActset -> wfToPrstepNav : {
                                    *			crType -> CrType
                                    *			prevAction -> PrevAction
                                    *			prevStep -> PrevStep
                                    *			stepStatus -> StepStatus
                                    *		}
                                    *		drivingentityset -> DRIVINGENTITYSet : {
                                    *			colNum -> ColNum
                                    *			usmdAttribute -> UsmdAttribute
                                    *			usmdEntity -> UsmdEntity
                                    *		}
                                    *		derivingentityset -> DERIVINGENTITYSet : {
                                    *			colName -> ColName
                                    *			colNum -> colNum
                                    *		}
                                    *		dtvaluesset -> DTVALUESSet : {
                                    *			colName -> ColName
                                    *			colNum -> ColNum
                                    *			colValue -> ColValue
                                    *			rowNum -> RowNum
                                    *		}
                                    *	}
                                    */
                                let oParsedDynData = JSON.parse(oData.results[0].DynWfExpr);

                                let arrApprovalSets = oParsedDynData.approvalset;

                                arrWorkflowData = [];
                                arrApprovalSets.forEach(function (approvalSet) {
                                    let oTempApprovalSet = {
                                        ApproverType: approvalSet.approverType,
                                        ApproverVal: approvalSet.approverValue,
                                        Apprdesc: approvalSet.apprdesc,
                                        CrStep: approvalSet.crStep,
                                        CrType: approvalSet.crType,
                                        Changedoc: approvalSet.changedoc ? true : false,
                                        StepType: approvalSet.stepType,
                                        Mergestep: approvalSet.mergestep,
                                        wfToPrstepNav: {
                                            results: []
                                        },
                                        Servicename: approvalSet.servicename
                                    };
                                    /** Task 12353 - Follow Up CR
                                     * Add follow up cr details to the model (To be changed for multi CR types)
                                     */
                                    if(approvalSet.followupcrnav && approvalSet.followupcrnav.length > 0) {
                                        let arrCrType = [];
                                        for(let i = 0; i < approvalSet.followupcrnav.length; i++){
                                            arrCrType.push({CrType: approvalSet.followupcrnav[i].crType});
                                        }
                                        oTempApprovalSet.FOLLOWUPCRNAV = {results: arrCrType};
                                    }
                                    
                                    // If step information is available map the information
                                    if (Array.isArray(approvalSet.prstepactset)) {
                                        approvalSet.prstepactset.forEach(function (stepAction) {
                                            let oPrevStepData = {
                                                CrType: stepAction.crType,
                                                PrevAction: stepAction.prevAction,
                                                PrevStep: stepAction.prevStep,
                                                StepStatus: stepAction.stepStatus,
                                                UsmdPriority: stepAction.usmdPriority,
                                                UsmdReason: stepAction.usmdReason,
                                                UsmdReasonRej: stepAction.rejectReason,
                                                Sign: stepAction.sign,
                                                AppStep: stepAction.appStep,
                                                Apprdesc: stepAction.apprdesc,
                                                AddlEmails: stepAction.addlEmails,
                                                Emailtemp: stepAction.emailtemp
                                            };
                                            oTempApprovalSet.wfToPrstepNav.results.push(oPrevStepData);
                                        });
                                    }
                                    /**
                                    * Parallel child information to be added under this Navigation for creating lines.
                                    * In save we removed these entries from PR_STEPACt_set navigation. We need to re add them here
                                    */
                                    if (Array.isArray(approvalSet.parallelapprovalset)) {
                                        approvalSet.parallelapprovalset.forEach(function (stepAction) {
                                            if(stepAction.crStep === approvalSet.crStep){
                                                let oPrevStepData = {
                                                    CrType: stepAction.crType,
                                                    ApproverType: stepAction.approverType,
                                                    ApproverVal: stepAction.approverValue,
                                                    Apprdesc: stepAction.apprdesc,
                                                    PrevAction: stepAction.paralleltoprstep[0].prevAction,
                                                    PrevStep: stepAction.paralleltoprstep[0].prevStep,
                                                    StepStatus: stepAction.paralleltoprstep[0].stepStatus,
                                                    UsmdPriority: stepAction.paralleltoprstep[0].usmdPriority,
                                                    UsmdReason: stepAction.paralleltoprstep[0].usmdReason,
                                                    UsmdReasonRej: stepAction.paralleltoprstep[0].rejectReason,
                                                    Sign: stepAction.paralleltoprstep[0].sign,
                                                    AppStep: stepAction.appstep,
                                                    AddlEmails: stepAction.paralleltoprstep[0].addlEmails,
                                                    Emailtemp: stepAction.paralleltoprstep[0].emailtemp
                                                };
                                                oTempApprovalSet.wfToPrstepNav.results.push(oPrevStepData);
                                            }
                                        });
                                    }

                                    // Check for Dynamic or parallel and read the data if either is available 
                                    let oDrivingEntitySetData;
                                    if (Array.isArray(approvalSet.drivingentityset) && approvalSet.drivingentityset.length > 0) {
                                        oDrivingEntitySetData = approvalSet.drivingentityset;
                                        oTempApprovalSet.Dynamic = true;
                                    } else if (Array.isArray(approvalSet.paralleldrivingset) && approvalSet.paralleldrivingset.length > 0) {
                                        oDrivingEntitySetData = approvalSet.paralleldrivingset;
                                        oTempApprovalSet.Parallel = true;
                                    }

                                    // If driving entity information is available 
                                    if (Array.isArray(oDrivingEntitySetData) && oDrivingEntitySetData.length > 0) {
                                        oTempApprovalSet.DRIVINGENTITYSet = [];
                                        oDrivingEntitySetData.forEach(function (drivingEntity) {
                                            oTempApprovalSet.DRIVINGENTITYSet.push({
                                                ColNum: drivingEntity.colNum,
                                                UsmdAttribute: drivingEntity.usmdAttribute,
                                                UsmdEntity: drivingEntity.usmdEntity,
                                                Attrdatatype: drivingEntity.attrdatatype
                                            });
                                        });
                                    }

                                    let oDerivingEntitySetData;
                                    if (Array.isArray(approvalSet.derivingentityset) && approvalSet.derivingentityset.length > 0) {
                                        oDerivingEntitySetData = approvalSet.derivingentityset;
                                    } else if (Array.isArray(approvalSet.parallelderivingset) && approvalSet.parallelderivingset.length > 0) {
                                        oDerivingEntitySetData = approvalSet.parallelderivingset;
                                    }
                                    // If deriving entity information is available 
                                    if (Array.isArray(oDerivingEntitySetData) && oDerivingEntitySetData.length > 0) {
                                        oTempApprovalSet.DERIVINGENTITYSet = [];
                                        oDerivingEntitySetData.forEach(function (drivingEntity) {
                                            oTempApprovalSet.DERIVINGENTITYSet.push({
                                                ColName: drivingEntity.colName,
                                                ColNum: drivingEntity.colNum
                                            });
                                        });
                                    }
                                    
                                    /**
                                     * Fetch the PARALLELAPPROVALSET and its connections navigation in the required format
                                     */
                                    let oParallelApprovalSetData;
                                    if(Array.isArray(approvalSet.parallelapprovalset) && approvalSet.parallelapprovalset.length > 0) {
                                        oParallelApprovalSetData = approvalSet.parallelapprovalset;
                                    }
                                    // If deriving entity information is available 
                                    if (Array.isArray(oParallelApprovalSetData) && oParallelApprovalSetData.length > 0) {
                                        oTempApprovalSet.PARALLELAPPROVALSet = [];
                                        oParallelApprovalSetData.forEach(oParallelApprover => {
                                            let oTempParallelApprovalSet = {
                                                ApproverType: oParallelApprover.approverType,
                                                ApproverVal: oParallelApprover.approverValue,
                                                Apprdesc: oParallelApprover.apprdesc,
                                                CrStep: oParallelApprover.crStep === "99" ? approvalSet.mergestep : oParallelApprover.crStep,
                                                CrType: oParallelApprover.crType,
                                                StepType: oParallelApprover.stepType,
                                                Row: oParallelApprover.row,
                                                AppStep: oParallelApprover.appstep,
                                                ParallelWf: oParallelApprover.parallelwf,
                                                ProcessPattern: oParallelApprover.processpattern,
                                                wfToPrstepNav: {
                                                    results: []
                                                },
                                                Servicename: oParallelApprover.servicename
                                            };
                                                                                    
                                            if (Array.isArray(oParallelApprover.paralleltoprstep)) {
                                                oParallelApprover.paralleltoprstep.forEach(function (stepAction) {
                                                    let oParallelPrStepDataData = {
                                                        CrType: stepAction.crType,
                                                        PrevAction: stepAction.prevAction,
                                                        PrevStep: stepAction.prevStep,
                                                        StepStatus: stepAction.stepStatus,
                                                        UsmdPriority: stepAction.usmdPriority,
                                                        UsmdReason: stepAction.usmdReason,
                                                        UsmdReasonRej: stepAction.rejectReason,
                                                        Sign: stepAction.sign,
                                                        Row: stepAction.row,
                                                        AppStep: stepAction.appStep,
                                                        Apprdesc: stepAction.apprdesc,
                                                        AddlEmails: stepAction.addlEmails,
                                                        Emailtemp: stepAction.emailtemp
                                                    };
                                                    oTempParallelApprovalSet.wfToPrstepNav.results.push(oParallelPrStepDataData);
                                                });
                                            }
                                            oTempApprovalSet.PARALLELAPPROVALSet.push(oTempParallelApprovalSet);
                                        });
                                    }

                                    let oDTValeuSetData;
                                    if (Array.isArray(approvalSet.dtvaluesset) && approvalSet.dtvaluesset.length > 0) {
                                        oDTValeuSetData = approvalSet.dtvaluesset;
                                    } else if (Array.isArray(approvalSet.paralleldtvaluesset) && approvalSet.paralleldtvaluesset.length > 0) {
                                        oDTValeuSetData = approvalSet.paralleldtvaluesset;
                                    }
                                    // If data values entity information is available 
                                    if (Array.isArray(oDTValeuSetData) && oDTValeuSetData.length > 0) {
                                        oTempApprovalSet.DTVALUESSet = [];
                                        oTempApprovalSet.DTACTIONS = [];
                                        //Feature/12334 - Operator added to DTVALUESSet to be retrieved after save it
                                        oDTValeuSetData.forEach(function (dataValue) {
                                            oTempApprovalSet.DTVALUESSet.push({
                                                ColName: dataValue.colName,
                                                ColNum: dataValue.colNum,
                                                ColValue: dataValue.colValue,
                                                ColValueDescr: dataValue.colValuedescr,
                                                RowNum: dataValue.rowNum,
                                                //Bug 12478 - Read back DefaultStep value from payload
                                                Defaultstep: dataValue.defaultstep,
                                                Operator: dataValue.operator
                                            });
                                        });

                                        // Filter the dtvalueset by StepActions column and sort by row num 
                                        let arrTempDtValuesSet = oTempApprovalSet.DTVALUESSet.filter(function (oDtValue) {
                                            if (oDtValue.ColName === "StepActions") {
                                                return true;
                                            }
                                            return false;
                                        });

                                        // Sort the values by Row Number
                                        arrTempDtValuesSet = arrTempDtValuesSet.sort(function (a, b) {
                                            return parseInt(a.RowNum, 10) - parseInt(b.RowNum, 10);
                                        });

                                        // Copy the actions to the DTACTIONS array 
                                        arrTempDtValuesSet.forEach(function (oStepActionValue) {
                                            oTempApprovalSet.DTACTIONS.push({
                                                action: oStepActionValue.ColValue,
                                                RowNum: oStepActionValue.RowNum
                                            });
                                        });
                                    }

                                    arrWorkflowData.push(oTempApprovalSet);
                                });
                                
                            }
                            arrWorkflowData.forEach(function(oApprovalSet){
                                if(oApprovalSet.PARALLELAPPROVALSet){
                                    oApprovalSet.PARALLELAPPROVALSet.forEach(function(oStep, index){
                                        if (oApprovalSet.CrStep === oStep.CrStep){
                                            oApprovalSet.PARALLELAPPROVALSet.splice(index, 1);
                                        }
                                    });
                                }
                            });

                            // return the response to the caller
                            resolve(arrWorkflowData);
                        },
                        oParam: {
                            "controller": oController
                        }
                    },
                    error: {
                        fCallback: function () {
                            reject("Unable to fetch Workflow details");
                        },
                        oParam: oController
                    }
                },
                sUrlParameters);
            });

        return promise;
    };

    /**
     * Retrieves the Change request list based on the data model provided.
     * The API returns a promise that resolves to the recessary details. 
     * 
     * @param {*} oController 
     * @param {*} fAICallback 
     * @param {*} oParameters // Information needed to filter the data 
     * @returns {Array} Change Request Lists 
     */
    AiWorkflowApi.getChangeRequestLists = function(oController, fAICallback, oParameters){
        let sDataModel = oParameters.dataModel;

        ModelGetLists.getChangeRequestsList(oController, sDataModel, false)
        .then(function(oCrList){
            // check the results parameter of the received data
            // if the results parameter is empty, return an empty array
            if (oCrList.results.length === 0) {
                fAICallback({
                    status: "error",
                    data: "No Change Requests found"
                });
                return;
            }

            // if results have been returned, extract the CR Name and description into an array to return
            let arrCrList = [];
            oCrList.results.forEach(function(oCr){
                arrCrList.push({
                    CRName: oCr.UsmdCreqType,
                    CRDescription: oCr.Txtmi,
                    dataModel: oCr.UsmdModel
                });
            });

            fAICallback(
                { status: "success", data: arrCrList });
            return;
        })
        .catch(function(oError){
            fAICallback({
                status: "error",
                data: oError.message
            });
        }); 

        return;
    };
  

	return AiWorkflowApi;
});