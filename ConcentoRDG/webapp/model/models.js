sap.ui.define([
	"sap/ui/model/json/JSONModel",
	"sap/ui/Device",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
	"sap/m/MessageBox"
], function (JSONModel, Device, Utilities, DMRDataService, MessageBox) {
	"use strict";

	return {

		createDeviceModel: function () {
			let oModel = new JSONModel(Device);
			oModel.setDefaultBindingMode("OneWay");
			return oModel;
		}, 
		
		/**
		 * Use the user info retrieved and get the mapping information for the user. 
		 */
		getSapUserName: function(oController){
			let that = this;
			let promiseShowPopupAlert;
			let oRouter = sap.ui.core.UIComponent.getRouterFor(oController);
			// Get the user info
			let oPromiserUserInfo = this.getUserInfo();
			let oSapUserPromise = oPromiserUserInfo.then(function(oUserInfo){
				//UserInfo API for AWS returns properties name: S-user Id and email: BTP Email.
				//UserInfo API for Azure returns property name: BTP Email
				let sMail = oUserInfo.email ? oUserInfo.email.toUpperCase() : oUserInfo.name.toUpperCase();
				let sName;
				// user name url 
				let sUserNameUrl = "/USERNAMESet";
				sUserNameUrl += "(Name='" + (oUserInfo.name?oUserInfo.name:"") + "',Mail='" + (sMail ? sMail : "") + "')";
				
				let srvObject =
					new DMRDataService(
						oController,
						"AUTH_USERNAMES",
						sUserNameUrl,
						"DummyUnUsed",
						"/",
						"User Names Read."
					);

				let oPromise = new Promise(function(resolve){
					// Retrieve the global model 
					let oRDGGlobalModel = that.getRDGGlobalModel();
					
					// if the data has already been retrieved and stored, resolve immedeately
					let sapUserInfo = oRDGGlobalModel.getProperty("/sapUserInfo");
					if(sapUserInfo){
						resolve(sapUserInfo);
						return;
					}
					
					srvObject
						.showBusyIndicator(false)
						.getData({
							success: {
								fCallback: function (oParams, oData) {
									/**
									 * If user name is not found, route back to TargetHome
									 * userErrorFlag is used to make sure error popup is shown only once.
									 * Once user clicks on Ok, flag in unset
									 */
									if(oData.Messagetype === "S"){
										oRDGGlobalModel.setProperty("/sapUserInfo", oData);
										resolve(oData);
									} else {
										if(!oRDGGlobalModel.getProperty("/userInfo/userErrorFlag")) {
											sMail  = oUserInfo.email ? oUserInfo.email : "";
											sName =  oUserInfo.name ? "(" + oUserInfo.name + ")" : "";
											promiseShowPopupAlert =
												Utilities.showPopupAlert("SAP Username is not defined for user " + sMail + sName + ". Kindly contact the administrator",
													MessageBox.Icon.ERROR,
													"User not defined");
											promiseShowPopupAlert.then(function () {
												oRDGGlobalModel.setProperty("/userInfo/userErrorFlag", undefined);
												oRouter.navTo("TargetHome");
											});
											resolve({Sapname: "", Name: ""});
											oRDGGlobalModel.setProperty("/userInfo/userErrorFlag", "X");
										}
									}
								}
							},
							failure: {
								fCallback: function(){
									if(!oRDGGlobalModel.getProperty("/userInfo/userErrorFlag")) {
										sMail  = oUserInfo.email ? oUserInfo.email : "";
										sName =  oUserInfo.name ? "(" + oUserInfo.name + ")" : "";
										promiseShowPopupAlert =
											Utilities.showPopupAlert("Unable to fetch User information for user " + sMail + sName + ". Kindly contact the administrator",
												MessageBox.Icon.ERROR,
												"System Error");
										promiseShowPopupAlert.then(function () {
											oRDGGlobalModel.setProperty("/userInfo/userErrorFlag", undefined);
											oRouter.navTo("TargetHome");
										});
										resolve({Sapname: "", Name: ""});
										oRDGGlobalModel.setProperty("/userInfo/userErrorFlag", "X");
								}
							}
						}
					},
					undefined);
				});
				return oPromise;
			});

			return oSapUserPromise;
		},
		
		getCurrentSystem: function(oController) {
			let that = this;
			
			let oPromise = new Promise(function (resolve, reject) {
				
				// Retrieve the global model 
				let oRDGGlobalModel = that.getRDGGlobalModel();
				
				// if the data has already been retrieved and stored, resolve immedeately
				let systemInfo = oRDGGlobalModel.getProperty("/systemInfo");
				if(systemInfo){
					resolve(systemInfo);
					return;
				}
				
				let oDataModel = oController.getOwnerComponent().getModel("DETERMINE_SYSTEM");
				oDataModel.setHeaders({"Application-Interface-Key":"l6z1vjte"});

				let oGetParameterObject = {
					method: "GET",
					success: function (oData) {
						oRDGGlobalModel.setProperty("/systemInfo", oData.results[0]);
						resolve(oData.results[0]);
						return;
					},
					error: function (oErrorData) {
						reject(oErrorData);
					},
					urlParameters: undefined
				};
				oDataModel.read("/SYSTEMSet", oGetParameterObject);
			});
			return oPromise;
		},
		
		/**
		 * Get the logged in user info. If it has not been retrieve before, read it and store it. 
		 * Return the local user details if it has already been read and stored 
		 */
		getUserInfo : function() {
			let that = this;
			
			let promiseUserName = new Promise(function(resolve, reject){
				// Retrieve the global model 
				let oRDGGlobalModel = that.getRDGGlobalModel();
				
				// if the data has already been retrieved and stored, resolve immedeately
				let oUserInfo = oRDGGlobalModel.getProperty("/userInfo");
				if(oUserInfo){
					resolve(oUserInfo);
					return;
				}

				if (window.location.href.includes("localhost")) {
					// Get the user info locally
					let oModelLocal = new sap.ui.model.json.JSONModel();
					oModelLocal.loadData("./userInfo.json", {}, false);
					oRDGGlobalModel.setProperty("/userInfo", oModelLocal.getData());
					resolve(oModelLocal.getData());
				} else {
					// Get the user info from the server
					let oModel = new sap.ui.model.json.JSONModel();
					oModel.loadData("services/userapi/attributes");
					
					oModel.attachRequestCompleted(function onCompleted(oEvent) {
						if (oEvent.getParameter("success")) {
							oUserInfo = this.getData();
							oRDGGlobalModel.setProperty("/userInfo", oUserInfo);
							resolve(oUserInfo);
						} else {
							let msg = oEvent.getParameter("errorObject").textStatus; 
							if (msg) {
								this.setData("status", msg);
							} else {
								this.setData("status", "Unknown error retrieving user info");
							}
							reject(null);
						}
					});
				}
			});
			
			return promiseUserName;
		},
		
		
		getRDGGlobalModel : function() {
			let oGlobalModel = sap.ui.getCore().getModel("rdgGlobalModel");
			
			// If the named model does not exist, create
			if(!oGlobalModel)
			{
				// Create a model to be used as the Global model
				oGlobalModel = new sap.ui.model.json.JSONModel();
	
				sap.ui.getCore().setModel(oGlobalModel, "rdgGlobalModel");
			}
			
			return oGlobalModel;
		}

	};
});