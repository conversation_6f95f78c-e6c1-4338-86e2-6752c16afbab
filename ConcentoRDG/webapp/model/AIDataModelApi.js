sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists"
], function (ModelGetLists) {
	"use strict";
    let AiDataModelApi = {};

    /**
     * Retrieves the Data Model Information and returns information. The function returns 
     * a promise that resolves to the recessary details. 
     * 
     * @param {*} oController 
     * @param {*} fAICallback 
     * @returns {Object} DataModelInfo 
     *  {
     *      reportType: "PIE",
     *      reportInfo: { // DEPRECATE
     *          inActive : 5, 
     *          active: 10
     *      },
     *      reportActive: {
     *          inActive: iInactiveDataModels,
     *          active: iActiveDataModels
     *      },
     *      reportCustom: {
     *          customModels: iCustomDataModels,
     *          standardModels: iTotalDataModels - iCustomDataModels
     *      }
     *  };
     */
    AiDataModelApi.getDataModelStatistics = function(oController, fAICallback){

        // Call the get lists function to retrieve the data model information.
        ModelGetLists.getDataModelsList(oController)
        // Get the data model information
        .then(function(resultsDataModel){
            let arrDataModels = resultsDataModel.results;

            let iTotalDataModels = arrDataModels.length;
            let iActiveDataModels = arrDataModels.filter( (dataModel) => dataModel.Version === "E Same").length;
            let iInactiveDataModels = iTotalDataModels - iActiveDataModels;
            let iCustomDataModels = arrDataModels.filter( (dataModel) => (new RegExp("[YZ].")).test(dataModel.Model)).length;

            let responseInformation = {
                reportType: "PIE",
                reportDate: (new Date(Date.now())).toLocaleString(),
                // @deprecate
                reportInfo:{
                    reportType: "PIE",
                    inActive: iInactiveDataModels,
                    active: iActiveDataModels
                },
                reportDataModelActive: {
                    reportType: "PIE",
                    inActive: iInactiveDataModels,
                    active: iActiveDataModels
                },
                reportDataModelCustom: {
                    reportType: "PIE",
                    customModels: iCustomDataModels,
                    standardModels: iTotalDataModels - iCustomDataModels
                }
            };
            // Return the data models and the extracted info
            // Data Model info can be used for further processing
            return {dataModels: arrDataModels, dataModelStats: responseInformation };
        })
        // Get the entity information
        .then(function(responseInformation){
            ModelGetLists.getModelEntityList(oController, undefined, undefined, undefined, undefined, false)
            .then(function(results){
                let iCountEntites = results.length;
                // get Count of custom entities 
                let iCountCustomEntities = 
                    results.filter( (entity) => (new RegExp("[YZ].")).test(entity.UsmdEntity)).length;

                /********  Get info on entities per model *********/
                let arrDataModel = responseInformation.dataModels; // returned from previous call
                /**
                 * [
                 *      {
                 *          DataModel: "OG",
                 *          TotalEntites: 20,
                 *          CustomEntites: 10,
                 *          StandardEntites: 10
                 *      }
                 * ]
                 */
                let arrEntityStats = [];
                arrDataModel.forEach(element => {
                    // filter data by data model
                    let arrFilteredEntities = results.filter( entity => entity.UsmdModel === element.Model);
                    let iTotalEntities = arrFilteredEntities.length;
                    let iCustomEntities = arrFilteredEntities.filter( (entity) => (new RegExp("[YZ].")).test(entity.UsmdEntity)).length;
                    arrEntityStats.push({
                        DataModel: element.Model,
                        TotalEntities: iTotalEntities,
                        CustomEntities: iCustomEntities,
                        StandardEntities: iTotalEntities - iCustomEntities
                    });
                });

                let oAiResponse = responseInformation.dataModelStats;
                oAiResponse.reportEntities = {
                    reportType: "PIE",
                    TotalEntities: iCountEntites,
                    TotalCustomEntities: iCountCustomEntities,
                    TotalStandarEntities: iCountEntites - iCountCustomEntities
                };

                oAiResponse.reportEntitiesByDataModel = {
                    reportType: "BAR",
                    EntityInfoByDataMode: arrEntityStats
                };
                fAICallback(oAiResponse);
            });
        })
        .catch(function(){
            fAICallback({
                reportType: "ERROR",
                reportMessage: "Error retrieving Data Model Statistics"
            });
        });

        return;
    };
  

	return AiDataModelApi;
});