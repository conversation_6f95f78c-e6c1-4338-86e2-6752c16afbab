sap.ui.define([

], function () {
	"use strict";
	return {
		Type: {
			Mandatory : "1",
			ValueCheck: "2",
			DerivationCheck: "3",
			DecisionTableCheck: "4",
			DecisionTableValue: "5",
			UnIdentified: "unidentified"
		},
		"OrdinalComparisons": [
			{ "name": "is equal to", "constant": "GC_OPTION_EQUAL", "id": "EQ" }, 
			{ "name": "is not equal to", "constant": "GC_OPTION_NOT_EQUAL", "id": "NE" }, 
			{ "name": "is less than", "constant": "GC_OPTION_LESS", "id": "LT" }, 
			{ "name": "is less than or equal to", "constant": "GC_OPTION_LESS_EQUAL", "id": "LE"  }, 
			{ "name": "is greater than", "constant": "GC_OPTION_GREATER", "id": "GT" }, 
			{ "name": "is greater than or equal to", "constant": "GC_OPTION_GREATER_EQUAL", "id": "GE" }, 
			{ "name": "is between", "constant": "GC_OPTION_BETWEEN", "id": "BT" }, 
			{ "name": "is not between", "constant": "GC_OPTION_NOT_BETWEEN", "id": "NB" }, 
			{ "name": "matches pattern", "constant": "GC_OPTION_CONTAINS_PATTERN", "id": "CP" }, 
			{ "name": "does not match pattern", "constant": "GC_OPTION_NOT_CONTAINS_PATTERN", "id": "NP" }
		],
		"StringComparisons": [
			{ "name": "contains any",  "constant": "GC_OPTION_CONTAINS_ANY", "id": "CA"  }, 
			{ "name": "does not contain any", "constant": "GC_OPTION_CONTAINS_NOT_ANY", "id": "NA" }, 
			{ "name": "contains only", "constant": "GC_OPTION_CONTAINS_ONLY", "id": "CO" }, 
			{ "name": "does not contain only", "constant": "GC_OPTION_CONTAINS_NOT_ONLY", "id": "CN" }, 
			{ "name": "contains string", "constant": "GC_OPTION_CONTAINS_STRING", "id": "CS" }, 
			{ "name": "does not contain string", "constant": "GC_OPTION_CONTAINS_NO_STRING", "id": "NS" }, 
			{ "name": "matches pattern", "constant": "GC_OPTION_CONTAINS_PATTERN", "id": "CP" }, 
			{ "name": "does not match pattern", "constant": "GC_OPTION_NOT_CONTAINS_PATTERN", "id": "NP" }, 
			{ "name": "starts with", "constant": "GC_OPTION_STARTS_WITH", "id": "SW" }, 
			{ "name": "ends with", "constant": "GC_OPTION_ENDS_WITH", "id": "EW" }
		],
		"LogicGates": [
			{ "id": "and" }, 
			{ "id": "or" }, 
			{ "id": "not and" }, 
			{ "id": "not or" }
		]
	};
});