sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/model/ModelMessages",
    "dmr/mdg/supernova/SupernovaFJ/model/GetLists",
    "dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "sap/m/MessageBox"
], function (ModelMessages, GetLists, Utilities, MessageBox) {
	"use strict";
	return {
        BACKGROUNDJOB_TYPE: {
            NONE: undefined,
            DATA_MODEL: "-1", // Unsed in API, for readability only
            INTERFACES: "-2", // Unsed in API, for readability only
            BUSINESS_RULES: "1",
            UI_PROPERTIES: "2",
            GENERATE_MODEL_VIEWS: "3",
            PROCESS_ANALYTICAL_VIEW: "4",
            CHANGE_ANALYTICAL_VIEW: "5"
        },
        
        formatterViewState: function(status){
            let sState = "Success";

            switch(status){
                case "Generated":
                    sState = "Success";
                    break;
                case "Changed - To Be Regenerated":
                case "To be regenerated":
                    sState = "Error";
                    break;
                case "Saved as Draft":
                    sState = "Warning";
                    break;
                default:
                    sState = "Information";
            }

            return sState;
        },

        /**
         * 
         * @param {Controller} oController 
         * @param {BACKGROUNDJOB_TYPE} sFeatureType
         * @param {Object} oParams {
         *      sDataModel      : {DataModelName}       [CDS Views, Data Model, Generic]
         *      sViewName       : {ViewName}            [CDS Views,CR Type Sources]
         *      sCRtypeSrc      : {CR Type Source}      [CR Type Source]
         *      sAction         : {Data Model Action}   [Data Model]
         *    }
         * @param bNoPopupNotice: When true the check is performed and the status returned. 
         *                      But no popup is displayed.
         */
        checkStatus: async function(oController, sFeatureType, oParams, bNoPopupNotice,){
            let promise = undefined;
            switch(sFeatureType){
                case this.BACKGROUNDJOB_TYPE.PROCESS_ANALYTICAL_VIEW:
                case this.BACKGROUNDJOB_TYPE.CHANGE_ANALYTICAL_VIEW:
                    // if view name is not specified, throw an error
                    if(!oParams.sViewName){
                        // i18n not require, this is a programming error
                        throw new Error("View Name is mandatory");
                    }

                    // falls through for further procesing
                case this.BACKGROUNDJOB_TYPE.GENERATE_MODEL_VIEWS:
                    // If Data Model is not specified, throw an error
                    if(!oParams.sDataModel){
                        // i18n not require, this is a programming error
                        throw new Error("Data Model not specified");
                    }
                    promise = GetLists.getBackgroundJobStatus(oController, oParams.sDataModel, sFeatureType, undefined, undefined, oParams.sViewName);
                    break;

                default:
                    // i18n not require, this is a programming error
                    throw new Error("Not Implemented Yet");
            }
            // Await the promise and collect the response
            let returnPromise = promise.then(function(response){
                let arrResults = response.JOB2MESSAGENAV.results;
                let bErrorReceived = false;
                // Show a popup message if only a single result is received
                if(arrResults.length === 1){
                    let sMessage = arrResults[0].Message + " " + arrResults[0].Msgv1;
                    let sMessageBoxIcon = MessageBox.Icon.ERROR;
                    switch(arrResults[0].MessageType){
                        case "S":
                            sMessageBoxIcon = MessageBox.Icon.SUCCESS;
                            break;
                        case "W":
                            sMessageBoxIcon = MessageBox.Icon.INFORMATION;
                            break;
                        case "E": 
                            bErrorReceived = true;
                            break;
                        default: // Error etc
                            // Do nothing
                    }
                    if( bNoPopupNotice === false){
                        Utilities.showPopupAlert(sMessage, sMessageBoxIcon, 
                            oController.geti18nText("analyticalViews.common.backgroundJob.popup.title"));
                    }
                } 
                // Implement the showMessagesDialog irrespective of the number of messages. 
                else if(arrResults.length > 1){
                    // Copy messages into an array
                    let arrMessages = [];
                    jQuery.extend(true, arrMessages, arrResults);

                    // Sort the array by error types, also identify if any errors were found
                    arrMessages.sort(function(message){
                        if(message.MessageType === "E"){
                            bErrorReceived = true;
                            return -1;
                        }else if(message.MessageType === "W"){
                            return 1;
                        } 
                        return 0;
                    });

                    // Show the message dialog only if the bNoPopupNotice is not set to false
                    if(bNoPopupNotice === false){
                        ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
                    }
                }
                // Returned as a promised response
                return bErrorReceived;
            });

            return returnPromise;
        }
    };
});