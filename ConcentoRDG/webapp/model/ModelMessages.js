sap.ui.define([
	"sap/ui/model/json/JSONModel"
], function (JSONModel) {
	"use strict";
	return {

		messageEmphasis: function(sMsgType){
			let bEmphasize = false;
			switch(sMsgType){
				case "W":
				case "S":
				case "I":
				case "":
					bEmphasize = false;
					break;
				case "E":
				default:
					bEmphasize = true;
					break;
			}
			return bEmphasize;
		},
		messageStatus: function(sMsgType) {
			let sState = "Error";
			switch(sMsgType){
				case "W":
					sState = "Warning";
					break;
				case "S":
					sState = "Success";
					break;
				case "I":
				case "":
					sState = "Information";
					break;
				case "E":
				default:
					sState = "Error";
					break;
			}
			return sState;
		},
		closeMessageDialog: function () {
			if (this.messagedDialogHandle) {
				this.messagedDialogHandle.close();
				/**
				 * Property type must be populated before event bus subscribe
				 * Publish of event only possible if type is specified
				 */
				let sType = this.ModelMessages.type;
				let oRouter = this.ModelMessages.router;
				if(sType === "InterfaceMapping") {
					sap.ui.getCore().getEventBus().publish("rdgChannel", "interfaceMapping");
				} else if (sType === "EntityMapping") {
					//Fix/12400 - Callback function is called here to be executed after displaying messages if applicable
					sap.ui.getCore().getEventBus().publish("rdgChannel", "showMapping");
					this.ModelMessages.callback();
				// Bug 13082 - change the type to "InterfaceWizard" so it can be a general type between proxy and idoc wizards
				} else if(sType === "InterfaceWizard"){


					//Implement the back navigation
					let oHistory = this.ModelMessages.history;
					let sPreviousHash = oHistory.getPreviousHash();

					if (sPreviousHash && sPreviousHash !== "") {
						// We have a previus hash, so browser knows how to go back of a page
						window.history.go(-1);
					} else {
						let arrCurrentRoute = oHistory.aHistory[oHistory.aHistory.length - 1].split("*");
						// Bug 13082 - change the type to "InterfaceWizard" so it can be a general type between proxy and idoc wizards
						if(arrCurrentRoute[0].includes("InterfaceWizard")) {
							oRouter.navTo("Interfaces");
						} else {
							// We got here with a deeplink so we don't know how to go back
							// Just return to your homepage or whatever route you want to!
							// Replace HomeRoute with your current route ;)
							oRouter.navTo("TargetHome", {}, true);
						}

					}	
					//Fix/12400 - Condition added to identify if after accept the popup messages it has to return to dataModel screen			
				} else if(sType === "DataModelPending"){
					oRouter.navTo("dataModel", {}, true);
				}
					
				// Bug 13537 - Clearing the type and router properties after we are done with the additional checks
				this.ModelMessages.type = "";
				this.ModelMessages.router = "";

				// Call the resolve handler 
				this.promiseResolveHandler();
			}
		},
		showMessagesDialog: function (oParentController, arrMessages, oParentView) {

			let promise = new Promise(function(resolve, reject){
				// Get the Messages Models, if not found create 
				let oMessageModel = oParentController.getModel("messageData");
				if (!oMessageModel) {
					oMessageModel = new JSONModel();
					oParentController.setModel(oMessageModel, "messageData");
				}
				oMessageModel.setProperty("/messages", arrMessages);
				if (!oParentController.messagedDialogHandle) {
					oParentController.messagedDialogHandle =
						sap.ui.xmlfragment(
							"dmr.mdg.supernova.SupernovaFJ.view.shared.ModelMessages",
							oParentController);
					oParentView.addDependent(oParentController.messagedDialogHandle);
				}

				// Store the resolve handler to use during close
				oParentController.promiseResolveHandler = resolve; 
				oParentController.promiseRejectHandler = reject; 

				// Show Dialog 
				oParentController.messagedDialogHandle.open();
			});

			return promise; 
		}
	};
});