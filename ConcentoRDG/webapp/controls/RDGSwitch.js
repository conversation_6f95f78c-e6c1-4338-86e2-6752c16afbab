sap.ui.define(
    ["sap/m/Switch"],
    function(Switch) {
        return Switch.extend("dmr.mdg.supernova.SupernovaFJ.controls.RDGSwitch", {
            metadata: {
                properties: {
                    editable: {
                        type: "boolean",
                        defaultValue: false
                    }
                }
            },
            renderer: function(oRm, oControl){
                if(oControl.getEditable()) {
                    sap.m.SwitchRenderer.render(oRm, oControl); //use supercass renderer routine
                } else {
                    let sText = "Disabled";
                    oRm.write("<span tabindex=\"0\"");
                    // oRm.write("<span");
                    oRm.writeControlData(oControl);
                    oRm.writeClasses(oControl);
                    oRm.write(">");
                    oRm.write( jQuery.sap.encodeHTML( sText ) );
                    oRm.write("</span>");
                }
            }
        });
    }
);