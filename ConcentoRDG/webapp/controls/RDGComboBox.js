sap.ui.define(
    ["sap/m/ComboBox"],
    function(ComboBox) {
        return ComboBox.extend("dmr.mdg.supernova.SupernovaFJ.controls.RDGComboBox", {
            metadata: {
                properties: {
                    rdgSpecialProp: {
                        type: "boolean",
                        defaultValue: true
                    }
                },
                events: {
                    customChange: {}
                }
            },
            renderer: function(oRm, oControl){
                sap.m.ComboBoxRenderer.render(oRm, oControl); //use supercass renderer routine
            },

            init: function() {
                ComboBox.prototype.init.apply(this, arguments);
                this.attachCustomChange(this._handleCustomChange, this);

            },

            onchange: function() {
                this.fireCustomChange();
            },

            // onBeforeRendering: function() {
            //     ComboBox.prototype.onBeforeRendering.apply(this, arguments);
            //     this.detachChange(this._handleSelectionChange, this);
            //     this.attachChange(this._handleCustomSelectionChange, this);
            // },

            _handleCustomChange: function(oEvent) {
                let selectedItem = this.getSelectedItem();
                if(selectedItem) {
                    this.fireChange({
                        selectedItem: selectedItem
                    });
                    oEvent.getSource().setValueState("None");
                } else {
                    oEvent.getSource().setSelectedKey("");
                    oEvent.getSource().setValue("");
                    oEvent.getSource().setValueState("Error");
                    this.fireChange({
                        selectedItem: undefined
                    });
                }
            }
        });
    }
);

