{"welcomeFile": "/index.html", "authenticationMethod": "route", "websockets": {"enabled": true}, "routes": [{"source": "^(/Components/.*?)/sap/(.*)$", "target": "/sap/$2", "destination": "supernovat42", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/sap/(.*)$", "target": "/sap/$1", "destination": "supernovat42", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/services/userapi/attributes", "target": "user-api/currentUser", "service": "sap-approuter-userapi"}, {"source": "^/resources/(.*)$", "target": "/resources/$1", "authenticationType": "none", "destination": "ui5"}, {"source": "^/test-resources/(.*)$", "target": "/test-resources/$1", "authenticationType": "none", "destination": "ui5"}, {"source": "^(.*)$", "target": "$1", "service": "html5-apps-repo-rt", "authenticationType": "xsuaa"}], "whitelistService": {"endpoint": "/whitelist/service"}}