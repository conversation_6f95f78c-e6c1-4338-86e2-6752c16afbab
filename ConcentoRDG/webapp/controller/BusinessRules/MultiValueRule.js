sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"sap/m/MessageBox",
	"jquery.sap.global",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService"
], function (Utilities, MessageBox, jQuery, DMRDataService) {
	"use strict";
	let oMultiValueRuleModel = {};

    
	oMultiValueRuleModel.sendDeleteRequest = function(oController, oBusinessRuleModel, oData, bMainBusinessRules, sOperationType){
		let sEntity = sOperationType === "Validation" ? "YGW_ZEUS_BRF_VALIDATION_MODEL" : "YGW_ZEUS_BRF_DERIVE_MODEL";
		let promise = new Promise(function(resolve, reject){
			// Send a request for changing the status
			(new DMRDataService(
				oController,
				sEntity,
				"/MODELSet",
				undefined,
				"/", // Root of the received data
				"Multi Value Rule Delete"
			))
			.showBusyIndicator(true)
			.saveData(
				false,
				oData,
				null,
				{
					success:{ 
						fCallback: function (oParams, oDataResult) {
							let arrMessages = [];
							jQuery.extend(true, arrMessages, oDataResult.MODELTOMESSAGE.results);
							resolve(arrMessages, oDataResult);
						},
					},
					error:{ 
						fCallback: function () {
							reject();
						}
					}
				}
			);
		});

		promise.then(function(arrMessages){
			let bError = false; 
			// Sort the messages
			arrMessages.sort(function (m1) {
				if (m1.MessageType === "E"){
					return -1; 
				}
				if (m1.MessageType === "W") { return 1; }
				return 0;
			});

			// Check of error in the messages. Sort does not iterate if there is a single element
			// Hence a check is needed post the sort
			arrMessages.every((element) => {
				if(element.MessageType === "E"){
					bError = true;
					return false; 
				}

				return true;
			});
			
			// Throw an exception if an error was received
			if(bError) throw new Error(JSON.stringify(arrMessages));
			
			if(bMainBusinessRules){
	
				// Notify success state
				Utilities.sendNotification(
					oController, sap.ui.core.MessageType.Information, 
					oData.AppName + ": " + arrMessages[0].Message, 
					"Success.",
					undefined
				);
				oController.onSearch();
			} else{
				Utilities.showPopupAlert(arrMessages[0].Message, MessageBox.Icon.SUCCESS, "Success")                       
				.then(function (){
					oController.onClickLinkBusinessRules();
				});
			}
		})
		.catch(function(arrMessages){

			// Parse the JSON Error message 
			let arrMesgs = JSON.parse(arrMessages.message);

			Utilities.showPopupAlert("Error: " + arrMesgs[0].Message, MessageBox.Icon.ERROR, "Error");                        

		});

		return promise;

	};

	oMultiValueRuleModel.createDeletePayload = function (oController, oSelectedBusinessRule, bMainBusinessRules) {

		let sDraftAI = "";

		if(bMainBusinessRules){
			if(oSelectedBusinessRule.AiGenerated) {
				sDraftAI = "X";
			}
		} else {
			sDraftAI = oSelectedBusinessRule.RuleExpression.draftai;
		}

		let oPayload = {
			UsmdModel: oSelectedBusinessRule.DataModelId,
			UsmdCreqType: oSelectedBusinessRule.CRTypeId,
			UsmdEntity: oSelectedBusinessRule.EntityId,
			Delete: "X",
			AppName: oSelectedBusinessRule.BusinessRuleName,
			WbTransport: "",
			MODELTOMESSAGE: [],
			DraftAi: sDraftAI,
		};

		let promise = new Promise(function(resolve){
			if(sDraftAI === "X"){
				oPayload.CustTransport = "";
				resolve(oPayload);
			} else {
				oController._getSelectedTransportPackage(true, false, false)
				.then(function (oTransportPackageResponse) {
					oPayload.CustTransport = oTransportPackageResponse.customizingTransport;
					resolve(oPayload);
				});
			}
		});

		return promise;
	};

    oMultiValueRuleModel._getSelectedTransportPackage = function(oController, bCustomizing, bWorkbench, bPackage) {
		let oModel = oController.getView().getModel("BusinessRulesDetails");

		// get stored package and transports from the model 
		let oSelectedTransportPackage = oModel.getProperty("/selected");

		let promise = 
			oController._transportPackageSelectionDialog.open(
				bCustomizing, oSelectedTransportPackage?.transports?.customizing, 
				bWorkbench, oSelectedTransportPackage?.transports?.workbench, 
				bPackage, oSelectedTransportPackage?.package);
		
		let promiseReturn = promise.then(function(oTransporPackageResponse){
			if(bCustomizing){
				oModel.setProperty("/selected/transports/customizing", oTransporPackageResponse.customizingTransport);
			}

			if(bWorkbench){
				oModel.setProperty("/selected/transports/workbench", oTransporPackageResponse.workbenchTransport);
			}

			if(bPackage){
				oModel.setProperty("/selected/package", oTransporPackageResponse.package);
			}

			return oTransporPackageResponse;
		});
		

		return promiseReturn;
	};

    return oMultiValueRuleModel;
});