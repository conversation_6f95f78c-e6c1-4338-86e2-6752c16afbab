sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"sap/m/MessageBox",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService"
], function (Utilities, MessageBox, DMRDataService) {
	"use strict";
	let oCRStepTypePropModel = {};


	oCRStepTypePropModel.getBusinessRuleDetails = function (oController, oRouteParams) {
		// Get model 
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let {DataModelId, CRTypeId, CRStep} = oRouteParams;	
		let oParameters = {};

		if (CRStep) {
			oParameters = {
				"$expand": "SEARCHTOCHECKNAV,SEARCHTOMESSAGEOUTPUTNAV,SEARCHTOEXECUTIONNAV",
				"$filter": "UsmdModel eq '"+ DataModelId +"'",
				"$format": "json"
			};
			//Returns the CR type properties by step that could be assigned to the selected data model
			let promisePropsList = oCRStepTypePropModel.serviceCRTypeProps(oController, oParameters, "read", "/SearchSet");
			promisePropsList.then(function(results){
				if(results){
					oBusinessRulesDetailsModel.setProperty("/crStepTypeProps/MessageOutputList", results.SEARCHTOMESSAGEOUTPUTNAV.results);
					oBusinessRulesDetailsModel.setProperty("/crStepTypeProps/ExecutionList", results.SEARCHTOEXECUTIONNAV.results);
					oBusinessRulesDetailsModel.setProperty("/crStepTypeProps/EnrichmentList", results.SEARCHTOCHECKNAV.results);
				}
			});
		}
		
		oParameters = {
			"$expand": "CRSTEPTOENRICHNAV",
			"$filter": "UsmdCreqType eq '"+ CRTypeId +"' and Crstep eq '"+ CRStep +"'",
			"$format": "json"
		};
		
		//Returns current CR Type by step properties
		let promiseCRStepProps = oCRStepTypePropModel.serviceCRTypeProps(oController, oParameters, "read", "/CRSTEPSet");
		promiseCRStepProps.then(function(results){
			oBusinessRulesDetailsModel.setProperty("/crStepTypeProps/CRStepPropsList", results ? results.CRSTEPTOENRICHNAV.results : []);
		});


		return {
			DataModelId: DataModelId,
			DataModelDescription: "",
			CRTypeId: CRTypeId,
			CRTypeDescription: "",
			RuleTypeId: "6",
			RuleTypeDescription: "",
			BusinessRuleName: "CR Type Properties",
			UserRuleName: "",
			TransportId: "",
			CRStep: CRStep,
			lists: {}
		};
	};

	//Function to call the service both, to read and to create
	oCRStepTypePropModel.serviceCRTypeProps = function (oController, oParameters, type, entitySet){
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");

		if(type === "read"){
			let CRTypePropspromise = new Promise(function(resolve, reject) {
				(new DMRDataService(
					oController,
					"YGW_ZEUS_BRF_VALIDATION_MODEL",
					entitySet
				)).getData({
					success: {
						fCallback: function (oParams, oData) {
							resolve(oData.results[0]);
						},
						oParam: {
							"controller": oController
						}
					},
					error: {
						fCallback: function (oParams, oErrorData) {
							reject(oErrorData);
						},
						oParam: null
					}
				}, oParameters);
			});

			return CRTypePropspromise;
		}else{
			(new DMRDataService(
				oController,
				"YGW_ZEUS_BRF_VALIDATION_MODEL",
				entitySet
			)).saveData(false, oParameters, null, {
				success: {
					fCallback: function (oParams, oDataResult) {
						let arrMessages = [{Message: oDataResult.Message, MessageType: oDataResult.MessageType}];
						oController.ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
						oBusinessRulesDetailsModel.setProperty("/newRule", false);
						oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/BusinessRuleName", "CR Type Properties");
						oBusinessRulesDetailsModel.setProperty("/transport", undefined);
					},
					oParam: oController
				},
				error: function () {
					let promiseShowPopupAlert =
						Utilities.showPopupAlert("Change request type properties could not be updated.", MessageBox.Icon.INFORMATION, "Change Request Type Properties");
					promiseShowPopupAlert.then(function () {});
				}
			});

			return true;
		}
	};

	oCRStepTypePropModel.getCRTypePropsLists = function (oController){
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let sDataModelId = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/DataModelId");
		let oParameters = {
			"$expand": "SEARCHTOCHECKNAV,SEARCHTOMESSAGEOUTPUTNAV,SEARCHTOEXECUTIONNAV",
			"$filter": "UsmdModel eq '"+ sDataModelId +"'",
			"$format": "json"
		};
		let promisePropsList = oCRStepTypePropModel.serviceCRTypeProps(oController, oParameters, "read", "/SearchSet");
		promisePropsList.then(function(results){
			if(results){
				oBusinessRulesDetailsModel.setProperty("/crStepTypeProps/MessageOutputList", results.SEARCHTOMESSAGEOUTPUTNAV.results);
				oBusinessRulesDetailsModel.setProperty("/crStepTypeProps/ExecutionList", results.SEARCHTOEXECUTIONNAV.results);
				oBusinessRulesDetailsModel.setProperty("/crStepTypeProps/EnrichmentList", results.SEARCHTOCHECKNAV.results);
			}
		});
	};

	//Creates a Dialog to select CR Type Properties
	oCRStepTypePropModel.addCRTypeProps = function (){
		let oController = this;
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let enrichmentList = oBusinessRulesDetailsModel.getProperty("/crStepTypeProps/EnrichmentList");
		let CRStepPropsList = oBusinessRulesDetailsModel.getProperty("/crStepTypeProps/CRStepPropsList");
		let enrListToSelect = [];
		if (CRStepPropsList && CRStepPropsList.length > 0) {
			for(let i = 0; i < enrichmentList.length; i++){
				let elements = CRStepPropsList.filter(({Enrich}) => Enrich === enrichmentList[i].Name);
				if(elements.length === 0){
					enrListToSelect.push(enrichmentList[i]);
				}
			}
			oBusinessRulesDetailsModel.setProperty("/crStepTypeProps/EnrListToSelect", enrListToSelect);
		}else{
			oBusinessRulesDetailsModel.setProperty("/crStepTypeProps/EnrListToSelect", enrichmentList);
		}
		
		if(!this.crTypePropsToSelectDialog){
			this.crTypePropsToSelectDialog = new sap.m.Dialog({
				title: "Select CR Type Properties",
				beforeClose: function(){
					sap.ui.getCore().byId("idSearchFieldCRType").setValue("");
					sap.ui.getCore().byId("idSearchFieldCRType").fireLiveChange();
					sap.ui.getCore().byId("CRTypePropsList").removeSelections();
				},
				content: new sap.m.List("CRTypePropsList", {
					mode: "SingleSelect",
					includeItemInSelection: true,
					headerToolbar: new sap.m.OverflowToolbar({
						content: new sap.m.SearchField("idSearchFieldCRType", {
							width: "100%", 
							showSearchButton: false, 
							placeholder: "Select Properties",
							ariaDescribedBy: "Select Properties",
							ariaLabelledBy: "selProperties",
							liveChange: function(oEvent) {
								oCRStepTypePropModel.onSearchCRStepPropsList(oEvent);
							}
						})
					}),
					items: {
						path: "/crStepTypeProps/EnrListToSelect",
						template: new sap.m.StandardListItem({
							title: "{Name}",
							description: "{EnrichmentDescription}"
						})
					}

				}).setModel(oBusinessRulesDetailsModel),
				beginButton: new sap.m.Button({
					text: "Select", 
					press: function(oEvent){
						oCRStepTypePropModel.onSelectedCRTypeProps(oEvent, oController);
					}
				}),
				endButton: new sap.m.Button({
					text: "Cancel", 
					press: function(){
						oController.crTypePropsToSelectDialog.close();
					}
				})
			});
		}
		
		this.crTypePropsToSelectDialog.open();
	};

	oCRStepTypePropModel.onSelectedCRTypeProps = function(oEvent, oController){
		let EnhSelected = oEvent.getSource().getParent().getContent()[0].getSelectedItem();
		oEvent.getSource().getParent().getContent()[0].removeSelections();
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let aCRStepPropsList = oBusinessRulesDetailsModel.getProperty("/crStepTypeProps/CRStepPropsList");
		let Step = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/CRStep");
		let newEntry = {
			Crstep: Step,
			Enrich: EnhSelected.getTitle(),
			EnrichmentDescription: EnhSelected.getDescription(),
			Execution: "1",
			Messageoutput: "",
			Relevant: "X"
		};
		aCRStepPropsList.push(newEntry);
		oBusinessRulesDetailsModel.refresh();
		oController.crTypePropsToSelectDialog.close();
	};

	//Save step changes
	oCRStepTypePropModel.saveCRTypePropsConfiguration = function(oController){
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let { CRTypeId, CRStep } = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule");
		let aCRStepToEnrich = oBusinessRulesDetailsModel.getProperty("/crStepTypeProps/CRStepPropsList");
		if (aCRStepToEnrich.length === 0) {
			Utilities.showPopupAlert("Please enter at least one configuration", MessageBox.Icon.ERROR,
					"No properties found");
			return;
		}
		aCRStepToEnrich = aCRStepToEnrich.map(({Crstep, Enrich, Messageoutput, Relevant, Execution}) => {
			return {
				Crstep,
				Enrich,
				Messageoutput,
				Relevant,
				Execution
			};
		});
		oController._getSelectedTransportPackage(true, false, false)
		.then(function(oTransporPackageResponse){
			let oServerData = {
				"UsmdCreqType": CRTypeId,
				"Crstep": CRStep,
				"CRSTEPTOENRICHNAV": aCRStepToEnrich,
				"CustTransport" : oTransporPackageResponse.customizingTransport
			   };
	
			oCRStepTypePropModel.serviceCRTypeProps(oController, oServerData, "create", "/CRSTEPSet");
		
		});
	};

	oCRStepTypePropModel.onSelectRelevant = function(oEvent){
		let sPath = oEvent.getSource().getBindingContext("BusinessRulesDetails").getPath();
		let sValue = oEvent.getSource().getSelected();
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		oBusinessRulesDetailsModel.setProperty(sPath + "/Relevant", (sValue ? "X" : ""));
	};

	oCRStepTypePropModel.onSearchCRStepPropsList = function (oEvent) {
		// add filter for search
		let aFilters;

		// Get the search text
		let sQuery = oEvent.getSource().getValue();

		if (sQuery && sQuery.length > 0) {
			aFilters = new sap.ui.model.Filter({
				filters: [
					new sap.ui.model.Filter({
							path: "Name",
							operator: sap.ui.model.FilterOperator.Contains,
							value1: sQuery,
							caseSensitive: false
						}),
					new sap.ui.model.Filter({
							path: "EnrichmentDescription",
							operator: sap.ui.model.FilterOperator.Contains,
							value1: sQuery,
							caseSensitive: false
						})
				],
				and: false
			});
		}
		
		// update list binding
		sap.ui.getCore()
			.byId("CRTypePropsList")
			.getBinding("items")
			.filter(aFilters);
	};



	return oCRStepTypePropModel;
});