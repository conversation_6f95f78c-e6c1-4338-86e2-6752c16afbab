sap.ui.define([

], function () {
	"use strict";
	let ProcessFlowLibrary = {};

	/**
	 * Parse the process expression and return the data to display the Process Flow 
	 * 
	 * 	INVOKED FROM THE RULE FILES AS A HELPER FUNCTION (NOT AN EVENT HANDLER)
	 */

	ProcessFlowLibrary.setConditionArray = function (oCondStr, oFlowArr, oCurLane, oCurCond) {
		// Task 10260 - Provide a better User interface for the SIngle value derivation (Check Expression)
		oCondStr = oCondStr.toLowerCase().replaceAll(" ", "");
		oCondStr = oCondStr.toLowerCase().replaceAll("and", " and ");
		oCondStr = oCondStr.toLowerCase().replaceAll("or", " or ");

		let expArr = [];
		oCondStr.split(")").forEach(function(part){
			if(part !== ""){
				let firstIdx = part.indexOf("(");
				if(firstIdx === 0){
					expArr.push(part.slice(1, part.length));
				}else if(firstIdx > 0){
					part.split("(").forEach(function(part2){
						expArr.push(part2);
					});
				}else{
					expArr.push(part);
				}
			}
		});
		
		expArr.forEach(function(nodes){
			oFlowArr.lanes.push({
				"id": oFlowArr.lanes.length,
				"icon": "sap-icon://workflow-tasks",
				"label": "Condition: " + oFlowArr.lanes.length,
				"position": oFlowArr.lanes.length
			});
			let arrExpressions = nodes.split(" ");
			arrExpressions.forEach(function(expression){
				oCurLane = oFlowArr.lanes.length - 1;
				if (expression.match(/\<\d+\>/)) {
					let title;
					let stateText;
					let texts = [];
					if (oCurCond.nodes) {
						let oNode = oCurCond.nodes.find(function (el) {
							return el.id === (oFlowArr.nodes.length * 2).toString();
						});

						if (oNode) {
							title = oNode.title;
							stateText = oNode.stateText;
							texts = oNode.texts;

						}
					}
					oFlowArr.nodes.push({
						"id": (oFlowArr.nodes.length * 2).toString(),
						"lane": oCurLane,
						"title": title,
						"stateText": stateText,
						"texts": texts,
						"children": []
					});
				}
			});
		});

		expArr.forEach(function(nodes){
			let arrExpressions = nodes.split(" ");
			arrExpressions.forEach(function(expression){
				if(expression === "or" || expression === "and"){
					for(let idx = 0; idx < oFlowArr.nodes.length; idx++){
						if(oFlowArr.nodes[idx].children.length === 0){
							oFlowArr.nodes[idx].children.unshift({
								"nodeId": parseInt(oFlowArr.nodes[idx + 1].id, 10),
								"connectionLabel": {
									"id": "myButtonId" + idx + "To" + (idx + 1),
									"text": expression,
									"enabled": true,
									"state": "Neutral"
								}
							});
							break;
						}
					}
				}
			});
		});
		return oFlowArr;
	};

	return ProcessFlowLibrary;
});