sap.ui.define([
	"jquery.sap.global",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"sap/m/MessageBox"
], function (jQuery, DMRDataService, GetLists, Utilities, MessageBox) {
	"use strict";
	let oMandatoryRuleModel = {	};

    oMandatoryRuleModel.getBusinessRuleDetails = async function(oController, oRouteParams, bIsNewRule) {
        let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule;
		let oRuleExpression;

		// Initialize the request queue
		oController.mandatoryRequestQueue= {
			requests: []
		};

        if(!bIsNewRule) {
            let oBusinessRulesDetails = await GetLists.getBusinessRuleDetails(oController, oRouteParams.BusinessRuleName);
            oRuleExpression = JSON.parse(oBusinessRulesDetails[0].RuleExpr);
            oSelectedBusinessRule = {
                DataModelId: oBusinessRulesDetails[0].UsmdModel,
                DataModelDescription: oBusinessRulesDetails[0].ModelDesc,
                CRTypeId: oBusinessRulesDetails[0].UsmdCreqType,
                CRTypeDescription: oBusinessRulesDetails[0].CreqtypeDesc,
                EntityId: oBusinessRulesDetails[0].UsmdEntity,
                EntityDescription: oBusinessRulesDetails[0].EntityDesc,
                RuleTypeId: oBusinessRulesDetails[0].RuleType,
                RuleTypeDescription: "",
                BusinessRuleId: oBusinessRulesDetails[0].RuleId,
                BusinessRuleName: oBusinessRulesDetails[0].RuleName,
                UserRuleName: oBusinessRulesDetails[0].UserRuleName,
                TransportId: oBusinessRulesDetails[0].Transport,
                RuleExpression: oRuleExpression,
                lists: {}
            };
        } 
        else {
            oSelectedBusinessRule = oRouteParams;
        }

		// this.getTransportToSelect(oController);
        

        // Load the list of attributes for the entity 
		let promiseAttribList = GetLists.getEntityAttributeList(
			oController, 
			oSelectedBusinessRule.DataModelId, 
			oSelectedBusinessRule.EntityId, undefined);
			
		// Load the list of rules for the specified path 
		let promiseRuleList = GetLists.getBusinessRuleList(
			oController, 
			oSelectedBusinessRule.DataModelId, 
			oSelectedBusinessRule.EntityId,
			oSelectedBusinessRule.CRTypeId);
			
		Promise.all([promiseAttribList, promiseRuleList]).then(function(results){
			
			let arrAttrbList = results[0];
			let arrRuleList = results[1];
			
			oBusinessRulesDetailsModel.setProperty("/mandatory/attributeList", arrAttrbList);  
		
			if(bIsNewRule)
			{
				// Loop through the attributes and set the visible flag for the ones that 
				// do not have rules for them 
				arrAttrbList.forEach(function(attribute){
					// default to true .. set to false if a rule is found 
					attribute.visible = true;
					
					arrRuleList.forEach(function(rule){
						if(
							rule.RuleName.startsWith(oSelectedBusinessRule.CRTypeId) && 
							rule.RuleName.includes(oSelectedBusinessRule.EntityId) && 
							rule.RuleName.includes(attribute.UsmdAttribute) &&
							rule.RuleName.includes("MNDT") // Check mandatory rules only
						)	
						{
							attribute.visible = false;
						}
					});
					
				});
				

				// Filter the attribute that should not be shown.
				let arrFilteredAttrbs = arrAttrbList.filter(attr => attr.visible !== false);

				oBusinessRulesDetailsModel.setProperty("/mandatory/attributeList", 
					//arrAttrbList
					arrFilteredAttrbs
				);
				return;
			}
			
			// If it is an existing rule, update the attribute value
			
			// Parse the expression and set the mandatory status 
            let oMandatoryRule = oRuleExpression.modeltobrfnav[0].brftoattrnav[0];
			let bMandatoryState = oMandatoryRule.mandt === "YES"? true: false;
			oBusinessRulesDetailsModel.setProperty("/mandatory/Attribute", oRuleExpression.modeltobrfnav[0].brftoattrnav[0].usmdAttribute);
			oBusinessRulesDetailsModel.setProperty("/mandatory/State", bMandatoryState); 
			
	
			// Loop through the attributes and set to visible the attribute marked in this rule 
			arrAttrbList = arrAttrbList.filter(function(attribute){
				attribute.visible = true;
				attribute.state = bMandatoryState;
				if(attribute.UsmdAttribute === oMandatoryRule.usmdAttribute) return true;	
			});
			oBusinessRulesDetailsModel.setProperty("/mandatory/attributeList", arrAttrbList);
		});

        return oSelectedBusinessRule;
    };

	oMandatoryRuleModel.createPayload = function(oController, oSelectedBusinessRule, sAttribute, sSwitchState, bDelete = false){
		
		// Build the packet to be sent to the server 
		let oDataToWrite = {
			"CustTransport": undefined,
			"UsmdCreqType": oSelectedBusinessRule.CRTypeId,
			"UsmdModel": oSelectedBusinessRule.DataModelId,
			"modeltobrfNav": [
				{
					"UsmdModel": oSelectedBusinessRule.DataModelId,
					"UsmdEntity": oSelectedBusinessRule.EntityId,
					"brftoattrNav": [
						{	
							"UsmdModel": oSelectedBusinessRule.DataModelId,
							"UsmdAttribute": sAttribute,
							"Mandt": sSwitchState,
							"Delete": bDelete ? "X" : ""
						}
					]
				}
			],
			"modeltomessageNav": [],
			"DraftAi": oSelectedBusinessRule.AiGenerated ? "X" : ""
		};

		// Get the transport and assign to the packet. Then queue the request for further processing
		let promiseTransport = GetLists.getBRFTransport(oController, "Mandatory Rule", oSelectedBusinessRule.EntityId, oSelectedBusinessRule.CRTypeId);
		return promiseTransport.then(function(sSelectedTransport){
			oDataToWrite.CustTransport = sSelectedTransport;
			return oDataToWrite;
		});
	};

	oMandatoryRuleModel.sendRequest = function(oController, bShowProgress, oData, oSwitchComponent, sOperationType){
		let promise = new Promise(function(resolve, reject){
			// Send a request for changing the status
			(new DMRDataService(
				oController,
				"CREATEBRF",
				"/MODELSet",
				undefined,
				"/", // Root of the received data
				"Mandatory Rule Update"
			))
			.showBusyIndicator(bShowProgress)
			.saveData(
				false,
				oData,
				null,
				{
					success:{ 
						fCallback: function (oParams, oDataResult) {
							let arrMessages = [];
							jQuery.extend(true, arrMessages, oDataResult.modeltomessageNav.results);
							resolve(arrMessages);
						},
					},
					error:{ 
						fCallback: function () {
							reject();
						}
					}
				}
			);
		});

		promise.then(function(arrMessages){
			let bError = false; 
			// Sort the messages
			arrMessages.sort(function (m1) {
				if (m1.MessageType === "E"){
					return -1; 
				}
				if (m1.MessageType === "W") { return 1; }
				return 0;
			});

			// Check of error in the messages. Sort does not iterate if there is a single element
			// Hence a check is needed post the sort
			arrMessages.every((element) => {
				if(element.MessageType === "E"){
					bError = true;
					return false; 
				}

				return true;
			});
			
			// Throw an exception if an error was received
			if(bError) throw new Error(JSON.stringify(arrMessages));

			if(!sOperationType.includes("Main") && sOperationType.includes("Delete")){
				Utilities.showPopupAlert(arrMessages[0].Message, MessageBox.Icon.SUCCESS, "Success")                       
				.then(function (){
					oController.onClickLinkBusinessRules();
				});
			} else{
	
				// Notify success state
				Utilities.sendNotification(
					oController, sap.ui.core.MessageType.Information, 
					oData.modeltobrfNav[0].brftoattrNav[0].UsmdAttribute + ": " + arrMessages[0].Message, 
					"Success.",
					undefined
				);

				if(sOperationType.includes("Main")){
					oController.onSearch();
				}
			}
		})
		.catch(function(arrMessages){
			
			if(sOperationType.includes("Save")){
				// Revert the status of the switch state on error 
				let bSwitchState = oSwitchComponent.getState();
				oSwitchComponent.setState(!bSwitchState);
			}

			// Parse the JSON Error message 
			let arrMesgs = JSON.parse(arrMessages.message);

			Utilities.showPopupAlert("Error: " + arrMesgs[0].Message, MessageBox.Icon.ERROR, "Error");                        

		});

		return promise;
	};

    oMandatoryRuleModel.onChangeMandatoryStatus = function(oEvent){
		let oController = this;
		let oSwitchComponent = oEvent.getSource();
		let sSwitchState = oSwitchComponent.getState()? "YES" : "NO";
		let bShowProgress = false;
		let oCustomData = oSwitchComponent.getCustomData();

		if(oCustomData[0]?.getValue() === true){
			bShowProgress = true;
		}

		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule");
		let sAttribute = oSwitchComponent.getName();
		

		oSwitchComponent.setBusy(true).setBusyIndicatorDelay(0);
		oController.setBusyStatus(oController, true);

		oController.MandatoryRuleDialog.createPayload(oController, oSelectedBusinessRule, sAttribute, sSwitchState)
		.then(function (oDataToWrite){

			// add the request to the queue
			oController.mandatoryRequestQueue.requests.push({
				clickedElement: oSwitchComponent,
				dataToWrite: oDataToWrite
			});
	
			oBusinessRulesDetailsModel.setProperty("/mandatory/pendingRequestCount", oController.mandatoryRequestQueue.requests.length);
		})
		.catch(function(){
			oSwitchComponent.setBusy(false);
		})
		.finally(function(){
			// Continue with next request 
			oController.MandatoryRuleDialog.sendQueuedRequest(oController, bShowProgress);
		});
	};

	oMandatoryRuleModel.sendQueuedRequest = function(oController, bShowProgress){

		let oModel = oController.getView().getModel("BusinessRulesDetails");
		
		// Get the request queue
		let arrRequestQueue = oController.mandatoryRequestQueue.requests;
		// if there are no requests pending or if a request is being processed, return with no action
		if(arrRequestQueue.length === 0 || arrRequestQueue[0].inProgress === true){
			oController.setBusyStatus(oController, arrRequestQueue.length === 0? false : true);
			return; // No action to perform, wait for new requests or the current action to complete.
		}

		// set the first element in the queue to inProgress and set the request
		arrRequestQueue[0].inProgress = true;
		let oSwitchComponent = arrRequestQueue[0].clickedElement;
		// let sAttribute = oSwitchComponent.getName();
		let oData = arrRequestQueue[0].dataToWrite;

		let promise = oController.MandatoryRuleDialog.sendRequest(oController, bShowProgress, oData, oSwitchComponent, "Save");
		promise.finally(function(){
			oSwitchComponent.setBusy(false);
			// remove the first element (Currently processed element)
			arrRequestQueue.shift();

			oModel.setProperty("/mandatory/pendingRequestCount", 
				oController.mandatoryRequestQueue.requests.length);

			// Continue with next request 
			oController.MandatoryRuleDialog.sendQueuedRequest(oController, bShowProgress);
		});
	};

	oMandatoryRuleModel.deleteRule = function(oController) {
		let oThisObject = this;
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule");
		let oMandatoryDetails = oBusinessRulesDetailsModel.getProperty("/mandatory");
		let sSwitchState = oMandatoryDetails.State ? "YES" : "NO";

		oThisObject.createPayload(oController, oSelectedBusinessRule, oMandatoryDetails.Attribute, sSwitchState, true)
		.then(function(oDataToWrite){
			oThisObject.sendRequest(oController, true, oDataToWrite, {}, "Delete");
		});
	};

return oMandatoryRuleModel;
});