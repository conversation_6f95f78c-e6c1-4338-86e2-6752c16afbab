sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"sap/m/MessageToast"
], function (GetListsModel, MessageToast) {
	"use strict";
	let NodeEdit = {};
	/**
	 * Open the Node Edit dialog with the initial data
	 *
	 * CALLED FROM DIFFERENT MODULE. RUNS IN ITS OWN CONTEXT
	 */
	NodeEdit.openNodeEditDialog = function (oParentController, oNodeEditDetails) {
		// Get model
		let oController = this;
		let oBusinessRulesDetailsModel = oParentController.getView().getModel("BusinessRulesDetails");
        let oSelectedBusinessRule = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule");
		
		// Initialize the Node Edit Model Data
		oController.NodeEditFragmentId = oParentController.NodeEditFragmentId;
		oBusinessRulesDetailsModel.setProperty("/NodeEditDetails", {});
		oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData", oNodeEditDetails);
		oBusinessRulesDetailsModel.setProperty("NodeEditDetails/nodeEditData/selectedValueType", -1);
		oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditToValueList", "");
		oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditFromValueList", "");
		// Fix 12540 - Deleting request because fragment is already receiving complete values
		oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputToValueData", oNodeEditDetails.inputToValue);
		oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValueData", oNodeEditDetails.inputFromValue);

		//Create the fragment to be called
		let oNodeEditDialog = oController.getNodeEditFragment(oParentController);
		
		// Fill the entity list
		// Bug 12199 - Field Property Shows Entities which are not part of the CR type - 0G
		let promiseEntityList = GetListsModel.getModelEntityList(oParentController, oSelectedBusinessRule.DataModelId, "UsmdModel", oSelectedBusinessRule.CRTypeId, null, true);
		promiseEntityList.then(function (oEntityList) {
			oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditEntityList", oEntityList);
			
			/* If there is a previous selection for Entity, trigger the selection Change event on the combobox */
			let oEntityComboBox = sap.ui.getCore().byId(oController.NodeEditFragmentId + "--idCBoxEntities");
			// Force Display the list content with the updated data
			oEntityComboBox.syncPickerContent();
			// trigger the selection change event if an item was selected
			let oSelectedEntity = oEntityComboBox.getSelectedItem();
			if (oSelectedEntity) {
				oEntityComboBox.fireSelectionChange({
					selectedItem: oSelectedEntity,
					entitySelectionChange: false
				});
			}

			/* If there is a previous selection for Entity Value, trigger the selection Change event on the combobox */
			let oEntityValueComboBox = sap.ui.getCore().byId(oController.NodeEditFragmentId + "--idValueTypeEntity");
			// Force Display the list content with the updated data
			oEntityValueComboBox.syncPickerContent();
			// trigger the selection change event if an item was selected
			let oSelectedEntityValue = oEntityValueComboBox.getSelectedItem();
			if (oSelectedEntityValue) {
				oEntityValueComboBox.fireSelectionChange({
					selectedItem: oSelectedEntityValue,
					entitySelectionChange: false
				});
			}

			sap.ui.getCore().byId(oController.NodeEditFragmentId + "--idInputFromValueSugg").setValueState("Information");
			sap.ui.getCore().byId(oController.NodeEditFragmentId + "--idInputFromValueSugg").setValueStateText("Type to refresh list");
			sap.ui.getCore().byId(oController.NodeEditFragmentId + "--idInputToValueSugg").setValueState("Information");
			sap.ui.getCore().byId(oController.NodeEditFragmentId + "--idInputToValueSugg").setValueStateText("Type to refresh list");
		});
		oNodeEditDialog.open();
		

	};

	NodeEdit.getNodeEditFragment = function(oController) {
		if (!this._oNodeEditFrgmt) {
			this._oNodeEditFrgmt = sap.ui.xmlfragment(
				this.NodeEditFragmentId,
				"dmr.mdg.supernova.SupernovaFJ.view.BusinessRules.NodeEdit",
				oController
			);

			oController.getView().addDependent(this._oNodeEditFrgmt);
		}

		return this._oNodeEditFrgmt;
	};

	/**
	 * Invoved as a handler to the Node Edit Dialog - Entity selection change event
	 *
	 * INVOKED IN THE CONTEXT OF THE CONTROLLER. this = CONTROLLER
	 */
	NodeEdit.onNodeEditEntitySelectionChange = function (oEvent) {
		let sSelectedEntity = oEvent.getSource().getSelectedKey();
		let bentitySelectionChange = oEvent.getParameters().entitySelectionChange;
		let oEntityComboBox = oEvent.getParameter("id");
		let oController = this;
		// Get model
		let oBusinessRuleModel = oController.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule = oBusinessRuleModel.getProperty("/selectedBusinessRule");

		// Retrieve the data model
		let sDataModel = oSelectedBusinessRule.DataModelId;

		let promiseAttributeList =
			GetListsModel.getEntityAttributeList(this, sDataModel, sSelectedEntity, undefined);
		promiseAttributeList.then(function (oAttributes) {
			let oAttributeComboBox;
			//Update the attribute combobox only for its corresponding entity
			if(oEntityComboBox === oController.NodeEditFragmentId + "--idCBoxEntities" ) {
				oAttributeComboBox = sap.ui.getCore().byId(oController.NodeEditFragmentId + "--idCBoxAttributes");

				oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditAttributeList", oAttributes);

				if (bentitySelectionChange === undefined) {
					oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/selectedAttribute", "");
				}
			} else if(oEntityComboBox === oController.NodeEditFragmentId + "--idValueTypeEntity" ) {
				oAttributeComboBox = sap.ui.getCore().byId(oController.NodeEditFragmentId + "--idValueTypeAttribute");
				oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditAttributeValueList", oAttributes);
				if (bentitySelectionChange === undefined) {
					oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/selectedAttributeValue", "");
				}
			}
			
			/* If there is a previous selection for Attribute, trigger the selection Change event on the combobox */
			// Force Display the list content with the updated data
			oAttributeComboBox.syncPickerContent();
			// trigger the selection change event if an item was selected
			let oSelectedAttribute = oAttributeComboBox.getSelectedItem();
			oAttributeComboBox.fireSelectionChange({
				selectedItem: oSelectedAttribute,
				attributeSelectionChange: false
			});
		});
	};

	/**
	 * Invoved as a handler to the Node Edit Dialog - Attribute selection change event
	 *
	 * INVOKED IN THE CONTEXT OF THE CONTROLLER. this = CONTROLLER
	 */
	NodeEdit.onNodeEditAttributeSelectionChange = function (oEvent) {
		sap.ui.getCore().byId(this.NodeEditFragmentId + "--save").setEnabled(true);
		let oController = this;
		let sSelectedAttribute = oEvent.getSource().getSelectedKey();
		let bAttributeSelectionChange = oEvent.getParameters().attributeSelectionChange;
		//Get Model
		let oBusinessRuleModel = oController.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule = oBusinessRuleModel.getProperty("/selectedBusinessRule");
		let oAttributes = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditAttributeList");
		if(bAttributeSelectionChange === undefined){
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValue", "");
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputToValue", "");
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValueData", "");
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputToValueData", "");
		}

		let sDataModel = oSelectedBusinessRule.DataModelId;
		let sEntity = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditData/selectedEntity");

		if (sSelectedAttribute) {
			let {Length} = oAttributes.find(attr => attr.UsmdAttribute === sSelectedAttribute);
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/ElementLength", (Length) ? Number(Length) : 0);
			
			// Bug 11300 - List items for MARCATP - MTVFP incorrect in MG1
			sap.ui.core.BusyIndicator.show();
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditToValueList", "");
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditFromValueList", "");
			let promiseValueList = GetListsModel.getAttributeValuesList(oController, sDataModel, sEntity, sSelectedAttribute, undefined);
			promiseValueList.then(function (oValueList) {
				oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditToValueList", oValueList);
				oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditFromValueList", oValueList);
				sap.ui.core.BusyIndicator.hide();
			});

			let promiseElementType =
			GetListsModel.getElementType(oController, sDataModel, sEntity, sSelectedAttribute);
			promiseElementType.then(function(oElementType) {
				oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/ElementType", oElementType.Elementtype);

				// Fill the operand list
				let arrBRFOperands = GetListsModel.getBRFOperands(oController, "OrdinalComparisons");
				if(oElementType.Elementtype === "Q" || oElementType.Elementtype === "A"){
					arrBRFOperands = arrBRFOperands.filter(operand => (operand.id === "NE" || operand.id === "LE" || operand.id === "LT"));
				}
				oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditComparatorList", arrBRFOperands);
				let arrBRFAttributeOperands = arrBRFOperands.filter(function(oOperand) {
					return oOperand.id !== "BT" && oOperand.id !== "NB";
				});
				oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditAttributeComparatorList", arrBRFAttributeOperands);

				if(oElementType.Elementtype === "Q") {
					let promiseDimensionsList = GetListsModel.getDimensionsList(oController);
					promiseDimensionsList.then(function(oDimensionsList) {
						oBusinessRuleModel.setProperty("/NodeEditDetails/dimensionsList", oDimensionsList);

						/* If there is a previous selection for Attribute, trigger the selection Change event on the combobox */
						let oDimensionComboBox = sap.ui.getCore().byId(oController.NodeEditFragmentId + "--idDimension");
						// Force Display the list content with the updated data
						oDimensionComboBox.syncPickerContent();
						// trigger the selection change event if an item was selected
						let oSelectedDimension = oDimensionComboBox.getSelectedItem();

						//10927 - Take care of units in badi
						//Find a match between the dimension on the list and the one in the API response, 
						//and setting to be displayed automatically in the field.
						let oMatchedDimension = oDimensionsList.find((oMatchDimension => {
							return oMatchDimension.Dimension === oElementType.Dimension;
						}));
					
						if(oMatchedDimension){
							let oSelectedDimen = oMatchedDimension.Dimension;
							oDimensionComboBox.setSelectedKey(oSelectedDimen);
							oDimensionComboBox.fireSelectionChange({
								selectedItem: oSelectedDimen
							});	
							oDimensionComboBox.setEnabled(false);
						} else {
							oDimensionComboBox.fireSelectionChange({
								selectedItem: oSelectedDimension
							});	
							oDimensionComboBox.setEnabled(true);
						}
					});
				} else if(oElementType.Elementtype === "A") {
					let promiseUnitsList = GetListsModel.getUnitsList(oController, undefined, oElementType.Elementtype);
					promiseUnitsList.then(function(oUnitsList) {
						oBusinessRuleModel.setProperty("/NodeEditDetails/UnitsList", oUnitsList);
					});
				} 
				
				if(oElementType.Elementtype === "P" && oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditToValueList").length === 0){
					let sToValue = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditData/inputToValue");
					let sFromValue = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditData/inputFromValue");

					if(sToValue === "Current Date" || sToValue === "-1"){
						oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/ebDatePickerToValue", false);
						oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/currentDateCheckToValue", true);
					} else {
						oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/ebDatePickerToValue", true);
						oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/currentDateCheckToValue", false);
					}

					if(sFromValue === "Current Date" || sFromValue === "-1"){
						oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/ebDatePickerFromValue", false);
						oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/currentDateCheckFromValue", true);
					} else {
						oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/ebDatePickerFromValue", true);
						oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/currentDateCheckFromValue", false);
					}
				}
			});
		}
		else
		{
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValue", "");
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputToValue", "");
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditToValueList", "");
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditFromValueList", "");
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/selectedDimension", "");
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/selectedUnit", "");
		}

	};

	NodeEdit.onNodeEditDimensionChange = function (oEvent) {
		let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");
		let sSelectedDimension = oEvent.getSource().getSelectedKey();
		let sElementType = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditData/ElementType");

		if(sSelectedDimension) {
			let promiseUnitsList = GetListsModel.getUnitsList(this, sSelectedDimension, sElementType);
			promiseUnitsList.then(function(oUnitsList) {
				oBusinessRuleModel.setProperty("/NodeEditDetails/UnitsList", oUnitsList);
			});
		} else {
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/selectedUnit", "");
		}

	};

	NodeEdit.onNodeEditComparisonChange = function () {
		let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");
		oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValue", "");
		oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputToValue", "");
		oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValueData", "");
		oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputToValueData", "");
	};

	NodeEdit.onLiveChangeToValue = function (oEvent) {
		let oInput = oEvent.getSource();
		let sValue = oEvent.getParameter("value");
		// Bug 12854 - added conditional to validate the value and the selected Item
		clearTimeout(this.timeout);
		this.timeout = setTimeout(() => {
			let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");
			let oSelectedBusinessRule = oBusinessRuleModel.getProperty("/selectedBusinessRule");
			let sDataModel = oSelectedBusinessRule.DataModelId;
			let sEntity = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditData/selectedEntity");
			let sAttribute = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditData/selectedAttribute");
			let aList = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditToValueList");
			if (aList.length > 0) {
				let promiseValueList =
					GetListsModel.getAttributeValuesList(this, sDataModel, sEntity, sAttribute, sValue);
				promiseValueList.then(function (oValueList) {
					if(oValueList.length > 0){
						oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditToValueList", oValueList);
					}
					oInput.fireChange({value: sValue});
				});
			}
		}, 300);
	};

	NodeEdit.onLiveChangeFromValue = function (oEvent) {
		let oInput = oEvent.getSource();
		let sValue = oEvent.getParameter("value");
		clearTimeout(this.timeout);
		this.timeout = setTimeout(() => {
			let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");
			let oSelectedBusinessRule = oBusinessRuleModel.getProperty("/selectedBusinessRule");
			let sDataModel = oSelectedBusinessRule.DataModelId;
			let sEntity = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditData/selectedEntity");
			let sAttribute = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditData/selectedAttribute");
			let aList = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditFromValueList");
			if (aList.length > 0) {
				let promiseValueList =
					GetListsModel.getAttributeValuesList(this, sDataModel, sEntity, sAttribute, sValue);
				promiseValueList.then(function (oValueList) {
					if (oValueList.length > 0) {
						oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditFromValueList", oValueList);
					}
					oInput.fireChange({value: sValue});
				});
			}
		}, 300);
	};

	/**
	 * Task 11217 - Value Conversion and Data Check for Single value rules and properties
	 * This function gets called on change event of Value field and validates the input types
	 */
	NodeEdit.onChangeValue = function (oEvent) {
		// Bug 12854 - added conditional to validate the value and the selected Item
		let oInput = oEvent.getSource();
		let sValue = oEvent.getParameter("value");
		let aSuggestionItems = oInput.getSuggestionItems();
		if (aSuggestionItems.length > 0) {
			let oSelectedItem = aSuggestionItems.find(item => item.getText() === sValue);
			if(!sValue){
				oInput.setValueState("Information");
				oInput.setValueStateText("Type to refresh list");
			}else if (sValue && !oSelectedItem) {
				oInput.setValueState("Warning");
				oInput.setValueStateText("The selected value is not in the list");
			} 
		}
		let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");
		let sAttribute = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditData/selectedAttribute");
		//Bug 12571 - Get the attribute list for the entity which has been selected by the user
		let arrAttributeList = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditAttributeList");
		let oSelectedAttributeDetails = arrAttributeList.find((oAttribute => {
			return oAttribute.UsmdAttribute === sAttribute;
		}));
		
		let oColumnInfo = {
			"entity": oSelectedAttributeDetails.UsmdEntity,
			"SelectedLengthComparison": false,
			"attribute": oSelectedAttributeDetails.UsmdAttribute,
			"attributeType": "Driving",
			"attributeDataType": oSelectedAttributeDetails.DATATYPE,
			"attributeKind": oSelectedAttributeDetails.Kind,
			"attributeDecimals": oSelectedAttributeDetails.Decimals,
			"attributeDataLenght": oSelectedAttributeDetails.Length,
			// Bug 12606 - Comparator was added to be avaluated in _changeCellValue
			"comparator": oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditData/selectedComparator")
		};
		
		NodeEdit._changeCellValue(oEvent, oColumnInfo);
	};

	/** 
	 * Task 11217 - Value Conversion and Data Check for Single value rules and properties
	 * Condition "N" was modified to not allow letters and spetial characters
	 * Condition "P" was modified to not allow NaN value
	 * Condition "I" was added to rounded the Int values
	 * Condition "F" was added to compare the value with a regular expression 
	 * Message was added to inform about wrong value and when this occurs the field is cleaned
	 */
	NodeEdit._changeCellValue = function (oEvent, oColumnInfo) {
		let oInput = oEvent.getSource();
		let sValue = oInput.getValue();
		let aSuggestionItems = oInput.getSuggestionItems();
		// Bug 13336 - Check if field has suggestions to decide which validation corresponds
		if (oInput.getValueState() !== "None" && aSuggestionItems.length > 0) {
			let oSelectedItem = aSuggestionItems.find(item => item.getText() === sValue);
			// Bug 12854 - added conditional to validate the value and the selected Item
			if (sValue && !oSelectedItem) {
				oInput.setValueState("Warning");
				oInput.setValueStateText("The selected value is not in the list");
			}else{
				oInput.setValueState("Information");
				oInput.setValueStateText("Type to refresh list");
			}
			
		}else{
			let sOperator = oColumnInfo.comparator;
			let regexNum = "^[0-9]+$";
			let regexFLTP = "(\\+|\\-|\\.|[0-9]|(e|E))(\\+|\\-|\\.|[0-9]|(e|E))*";
			// Bug 12606 - Regex for Pattern were added
			let regexNumPattern = "^(\\*?[0-9]+\\*?)+$";
			let regexDecPattern = "^(\\*?\\.?[0-9]+\\.?\\*?)+$";
			let regexFLTPPattern = "^(\\*?[+-]?\\.?[0-9]*\\*?([+-]?(e|E))?\\*?)+$";
			let sCellValue = oEvent.getParameter("value");
			let iCellValue = Number(sCellValue);
			let flagInvalid = false;
			if(oColumnInfo.attributeKind === "C") {
				oEvent.getSource().setValue(sCellValue.toUpperCase());
			} else if(oColumnInfo.attributeKind === "N") {
				// Bug 12606 - Conditions were added to evaluate if the selected operator was CP or NP and use other regex which allows asterisks
				if (sOperator === "CP" || sOperator === "NP") {
					if(!sCellValue.match(regexNumPattern) && sCellValue.trim() !== ""){
						oEvent.getSource().setValue();
						flagInvalid = true;
					}
				}else{
					if(iCellValue.toFixed(0).toString().match(regexNum) && sCellValue.trim() !== ""){
						oEvent.getSource().setValue(iCellValue.toFixed(0).toString().padStart(Number(oColumnInfo.attributeDataLenght), 0));
					}else{
						oEvent.getSource().setValue();
						flagInvalid = true;
					}
				}
			} else if(oColumnInfo.attributeKind === "P") {
				if (sOperator === "CP" || sOperator === "NP") {
					if(!sCellValue.match(regexDecPattern) && sCellValue.trim() !== ""){
						oEvent.getSource().setValue();
						flagInvalid = true;
					}
				}else{
					if(isNaN(iCellValue) || sCellValue.trim() === ""){
						oEvent.getSource().setValue();
						flagInvalid = true;
					}else{
						let cellValueFixed = iCellValue.toFixed(Number(oColumnInfo.attributeDecimals)).toString().replace(".", "");
						// Task 11217 - Value Conversion and Data Check for Single value rules and properties
						// This fixed the bug for BP_SALES.ANTLF Attribute, which is Decimal with lenght 1
						if (cellValueFixed.length === 1 && Number(oColumnInfo.attributeDataLenght) === 1) {
							oEvent.getSource().setValue(Number(iCellValue));
						} else if(cellValueFixed.length >= Number(oColumnInfo.attributeDataLenght)) {
							// Bug 12157 - Input validation for BP_COMPNY - KULTG
							// Only apply this formula if the data type is decimal and oColumnInfo.attributeDecimals is greater than zero
							if (Number(oColumnInfo.attributeDecimals) > 0) {	
								let newValue = cellValueFixed.slice(0, (oColumnInfo.attributeDataLenght - (Number(oColumnInfo.attributeDecimals) + 1))) + "."
												+ cellValueFixed.slice((oColumnInfo.attributeDataLenght - (Number(oColumnInfo.attributeDecimals) + 1)), oColumnInfo.attributeDataLenght - 1);
								oEvent.getSource().setValue(Number(newValue).toFixed(Number(oColumnInfo.attributeDecimals)));
							}
						}else{
							oEvent.getSource().setValue(iCellValue.toFixed(Number(oColumnInfo.attributeDecimals)));
						}
					}
				}
			} else if(oColumnInfo.attributeKind === "I" || oColumnInfo.attributeKind === "D" || oColumnInfo.attributeKind === "T") {
				if(iCellValue.toFixed(0).toString().match(regexNum) && sCellValue.trim() !== ""){
					oEvent.getSource().setValue(iCellValue.toFixed(0));
				}else{
					oEvent.getSource().setValue();
					flagInvalid = true;
				}
			} else if(oColumnInfo.attributeKind === "F"){
				if(((sOperator === "CP" || sOperator === "NP") && sCellValue.match(regexFLTPPattern)) || sCellValue.match(regexFLTP) && sCellValue.trim() !== ""){
					oEvent.getSource().setValue(sCellValue.toUpperCase());
				}else{
					oEvent.getSource().setValue();
					flagInvalid = true;
				}
			}
			
			// Bug 13336 - Removed setValueState methods to avoid setting wrong states
			if (flagInvalid && oEvent.getParameter("value")) {
				oEvent.getSource().setValue("");
				// Bug 13336 - Added MessageToast to tell the user when a value is incorrect
				MessageToast.show("Enter a valid value", {
					at: "center center"
				});
			}
		}
	};

	// Validation function of format value
	NodeEdit.onChangeDate = function (oEvent) {
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let	selectedComparator = oBusinessRulesDetailsModel.getProperty("/NodeEditDetails/nodeEditData/selectedComparator"),
			inpDPFrom = sap.ui.getCore().byId(this.NodeEditFragmentId + "--idDatePFromValue"),
			inpDPTo = sap.ui.getCore().byId(this.NodeEditFragmentId + "--idDatePToValue"),
			btnSave = sap.ui.getCore().byId(this.NodeEditFragmentId + "--save"),
			oSource = oEvent.getSource();

			oSource.setValueState((oSource.isValidValue()) ? "None" : "Error");

		if(selectedComparator === "BT"){
			if(inpDPFrom.isValidValue() && inpDPTo.isValidValue()){
				btnSave.setEnabled(true);
				oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputToValueData", inpDPTo.getValue());
				oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputToValue", inpDPTo.getValue());
				oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValueData", inpDPFrom.getValue());
				oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValue", inpDPFrom.getValue());
			} else {
				btnSave.setEnabled(false);
			}
		}else{
			if(inpDPTo.isValidValue()){
				btnSave.setEnabled(true);
				oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputToValueData", inpDPTo.getValue());
				oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputToValue", inpDPTo.getValue());
				oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValueData", inpDPFrom.getValue());
				oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValue", inpDPFrom.getValue());
			} else {
				btnSave.setEnabled(false);
			}
		}
	};

	// RDG-12: event that sets the datepicker value and enables depending on the checbox status
	NodeEdit.onCurrentDateSelect = function (oEvent){
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let bCheckboxStatus = oEvent.getSource().getSelected();
		let sSource = oEvent.getSource().getId().includes("To") ? "To" : "From";

		oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/ebDatePicker" + sSource + "Value", !bCheckboxStatus);
		
		if(bCheckboxStatus){
			oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/input" + sSource + "ValueData", "-1");
			oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/input" + sSource + "Value", "-1");
		} else {
			oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/input" + sSource + "ValueData", "");
			oBusinessRulesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/input" + sSource + "Value", "");
		}
	};

	/**
	 * Invoved as a handler to the Node Edit Dialog - Save Dialog Operation
	 *
	 * INVOKED IN THE CONTEXT OF THE CONTROLLER. this = oController
	 */
	NodeEdit.onNodeEditSave = function () {
		// Get model
		let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");

		// Initialize the Node Edit Model Data
		let oNodeDetails = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditData");
		//Bug 12118 - Multiple words for attribute value not saved in single value rules
		let oNodeValueList = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditToValueList");
		//end Bug 12118
		let sInputToValue = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditData/inputToValueData");
		let sInputFromValue = oBusinessRuleModel.getProperty("/NodeEditDetails/nodeEditData/inputFromValueData");
		let iIndex;

		//Bug 12118 - Multiple words for attribute value not saved in single value rules
		if (oNodeValueList.length !== 0 && oNodeDetails.ElementType !== "P"){
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputToValue", sInputToValue);
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValue", sInputFromValue);
		}
		else if(oNodeValueList.length === 0 && oNodeDetails.ElementType === "P"){
			if(sInputFromValue){
				iIndex = sInputFromValue.indexOf(" ");
				if(iIndex > 0){
					sInputFromValue = sInputFromValue.substring(0, iIndex);
				}
				oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValue", sInputFromValue);
			}
			if(sInputToValue === "Current Date"){
				oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputToValue", "-1");
			}
		} else{
			//Bug 12572 - Reset node value should be allowed
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputToValue", sInputToValue);
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValue", sInputFromValue);
		}
		//end Bug 12118


		// Update the details to the node / process flow
		this.SingleValueRuleDialog.updateNodeData(this, oNodeDetails);

		// Close the dialog
		let oNodeEditDialog = this.NodeEditDialog.getNodeEditFragment();
		oNodeEditDialog.close();
	};
	//11890 - Error in creating single validation/derivation business rules for more than two entities
	//Values are assigned to the fields Entity and Attribute based on the parent
	NodeEdit.onAttrList = function (oEvent){
		let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");
		let sSelectedIndex = oEvent.getParameters("SelectedIndex");
		let oSelectedBusinessRule = oBusinessRuleModel.getProperty("/selectedBusinessRule");
		let selectedAttr = oBusinessRuleModel.getProperty("/valueCheck/selectedAttributeName");

		if(sSelectedIndex.selectedIndex === 1){
			
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/selectedEntity", oSelectedBusinessRule.EntityId);
			let oEntityComboBox = sap.ui.getCore().byId(this.NodeEditFragmentId + "--idCBoxEntities");
			let oSelectedEntity = oEntityComboBox.getSelectedItem();
			if (oSelectedEntity) {
				oEntityComboBox.fireSelectionChange({
					selectedItem: oSelectedEntity,
					entitySelectionChange: false
				});
			}
			oBusinessRuleModel.setProperty("/NodeEditDetails/nodeEditData/selectedAttribute", selectedAttr);
		}


	};

	/**
	 * Invoved as a handler to the Node Edit Dialog - Cancel Dialog Operation
	 *
	 * INVOKED IN THE CONTEXT OF THE CONTROLLER. this = oController
	 */
	NodeEdit.onNodeEditCancel = function () {
		let oNodeEditDialog = this.NodeEditDialog.getNodeEditFragment();
		oNodeEditDialog.close();
	};

	return NodeEdit;
});