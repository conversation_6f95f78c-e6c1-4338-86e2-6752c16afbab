sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"sap/m/MessageBox",
	"sap/ui/model/json/JSONModel",
	"dmr/mdg/supernova/SupernovaFJ/model/ModelMessages",
	"jquery.sap.global",
	"sap/ui/model/Filter",
	"sap/ui/model/FilterOperator",
	"sap/m/MessageToast",
	"./MandatoryRule",
	"./SingleValueRule",
	"./MultiValueRule",
	"./ProcessFlowLibrary",
	"./NodeEdit",
	"./CRStepTypeProps",
	"./UserDefinedExpression",
	"dmr/components/FormulaEdit/FormulaType",
], function (BaseController, Utilities, GetLists, MessageBox, JSONModel, ModelMessages, jQuery, Filter, FilterOperator, MessageToast, 
	MandatoryRuleDialog, SingleValueRuleDialog, MultiValueRuleDialog, ProcessFlowLibraryDialog, NodeEditDialog, CRStepTypePropsDialog, UserDefinedExpressionDialog, 
	FormulaType) {
	"use strict";

	let BusinessRulesDetailsController = {};
	
	BusinessRulesDetailsController.onInit = function () {
		this.ModelMessages = ModelMessages;
		this.MandatoryRuleDialog = MandatoryRuleDialog;
		this.SingleValueRuleDialog = SingleValueRuleDialog;
		this.MultiValueRuleDialog = MultiValueRuleDialog;
		this.ProcessFlowLibraryDialog = ProcessFlowLibraryDialog;
		this.NodeEditDialog = NodeEditDialog;
		this.CRStepTypePropsDialog = CRStepTypePropsDialog;
		this.UserDefinedExpressionDialog = UserDefinedExpressionDialog;
		this.NodeEditFragmentId = "NodeEditFragment";
		this.SingleValueRuleFragmentId = "SingleValueRuleFragment";
		this.UserDefinedFragmentId = "UserDefinedFragment";

		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		if (!oBusinessRulesDetailsModel) {
			let oJSONModel = new JSONModel();
			this.getView().setModel(oJSONModel, "BusinessRulesDetails");
		}

		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		oRouter.getRoute("BusinessRulesDetails").attachMatched(this.onRouteMatched, this);
		this.addDrivingInputToList();
	};

	// Called from detailed rule screen to set the busy status.
	BusinessRulesDetailsController.setBusyStatus = function(oController, bBusy) {
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");

		oBusinessRulesDetailsModel.setProperty("/isBusy", bBusy);
	};

    BusinessRulesDetailsController.onRouteMatched = async function(oEvent) {
        let oController = this;
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		// Initialize the transport and package setting 
		oBusinessRulesDetailsModel.setProperty("/selected", {
			transports: {},
			package: undefined
		});
		this.fetchDataModelsList(this);

		let oArguments = oEvent.getParameter("arguments");
		oBusinessRulesDetailsModel.setProperty("/RouteParams", oArguments);
		oArguments.CRStep = oArguments.CRStep === "All" ? "**" : oArguments.CRStep;
		let sFragmentToLoad = "";
		switch(oArguments.RuleType) {
			case "1":
				sFragmentToLoad = "Mandatory";
				oBusinessRulesDetailsModel.setProperty("/selectedRulesNumber", 1);
				oBusinessRulesDetailsModel.setProperty("/selectedRule", {colValue: "**", description: "For every WF step"});
				break;
			case "2":
			case "3":
				sFragmentToLoad = "ValueCheck";
				oBusinessRulesDetailsModel.setProperty("/selectedRulesNumber", 1);
				oBusinessRulesDetailsModel.setProperty("/selectedRule", {colValue: "**", description: "For every WF step"});
				break;
			case "4":
			case "5":
				sFragmentToLoad = "Init";
				break;
			case "6":
				sFragmentToLoad = "CRStepTypeProps";
				break;
			default:
				sFragmentToLoad = "Unidentified";
		}
		oBusinessRulesDetailsModel.setProperty("/FragmentToLoad", sFragmentToLoad);
		this.getView().byId("rulesListId").removeSelections();
		let oSelectedBusinessRule;
		if(sFragmentToLoad === "Init") {
			if (oArguments.BusinessRuleName === "New Rule") {
				let oRuleExpression = {};
				oRuleExpression.modeltodriving = [{
					usmdEntity: "WF",
					usmdAttribute: "STEP",
					colNum: "1",
					lenghttype: ""
				}];
				oRuleExpression.modeltoderiving = [];
				oRuleExpression.modeltovalidation = [];
				oRuleExpression.modeltoattrvalues = [{
					colName: "WF__STEP",
					colValue: "**",
					colNum: "1",
					rowNum: "1",
					operator: "EQ"
				}];
				oSelectedBusinessRule = {
					DataModelId: oArguments.DataModelId ? oArguments.DataModelId : "",
					DataModelDescription: "",
					CRTypeId: oArguments.DataModelId && oArguments.CRTypeId ? oArguments.CRTypeId : "",
					CRTypeDescription: "",
					EntityId: oArguments.DataModelId && oArguments.CRTypeId && oArguments.EntityId ? oArguments.EntityId : "",
					EntityDescription: "",
					RuleTypeId: oArguments.RuleType,
					RuleTypeDescription: "",
					BusinessRuleId: "",
					BusinessRuleName: "New Rule",
					UserRuleName: "",
					CustomizingTransport: "",
					WorkbenchTransport: "",
					RuleExpression: oRuleExpression,
					lists: {}
				};
				oBusinessRulesDetailsModel.setProperty("/newRule", true);
				//Bug 13259 - Clear values to avoid saving previous selections
				oBusinessRulesDetailsModel.setProperty("/selected/transports", {
					customizing: undefined,
					workbench: undefined
				});
				oBusinessRulesDetailsModel.setProperty("/selected/package", undefined);
				oBusinessRulesDetailsModel.setProperty("/BAdISelected", undefined);
				oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule", oSelectedBusinessRule);
				oController._populateHeaderList();
			} else {
				let oBusinessRulesDetails = await GetLists.getBusinessRuleDetails(this, oArguments.BusinessRuleName);
				let oRuleExpression = JSON.parse(oBusinessRulesDetails[0].RuleExpr);
				if (oRuleExpression.crossentity === "X") {
					oRuleExpression.crossentityDescription = "Cross Entity Derive (Keys/Non Keys)";
				}else if(oRuleExpression.crossentity === "N"){
					oRuleExpression.crossentityDescription = "Cross Entity Derive (Non Keys)";
				}else{
					oRuleExpression.crossentityDescription = "Cross Entity Derivation";
				}
				oSelectedBusinessRule = {
					DataModelId: oBusinessRulesDetails[0].UsmdModel,
					DataModelDescription: oBusinessRulesDetails[0].ModelDesc,
					CRTypeId: oBusinessRulesDetails[0].UsmdCreqType,
					CRTypeDescription: oBusinessRulesDetails[0].CreqtypeDesc,
					EntityId: oBusinessRulesDetails[0].UsmdEntity,
					EntityDescription: oBusinessRulesDetails[0].EntityDesc,
					RuleTypeId: oBusinessRulesDetails[0].RuleType,
					RuleTypeDescription: "",
					BusinessRuleId: oBusinessRulesDetails[0].RuleId,
					BusinessRuleName: oBusinessRulesDetails[0].RuleName,
					UserRuleName: oBusinessRulesDetails[0].UserRuleName,
					CustomizingTransport: oBusinessRulesDetails[0].CustTransport,
					WorkbenchTransport: oBusinessRulesDetails[0].WbTransport,
					RuleExpression: oRuleExpression,
					lists: {}
				};
				oSelectedBusinessRule.RuleExpression.modeltoattrvalues.forEach(oRule => {
					oRule.colName = oRule.colName.replace("___", "__");
					oRule.validValue = true;
					if (oBusinessRulesDetails[0].RuleType === "4" && oRule.colName === "MSGNO") {
						oRule.colValue = oRule.colValue + " - " + oRule.colValuedescr;
					}
				});

				//Testing things out
				oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule", oSelectedBusinessRule);
				oBusinessRulesDetailsModel.setProperty("/selected/transports", {
					customizing: oBusinessRulesDetails[0].CustTransport,
					workbench: oBusinessRulesDetails[0].WbTransport
				});
				oBusinessRulesDetailsModel.setProperty("/BAdISelected", oSelectedBusinessRule.RuleExpression.badiimpl);
				oBusinessRulesDetailsModel.setProperty("/newRule", false);
				oController._routeToFragment({RuleTypeId: oArguments.RuleType, BusinessRuleName: oArguments.BusinessRuleName}, false);
			}
			let aRulesByStep = oSelectedBusinessRule.RuleExpression.modeltoattrvalues.filter(oRule => oRule.colName === "WF__STEP");
			aRulesByStep.forEach(oStep => {
				oStep.colValue = oStep.colValue.length > 1 ? oStep.colValue : "0" + oStep.colValue;
			});
			oBusinessRulesDetailsModel.setProperty("/selectedRule", undefined);
			oBusinessRulesDetailsModel.setProperty("/selectedRulesNumber", undefined);
			oBusinessRulesDetailsModel.setProperty("/rulesByStep", aRulesByStep);
		} else {
			oSelectedBusinessRule = {};
			if(oArguments.BusinessRuleName === "New Rule") {
				oSelectedBusinessRule = {
					DataModelId: oArguments.DataModelId ? oArguments.DataModelId : "",
					DataModelDescription: "",
					CRTypeId: oArguments.DataModelId && oArguments.CRTypeId ? oArguments.CRTypeId : "",
					CRTypeDescription: "",
					EntityId: oArguments.DataModelId && oArguments.CRTypeId && oArguments.EntityId ? oArguments.EntityId : "",
					EntityDescription: "",
					RuleTypeId: oArguments.RuleType,
					RuleTypeDescription: "",
					BusinessRuleId: "",
					BusinessRuleName: "New Rule",
					UserRuleName: "",
					CustomizingTransport: undefined,
					WorkbenchTransport: undefined,
					RuleExpression: "",
					CRStep: "",
					lists: {}
				};
				oBusinessRulesDetailsModel.setProperty("/newRule", true);
				//Bug 13259 - Clear values to avoid saving previous selections
				oBusinessRulesDetailsModel.setProperty("/selected/package", undefined);
				oBusinessRulesDetailsModel.setProperty("/selected/transports", {
					customizing: undefined,
					workbench: undefined
				});
				oBusinessRulesDetailsModel.setProperty("/BAdISelected", undefined);
				oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule", oSelectedBusinessRule);
				oController._populateHeaderList();
			} else {
				oController._routeToFragment(
					{
						DataModelId: oArguments.DataModelId, 
						CRTypeId: oArguments.CRTypeId, 
						CRStep: oArguments.CRStep, 
						RuleTypeId: oArguments.RuleType, 
						BusinessRuleName: oArguments.BusinessRuleName
					}, 
					false
				);
			}
		}
		
		if (oArguments.CRTypeId) {
			oController._getBackgroundJobStatus(oArguments.CRTypeId);
		}
		
    };

	BusinessRulesDetailsController._populateHeaderList = function() {
		let oController = this;
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule");
		let bNewRule = oBusinessRulesDetailsModel.getProperty("/newRule");		

		if (oSelectedBusinessRule.DataModelId) {
			oController.fetchChangeRequestsList(oController, oSelectedBusinessRule.DataModelId);
			
			if(oSelectedBusinessRule.CRTypeId) {
				oController.fetchEntitiesList(oController, oSelectedBusinessRule.DataModelId, oSelectedBusinessRule.CRTypeId);

				let promiseWorkflowStepsList = GetLists.getWorkflowStepList(this, oSelectedBusinessRule.CRTypeId, "RL");
				promiseWorkflowStepsList.then(function (arrWorkflowStepsList) {
					if (!arrWorkflowStepsList) {
						arrWorkflowStepsList = [];
					}
					arrWorkflowStepsList.forEach(oStep => {
						oStep.UsmdCreqStep = oStep.UsmdCreqStep.length > 1 ? oStep.UsmdCreqStep : "0" + oStep.UsmdCreqStep;
					});

					let aRulesByStep = oBusinessRulesDetailsModel.getProperty("/rulesByStep");
					aRulesByStep?.forEach(oRule => {
						let oTempRule = arrWorkflowStepsList.find(oStep => oStep.UsmdCreqStep === oRule.colValue);
						oRule.description = oTempRule.Txtmi;
					});

					if (arrWorkflowStepsList.length === 0) {
						Utilities.showPopupAlert(
							oController.geti18nText("businessRulesDetails.header.wfstep.message"), 
							MessageBox.Icon.WARNING,
							oController.geti18nText("businessRulesDetails.header.wfstep.title"));
					}
					oBusinessRulesDetailsModel.setProperty("/WorkflowStepsList", arrWorkflowStepsList);
					// Bug 12781 - Adding blank option for filter
					oBusinessRulesDetailsModel.setProperty("/WorkflowStepsListForFilter", [{UsmdCreqStep: "", Txtmi: ""}, ...arrWorkflowStepsList]);

					if (bNewRule && oSelectedBusinessRule.RuleTypeId === "6") {
						let aFilteredWorkflowStepsList = arrWorkflowStepsList.filter(oStep => Number(oStep.Uicounter) === 0);
						oBusinessRulesDetailsModel.setProperty("/CRStepsList", aFilteredWorkflowStepsList);
						if (aFilteredWorkflowStepsList.length === 0) {
							Utilities.showPopupAlert(
								oController.geti18nText("businessRulesDetails.header.wfstep.message"),
								MessageBox.Icon.WARNING,
								oController.geti18nText("businessRulesDetails.header.wfstep.title"));
						}
					}else{
						oBusinessRulesDetailsModel.setProperty("/CRStepsList", arrWorkflowStepsList);
					}
				});

				if(oSelectedBusinessRule.EntityId) {

					//Best place to add the functions to to load additional models used for Rule Type 4 and 5.
					//To-do - Add the functions to the segregated JS file for Multi value page
					oController._loadAttributesValuesLists()
					.then(()=>{
						oController._addAttributeSelectionModel();
						oController._loadKeyAttributesList();
						
						if (oSelectedBusinessRule.RuleTypeId === "5" && bNewRule) {
							oController.selectCrossEntity(oSelectedBusinessRule.EntityId);
						} else if(bNewRule){
							oController._routeToFragment(oSelectedBusinessRule, bNewRule);
						}
					});
				}
			}else{
				oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/DataModelId", "");
				oBusinessRulesDetailsModel.setProperty("/CRStepsList", []);
			}
		}else{
			oBusinessRulesDetailsModel.setProperty("/CRStepsList", []);
			oController._routeToFragment(oSelectedBusinessRule, bNewRule);
		}

		if (oSelectedBusinessRule.RuleTypeId === "6" && bNewRule) {
			oController._routeToFragment(oSelectedBusinessRule, bNewRule);
		}

		// Retrieve list of Rule Types to populate filter options
		let promiseRuleTypesList = GetLists.getRuleTypesList(this);
		promiseRuleTypesList.then(function (arrRuleTypesList) {
			if (!arrRuleTypesList) {
				arrRuleTypesList = [];
			}
			oBusinessRulesDetailsModel.setProperty("/RuleTypesList", arrRuleTypesList.map(oResult => {
				return {
					RuleTypeId: oResult.RuleTypeId,
					RuleTypeDescription: oResult.RuleTypeDescription
				};
			}));
		});
	};

	BusinessRulesDetailsController._routeToFragment = async function(oRouteParams, bIsNewRule) {
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule;
		switch(oRouteParams.RuleTypeId) {
			case "1":
				oBusinessRulesDetailsModel.setProperty("/mandatory", {});
				oSelectedBusinessRule = await MandatoryRuleDialog.getBusinessRuleDetails(this, oRouteParams, bIsNewRule);
				oBusinessRulesDetailsModel.setProperty("/newRule", bIsNewRule);
				bIsNewRule || oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule", oSelectedBusinessRule);
				break;
			case "2":
			case "3":
				oBusinessRulesDetailsModel.setProperty("/valueCheck", {});
				oSelectedBusinessRule = await SingleValueRuleDialog.getBusinessRuleDetails(this, oRouteParams, bIsNewRule);
				oBusinessRulesDetailsModel.setProperty("/newRule", bIsNewRule);
				bIsNewRule || oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule", oSelectedBusinessRule);
				break;
			case "6":
				oBusinessRulesDetailsModel.setProperty("/crStepTypeProps", {});
				oSelectedBusinessRule = await CRStepTypePropsDialog.getBusinessRuleDetails(this, oRouteParams);
				oBusinessRulesDetailsModel.setProperty("/newRule", bIsNewRule);
				bIsNewRule || oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule", oSelectedBusinessRule);
				break;
			default:						
		}
		bIsNewRule || this._populateHeaderList();
	};

	BusinessRulesDetailsController._addAttributeSelectionModel = function () {
		let oController = this;
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let sDataModelId = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/DataModelId");
		let sCRTypeId = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/CRTypeId");
		let oAttributeSelectionModel = new JSONModel();
		let promiseEntitiesList = GetLists.getModelEntityList(this, sDataModelId, "UsmdModel", sCRTypeId, null, true);
		promiseEntitiesList.then(function (arrEntitiesList) {
			if (!arrEntitiesList) {
				arrEntitiesList = [];
			}
			oAttributeSelectionModel.setData({
				mainEntityList: arrEntitiesList
			});
			oController.getView().setModel(oAttributeSelectionModel, "attributeSelectionModel");
		});
	};

	BusinessRulesDetailsController._loadAttributesValuesLists = function (sEntity, sAttribute) {
		let oController = this;
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule");
	
		return new Promise((resolve, reject) => {
			try {
				if (sEntity && sAttribute) {
					GetLists.getAttributeValuesList(oController, oSelectedBusinessRule.DataModelId, sEntity, sAttribute, undefined)
						.then(function (arrValues) {
							if (arrValues) {
								if (arrValues.results) {
									arrValues = arrValues.results;
								}
								oSelectedBusinessRule.lists[sEntity + "__" + sAttribute] = arrValues;
							}
							resolve(); 
						})
						.catch(reject);
				} else {
					let promises = []; 
	
					let modeltodriving = oSelectedBusinessRule.RuleExpression.modeltodriving;
					if (modeltodriving && modeltodriving.length > 0) {
						for (let i = 0; i < modeltodriving.length; i++) {
							let promise = GetLists.getAttributeValuesList(oController, oSelectedBusinessRule.DataModelId, modeltodriving[i].usmdEntity, modeltodriving[i].usmdAttribute, undefined)
								.then(function (arrValues) {
									if (arrValues) {
										if (arrValues.results) {
											arrValues = arrValues.results;
										}
										oSelectedBusinessRule.lists[modeltodriving[i].usmdEntity + "__" + modeltodriving[i].usmdAttribute] = arrValues;
									}
								});
							promises.push(promise);
						}
					}
	
					let modeltoderiving = oSelectedBusinessRule.RuleExpression.modeltoderiving;
					if (modeltoderiving && modeltoderiving.length > 0) {
						for (let i = 0; i < modeltoderiving.length; i++) {
							let promise = GetLists.getAttributeValuesList(oController, oSelectedBusinessRule.DataModelId, modeltoderiving[i].usmdEntity, modeltoderiving[i].usmdAttribute, undefined)
								.then(function (arrDerivingValues) {
									if (arrDerivingValues) {
										if (arrDerivingValues.results) {
											arrDerivingValues = arrDerivingValues.results;
										}
										oSelectedBusinessRule.lists[modeltoderiving[i].usmdEntity + "__" + modeltoderiving[i].usmdAttribute] = arrDerivingValues;
									}
								});
							promises.push(promise);
						}
					}
	
					// Bug 13109 - Changed condition to get message types list
					if (oSelectedBusinessRule.RuleTypeId === "4") {
						let promiseMessageType = GetLists.getMessageTypeList()
							.then((MessageTypeList) => {
								oSelectedBusinessRule.lists.MessageTypeValues = MessageTypeList;
							});
						promises.push(promiseMessageType); 
					}
	
					Promise.all(promises)
						.then(() => resolve())
						.catch(reject);
				}
			} catch (error) {
				reject(error); // Reject the Promise if there's a synchronous error
			}
		});
	};

	BusinessRulesDetailsController.onClickLinkHome = function() {
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		oRouter.navTo("TargetHome", {}, true);
		this.getView().getModel("BusinessRulesDetails").setData({});
	};

	BusinessRulesDetailsController._loadKeyAttributesList = function() {
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule");
		let oKeyAtrributes = {};
		let oPromiseKeyAttrList =
				GetLists.getEntityAttributeList(this, oSelectedBusinessRule.DataModelId, oSelectedBusinessRule.EntityId, undefined);
		oPromiseKeyAttrList.then(function(aAttrList){
			aAttrList = aAttrList.filter(oAttr => oAttr.IsKey === "X");
			let aKeyAttrWithEntity = [];
			let aKeyAttr = [];
			aAttrList.forEach(oAttr => {
				aKeyAttrWithEntity.push(oAttr.UsmdEntity + "__" + oAttr.UsmdAttribute);
				aKeyAttr.push(oAttr.UsmdAttribute);
			});
			oKeyAtrributes.keyAttributesList = aKeyAttr;
			oKeyAtrributes.keyAttributesWithEntityList = aKeyAttrWithEntity;
			oBusinessRulesDetailsModel.setProperty("/keyAtrributes", oKeyAtrributes);
		});
	};

	BusinessRulesDetailsController.addDrivingInputToList = function() {
		let oController = this;
		let oHBox = this.getView().byId("gridHBoxDriving");
		let oInput = new sap.m.Input({
			width: "100%",
			fieldWidth: "100%",
			value: "{BusinessRulesDetails>colValue}",
			valueLiveUpdate: true,
			autocomplete: false,
			startSuggestion: 0,
			valueStateText: "{= ${BusinessRulesDetails>validValue} ? 'Type to refresh list' : 'The selected value is not in the list'}",
			visible: "{= ${BusinessRulesDetails>attrdatatype} !== 'DATS'}",
			type: "Text"
		}).attachLiveChange(oController.liveChangeValue, oController)
			.addEventDelegate({
				onfocusin: function(oEvent) {
					let bShowSuggestion = oEvent.srcControl.getShowSuggestion();
					let sValue = oEvent.srcControl.getValue();
					let bPopoverOpened = oEvent.srcControl._isSuggestionsPopoverOpen();
					if (!sValue && !bPopoverOpened && bShowSuggestion) {
						oEvent.srcControl.fireLiveChange();
					}
				}
			})
			.bindProperty("valueState", {
				parts: [{path: "BusinessRulesDetails>colName"}, {path: "BusinessRulesDetails>validValue"}, {path: "BusinessRulesDetails>operator"}], 
				formatter: oController.getInputValueState
			})
			.bindProperty("showSuggestion", {
				parts: [{path: "BusinessRulesDetails>colName"}, {path: "BusinessRulesDetails>operator"}], 
				formatter: oController.getInputShowSuggestion
			})
			.bindProperty("maxLength", {
				parts: [{path: "BusinessRulesDetails>colName"}, {path: "BusinessRulesDetails>operator"}, {path: "BusinessRulesDetails>lenghttype"}], 
				formatter: oController.getInputMaxLength
			})
			.attachChange(oController.changeInputValue, oController);

		oInput.setLayoutData(new sap.m.FlexItemData({growFactor: 4}));
		oHBox.addItem(oInput);
	};

	BusinessRulesDetailsController.bindItemsToDerivingList = function () {
		let oController = this;
		let oGridList = this.getView().byId("gridListDeriving");
		oGridList.unbindItems();

		let oGridListItem = new sap.f.GridListItem({
			content: [
				new sap.m.VBox({
					items: [
						new sap.m.HBox({
							width: "100%",
							justifyContent: "SpaceBetween",
							alignItems: "Center",
							items: [
								// Bug 12909 - Added specific width to the title to respect wrapping property
								new sap.m.Title({
									text: "{BusinessRulesDetails>colName}",
									width: "17rem",
									wrapping: true
								}),
								new sap.m.Button({
									type: "Transparent",
									tooltip: "Delete Attribute",
									icon: "sap-icon://decline",
									enabled: "{= ${BusinessRulesDetails>isKey} !== 'X'}",
									press: function(oEvent) {
										oController.onDeleteAttribute(oEvent, "Deriving");
									}
								})
							]
						}),
						//Task 12923 - Add Radio button group to allow user to select either drop down value or attribute
						new sap.m.RadioButtonGroup({
							columns: 2,
							selectedIndex: "{BusinessRulesDetails>attrSelectionType}",
							buttons: [
								new sap.m.RadioButton({
									text: "Value"
								}),
								new sap.m.RadioButton({
									text: "Attribute"
								})
							]
						})
						.attachSelect(oController.onSelectSelectionType, oController)
						.bindProperty("visible", {
							parts: [{path: "BusinessRulesDetails>colName"}, {path: "BusinessRulesDetails>attrdatatype"}], 
							formatter: oController.getAttrSelectionTypeVisible
						}),
		
						new sap.m.HBox({
							items: [
								new sap.m.VBox({
									items:[
										new sap.m.DatePicker({
											displayFormat: "M/d/yyyy",
											valueFormat: "yyyyMMdd",
											visible: "{= ${BusinessRulesDetails>attrdatatype} === 'DATS'}",
											enabled: "{BusinessRulesDetails>ebDatePicker}"
										}).attachChange(oController.changeDatePickerValue, oController)
										.bindProperty("value", {
											parts:[{path: "BusinessRulesDetails>colValue"}, {path:"BusinessRulesDetails>currentDateCheck"}],
											formatter:oController.datePickerFormatter
										}),
		
										new sap.m.CheckBox({
											selected:"{BusinessRulesDetails>currentDateCheck}",
											visible:"{= ${BusinessRulesDetails>attrdatatype} === 'DATS'}",
											text:"Current Date"
										}).attachSelect(oController.onCurrentDateSelect, oController)
									
									]
								}),
		
								new sap.ui.core.ComponentContainer({
									name: "dmr.components.FormulaEdit",
									propagateModel:true,
									settings:{ 
										formulaType: "FORMULA",
										formulaTypeSelected:"{BusinessRulesDetails>selectedType}",
										formulaText:"{BusinessRulesDetails>colValue}",
										ruleNumber:"",
										colName: "",
										value:"",
										dataModel:"{BusinessRulesDetails>/selectedBusinessRule/DataModelId}",
										crType:"{BusinessRulesDetails>/selectedBusinessRule/CRTypeId}",
										entity:{
											parts: ["BusinessRulesDetails>colName"], 
											formatter: function(sColName) {
												return sColName.split("__")[0];
											}
										},
										attribute:{
											parts: ["BusinessRulesDetails>colName"], 
											formatter: function(sColName) {
												return sColName.split("__")[1];
											}
										},
										restrictEntitySelection: true,
										isCrossEntity:"{BusinessRulesDetails>/selectedBusinessRule/RuleExpression/crossentity}",
										delimiter:"{BusinessRulesDetails>delimiter}",
										tableStruct:"{BusinessRulesDetails>tableDetails}"
									},
									visible: {parts:["BusinessRulesDetails>colName", "BusinessRulesDetails>attrdatatype" ], formatter:oController.getFormulaVisible}
								})
								.setLayoutData(new sap.m.FlexItemData({growFactor: 4})),

								new sap.m.Input({
									width: "100%",
									fieldWidth: "100%",
									value: "{BusinessRulesDetails>colValue}",
									valueLiveUpdate: true,
									autocomplete: false,
									showSuggestion: true,
									startSuggestion: 0,
									valueStateText: "{= ${BusinessRulesDetails>validValue} ? 'Type to refresh list' : 'The selected value is not in the list'}",
									type: "Text"
								}).attachLiveChange(oController.liveChangeValue, oController)
									.addEventDelegate({
										onfocusin: function(oEvent) {
											let sValue = oEvent.srcControl.getValue();
											let bPopoverOpened = oEvent.srcControl._isSuggestionsPopoverOpen();
											if (!sValue && !bPopoverOpened) {
												oEvent.srcControl.fireLiveChange();
											}
										}
									})
									.attachChange(oController.changeInputValue, oController)
									.bindProperty("valueState", {
										parts: [{path: "BusinessRulesDetails>colName"}, {path: "BusinessRulesDetails>validValue"}], 
										formatter: oController.getInputValueState
									}).bindProperty("visible", {
										parts: [{path: "BusinessRulesDetails>colName"}, {path: "BusinessRulesDetails>attrdatatype"}, {path: "BusinessRulesDetails>attrSelectionType"}], 
										formatter: oController.getInputVisible
									}).setLayoutData(new sap.m.FlexItemData({growFactor: 4}))
							]
						}).addStyleClass("sapUiSmallMarginTop"),

						//Task 12923 - Add an option to the user to select entity-attribute instead of a drop down value
						new sap.m.HBox({
							items: [
								new sap.m.VBox({
									items: [
										new sap.m.Label({
											text: "Entity"
										}),
										new sap.m.ComboBox({
											width: "100%",
											selectedKey: "{BusinessRulesDetails>colEntityVal}",
											items:{ 
												path: "attributeSelectionModel>/mainEntityList", 
												length: "2000",
												templateShareable: true,
												template: new sap.ui.core.Item({
													key: "{attributeSelectionModel>UsmdEntity}",
													text:"{= ${attributeSelectionModel>UsmdEntity}.concat(${attributeSelectionModel>Txtlg}==='' ? '' : ' - '.concat(${attributeSelectionModel>Txtlg}))}"
												})
											}
										})
										.attachSelectionChange(oController.onEntityChange, oController)
									]
								}),
								new sap.m.VBox({
									items: [
										new sap.m.Label({
											text: "Attribute"
										}),
										new sap.m.ComboBox({
											width: "100%",
											selectedKey: "{BusinessRulesDetails>colAttrVal}",
											items:{ 
												path: "BusinessRulesDetails>attrList",
												length: "2000",
												templateShareable: true,
												template: new sap.ui.core.Item({
													key: "{BusinessRulesDetails>UsmdAttribute}",
													text:"{= ${BusinessRulesDetails>UsmdAttribute}.concat(${BusinessRulesDetails>Txtlg}==='' ? '' : ' - '.concat(${BusinessRulesDetails>Txtlg}))}"
												})
											}
										})
									]
								}).addStyleClass("sapUiSmallMarginBegin")
							]		
						}).bindProperty("visible", {
							parts: [{path: "BusinessRulesDetails>colName"}, {path: "BusinessRulesDetails>attrdatatype"}, {path: "BusinessRulesDetails>attrSelectionType"}], 
							formatter: oController.getAttributeComboBoxVisible
						})
					]
				}).addStyleClass("sapUiSmallMargin")
			]
		});


		oGridList.bindItems({
			path: "/derivingAttributes",
			model: "BusinessRulesDetails",
			template: oGridListItem,
			templateShareable: true
		});
	};

	BusinessRulesDetailsController.getFormulaVisible = function (colName, attrdatatype) {
		let oBusinessRulesDetailsModel = this.getModel("BusinessRulesDetails");
		let arrList = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/lists/" + colName);
		if ((!arrList || arrList.length === 0) && attrdatatype !== "DATS") {
			return true;
		}else{
			return false;
		}
	};

	BusinessRulesDetailsController.getInputVisible = function (colName, attrdatatype, attrSelectionType) {
		let oBusinessRulesDetailsModel = this.getModel("BusinessRulesDetails");
		let arrList = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/lists/" + colName);
		if ((arrList && arrList.length > 0 && attrSelectionType === 0 || colName.includes("__LENGTH")) && attrdatatype !== "DATS") {
			return true;
		}else{
			return false;
		}
	};

	BusinessRulesDetailsController.getInputValueState = function (colName, validValue, operator) {
		let oBusinessRulesDetailsModel = this.getModel("BusinessRulesDetails");
		let arrList = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/lists/" + colName);
		if (arrList && arrList.length > 0) {
			if (operator === "CP" || operator === "NP") {
				return "None";
			}else{
				if (!validValue) {
					return "Warning";
				}else{
					return "Information";
				}
			}
		}else{
			return "None";
		}
	};

	BusinessRulesDetailsController.getInputShowSuggestion = function (colName, operator) {
		let oBusinessRulesDetailsModel = this.getModel("BusinessRulesDetails");
		let arrList = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/lists/" + colName);
		if (arrList && arrList.length > 0) {
			if (operator === "CP" || operator === "NP") {
				return false;
			}
			return true;
		}else{
			return false;
		}
	};

	BusinessRulesDetailsController.getInputMaxLength = function (colName, operator, lenghttype) {
		let oBusinessRulesDetailsModel = this.getModel("BusinessRulesDetails");
		let arrList = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/lists/" + colName);
		if (arrList && arrList.length > 0) {
			if (operator === "CP" || operator === "NP") {
				return Number(lenghttype);
			}
			return 0;
		}else{
			return Number(lenghttype);
		}
	};

	/**
	 * Task 12923 - Add an option to the user to select entity-attribute instead of a drop down value
	 * Radio button group to be visible when the attribute consists of a dropdown list
	 */
	BusinessRulesDetailsController.getAttrSelectionTypeVisible = function(sColName, sAttrdatatype) {
		let oBusinessRulesDetailsModel = this.getModel("BusinessRulesDetails");
		let arrList = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/lists/" + sColName);
		if ((arrList && arrList.length > 0  || sColName.includes("__LENGTH")) && sAttrdatatype !== "DATS") {
			return true;
		}else{
			return false;
		}
	};

	/**
	 * Task 12923 - Add an option to the user to select entity-attribute instead of a drop down value
	 * HBOX consisting of Entity Attribute Combobox to be visible when user selects the Attribute radio button
	 */
	BusinessRulesDetailsController.getAttributeComboBoxVisible = function(colName, attrDataType, attrSelectionType) {
		let oBusinessRulesDetailsModel = this.getModel("BusinessRulesDetails");
		let arrList = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/lists/" + colName);
		if ((arrList && arrList.length > 0 && attrSelectionType === 1) && attrDataType !== "DATS") {
			return true;
		} else {
			return false;
		}
	};

	/**
	 * Task 12923 - Add an option to the user to select entity-attribute instead of a drop down value
	 * Fill the Attribute list for card when the user selects the entity.
	 * Clear the attribute on entity selection
	 */
	BusinessRulesDetailsController.onEntityChange = async function(oEvent) {
		let sSelectedEntity = oEvent.getSource().getSelectedKey();
		// Get model 
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let sDataModelId = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/DataModelId");
		let sPath = oEvent.getSource().getBindingContext("BusinessRulesDetails").getPath();
		
		oBusinessRulesDetailsModel.setProperty(sPath + "/colAttrVal", undefined);

		let arrAttrList = await GetLists.getEntityAttributeList(this, sDataModelId, sSelectedEntity, undefined);
		if(arrAttrList && arrAttrList.length > 0) {
			oBusinessRulesDetailsModel.setProperty(sPath + "/attrList", arrAttrList.filter(oAttr => {
				return oAttr.DATATYPE === oBusinessRulesDetailsModel.getProperty(sPath + "/attrdatatype") &&
						oAttr.Length === oBusinessRulesDetailsModel.getProperty(sPath + "/lenghttype");
			}));
		}
	};

	/**
	 * Task 12923 - Add an option to the user to select entity-attribute instead of a drop down value
	 * Clear the relevant element values on radio button selection
	 */
	BusinessRulesDetailsController.onSelectSelectionType = function(oEvent) {
		let iSelectedIndex = oEvent.getSource().getSelectedIndex();
		let sPath = oEvent.getSource().getBindingContext("BusinessRulesDetails").getPath();
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		if (iSelectedIndex === 0) {
			oBusinessRulesDetailsModel.setProperty(sPath + "/colEntityVal", undefined);
			oBusinessRulesDetailsModel.setProperty(sPath + "/colAttrVal", undefined);
		} else {
			oBusinessRulesDetailsModel.setProperty(sPath + "/colValue", undefined);
		}
	};

	BusinessRulesDetailsController.liveChangeValue = function (oEvent) {
		let oInput = oEvent.getSource();
		let bShowSuggestion = oInput.getShowSuggestion();
		// Bug 12854 - added conditional to validate the value and the selected Item
		let sValue = oEvent.getParameter("value");
		if (bShowSuggestion) {
			let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
			let sPath = oInput.getBindingContext("BusinessRulesDetails").getPath();
			let sColName = oBusinessRulesDetailsModel.getProperty(sPath + "/colName");
			let sEntity = sColName.split("__")[0];
			let sAttribute = sColName.split("__")[1];
			let sDataModel = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/DataModelId");
			clearTimeout(this.timeout);
			this.timeout = setTimeout(() => {		
				oInput.removeAllSuggestionItems();
				let promiseValueList = GetLists.getAttributeValuesList(this, sDataModel, sEntity, sAttribute, sValue);
				
				promiseValueList.then(function (oValueList) {
					let arrValueList = oValueList;
					/**
					 * Some functions return the data in oData -> results ... check if "results" exist and then point the model to that
					 */
					if(oValueList.results){
						arrValueList = oValueList.results;
					}
					arrValueList.forEach(item => {
						oInput.addSuggestionItem(new sap.ui.core.Item({
							key: item.Key,
							text: item.Key + (item.Ddtext.length > 0 ? " - " : "") + item.Ddtext
						}));
					});
					// Bug 13190 - Execute change event once the suggestion list is ready
					oInput.fireChange({value: sValue});
				});
				
			}, 300);
		}
	};

	// Utility method to set the invalid value
	BusinessRulesDetailsController.setInvalidValue = function(oEvent) {
		oEvent.getSource().setValue();
		return true;
	};

	// Method to validate LENGTH type input
	BusinessRulesDetailsController.validateLength = function(sValue, regexNum, oEvent) {
		if (!sValue.match(regexNum)) {
			return this.setInvalidValue(oEvent);
		}
		return false;
	};

	// Method to validate 'C' (Character) type input
	BusinessRulesDetailsController.validateCType = function(sValue, oEvent) {
		oEvent.getSource().setValue(sValue.toUpperCase());
		return false;
	};

	// Method to validate 'N' (Numeric) type input
	BusinessRulesDetailsController.validateNType = function(oInputData, sValue, regexNumPattern, regexNum, oEvent) {
		const iValue = Number(sValue);

		if (oInputData.operator === "CP" || oInputData.operator === "NP") {
			if (!sValue.match(regexNumPattern) && sValue.trim() !== "") {
				return this.setInvalidValue(oEvent);
			}
		} else {
			if (iValue.toFixed(0).toString().match(regexNum) && sValue.trim() !== "") {
				oEvent.getSource().setValue(iValue.toFixed(0).toString().padStart(Number(oInputData.lenghttype), "0"));
			} else {
				return this.setInvalidValue(oEvent);
			}
		}
		return false;
	};

	// Method to validate 'P' (Percentage) type input
	BusinessRulesDetailsController.validatePType = function(oInputData, sValue, regexDecPattern, oEvent) {
		const iValue = Number(sValue);

		if (oInputData.operator === "CP" || oInputData.operator === "NP") {
			if (!sValue.match(regexDecPattern) && sValue.trim() !== "") {
				return this.setInvalidValue(oEvent);
			}
		} else {
			if (isNaN(iValue) || sValue.trim() === "") {
				return this.setInvalidValue(oEvent);
			} else {
				let cellValueFixed = iValue.toFixed(Number(oInputData.decimals)).toString().replace(".", "");
				if (cellValueFixed.length === 1 && Number(oInputData.lenghttype) === 1) {
					oEvent.getSource().setValue(Number(iValue));
				} else if (cellValueFixed.length >= Number(oInputData.lenghttype)) {
					let newValue = cellValueFixed.slice(0, (oInputData.lenghttype - (Number(oInputData.decimals) + 1))) + "." +
						cellValueFixed.slice((oInputData.lenghttype - (Number(oInputData.decimals) + 1)), oInputData.lenghttype - 1);
					oEvent.getSource().setValue(Number(newValue).toFixed(Number(oInputData.decimals)));
				} else {
					oEvent.getSource().setValue(iValue.toFixed(Number(oInputData.decimals)));
				}
			}
		}
		return false;
	};

	// Method to validate general types ('I', 'D', 'T')
	BusinessRulesDetailsController.validateGeneralType = function(sValue, regexNum, oEvent) {
		const iValue = Number(sValue);
		if (iValue.toFixed(0).toString().match(regexNum) && sValue.trim() !== "") {
			oEvent.getSource().setValue(iValue.toFixed(0));
		} else {
			return this.setInvalidValue(oEvent);
		}
		return false;
	};

	// Method to validate 'F' (Floating point) type input
	BusinessRulesDetailsController.validateFType = function(sValue, regexFLTP, regexFLTPPattern, oInputData, oEvent) {
		if ((oInputData.operator === "CP" || oInputData.operator === "NP") && sValue.match(regexFLTPPattern) ||
			sValue.match(regexFLTP) && sValue.trim() !== "") {
			oEvent.getSource().setValue(sValue.toUpperCase());
		} else {
			return this.setInvalidValue(oEvent);
		}
		return false;
	};

	BusinessRulesDetailsController.validateInputValue = function (oModel, bSuggestion, sPath, oInput, oEvent){
		const sValue = oInput.getValue();
		let flagInvalid = false;
	
		// Regular expressions
		const regexPatterns = {
			num: /^[0-9]+$/,
			fltp: /^([+-]?\.?[0-9]+([+-]?(e|E))?)+$/,
			numPattern: /^(?:\*?[0-9]+\*?)+$/,
			decPattern: /^(?:\*?\.?[0-9]+\.?\*?)+$/,
			fltpPattern: /^(?:\*?[+-]?\.?[0-9]*\*?([+-]?(e|E))?\*?)+$/
		};
	
		// Suggestion validation
		if (bSuggestion) {
			const aSuggestionItems = oInput.getSuggestionItems();
			const oSelectedItem = aSuggestionItems.find(item => item.getText() === sValue);
			oModel.setProperty(sPath + "/validValue", sValue && !oSelectedItem ? false : true);
		} else {
			const oInputData = oModel.getProperty(sPath);
	
			// Perform validation based on input type
			if (oInputData.colName.includes("LENGTH")) {
				flagInvalid = this.validateLength(sValue, regexPatterns.num, oEvent);
			} else if (oInputData.kind === "C") {
				flagInvalid = this.validateCType(sValue, oEvent);
			} else if (oInputData.kind === "N") {
				flagInvalid = this.validateNType(oInputData, sValue, regexPatterns.numPattern, regexPatterns.num, oEvent);
			} else if (oInputData.kind === "P") {
				flagInvalid = this.validatePType(oInputData, sValue, regexPatterns.decPattern, oEvent);
			} else if (["I", "D", "T"].includes(oInputData.kind)) {
				flagInvalid = this.validateGeneralType(sValue, regexPatterns.num, oEvent);
			} else if (oInputData.kind === "F") {
				flagInvalid = this.validateFType(sValue, regexPatterns.fltp, regexPatterns.fltpPattern, oInputData, oEvent);
			}
		}
	
		// Show message if invalid value
		if (flagInvalid) {
			MessageToast.show("Enter a valid value", {
				at: "center top",
				offset: "0 200"
			});
		}
	};
	
	BusinessRulesDetailsController.changeInputValue = function (oEvent) {
		let oInput = oEvent.getSource();
		let sPath = oInput.getBindingContext("BusinessRulesDetails").getPath();
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let sValue = oInput.getValue();

		this.cValue = sValue;

		if( oBusinessRulesDetailsModel.oData.validationMessage.length > 0) {

			for (let i = 0; i < oBusinessRulesDetailsModel.oData.validationMessage.length; i++) {
				let message = oBusinessRulesDetailsModel.oData.validationMessage[i];
				if (message.colName === "MSGNO" && message.colValue) {
					let messageParts = message.colValue.split("-");
					if (messageParts.length > 1) {
						// Trim any leading/trailing spaces and set colValuedescr to the part after the '-'
						let description = messageParts[1].trim();
						message.colValuedescr = description;
					}

					// Clear the validation table. Clear the table
					this._updateValidationFormulaTable(undefined);
					this.oFormulaComp.setTableStruct([]);
				}
			}
		}

		this.validateInputValue(oBusinessRulesDetailsModel, oInput.getShowSuggestion(), sPath, oInput, oEvent);
	};

	BusinessRulesDetailsController.changeDatePickerValue = function (oEvent) {
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let sPath = oEvent.getSource().getBindingContext("BusinessRulesDetails").getPath();
		let oDatePicker = oEvent.getSource();
		let sDatePickerValue = oDatePicker.getValue();
		let flagValid = oDatePicker.isValidValue();
		if (!flagValid) {
			oDatePicker.setValue();
			MessageToast.show("Enter correct format for dates", {
				at: "center top",
				offset: "0 200"
			});
		}else{
			oDatePicker.setValueState("None");
			oDatePicker.setValueStateText();
			oBusinessRulesDetailsModel.setProperty(sPath + "/colValue", sDatePickerValue);
		}
	};

	// RDG-12: event that sets the datepicker value and enables depending on the checbox status
	BusinessRulesDetailsController.onCurrentDateSelect = function (oEvent){
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let sPath = oEvent.getSource().getBindingContext("BusinessRulesDetails").getPath();
		let bCheckboxStatus = oEvent.getSource().getSelected();

		oBusinessRulesDetailsModel.setProperty(sPath + "/ebDatePicker", !bCheckboxStatus);
		
		if(bCheckboxStatus){
			oBusinessRulesDetailsModel.setProperty(sPath + "/colValue", "-1");
		} else {
			oBusinessRulesDetailsModel.setProperty(sPath + "/colValue", "");
		}
	};

	BusinessRulesDetailsController.datePickerFormatter = function (sDatePickerValue, bCurrentDate) {
		if(bCurrentDate){
			return "Current Date";
		} 

		return sDatePickerValue;
	};

	BusinessRulesDetailsController.onLiveChangeMessageValue = function(oEvent) {
		let oInput = oEvent.getSource();
		let sType = oInput.getParent().getParent().getItems()[0].getText();
		let sValue = oEvent.getParameter("value");
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");

		if (sType === "MESSAGE CLASS") {
			//Bug 13190 - Split the value to send only the key to search for values
			sValue = sValue?.split(" - ")[0];
			let oMsgNumber = oBusinessRulesDetailsModel.getProperty("/validationMessage").find(oMsg => oMsg.colName === "MSGNO");
			let promiseMessageTextList = GetLists.getMessageClassList(this, sValue.toUpperCase(), 100);
			
			oMsgNumber.colValue = "";
			oInput.setBusy(true);
			promiseMessageTextList.then(function (oMessageClassList) {
	
				//Remove all suggestion Items from the Aggregation and rebind it in a loop
				if (oMessageClassList.length > 0) {
					oInput.removeAllSuggestionItems();
					oMessageClassList.forEach(function (item) {
						oInput.addSuggestionItem(new sap.ui.core.Item({
							key: item.Arbgb,
							text: item.Arbgb + (item.Stext.length > 0 ? " - " : "") + item.Stext
						}));
					});
				}
				oInput.setBusy(false);
				//Bug 13190 - Execute change event once the suggestion list is ready
				oInput.fireChange({value: sValue});
			});
		}else{
			let oMsgClass = oBusinessRulesDetailsModel.getProperty("/validationMessage").find(oMsg => oMsg.colName === "MSGID");
			let sValidationClass = oMsgClass.colValue.split(" - ")[0];
			let promiseMessageTextList = GetLists.getMessageTextList(this, sValidationClass);

			oInput.setBusy(true);
			promiseMessageTextList.then(function (oMessageList) {

				//Remove all suggestion Items from the Aggregation and rebind it in a loop
				oInput.removeAllSuggestionItems();
				oMessageList.forEach(function (item) {
					oInput.addSuggestionItem(new sap.ui.core.Item({
						key: item.Msgnr,
						text: item.Msgnr + (item.Text.length > 0 ? " - " : "") + item.Text
					}));
				});
				oInput.setBusy(false);
			});
		}		
	};

	BusinessRulesDetailsController.onOperatorChange = function(oEvent) {
		let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails"); 
		let sPath = oEvent.getSource().getBindingContext("BusinessRulesDetails").getPath();
		oBusinessRuleModel.setProperty(sPath + "/colValue", "");
		oBusinessRuleModel.setProperty(sPath + "/ebDatePicker", true);
		oBusinessRuleModel.setProperty(sPath + "/currentDateCheck", false);
	};
	
	BusinessRulesDetailsController.onPressRuleByStep = async function(oEvent) {
		sap.ui.core.BusyIndicator.show(100);
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let oSelectedItem = oEvent.getSource().getSelectedItem();
		let oPath = oSelectedItem.getBindingContext("BusinessRulesDetails").getPath();
		let oSelectedRule = oBusinessRulesDetailsModel.getProperty(oPath);
		let sRuleNumber = oSelectedItem.getTitle().split(" ")[1];
		oBusinessRulesDetailsModel.setProperty("/selectedRule", oSelectedRule);
		oBusinessRulesDetailsModel.setProperty("/selectedRulesNumber", sRuleNumber);
		this._loadAttributesModels();
	};

	BusinessRulesDetailsController.addDrivingValues = async function(oBusinessRulesDetailsModel, aModelToDriving, aModelToDrivingAttrValues, sDataModel){
		let aDrivingAttributesValues = [];
		for(let i = 0; i < aModelToDriving.length; i++){
			let sColName = aModelToDriving[i].usmdEntity + "__" + aModelToDriving[i].usmdAttribute;
			for(let j = 0; j < aModelToDrivingAttrValues.length; j++){
				if(sColName === aModelToDrivingAttrValues[j].colName) {
					aModelToDrivingAttrValues[j].colValue = aModelToDrivingAttrValues[j].colValue === "INIT" ? "" : aModelToDrivingAttrValues[j].colValue;
					aModelToDrivingAttrValues[j].attrdatatype = aModelToDriving[i].attrdatatype;
					// RDG-12: if type is date and the col value is -1 then disable the datepicker and check the current date checkbox
					// otherwise, do the oposite
					if(aModelToDrivingAttrValues[j].attrdatatype ==="DATS" && aModelToDrivingAttrValues[j].colValue === "-1"){
						aModelToDrivingAttrValues[j].ebDatePicker = false;
						aModelToDrivingAttrValues[j].currentDateCheck = true;
						// aModelToDrivingAttrValues[j].colValue = "Current Date";
					} else {
						aModelToDrivingAttrValues[j].ebDatePicker = true;
						aModelToDrivingAttrValues[j].currentDateCheck = false;
					}
					aModelToDrivingAttrValues[j].decimals = aModelToDriving[i].decimals;
					aModelToDrivingAttrValues[j].kind = aModelToDriving[i].kind;
					aModelToDrivingAttrValues[j].lenghttype = aModelToDriving[i].lenghttype;
					aDrivingAttributesValues.push(aModelToDrivingAttrValues[j]);
				}
			}
		}

		for(let i = 0; i < aDrivingAttributesValues.length; i++){
			let sEntity = aDrivingAttributesValues[i].colName.split("__")[0];
			let sAttribute = aDrivingAttributesValues[i].colName.split("__")[1];
			let sOperator = aDrivingAttributesValues[i].operator;
			let sValue = aDrivingAttributesValues[i].colValue;
			let sDescription = aDrivingAttributesValues[i].colValuedescr;
			if (sValue && !sValue.includes(" - ") && sOperator !== "CP" && sOperator !== "NP" && !sDescription) {
				let oValue = await  GetLists.getAttributeValuesList(this, sDataModel, sEntity, sAttribute, sValue, true);
				if (oValue.length > 0) {
					if(oValue.results){
						oValue = oValue.results;
					}
					aDrivingAttributesValues[i].colValue = oValue[0].Ddtext ? oValue[0].Key + " - " + oValue[0].Ddtext : oValue[0].Key;
				}
			}else if(sDescription && !sValue.includes(" - ")){
				sDescription = sDescription.replace("-BLANK-", "");
				aDrivingAttributesValues[i].colValue += (sDescription ? " - " + sDescription: "");
			}
		}
		oBusinessRulesDetailsModel.setProperty("/drivingAttributes", aDrivingAttributesValues);
	};

	BusinessRulesDetailsController.addDerivingValues = async function(oBusinessRulesDetailsModel, aModelToDeriving, aModelToDerivingAttrValues, isCrossEntity, oKeyAttributesColValueList, sDataModel) {
		let aDerivingAttributesValues = [];
		let oController = this;
		
		for (let i = 0; i < aModelToDeriving.length; i++) {
			let sColName = aModelToDeriving[i].usmdEntity + "__" + aModelToDeriving[i].usmdAttribute;
			
			for (let j = 0; j < aModelToDerivingAttrValues.length; j++) {
				if (sColName === aModelToDerivingAttrValues[j].colName) {
					// Update the attributes
					oController.updateAttrValues(aModelToDerivingAttrValues[j], aModelToDeriving[i]);
	
					// Handle the date picker logic
					oController.addDatePickerProperties(aModelToDerivingAttrValues[j]);
	
					// Set attribute selection type
					await oController.setAttrSelectionType(aModelToDerivingAttrValues[j], sDataModel);
	
					// Handle cross-entity flag
					if (isCrossEntity === "X") {
						aModelToDerivingAttrValues[j].isKey = oKeyAttributesColValueList.includes(sColName) ? "X" : "";
					}
					
					aDerivingAttributesValues.push(aModelToDerivingAttrValues[j]);
				}
			}
		}
	
		// Populate the values for derived attributes
		await oController.populateDerivedAttributes(aDerivingAttributesValues, sDataModel);
	
		// Set the final list to the model
		oBusinessRulesDetailsModel.setProperty("/derivingAttributes", aDerivingAttributesValues);
	};
	
	// Helper function to update the attribute values
	BusinessRulesDetailsController.updateAttrValues = function(oModelToDerivingAttrValues, oModelToDeriving) {
		oModelToDerivingAttrValues.attrvaltoconcatnav = oModelToDerivingAttrValues.attrvaltoconcatnav.map(oVal => ({
			fixedVal: oVal.fixedVal ? oVal.fixedVal : oVal.FixedVal,
			paranthesis: oVal.paranthesis ? oVal.paranthesis : oVal.Paranthesis,
			operator: oVal.operator ? oVal.operator : oVal.Operator,
			usmdAttribute: oVal.usmdAttribute ? oVal.usmdAttribute : oVal.UsmdAttribute,
			usmdEntity: oVal.usmdEntity ? oVal.usmdEntity : oVal.UsmdEntity,
			usmdModel: oVal.usmdModel ? oVal.usmdModel : oVal.UsmdModel
		}));
	
		oModelToDerivingAttrValues.colValue = oModelToDerivingAttrValues.colValue === "INIT" ? "" : oModelToDerivingAttrValues.colValue;
		oModelToDerivingAttrValues.attrdatatype = oModelToDeriving.attrdatatype;
		oModelToDerivingAttrValues.decimals = oModelToDeriving.decimals;
		oModelToDerivingAttrValues.kind = oModelToDeriving.kind;
		oModelToDerivingAttrValues.lenghttype = oModelToDeriving.lenghttype;
		oModelToDerivingAttrValues.selectedType = oModelToDerivingAttrValues.selectedType || oModelToDerivingAttrValues.type;
		oModelToDerivingAttrValues.selectedType = oModelToDerivingAttrValues.selectedType ? oModelToDerivingAttrValues.selectedType : "1";
		oModelToDerivingAttrValues.tableDetails = oModelToDerivingAttrValues.attrvaltoconcatnav;
		oModelToDerivingAttrValues.formulaText = oModelToDerivingAttrValues.colValue;
		oModelToDerivingAttrValues.delimiter = oModelToDerivingAttrValues.delimiter ? oModelToDerivingAttrValues.delimiter.replace(/{{SPACE}}/g, " ") : "";
	};
	
	// Helper function to handle date picker logic
	BusinessRulesDetailsController.addDatePickerProperties = function(oModelToDerivingAttrValues) {
		if (oModelToDerivingAttrValues.attrdatatype === "DATS" && oModelToDerivingAttrValues.colValue === "-1") {
			oModelToDerivingAttrValues.ebDatePicker = false;
			oModelToDerivingAttrValues.currentDateCheck = true;
		} else {
			oModelToDerivingAttrValues.ebDatePicker = true;
			oModelToDerivingAttrValues.currentDateCheck = false;
		}
	};
	
	// Helper function to set the attribute selection type
	BusinessRulesDetailsController.setAttrSelectionType = async function(oModelToDerivingAttrValues, sDataModel) {
		if (oModelToDerivingAttrValues.attrvaltoconcatnav.length === 0 && !oModelToDerivingAttrValues.colValue && oModelToDerivingAttrValues.colEntityVal && oModelToDerivingAttrValues.colAttrVal) {
			oModelToDerivingAttrValues.attrSelectionType = 1;
			if (oModelToDerivingAttrValues.attrSelectionType === 1 && oModelToDerivingAttrValues.colEntityVal) {
				let arrAttrList = await GetLists.getEntityAttributeList(this, sDataModel, oModelToDerivingAttrValues.colEntityVal, undefined);
				if (arrAttrList && arrAttrList.length > 0) {
					oModelToDerivingAttrValues.attrList = arrAttrList.filter(oAttr => oAttr.DATATYPE === oModelToDerivingAttrValues.attrdatatype && oAttr.Length === oModelToDerivingAttrValues.lenghttype);
				}
			}
		} else {
			oModelToDerivingAttrValues.attrSelectionType = 0;
		}
	};
	
	// Helper function to populate derived attribute values
	BusinessRulesDetailsController.populateDerivedAttributes = async function(aDerivingAttributesValues, sDataModel) {
		for (let i = 0; i < aDerivingAttributesValues.length; i++) {
			let sEntity = aDerivingAttributesValues[i].colName.split("__")[0];
			let sAttribute = aDerivingAttributesValues[i].colName.split("__")[1];
			let sValue = aDerivingAttributesValues[i].colValue;
			let sDescription = aDerivingAttributesValues[i].colValuedescr;
	
			if (sValue && !sValue.includes(" - ") && !sDescription) {
				let oValue = await GetLists.getAttributeValuesList(this, sDataModel, sEntity, sAttribute, sValue, true);
				if (oValue.length > 0) {
					if (oValue.results) {
						oValue = oValue.results;
					}
					aDerivingAttributesValues[i].colValue = oValue[0].Ddtext ? oValue[0].Key + " - " + oValue[0].Ddtext : oValue[0].Key;
				}
			} else if (sDescription && !sValue.includes(" - ")) {
				sDescription = sDescription?.replace("-BLANK-", "");
				aDerivingAttributesValues[i].colValue += (sDescription ? " - " + sDescription : "");
			}
		}
	};

	BusinessRulesDetailsController.addValidationValues = async function(oBusinessRulesDetailsModel, aModelToValidation, aModelToDerivingAttrValues){
		let aValidationMessageValues = [];
		let aMessageTextList = [];
		for(let i = 0; i < aModelToValidation.length; i++){
			let sColName = aModelToValidation[i].usmdEntity;
			for(let j = 0; j < aModelToDerivingAttrValues.length; j++){
				if(sColName === aModelToDerivingAttrValues[j].colName) {
					aModelToDerivingAttrValues[j].attrdatatype = aModelToValidation[i].attrdatatype;
					aModelToDerivingAttrValues[j].decimals = aModelToValidation[i].decimals;
					aModelToDerivingAttrValues[j].kind = aModelToValidation[i].kind;
					aModelToDerivingAttrValues[j].lenghttype = aModelToValidation[i].lenghttype;

					if(sColName === "MSGID"){
						aMessageTextList = await GetLists.getMessageTextList(this, aModelToDerivingAttrValues[j].colValue)	;
					}

					if (sColName === "MSGNO" ){
						if (aMessageTextList) {
							aModelToDerivingAttrValues[j].colValue += ""  + (aMessageTextList.find(item => item.Msgnr === aModelToDerivingAttrValues[j].colValue.replace(/[-\s]/g, ""))?.Text || "");
						}
						let sFormText ="";
						if(aModelToDerivingAttrValues[j].attrvaluestoplaceh){
							sFormText = this.checkFormText(aModelToDerivingAttrValues[j], sFormText);
						}
					}	

					aValidationMessageValues.push(aModelToDerivingAttrValues[j]);
				}
			}
		}

		await this.addMessageValues(aValidationMessageValues);

		oBusinessRulesDetailsModel.setProperty("/validationMessage", aValidationMessageValues);
	};

	BusinessRulesDetailsController.addMessageValues = async function(aValidationMessageValues){
		let sMsgClass;
		for(let i = 0; i < aValidationMessageValues.length; i++){
			let sValue = aValidationMessageValues[i].colValue;
			let sDescription = aValidationMessageValues[i].colValuedescr;
			
			if (sValue && !sValue.includes(" - ") && !sDescription) {
				let aValues = [];
				if (aValidationMessageValues[i].colName === "MSGID") {
					sMsgClass = sValue;
					aValues = await GetLists.getMessageClassList(this, sValue.toUpperCase(), 100);
					if (aValues.length > 0) {
						if(aValues.results){
							aValues = aValues.results;
						}
						let oMsgClass = aValues.find(oValue => oValue.Arbgb === sValue.toUpperCase());
						aValidationMessageValues[i].colValue += (oMsgClass.Stext ? " - " + oMsgClass.Stext : "");
					}
				}else if (aValidationMessageValues[i].colName === "MSGNO") {
					aValues = await GetLists.getMessageTextList(this, sMsgClass);
					if (aValues.length > 0) {
						if(aValues.results){
							aValues = aValues.results;
						}
						let oMsgNumber = aValues.find(oValue => oValue.Msgnr === sValue.toUpperCase());
						aValidationMessageValues[i].colValue += (oMsgNumber.Text ? " - " + oMsgNumber.Text : "");
					}
				}
				
				
			}else if (!sValue.includes(" - ") && sDescription) {
				sDescription = sDescription.replace("-BLANK-", "");
				aValidationMessageValues[i].colValue += (sDescription ? " - " + sDescription: "");
			}
		}
	};

	BusinessRulesDetailsController.checkFormText = async function(oModelToDerivingAttrValues, sFormText){
		for (let k = 0; k < oModelToDerivingAttrValues.attrvaluestoplaceh.length; k++) {
			let sValue = oModelToDerivingAttrValues.attrvaluestoplaceh[k];

			
			if (( sValue.usmdEntity !== undefined && sValue.usmdEntity !== ""  ) && (sValue.usmdAttribute !== undefined && sValue.usmdAttribute !== ""))  {
				sFormText += "{{" + sValue.usmdEntity + "-" + sValue.usmdAttribute + "}}";
			}
			
			if(sValue.fixedVal) {
				sFormText += sValue.fixedVal;
			}
			
			if(sFormText!==""){
				oModelToDerivingAttrValues.formulaText = sFormText;
			}
		}
	};

	BusinessRulesDetailsController._loadAttributesModels = async function () {
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let sRuleNumber = oBusinessRulesDetailsModel.getProperty("/selectedRulesNumber");
		let	oKeyAttributesColValueList = oBusinessRulesDetailsModel.getProperty("/keyAtrributes/keyAttributesWithEntityList");
		let isCrossEntity = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/crossentity");
		let sDataModel = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/DataModelId");
		let aModelToDriving = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltodriving");
		let oWorkFlowValue = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues").find(oRule => oRule.colName === "WF__STEP");
		let aModelToDrivingAttrValues = 
			oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues")
			.filter(oRule => oRule.rowNum === sRuleNumber && oRule.colName !== "WF__STEP" && Number(oRule.colNum) < Number(oWorkFlowValue.colNum));
		let aModelToDerivingAttrValues = 
			oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues")
			.filter(oRule => oRule.rowNum === sRuleNumber && oRule.colName !== "WF__STEP" && Number(oRule.colNum) > Number(oWorkFlowValue.colNum));
		let aModelToDeriving = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltoderiving");
		let aModelToValidation = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltovalidation");
		let oController = this;

		await this.addDrivingValues(oBusinessRulesDetailsModel, aModelToDriving, aModelToDrivingAttrValues, sDataModel);

		if (aModelToDeriving) {
			await this.addDerivingValues(oBusinessRulesDetailsModel, aModelToDeriving, aModelToDerivingAttrValues, isCrossEntity, oKeyAttributesColValueList, sDataModel);
		}

		if (aModelToValidation) {
			await this.addValidationValues(oBusinessRulesDetailsModel, aModelToValidation, aModelToDerivingAttrValues);
		}	
		oController.bindItemsToDerivingList();
		this.bindFormulaMessage();
		sap.ui.core.BusyIndicator.hide();
	};

	BusinessRulesDetailsController.bindFormulaMessage = function () {
		let oController = this;
		let oGridList = this.getView().byId("gridListValidation");
		oGridList.unbindItems();

		let oGridListIt =new sap.f.GridListItem({
			content: [
				new sap.m.VBox({
					//class: "sapUiSmallMargin",
					items: [
						new sap.m.Title({
							wrapping: true,
							width: "17rem",
							text: {
								parts: ["BusinessRulesDetails>colName"],
								formatter: function (colName) {
									switch (colName) {
										case "MSGTY":
											return "MESSAGE TYPE";
										case "MSGID":
											return "MESSAGE CLASS";
										default:
											return "MESSAGE NUMBER";
									}
								}
							}
						}),
						new sap.m.HBox({
							items: [
								new sap.m.Input({
									value: "{BusinessRulesDetails>colValue}",
									visible: {
										parts: ["BusinessRulesDetails>colName"],
										formatter: function (colName) {
											return colName !== "MSGTY";
										}
									},
									liveChange: oController.onLiveChangeMessageValue.bind(oController),
									showSuggestion: true,
									autocomplete: false,
									valueState: {
										parts: ["BusinessRulesDetails>validValue"],
										formatter: function (validValue) {
											return validValue ? "Information" : "Warning";
										}
									},
									valueStateText: {
										parts: ["BusinessRulesDetails>validValue"],
										formatter: function (validValue) {
											return validValue ? "Type to refresh list" : "The selected value is not in the list";
										}
									},
									valueLiveUpdate: true,
									change: oController.changeInputValue.bind(oController),
									layoutData: new sap.m.FlexItemData({
										growFactor: 4
									})
								}),
								new sap.m.ComboBox({
									items: {
										path: "BusinessRulesDetails>/selectedBusinessRule/lists/MessageTypeValues",
										templateShareable: true,
										template: new sap.ui.core.Item({
											key: "{BusinessRulesDetails>Etype}",
											text: "{BusinessRulesDetails>Etype} - {BusinessRulesDetails>Etext}"
										})
									},
									selectedKey: "{BusinessRulesDetails>colValue}",
									visible: {
										parts: ["BusinessRulesDetails>colName"],
										formatter: function (colName) {
											return colName === "MSGTY";
										}
									},
									width: "100%",
									layoutData: new sap.m.FlexItemData({
										growFactor: 4
									})
								}),
								new sap.m.HBox({
									items: [
										new sap.m.VBox({
											width: "0.2em"
										}),
										new sap.ui.core.ComponentContainer({
											name: "dmr.components.FormulaEdit",
											propagateModel: true,
											settings: {
												formulaType: FormulaType.PLACEHOLDER,
												ruleNumber: "{BusinessRulesDetails>/selectedRulesNumber}", 
												colName: "{BusinessRulesDetails>colName}",
												value: "{BusinessRulesDetails>colValue}",
												dataModel: "{BusinessRulesDetails>/selectedBusinessRule/DataModelId}",
												crType: "{BusinessRulesDetails>/selectedBusinessRule/CRTypeId}",
												entity: "{BusinessRulesDetails>colName}",
												isCrossEntity: "{BusinessRulesDetails>/selectedBusinessRule/RuleExpression/crossentity}",
												tableStruct: "{BusinessRulesDetails>attrvaluestoplaceh}"
											},
											visible: {
												parts: ["BusinessRulesDetails>colValuedescr", "BusinessRulesDetails>colValue"],
												formatter: function (validationMessages, validationMessageVal) {
													if (!validationMessages) {
														if(!validationMessageVal){
														return false;
														}
													}
												    	let message = validationMessages;
														let messageVal = validationMessageVal;
														if ( message && message.includes("&") || messageVal && messageVal.includes("&") ) { 
															return true;
														}
											
										
													return false;
												}
											},
											async: false
										})
										.attachComponentCreated(
											oController.onFormulaComponentMess,
											oController
										)
									]
								})
							]
						})
						.addStyleClass("sapUiTinyMarginTop")
					]
				}).addStyleClass("sapUiSmallMargin")
			]
		});

		oGridList.bindItems({
			path: "/validationMessage",
			model: "BusinessRulesDetails",
			template: oGridListIt,
			templateShareable: true
		});		
	};

	BusinessRulesDetailsController.onFormulaEditComplete = function(oEvent) {
		let oEventParams = oEvent.getParameters();
		let arrUpdatedTable = oEventParams.Data.TABLE;

		this._updateValidationFormulaTable(arrUpdatedTable);

		return;
	};

	/**
	 * Update validation formula table with the update table information 
	 *  The input table could be undefined, if the table is to be cleared. 
	 */
	BusinessRulesDetailsController._updateValidationFormulaTable = function(arrTable) {
		// if an input table has been provided, use the same, else assign empty array
		let arrUpdatedTable = arrTable? arrTable: [];
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let oWorkFlowValue = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues").find(oRule => oRule.colName === "WF__STEP");
		let sRuleNumber = oBusinessRulesDetailsModel.getProperty("/selectedRulesNumber");

		// Update the selectedBusinessRuleInformation
		oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues")
			.find(function(oRule, iIndex, arrayValidation){
				if( oRule.rowNum === sRuleNumber && 
					oRule.colName === "MSGNO" && 
					Number(oRule.colNum) > Number(oWorkFlowValue.colNum)){
						// Deep Copy the array to avoid reference links 
						arrayValidation[iIndex].attrvaluestoplaceh.splice(0); // Empty the array;
						for(let i  = 0; i < arrUpdatedTable.length; i++ ) {
							let oElement = Object.create(arrUpdatedTable[i]);
							arrayValidation[iIndex].attrvaluestoplaceh.push({
								fixedVal: oElement.fixedVal,
								placeholder: oElement.placeholder,
								usmdAttribute: oElement.UsmdAttribute,
								usmdEntity: oElement.UsmdEntity,
								usmdModel: oElement.UsmdModel
							});
						} 
					return true;
				}
				return false;
			});

		return;
	};

	BusinessRulesDetailsController.onFormulaComponentMess = function(oEvent) {
		let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");
		let comp = oEvent.getParameter("component");
		let sPath = oEvent.getSource().getBindingContext("BusinessRulesDetails").getPath();
		this.oFormulaComp = comp;

		comp.setFormulaType(FormulaType.PLACEHOLDER);
		comp.attachEditComplete(this.onFormulaEditComplete, this);

		comp.setBindingContext(new sap.ui.model.Context(oBusinessRuleModel, sPath));
	};


	BusinessRulesDetailsController.onAddAttribute = function (oEvent) {
		let sButtonId = oEvent.getSource().getId();
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let sCrossEntity = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/crossentity");
		let sDataModelId = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/DataModelId");
		let sCRTypeId = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/CRTypeId");
		let sEntityId = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/EntityId");
		let sRuleTypeId = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleTypeId");

		// Bug 12723 - Condition added to avoid adding attributes if there is missing data
		if (!sDataModelId || !sCRTypeId || !sEntityId || !sRuleTypeId) {
			Utilities.showPopupAlert(
				this.geti18nText("businessRulesDetails.header.missingdata.message"), 
				MessageBox.Icon.ERROR,
				this.geti18nText("businessRulesDetails.header.missingdata.title"));
			return;
		}

		let oAttributeSelectionModel = this.getView().getModel("attributeSelectionModel");
		let arrList = oAttributeSelectionModel.getProperty("/mainEntityList");
		oAttributeSelectionModel.setProperty("/selectedType", 0);
		
		if (!this._oDerivationValuesFrgmt) {
			this._oDerivationValuesFrgmt = sap.ui.xmlfragment(
				"SelectAttributeFragmentId",
				"dmr.mdg.supernova.SupernovaFJ.view.BusinessRules.SelectAttribute",
				this
			);

			this.getView().addDependent(this._oDerivationValuesFrgmt);
		}

		if (sButtonId.includes("btnAddDriving")) {
			oAttributeSelectionModel.setProperty("/attributeType", "Driving");
			//Bug 12810 - Added condition to identify if it is cross entity derivation and show or hide some attributes
			if (sCrossEntity === "X") {
				arrList = arrList.filter(oEntity => oEntity.UsmdEntity !== sEntityId);
			}
		}else{
			oAttributeSelectionModel.setProperty("/attributeType", "Deriving");
			oAttributeSelectionModel.setProperty("/selectedEntity", sEntityId);
			let oMultiComboBox = sap.ui.getCore().byId("SelectAttributeFragmentId--idCBoxEntities");
			oMultiComboBox.fireSelectionChange({selectedKey: sEntityId});
		}
		oAttributeSelectionModel.setProperty("/entityList", arrList);

		this._oDerivationValuesFrgmt.open();
	};

	BusinessRulesDetailsController.onDeleteAttribute = function(oEvent, sAttributeType) {
		let oController = this;
		let oItem = oEvent.getSource();
		let oPath = oItem.getBindingContext("BusinessRulesDetails").getPath();
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let aModelToDriving = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltodriving");
		let aModelToDeriving = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltoderiving");
		let aModelToValidation = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltovalidation");
		let aModelToAttrValues = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues");
		let oWorkflowValue = aModelToAttrValues.find(oAttribute => oAttribute.colName === "WF__STEP");
		let sAttributeToDelete = oBusinessRulesDetailsModel.getProperty(oPath);
		let promiseShowPopupAlert = 
			Utilities.showPopupAlert(
				oController.geti18nText("businessRulesDetails.gridlistitem.delete.confirm"), 
				MessageBox.Icon.WARNING,
				oController.geti18nText("businessRulesDetails.gridlistitem.delete.title"), 
				[MessageBox.Action.YES, MessageBox.Action.NO]
			);

			promiseShowPopupAlert.then(function () {
				if (sAttributeType === "Driving") {
					aModelToDriving = aModelToDriving.filter((oAttribute) => (oAttribute.usmdEntity + "__" + oAttribute.usmdAttribute) !== sAttributeToDelete.colName);
					aModelToDriving.forEach((oAttribute) => {
						if (Number(oAttribute.colNum) > Number(sAttributeToDelete.colNum)) {
							oAttribute.colNum = (Number(oAttribute.colNum) - 1).toString();
						}
					});
					oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/RuleExpression/modeltodriving", aModelToDriving);

					if (aModelToValidation) {
						aModelToValidation.forEach((oValidation) => {
							if (Number(oValidation.colNum) > Number(sAttributeToDelete.colNum)) {
								oValidation.colNum = (Number(oValidation.colNum) - 1).toString();
							}
						});
						oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/RuleExpression/modeltovalidation", aModelToValidation);
					}

					if (aModelToDeriving) {
						aModelToDeriving.forEach((oAttribute) => {
							oAttribute.colNum = (Number(oAttribute.colNum) - 1).toString();
						});
						oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/RuleExpression/modeltoderiving", aModelToDeriving);
					}
		
					aModelToAttrValues = aModelToAttrValues.filter((oAttribute) => (oAttribute.colName !== sAttributeToDelete.colName 
																	|| Number(oAttribute.colNum) >= Number(oWorkflowValue.colNum)));
					aModelToAttrValues.forEach((oAttributeValue) => {
						if (Number(oAttributeValue.colNum) > Number(sAttributeToDelete.colNum)) {
							oAttributeValue.colNum = (Number(oAttributeValue.colNum) - 1).toString();
						}
					});
					oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues", aModelToAttrValues);
				}else{
					aModelToDeriving = aModelToDeriving.filter((oAttribute) => (oAttribute.usmdEntity + "__" + oAttribute.usmdAttribute) !== sAttributeToDelete.colName);
					aModelToDeriving.forEach((oAttribute) => {
						if (Number(oAttribute.colNum) > Number(sAttributeToDelete.colNum)) {
							oAttribute.colNum = (Number(oAttribute.colNum) - 1).toString();
						}
					});
					oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/RuleExpression/modeltoderiving", aModelToDeriving);
		
					aModelToAttrValues = aModelToAttrValues.filter((oAttribute) => (oAttribute.colName !== sAttributeToDelete.colName 
																	|| Number(oAttribute.colNum) <= Number(oWorkflowValue.colNum)));
					aModelToAttrValues.forEach((oAttributeValue) => {
						if (Number(oAttributeValue.colNum) > Number(sAttributeToDelete.colNum)) {
							oAttributeValue.colNum = (Number(oAttributeValue.colNum) - 1).toString();
						}
					});
					oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues", aModelToAttrValues);
				}
		
				oController._loadAttributesModels();
			}, function(){});
	};

	BusinessRulesDetailsController.onChangeStepFilter = function (oEvent) {
		let oList = this.getView().byId("rulesListId");
		let sKey = oEvent.getSource().getSelectedKey();
		let oFilter = sKey ? new Filter("colValue", FilterOperator.EQ, sKey) : [];
		oList.getBinding("items").filter(oFilter);
	};

	BusinessRulesDetailsController.onChangeStep = function (oEvent) {
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let sSelectedValue = oEvent.getSource().getSelectedItem().getText();
		let oSelectedRule = oBusinessRulesDetailsModel.getProperty("/selectedRule");
		oSelectedRule.colValue = sSelectedValue.split(" - ")[0];
		oSelectedRule.description = sSelectedValue.split(" - ")[1];
	};
	
	BusinessRulesDetailsController.onChangeCRStepType = function () {
		CRStepTypePropsDialog.getCRTypePropsLists(this);
	};

	BusinessRulesDetailsController.onRuleSavePressed = function (oEvent) {
		let oController = this;
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule");
		if(oSelectedBusinessRule.RuleTypeId === "2") {
			SingleValueRuleDialog.onSaveValueCheckBusinesRule(oEvent, oController);
		} else if(oSelectedBusinessRule.RuleTypeId === "3") {
			SingleValueRuleDialog.onSaveDerivationBusinesRule(oEvent, oController);
		} else if(oSelectedBusinessRule.RuleTypeId === "6") {
			CRStepTypePropsDialog.saveCRTypePropsConfiguration(oController);
		} else {
			let oRuleExpression = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression");
			let aKeyAttrWithEntity = oBusinessRulesDetailsModel.getProperty("/keyAtrributes/keyAttributesWithEntityList");
			let bNewRule = oBusinessRulesDetailsModel.getProperty("/newRule");
			let oServerData = {};
			let oView = this.getView();
			let oWriteParameterObject = {
				success: function (oDataResult) {
					let arrMessages = [];
					jQuery.extend(true, arrMessages, oDataResult.MODELTOMESSAGE.results);
					// Sort the messages
					arrMessages.sort(function (m1) {
						if (m1.MessageType === "E") {
							return -1;
						}
						if (m1.Msgty === "W") {
							return 1;
						}
						return 0;
					});
					
					ModelMessages.showMessagesDialog(oController, arrMessages, oView);
					if (oDataResult.AppName) {
						oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/BusinessRuleName", oDataResult.AppName);
						oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/UserRuleName", oDataResult.UserRuleName);
						oBusinessRulesDetailsModel.setProperty("/newRule", false);
					}
					sap.ui.core.BusyIndicator.hide();
				},
				error: function () {
					sap.ui.core.BusyIndicator.hide();
					let promiseShowPopupAlert =
						Utilities.showPopupAlert(
							oController.geti18nText("businessRulesDetails.derivationFragment.saveerror.message"), 
							MessageBox.Icon.ERROR, 
							oController.geti18nText("businessRulesDetails.derivationFragment.saveerror.title")
						);
					promiseShowPopupAlert.then(function () {});
				}
			};

			if (!oSelectedBusinessRule.DataModelId || !oSelectedBusinessRule.CRTypeId || !oSelectedBusinessRule.EntityId || !oSelectedBusinessRule.RuleTypeId) {
				Utilities.showPopupAlert(
					oController.geti18nText("businessRulesDetails.header.missingdata.message"), 
					MessageBox.Icon.ERROR,
					oController.geti18nText("businessRulesDetails.header.missingdata.title"), 
				);

				return;
			}

			if (oRuleExpression.modeltodriving.length <= 1) {
				Utilities.showPopupAlert(
					oController.geti18nText("businessRulesDetails.derivationFragment.adddriving.message"), 
					MessageBox.Icon.ERROR,
					oController.geti18nText("businessRulesDetails.derivationFragment.adddriving.title"));

				return;
			}
			
			let amodeltodriving = oRuleExpression.modeltodriving.map(oAttribute => {
				return {
					UsmdEntity: oAttribute.usmdEntity,
					UsmdAttribute: oAttribute.usmdAttribute,
					ColNum: oAttribute.colNum,
					Attrdatatype: oAttribute.attrdatatype,
					Lenghttype: oAttribute.lenghttype,
					Decimals: oAttribute.decimals,
					Kind: oAttribute.kind
				};
			});
			

			let oDataModel;
			let amodeltovalues;

			if (oSelectedBusinessRule.RuleTypeId === "4") {
				let amodeltovalidation = oRuleExpression.modeltovalidation.map(oValidation => {
					return {
						UsmdEntity: oValidation.usmdEntity,
						ColNum: oValidation.colNum,
						Lenghttype: oValidation.lenghttype
					};
				});

				//Bug 13109 - Added validation to avoid saving the rules without validation message values
				let bMissingValues = false;
				for(let i = 0; i < oRuleExpression.modeltoattrvalues.length; i++){
					const oValue = oRuleExpression.modeltoattrvalues[i];
					if ((oValue.colName === "MSGTY" || oValue.colName === "MSGID" || oValue.colName === "MSGNO") && !oValue.colValue) {
						bMissingValues = true;
						break;
					}
				}

				if (bMissingValues) {
					Utilities.showPopupAlert(
						oController.geti18nText("businessRulesDetails.derivationFragment.addvalidation.message"), 
						MessageBox.Icon.ERROR,
						oController.geti18nText("businessRulesDetails.derivationFragment.addvalidation.title"));
					return;
				}

				amodeltovalues = oRuleExpression.modeltoattrvalues.map(oValue => {
					let sDescription = "";
					let sColValue = "";

					if(oValue.colValue){
						const arrParts = oValue.colValue.split(" - ");
						if(arrParts[1]){
							sDescription = arrParts[1];
						} else {
							sDescription = "-BLANK-";
						}

						if(oValue.colValue === "-1" && oValue.attrdatatype === "DATS"){
							sColValue = "-1";
						} else {
							sColValue = oValue.colValue.split(" - ")[0];
						}
					} else {
						sColValue = "INIT";
					}

					let oReturnObject = {
						ColName: oValue.colName,
						// Bug 12933 - Added condition to send "INIT" instead of blank values
						ColValue: sColValue,
						ColValueDescr: sDescription,
						ColNum: oValue.colNum,
						RowNum: oValue.rowNum,
						Operator: oValue.operator
					};

					if(oValue.colName === "MSGNO"){
						oReturnObject.ATTRVALUESTOPLACEH = oValue.attrvaluestoplaceh.map(oAttribute => {
							return {
								UsmdEntity: oAttribute.usmdEntity,
								UsmdAttribute: oAttribute.usmdAttribute,
								Placeholder: oAttribute.placeholder,
								UsmdModel: oAttribute.usmdModel,
								FixedVal: oAttribute.fixedVal
							};
						});
					}

					return oReturnObject;
				});

				oServerData.MODELTOVALIDATION = amodeltovalidation.sort((a, b) => a.ColNum - b.ColNum);	
				oDataModel = oController.getModel("YGW_ZEUS_BRF_VALIDATION_MODEL");
			}else{
				if (oRuleExpression.modeltoderiving.length === 0) {
					Utilities.showPopupAlert(
						oController.geti18nText("businessRulesDetails.derivationFragment.addderiving.message"), 
						MessageBox.Icon.ERROR,
						oController.geti18nText("businessRulesDetails.derivationFragment.addderiving.title"));
						
					return;
				}

				if (oRuleExpression.crossentity === "X") {
					let bMissingValues = false;
					oRuleExpression.modeltoattrvalues.forEach(oValue => {
						if (aKeyAttrWithEntity.includes(oValue.colName) && !(oValue.colValue || oValue.colEntityVal && oValue.colAttrVal)) {
							bMissingValues = true;
						}
					});
					if (bMissingValues) {
						Utilities.showPopupAlert(
							oController.geti18nText("businessRulesDetails.derivationFragment.addkeyattributes.message"), 
							MessageBox.Icon.ERROR,
							oController.geti18nText("businessRulesDetails.derivationFragment.addkeyattributes.title"));
						return;
					}
				}

				let amodeltoderiving = oRuleExpression.modeltoderiving.map(oAttribute => {
					return {
						UsmdEntity: oAttribute.usmdEntity,
						UsmdAttribute: oAttribute.usmdAttribute,
						ColNum: oAttribute.colNum,
						Attrdatatype: oAttribute.attrdatatype,
						Lenghttype: oAttribute.lenghttype
					};
				});

				amodeltovalues = oRuleExpression.modeltoattrvalues.map(oValue => {
					let sDescription = "";
					let sColValue = "";
					let sColValueDesc = "";
					let sColType = "";

					if(oValue.colValue){
						const arrParts = oValue.colValue.split(" - ");
						if(arrParts[1]){
							sDescription = arrParts[1];
						} else {
							sDescription = "-BLANK-";
						}
						
					}
					
					if(oValue.attrvaltoconcatnav && oValue.attrvaltoconcatnav.length > 0 ){
						if (oValue.colValue) {
							if(oValue.colValue === "-1" && oValue.attrdatatype === "DATS"){
								// sColValue = "-1";
								sColType = "2";
							} else {
								// sColValue = oValue.colValue;
								sColType = oValue.selectedType;
							}
							sColValue = oValue.colValue;
							sColValueDesc = oValue.colValue;
						} else {
							sColValue = "INIT";
							sColValueDesc = "INIT";
						}
					} else {
						if (oValue.colValue) {
							if(oValue.colValue === "-1" && oValue.attrdatatype === "DATS"){
								// sColValue = "-1";
								sColType = "2";
							} else {	
								// sColValue = oValue.colValue.split(" - ")[0];
								sColType = oValue.selectedType;
							}
							sColValue = oValue.colValue.split(" - ")[0];

						} else {
							sColValue = "INIT";
							sColValueDesc = "INIT";
						}
						sColValueDesc = sDescription;  // Uses the computed sDescription
					}

					return {
						ColName: oValue.colName,
						// Bug 12933 - Added condition to send "INIT" instead of blank values
						ColValue: sColValue,
						ColValueDesc: sColValueDesc,													
						ColEntityVal: oValue.colEntityVal,
						ColAttrVal: oValue.colAttrVal,
						ColNum: oValue.colNum,
						RowNum: oValue.rowNum,
						Operator: oValue.operator,
						Type: sColType,
						Delimiter: oValue.delimiter ? oValue.delimiter.replace(/ /g, "{{SPACE}}") : "",
						ATTRVALTOCONCATNAV: oValue.attrvaltoconcatnav ? oValue.attrvaltoconcatnav.map(oValConcat => {
							return {
								UsmdEntity: oValConcat.usmdEntity || oValConcat.UsmdEntity || oValConcat.smdEntity,
								UsmdAttribute: oValConcat.usmdAttribute || oValConcat.UsmdAttribute || "",
								FixedVal: oValConcat.fixedVal || oValConcat.FixedVal || "",
								Operator: oValConcat.operator || oValConcat.Operator || "",
								UsmdModel: oValConcat.usmdModel || oValConcat.UsmdModel || "",
								Paranthesis: oValConcat.paranthesis || oValConcat.Paranthesis || ""
							};
						}) : []
					};
				});

				oServerData.Crossentity = oRuleExpression.crossentity;
				oServerData.MODELTODERIVING = amodeltoderiving.sort((a, b) => a.ColNum - b.ColNum);
				oDataModel = oController.getModel("YGW_ZEUS_BRF_DERIVE_MODEL");
			}

			let promiseBAdI = oController._getSelectedBADIDialog(oController, oSelectedBusinessRule);
			promiseBAdI.then(function(oSelectedBAdI){
				oController._getSelectedTransportPackage(true, true, true)
				.then(function(oTransportPackageResponse){
					if (!bNewRule) {
						oServerData.AppName = oSelectedBusinessRule.BusinessRuleName;
					}
					oServerData.UserRuleName = oSelectedBusinessRule.UserRuleName;
					oServerData.UsmdModel = oSelectedBusinessRule.DataModelId;
					oServerData.UsmdCreqType = oSelectedBusinessRule.CRTypeId;
					oServerData.UsmdDevclass = oTransportPackageResponse.package;
					oServerData.UsmdEntity = oSelectedBusinessRule.EntityId;
					oServerData.BadiImpl  = oSelectedBAdI.BADISelected || oSelectedBAdI;
					oServerData.NewBadi  = oSelectedBAdI.isNewBADI || "";
					oServerData.CustTransport = oTransportPackageResponse.customizingTransport;
					oServerData.WbTransport = oTransportPackageResponse.workbenchTransport;
	
					oServerData.MODELTODRIVING = amodeltodriving.sort((a, b) => a.ColNum - b.ColNum);
					oServerData.MODELTOATTRVALUES = amodeltovalues.sort((a, b) => a.ColNum - b.ColNum).sort((a, b) => a.RowNum - b.RowNum);
											
					oServerData.MODELTOMESSAGE = [];
					oServerData.DraftAi = "";
					
					sap.ui.core.BusyIndicator.show(300);
					oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
					oDataModel.create("/MODELSet", oServerData, oWriteParameterObject);
				});
	
			});
		}
		
	};

	BusinessRulesDetailsController.onRuleDeletePressed = function (oEvent) {
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule");
		let oController = this;
		let promiseShowPopupAlert;

		promiseShowPopupAlert = Utilities.showPopupAlert(
			oController.geti18nText("businessRulesDetails.derivationFragment.deleterule.confirm"), 
			MessageBox.Icon.WARNING,
			oController.geti18nText("businessRulesDetails.derivationFragment.deleterule.title"),
			[MessageBox.Action.YES, MessageBox.Action.NO]
		);
		
		promiseShowPopupAlert.then(function(){
			//Display Error Message as User is trying to delete a rule which has not been created
			if (oSelectedBusinessRule.BusinessRuleName === "New Rule") {
				Utilities.showPopupAlert(
					"Error: Cannot delete a business rule which has not been created.", 
					MessageBox.Icon.ERROR,
					"Action not possible");
	
				return;
			}
			if (oSelectedBusinessRule.RuleTypeId === "1") {
				MandatoryRuleDialog.deleteRule(oController);
			} else if(oSelectedBusinessRule.RuleTypeId === "2" || oSelectedBusinessRule.RuleTypeId === "3") {
				SingleValueRuleDialog.onRuleDeletePressed(oEvent, oController);
			} else {
				oController.MultiValueRuleDialog.createDeletePayload(oController, oSelectedBusinessRule, false)
				.then((oDataToDelete) => {
					if (oSelectedBusinessRule.RuleTypeId === "4") {
						oController.MultiValueRuleDialog.sendDeleteRequest(oController, oBusinessRulesDetailsModel, oDataToDelete, false, "Validation");
					} else if (oSelectedBusinessRule.RuleTypeId === "5") {
						oController.MultiValueRuleDialog.sendDeleteRequest(oController, oBusinessRulesDetailsModel, oDataToDelete, false, "Derivation");
					}
				});
			}
		}, function () {});
	};



	BusinessRulesDetailsController.onDeleteStepRule = async function(){
		this.getView().byId("rulesListId").removeSelections();
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let nSelectedRule = oBusinessRulesDetailsModel.getProperty("/selectedRulesNumber");
		let aRulesByStep = oBusinessRulesDetailsModel.getProperty("/rulesByStep");
		let aModelToAttrValues = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues");
		aRulesByStep = aRulesByStep.filter(oRule => oRule.rowNum !== nSelectedRule.toString());
		aModelToAttrValues = aModelToAttrValues.filter(oValue => oValue.rowNum !== nSelectedRule.toString());
		for(let i = 0; i < aRulesByStep.length; i++){
			if (Number(aRulesByStep[i].rowNum) > Number(nSelectedRule)) {
				aRulesByStep[i].rowNum = (Number(aRulesByStep[i].rowNum) -1).toString();
			}
		}
		for(let i = 0; i < aModelToAttrValues.length; i++){
			if (aModelToAttrValues[i].colName !== "WF__STEP") {
				if (Number(aModelToAttrValues[i].rowNum) > Number(nSelectedRule)) {
					aModelToAttrValues[i].rowNum = (Number(aModelToAttrValues[i].rowNum) -1).toString();
				}
			}
		}
		oBusinessRulesDetailsModel.setProperty("/rulesByStep", aRulesByStep);
		oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues", aModelToAttrValues);
		oBusinessRulesDetailsModel.setProperty("/selectedRule", undefined);
		oBusinessRulesDetailsModel.setProperty("/selectedRulesNumber", undefined);
	};

	BusinessRulesDetailsController.onAddStepRule = function(){
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let aRulesByStep = oBusinessRulesDetailsModel.getProperty("/rulesByStep");
		let aModelToAttrValues = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues");
		let aValuesFirstRule = aModelToAttrValues.filter(oValue => oValue.rowNum === "1");
		for(let i = 0; i < aValuesFirstRule.length; i++){
			let tmpValue = Object.assign({}, aValuesFirstRule[i]);
			tmpValue.colValue = "";
			tmpValue.colValuedescr = "";
			if (tmpValue.colName === "WF__STEP") {
				tmpValue.colValue = "**";
				tmpValue.description = "For every WF step";
			}

			if (tmpValue.colName === "MSGNO") {
				tmpValue.attrvaluestoplaceh = [];
			}

			tmpValue.validValue = true;
			tmpValue.operator = "EQ";
			tmpValue.rowNum = (aRulesByStep.length + 1).toString();
			tmpValue.attrvaltoconcatnav = [];
			tmpValue.tableDetails = [];
			aModelToAttrValues.push(tmpValue);
		}
		aRulesByStep = aModelToAttrValues.filter(oValue => oValue.colName === "WF__STEP");
		oBusinessRulesDetailsModel.setProperty("/rulesByStep", aRulesByStep);
		oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues", aModelToAttrValues);
	};

	BusinessRulesDetailsController.onChangeDataModel = function() {
		let oController = this;
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule");

		oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule", {
			...oSelectedBusinessRule,
			CRTypeId: "",
			EntityId: ""
		});

		// Retrieve list of CR Types to populate filter options
		oController.fetchChangeRequestsList(oController, oSelectedBusinessRule.DataModelId);
	};

	BusinessRulesDetailsController.onChangeCRType = function() {
		let oController = this;
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule");
		let bNewRule = oBusinessRulesDetailsModel.getProperty("/newRule");

		oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/EntityId", "");

		// Retrieve list of Entities to populate filter options
		oController.fetchEntitiesList(oController, oSelectedBusinessRule.DataModelId, oSelectedBusinessRule.CRTypeId);

		let promiseWorkflowStepsList = GetLists.getWorkflowStepList(this, oSelectedBusinessRule.CRTypeId, "RL");
		promiseWorkflowStepsList.then(function (arrWorkflowStepsList) {
			if (!arrWorkflowStepsList) {
				arrWorkflowStepsList = [];
			}
			arrWorkflowStepsList.forEach(oStep => {
				oStep.UsmdCreqStep = oStep.UsmdCreqStep.length > 1 ? oStep.UsmdCreqStep : "0" + oStep.UsmdCreqStep;
			});
			if (arrWorkflowStepsList.length === 0) {
				Utilities.showPopupAlert(
					oController.geti18nText("businessRulesDetails.header.wfstep.message"), 
					MessageBox.Icon.WARNING,
					oController.geti18nText("businessRulesDetails.header.wfstep.title"));
			}

			if(bNewRule){
				oBusinessRulesDetailsModel.setProperty("/CRStepsList", arrWorkflowStepsList.filter(oStep => Number(oStep.Uicounter) === 0));
			}
			oBusinessRulesDetailsModel.setProperty("/WorkflowStepsList", arrWorkflowStepsList);
			// Bug 12781 - Adding blank option for filter
			oBusinessRulesDetailsModel.setProperty("/WorkflowStepsListForFilter", [{UsmdCreqStep: "", Txtmi: ""}, ...arrWorkflowStepsList]);
		});

		oController._getBackgroundJobStatus(oSelectedBusinessRule.CRTypeId);
		
	};

	BusinessRulesDetailsController.onChangeEntity = function() {
		let oController = this;
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule");

		//Best place to add the functions to to load additional models used for Rule Type 4 and 5.
		//To-do - Add the functions to the segregated JS file for Multi value page
		oController._loadAttributesValuesLists()
		.then(()=>{
			oController._addAttributeSelectionModel();
			oController._loadKeyAttributesList();
	
			if (oSelectedBusinessRule.DataModelId && oSelectedBusinessRule.CRTypeId && 
				oSelectedBusinessRule.EntityId && oSelectedBusinessRule.RuleTypeId === "5" && 
				oBusinessRulesDetailsModel.getProperty("/newRule")) {
					oController.selectCrossEntity(oSelectedBusinessRule.EntityId);
			} else {
				oController._routeToFragment(oSelectedBusinessRule, oBusinessRulesDetailsModel.getProperty("/newRule"));
			}
		});
	};

	BusinessRulesDetailsController.selectCrossEntity = function(sSelectedEntity) {
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let sRuleTypeId = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleTypeId");
		let aEntitiesList = oBusinessRulesDetailsModel.getProperty("/EntitiesList");
		let sCardinality = aEntitiesList.find(oEntity => oEntity.EntityId === sSelectedEntity).Cardinality;
		if (sRuleTypeId === "5") {
			let promiseCrossEntity = new Promise(function (resolve) {
				sap.m.MessageBox.show(
					"This setting cannot be modifed after the configuration has been saved. You can choose to create cross entity derivation with Keys and Non Keys or only Non Keys", {
						icon: sap.m.MessageBox.Icon.WARNING,
						title: "Cross Entity Derivation",
						actions: sCardinality === "" ? ["No Cross Entity Derivation", "With Non Keys"] : ["No Cross Entity Derivation", "With Keys/Non Keys", "With Non Keys"],
						emphasizedAction: "No Cross Entity Derivation",
						onClose: function (sAction) {
							resolve(sAction);
						}
					}
				);
			});
			promiseCrossEntity.then(function(sAction){
				let sCrossEntity;
				let sCrossEntityDescription;
				if (sAction === "With Keys/Non Keys") {
					sCrossEntity = "X";
					sCrossEntityDescription = "Cross Entity Derive (Keys/Non Keys)";
				}else if(sAction === "With Non Keys"){
					sCrossEntity = "N";
					sCrossEntityDescription = "Cross Entity Derive (Non Keys)";
				}else{
					sCrossEntity = "";
					sCrossEntityDescription = "Cross Entity Derivation";
				}
				oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/RuleExpression/crossentity", sCrossEntity);
				oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/RuleExpression/crossentityDescription", sCrossEntityDescription);
			});
		}
	};

	BusinessRulesDetailsController.onTransportPackageDialogCreated = function(oEvent){
		let comp = oEvent.getParameter("component");
		// store the component handle 
		this._transportPackageSelectionDialog = comp;

		this.getUsername()
		.then(function (oSapUserInfo) {
			let sUsername = oSapUserInfo.Sapname;
			if (!sUsername) {
				Utilities.showPopupAlert("Please login to continue", MessageBox.Icon.ERROR, "Logged out");
			} else {
				comp.setUser(sUsername);
			}
		});
	};

	BusinessRulesDetailsController._getSelectedTransportPackage = function(bCustomizing, bWorkbench, bPackage) {
		let oModel = this.getView().getModel("BusinessRulesDetails");

		// get stored package and transports from the model 
		let oSelectedTransportPackage = oModel.getProperty("/selected");

		let promise = 
			this._transportPackageSelectionDialog.open(
				bCustomizing, oSelectedTransportPackage?.transports?.customizing, 
				bWorkbench, oSelectedTransportPackage?.transports?.workbench, 
				bPackage, oSelectedTransportPackage?.package);
		
		let promiseReturn = promise.then(function(oTransporPackageResponse){
			if(bCustomizing){
				oModel.setProperty("/selected/transports/customizing", oTransporPackageResponse.customizingTransport);
			}

			if(bWorkbench){
				oModel.setProperty("/selected/transports/workbench", oTransporPackageResponse.workbenchTransport);
			}

			if(bPackage){
				oModel.setProperty("/selected/package", oTransporPackageResponse.package);
			}

			return oTransporPackageResponse;
		});
		

		return promiseReturn;
	};

	BusinessRulesDetailsController.getBRFTransport = function(sRuleTypeName, sEntity, sCRType) {
		let oController = this;
		let oPromiseTransport = new Promise(function(resolve, reject) {
			GetLists.getTransportToSelect(oController, sRuleTypeName, sEntity, sCRType)
			.then(function(sTransportToSelect) {
				return GetLists.getTransportType(oController, sTransportToSelect);
			})
			.then(
				function(oTransport){
					let sTransportType;
					if (oTransport.Typetransport === "K") {
						sTransportType = "Workbench";
					} else {
						sTransportType = "Customizing";
					}
					Utilities.showPopupAlert(
						"Changes to the BRF Application will be saved in the " + sTransportType + " Transport " + oTransport.Transport + 
						". Do you wish to continue?",
						MessageBox.Icon.INFORMATION, "Transport Request", [MessageBox.Action.YES, MessageBox.Action.NO])
					.then(() => {
						// User selected YES
						resolve(oTransport.Transport);
					})
					.catch(() => {
						// User Selected NO
						reject();
					});
				},
				() => {
					Utilities.showPopupAlert(
						"Use Transaction USMD_RULE to generate FMDM Application in BRFPlus", 
						MessageBox.Icon.ERROR, "FMDM Application to create rules not generated");
					reject();
				}
			);
		});
		return oPromiseTransport;
	};

	BusinessRulesDetailsController.onBADISelectionDialogCreated = function (oEvent) {
		let comp = oEvent.getParameter("component");
		// store the component handle 
		this._BADISelectionDialog = comp;

		this._BADISelectionDialog.attachBADISelected(this.onBADISelected, this);
	};

	BusinessRulesDetailsController.onBADISelected = function (oBADISelected) {		
		let sSelectedBADI = oBADISelected.getParameters();

		// Store the BAdI to the model
		this.getView()
			.getModel("BusinessRulesDetails").setProperty("/BAdISelected", sSelectedBADI);

		this._BADISelectionDialogPromiseResolveFunction(sSelectedBADI);
	};

	BusinessRulesDetailsController.getSelectedBADI = function () {
		let oController = this;

		if (this._BADISelectionDialogPromiseRejectFunction) {
			this._BADISelectionDialogPromiseResolveFunction = undefined;
			this._BADISelectionDialogPromiseRejectFunction({});
			this._BADISelectionDialogPromiseRejectFunction = undefined;
		}

		this._BADISelectionDialogPromise = new Promise(function (resolve, reject) {
			let sBAdISelected = oController.getView()
				.getModel("BusinessRulesDetails")
				.getProperty("/BAdISelected");

			if (sBAdISelected) {
				resolve(sBAdISelected);
			}else{
				oController._BADISelectionDialogPromiseResolveFunction = resolve;
				oController._BADISelectionDialogPromiseRejectFunction = reject;
				oController._BADISelectionDialog.open();
			}

		});

		return this._BADISelectionDialogPromise;
	};

	BusinessRulesDetailsController._getSelectedBADIDialog = function (oController, oRuleInfo) {
		let BADIDialog = oController.byId("BADISelectDialog").getComponentInstance();
		BADIDialog.setRuleDetails({
			ruleType: oRuleInfo.RuleTypeId,
			usmdModel: oRuleInfo.DataModelId,
			usmdEntity: oRuleInfo.EntityId,
			usmdCrtype: oRuleInfo.CRTypeId,
			featureType: "1",
			// Bug 12745 - Added a condition to send an empty string instead of an undefined value
			crossEntity: oRuleInfo.RuleExpression.crossentity ? oRuleInfo.RuleExpression.crossentity : ""
		});
		return oController.getSelectedBADI();
		
	};

	BusinessRulesDetailsController.onClickLinkBusinessRules = function() {
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		oRouter.navTo("BusinessRules", {}, true);
		this.getView().getModel("BusinessRulesDetails").setData({});
	};

	BusinessRulesDetailsController.onClickLinkThisPage = function() {
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let oSelectedBusinessRule = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule");
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		// Bug 12764 - Added missing parameters for navigation
		oRouter.navTo("BusinessRulesDetails", {
			DataModelId: oSelectedBusinessRule.DataModelId,
			CRTypeId: oSelectedBusinessRule.CRTypeId,
			EntityId: oSelectedBusinessRule.EntityId,
			RuleType: oSelectedBusinessRule.RuleTypeId,
			BusinessRuleName: oSelectedBusinessRule.BusinessRuleName,
			CRStep: oSelectedBusinessRule.CRStep
		});
	};

	BusinessRulesDetailsController._getBackgroundJobStatus = async function(sCRType) {
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let oStatus = await GetLists.getBackgroundJobStatus(this, undefined, "1", undefined, sCRType);
		oBusinessRulesDetailsModel.setProperty("/lockedActions", oStatus.Restype);
		let bNewRule = oBusinessRulesDetailsModel.getProperty("/newRule");
		if ((oStatus.Restype.includes("S") && bNewRule) || (oStatus.Restype.includes("E") && !bNewRule)) {
			let sMessage = oStatus.JOB2MESSAGENAV.results[0].Message + " " + oStatus.JOB2MESSAGENAV.results[0].Msgv1;
			Utilities.showPopupAlert(sMessage, MessageBox.Icon.INFORMATION, "Locked Actions");
		}
	};

	/* Start of SelectAttribute fragment methods */
	BusinessRulesDetailsController.onEntitySelectionChange = function (oEvent) {
		let sSelectedEntity = oEvent.getSource().getSelectedKey();
		// Get model 
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let oAttributeSelectionModel = this.getView().getModel("attributeSelectionModel");
		let sDataModelId = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/DataModelId");
		
		let promiseAttributeList = 
			GetLists.getEntityAttributeList(this, sDataModelId, sSelectedEntity, undefined);
		promiseAttributeList.then(function(arrAttributes){
			let sAttributeType = oAttributeSelectionModel.getProperty("/attributeType");
			let isCrossEntity = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/crossentity");
			oAttributeSelectionModel.setProperty("/attributeList", arrAttributes);
			if (sAttributeType === "Deriving" && isCrossEntity) {
				let arrFilteredAttributes = arrAttributes.filter(oAttribute => oAttribute.IsKey !== "X");
				oAttributeSelectionModel.setProperty("/attributeListFiltered", arrFilteredAttributes);
			} else {
				if(oAttributeSelectionModel.getProperty("/selectedType") === 0) {
					oAttributeSelectionModel.setProperty("/attributeListFiltered", arrAttributes);
				} else {
					let arrFilteredAttributes = arrAttributes.filter(oAttribute => {
						return oAttribute.Kind === "C";
					});
					oAttributeSelectionModel.setProperty("/attributeListFiltered", arrFilteredAttributes);
				}
			}
		});
	};

	BusinessRulesDetailsController.onAddAttributesConfirm = async function (sValidationNeeded) {
		let oBusinessRulesDetailsModel = this.getView().getModel("BusinessRulesDetails");
		let arrModelToAttrvalues = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues");
		let arrModelToDeriving = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltoderiving");
		let arrModelToDriving = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltodriving");
		let arrModelToValidation = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/modeltovalidation");
		let sRuleTypeId = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleTypeId");
		let arrRulesByStep = oBusinessRulesDetailsModel.getProperty("/rulesByStep");
		let oAttributeSelectionModel = this.getView().getModel("attributeSelectionModel");
		let arrAttributesList = oAttributeSelectionModel.getProperty("/attributeList");
		let arrSelectedAttributes = oAttributeSelectionModel.getProperty("/selectedAttributes");
		let sAttributeType = oAttributeSelectionModel.getProperty("/attributeType");
		let nSelectedType = oAttributeSelectionModel.getProperty("/selectedType");
		let sSelectedEntity = oAttributeSelectionModel.getProperty("/selectedEntity");
		let isCrossEntity = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/RuleExpression/crossentity");
		let	aKeyAttributesList = oBusinessRulesDetailsModel.getProperty("/keyAtrributes/keyAttributesList");
		let attributePromise;
		
		if (sAttributeType === "Driving") {
			//Bug 13084 - Changed condition to validate length attributes
			let arrAttributesToCompare = arrSelectedAttributes.map(sAttribute => sSelectedEntity + "__" + sAttribute + (nSelectedType === 0 ? "" : "__LENGTH"));
			for(let i = 0; i < arrModelToDriving.length; i++){
				if (arrAttributesToCompare.includes(arrModelToDriving[i].usmdEntity + "__" + arrModelToDriving[i].usmdAttribute)) {
					Utilities.showPopupAlert("Selected attribute is already added, please select other one.", MessageBox.Icon.ERROR,
						"Attribute can not be added");
					return;
				}
			}

			if (sRuleTypeId === "4") {
				if (arrModelToValidation.length === 0) {
					let aMessages = ["MSGTY", "MSGID", "MSGNO"];
					for(let num = 0; num < aMessages.length; num++){
						let oNewValue = {
							attrdatatype: "",
							colName: aMessages[num],
							colNum: num + 2,
							colValue: "",
							decimals: "",
							delimiter: "",
							kind: "",
							lenghttype: "",
							operator: "EQ",
							rowNum: "1",
							type: "",
							usmdModel: "",
							validValue: true
							, attrvaluestoplaceh: []
						};

						arrModelToAttrvalues.push(oNewValue);

						let oNewValidation = {
							attrdatatype: "",
							colNum: num + 2,
							decimals: "",
							derivingid: "",
							kind: "",
							lenghttype: "",
							usmdAttribute: "",
							usmdEntity: aMessages[num],
							usmdModel: ""
							, attrvaluestoplaceh: oNewValue.attrvaluestoplaceh
						};

						arrModelToValidation.push(oNewValidation);
					}

					oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/RuleExpression/modeltoattrvalues", arrModelToAttrvalues);
					oBusinessRulesDetailsModel.setProperty("/selectedBusinessRule/RuleExpression/modeltovalidation", arrModelToValidation);
				}
			}
			
			for(let i = 0; i < arrSelectedAttributes.length; i++){
				let oSelectedAttribute = arrAttributesList.find(oAttribute => oAttribute.UsmdAttribute === arrSelectedAttributes[i]);
				let tmpColName = nSelectedType === 0 ? oSelectedAttribute.UsmdEntity + "__" + oSelectedAttribute.UsmdAttribute 
													 : oSelectedAttribute.UsmdEntity + "__" + oSelectedAttribute.UsmdAttribute + "__LENGTH";
				for(let ruleNumber = 1; ruleNumber <= arrRulesByStep.length; ruleNumber++){
					let oNewValue = {
						attrdatatype: oSelectedAttribute.DATATYPE,
						attrvaltoconcatnav: [],
						colName: tmpColName,
						colNum: (arrModelToDriving.length).toString(),
						colValue: "",
						decimals: oSelectedAttribute.Decimals,
						delimiter: "",
						kind: oSelectedAttribute.Kind,
						lenghttype: oSelectedAttribute.Length,
						operator: "EQ",
						rowNum: ruleNumber.toString(),
						type: "",
						usmdModel: "",
						validValue: true
					};
					
					arrModelToAttrvalues.push(oNewValue);
				}

				arrModelToAttrvalues.forEach(oValue => {
					if (Number(oValue.colNum) > arrModelToDriving.length || oValue.colName === "WF__STEP") {
						oValue.colNum = (Number(oValue.colNum) + 1).toString();
					}
				});
	
				let oNewAttribute = {
					attrdatatype: oSelectedAttribute.DATATYPE,
					colNum: (arrModelToDriving.length).toString(),
					decimals: oSelectedAttribute.Decimals,
					derivingid: "",
					kind: oSelectedAttribute.Kind,
					lenghttype: oSelectedAttribute.Length,
					usmdAttribute: nSelectedType === 0 ? oSelectedAttribute.UsmdAttribute : oSelectedAttribute.UsmdAttribute + "__LENGTH",
					usmdEntity: oSelectedAttribute.UsmdEntity,
					usmdModel: ""
				};
	
				arrModelToDriving.push(oNewAttribute);
				
				arrModelToDriving.forEach(oValue => {
					if (oValue.usmdEntity === "WF" && oValue.usmdAttribute === "STEP") {
						oValue.colNum = (Number(oValue.colNum) + 1).toString();
					}
				});

				if (arrModelToDeriving) {
					arrModelToDeriving.forEach(oValue => {
						oValue.colNum = (Number(oValue.colNum) + 1).toString();
					});
				}

				if (arrModelToValidation) {
					arrModelToValidation.forEach(oValue => {
						oValue.colNum = (Number(oValue.colNum) + 1).toString();
					});
				}

				attributePromise = this._loadAttributesValuesLists(oSelectedAttribute.UsmdEntity, oSelectedAttribute.UsmdAttribute);
			}

			// Bug 12747 - Added condition to add key Attributes
			//Check if key Attributes are needed
			if (isCrossEntity === "X" && arrModelToDeriving.length === 0) {
				let sDataModelId = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/DataModelId");
				let sEntity = oBusinessRulesDetailsModel.getProperty("/selectedBusinessRule/EntityId");
				let arrAttributes = await GetLists.getEntityAttributeList(this, sDataModelId, sEntity, undefined);
				oAttributeSelectionModel.setProperty("/attributeList", arrAttributes);
				oAttributeSelectionModel.setProperty("/attributeType", "Deriving");
				oAttributeSelectionModel.setProperty("/selectedAttributes", aKeyAttributesList);
				oAttributeSelectionModel.setProperty("/selectedType", 0);
				oAttributeSelectionModel.setProperty("/selectedEntity", sEntity);
				this.onAddAttributesConfirm("NoNeeded");
			}
		}else{
			for(let i = 0; i < arrModelToDeriving.length; i++){
				let arrAttributesToCompare = arrSelectedAttributes.map(sAttribute => sSelectedEntity + "__" + sAttribute);
				if (arrAttributesToCompare.includes(arrModelToDeriving[i].usmdEntity + "__" + arrModelToDeriving[i].usmdAttribute)) {
					Utilities.showPopupAlert("Selected attribute is already added, please select other one.", MessageBox.Icon.ERROR,
						"Attribute can not be added");
					return;
				}
			}

			//Check if key Attributes are needed
			if (isCrossEntity === "X" && arrModelToDeriving.length === 0 && sValidationNeeded !== "NoNeeded") {
				arrSelectedAttributes = aKeyAttributesList.concat(arrSelectedAttributes);
			}


			for(let i = 0; i < arrSelectedAttributes.length; i++){
				let oSelectedAttribute = arrAttributesList.find(oAttribute => oAttribute.UsmdAttribute === arrSelectedAttributes[i]);
				for(let ruleNumber = 1; ruleNumber <= arrRulesByStep.length; ruleNumber++){
					let oNewValue = {
						attrdatatype: oSelectedAttribute.DATATYPE,
						attrSelectionType: 0,
						attrvaltoconcatnav: [],
						colName: oSelectedAttribute.UsmdEntity + "__" + oSelectedAttribute.UsmdAttribute,
						colNum: (arrModelToDriving.length + arrModelToDeriving.length + 1).toString(),
						colValue: "",
						decimals: oSelectedAttribute.Decimals,
						delimiter: "",
						kind: oSelectedAttribute.Kind,
						lenghttype: oSelectedAttribute.Length,
						operator: "EQ",
						rowNum: ruleNumber.toString(),
						type: "",
						usmdModel: "",
						validValue: true
					};
					
					arrModelToAttrvalues.push(oNewValue);
				}
	
				let oNewAttribute = {
					attrdatatype: oSelectedAttribute.DATATYPE,
					colNum: (arrModelToDriving.length + arrModelToDeriving.length + 1).toString(),
					decimals: oSelectedAttribute.Decimals,
					derivingid: "",
					kind: oSelectedAttribute.Kind,
					lenghttype: oSelectedAttribute.Length,
					usmdAttribute: oSelectedAttribute.UsmdAttribute,
					usmdEntity: oSelectedAttribute.UsmdEntity,
					usmdModel: ""
				};
				
				arrModelToDeriving.push(oNewAttribute);
				attributePromise = this._loadAttributesValuesLists(oSelectedAttribute.UsmdEntity, oSelectedAttribute.UsmdAttribute);
			}
		}
		oAttributeSelectionModel.setProperty("/attributeListFiltered", []);
		oAttributeSelectionModel.setProperty("/selectedEntity", "");
		oAttributeSelectionModel.setProperty("/selectedAttributes", []);
		attributePromise.then(()=>{
			this._loadAttributesModels();
			
			this._oDerivationValuesFrgmt.close();
		});
	};

	BusinessRulesDetailsController.onCancelAddDerivingAttribute = function () {
		let oAttributeSelectionModel = this.getView().getModel("attributeSelectionModel");
		oAttributeSelectionModel.setProperty("/attributeListFiltered", []);
		oAttributeSelectionModel.setProperty("/selectedEntity", "");
		oAttributeSelectionModel.setProperty("/selectedAttributes", []);
		this._oDerivationValuesFrgmt.close();
	};

	/**
	 * Fetch the Data model list at the load of page
	 */
	BusinessRulesDetailsController.fetchDataModelsList = function(oController) {
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let promiseDataModelsList = GetLists.getDataModelsList(oController);
		promiseDataModelsList.then(function (arrDataModels) {
			if (!arrDataModels.results) {
				arrDataModels.results = [];
			}
			oBusinessRulesDetailsModel.setProperty("/DataModelsList", arrDataModels.results.map(oResult => {
				return {
					DataModelId: oResult.Model,
					DataModelDescription: oResult.Desc
				};
			}));
		});
		return promiseDataModelsList;
	};

	/**
	 * 
	 * @param {object} oController 
	 * @param {string} sDataModel Data Model for which the change request list must be fetched 
	 */
	BusinessRulesDetailsController.fetchChangeRequestsList = function(oController, sDataModel) {
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		// Retrieve list of CR Types to populate filter options
		let promiseCRTypesList = GetLists.getChangeRequestsList(oController, sDataModel);
		promiseCRTypesList.then(function (arrCRTypesList) {
			if (!arrCRTypesList.results) {
				arrCRTypesList.results = [];
			}
			oBusinessRulesDetailsModel.setProperty("/CRTypesList", arrCRTypesList.results.map(oResult => {
				return {
					CRTypeId: oResult.UsmdCreqType,
					CRTypeDescription: oResult.Txtmi
				};
			}));
		});
		return promiseCRTypesList;
	};

	/**
	 * 
	 * @param {object} oController 
	 * @param {string} sDataModel Data Model for which the entity list list must be fetched 
	 * @param {string} sCRType CR Type for which the entity list must be fetched
	 */
	BusinessRulesDetailsController.fetchEntitiesList = function(oController, sDataModel, sCRType) {
		let oBusinessRulesDetailsModel = oController.getView().getModel("BusinessRulesDetails");
		let promiseEntitiesList = GetLists.getModelEntityList(oController, sDataModel, "UsmdModel", sCRType);
		promiseEntitiesList.then(function (arrEntitiesList) {
			if (!arrEntitiesList) {
				arrEntitiesList = [];
			}
			oBusinessRulesDetailsModel.setProperty("/EntitiesList", arrEntitiesList.map(oResult => {
				return {
					EntityId: oResult.UsmdEntity,
					EntityDescription: oResult.Txtlg,
					Cardinality: oResult.Cardinality
				};
			}));
		});
		return promiseEntitiesList;
	};

	/* End of SelectAttribute fragment methods */

	return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.BusinessRules.BusinessRulesDetails", BusinessRulesDetailsController);

});