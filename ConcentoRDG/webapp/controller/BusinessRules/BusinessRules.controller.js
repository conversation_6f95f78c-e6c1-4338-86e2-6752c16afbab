sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"sap/m/MessageBox",
	"./MandatoryRule",
	"./SingleValueRule",
	"./MultiValueRule",
	"sap/ui/model/json/JSONModel",
	"dmr/mdg/supernova/SupernovaFJ/model/ModelMessages",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService"
], function (BaseController, Utilities, GetLists, MessageBox, MandatoryRuleFragment, SingleValueRuleFragment, MultiValueRuleFragment, JSONModel, ModelMessages, DMRDataService) {
	"use strict";

	let BusinessRulesController = {};
	
	BusinessRulesController.onInit = function () {
		this.ModelMessages = ModelMessages;
		this.MandatoryRuleFragment =  MandatoryRuleFragment;
		this.SingleValueRuleFragment = SingleValueRuleFragment;
		this.MultiValueRuleFragment = MultiValueRuleFragment;

		// Set the model for the view 
		this._initializeDataModel(0);
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		oRouter.getRoute("BusinessRules").attachMatched(this.onRouteMatched, this);
	};

	/**
	 * 
	 * @param {int} iRulesBy Provide the rules by that the data needs to be initialized to
	 * If nothing is provided the rules by is defaulted to 0 (By Entity)
	 */
	BusinessRulesController._initializeDataModel = function (iRulesBy = 0) {
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");
		if (!oBusinessRulesModel) {
			oBusinessRulesModel = new JSONModel();
			this.getView().setModel(oBusinessRulesModel, "BusinessRules");
		}

		// Set the basic defaults 
		oBusinessRulesModel.setData({
			RulesBy: iRulesBy,
			importAllowed: false
		});

		// Retrieve the rule type list .. always static
		this.getRuleTypesList(this);
	};

	BusinessRulesController.onRouteMatched = function() {

		// Bug 12773 - No need to reset the model if navigating from Business Rules page
		let sRouteDirection = sap.ui.core.routing.History.getInstance().getDirection();

		/** If the navigation is NewEntry or Forwards, 
		 * 1. Initialize the data model for the page
		 * 2. Retrieve the Data Model List 
		 * 3. Retrieve the Change request list 
		 */
		if(sRouteDirection === sap.ui.core.routing.HistoryDirection.NewEntry || 
			sRouteDirection === sap.ui.core.routing.HistoryDirection.Forwards){
			this._initializeDataModel(0);
			this.getDataModelList(this);
			this.getChangeRequestList(this);
		} else {
			/**
			 * 1. Update the entity list (Which is likely to have changed)
			 * 2. Update the rules list based on current selections
			 */
			this.getEntityList(this);
			this.onSearch(undefined, true);
		}

		this._getBackgroundJobStatus();
	};

	BusinessRulesController.searchResultsListView = function() {
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");
		oBusinessRulesModel.setProperty("/SearchResults", []);
	};

	BusinessRulesController.getDataModelList = function(oController){
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");

		// Retrieve list of Data Models to populate filter options
		let promiseDataModelsList = GetLists.getDataModelsList(oController);
		promiseDataModelsList.then(function (arrDataModels) {
			if (!arrDataModels.results) {
				arrDataModels.results = [];
			}
			arrDataModels.results.splice(0, 0, { 
				DataModelId: "", 
				DataModelDescription: ""
			});
			oBusinessRulesModel.setProperty("/DataModelsList", arrDataModels.results.map(oResult => {
				return {
					DataModelId: oResult.Model,
					DataModelDescription: oResult.Desc
				};
			}));
		});

		return promiseDataModelsList;
	};

	BusinessRulesController.getChangeRequestList = function(oController) {
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");
		let sDataModelFilter = oBusinessRulesModel.getProperty("/DataModelFilter");

		// Retrieve list of CR Types to populate filter options
		let promiseCRTypesList = GetLists.getChangeRequestsList(oController, sDataModelFilter);
		promiseCRTypesList.then(function (arrCRTypesList) {
			if (!arrCRTypesList.results) {
				arrCRTypesList.results = [];
			}
			arrCRTypesList.results.splice(0, 0, { 
				CRTypeId: "", 
				CRTypeDescription: ""
			});
			oBusinessRulesModel.setProperty("/CRTypesList", arrCRTypesList.results.map(oResult => {
				return {
					CRTypeId: oResult.UsmdCreqType,
					CRTypeDescription: oResult.Txtmi
				};
			}));
		});

		return promiseCRTypesList;
	};

	BusinessRulesController.getEntityList = function(oController){
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");
		let sDataModelFilter = oBusinessRulesModel.getProperty("/DataModelFilter");
		let sCRTypeFilter = oBusinessRulesModel.getProperty("/CRTypeFilter");

		// If either data model or cr type is not selected, clear the list.
		if(!sDataModelFilter && !sCRTypeFilter){
			oBusinessRulesModel.setProperty("/EntitiesList", [{ 
				EntityId: "", 
				EntityDescription: ""
			}]);

			return;
		}

		// Retrieve list of Entities to populate filter options
		GetLists.getModelEntityList(oController, sDataModelFilter, "UsmdModel", sCRTypeFilter)
		.then(function (arrEntitiesList) {
			if (!arrEntitiesList) {
				arrEntitiesList = [];
			}
			arrEntitiesList.splice(0, 0, { 
				EntityId: "", 
				EntityDescription: ""
			});
			oBusinessRulesModel.setProperty("/EntitiesList", arrEntitiesList.map(oResult => {
				return {
					EntityId: oResult.UsmdEntity,
					EntityDescription: oResult.Txtlg
				};
			}));
		});
	};

	BusinessRulesController.getRuleTypesList = function(oController) {
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");

		// Retrieve list of Rule Types to populate filter options
		let promiseRuleTypesList = GetLists.getRuleTypesList(oController);
		promiseRuleTypesList.then(function (arrRuleTypesList) {
			if (!arrRuleTypesList) {
				arrRuleTypesList = [];
			}
			arrRuleTypesList.splice(0, 0, { 
				RuleTypeId: "", 
				RuleTypeDescription: ""
			});
			oBusinessRulesModel.setProperty("/RuleTypesList", arrRuleTypesList.map(oResult => {
				return {
					RuleTypeId: oResult.RuleTypeId,
					RuleTypeDescription: oResult.RuleTypeDescription
				};
			}));
		});

		return promiseRuleTypesList;
	};
	
	BusinessRulesController.getCRStepTypeList = function(oController) {
		let oBusinessRulesModel = oController.getView().getModel("BusinessRules");
		let sCRTypeFilter = oBusinessRulesModel.getProperty("/CRTypeFilter");

		let sCountType = "RL";
		let promiseWFStepList = GetLists.getWorkflowStepList(oController, sCRTypeFilter, sCountType);
		promiseWFStepList.then((aWFStepList) => {
			if (!aWFStepList) {
				aWFStepList = [];
			}
			aWFStepList = aWFStepList.filter(oWFStep => Number(oWFStep.Uicounter) > 0);
			aWFStepList.splice(0, 0, { 
				StepTypeId: "", 
				StepTypeDescription: ""
			});
			oBusinessRulesModel.setProperty("/StepTypesList", aWFStepList.map(oResult => {
				return {
					StepTypeId: oResult.UsmdCreqStep,
					StepTypeDescription: oResult.Txtmi
				};
			}));
		});

		return promiseWFStepList;
	};

	BusinessRulesController.onClickLinkHome = function() {
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		oRouter.navTo("TargetHome", {}, true);
	};

	BusinessRulesController.onClickLinkThisPage = function() {
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		oRouter.navTo("BusinessRules", {}, true);
	};

	BusinessRulesController.onChangeDataModelFilter = function() {

		// Get the selected Data Model and retrieve the CR Type List. 
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");

		// Reset the CR Type, Entity and CR Step Type properties
		oBusinessRulesModel.setProperty("/CRTypeFilter", "");
		oBusinessRulesModel.setProperty("/CRTypesList", "");
		
		oBusinessRulesModel.setProperty("/EntityFilter", "");
		oBusinessRulesModel.setProperty("/EntitiesList", "");
		
		oBusinessRulesModel.setProperty("/StepTypeFilter", "");
		oBusinessRulesModel.setProperty("/StepTypesList", "");

		// Retrieve the CR Type List
		this.getChangeRequestList(this);

		// Retrieve the Entity List 
		this.getEntityList(this);
	};

	BusinessRulesController.onChangeCRTypeFilter = function() {
		
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");
		let sRulesBy = oBusinessRulesModel.getProperty("/RulesBy");

		oBusinessRulesModel.setProperty("/EntityFilter", "");
		oBusinessRulesModel.setProperty("/EntitiesList", "");
		
		oBusinessRulesModel.setProperty("/StepTypeFilter", "");
		oBusinessRulesModel.setProperty("/StepTypesList", "");

		// Get the entity list or CR StepType list. 
		if(sRulesBy === 0) {
			this.getEntityList(this);
		} else {
			this.getCRStepTypeList(this);
		}
	};

	BusinessRulesController.onSelectRulesBy = function (oEvent){
		let iSelectedRulesBy = oEvent.getSource().getSelectedIndex();
		// Initialize the page and pull the DB and CrType lists
		this._initializeDataModel(iSelectedRulesBy);
		this.getDataModelList(this);
		this.getChangeRequestList(this);
	};

	BusinessRulesController.onSearch = function (oEvent, bIgnoreErrorMessages) {
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");
		
		let sDataModelFilter = oBusinessRulesModel.getProperty("/DataModelFilter");
		let sEntityFilter = oBusinessRulesModel.getProperty("/EntityFilter");
		let sCRTypeFilter = oBusinessRulesModel.getProperty("/CRTypeFilter");
		let sRuleTypeFilter = oBusinessRulesModel.getProperty("/RuleTypeFilter");
		let sAIGenerated = oBusinessRulesModel.getProperty("/IsAIGenerated");
		let sStepType = oBusinessRulesModel.getProperty("/StepTypeFilter");
		let iRulesBy = oBusinessRulesModel.getProperty("/RulesBy");

		if (iRulesBy === 0 && !sDataModelFilter && !sCRTypeFilter) {
			oBusinessRulesModel.setProperty("/SearchResults", []);
			if(!bIgnoreErrorMessages) {
				Utilities.showPopupAlert(
					this.geti18nText("businessRules.listPage.validation.selectDMorCrType.message"),
					MessageBox.Icon.ERROR, 
					this.geti18nText("businessRules.listPage.validation.error.title")
				);
			}
			return;
		}

		if (iRulesBy === 1 && !sCRTypeFilter) {
			oBusinessRulesModel.setProperty("/SearchResults", []);
			if(!bIgnoreErrorMessages) {
				Utilities.showPopupAlert(
					this.geti18nText("businessRules.listPage.validation.selectCrType.message"),
					MessageBox.Icon.ERROR, 
					this.geti18nText("businessRules.listPage.validation.error.title")
				);
			}
			return;
		}

		if (iRulesBy === 0) {
			let promiseBusinessRulesList = GetLists.getBusinessRuleList(this, sDataModelFilter, sEntityFilter, sCRTypeFilter, sRuleTypeFilter, sAIGenerated);
			promiseBusinessRulesList.then(function (arrBusinessRulesList) {
				if (!arrBusinessRulesList) {
					arrBusinessRulesList = [];
				}
				oBusinessRulesModel.setProperty("/SearchResults", arrBusinessRulesList.map(oResult => {
					let bActive;
					let oRuleExpr = oResult.RuleExpr ? JSON.parse(oResult.RuleExpr) : "";
					if(oRuleExpr){
						if(oResult.RuleType === "1"){
							bActive = oRuleExpr.modeltobrfnav[0].brftoattrnav[0].mandt === "YES" ? true : false;
						} else if (oResult.RuleType === "2" || oResult.RuleType === "3"){
							bActive = oRuleExpr.modeltoentity[0].entitytoattr[0].isNeeded === "YES" ? true : false;
						}
					}
					return {
						DataModelId: oResult.ModelName,
						DataModelDescription: oResult.ModelDesc,
						CRTypeId: oResult.CRTypeId,
						CRTypeDescription: oResult.CRTypeDescription,
						EntityId: oResult.EntityName,
						EntityDescription: oResult.EntityDesc,
						RuleTypeId: oResult.RuleType,
						RuleTypeDescription: oResult.RuleDesc,
						BusinessRuleId: oResult.RuleId,
						BusinessRuleName: oResult.RuleName,
						UserRuleName: oResult.UserRuleName,
						RuleExpr: oRuleExpr,
						IsActive: bActive,
						AiGenerated: oResult.AiGenerated
					};
				}));
			});
		}else{
			let sCountType = "RL";
			if(sStepType){
				// pad the step type with 0 to make it 2 digits
				sStepType = sStepType.padStart(2, "0");
			}
			let oPromiseWFStepList = GetLists.getWorkflowStepList(this, sCRTypeFilter, sCountType, sStepType);
			oPromiseWFStepList.then((aWFStepList) => {
				if (!aWFStepList) {
					aWFStepList = [];
				}

				// TO DO: VERIFY WHY DATA MODEL NAME AND DESC ARE NOT BEING RETURNED.
				aWFStepList = aWFStepList.filter(oWFStep => Number(oWFStep.Uicounter) > 0);
				oBusinessRulesModel.setProperty("/SearchResults", aWFStepList.map(oResult => {
					return {
						DataModelId: oResult.UsmdModel,
						DataModelDescription: oResult.ModelDesc,
						CRTypeId: oResult.UsmdCreqType,
						CRTypeDescription: oResult.CreqTypeDesc,
						CRStep: oResult.UsmdCreqStep.padStart(2, "0"), 
						CRStepDescription: oResult.Txtmi,
						RuleTypeId: "6"
					};
				}));
			});
		}
		
	};

	// Called when the Import Rules list component is loaded in the import fragment.
	BusinessRulesController.onImportRulesListCreated = function(oEvent) {
		let oController = this;

		let comp = oEvent.getParameter("component");
		oController.ImportRulesList = comp;

		// update the mapping to the component 
		oController.ImportRulesList.setDataMapping({
			"title": "UserRuleName", //Task 12487 - Add User Rule Name to Rules/Properties
			"description": undefined,
			"info": "RuleDesc"
		});
		oController.ImportRulesList.setMode("MultiSelect");
		oController.ImportRulesList.setRememberSelections(true);

		oController.ImportRulesList.setHeaderText(oController.geti18nText("businessRules.title.small"));
		oController.ImportRulesList.setListGroupingPath("EntityName");
	};

	// When the rule types are selected on the import rule popup fragment
	BusinessRulesController.onSelectImportRuleType = function () {
		this.ImportRulesList._searchList.removeSelections(true);
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");
		let aSelectedRuleTypes = oBusinessRulesModel.getProperty("/importRules/selectedRuleTypes");

		let aRulesList = this.ImportRulesList.getListData();
		// Iterate the list and select the rules by type selected.
		for(let i = 0; i < aRulesList.length; i++){
			if (aSelectedRuleTypes.includes(aRulesList[i].RuleDesc)) {
				this.ImportRulesList.setSelectedItemByTitle(aRulesList[i].UserRuleName);
			}
		}
	};

	/**
		 * On press Import Rules
		 * Open the ImportRules Fragment
		 * Fetch all Change Requests filtered by Data Model. Filter out the selected Change Request type from the list
		 */
	BusinessRulesController.onClickImportRules = function() {
		let oController = this;
		let oView = oController.getView();
		let oBusinessRulesModel = oView.getModel("BusinessRules");
		oBusinessRulesModel.setProperty("/importRules", {});
		if (!oController.ImportRulesDialog) {
			oController.ImportRulesDialog =
				sap.ui.xmlfragment(
					"ImportRules",
					"dmr.mdg.supernova.SupernovaFJ.view.BusinessRules.ImportRules",
					oController);
			oView.addDependent(oController.ImportRulesDialog);
		}
		// Open the dialog
		oController.ImportRulesDialog.open();
	};

	BusinessRulesController.onChangeDataModelImport = function (oEvent) {
		let oController = this;
		let sDataModel = oEvent.getSource().getSelectedKey();
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");
		let iRulesBy = oBusinessRulesModel.getProperty("/RulesBy");
		// Clear all the properties and set the Data Model Selection
		oBusinessRulesModel.setProperty("/importRules", {selectedDataModel: sDataModel});

		let promiseCRList = GetLists.getChangeRequestsList(this, sDataModel);
		promiseCRList.then(function(arrCrList){
			let arrFilteredCRList = arrCrList.results;
			
			//Added the method setSizeLimit
			oBusinessRulesModel.setSizeLimit(arrFilteredCRList.length);
			// Update the From CR Type List
			oBusinessRulesModel.setProperty("/importRules/fromChangeRequestList", arrFilteredCRList);
			/**
			 * If the rule type is ByEntity (0)
			 * 	1. Clear the Rule list 
			 * Else If By Step Type (1)
			 * 	1. Update the To CR Type List 
			 */
			if(iRulesBy === 0){
				oController.ImportRulesList.setListData();
			} else { //(iRulesBy === 1)
				oBusinessRulesModel.setProperty("/importRules/toChangeRequestList", arrFilteredCRList);
			}
		});
	};

	BusinessRulesController.onSelectImportFromCRType = function (oEvent) {
		let sCRType = oEvent.getSource().getSelectedKey();
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");
		let iRulesBy = oBusinessRulesModel.getProperty("/RulesBy");

		/**
		 * If Rule By Type = By Entity
		 * 1. Clear the To CR Type selection
		 * 2. Clear Rules List Data
		 * 3. Clear Rule Type Selection Multi ComboBox 
		 * 4. Set the From  CR Type list not include the select To CR Type List
		 * Retrieve and set the To Change Request
		 */
		if (iRulesBy === 0) {
			oBusinessRulesModel.setProperty("/importRules/selectedToCRType", undefined);
			oBusinessRulesModel.setProperty("/importRules/selectedRuleTypes", undefined);
			this.ImportRulesList.setListData(); // Clear the rules list 
			// Get the filtered list of CR Types and set to To CR Type List
			let aFromCRList = oBusinessRulesModel.getProperty("/importRules/fromChangeRequestList");
			let aToCRList = aFromCRList.filter(oCRType => oCRType.UsmdCreqType !== sCRType);
			oBusinessRulesModel.setProperty("/importRules/toChangeRequestList", aToCRList);
			return;
		}
		
		/**
		 * If Rule By Type = By Step Type
		 * 1. Clear the From CR Step Type selection
		 * 2. Clear the From CR Step Type List
		 * 3. Set the From CR Type list 
		 */
		if(iRulesBy === 1) {
			oBusinessRulesModel.setProperty("/importRules/selectedFromCRStep", undefined);
			oBusinessRulesModel.setProperty("/importRules/fromCRStepsList", []);
			if(!sCRType) {
				// If no CR was selected, return
				return;
			}
			let oPromiseWFStepList = GetLists.getWorkflowStepList(this, sCRType);
			oPromiseWFStepList.then(function(aWFStepList){
				aWFStepList?.forEach(oStep => {
					oStep.UsmdCreqStep = oStep.UsmdCreqStep.padStart(2, "0");
				});
				oBusinessRulesModel.setProperty("/importRules/fromCRStepsList", aWFStepList);
			});
		}
	};

	BusinessRulesController.isImportSaveEnabled = function(iRulesBy, sFromCrType, sFromCrStepType, sToCrType, sToCrStepType, bImportEnabled) {
		let bEnableSave = false;
		if(iRulesBy === 0) { // By Entity
			bEnableSave = (
				iRulesBy === 0 &&
				sFromCrType !== undefined && sFromCrType.length > 0 &&
				sToCrType !== undefined && sToCrType.length > 0 &&
				bImportEnabled === true
			);
			return bEnableSave;
		}

		if( iRulesBy === 1) { // By Step Type
			bEnableSave = (
				iRulesBy === 1 &&
				sFromCrType !== undefined && sFromCrType.length > 0 &&
				sFromCrStepType !== undefined && sFromCrStepType.length > 0 &&
				sToCrType !== undefined && sToCrType.length > 0 &&
				sToCrStepType !== undefined && sToCrStepType.length > 0 &&
				bImportEnabled === true
			);
			return bEnableSave;			
		}
		return bEnableSave;
	};

	BusinessRulesController.createPayload = function(oController, sCustomizingTransport, oSelectedBusinessRule, sSwitchState = ""){
		let oData;

		if(oSelectedBusinessRule.RuleTypeId === "2" || oSelectedBusinessRule.RuleTypeId === "3") {
			if(sSwitchState){
				oData = {
					oDataToWrite: {
						"CustTransport": sCustomizingTransport,
						"UsmdCreqType": oSelectedBusinessRule.CRTypeId,
						"UsmdModel": oSelectedBusinessRule.DataModelId,
						"MODELTOENTITY": [				
							{
								"UsmdModel": oSelectedBusinessRule.DataModelId,
								"UsmdEntity": oSelectedBusinessRule.EntityId,
								"ENTITYTOATTR": [
									{
										"Delete": "",
										"RuleName": oSelectedBusinessRule.BusinessRuleName,
										"UsmdModel": oSelectedBusinessRule.DataModelId,
										"UsmdAttribute": oSelectedBusinessRule.RuleExpr.modeltoentity[0].entitytoattr[0].usmdAttribute,
										"ATTRTOCONDITION": [],
										"ATTRTOMSGDET": []
									}
								]
							}
						],
						"MODELTOMESSAGE": []
					},
					oDataModel : oController.getModel("BRFVALCHECK")
				};
			} else {

				oData = {
					oDataToWrite: {
						"CustTransport": sCustomizingTransport,
						"UsmdCreqType": oSelectedBusinessRule.CRTypeId,
						"UsmdModel": oSelectedBusinessRule.DataModelId,
						"MODELTOENTITY": [				
							{
								"UsmdModel": oSelectedBusinessRule.DataModelId,
								"UsmdEntity": oSelectedBusinessRule.EntityId,
								"ENTITYTOATTR": [
									{
										"Delete": "X",
										"RuleName": oSelectedBusinessRule.BusinessRuleName,
										"UsmdModel": oSelectedBusinessRule.DataModelId,
										"UsmdAttribute": oSelectedBusinessRule.RuleExpr.modeltoentity[0].entitytoattr[0].usmdAttribute,
										"ATTRTOCONDITION": [],
										"ATTRTOMSGDET": []
									}
								]
							}
						],
						"MODELTOMESSAGE": []
					},
					oDataModel : oController.getModel("BRFVALCHECK")
				};
			}


			if(sSwitchState){
				oData.oDataToWrite.MODELTOENTITY[0].ENTITYTOATTR[0].IsNeeded = sSwitchState;
				oData.oDataToWrite.MODELTOENTITY[0].ENTITYTOATTR[0].ATTRTOCONDITION = [...oSelectedBusinessRule.RuleExpr.modeltoentity[0].entitytoattr[0].attrtocondition];
				oData.oDataToWrite.MODELTOENTITY[0].ENTITYTOATTR[0].ATTRTOMSGDET = [...oSelectedBusinessRule.RuleExpr.modeltoentity[0].entitytoattr[0].attrtomsgdet];
			}
		} else {
			oData = {
				oDataToWrite: {
					"CustTransport": sCustomizingTransport,
					"UsmdModel": oSelectedBusinessRule.DataModelId,
					"UsmdCreqType": oSelectedBusinessRule.CRTypeId,
					"UsmdEntity": oSelectedBusinessRule.EntityId,
					"Delete": sSwitchState ? "" : "X",
					"AppName": oSelectedBusinessRule.BusinessRuleName,
					"MODELTOMESSAGE": []
				}
			};

			if (oSelectedBusinessRule.RuleTypeId === "4") {
				oData.oDataModel = oController.getModel("YGW_ZEUS_BRF_VALIDATION_MODEL");
			} else {
				oData.oDataModel = oController.getModel("YGW_ZEUS_BRF_DERIVE_MODEL");
			}
		}

		return oData;
	};

	BusinessRulesController.writeParameterObject = function(oController, oSelectedBusinessRule, oBusinessRulesModel, sAction){
		// The Write parameter object ... Callbacks and error management
		let oWriteParameterObject = {
			success: function (oDataResult) {
				let oResults;
				if(oSelectedBusinessRule.RuleTypeId === "1"){
					oResults = oDataResult.modeltomessageNav.results[0];
				} else {
					oResults = oDataResult.MODELTOMESSAGE.results[0];
				}
				// Check the message status and notify the result 
				if (oResults.MessageType === "S") {
					let sDataModelFilter = oBusinessRulesModel.getProperty("/DataModelFilter");
					let sEntityFilter = oBusinessRulesModel.getProperty("/EntityFilter");
					let sCRTypeFilter = oBusinessRulesModel.getProperty("/CRTypeFilter");
					let sRuleTypeFilter = oBusinessRulesModel.getProperty("/RuleTypeFilter");
					let sAIGenerated = oBusinessRulesModel.getProperty("/IsAIGenerated");

					let promiseBusinessRulesList = GetLists.getBusinessRuleList(oController, sDataModelFilter, sEntityFilter, sCRTypeFilter, sRuleTypeFilter, sAIGenerated);
					promiseBusinessRulesList.then(function (arrBusinessRulesList) {
						if (!arrBusinessRulesList) {
							arrBusinessRulesList = [];
						}
						oBusinessRulesModel.setProperty("/SearchResults", arrBusinessRulesList.map(oResult => {
							let bActive;
							let oRuleExpr = oResult.RuleExpr ? JSON.parse(oResult.RuleExpr) : "";
							if(oRuleExpr){
								if(oResult.RuleType === "1"){
									bActive = oRuleExpr.modeltobrfnav[0].brftoattrnav[0].mandt === "YES" ? true : false;
								} else if (oResult.RuleType === "2" || oResult.RuleType === "3"){
									bActive = oRuleExpr.modeltoentity[0].entitytoattr[0].isNeeded === "YES" ? true : false;
								}
							}
							return {
								DataModelId: oResult.ModelName,
								DataModelDescription: oResult.ModelDesc,
								CRTypeId: oResult.CRTypeId,
								CRTypeDescription: oResult.CRTypeDescription,
								EntityId: oResult.EntityName,
								EntityDescription: oResult.EntityDesc,
								RuleTypeId: oResult.RuleType,
								RuleTypeDescription: oResult.RuleDesc,
								BusinessRuleId: oResult.RuleId,
								BusinessRuleName: oResult.RuleName,
								UserRuleName: oResult.UserRuleName,
								RuleExpr: oRuleExpr,
								IsActive: bActive,
								AiGenerated: oResult.AiGenerated
							};
						}));
						oBusinessRulesModel.refresh();
						Utilities.showPopupAlert(oResults.Message, MessageBox.Icon.SUCCESS, sAction + " Business Rule");
					});
				} else {
					Utilities.showPopupAlert(oResults.Message, MessageBox.Icon.ERROR, sAction + " Business Rule");
				}
				
				sap.ui.core.BusyIndicator.hide();
			},
			error: function () {
				sap.ui.core.BusyIndicator.hide();
				let promiseShowPopupAlert =
				Utilities.showPopupAlert("Rule could not be " + sAction + "d.", MessageBox.Icon.ERROR, sAction + " Business Rule");
				promiseShowPopupAlert.then(function () {});
			}
		};
		return oWriteParameterObject;
	};

	BusinessRulesController.getAttributeFromString = function(sDataString, sCRType, sEntity, sRuleType) {
		// Construct the prefix and suffix to locate the attribute
		let sPrefix = `${sCRType}${sEntity}`;
		let sSuffix;
		
		if(sRuleType === "1"){
			sSuffix = "MNDT";
		} else if(sRuleType === "2"){
			sSuffix = "V_0";
		} else if(sRuleType === "3"){
			sSuffix = "D_0";
		} else if(sRuleType === "4"){
			sSuffix = "_V01";
		} else if(sRuleType === "5"){
			sSuffix = "_D01";
		}
	  
		// Find the substring after the prefix and before the suffix
		let sStartIndex = sDataString.indexOf(sPrefix) + sPrefix.length;
		let sEndIndex = sDataString.indexOf(sSuffix, sStartIndex);
	  
		// Extract and return the attribute name
		if (sStartIndex > -1 && sEndIndex > sStartIndex) {
		  return sDataString.slice(sStartIndex, sEndIndex);
		}
	  
		// Return null if the attribute cannot be found
		return null;
	};

	BusinessRulesController.onStatusChange = function(oEvent){
		let oController = this;
		let oBusinessRulesModel = oController.getView().getModel("BusinessRules");
		let oSwitchComponent = oEvent.getSource();
		let sPath = oSwitchComponent.getParent().getBindingContextPath();
		let oSelectedBusinessRule = oBusinessRulesModel.getProperty(sPath);
		let bActiveRuleState = oSwitchComponent.getState(); 
		let sSwitchState = bActiveRuleState ? "YES" : "NO";
		let oEntityToAttr, oAttributeToCondition, oValueCheckModel;
		switch(oSelectedBusinessRule.RuleTypeId){

			case "1":
				oController.MandatoryRuleFragment.createPayload(oController, oSelectedBusinessRule, oSelectedBusinessRule.RuleExpr.modeltobrfnav[0].brftoattrnav[0].usmdAttribute, sSwitchState)
				.then(function (oDataToWrite){
					oController.MandatoryRuleFragment.sendRequest(oController, true, oDataToWrite, oSwitchComponent, "MainSave");
				})
				.catch(()=>{
					oSwitchComponent.setState(!bActiveRuleState);
				});
				break;

			case "2":
				oEntityToAttr = oSelectedBusinessRule.RuleExpr.modeltoentity[0].entitytoattr[0];
				oAttributeToCondition = oController.SingleValueRuleFragment.createAttributeToCondition(oController, oEntityToAttr.attrtocondition, true);
				oValueCheckModel = {
					selectedAttributeName: oEntityToAttr.usmdAttribute,
					checkExpressionType: oEntityToAttr.exprType,
					expressionToSave: oEntityToAttr.expression,
					ruleEnabled: sSwitchState
				};
				let oAttrToMsgDet = oEntityToAttr.attrtomsgdet[0];
				let {arrMessageClassPlaceholderTable} = oController.SingleValueRuleFragment.createMessageData(undefined, undefined, true, oAttrToMsgDet.msgdettoplaceh);
				let oMessageData = {
					message: oAttrToMsgDet.message,
					messageType: oAttrToMsgDet.messageType,
					messageClass: oAttrToMsgDet.messageClass,
					msgno: oAttrToMsgDet.msgno,
					messageClassPlaceholderTable: arrMessageClassPlaceholderTable
				};
		
				oController.SingleValueRuleFragment.createPayload(oController, oAttributeToCondition, oSelectedBusinessRule, oValueCheckModel, oMessageData, true, "Check")
				.then(function (oDataToWrite){
					oController.SingleValueRuleFragment.sendRequest(oController, oBusinessRulesModel, oDataToWrite, oSelectedBusinessRule, oSwitchComponent, true, "Check");
				})				
				.catch(()=>{
					oSwitchComponent.setState(!bActiveRuleState);
				});
				break;

			case "3":
				oEntityToAttr = oSelectedBusinessRule.RuleExpr.modeltoentity[0].entitytoattr[0];
				oAttributeToCondition = oController.SingleValueRuleFragment.createAttributeToCondition(oController, oEntityToAttr.attrtocondition, true);
				let oAttrToSetValue = oEntityToAttr.attrtosetvalue[0];
				let sModelId = oAttrToSetValue.usmdModel;
				let sAttributeName = oAttrToSetValue.usmdAttribute;
				oValueCheckModel = {
					selectedAttributeName: oEntityToAttr.usmdAttribute,
					checkExpressionType: oEntityToAttr.exprType,
					expressionToSave: oEntityToAttr.expression,
					ruleEnabled: sSwitchState
				};
				let oCondition = {
					tovalattr: oAttrToSetValue.tovalattr,
					value: oAttrToSetValue.value,
					dimension: oAttrToSetValue.dimension,
					unit: oAttrToSetValue.unit,
					type: oAttrToSetValue.type, 
					setValToConcat: oAttrToSetValue.setvaltoconcat,
					ifTrue: oAttrToSetValue.ifTrue,
					ifFalse: oAttrToSetValue.ifFalse
				};
				let sDescription = oAttrToSetValue.valuedescr;
				let sDelimiter = oAttrToSetValue.delimiter;
				let arrATTRTOSETVALUE = oController.SingleValueRuleFragment.createAttrSetValue(sModelId, sAttributeName, oCondition, undefined, sDelimiter, sDescription, true);
		
				oController.SingleValueRuleFragment.createPayload(oController, oAttributeToCondition, oSelectedBusinessRule, oValueCheckModel, arrATTRTOSETVALUE, true, "Derivation")
				.then(function (oDataToWrite){
					oController.SingleValueRuleFragment.sendRequest(oController, oBusinessRulesModel, oDataToWrite, oSelectedBusinessRule, oSwitchComponent, true, "Derivation");
				})
				.catch(()=>{
					oSwitchComponent.setState(!bActiveRuleState);
				});
				break;

			default:
				break;
		}
	};

	BusinessRulesController.onRuleDeletePressed = function(oEvent){
		let oController = this;
		let oBusinessRulesModel = oController.getView().getModel("BusinessRules");
		let sPath = oEvent.getSource().getParent().getBindingContextPath();
		let oSelectedBusinessRule = oBusinessRulesModel.getProperty(sPath);
		let oEntityToAttr, oAttributeToCondition, oValueCheckModel;

		let promiseShowPopupAlert = Utilities.showPopupAlert(
			oController.geti18nText("businessRulesDetails.derivationFragment.deleterule.confirm"), 
			MessageBox.Icon.WARNING,
			oController.geti18nText("businessRulesDetails.derivationFragment.deleterule.title"),
			[MessageBox.Action.YES, MessageBox.Action.NO]
		);
		
		promiseShowPopupAlert.then(function(){

			switch (oSelectedBusinessRule.RuleTypeId){
				case "1":
					let sSwitchState = oSelectedBusinessRule.IsActive ? "YES" : "NO";
					let sAttribute;
					if(oSelectedBusinessRule.AiGenerated){
						sAttribute = oController.getAttributeFromString(oSelectedBusinessRule.BusinessRuleName, oSelectedBusinessRule.CRTypeId, oSelectedBusinessRule.EntityId, oSelectedBusinessRule.RuleTypeId);
					} else {
						sAttribute = oSelectedBusinessRule.RuleExpr.modeltobrfnav[0].brftoattrnav[0].usmdAttribute;
					}
					oController.MandatoryRuleFragment.createPayload(oController, oSelectedBusinessRule, sAttribute, sSwitchState, true)
					.then(function (oDataToWrite){
						oController.MandatoryRuleFragment.sendRequest(oController, true,  oDataToWrite, {}, "MainDelete");
					});
					break;
				case "2":
					let oMessageData;
					if(oSelectedBusinessRule.AiGenerated){
						oAttributeToCondition = [];
						oValueCheckModel = {
							selectedAttributeName: oController.getAttributeFromString(oSelectedBusinessRule.BusinessRuleName, oSelectedBusinessRule.CRTypeId, oSelectedBusinessRule.EntityId, oSelectedBusinessRule.RuleTypeId),
							ruleEnabled: false
						};
						oMessageData = undefined;
					} else{

						oEntityToAttr = oSelectedBusinessRule.RuleExpr.modeltoentity[0].entitytoattr[0];
						oAttributeToCondition = oController.SingleValueRuleFragment.createAttributeToCondition(oController, oEntityToAttr.attrtocondition, true);
						
						oValueCheckModel = {
							selectedAttributeName: oEntityToAttr.usmdAttribute,
							checkExpressionType: oEntityToAttr.exprType,
							expressionToSave: oEntityToAttr.expression,
							ruleEnabled: oEntityToAttr.isNeeded
						};
						let oAttrToMsgDet = oEntityToAttr.attrtomsgdet[0];
						let {arrMessageClassPlaceholderTable} = oController.SingleValueRuleFragment.createMessageData(undefined, undefined, true, oAttrToMsgDet.msgdettoplaceh);
						oMessageData = {
							message: oAttrToMsgDet.message,
							messageType: oAttrToMsgDet.messageType,
							messageClass: oAttrToMsgDet.messageClass,
							msgno: oAttrToMsgDet.msgno,
							messageClassPlaceholderTable: arrMessageClassPlaceholderTable
						};
					}
						oController.SingleValueRuleFragment.createPayload(oController, oAttributeToCondition, oSelectedBusinessRule, oValueCheckModel, oMessageData, true, "CheckDelete")
						.then(function (oDataToWrite){
							oController.SingleValueRuleFragment.sendRequest(oController, oBusinessRulesModel, oDataToWrite, oSelectedBusinessRule, {}, true, "CheckDelete");
						});
					break;
				case "3":
					let arrATTRTOSETVALUE;
					if(oSelectedBusinessRule.AiGenerated){
						oAttributeToCondition = [];
						oValueCheckModel = {
							selectedAttributeName: oController.getAttributeFromString(oSelectedBusinessRule.BusinessRuleName, oSelectedBusinessRule.CRTypeId, oSelectedBusinessRule.EntityId, oSelectedBusinessRule.RuleTypeId),
							ruleEnabled: false
						};
						arrATTRTOSETVALUE = undefined;
					} else {

						oEntityToAttr = oSelectedBusinessRule.RuleExpr.modeltoentity[0].entitytoattr[0];
						oAttributeToCondition = oController.SingleValueRuleFragment.createAttributeToCondition(oController, oEntityToAttr.attrtocondition, true);
						let oAttrToSetValue = oEntityToAttr.attrtosetvalue[0];
						let sModelId = oAttrToSetValue.usmdModel;
						let sAttributeName = oAttrToSetValue.usmdAttribute;
						oValueCheckModel = {
							selectedAttributeName: oEntityToAttr.usmdAttribute,
							checkExpressionType: oEntityToAttr.exprType,
							expressionToSave: oEntityToAttr.expression,
							ruleEnabled: oEntityToAttr.isNeeded
						};
						let oCondition = {
						tovalattr: oAttrToSetValue.tovalattr,
						value: oAttrToSetValue.value,
						dimension: oAttrToSetValue.dimension,
						unit: oAttrToSetValue.unit,
						type: oAttrToSetValue.type, 
						setValToConcat: oAttrToSetValue.setvaltoconcat,
						ifTrue: oAttrToSetValue.ifTrue,
						ifFalse: oAttrToSetValue.ifFalse
					};
					let sDescription = oAttrToSetValue.valuedescr;
					let sDelimiter = oAttrToSetValue.delimiter;
					arrATTRTOSETVALUE = oController.SingleValueRuleFragment.createAttrSetValue(sModelId, sAttributeName, oCondition, undefined, sDelimiter, sDescription, true);
				}
			
					oController.SingleValueRuleFragment.createPayload(oController, oAttributeToCondition, oSelectedBusinessRule, oValueCheckModel, arrATTRTOSETVALUE, true, "DerivationDelete")
					.then(function (oDataToWrite){
						oController.SingleValueRuleFragment.sendRequest(oController, oBusinessRulesModel, oDataToWrite, oSelectedBusinessRule, {}, true, "DerivationDelete");
					});
					break;
				case "4":
					oController.MultiValueRuleFragment.createDeletePayload(oController, oSelectedBusinessRule, true)
					.then((oDataToDelete) => {
						oController.MultiValueRuleFragment.sendDeleteRequest(oController, oBusinessRulesModel, oDataToDelete, true, "Validation");
					});
					break;
				case "5":
					oController.MultiValueRuleFragment.createDeletePayload(oController, oSelectedBusinessRule, true)
					.then((oDataToDelete) => {
						oController.MultiValueRuleFragment.sendDeleteRequest(oController, oBusinessRulesModel, oDataToDelete, true, "Derivation");
					});
					break;
				default:
					return;
				}
		});
	};

	BusinessRulesController.onClickLinkBusinessRules = function() {
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		oRouter.navTo("BusinessRules", {}, true);
		this.getView().getModel("BusinessRulesDetails").setData({});
	};

	BusinessRulesController.onSelectImportToCRType = function (oEvent) {
		let oController = this;
		let sSelectedToCRType = oEvent.getSource().getSelectedKey();
		let oView = oController.getView();
		let oBusinessRulesModel = oView.getModel("BusinessRules");
		let sDataModel = oBusinessRulesModel.getProperty("/importRules/selectedDataModel");
		let sSelectedFromCRType = oBusinessRulesModel.getProperty("/importRules/selectedFromCRType");
		let iRulesBy = oBusinessRulesModel.getProperty("/RulesBy");
		
		this._getBackgroundJobStatus(sSelectedToCRType);
		
		/**
		 * If Rule By Type = By Entity
		 * 1. Clear Rules List Data
		 * 2. Retrieve the rules and add to the search list.
		 */
		if(iRulesBy === 0){
			this.ImportRulesList.setListData(); // Clear the rules list 
			let oBusinessRulesPromise = 
				GetLists.getBusinessRuleList(oController, sDataModel, undefined, sSelectedFromCRType, undefined);
		
			oBusinessRulesPromise.then((arrRules) => {
				// Filter the data with the CR Type selected 
				arrRules.sort((a, b) => (a.EntityName - b.EntityName || a.RuleDesc.localeCompare(b.RuleDesc)));
				oController.ImportRulesList.setListData(arrRules);
			}); 
			return;
		}

		/**
		 * If Rule By Type = By Step Type
		 * 1. Clear the TO Step Type 
		 * 2. Retrieve the to Step list and set 
		 */
		if(iRulesBy === 1){
			oBusinessRulesModel.setProperty("/importRules/selectedToCRStep", undefined);
			let oPromiseWFStepList = GetLists.getWorkflowStepList(this, sSelectedToCRType);
			oPromiseWFStepList.then(function(aWFStepList){
				aWFStepList?.forEach(oStep => {
					oStep.UsmdCreqStep = oStep.UsmdCreqStep.padStart(2, "0");
				});

				oBusinessRulesModel.setProperty("/importRules/toCRStepsList", aWFStepList);
			});

			return;
		}
	};

	BusinessRulesController.onImportRulesSave = function() {
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");
		let iRulesBy = oBusinessRulesModel.getProperty("/RulesBy");
		if (iRulesBy === 0) {
			this._ImportRulesByEntity();
		}else{
			this._ImportRulesByCRStepType();
		}
	};

	BusinessRulesController.onImportRulesCancel = function () {
		if (this.ImportRulesList) {
			this.ImportRulesList.removeMultiSelectedItems();
			this.ImportRulesList.setListData([]);
		}
		
		this.ImportRulesDialog.close();
	};

	BusinessRulesController._ImportRulesByEntity = function(){
		let oController = this;
		let oView = oController.getView();
		let oBusinessRulesModel = oView.getModel("BusinessRules");
		let sDataModel = oBusinessRulesModel.getProperty("/importRules/selectedDataModel");
		let sSelectedFromCRType = oBusinessRulesModel.getProperty("/importRules/selectedFromCRType");
		let sSelectedToCRType = oBusinessRulesModel.getProperty("/importRules/selectedToCRType");
		let promiseShowPopupAlert;
		let arrSelectedItems = oController.ImportRulesList.getMultiSelectedItems();
		
		if(!sSelectedFromCRType || !sSelectedToCRType) {
			Utilities.showPopupAlert(this.geti18nText("businessRules.listPage.importValidation.crTypesMandatory.message"),
				MessageBox.Icon.ERROR,
				this.geti18nText("businessRules.listPage.importValidation.crTypesMandatory.title")
			);
			return;
		}
		
		if(arrSelectedItems.length === 0) {
			promiseShowPopupAlert =
				Utilities.showPopupAlert(
					this.geti18nText("businessRules.listPage.importValidation.rulesNotSelected.message"),
					MessageBox.Icon.ERROR,
					this.geti18nText("businessRules.listPage.importValidation.rulesNotSelected.title")
				);
			promiseShowPopupAlert.then(function () {
			});
			return;
		}
		
		let oDataToWrite = {
			UsmdModel: sDataModel,
			SourceCr: sSelectedFromCRType,
			TargetCr: sSelectedToCRType,
			Featuretype: "1",
			NAV_CRTORULES: [],
			NAV_CRTOMSGDET: []
		};
		
		arrSelectedItems.forEach(oSelectedItem => {
			oDataToWrite.NAV_CRTORULES.push({
				UsmdModel: oSelectedItem.data.ModelName,
				UsmdEntity: oSelectedItem.data.EntityName,
				RuleId: oSelectedItem.data.RuleId,
				RuleName: oSelectedItem.data.RuleName
			});
		});

		let oWriteParameterObject = {
			success: {
				fCallback: function (oParamController, oDataResult) {
					let arrMessages = [];
					let sSaveError = "";
					jQuery.extend(true, arrMessages, oDataResult.NAV_CRTOMSGDET.results);
					// Sort the messages
					arrMessages.sort(function (m1) {
						if (m1.MessageType === "E")
						{
							sSaveError = "Yes";
							return -1;
						}
						if (m1.MessageType === "W") { return 1; }
						return 0;
					});
					if (sSaveError === "")
					{
						oParamController.ImportRulesList.removeMultiSelectedItems();
						oParamController.ImportRulesList.setListData([]);
						oParamController.ImportRulesDialog.close();
					}
					ModelMessages.showMessagesDialog(oParamController, arrMessages, oParamController.getView());
					sap.ui.core.BusyIndicator.hide();
				},
				oParam: this
			},
			error: {
				fCallback: function () {
					sap.ui.core.BusyIndicator.hide();
					promiseShowPopupAlert =
						Utilities.showPopupAlert(
							oController.geti18nText("businessRules.listPage.import.failed.message"), 
							MessageBox.Icon.INFORMATION, 
							oController.geti18nText("businessRules.listPage.import.failed.title"));
					promiseShowPopupAlert.then(function () {
					});	
				},
				oParam: this
			}
		};
	
		oController.invokeImportCall(
			oController, oDataToWrite, oWriteParameterObject, 
			oController.geti18nText("businessRules.listPage.import.byEntity.service.message"));
	};

	BusinessRulesController._ImportRulesByCRStepType = function(){
		let oController = this;
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");
		let oImportData = oBusinessRulesModel.getProperty("/importRules");

		if(!oImportData.selectedDataModel || !oImportData.selectedFromCRType || !oImportData.selectedToCRType 
			|| !oImportData.selectedFromCRStep || !oImportData.selectedToCRStep) {
			Utilities.showPopupAlert(
				this.geti18nText("businessRules.listPage.import.byStepType.fieldsMandatory.message"),
				MessageBox.Icon.ERROR,
				this.geti18nText("businessRules.listPage.import.byStepType.fieldsMandatory.title")
			);
			return;
		}

		// Fill the data into the target object to be sent to the server 
		let oServerData = {
			UsmdModel: oImportData.selectedDataModel,
			SourceCr: oImportData.selectedFromCRType,
			TargetCr: oImportData.selectedToCRType,
			Featuretype: "5",        
			SourceCrstep: oImportData.selectedFromCRStep,
			TargetCrstep: oImportData.selectedToCRStep,
			NAV_CRTORULES: [],
			NAV_CRTOMSGDET: []
		};

		let oWriteParameterObject = {
			success: {
				fCallback: function (oParamController, oDataResult) {
					let arrMessages = [];
					jQuery.extend(true, arrMessages, oDataResult.NAV_CRTOMSGDET.results);
					// Sort the messages
					arrMessages.sort(function (m1) {
						if (m1.MessageType === "E")
						{
							return -1;
						}
						if (m1.MessageType === "W") { return 1; }
						return 0;
					});
					ModelMessages.showMessagesDialog(oParamController, arrMessages, oParamController.getView());
					sap.ui.core.BusyIndicator.hide();
					oParamController.ImportRulesDialog.close();
				},
				oParam: this
			},
			error:	{
				fCallback: function () {
					sap.ui.core.BusyIndicator.hide();
					let promiseShowPopupAlert =
						Utilities.showPopupAlert(
						oController.geti18nText("businessRules.listPage.import.byCrType.failed.message"), 
						MessageBox.Icon.INFORMATION, 
						oController.geti18nText("businessRules.listPage.import.failed.title"));
						promiseShowPopupAlert.then(function () {
						});	
				}, 
				oParam:this
			}
		};

		oController.invokeImportCall(
			oController, oServerData, oWriteParameterObject, 
			oController.geti18nText("businessRules.listPage.import.byStepType.service.message"));
	};

	BusinessRulesController.invokeImportCall = function (oController, oServerData, oWriteParameterObject, sMessage){
		oController._getSelectedTransportPackage(true, true, true)
		.then(function(oTransporPackageResponse){
			// Store the transport to the server data 
			oServerData.CustTransport = oTransporPackageResponse.customizingTransport;
			// Store the transport to the server data 
			oServerData.WbTransport = oTransporPackageResponse.workbenchTransport;
			// Store the package to the server data 
			oServerData.Package = oTransporPackageResponse.package;

			(new DMRDataService(
				oController,
				"COPY_BUSINESS_RULES",
				"/CRTYPESSet",
				undefined,
				"/", // Root of the received data
				sMessage
			))
			.saveData(false, oServerData, null, oWriteParameterObject);
		});
	};

	BusinessRulesController.onTransportPackageDialogCreated = function(oEvent) {
		let oController = this;
		let comp = oEvent.getParameter("component");
		// store the component handle 
		this._transportPackageSelectionDialog = comp;
		
		this.getUsername()
		.then(function (oSapUserInfo) {
			let sUsername = oSapUserInfo.Sapname;
			if (!sUsername) {
				Utilities.showPopupAlert(
					oController.geti18nText("common.noLogin.description"), 
					MessageBox.Icon.ERROR, 
					oController.geti18nText("common.noLogin.title"));
			} else {
				comp.setUser(sUsername);
			}
		});
	};

	BusinessRulesController._getSelectedTransportPackage = function(bCustomizing, bWorkbench, bPackage) {
		return this._transportPackageSelectionDialog.open(bCustomizing, undefined, bWorkbench, undefined, bPackage, undefined);
	};

	BusinessRulesController._getBackgroundJobStatus = async function(sCRType) {
		let oStatus;
		if (sCRType) {
			let oBusinessRulesModel = this.getView().getModel("BusinessRules");
			oStatus = await GetLists.getBackgroundJobStatus(this, undefined, "1", undefined, sCRType);
			if (oStatus.Restype.includes("S")) {
				let sMessage = oStatus.JOB2MESSAGENAV.results[0].Message + " " + oStatus.JOB2MESSAGENAV.results[0].Msgv1;
				Utilities.showPopupAlert(
					sMessage, 
					MessageBox.Icon.INFORMATION, 
					this.geti18nText("businessRules.listPage.import.notAllowed.title"));
				oBusinessRulesModel.setProperty("/importAllowed", false);
			}else{
				oBusinessRulesModel.setProperty("/importAllowed", true);
			}
		}else{
			oStatus = await GetLists.getBackgroundJobStatus(this, undefined, "1");
			let aMessages = oStatus.JOB2MESSAGENAV.results;
			for (let i = 0; i < aMessages.length; i++) {
				let sMessageType = aMessages[i].MessageType === "S" ? sap.ui.core.MessageType.Success 
																	: aMessages[i].MessageType === "E" 
																	? sap.ui.core.MessageType.Error 
																	: sap.ui.core.MessageType.Information;
				Utilities.sendNotification(
					this, sMessageType,  
					aMessages[i].Message, 
					aMessages[i].Msgv1, 
					this.geti18nText("businessRules.title")
				);
			}
		}
	};

	BusinessRulesController.onClickAddBusinessRule = function(oEvent) {
		let sId = oEvent.getSource().getId();
		let sRuleType;
		let oBusinessRulesModel = this.getView().getModel("BusinessRules").getData("oData");

		if(sId.includes("btnAddCRStepTypeRule")){
			sRuleType = "6";
		}else{
			sRuleType = oEvent.getSource().getKey();
		}
		sap.ui.core.UIComponent.getRouterFor(this).navTo("BusinessRulesDetails", {
			DataModelId: oBusinessRulesModel.DataModelFilter, //oBusinessRulesModelData.DataModelFilter,
			CRTypeId: oBusinessRulesModel.CRTypeFilter, //oBusinessRulesModelData.CRTypeFilter,
			EntityId: oBusinessRulesModel.EntityFilter, //oBusinessRulesModelData.EntityFilter,
			RuleType: sRuleType,
			BusinessRuleName: this.geti18nText("businessRules.common.ruleType.new")
		});
	};

	BusinessRulesController.onClickEditBusinessRule = function(oEvent) {
		let oBusinessRulesModel = this.getView().getModel("BusinessRules");
		let sContextPath = oEvent.getSource().getParent().getBindingContextPath();
		let oBusinessRuleDetails = oBusinessRulesModel.getProperty(sContextPath);
		//12790 - Add mandatory and single value rules to new business rules screen
		sap.ui.core.UIComponent.getRouterFor(this).navTo("BusinessRulesDetails", {
			DataModelId: oBusinessRuleDetails.DataModelId,
			CRTypeId: oBusinessRuleDetails.CRTypeId,
			RuleType: oBusinessRuleDetails.RuleTypeId,
			BusinessRuleName: oBusinessRuleDetails.BusinessRuleName,
			CRStep: oBusinessRuleDetails.CRStep === "**" ? "All" : oBusinessRuleDetails.CRStep
		});
	};

	BusinessRulesController.onSearchRuleByName = function(oEvent) {
		// add filter for search
		let aFilters;
		// Get the search text
		let sQuery = oEvent.getSource().getValue();

		if (sQuery && sQuery.length > 0) {
			aFilters = new sap.ui.model.Filter({
				filters: [
					new sap.ui.model.Filter({
							path: "UserRuleName",
							operator: sap.ui.model.FilterOperator.Contains,
							value1: sQuery,
							caseSensitive: false
						}),
					new sap.ui.model.Filter({
							path: "BusinessRuleName",
							operator: sap.ui.model.FilterOperator.Contains,
							value1: sQuery,
							caseSensitive: false
						})
				],
				and: false
			});
		}
		
		// update list binding
		this.getView()
			.byId("tblBusinessRules")
			.getBinding("items")
			.filter(aFilters);
	};

	return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.BusinessRules.BusinessRules", BusinessRulesController);

});