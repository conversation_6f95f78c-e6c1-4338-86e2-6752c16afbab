sap.ui.define([

], function () {
	"use strict";
    
    let oUserDefinedExpressionModel = {};

    oUserDefinedExpressionModel.openUserDefinedExpressionDialog = function(oParentController) {
        let oController = this;
        oController.UserDefinedFragmentId = oParentController.UserDefinedFragmentId;
        let oBusinessRuleModel = oParentController.getView().getModel("BusinessRulesDetails");
        oBusinessRuleModel.setProperty("/userDefinedExpression", "");
        oBusinessRuleModel.setProperty("/userDefinedExpressionValueState", "None");
        oBusinessRuleModel.setProperty("/userDefinedExpressionValueStateText", "");
        
        // Open the User-Defined Expression dialog
        let oUserDefinedExpressionDialog = oController.getUserDefinedExpressionFragment(oParentController);
        oUserDefinedExpressionDialog.open();
    };

    oUserDefinedExpressionModel.getUserDefinedExpressionFragment = function(oParentController) {
		if (!this._oUserDefinedFrgmt) {
			this._oUserDefinedFrgmt = sap.ui.xmlfragment(
				this.UserDefinedFragmentId,
				"dmr.mdg.supernova.SupernovaFJ.view.BusinessRules.UserDefinedExpression",
				oParentController
			);

			oParentController.getView().addDependent(this._oUserDefinedFrgmt);
		}

		return this._oUserDefinedFrgmt;
	};
	
    oUserDefinedExpressionModel.onClickNewAttribute = function() {
        let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");
        
        let sUserDefinedExpression = oBusinessRuleModel.getProperty("/userDefinedExpression");
        let nHowManyTimes = sUserDefinedExpression.replace(/[^<]/g, "").length;
        oBusinessRuleModel.setProperty("/userDefinedExpression", sUserDefinedExpression + "<" + (nHowManyTimes + 1) + ">");
    };

    oUserDefinedExpressionModel.onClickOpenParenthesis = function() {
        let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");
        
        let sUserDefinedExpression = oBusinessRuleModel.getProperty("/userDefinedExpression");
        oBusinessRuleModel.setProperty("/userDefinedExpression", sUserDefinedExpression + "(");
    };

    oUserDefinedExpressionModel.onClickCloseParenthesis = function() {
        let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");
        
        let sUserDefinedExpression = oBusinessRuleModel.getProperty("/userDefinedExpression");
        oBusinessRuleModel.setProperty("/userDefinedExpression", sUserDefinedExpression + ")");
    };

    oUserDefinedExpressionModel.onClickAndOperator = function() {
        let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");
        
        let sUserDefinedExpression = oBusinessRuleModel.getProperty("/userDefinedExpression");
        oBusinessRuleModel.setProperty("/userDefinedExpression", sUserDefinedExpression + " and ");
    };

    oUserDefinedExpressionModel.onClickOrOperator = function() {
        let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");

        let sUserDefinedExpression = oBusinessRuleModel.getProperty("/userDefinedExpression");
        oBusinessRuleModel.setProperty("/userDefinedExpression", sUserDefinedExpression + " or ");
    };

    oUserDefinedExpressionModel.checkExpressionState = function(sExpression) {
        let oBusinessRuleModel = this.getView().getModel("BusinessRulesDetails");
        let sValueStateText = "";
        let sValueState = sap.ui.core.ValueState.None;

        /**
         * Error if
         * 1. Number of elements exceeds 30 <1> <2> -- <30>
         */
        const array = [...sExpression.matchAll(/\<\d+\>/g)];
        if(array.length > 8){
            sValueStateText = "Long expression. It can take too long to evaluate.";
            sValueState = sap.ui.core.ValueState.Warning;
        }
        if(array.length > 30){
            sValueStateText = "Expression too long. Unsupported.";
            sValueState = sap.ui.core.ValueState.Error;
        }

        oBusinessRuleModel.setProperty("/userDefinedExpressionValueState", sValueState);
        oBusinessRuleModel.setProperty("/userDefinedExpressionValueStateText", sValueStateText);
        return true;
    };

    oUserDefinedExpressionModel.onClickSave = function() {
        let oController = this;
        let oBusinessRuleModel = oController.getView().getModel("BusinessRulesDetails");
        let sUserDefinedExpression = oBusinessRuleModel.getProperty("/userDefinedExpression");
        let oSelectedBusinessRule = oBusinessRuleModel.getProperty("/selectedBusinessRule");
        
        let oDataToWrite = {
            "UsmdModel": oSelectedBusinessRule.DataModelId,
            "UsmdCreqType": oSelectedBusinessRule.CRTypeId,
            "RsEntity": " ",
            "RsAttribute": " ",
            "MODELTOVALIDATENAV": [{
                "Usmdmodel": oSelectedBusinessRule.DataModelId,
                "Expression": sUserDefinedExpression,
                "Message": " "
            }],
            "MODELTOMESSAGE": []
        };
        
        let oWriteParameterObject = {
            success: function (oDataResult, oResponse) {
                let bError = false;
                let sErrorMessage = "";
                
                sap.ui.core.BusyIndicator.hide();
                
                if (oResponse.data.MODELTOMESSAGE.results) {
                    for (let result of oResponse.data.MODELTOMESSAGE.results) {
                        if (result.MessageType === "E") {
                            bError = true;
                            sErrorMessage = result.Message;
                            break;
                        }
                    }
                }
                
                if (bError) {
                    oBusinessRuleModel.setProperty("/userDefinedExpressionValueState", "Error");
			        oBusinessRuleModel.setProperty("/userDefinedExpressionValueStateText", sErrorMessage);
                } else {
                    oBusinessRuleModel.setProperty("/userDefinedExpressionValueState", "None");
			        oBusinessRuleModel.setProperty("/userDefinedExpressionValueStateText", "");
                    oBusinessRuleModel.setProperty("/valueCheck/checkExpression", sUserDefinedExpression);
                    
                    let oUserDefinedExpressionDialog = oController.UserDefinedExpressionDialog.getUserDefinedExpressionFragment();
                    oUserDefinedExpressionDialog.close();
                }
            },
            error: function () {
                sap.ui.core.BusyIndicator.hide();
                oBusinessRuleModel.setProperty("/userDefinedExpressionValueState", "Error");
			    oBusinessRuleModel.setProperty("/userDefinedExpressionValueStateText", "The expression could not be validated");
            }
        };

        sap.ui.core.BusyIndicator.show(300);
        let oDataModel = oController.getModel("SAVE_BRF_DERIVATION_RULE");
        oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
        oDataModel.create("/MODELSet", oDataToWrite, oWriteParameterObject);
    };

    oUserDefinedExpressionModel.onClickCancel = function() {
        let oUserDefinedExpressionDialog = this.UserDefinedExpressionDialog.getUserDefinedExpressionFragment();
		oUserDefinedExpressionDialog.close();
    };

    return oUserDefinedExpressionModel;
});