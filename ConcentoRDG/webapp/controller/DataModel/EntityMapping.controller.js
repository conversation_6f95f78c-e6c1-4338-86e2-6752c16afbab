sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
	"sap/ui/model/json/JSONModel",
	"sap/ui/core/mvc/Controller",
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
	"sap/m/MessageBox",
	"dmr/mdg/supernova/SupernovaFJ/model/ModelMessages",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities"
], function (BaseController, JSONModel, Controller, GetListsData, DMRDataService, ModelMessages, MessageBox, Utilities ) {
	"use strict";
	
	let EntityMappingController = {};
	
	EntityMappingController.constructor = function () {
			this.arrEntitiesData = [];
	};
 
	EntityMappingController.openMappingDialog = function(oParentController, arrMappingEntities) {
		
		
		if (!this.dialogHandle) {
				this.dialogHandle = 
					sap.ui.xmlfragment("entityDetailsFragment", 
					"dmr.mdg.supernova.SupernovaFJ.view.DataModel.EntityMapping",
					this);
				oParentController.getView().addDependent(this.dialogHandle);
		}
		
		// Store the parent controller.
		this.ParentController = oParentController;
		
		// Store the entity parameters received 
		this.setEntityData(arrMappingEntities);
		
		// Get the SMT mapping data
		this.getSMTMappingList();
		
		// Open the dialog while the content is loading
		this.dialogHandle.open();

	};
 
	// Store the necessary information to the controller for later use
	EntityMappingController.setEntityData = function(arrEntities)
	{
		// Make a copy of the array
		jQuery.extend(true, this.arrEntitiesData, arrEntities);
		
		// Save to the model 
		this.getDataModel().setProperty("/entitiesList", this.arrEntitiesData);
		this.getDataModel().setProperty("/bEnableDone", false);
		// return this for chaining
		return this; 
	};

	// Get the data model to store the binding information, retrieve the reference from parent controller
	EntityMappingController.getDataModel = function()
	{
		let oParentView = this.getParentView();
		let oEntityMappingModel = oParentView.getModel("entityMappingModel");

		if(!oEntityMappingModel)
		{
			oParentView.setModel(new JSONModel(), "entityMappingModel");
			oEntityMappingModel = oParentView.getModel("entityMappingModel");
		}

		return oEntityMappingModel;
	};
	
	// Get the SMT mapping list for the selected entities
	EntityMappingController.getSMTMappingList = function(){
		let oDataModelJSON = this.getDataModel();
		let oParentController = this.ParentController;
		let oController = this;
		let arrSMTMapping =[];

	   //let oTable = this.byId("idEntityMappingTable");
		let promiseSMTMappingList =
			GetListsData.getSMTMappingList(oParentController, this.arrEntitiesData);

		promiseSMTMappingList.then(function (oData) {
			if(oData){
				for(let i=0; i < oData.length; i++){
					// Start filling in the data into the structure 
					let arrMappingInfo = {
						editable: false,
						Application: oData[i].Application,
						Usmdentity: oData[i].Usmdentity,
						Usmdmodel: oData[i].Usmdmodel,
						arrAttributes: oData[i].arrAttributes,
						MappingSteps: {}
					};
					
					// Copy the results array -- deep copy
					jQuery.extend(true, arrMappingInfo.MappingSteps, oData[i].SMTAPPLTOMAPSTEP);

					// Add properties to Include Structures in case of only one option
					if(arrMappingInfo.MappingSteps &&
					   arrMappingInfo.MappingSteps.results &&
					   arrMappingInfo.MappingSteps.results.length === 1 &&
					   arrMappingInfo.MappingSteps.results[0].MAP2SOURCEINCL &&
					   arrMappingInfo.MappingSteps.results[0].MAP2SOURCEINCL.results &&
					   arrMappingInfo.MappingSteps.results[0].MAP2SOURCEINCL.results.length === 1){
						arrMappingInfo.IncludeStructures = arrMappingInfo.MappingSteps.results[0].MAP2SOURCEINCL.results;
					}

					arrSMTMapping.push(arrMappingInfo);
				}
			}
			oDataModelJSON.setProperty("/smtMappingList", arrSMTMapping);
			oController.setComboBoxes();
		});
	};

	EntityMappingController.setComboBoxes = function() {
		// Set Combo Boxes in case of only one option for Mapping Steps and Include Structures
		let oTable = sap.ui.getCore().byId("entityDetailsFragment--idEntityMappingTable");
		let arrSMTMapping = this.getDataModel().getProperty("/smtMappingList");
		for(let i=0; i<arrSMTMapping.length; ++i){
			if(arrSMTMapping[i].MappingSteps &&
			   arrSMTMapping[i].MappingSteps.results &&
			   arrSMTMapping[i].MappingSteps.results.length === 1 &&
			   arrSMTMapping[i].MappingSteps.results[0].MAP2SOURCEINCL &&
			   arrSMTMapping[i].MappingSteps.results[0].MAP2SOURCEINCL.results &&
			   arrSMTMapping[i].MappingSteps.results[0].MAP2SOURCEINCL.results.length === 1){
				oTable.getItems()[i].getCells()[2].setSelectedItem(oTable.getItems()[i].getCells()[2].getItems()[0]);
				arrSMTMapping[i].SourceStructure = arrSMTMapping[i].MappingSteps.results[0].Sourcestructure;
				arrSMTMapping[i].TargetStructure = arrSMTMapping[i].MappingSteps.results[0].Targetstructure;
				arrSMTMapping[i].Viewref = arrSMTMapping[i].MappingSteps.results[0].MAP2SOURCEINCL.results[0].Viewref;
				oTable.getItems()[i].getCells()[5].setSelectedItem(oTable.getItems()[i].getCells()[5].getItems()[0]);
			}
		}
	};
	
	EntityMappingController.mappingStepSelectionChange = function(oEvent){
		// Get the selected key
        let sSelectedMappingStep = oEvent.getSource().getSelectedKey();

		// Get the table selected index 
		let oTable = sap.ui.getCore().byId("entityDetailsFragment--idEntityMappingTable");
		let iSelectedIndex = oTable.indexOfItem(oTable.getSelectedItem());

		// Retrieve the data model and update with the selected information		
        let arrSelectedMapping = this.getDataModel().getProperty("/smtMappingList");
        
        // Update the mapping mode with the updated data
        arrSelectedMapping[iSelectedIndex].selectedStructure = undefined;
        arrSelectedMapping[iSelectedIndex].MappingSteps.results.forEach(function(mappingItem){
        	if(mappingItem.Mappingstep === sSelectedMappingStep){
        		arrSelectedMapping[iSelectedIndex].SourceStructure = mappingItem.Sourcestructure; 
        		arrSelectedMapping[iSelectedIndex].TargetStructure = mappingItem.Targetstructure;
        		arrSelectedMapping[iSelectedIndex].IncludeStructures = mappingItem.MAP2SOURCEINCL.results;
        	}
        });
	};
		
				/**
	 * Executed in controller context. this = UIDetailsController
	 * 
	 * 1. Disable edit on all rows 
	 * 2. Identify the selected row
	 * 3. Enable editing on current row
	 */
	EntityMappingController.rowSelectionChange  = function () {
		// Get the selected item 
		let oTable = sap.ui.getCore().byId("entityDetailsFragment--idEntityMappingTable");
		let iSelectedIndex = oTable.indexOfItem(oTable.getSelectedItem());
        let arrSelectedMapping = this.getDataModel().getProperty("/smtMappingList");

  		// Loop through the data and set editable to false 
		arrSelectedMapping.forEach(function (rowData) {
			rowData.editable = false;
		});
		// If nothing is selected, return (unlikely scenario)
		if (iSelectedIndex === -1) { return; }
		// Set the selected row to editable
		arrSelectedMapping[iSelectedIndex].editable = true;
		
		this.getDataModel().setProperty("/smtMappingList", arrSelectedMapping);
	};

	EntityMappingController.isMappingButtonEnabled = function (sMappingStep, sIncludeStructure, sStatus) {
		if(!sMappingStep || !sIncludeStructure || sStatus){
			return false;
		}
		return true;
	};

	// Create and save mapping 
	EntityMappingController.onPressCreateSMTForRow = function (oEvent) {
		let oController = this.ParentController;
		let oMappingController = this;
		let oSMTMappingModel = this.getDataModel();
		let sDev = oController.getView().getModel("viewEntity").getProperty("/storedPackage");
		let sTransport = oController.getView().getModel("viewEntity").getProperty("/workbenchTransport");
		
		// Get selected index
		let oTable = sap.ui.getCore().byId("entityDetailsFragment--idEntityMappingTable");
		oTable.setSelectedItemById(oEvent.getSource().getParent().getId());
		let iSelectedIndex = oTable.indexOfItem(oTable.getSelectedItem());
		
		// Get the data for the row 
		let arrSelectedMapping = oSMTMappingModel.getProperty("/smtMappingList");
		let oSelectedRowData = arrSelectedMapping[iSelectedIndex];
		
		// Fill the data to be sent to the server		
		let oData = {
			Usmdmodel: oSelectedRowData.Usmdmodel,
			Usmdentity: oSelectedRowData.Usmdentity,
			Application: oSelectedRowData.Application,
			Mappingstep: oSelectedRowData.selectedMappingStep,
			Sourcestructure: oSelectedRowData.SourceStructure,
			Targetstructure: oSelectedRowData.TargetStructure,
			Include: oSelectedRowData.selectedStructure,
			Viewref: oSelectedRowData.Viewref,
			Transport: sTransport,
			Usmddevclass: sDev,
			"SMT2ATTR": [],
			"SMT2MESSAGE": []
		};

		let arrAttribuite = arrSelectedMapping[iSelectedIndex].arrAttributes;
		for (let i in arrAttribuite) {
			let sNav = {
				Usmdmodel: oData.Usmdmodel,
				Usmdentity: oData.Usmdentity,
				Usmdattribute: arrAttribuite[i]
			};
			oData.SMT2ATTR.push(sNav);
		}
		
		// Trigger the Request to save mapping 
		let promiseCreateSmt = new Promise(function (resolve, reject) {
			(new DMRDataService(
			oController,
				"SMT_MAPPING_LIST",
				"/CREATESMTSet",
				"SMT Create",
				"/", // Root of the received data
				"Create SMT"
			)).saveData(
				false,
				oData,
				null, {
					success: {
						fCallback: function (oParams, oResponseData) {
							resolve(oResponseData);
						},
						oParams: oController
					},
					error: {
						fCallback: function (oParams, oErrorData) {
							reject(oErrorData);
						},
						oParams: oController
					}
				}
			);
		});
		promiseCreateSmt
			.then(function(oResponseDataSuccess){
				arrSelectedMapping[iSelectedIndex].responseMessages = oResponseDataSuccess.SMT2MESSAGE.results.slice();
			}, function(){
			    // arrSelectedMapping[iSelectedIndex].status = "Error";
			})
			.then(function(){
				oSMTMappingModel.setProperty("/smtMappingList", arrSelectedMapping);
				/* on create SMT , backend updating Process status to 3 if SMT created successfully for a row.
				So here checking the job status on every create SMT row click and updating status accordingly.
				Finally calling enabledone() function which decides Done button to be enabled or not based on status update.
				*/
				let promiseGetBGJobStatus = GetListsData.getDataModelJobStatus(oController, oSelectedRowData.Usmdmodel);
				promiseGetBGJobStatus.then(function(oDataBGJob){
					if(oDataBGJob.ProcessStatus === GetListsData.BKGRD_PROCESSSTATUS.PROCESS_DONE){
						arrSelectedMapping[iSelectedIndex].status = "Success"; 	
					}
					else if(oDataBGJob.ProcessStatus === GetListsData.BKGRD_PROCESSSTATUS.PROCESS_SMT_PENDING)
					{
						arrSelectedMapping[iSelectedIndex].status = "In Process"; 
					}
					else if((oDataBGJob.ProcessStatus === GetListsData.BKGRD_PROCESSSTATUS.PROCESS_SMT_ERROR)){
						arrSelectedMapping[iSelectedIndex].status = "Error";
					}
					else{
						arrSelectedMapping[iSelectedIndex].status = "In Process";
					}
					oMappingController.enableDone();
				});
			});
	};
	EntityMappingController.onPresStatus = function(){
		let oController = this.ParentController;
		let oSMTMappingModel = this.getDataModel();
		// Get selected index
		let oTable = sap.ui.getCore().byId("entityDetailsFragment--idEntityMappingTable");
		let iSelectedIndex = oTable.indexOfItem(oTable.getSelectedItem());
		// Get the data for the row 
		let arrSelectedMapping = oSMTMappingModel.getProperty("/smtMappingList");
		let oSelectedRowData = arrSelectedMapping[iSelectedIndex];
		let promiseGetBGJobStatus = GetListsData.getDataModelJobStatus(oController, oSelectedRowData.Usmdmodel);
		promiseGetBGJobStatus.then(function(oDataBGJob){
			if(oDataBGJob.ProcessStatus === GetListsData.BKGRD_PROCESSSTATUS.PROCESS_DONE){
				arrSelectedMapping[iSelectedIndex].status = "Success";
			}
			else if(oDataBGJob.ProcessStatus === GetListsData.BKGRD_PROCESSSTATUS.PROCESS_SMT_PENDING)
			{
				arrSelectedMapping[iSelectedIndex].status = "In Process"; 
			}
			else if((oDataBGJob.ProcessStatus === GetListsData.BKGRD_PROCESSSTATUS.PROCESS_SMT_ERROR)){
				arrSelectedMapping[iSelectedIndex].status = "Error";
			}
			else{
				arrSelectedMapping[iSelectedIndex].status = "In Process";
			}
			oSMTMappingModel.setProperty("/smtMappingList", arrSelectedMapping);
			let arrMessages = [];
			jQuery.extend(true, arrMessages, oSelectedRowData.responseMessages);
			// Sort the messages
			arrMessages.sort(function (m1) {
				if (m1.MessageType === "E") {
					return -1;
				}
				if (m1.MessageType === "S") {
					return 1;
				}
				return 0;
			});
			oController.ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
		});
	};
	
	EntityMappingController.showMessagesDialog = function () {
			ModelMessages.showMessagesDialog(this, [], this.getView());
		};
	EntityMappingController.getParentView = function () {
		return this.ParentController.getView();
	};
	
	EntityMappingController.closeMappingPage = function () {
		let oController = this;
		let promiseShowPopupAlert = Utilities.showPopupAlert("Are you sure want to close the mapping window? Once the window is closed, \n\you should visit the SMT application to complete the structure and attribute mapping.",
		sap.m.MessageBox.Icon.WARNING, "WARNING: Action cannot be undone", [sap.m.MessageBox.Action.OK, sap.m.MessageBox.Action.CANCEL]);
		promiseShowPopupAlert.then(function () {
						// Close the dialog view
						oController.dialogHandle.close();
						// Reset the data content
						oController.arrEntitiesData = [];
						let oDataModelJSON = oController.getDataModel();
						oDataModelJSON.setProperty("/smtMappingList", []);
		}, function() {});

	};
	
	EntityMappingController.enableDone = function (){
		// let oController = this.ParentController;
		let oSMTMappingModel = this.getDataModel();
			// Get the data for the row 
		let arrSelectedMapping = oSMTMappingModel.getProperty("/smtMappingList");
		let arrMappingStatusEmpty = arrSelectedMapping.filter(function(mappingRecord){
			return !mappingRecord.status;	 
		});
		if(arrMappingStatusEmpty.length){
			oSMTMappingModel.setProperty("/bEnableDone", false);
		}
		else
		{
			oSMTMappingModel.setProperty("/bEnableDone", true);	
		}
	};
	
	return Controller.extend("dmr.mdg.supernova.SupernovaFJ.controller.DataModel.EntityMapping", EntityMappingController);
});