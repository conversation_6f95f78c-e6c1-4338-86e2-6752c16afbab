sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController"
], function (BaseController) {
	"use strict";

	return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.DataModel.EntryDetails", {

		/**
		 * Called when a controller is instantiated and its View controls (if available) are already created.
		 * Can be used to modify the View before it is displayed, to bind event handlers and do other one-time initialization.
		 * @memberOf dmr.mdg.supernova.web.view.EntryDetails
		 */
		onInit: function () {
			let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
			oRouter.getRoute("EntryDetails").attachMatched(this._onObjectMatched, this);
			
			let oRadioGroup = this.getView().byId("EntryGroup");
			oRadioGroup.addButton(new sap.m.RadioButton({
				text: "Insert Entity"
			}));
			oRadioGroup.addButton(
				new sap.m.RadioButton({
					text: "Insert Attribute"
				}));
		},
		
		_onObjectMatched: function (oEvent) {
			this.getView().byId("path").setTitle(oEvent.getParameter("arguments").path);
		},

		/**
		 * Similar to onAfterRendering, but this hook is invoked before the controller's View is re-rendered
		 * (NOT before the first rendering! onInit() is used for that one!).
		 * @memberOf dmr.mdg.supernova.web.view.EntryDetails
		 */
		//	onBeforeRendering: function() {
		//
		//	},

		/**
		 * Called when the View has been rendered (so its HTML is part of the document). Post-rendering manipulations of the HTML could be done here.
		 * This hook is the same one that SAPUI5 controls get after being rendered.
		 * @memberOf dmr.mdg.supernova.web.view.EntryDetails
		 */
		//	onAfterRendering: function() {
		//	
		//	},
		
		onInsertItem: function() {
			let oItem = this.getView().byId("path").getTitle();
			let oModel = this.getView().byId("EntryGroup").getSelectedButton().getText().split(/\s+/)[1];
			let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
				oRouter.navTo("Entity", {
							model: oItem + "|" + oModel
						});
		},

		onExit: function () {

		}

	});

});