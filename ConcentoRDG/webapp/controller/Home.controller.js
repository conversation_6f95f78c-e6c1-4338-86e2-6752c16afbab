sap.ui.define([
	//"sap/ui/core/mvc/Controller",
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
	"sap/ui/core/Fragment",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
	"sap/m/MessageBox",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"sap/ui/core/Popup",
	"dmr/mdg/supernova/SupernovaFJ/model/models"
], function (BaseController, Fragment, DataService, MessageBox, Utilities, Popup, RDGModels) {
	"use strict";

	return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.Home", {

		oBundle: null,
		onInit: function () {
			this.getUsername();

			// Load the menu fragment
			Fragment.load({
				name: "dmr.mdg.supernova.SupernovaFJ.view.shared.MainMenu",
				controller: this
			}).then(function (oMenu) {
					this._menu = oMenu;
					this.getView().addDependent(this._menu);
			}.bind(this));

			// this.getCurrentSystem();                          
			let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
			// Load the parameters received into the model
			oRouter.attachRouteMatched(this, this.onRouteMatched, this);

			// various categories under with the main menu tiles can be categorized
			this.homeTileCategories = [
				{
					categoryId: 1,
					categoryName: this.geti18nText("homePage.category.dataModeling")
				},
				{
					categoryId: 2,
					categoryName: this.geti18nText("homePage.category.dataQuality")
				},
				{
					categoryId: 3,
					categoryName: this.geti18nText("homePage.category.processModeling")
				},
				{
					categoryId: 4,
					categoryName: this.geti18nText("homePage.category.dataReplication")
				},
				{
					categoryId: 5,
					categoryName: this.geti18nText("homePage.category.administrator")
				}
			];

			this._oFeatureDetails = [{
				title: this.geti18nText("dataModel.title"),
				description: this.geti18nText("dataModel.description"),
				target: "dataModel",
				enabled: true,
				icon: "overview-chart",
				category: this.homeTileCategories[0].categoryId,
				beta: false
			}, {
				title: this.geti18nText("workflow.title"),
				description: this.geti18nText("workFlow.description"),
				target: "Workflow",
				enabled: true,
				icon: "collaborate",
				category: this.homeTileCategories[2].categoryId,
				beta: false
			}, {
				title: this.geti18nText("businessRules.title"),
				description: this.geti18nText("businessRules.description"),
				target: "BusinessRules",
				enabled: true,
				icon: "validate",
				category: this.homeTileCategories[1].categoryId,
				beta: false
			}, {
				title: this.geti18nText("fieldProperties.title"),
				description: this.geti18nText("fieldProperties.description"),
				target: "UIProperties",
				enabled: true,
				icon: "filter-fields",
				category: this.homeTileCategories[1].categoryId,
				beta: false
			}, {
				title: this.geti18nText("interfaceConfiguration.title"),
				description: this.geti18nText("interfaceConfiguration.description"),
				target: "Interfaces",
				enabled: true,
				icon: "customize",
				category: this.homeTileCategories[3].categoryId,
				beta: false
			}, {
				//Task 12308 - Master Data Consolidation - Add Tile to Homescreen
				title: this.geti18nText("masterDataConsolidation.title"),
				description: this.geti18nText("masterDataConsolidation.description"),
				target: "MDConsolidation",
				enabled: true,
				icon: "combine",
				category: this.homeTileCategories[0].categoryId,
				beta: false
			}, {
				title: this.geti18nText("slaMonitoring.title"),
				description: this.geti18nText("slaMonitoring.description"),
				target: "Not Ready",
				enabled: false,
				icon: "customize",
				category: this.homeTileCategories[2].categoryId,
				beta: true
			}, {
				title: this.geti18nText("administratorSettings.title"),
				description: this.geti18nText("administratorSettings.description"),
				target: "admin",
				hideOnHomePage: true,
				enabled: false,
				icon: "wrench",
				category: this.homeTileCategories[0].categoryId,
				beta: false
			}, {
				title: this.geti18nText("emaiTemplates.title"),
				description: this.geti18nText("emailTemplates.description"),
				target: "EmailTemplates",
				enabled: true,
				icon: "arobase",
				category: this.homeTileCategories[2].categoryId,
				beta: false
			},
			{
				title: this.geti18nText("analyticalViews.title.short"),
				description: this.geti18nText("analyticalViews.title"),
				target: "MainAnalytics",
				enabled: true,
				icon: "area-chart",
				category: this.homeTileCategories[2].categoryId,
				beta: false
			}, {
				title: this.geti18nText("systemInformation.title"),
				description: this.geti18nText("systemInformation.description"),
				target: "System Information",
				hideOnHomePage: true,
				enabled: true,
				icon: "information",
				category: this.homeTileCategories[0].categoryId,
				beta: false
			}, {
				title: this.geti18nText("signOut.buttonTitle"),
				description: this.geti18nText("signOut.buttonDescription"),
				target: "logout",
				hideOnHomePage: true,
				enabled: true,
				icon: "log",
				category: this.homeTileCategories[0].categoryId,
				beta: false
			}];

			let oMenuModel = this.getView().getModel("mainMenuModel");
			if (!oMenuModel) {
				oMenuModel = new sap.ui.model.json.JSONModel();
				this.getView().setModel(oMenuModel, "mainMenuModel");
			}

			// Distribute the menu items into sections based on the categories assigned
			let arrMenuCategories = [];
			this._oFeatureDetails.forEach((element) => {
				if(element.enabled === false) return;
				let oCategory = this.homeTileCategories.find((category) => category.categoryId === element.category);
				let index = this.homeTileCategories.indexOf(oCategory);
				if(!oCategory){
					oCategory = this.homeTileCategories[0]; // default to the first one
				}
				if(arrMenuCategories[index] === undefined){
					arrMenuCategories[index] = {
						categoryName: oCategory.categoryName,
						items: []
					};
				}

				arrMenuCategories[index].items.push(element);
			});
			oMenuModel.setProperty("/items", arrMenuCategories);
			oMenuModel.setProperty("/menuItems", this._oFeatureDetails);
			
			// Bug 13557 - Create a new tag within manifest.json to hold the application version
			oMenuModel.setProperty("/appVersion", this.getOwnerComponent().getManifestEntry("/sap.ui5/config/version"));
			
			// Do version check to disable any unsupported features for this current server
			this.disableUnsupportedFeature(this);
		},

		disableUnsupportedFeature: function(oController) {
			let oCurrentSystemPromise = oController.getCurrentSystem();
			oCurrentSystemPromise.then(function(oSystem) {
				// if the current system is S4H and version No is less than 2022, disable Process Analytics
				if(oSystem.IS_S4H !== "S4H" || Number(oSystem.VersionNo) >= 2022){
					return; // Nothing to be done.
				}

				let oMenuModel = oController.getView().getModel("mainMenuModel");
				let arrMenuCategories = oMenuModel.getProperty("/items");
				let arrFeatureList  = oMenuModel.getProperty("/menuItems");

				arrMenuCategories.forEach(function(category, categoryIndex){
					let items = category.items;
					items.forEach(function(item, itemIndex){
						if(item.target === "MainAnalytics"){
							arrMenuCategories[categoryIndex].items[itemIndex].enabled = false;
						}
					});
				});
				arrFeatureList.forEach(function(menu, index){
					if(menu.target === "MainAnalytics"){
						arrFeatureList[index].enabled = false;
					}
				});

				oMenuModel.setProperty("/items", arrMenuCategories);
				oMenuModel.setProperty("/menuItems", arrFeatureList);
			});
		},
		
		onRouteMatched: function () {
			let bHome = false;
			if (window.location.hash.includes("RouteHome")) {
				bHome = true;
			}

			let oHomeModel = this.getView().getModel("mainMenuModel");
			if (oHomeModel) {
				oHomeModel.setProperty("/isHomePage", bHome);
			}
		},

		onHomeButtonPress: function () {
			let isOnHomePage = window.location.hash.includes("#RouteHome");
			let loggedOutModel = this.getView().getModel("loggedOutModel");
			if(loggedOutModel){
				let isLoggedOut = loggedOutModel.getProperty("/loggedOut");
				if (isLoggedOut) {
					isOnHomePage = true;
				}
			}
			let promise = new Promise(function (resolve, reject) {
				if (!isOnHomePage) {
					let promiseShowPopupAlert = Utilities.showPopupAlert("Exiting this page may cause loss of data. Do you want to continue?",
						MessageBox.Icon.ERROR, "Leave Page?", [MessageBox.Action.OK, MessageBox.Action.CANCEL]);
					promiseShowPopupAlert.then(function () {
						resolve();
					}, function () {
						reject();
					});
				} else {
					resolve();
				}
			});
			promise.then(function () {
				if (!isOnHomePage) {
					window.location.hash = "RouteHome";
					window.location.reload();
				}
			});

		},
		onMenuOpenPress: function (oEvent) {
			let oButton = oEvent.getSource();

			if(this._menu){
				this._menu.openBy(oButton, false);
			}
		},
		onMenuItemPressed: function (oEvent) {
			// Identify the source of the event  [Menu or Tile]
			let sSourceElement = oEvent.getSource().getMetadata().getName();
			let sTargetScreen = null;
			let sFeatureName = null;
			switch (sSourceElement) {
			case "sap.m.MenuItem":
				sTargetScreen = oEvent.getSource().getProperty("key");
				sFeatureName = oEvent.getSource().getText();
				break;
			case "sap.m.GenericTile":
				sFeatureName = oEvent.getSource().getHeader();
				sFeatureName = sFeatureName.split("[Beta]")[0].trim();
				// Search for the header in the _featureDetails
				let oFeatureDetails = this._oFeatureDetails.find(function (featureDetail) {
					if (featureDetail.title === sFeatureName) {
						return true;
					}
					return false;
				});
				sTargetScreen = oFeatureDetails.target;
				break;
			default:
				return; // Do nothing
			}

			let oRDGGlobalModel = RDGModels.getRDGGlobalModel();
			let oController = this;
			let sUsername;
			this.getUsername()
				.then(function (oSapUserInfo) {
					sUsername = oSapUserInfo.Sapname;
					let oMenuModel = oController.getView().getModel("mainMenuModel");
					oMenuModel.setProperty("/sysysid", oRDGGlobalModel.getProperty("/systemInfo/sysysid"));
					oMenuModel.setProperty("/symandt", oRDGGlobalModel.getProperty("/systemInfo/symandt"));
					oMenuModel.setProperty("/syuname", oRDGGlobalModel.getProperty("/systemInfo/syuname"));
					if (!sUsername) {
						/**
						 * 17-02-2022
						 * Error message and route to Home already taken care of in models.js. 
						 * Do nothing if no username is assigned
						 */
						return;
					} else
					if (sTargetScreen === "Not Ready") {
						Utilities.showPopupAlert("Development of " + sFeatureName + " is in the pipeline.", MessageBox.Icon.INFORMATION,
							"Not Available");
					} 	if(sTargetScreen === "System Information"){
							if (!oController.systemInfoDialogHandle) {
								oController.systemInfoDialogHandle =
									sap.ui.xmlfragment(
										"dmr.mdg.supernova.SupernovaFJ.view.shared.SystemInformation",
										oController);
								oController.getView().addDependent(oController.systemInfoDialogHandle);
							}
						// Show Dialog 
						oController.systemInfoDialogHandle.open();
					}else {
						// Navigate to the target Location
						oController.getRouter().navTo(sTargetScreen);
						let oUserInfo = oRDGGlobalModel.getProperty("/userInfo");
						oUserInfo.name = sUsername;
						//oRDGGlobalModel.setProperty("/userInfo", oUserInfo);
					}

				});
		},
		
		closeSystemInfoDialog: function(){
			if (this.systemInfoDialogHandle) {
				this.systemInfoDialogHandle.close();
			}
		}
	});
});