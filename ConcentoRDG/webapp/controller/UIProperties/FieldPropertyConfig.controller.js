sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
	"sap/m/library",
	"sap/ui/model/json/JSONModel",
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"sap/ui/core/Item",
	"dmr/mdg/supernova/SupernovaFJ/model/ModelMessages",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"sap/m/MessageBox",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService"
], function (BaseController, MobileLibrary, JSONModel, GetListsData, SelectItem, ModelMessages, Utilities, MessageBox, DMRDataService ) {

	return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.UIProperties.FieldPropertyConfig", {
		onInit: function () {

			let oView = this.getView();

			// Initialize the model 
			let oFieldPropertyConfigModel = oView.getModel("FieldPropertyConfigModel");
			if(!oFieldPropertyConfigModel)
			{
				oView.setModel(new JSONModel(), "FieldPropertyConfigModel");
				oFieldPropertyConfigModel = oView.getModel("FieldPropertyConfigModel");
			}
			let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
			oRouter.getRoute("UIProperties").attachPatternMatched(this, this._onRouteMatched);

			this.ModelMessages = ModelMessages;
			
		},
		
		_onRouteMatched: async function (oEvent, oController) {
			let oIconTabBar = oController.getView().byId("iconTabBar");
			oIconTabBar.destroyItems();
			
			// Store the params to the model 
			let oFieldPropertyConfigModel = oController.getView().getModel("FieldPropertyConfigModel");
			oFieldPropertyConfigModel.setProperty("/AttributesList", null);
			oFieldPropertyConfigModel.setProperty("/selectedEntity", null);
			let oFieldPropertiesModel = oController.getView().getModel("fieldPropertiesModel");
			let sCRType = oFieldPropertiesModel.getProperty("/selectedCRType");
			let sStepype = oFieldPropertiesModel.getProperty("/selectedStep");
			if(sCRType && sStepype){
				let promiseCRList = GetListsData.getConfiguredEntities(oController, sCRType, sStepype);
				promiseCRList.then(function(arrEntitiesList){
					oFieldPropertiesModel.setProperty("/HCIsEditable", arrEntitiesList[0].hciseditable === "X");
					oFieldPropertiesModel.setProperty("/highlightChanges", arrEntitiesList[0] ? arrEntitiesList[0].highlightchanges : false);
					let arrEntities = [];
					for(let i = 0; i < arrEntitiesList.length; i++){
						if (arrEntitiesList[i].usmdentity) {
							arrEntities.push({title: arrEntitiesList[i].usmdentity, description: arrEntitiesList[i].text});
						}
					}
					oController.createIconTabFilters(arrEntities);
	
				});
			}else{
				oController.byId("idUnidentifiedEntitiesBox").setVisible(true);
			}
		},

		onClickSelectEntities: function(){
			let oController = this;
			let oView = this.getView();
			let oIconTabBarItems = oView.byId("iconTabBar").getItems();
			let oFieldPropertiesModel = oView.getModel("fieldPropertiesModel");
			let sCRType = oFieldPropertiesModel.getProperty("/selectedCRType");
			let sStepype = oFieldPropertiesModel.getProperty("/selectedStep");
			let oFieldPropertyConfigModel = oController.getView().getModel("FieldPropertyConfigModel");
			if (!oController.SelectEntitiesDialog) {
				oController.SelectEntitiesDialog =
					sap.ui.xmlfragment(
						"SelectEntities",
						"dmr.mdg.supernova.SupernovaFJ.view.UIProperties.SelectEntities",
						oController);
				oController.getView().addDependent(oController.SelectEntitiesDialog);
			}
			/* Bug 11501 - Methods "setValue" and "fireLiveChange" were added to clean the filter when de dialog is opened */
			sap.ui.getCore().byId("SelectEntities--selEntity").setValue();
			sap.ui.getCore().byId("SelectEntities--selEntity").fireLiveChange();
			// Bug 12827 - Sending parameter to removeSelections method to clear all the selections even if rememberSelections property is set to true
			sap.ui.getCore().byId("SelectEntities--EntitiesList").removeSelections(true);
			if(sCRType && sStepype){
				sap.ui.getCore().byId("SelectEntities--dialogSelectEntities").setBusy(true);
				let promiseCRList = GetListsData.getEntityListByStep(this, sCRType, "");
				promiseCRList.then(function(arrEntitiesList){
					for(let i = 0; i < oIconTabBarItems.length; i++){
						arrEntitiesList = arrEntitiesList.filter(item => item.UsmdEntity !== oIconTabBarItems[i].getKey());
					}
					oFieldPropertyConfigModel.setSizeLimit(arrEntitiesList.length);
					oFieldPropertyConfigModel.setProperty("/entByStepList", arrEntitiesList);
					sap.ui.getCore().byId("SelectEntities--dialogSelectEntities").setBusy(false);
	
				});
			}else{
				oFieldPropertyConfigModel.setProperty("/entByStepList", []);
			}
			
			// Open the dialog
			oController.SelectEntitiesDialog.open();
		},

		onSelectEntitiesSave: function(oEvent){
			let sEntitiesList = oEvent.getSource().getParent().getContent()[0].getSelectedItems();
			let arrEntities = [];
			for(let i = 0; i < sEntitiesList.length; i++){
				arrEntities.push({title: sEntitiesList[i].getTitle(), description: sEntitiesList[i].getDescription()});
			}
			if(arrEntities.length > 0){
				this.createIconTabFilters(arrEntities);
			}
			this.SelectEntitiesDialog.close();
		},

		onAttributeChange: function() {
			// Task 12186 - UI Property by CR Step Change Entity Tab
			if (this.areThereChanges()) {
				let oFieldPropertyConfigModel = this.getView().getModel("FieldPropertyConfigModel");
				let sSelectedEntity = oFieldPropertyConfigModel.getProperty("/selectedEntity");
				let oIconTabBar = this.byId("iconTabBar");
				let arrEntities = oIconTabBar.getItems();
				for (let oEntity of arrEntities) {
					if (oEntity.getProperty("key") === sSelectedEntity.colvalue.name) {
						oEntity.setProperty("enabled", true);
					} else {
						oEntity.setProperty("enabled", false);
					}
				}
			}
		},

		createIconTabFilters: function(entitiesList){
			let oIconTabBar = this.byId("iconTabBar");
			let oUnidentifiedF = this.byId("idUnidentifiedEntitiesBox");
			if(entitiesList.length > 0){
				oIconTabBar.setVisible(true);
				oUnidentifiedF.setVisible(false);
				let oItemTemplate = new MobileLibrary.InputListItem({
					label: "{FieldPropertyConfigModel>colvalue/name} {FieldPropertyConfigModel>Text}",
					content: new MobileLibrary.Select({
						selectedKey: "{FieldPropertyConfigModel>colvalue/config}",
						editable: "{= ${FieldPropertyConfigModel>/selectedEntity/colvalue/config} !== 'R'}",
						// Task 12186 - UI Property by CR Step Change Entity Tab
						change: () => this.onAttributeChange(),
						items: [
							new SelectItem({
								key: "S",
								text: "S - Standard"
							}),
							new SelectItem({
								key: "NRC",
								text: "NRC - No required field check"
							}),
							new SelectItem({
								key: "R",
								text: "R - Not relevant(Read only)"
							}),
							new SelectItem({
								key: "H",
								text: "H - Hide"
							}),
							new SelectItem({
								key: "M",
								text: "M - Mandatory"
							})
						]
					})
				}); 
	
				for(let i = 0; i < entitiesList.length; i++){
					oIconTabBar.addItem(new MobileLibrary.IconTabFilter({
						key: entitiesList[i].title,
						text: entitiesList[i].title,
						content: [
							new MobileLibrary.Toolbar({
								content: [
									new MobileLibrary.ToolbarSpacer({}),
									new MobileLibrary.Button({
										id: "resetButton_" + entitiesList[i].title,
										icon: "sap-icon://reset",
										text: "Reset",
										press: () => this.onResetConfigurations()
									}),
									new MobileLibrary.Button({
										id: "saveButton_" + entitiesList[i].title,
										icon: "sap-icon://save",
										text: "Save",
										press: () => this.onSaveConfigurations()
									})
								]
							}).addStyleClass("sapUiMediumMarginBottom"),
							new MobileLibrary.Toolbar({
								content: new MobileLibrary.List({
									items: [new MobileLibrary.InputListItem({
										label: "{FieldPropertyConfigModel>/selectedEntity/colvalue/name} {FieldPropertyConfigModel>/selectedEntity/Text}",
										content: new MobileLibrary.Select({
											selectedKey: "{FieldPropertyConfigModel>/selectedEntity/colvalue/config}",
											// Task 12186 - UI Property by CR Step Change Entity Tab
											change: () => this.onAttributeChange(),
											items: [
												new SelectItem({
													key: "S",
													text: "S - Standard"
												}),
												new SelectItem({
													key: "NRC",
													text: "NRC - No required field check"
												}),
												new SelectItem({
													key: "R",
													text: "R - Not relevant"
												})
											]
										})
									})]
								})
							}).addStyleClass("toolbarBackground sapUiSmallMarginBottom"),
							new MobileLibrary.List({
								headerText: "Attributes"
							}).bindItems({
								path: "/AttributesList",
								model: "FieldPropertyConfigModel",
								template: oItemTemplate,
								templateShareable: false
							})
					]
					}));
				}
				setTimeout(() => {
					oIconTabBar.fireSelect();
				}, 100);
			}else{
				oIconTabBar.setVisible(false);
				oUnidentifiedF.setVisible(true);
			}
		},

		onSelectEntityTab: function(oEvent){
			let oView = this.getView();
			let oController = this;
			let oIconTabBar = oEvent.getSource();
			let sEntity = oEvent.getSource().getSelectedKey();
			let oFieldPropertyConfigModel = oView.getModel("FieldPropertyConfigModel");
			let oCurrentEntity = oFieldPropertyConfigModel.getProperty("/selectedEntity");
			let flagChanges = oController.areThereChanges();
			let promiseSelEntity = new Promise(function(resolve, reject){
				if(oCurrentEntity){
					if(oCurrentEntity.colvalue.name !== sEntity){
						if (flagChanges) {
							let promiseShowPopupAlert = Utilities.showPopupAlert("Changes not saved. Do you want to continue?", 
								MessageBox.Icon.ERROR, "Change Entity?", [MessageBox.Action.OK, MessageBox.Action.CANCEL]);
								promiseShowPopupAlert.then(function (){
									resolve();
								}, function() {		
									reject();
								});
						} else {
							resolve();
						}
					}else{
						reject();
					}
				}else{
					resolve();
				}
			});
			promiseSelEntity.then(function(){
				oView.setBusy(true);
				let oFieldPropertiesModel = oView.getModel("fieldPropertiesModel");
				let sCRType = oFieldPropertiesModel.getProperty("/selectedCRType");
				let sStepype = oFieldPropertiesModel.getProperty("/selectedStep");
				let promiseCRList = GetListsData.getAttributesListByStep(oController, sCRType, sStepype, sEntity );
				promiseCRList.then(function(arrAttributesList){
					// Task 12413 - Filter rules, field properties if any rules are created for the crtype( UI5 Only task )
					let arrResults = [];
					if (arrAttributesList && arrAttributesList.length && arrAttributesList[0].hasOwnProperty("modeltocrfldprop")) {
						arrResults = arrAttributesList[0].modeltocrfldprop.results;
					}
					let arrAttributes = arrResults.map(item => ({colvalue: {name: item.colvalue.replace("{", "").split(",")[0], config: item.colvalue.replace("}", "").split(",")[1]}, isentity: item.isentity, Text: (item.Text !== "") ? "("+item.Text+")" : ""}));
					let oEntity = arrAttributes.find(item => item.isentity === "X");
					arrAttributes = arrAttributes.filter(item => item.isentity !== "X");
					oFieldPropertyConfigModel.setSizeLimit(arrAttributes.length);
					oController.oldAttributesList = arrResults;
					oFieldPropertyConfigModel.setProperty("/AttributesList", arrAttributes);
					oFieldPropertyConfigModel.setProperty("/selectedEntity", oEntity);
					oFieldPropertyConfigModel.setProperty("/selectedTransportPackage", {
						customizingTransport: arrAttributesList[0].CustTransport,
						workbenchTransport: arrAttributesList[0].WbTransport, 
						package: arrAttributesList[0].usmddevclass
					});
					oView.setBusy(false);
				});
			}).catch(function(){
				oIconTabBar.setSelectedKey(oFieldPropertyConfigModel.getProperty("/selectedEntity").colvalue.name);
			});
		},

		areThereChanges: function(){
			let oView = this.getView();
			let oFieldPropertyConfigModel = oView.getModel("FieldPropertyConfigModel");
			let arrAttributes = oFieldPropertyConfigModel.getProperty("/AttributesList");
			let sEntity = oFieldPropertyConfigModel.getProperty("/selectedEntity");
			if(!arrAttributes || !sEntity){
				return false;
			}
			arrAttributes.push(sEntity);
			let arrCurrAttributes = arrAttributes.map(item => (
				{colvalue: {name: item.colvalue.name, config: item.colvalue.config}}
			));
			let compArrAttr = this.oldAttributesList.map(item => (
				{colvalue: {name: item.colvalue.replace("{", "").split(",")[0], config: item.colvalue.replace("}", "").split(",")[1]}}
			));
			if(JSON.stringify(compArrAttr) === JSON.stringify(arrCurrAttributes)){
				arrAttributes.pop();
				return false;
			}else{
				arrAttributes.pop();
				return true;
			}
		},

		onSelectEntitiesCancel: function(){
			this.SelectEntitiesDialog.close();
		},

		onResetConfigurations: function(){
			let oController = this;
			let oView = this.getView();
			let promiseReset = new Promise(function(resolve, reject){
				let promiseShowPopupAlert = Utilities.showPopupAlert("All configurations will be assigned as standard.", 
					MessageBox.Icon.ERROR, "Do you want to reset?", [MessageBox.Action.OK, MessageBox.Action.CANCEL]);
					promiseShowPopupAlert.then(function (){
						resolve();
				}, function() {
					reject();
				});
			});
			promiseReset.then(function(){
				let oFieldPropertyConfigModel = oView.getModel("FieldPropertyConfigModel");
				let arrAttributes = oFieldPropertyConfigModel.getProperty("/AttributesList");
				oFieldPropertyConfigModel.setProperty("/selectedEntity/colvalue/config", "S");
				for(let i = 0; i < arrAttributes.length; i++){
					oFieldPropertyConfigModel.setProperty("/AttributesList/" + i + "/colvalue/config", "S");
				}

				// Task 12186 - UI Property by CR Step Change Entity Tab
				let oIconTabBar = oController.byId("iconTabBar");
				let arrEntities = oIconTabBar.getItems();
				for (let oEntity of arrEntities) {
					oEntity.setProperty("enabled", true);
				}
			}).catch(function(){});
		},

		onSaveConfigurations: function(){
			let oController = this;
			let oView = this.getView();
			let oFieldPropertyConfigModel = oView.getModel("FieldPropertyConfigModel");
			let arrAttributes = oFieldPropertyConfigModel.getProperty("/AttributesList");
			let sEntity = oFieldPropertyConfigModel.getProperty("/selectedEntity");
			let oFieldPropertiesModel = oView.getModel("fieldPropertiesModel");
			let sCRType = oFieldPropertiesModel.getProperty("/selectedCRType");
			let sModelName = oFieldPropertiesModel.getProperty("/selectedModel");
			let sStepype = oFieldPropertiesModel.getProperty("/selectedStep");
			let oStepsSearchList = sap.ui.getCore().byId("stepsSearchListComponent").getComponentInstance();
			let sCountType = "UI";

			let oDataToWrite = {
					crstep: sStepype,
					delete: "",
					usmdmodel: sModelName,
					usmdcreqtype: sCRType,
					usmddevclass: "",
					usmdentity: sEntity.colvalue.name,
					appname: "",
					apptype: "",
					CustTransport:"",
					WbTransport:"",
					modeltocrfldprop: [],
					modeltomessage: [] 
				};

				let oBADIcall = arrAttributes.some(item => {
					return item?.colvalue?.config === "M" || item?.colvalue?.config === "H";
				}) ? "X" : "";

			arrAttributes = arrAttributes.map(item => ({colvalue: "{" + item.colvalue.name + "," + item.colvalue.config + "}", usmdmodel: ""}));
			sEntity = { colvalue: "{" + sEntity.colvalue.name + "," + sEntity.colvalue.config + "}", usmdmodel: "", isentity: "X"};
			arrAttributes.push(sEntity);
			oDataToWrite.modeltocrfldprop = arrAttributes;

			let oDataModel = oController.getModel("CREATE_MULTI_UI_PROPERTY");
			let oWriteParameterObject = {
				success: function (oDataResult) {

					//Task 12508 Add count type as filter criteria
					let oPromiseWFStepList = 
						GetListsData.getWorkflowStepList(
							oController, 
							sCRType,
                            sCountType
						);
						
					oPromiseWFStepList.then(function(arrWFStepList){
						arrWFStepList.forEach(step => {
							if(step.UsmdCreqStep.length < 2){
								step.UsmdCreqStep = "0" + step.UsmdCreqStep;
							}

							// Task 12413 - Filter rules, field properties if any rules are created for the crtype( UI5 Only task )
							step.Uicounter = parseInt(step.Uicounter);
						});
						
						// Bug 12543 - Filter icon isn't refreshed on refresh of entity screen
						if (oStepsSearchList.getTooltip() === "Clear Filter") {
							if (arrWFStepList && arrWFStepList.length) {
								arrWFStepList = arrWFStepList.filter(oEntity => {
									return oEntity.Uicounter > 0;
								});
							}
						}
						// End Bug 12543 - Filter icon isn't refreshed on refresh of entity screen

						oStepsSearchList.setListData(arrWFStepList);
					});
					let arrMessages = [];
					jQuery.extend(true, arrMessages, oDataResult.modeltomessage.results);
					// Sort the messages
					arrMessages.sort(function (m1) {
						if (m1.MessageType === "E") {
							// eslint-disable-next-line no-undef
							sSaveError = "Yes";
							return -1;
						}
						if (m1.MessageType === "W") {
							return 1;
						}
						return 0;
					});

					sap.ui.core.BusyIndicator.hide();
					oController.ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView())
					.then(function(){
						oController.oldAttributesList = arrAttributes;
					
						// Task 12186 - UI Property by CR Step Change Entity Tab
						let oIconTabBar = oController.byId("iconTabBar");
						let arrEntities = oIconTabBar.getItems();
						for (let oEntity of arrEntities) {
							oEntity.setProperty("enabled", true);
						}	
					});
				},
				error: function () {
					sap.ui.core.BusyIndicator.hide();
					Utilities.showPopupAlert("Configuration could not be saved/updated.", MessageBox.Icon.INFORMATION, "Configurations")
					.then(function () {});
				}
			}; 

			arrAttributes.RuleType = "1";

			let oPromise;

			if(oBADIcall === "X"){
				oPromise = oController.getSelectedBADIDialog(oController, arrAttributes);
			} else {
				oPromise = Promise.resolve(undefined);
			}

			oPromise.then(function(oSelectedBAdI){
				if(oSelectedBAdI){
					// Add BAdI-related data
					Object.assign(oDataToWrite, {
						badiimpl: oSelectedBAdI.BADISelected,
						newbadi: oSelectedBAdI.isNewBADI
					});
				}
				// Select the transport and package
				return oController._getSelectedTransportPackage(true, true, true);
			})
			.then(oResponseSelection => {
				// Populate oDataToWrite
				Object.assign(oDataToWrite, {
					usmddevclass: oResponseSelection.package,
					CustTransport: oResponseSelection.customizingTransport,
					WbTransport: oResponseSelection.workbenchTransport
				});
			
				// Trigger BusyIndicator and make the create call
				sap.ui.core.BusyIndicator.show(300);
				oDataModel.setHeaders({ "Application-Interface-Key": "l6z1vjte" });
				oDataModel.create("/modelSet", oDataToWrite, oWriteParameterObject);
			});
		},

		onChangeHighlightChanges: function (oEvent) {
			let oSwitch = oEvent.getSource();
			let bState = oSwitch.getState();
			let oController = this;
			let oFieldPropertiesModel = this.getView().getModel("fieldPropertiesModel");
			let sCRType = oFieldPropertiesModel.getProperty("/selectedCRType");
			let sStepype = oFieldPropertiesModel.getProperty("/selectedStep");
			let bHighlightChanges = oFieldPropertiesModel.getProperty("/highlightChanges");

			let oHighlightChangesData = {
				crstep: sStepype,
				usmdcreqtype: sCRType,
				highlightchanges: bHighlightChanges   
			};

			let oDataModel = oController.getModel("CREATE_MULTI_UI_PROPERTY");
			let oWriteParameterObject = {
				success: function (oDataResult) {
					let arrMessages = [];
					if (oDataResult.Message) {
						arrMessages.push({
							Message: oDataResult.Message,
							MessageType: oDataResult.MessageType
						});
						
						oController.ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
					}
					//Bug 12932 - Added condition to set the state back as before if some error happens
					if (oDataResult.MessageType !== "S") {
						oSwitch.setState(!bState);
					}
					sap.ui.core.BusyIndicator.hide();
				},
				error: function () {
					Utilities.showPopupAlert("Highlight Changes could not be saved/updated.", MessageBox.Icon.INFORMATION, "Highlight Changes")
					.then(function () {});
					//Bug 12932 - Setting the state back as before if some error happens
					oSwitch.setState(!bState);
				}
			}; 

			this._getSelectedTransportPackage(true, false, false)
			.then(function(oResponseSelection){
				oHighlightChangesData.CustTransport = oResponseSelection.customizingTransport;
					
				sap.ui.core.BusyIndicator.show(300);
				oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
				oDataModel.create("/crstepentitySet", oHighlightChangesData, oWriteParameterObject);
			})
			.catch(function(){
				//Bug 12932 - Added condition to set the state back as before if user cancels
				oSwitch.setState(!bState);
			});
		},

		onTransportPackageSelectionDialogCreated: function(oEvent){
			let comp = oEvent.getParameter("component");
			// store the component handle 
			this._transportPackageDialog = comp;
			
			this.getUsername()
			.then(function (oSapUserInfo) {
				let sUsername = oSapUserInfo.Sapname;
				if (!sUsername) {
					Utilities.showPopupAlert("Please login to continue", MessageBox.Icon.ERROR, "Logged out");
				} else {
					comp.setUser(sUsername);
				}
			});
		},

		onBADISelectionDialogCreated : function (oEvent) {
			let comp = oEvent.getParameter("component");
			// store the component handle 
			this._BADISelectionDialog = comp;
	
			this._BADISelectionDialog.attachBADISelected(this.onBADISelected, this);
		},
	
	onBADISelected : function (oBADISelected) {		
			let sSelectedBADI = oBADISelected.getParameters();
	
			this._BADISelectionDialogPromiseResolveFunction(sSelectedBADI);
		},
	
		getSelectedBADI : function () {
	
			let oComponent = this;
	
			if (this._BADISelectionDialogPromiseRejectFunction) {
				this._BADISelectionDialogPromiseResolveFunction = undefined;
				this._BADISelectionDialogPromiseRejectFunction({});
				this._BADISelectionDialogPromiseRejectFunction = undefined;
			}
	
			this._BADISelectionDialogPromise = new Promise(function (resolve, reject) {
				oComponent._BADISelectionDialogPromiseResolveFunction = resolve;
				oComponent._BADISelectionDialogPromiseRejectFunction = reject;
				oComponent._BADISelectionDialog.open();
			});
	
			return this._BADISelectionDialogPromise;
		},
	
		getSelectedBADIDialog : function (oController, oRulePathInfo) {
			let BADIDialog = oController.byId("BADISelectDialog").getComponentInstance();
			BADIDialog.setRuleDetails({
				ruleType: oRulePathInfo.RuleType,
				usmdModel: oRulePathInfo.DataModel,
				usmdEntity: oRulePathInfo.EntityName,
				featureType: "2",
				crossEntity: ""
			});
			return oController.getSelectedBADI();
	
		},

		_getSelectedTransportPackage: function(bCustomizing, bWorkbench, bPackage){
			let oModel = this.getView().getModel("FieldPropertyConfigModel");
			let oSelectedTransportPackage = oModel.getProperty("/selectedTransportPackage");
			if(!oSelectedTransportPackage){
				oSelectedTransportPackage = {
					customizingTransport: undefined,
					workbenchTransport: undefined, 
					package: undefined
				};
			}

			let promise = this._transportPackageDialog.open(
				bCustomizing, oSelectedTransportPackage.customizingTransport, 
				bWorkbench, oSelectedTransportPackage.workbenchTransport, 
				bPackage, oSelectedTransportPackage.package);
			let returnPromise = promise.then(function(oResponseSelection){
				oSelectedTransportPackage.customizingTransport = oResponseSelection.customizingTransport;
				oSelectedTransportPackage.workbenchTransport = oResponseSelection.workbenchTransport;
				oSelectedTransportPackage.package = oResponseSelection.package;
				oModel.setProperty("/selectedTransportPackage", oSelectedTransportPackage);

				return oResponseSelection;
			});

			return returnPromise;
		},

		onSearchEntityList: function (oEvent) {

			// add filter for search
			let aFilters;
	
			// Get the search text
			let sQuery = oEvent.getSource().getValue();
	
			if (sQuery && sQuery.length > 0) {
				aFilters = new sap.ui.model.Filter({
					filters: [
						new sap.ui.model.Filter({
								path: "UsmdEntity",
								operator: sap.ui.model.FilterOperator.Contains,
								value1: sQuery,
								caseSensitive: false
							}),
						new sap.ui.model.Filter({
								path: "Txtlg",
								operator: sap.ui.model.FilterOperator.Contains,
								value1: sQuery,
								caseSensitive: false
							})
					],
					and: false
				});
			}
			
			// update list binding
			sap.ui.getCore()
				.byId("SelectEntities--EntitiesList")
				.getBinding("items")
				.filter(aFilters);
		},

		/**
		 * Task 10868 - UI Properties Copy by Step Type - UI5
		 * Triggered when the user clicks the Import button when rule type is By Step Type
		 */
		onImportByStepTypeClick: function() {
			let oController = this;
			let oView = oController.getView();
			let oFieldPropertiesModel = oView.getModel("fieldPropertiesModel");
			let sDataModel = oFieldPropertiesModel.getProperty("/selectedModel");
			
			oFieldPropertiesModel.setProperty("/importByStepType", {});
			// Bug 13573: added save enable property
			oFieldPropertiesModel.setProperty("/importByStepType/ebSaveImport", false);
			oFieldPropertiesModel.setProperty("/CreateNew", true);
			if (!oController.ImportByStepTypeDialog) {
				oController.ImportByStepTypeDialog =
					sap.ui.xmlfragment(
						"ImportRules",
						"dmr.mdg.supernova.SupernovaFJ.view.UIProperties.ImportByStepType",
						oController);
				oController.getView().addDependent(oController.ImportByStepTypeDialog);
			}
			
			let promiseCRList = GetListsData.getChangeRequestsList(this);
			promiseCRList.then(function(arrCrList){
				let arrFilteredCRList = arrCrList.results.filter((oChangeRequest) => {
					return oChangeRequest.UsmdModel === sDataModel;
				});
				/*Added the method setSizeLimit*/
				oFieldPropertiesModel.setSizeLimit(arrFilteredCRList.length);
				oFieldPropertiesModel.setProperty("/importByStepType/changeRequestList", arrFilteredCRList);
			});

			// Open the dialog
			oController.ImportByStepTypeDialog.open();
		},

		/**
		 * Task 10868 - UI Properties Copy by Step Type - UI5
		 * Define the mapping columns and the headers and groupings for the ComponentContainer list
		 */
		onImportByStepTypeListCreated: function(oEvent) {
			let oController = this;
			let comp = oEvent.getParameter("component");
			oController.ImportByStepTypeList = comp;
			
			oController.ImportByStepTypeList.setDataMapping({
				"title": "title",
				"description": "description",
				"info": "info"
			});
			
			oController.ImportByStepTypeList.setMode("MultiSelect");
			oController.ImportByStepTypeList.setRememberSelections(true);
	
			oController.ImportByStepTypeList.setHeaderText("Entities");
			oController.ImportByStepTypeList.setListGroupingPath("usmdentity");
		},

		/**
		 * Task 10868 - UI Properties Copy by Step Type - UI5
		 * Triggered when the user changes the "From Change Request Type" field inside the "Import by Step Type" dialog window
		 */
		onImportByStepTypeChangeCRType: function() {
			let oController = this;
			let oFieldPropertiesModel = oController.getView().getModel("fieldPropertiesModel");
			let sSelectedCRType = oFieldPropertiesModel.getProperty("/importByStepType/selectedCRType");
			let sCountType = "UI";
			
			oFieldPropertiesModel.setProperty("/importByStepType/stepTypesList", []);
			oFieldPropertiesModel.setProperty("/importByStepType/selectedStepType", "");
			oController.ImportByStepTypeList.removeMultiSelectedItems();
			oController.ImportByStepTypeList.setListData([]);
			
			//Task 12508 Filter CR Step type properties
			let promiseStepsList = GetListsData.getWorkflowStepList(oController, sSelectedCRType, sCountType);
			promiseStepsList.then(function(arrWFStepList) {
				arrWFStepList.forEach(step => {
					if (step.UsmdCreqStep.length < 2) {
						step.UsmdCreqStep = "0" + step.UsmdCreqStep;
					}
				});
				//Only remove if is for the same CR type
			    if(oFieldPropertiesModel.oData.importByStepType.selectedCRType === oFieldPropertiesModel.oData.selectedCRType){
					//Remove from selected steps
					arrWFStepList = arrWFStepList.filter((oChangeRequest) => {
						return oChangeRequest.UsmdCreqStep !== oFieldPropertiesModel.oData.selectedStep;
					});
				}
				oFieldPropertiesModel.setProperty("/importByStepType/stepTypesList", arrWFStepList);
			});
		},

		/**
		 * Task 10868 - UI Properties Copy by Step Type - UI5
		 * Triggered when the user changes the "From Step Type" field inside the "Import by Step Type" dialog window
		 */
		onImportByStepTypeChangeStepType: function() {
			let oController = this;
			let oFieldPropertiesModel = oController.getView().getModel("fieldPropertiesModel");
			let sSelectedCRType = oFieldPropertiesModel.getProperty("/importByStepType/selectedCRType");
			let sStepType = oFieldPropertiesModel.getProperty("/importByStepType/selectedStepType");
			
			oController.ImportByStepTypeList.removeMultiSelectedItems();

			let promiseEntitiesList = GetListsData.getConfiguredEntities(oController, sSelectedCRType, sStepType);
			
			/**
			 * Bug 13573: changed to a for loop to validate if the element has an entity 
			 * if so, then push the object to the arrEntities.
			 * then set the corresponding value to ebSaveImport to enable/disable the save button
			*/
			let arrEntities = [];
			promiseEntitiesList.then(function(arrEntitiesList){
				for (let i = 0; i < arrEntitiesList.length; i++) {
					const oEntity = arrEntitiesList[i];
					
					if(oEntity.usmdentity){
						arrEntities.push({
							title: oEntity.usmdentity,
							description: oEntity.text,
							info: oEntity.fldprop
						});
					}
					
				}
				if (arrEntities.length !== 0){
					oFieldPropertiesModel.setProperty("/importByStepType/ebSaveImport", true);
				} else {
					oFieldPropertiesModel.setProperty("/importByStepType/ebSaveImport", false);
				}
				oController.ImportByStepTypeList.setListData(arrEntities);
			});
		},

		/**
		 * Task 10868 - UI Properties Copy by Step Type - UI5
		 * Triggered when the user clicks the Save button inside the import by step type dialog window
		 */
		onImportByStepTypeSave: function() {
			let oController = this;
			let oFieldPropertiesModel = oController.getView().getModel("fieldPropertiesModel");
			let sSelectedModel = oFieldPropertiesModel.getProperty("/selectedModel");
			let sTargetCRType = oFieldPropertiesModel.getProperty("/selectedCRType");
			let sTargetStepType = oFieldPropertiesModel.getProperty("/selectedStep");
			let sSourceCRType = oFieldPropertiesModel.getProperty("/importByStepType/selectedCRType");
			let sSourceStepType = oFieldPropertiesModel.getProperty("/importByStepType/selectedStepType");
			let arrSelectedItems = oController.ImportByStepTypeList.getMultiSelectedItems();
			let promiseShowPopupAlert;

			if (!sSourceCRType || !sSourceStepType) {
				promiseShowPopupAlert = Utilities.showPopupAlert("Please select all mandatory fields", MessageBox.Icon.ERROR, "Missing mandatory details");
				promiseShowPopupAlert.then(function () {});
				return;
			}
			if (arrSelectedItems.length === 0) {
				promiseShowPopupAlert = Utilities.showPopupAlert("Please select one or more entities to be imported.", MessageBox.Icon.ERROR, "No records selected");
				promiseShowPopupAlert.then(function () {});
				return;
			}

			let oDataToWrite = {
				UsmdModel: sSelectedModel,
				SourceCr: sSourceCRType,
				TargetCr: sTargetCRType,
				Featuretype: "4",
				SourceCrstep: sSourceStepType,
				TargetCrstep: sTargetStepType,
				Package: undefined,
				NAV_CRTORULES: [],
				NAV_CRTOMSGDET: []
			};
			
			arrSelectedItems.forEach(oSelectedItem => {
				oDataToWrite.NAV_CRTORULES.push({
					UsmdModel: sSelectedModel,
					UsmdEntity: oSelectedItem.title
				});
			});

			// Bug 13573: added callbacks
			let oWriteParameterObject = {
				success:{
					fCallback: function (oThisObject, oDataResult) {
						let arrMessages = [];
						let sSaveError = "";
						
						jQuery.extend(true, arrMessages, oDataResult.NAV_CRTOMSGDET.results);
						
						// Sort the messages
						arrMessages.sort(function (m1) {
							if (m1.MessageType === "E") {
								sSaveError = "Yes";
								return -1;
							}
							else if (m1.MessageType === "W") { 
								return 1;
							} else {
								return 0;
							}
						});
						
						if (sSaveError === "") {
							oController.refreshEntities();
							oController.ImportByStepTypeList.removeMultiSelectedItems();
							oController.ImportByStepTypeList.setListData([]);
							oController.ImportByStepTypeDialog.close();
						}
						
						sap.ui.core.BusyIndicator.hide();
						ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
					},
					oParam:oController
				},
				error: {
					fCallback: function () {
						sap.ui.core.BusyIndicator.hide();
						promiseShowPopupAlert = Utilities.showPopupAlert("Selected entities could not be imported", MessageBox.Icon.INFORMATION, "Import Entities Failed");
						promiseShowPopupAlert.then(function () {});	
					}
				}
			};

			oController._getSelectedTransportPackage(true, true, true)
			.then(function(oResponseSelection){
				oDataToWrite.CustTransport = oResponseSelection.customizingTransport;
				oDataToWrite.WbTransport = oResponseSelection.workbenchTransport;
				oDataToWrite.Package = oResponseSelection.package;

				(new DMRDataService(
					oController,
					"COPY_BUSINESS_RULES",
					"/CRTYPESSet",
					undefined,
					"/", // Root of the received data
					""
				))
				.saveData(false, oDataToWrite, null, oWriteParameterObject);
			});
		},

		/**
		 * Task 10868 - UI Properties Copy by Step Type - UI5
		 * Triggered when the user clicks the Cancel button inside the import by step type dialog window
		 */
		onImportByStepTypeCancel: function() {
			let oController = this;
			oController.ImportByStepTypeList.removeMultiSelectedItems();
			oController.ImportByStepTypeList.setListData([]);
			oController.ImportByStepTypeDialog.close();
		},

		/*
		 * Task 10868 - UI Properties Copy by Step Type - UI5
		 * After importing new entities, this function gets triggered
		 * Gets the updated entities list for the selected CR and Step Type
		 * And refreshes the parent screen with the updated entities as tabs
		 */
		refreshEntities: function() {
			let oController = this;
			let oFieldPropertiesModel = oController.getView().getModel("fieldPropertiesModel");
			let sSelectedCRType = oFieldPropertiesModel.getProperty("/selectedCRType");
			let sStepType = oFieldPropertiesModel.getProperty("/selectedStep");
			let oStepsSearchList = sap.ui.getCore().byId("stepsSearchListComponent").getComponentInstance();
			let sCountType = "UI";
			
			// Refresh the step counters
			//Task 12508 Filter CR Step type properties
			let oPromiseWFStepList = GetListsData.getWorkflowStepList(oController, sSelectedCRType, sCountType);
			oPromiseWFStepList.then(function(arrWFStepList) {
				arrWFStepList.forEach(step => {
					if (step.UsmdCreqStep.length < 2) {
						step.UsmdCreqStep = "0" + step.UsmdCreqStep;
					}
				});
				oStepsSearchList.setListData(arrWFStepList);
			});

			// Refresh the entities tabs
			let oIconTabBar = oController.getView().byId("iconTabBar");
			oIconTabBar.destroyItems();

			if (sSelectedCRType && sStepType) {
				let promiseEntitiesList = GetListsData.getConfiguredEntities(oController, sSelectedCRType, sStepType);
				promiseEntitiesList.then(function(arrEntitiesList){
					let arrEntities = [];
					for(let i = 0; i < arrEntitiesList.length; i++){
						arrEntities.push({title: arrEntitiesList[i].usmdentity, description: arrEntitiesList[i].text});
					}
					
					oController.createIconTabFilters(arrEntities);
				});
			} else {
				oController.byId("idUnidentifiedEntitiesBox").setVisible(true);
			}
		}

	});
});