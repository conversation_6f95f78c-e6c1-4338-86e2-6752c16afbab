// Task 10260 - Provide a better User interface for the SIngle value derivation (Check Expression)
sap.ui.define([
    
], function () {
	"use strict";
    
    let oUserDefinedExpressionModel = {};
	
    oUserDefinedExpressionModel.onClickNewAttribute = function() {
        let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
        let sUserDefinedExpression = oFieldPropertiesDetailsModel.getProperty("/singleValueUIDetails/userDefinedExpression");
        let nHowManyTimes = sUserDefinedExpression.replace(/[^<]/g, "").length;
        oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpression", sUserDefinedExpression + "<" + (nHowManyTimes + 1) + ">");
    };

    oUserDefinedExpressionModel.onClickOpenParenthesis = function() {
        let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
        let sUserDefinedExpression = oFieldPropertiesDetailsModel.getProperty("/singleValueUIDetails/userDefinedExpression");
        oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpression", sUserDefinedExpression + "(");
    };

    oUserDefinedExpressionModel.onClickCloseParenthesis = function() {
        let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
        let sUserDefinedExpression = oFieldPropertiesDetailsModel.getProperty("/singleValueUIDetails/userDefinedExpression");
        oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpression", sUserDefinedExpression + ")");
    };

    oUserDefinedExpressionModel.onClickAndOperator = function() {
        let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
        let sUserDefinedExpression = oFieldPropertiesDetailsModel.getProperty("/singleValueUIDetails/userDefinedExpression");
        oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpression", sUserDefinedExpression + " and ");
    };

    oUserDefinedExpressionModel.onClickOrOperator = function() {
        let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
        let sUserDefinedExpression = oFieldPropertiesDetailsModel.getProperty("/singleValueUIDetails/userDefinedExpression");
        oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpression", sUserDefinedExpression + " or ");
    };

    oUserDefinedExpressionModel.onClickSave = function() {
        let oComponent = this;
        let oFieldPropertiesDetailsModel = oComponent.getModel("fieldPropertiesDetails");
        let sUserDefinedExpression = oFieldPropertiesDetailsModel.getProperty("/singleValueUIDetails/userDefinedExpression");
        
        let oDataToWrite = {
            "UsmdModel": oFieldPropertiesDetailsModel.oData.AttributePath.DataModel,
            "UsmdCreqType": oFieldPropertiesDetailsModel.oData.AttributePath.CRName,
			// Bug 13488: deleted 'Transport' property.
            "RsEntity": " ",
            "RsAttribute": " ",
            "MODELTOVALIDATENAV": [{
                "Usmdmodel": oFieldPropertiesDetailsModel.oData.AttributePath.DataModel,
                "Expression": sUserDefinedExpression,
                "Message": " "
            }],
            "MODELTOMESSAGE": []
        };
        
        let oWriteParameterObject = {
            success: function (oDataResult, oResponse) {
                let bError = false;
                let sErrorMessage = "";

                sap.ui.core.BusyIndicator.hide();
                
                if (oResponse.data.MODELTOMESSAGE.results) {
                    for (let result of oResponse.data.MODELTOMESSAGE.results) {
                        if (result.MessageType === "E") {
                            bError = true;
                            sErrorMessage = result.Message;
                            break;
                        }
                    }
                }

                if (bError) {
                    oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpressionValueState", "Error");
			        oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpressionValueStateText", sErrorMessage);
                } else {
                    oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpressionValueState", "None");
			        oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpressionValueStateText", "");
                    oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/checkExpression", sUserDefinedExpression);
                    
                    let oUserDefinedExpressionDialog = oComponent._getUserDefinedExpressionFragment();
                    oUserDefinedExpressionDialog.close();
                }
            },
            error: function () {
                sap.ui.core.BusyIndicator.hide();
                oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpressionValueState", "Error");
			    oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpressionValueStateText", "The expression could not be validated");
            }
        };

        sap.ui.core.BusyIndicator.show(300);
        let oDataModel = oComponent.getModel("SAVE_BRF_DERIVATION_RULE");
        oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
        oDataModel.create("/MODELSet", oDataToWrite, oWriteParameterObject);
    };

    oUserDefinedExpressionModel.onClickCancel = function() {
        let oUserDefinedExpressionDialog = this._getUserDefinedExpressionFragment();
		oUserDefinedExpressionDialog.close();
    };

    return oUserDefinedExpressionModel;
});