sap.ui.define([
	"sap/ui/model/json/JSONModel",
	"jquery.sap.global",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"sap/m/MessageBox"
], function (JSONModel, jQuery, DMRDataService, Utilities, MessageBox) {
	"use strict";
	let oSingleValueUIDetailsObject = {};
	
	/*
	*	Create the initial screen for Single Value UI Property
	*	Read the property details of existing Property selected from FieldProperties List
	*	Called from UIPropertiesDetails controller
	
	*	this refers to the current js file
	*	oController refers to UIPropertiesDetails controller
	*/
	oSingleValueUIDetailsObject.setPropertyDetails = function(oController){
		let oComponent = this;
		
		this.ParentController = oController;  
		
		let oFieldPropertiesDetailsModel = this.ParentController.getView().getModel("fieldPropertiesDetails");
		
		let oAttributePath = oFieldPropertiesDetailsModel.getProperty("/AttributePath");
		
		oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails", {});
		oFieldPropertiesDetailsModel.setProperty("/BAdISelected", null);
		
		// Reset the Process Flow Model 
		let oProcessFlow = sap.ui.getCore().byId(this.ParentController.SingleValueUIPropertyFragmentId + "--idPropertyCheckProcessFlow");
		let oProcessFlowModel = new JSONModel();
		oProcessFlow.setModel(oProcessFlowModel);
		
		if (oFieldPropertiesDetailsModel.getProperty("/CreateNew") === true) {
			oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/editForm", true);
			//Newly created rule must be active by default.
			oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/propertyEnabled", true);
			oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/editAttribute", true);
		} else {
			oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/editForm", false);
			oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/editAttribute", false);
		}
		
		let oPromiseAttributeList = 
			this.ParentController.GetListData.getEntityAttributeList(
				this.ParentController, 
				oAttributePath.DataModel, 
				oAttributePath.EntityName, 
				undefined
			); 
		
		oPromiseAttributeList.then(function(arrAttributeList){
			arrAttributeList.unshift({
				UsmdAttribute: "**",
				Txtlg: "For All Attributes"
			});
			oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/attributeList", arrAttributeList);
		    
			//Task 12508 Filter CR Step type properties
			let oPromiseWFStepList = 
				oComponent.ParentController.GetListData.getWorkflowStepList(
					oComponent.ParentController, 
					oAttributePath.CRName,
					undefined
				);
				
			oPromiseWFStepList.then(function(arrWFStepList){
				// Defect 12615 Show Wfstep as 00 instead of 0
				arrWFStepList.forEach(step => {
					if(step.UsmdCreqStep.length < 2){
						step.UsmdCreqStep = "0" + step.UsmdCreqStep;
					}
				});
				oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/wfStatusList", arrWFStepList);
			});
	
			// Return if it is new property
			if (oFieldPropertiesDetailsModel.getProperty("/CreateNew") === true){
				oFieldPropertiesDetailsModel.setProperty("/AttributePath/AppName", undefined);
				oFieldPropertiesDetailsModel.setProperty("/AttributePath/AppType", undefined);
				oFieldPropertiesDetailsModel.setProperty("/AttributePath/AppId", undefined);
				oFieldPropertiesDetailsModel.setProperty("/AttributePath/UserRuleName", undefined); //Task 12487 - Add User Rule Name to Rules/Properties
				return;
			}
			
			let promiseDecisionTable = oComponent._getSingleValueUIConfiguration(oComponent.ParentController, oAttributePath);
	
			promiseDecisionTable.then(function (arrDecisionTableDetails) {
				
				oAttributePath.CustTransport = arrDecisionTableDetails.CustTransport;
				let oJSONPropertyDetails = JSON.parse(arrDecisionTableDetails.ruleexpr);
				oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userRuleName", oAttributePath.UserRuleName); //Task 12487 - Add User Rule Name to Rules/Properties				
				//Update the Property Enabled switch based on service layer property isNeeded
				let sIsNeeded = oJSONPropertyDetails.modeltoentitynav[0].entitytoattrnav[0].isneeded;
				if (sIsNeeded === "YES") {
					oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/propertyEnabled", true);
				} else {
					oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/propertyEnabled", false);
				}
					
				//Update the Selected Attribute
				let sSelectedAttribute = oJSONPropertyDetails.modeltoentitynav[0].entitytoattrnav[0].usmdattribute;
				oFieldPropertiesDetailsModel.setProperty(
					"/singleValueUIDetails/selectedAttributeName", sSelectedAttribute);
				// Update the expression type 
				oFieldPropertiesDetailsModel.setProperty(
					"/singleValueUIDetails/checkExpressionType", 
					oJSONPropertyDetails.modeltoentitynav[0].entitytoattrnav[0].exprtype);
					
				//Exclude wf_step id from the Check expression and add to Property
				let sExpressionToDisplay = oJSONPropertyDetails.modeltoentitynav[0].entitytoattrnav[0].expression;
				sExpressionToDisplay = sExpressionToDisplay.substring(1, sExpressionToDisplay.lastIndexOf(")"));
				oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/checkExpression", sExpressionToDisplay);

				let oPromiseUIPropertyStatusList = 
				oComponent.ParentController.GetListData.getUIPropertyStatusList(
					oComponent.ParentController, sSelectedAttribute
				);
				
				oPromiseUIPropertyStatusList.then(function(arrStatusList){
					oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/statusList", arrStatusList);
				});
						
				// Derivation value update 
				let arrAttributeToSetValue = oJSONPropertyDetails.modeltoentitynav[0].entitytoattrnav[0].attrtosetvaluenav;
				if(arrAttributeToSetValue !== undefined && arrAttributeToSetValue !== null && arrAttributeToSetValue.length > 0 )
				{
					arrAttributeToSetValue.forEach(function(attrSetValue){
						if(attrSetValue.iftrue === "X") {
							oFieldPropertiesDetailsModel.setProperty(
								"/singleValueUIDetails/statusUIProperty", attrSetValue.value);
						}
					});
				}
			
				// Trigger the process flow creation. Fire "press" on Generate Flow button
				// Fix 12246 - Last part of code was wrapped into a promise to wait the backend response
				let promiseFlowBtn = oController.SingleValueUIDetailsDialog.onPressGeneratePropertyCheckFlow(oController);
				promiseFlowBtn.then(async function(){
					let attrList = oFieldPropertiesDetailsModel.oData.singleValueUIDetails.attributeList;
					
					oJSONPropertyDetails.modeltoentitynav[0].entitytoattrnav[0].attrtoconditionnav.map( oItem => {
						let otmpItem = attrList.find(attr => attr.UsmdEntity === oItem.usmdentity && attr.UsmdAttribute === oItem.usmdattribute);
						if(otmpItem){
							oItem.ElementType = otmpItem.DATATYPE;
						}
						return oItem;
					});
					
					// Update the Process Flow Model with the data 
					let iExprNodeCount = oJSONPropertyDetails.modeltoentitynav[0].entitytoattrnav[0].attrtoconditionnav.length;
					for(let i = 0; i < iExprNodeCount; i++)
					{
						let tempAttrCondition = oJSONPropertyDetails.modeltoentitynav[0].entitytoattrnav[0].attrtoconditionnav[i];
						if(tempAttrCondition.usmdentity !== "WF") {
							let oNodeDetails = {};
							// Due to the node number mechanism where the subnodes or relations also have a number all node
							// have an id = i * 2
							oNodeDetails.inputFromValue = tempAttrCondition.value;
							oNodeDetails.inputFromValueDescr = tempAttrCondition.valuedescr;
							oNodeDetails.inputToValue = tempAttrCondition.tovalue;
							oNodeDetails.inputToValueDescr = tempAttrCondition.tovaluedescr;
							oNodeDetails.nodeId = (i * 2).toString();
							oNodeDetails.ElementType = tempAttrCondition.attrdatatype === "DATS" ? "P" : tempAttrCondition.attrdatatype;
							oNodeDetails.selectedEntity = tempAttrCondition.usmdentity;
							oNodeDetails.selectedAttribute = tempAttrCondition.usmdattribute;
							oNodeDetails.selectedComparator = tempAttrCondition.operator;
							oNodeDetails.selectedDimension = tempAttrCondition.dimension;
							oNodeDetails.selectedUnit = tempAttrCondition.unit;
							
							oComponent.updateNodeData(oController, oNodeDetails);
						} else {
							oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/wfStatus", tempAttrCondition.value);
						}
					}	
					
				});
			});
		});
	};
	
	/*
	*	Event Handler to change model property on toggle of property acive switch
	*
	*	this refers to UIPropertiesDetails ccontroller 
	*/
	oSingleValueUIDetailsObject.onPropertyEnableChanged = function(oEvent) {
		let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
	 	let oSwitchComponent = oEvent.getSource();
		let sSwitchState = oSwitchComponent.getState();
		oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/propertyEnabled", sSwitchState);
	};
	
	/*
		* Turn all fields editable on click on Edit button for already existing UI Property
		* this = UIPropertiesDetails.controller.js
	*/
	oSingleValueUIDetailsObject.onPressEditPropertyCheckFlow = function() {
		let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
		
		// Enable the form edit status
		oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/editForm", true);
		oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/editAttribute", false);
	};

	/*
		* Event trigger on selecting Attribute
		* Populate the UI Property Status List based on the selected Attribute
	*/
	oSingleValueUIDetailsObject.onChangeAttribute = function(oEvent) {
		let oController = this;
		let oFieldPropertiesDetailsModel = oController.getModel("fieldPropertiesDetails");
		let sSelectedAttribute = oEvent.getSource().getSelectedKey();
		let oPromiseUIPropertyStatusList = 
			oController.GetListData.getUIPropertyStatusList(
				oController, sSelectedAttribute
			);
		
		oPromiseUIPropertyStatusList.then(function(arrStatusList){
			oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/statusList", arrStatusList);
		});

	};
	
	/*
	*	Event Handler to delete UI Property on click of button
	*
	*	this refers to UIPropertiesDetails ccontroller 
	*/
	oSingleValueUIDetailsObject.onPropertyDeletePressed = function() {
		let oController = this;
		let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
		let oAttributePath = oFieldPropertiesDetailsModel.getProperty("/AttributePath");
		let promiseShowPopupAlert;
		
		//Display Error Message as User is trying to delete a rule which has not been created
		if (oFieldPropertiesDetailsModel.getProperty("/CreateNew") === true) {
			Utilities.showPopupAlert("Error: Cannot delete a Field Property which has not been created.",
			MessageBox.Icon.ERROR, "Action not possible");
			return;
		}
		
		let oDataToWrite = {
			"usmdmodel": oAttributePath.DataModel,
			"usmdcreqtype": oAttributePath.CRName,
			"CustTransport": undefined,
			"WbTransport": undefined,
			"modeltoentitynav": [				
				{
					"usmdmodel": oAttributePath.DataModel,
					"usmdentity": oAttributePath.EntityName,
					"delete": "X",
					"appname": oAttributePath.AppName,
					"entitytoattrnav": []
				}
			],
			"modeltomessagenav": [
				{
					"usmdmodel": "",
					"MessageType": "",
					"Message": ""
				}
			]
		};
		
		let oWriteParameterObject = {
			success: function (oDataResult) {
				let arrResults = oDataResult.modeltomessagenav.results;
				// Check the message status and notify the result 
				if(arrResults[0].MessageType === "S") // Success
				{
					// Disable the form edit status
					oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/editForm", false);
					promiseShowPopupAlert =
					Utilities.showPopupAlert(arrResults[0].Message, MessageBox.Icon.SUCCESS, "Single Value UI Property");
					promiseShowPopupAlert.then(function () {
					});
					
					// Notify the delete status to parent component
					oController.notifyUIPropertyDeleted();
				}
				else // Error
				{
					promiseShowPopupAlert =
					Utilities.showPopupAlert(arrResults[0].Message, MessageBox.Icon.ERROR, "Single Value UI Property");
					promiseShowPopupAlert.then(function () {
					});	
				}
				
				sap.ui.core.BusyIndicator.hide();
			},
			error: function () {
				sap.ui.core.BusyIndicator.hide();
				promiseShowPopupAlert =
					Utilities.showPopupAlert("Property could not be deleted.", MessageBox.Icon.ERROR, "Single Value UI Property");
					promiseShowPopupAlert.then(function () {
					});	
			}
		};
		
		oFieldPropertiesDetailsModel.setProperty("/deleteRule", "X");

		oFieldPropertiesDetailsModel.setProperty("/selectedTransportPackage/customizingTransport", oAttributePath.CustTransport);
		oController._getSelectedTransportPackage(true, false, false)
		.then(function(oSelectedResponse){
			oDataToWrite.CustTransport = oSelectedResponse.customizingTransport;

			sap.ui.core.BusyIndicator.show(300);
			let oDataModel = oController.getModel("GET_UI_PROPERTY");
			oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
			oDataModel.create("/modelSet", oDataToWrite, oWriteParameterObject);
		});
	};
	
	/*
	*	Event Handler to save UI Property on click of button
	*
	*	this refers to UIPropertiesDetails ccontroller 
	*/
	oSingleValueUIDetailsObject.onSaveUIProperty = function() {
		
		let oController = this;
		let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
		let oAttributePath = oFieldPropertiesDetailsModel.getProperty("/AttributePath");
		let oSingleValueUIDetails = oFieldPropertiesDetailsModel.getProperty("/singleValueUIDetails");
		let promiseShowPopupAlert;
		
		let oProcessFlow = sap.ui.getCore().byId(this.SingleValueUIPropertyFragmentId + "--idPropertyCheckProcessFlow");
		oProcessFlow.getNodes();
		let oProcessFlowNodes = oProcessFlow.getNodes();
		
		if (!oProcessFlowNodes || oProcessFlowNodes.length === 0) {
			promiseShowPopupAlert =
				Utilities.showPopupAlert("Error: Please enter Node Details", MessageBox.Icon.ERROR, "Missing Mandatory Details");
				promiseShowPopupAlert.then(function () {
			});
			
			return;
		}
		
		let sLastExpressionId = oProcessFlowNodes.length + 1;
		
		// Iterate through the Process Flow Data and add to the array
		let arrAttrToCondition = [];
		let sValue;
		let sToValue;
		let oCustomData;
		for(let i = 0; i < oProcessFlowNodes.length; i++)
		{
			oCustomData = oProcessFlowNodes[i].getCustomData()[0].getValue();
			// If the operation is of type BT or NB
			if(oProcessFlowNodes[i].getStateText() === "BT" || oProcessFlowNodes[i].getStateText() === "NB")
			{
				sValue = oProcessFlowNodes[i].getTexts()[0];
				sToValue = oProcessFlowNodes[i].getTexts()[1];
			}
			else
			{
				sValue = oProcessFlowNodes[i].getTexts()[0];
			}
			
			if(oProcessFlowNodes[i].getCustomData()[0].getValue().ElementType === "P"){
				sValue = sValue.split(".")[2] + sValue.split(".")[1] + sValue.split(".")[0];
				sToValue = ( sToValue === undefined ) ? undefined : sToValue.split(".")[2] + sToValue.split(".")[1] + sToValue.split(".")[0];
			}
			
			// Bug 12519 - When attributes of type Date is set as blank value in RDG, in the payload NaN value is sent instead of empty value
			if (sValue === "NaN") {
				sValue = "";
			}

			// Retrieve the Entity Name and Attribute name 
			let arrEntityAttribute = oProcessFlowNodes[i].getTitle().split(" - ");
			
			let sODataValue = undefined;
			let sODataDesc = undefined;
			let sODataToValue = undefined;
			let sODataToDesc = undefined;

			if(sValue){
				sODataValue = sValue.includes(" - ") ? sValue.split(" - ")[0].trim() : sValue;
				sODataDesc = sValue.includes(" - ") ? sValue.split(" - ")[1].trim() : "-BLANK-";
			}

			if(sToValue){
				sODataToValue = sToValue.includes(" - ") ? sToValue.split(" - ")[0].trim() : sToValue;
				sODataToDesc = sToValue.includes(" - ") ? sToValue.split(" - ")[1].trim() : "-BLANK-";
			}

			arrAttrToCondition.push({
				"usmdmodel": oAttributePath.DataModel,
				"usmdentity": arrEntityAttribute[0].trim(),
				"usmdattribute": arrEntityAttribute[1].trim(),
				"Attrdatatype": (oProcessFlowNodes[i].getCustomData()[0].getValue().ElementType === "P") ? "DATS" : oProcessFlowNodes[i].getCustomData()[0].getValue().ElementType,
				"operator": oProcessFlowNodes[i].getStateText(),
				"value": sODataValue,
				"ValueDescr": sODataDesc,
				"tovalue": sODataToValue,
				"ToValueDescr": sODataToDesc,
				"Dimension": oCustomData.Dimension,
				"Unit": oCustomData.Unit,
				"exprid": (i + 1).toString()
			});
			//clear the variables for next iteration
			sValue = undefined;
			sToValue = undefined;
		}

		// Bug 12519 - When attributes of type Date is set as blank value in RDG, in the payload NaN value is sent instead of empty value
		let sWfStatus = oSingleValueUIDetails.wfStatus;
		if (sWfStatus === "NaN") {
			sWfStatus = "";
		}
		
		//Pushing the workflow step details to AttrToCondition array as the last expression
		arrAttrToCondition.push({
			"usmdmodel": oAttributePath.DataModel,
			"usmdentity": "WF",
			"usmdattribute": "STEP",
			"operator": "EQ",
			"value": sWfStatus,
			"exprid": (sLastExpressionId).toString()
		});
		
		//Change the expression to incorporate wf__step. Need not be shown on the UI.
		let sExpressionToSave = "(" + oSingleValueUIDetails.checkExpression + ") and <" + sLastExpressionId + ">";
		oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/expressionToSave", sExpressionToSave);
		
		// Bug 12519 - When attributes of type Date is set as blank value in RDG, in the payload NaN value is sent instead of empty value
		let sStatusUIProperty = oSingleValueUIDetails.statusUIProperty;
		if (sStatusUIProperty === "NaN") {
			sStatusUIProperty = "";
		}

		// Update the value data based on the settings for IF TRUE and IF FALSE fields 
		let arrAttrToSetValue = [];
		if (sStatusUIProperty) {
			arrAttrToSetValue.push({
				"usmdmodel": oAttributePath.DataModel,
				"usmdattribute": oSingleValueUIDetails.selectedAttributeName,
				"value": sStatusUIProperty,
				"iftrue": "X",
				"iffalse": ""
			});
		} else {
			promiseShowPopupAlert =
				Utilities.showPopupAlert("Error: Please enter UI Property", MessageBox.Icon.ERROR, "Missing Mandatory Details");
				promiseShowPopupAlert.then(function () {
			});
			
			return;
		}
		
		let oDataToWrite = {
			"usmdmodel": oAttributePath.DataModel,
			"usmdcreqtype": oAttributePath.CRName,
			"package": undefined,
			"CustTransport": undefined,
			"WbTransport": undefined,


			"modeltoentitynav": [				
				{
					"usmdmodel": oAttributePath.DataModel,
					"usmdentity": oAttributePath.EntityName,
					"delete": "",
					"appname": ( oFieldPropertiesDetailsModel.getProperty("/CreateNew") === true ) ? "" : oAttributePath.AppName,
					"userrulename": oSingleValueUIDetails.userRuleName ? oSingleValueUIDetails.userRuleName : undefined, //Task 12487 - Add User Rule Name to Rules/Properties
					"entitytoattrnav": [
						{
							"usmdmodel": oAttributePath.DataModel,
							"usmdattribute": oSingleValueUIDetails.selectedAttributeName,
							"exprtype": oSingleValueUIDetails.checkExpressionType,
							"expression": sExpressionToSave,
							"isneeded": ( oSingleValueUIDetails.propertyEnabled === true ) ? "YES" : "NO",
							"attrtoconditionnav": arrAttrToCondition,
							"attrtosetvaluenav": arrAttrToSetValue
						}
					]
				}
			],
			"modeltomessagenav": [
				{
					"usmdmodel": "",
					"MessageType": "",
					"Message": ""
				}
			]
		};
		
		let oWriteParameterObject = {
			success: function (oDataResult) {
				let arrMessages = [];
				let sSaveError = "";
				jQuery.extend(true, arrMessages, oDataResult.modeltomessagenav.results);
				// Sort the messages
				arrMessages.sort(function (m1) {
					if (m1.MessageType === "E")
					{
						sSaveError = "Yes";
						return -1;
					}
					if (m1.MessageType === "W") { return 1; }
					return 0;
				});
				if (sSaveError === "")
				{
					oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/editForm", false);
					oController.notifyUIPropertySaved();
				}
				oController.ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
				// Check the message status and notify the result 
				sap.ui.core.BusyIndicator.hide();
			},
			error: function () {
				sap.ui.core.BusyIndicator.hide();
				promiseShowPopupAlert =
					Utilities.showPopupAlert("Property could not be saved/updated.", MessageBox.Icon.ERROR, "Single Value UI Property");
					promiseShowPopupAlert.then(function () {
					});	
			}
		};

		oAttributePath.RuleType = "1";
		oController.getSelectedBADIDialog(oController, oAttributePath)
		.then(function(oSelectedBAdI){
			oDataToWrite.badiimpl  = oSelectedBAdI.BADISelected;
			oDataToWrite.newbadi  = oSelectedBAdI.isNewBADI;
			oFieldPropertiesDetailsModel.setProperty("/selectedTransportPackage/customizingTransport", oAttributePath.CustTransport);
			return oController._getSelectedTransportPackage(true, true, true);
		})
		.then(function(oSelectedResponse){
			oDataToWrite.CustTransport = oSelectedResponse.customizingTransport;
			oDataToWrite.WbTransport = oSelectedResponse.workbenchTransport;
			oDataToWrite.package = oSelectedResponse.package;
			
			sap.ui.core.BusyIndicator.show(300);
			let oDataModel = oController.getModel("GET_UI_PROPERTY");
			oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
			oDataModel.create("/modelSet", oDataToWrite, oWriteParameterObject);
		});
	};
	
	/*
	 * Event handler to read the selected expression and display the Process Flow graph.
	 *		Read the expression
	 *		Parse 
	 *		Build the Process Flow
	 *	
	 * this = UIPropertiesDetails controller
	 */
	oSingleValueUIDetailsObject.onPressGeneratePropertyCheckFlow = function(oController){
		// Task 10260 - Provide a better User interface for the SIngle value derivation (Check Expression)
		let oComponent = (oController.sId) ? this : oController;
        let oFieldPropertiesDetailsModel = oComponent.getModel("fieldPropertiesDetails");
		let sCheckExpression = oFieldPropertiesDetailsModel.getProperty("/singleValueUIDetails/checkExpression");

		// Fix 12246 - A promise was added due to a request for backend was added
		let promise = new Promise(function(resolve){
			let oDataToWrite = {
				"UsmdModel": oFieldPropertiesDetailsModel.oData.AttributePath.DataModel,
				"UsmdCreqType": oFieldPropertiesDetailsModel.oData.AttributePath.CRName,
				// Bug 13488: deleted 'Transport' property.
				"RsEntity": " ",
				"RsAttribute": " ",
				"MODELTOVALIDATENAV": [{
					"Usmdmodel": oFieldPropertiesDetailsModel.oData.AttributePath.DataModel,
					"Expression": sCheckExpression,
					"Message": " "
				}],
				"MODELTOMESSAGE": []
			};
			
			let oWriteParameterObject = {
				success: function (oDataResult, oResponse) {
					let bError = false;
					let sErrorMessage = "";

					sap.ui.core.BusyIndicator.hide();
					
					if (oResponse.data.MODELTOMESSAGE.results) {
						for (let result of oResponse.data.MODELTOMESSAGE.results) {
							if (result.MessageType === "E") {
								bError = true;
								sErrorMessage = result.Message;
								break;
							}
						}
					}

					if (bError) {
						oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/checkExpressionValueState", "Error");
						oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/checkExpressionValueStateText", sErrorMessage);
					} else {
						oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/checkExpressionValueState", "None");
						oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/checkExpressionValueStateText", "");
						
						let oProcessFlow = sap.ui.getCore().byId(oComponent.SingleValueUIPropertyFragmentId + "--idPropertyCheckProcessFlow");
						
						let oProcessFlowModel = oProcessFlow.getModel(); 
						let oProcessFlowData = oProcessFlowModel.getData();

						let flowArr = {
							"lanes": [],
							"nodes": []
						};

						let i = 0;
						oComponent.ProcessFlowLibraryModel.setConditionArray(sCheckExpression, flowArr, i, oProcessFlowData);
						oProcessFlowModel.setData(flowArr);

						//oProcessFlow.setModel(oModel);
						oProcessFlow.setZoomLevel(sap.suite.ui.commons.ProcessFlowZoomLevel.Two);
						oProcessFlow.updateModel();
					}
					resolve();
				},
				error: function () {
					sap.ui.core.BusyIndicator.hide();
					oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/checkExpressionValueState", "Error");
					oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/checkExpressionValueStateText", "The expression could not be validated");
				}
			};

			sap.ui.core.BusyIndicator.show(300);
			let oDataModel = oComponent.getModel("SAVE_BRF_DERIVATION_RULE");
			oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
			oDataModel.create("/MODELSet", oDataToWrite, oWriteParameterObject);

		});

		return promise;
	};
	
	oSingleValueUIDetailsObject.formatConnectionLabels = function (childrenData) {
			let aChildren = [];
			for (let i = 0; childrenData && i < childrenData.length; i++) {
				if (childrenData[i].connectionLabel && childrenData[i].connectionLabel.id) {
					let oConnectionLabel = sap.ui.getCore().byId(childrenData[i].connectionLabel.id);
					if (!oConnectionLabel) {
						oConnectionLabel = new sap.suite.ui.commons.ProcessFlowConnectionLabel({
							id: childrenData[i].connectionLabel.id,
							text: childrenData[i].connectionLabel.text,
							enabled: childrenData[i].connectionLabel.enabled,
							icon: childrenData[i].connectionLabel.icon,
							state: childrenData[i].connectionLabel.state,
							priority: childrenData[i].connectionLabel.priority
						});
					} else {
						oConnectionLabel.setText(childrenData[i].connectionLabel.text);
					}
					aChildren.push({
						nodeId: childrenData[i].nodeId,
						connectionLabel: oConnectionLabel
					});
				} else if (jQuery.type(childrenData[i]) === "number") {
					aChildren.push(childrenData[i]);
				}
			}
			return aChildren;
	};
	
	/**
	 * Event handler to edit the node on the process flow diagram
	 * 
	 * INVOKED AS AN EVENT HANDLER. this = UIPropertiesDetails Controller
	 */
	oSingleValueUIDetailsObject.onNodePressPropertyCheckProcessFlow = function (oEvent) {
	
		let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
		
		// Check if edit is enabled ... return without action if not enabled 
		let bEditEnabled = oFieldPropertiesDetailsModel.getProperty("/singleValueUIDetails/editForm");
		if(!bEditEnabled) {
			// Do nothing and return 
			return;
		}

		let oNode = oEvent.getParameters();
		let sNodeIdClicked = oNode.getNodeId();
		let oCustomData;
		
		try {
			 oCustomData = oNode.getCustomData()[0].getValue();
		} catch (err) {
			oCustomData = {};
		}
		
		// Read the node details and initialize the data to be edited
		let sNodeTitle = oNode.getTitle();
		// Split the title at "-" and use the data for entity and attribute. If 2 elements are not found, initialize
		let sEntityName = "";
		let sAttributeName = "";
		let arrNodeTitle = sNodeTitle.split("-");
		let sSelectedDimension;
		let sSelectedUnit;
		if(arrNodeTitle.length === 2)
		{
			sEntityName = arrNodeTitle[0].trim();
			sAttributeName = arrNodeTitle[1].trim();
		}
		let sComparator = oNode.getStateText();
		
		// Update the values 
		let sFromValue = "";
		let sToValue = "";
		let arrNodeTexts = oNode.getTexts();

		if(arrNodeTexts.length === 2)
		{
			sFromValue = arrNodeTexts[0];
			sToValue = arrNodeTexts[1];
		}
		sSelectedDimension = oCustomData.Dimension;
		sSelectedUnit = oCustomData.Unit;
	   
		// If Entity or Attribute are empty, default the values to the selected entity and attribute
		if(!sEntityName || sEntityName.length === 0)
		{
			sEntityName = oFieldPropertiesDetailsModel.getProperty("/AttributePath/EntityName");
		}
		
		if(!sAttributeName || sAttributeName.length === 0)
		{
			sAttributeName = oFieldPropertiesDetailsModel.getProperty("/singleValueUIDetails/selectedAttributeName");
		}
		
		this.NodeEditModel.openNodeEditDialog(this, {
			nodeId: sNodeIdClicked,
			selectedEntity: sEntityName,
			selectedAttribute: sAttributeName,
			selectedComparator: sComparator,
			inputFromValue: sFromValue,
			inputToValue: sToValue,
			selectedDimension: sSelectedDimension,
			selectedUnit: sSelectedUnit
		});
	};
	
	/*
	* Save the node details to the node. 
	* 
	* FUNCTION INVOKED FROM NODEEDIT JS - For create/edit
	* FUNCTION INVOKED FROM SINGLEVALUEUIDETAILS JS - For read
	*
	* this refers to the current JS File
	* this.ParentController and oParentController refers to UIPropertiesDetails controller
	*/
	oSingleValueUIDetailsObject.updateNodeData = function(oParentController, oNodeDetails) {
		let oFieldPropertiesDetailsModel = this.ParentController.getView().getModel("fieldPropertiesDetails");
		let oAttributePath = oFieldPropertiesDetailsModel.getProperty("/AttributePath");
		let oProcessFlow = sap.ui.getCore().byId(this.ParentController.SingleValueUIPropertyFragmentId + "--idPropertyCheckProcessFlow");
		let sAttributeValue = "";
		
		let oProcessNodes = oProcessFlow.getNodes();
		
		oProcessNodes.forEach(async function(oNode){
			if(oNode.getNodeId() === oNodeDetails.nodeId)
			{
				oNode.setTitle(oNodeDetails.selectedEntity + " - " + oNodeDetails.selectedAttribute);
				if(oNodeDetails.selectedComparator !== "CP" && oNodeDetails.selectedComparator !== "NP"){
					if (oNodeDetails.inputFromValue) {
						//Bug 12841 - For key - value pair, only use the key to validate the user selection
					
						if (!oNodeDetails.inputFromValue.includes(" - ") && !oNodeDetails.inputFromValueDescr) {
							sAttributeValue = oNodeDetails.inputFromValue;
							let aInputFromValue = await oParentController.GetListData.getAttributeValuesList(
								oParentController, 
								oAttributePath.DataModel, 
								oNodeDetails.selectedEntity, 
								oNodeDetails.selectedAttribute,
								sAttributeValue, 
								true
							); 
							if (aInputFromValue.length > 0) {
								oNodeDetails.inputFromValue = aInputFromValue[0].Key + (aInputFromValue[0].Ddtext.length > 0 ? " - " : "") + aInputFromValue[0].Ddtext;
							}
						}else if(oNodeDetails.inputFromValueDescr){
							oNodeDetails.inputFromValueDescr = oNodeDetails.inputFromValueDescr.replace("-BLANK-", "");
							oNodeDetails.inputFromValue += (oNodeDetails.inputFromValueDescr ? " - " + oNodeDetails.inputFromValueDescr : "");
						}
					}

					if (oNodeDetails.inputToValue) {
						//Bug 12841 - For key - value pair, only use the key to validate the user selection
						if (!oNodeDetails.inputToValue.includes(" - ") && !oNodeDetails.inputToValueDescr) {
							sAttributeValue = oNodeDetails.inputToValue;
							let aInputToValue = await oParentController.GetListData.getAttributeValuesList(
								oParentController, 
								oAttributePath.DataModel, 
								oNodeDetails.selectedEntity, 
								oNodeDetails.selectedAttribute,
								sAttributeValue, 
								true
							); 
							if (aInputToValue.length > 0) {
								oNodeDetails.inputToValue = aInputToValue[0].Key + (aInputToValue[0].Ddtext.length > 0 ? " - " : "") + aInputToValue[0].Ddtext;
							}
						}else if (oNodeDetails.inputToValueDescr) {
							oNodeDetails.inputToValueDescr = oNodeDetails.inputToValueDescr.replace("-BLANK-", "");
							oNodeDetails.inputToValue += (oNodeDetails.inputToValueDescr ? " - " + oNodeDetails.inputToValueDescr : "");
						}
					}
				}
				oNode.setStateText(oNodeDetails.selectedComparator);
				oNode.setTexts([ oNodeDetails.inputFromValue, oNodeDetails.inputToValue ]);
				if(oNodeDetails.ElementType === "P"){
					oNodeDetails.inputFromValue = oNodeDetails.inputFromValue.includes(".") ? oNodeDetails.inputFromValue : oNodeDetails.inputFromValue.slice(6, 8) + "." +
									oNodeDetails.inputFromValue.slice(4, 6) + "." + oNodeDetails.inputFromValue.slice(0, 4);
					oNodeDetails.inputToValue = oNodeDetails.inputToValue.includes(".") ? oNodeDetails.inputToValue : oNodeDetails.inputToValue.slice(6, 8) + "." +
									oNodeDetails.inputToValue.slice(4, 6) + "." + oNodeDetails.inputToValue.slice(0, 4);
									
					oNode.setTexts([(oNodeDetails.inputFromValue === "..") ? "" : oNodeDetails.inputFromValue, 
										(oNodeDetails.inputToValue === "..") ? "" : oNodeDetails.inputToValue ]);
				}else{
					oNode.setTexts([	(oNodeDetails.inputFromValue === undefined) ? "" : oNodeDetails.inputFromValue, 
										(oNodeDetails.inputToValue === undefined) ? "" : oNodeDetails.inputToValue ]);
				}
				oNode.removeAllCustomData();
				let oNodeProperties = {
					ElementType: oNodeDetails.ElementType,
					Dimension: oNodeDetails.selectedDimension,
					Unit: oNodeDetails.selectedUnit
				};
				oNode.addCustomData(
					new sap.ui.core.CustomData({
						key: "NodeData",
						value: JSON.parse(JSON.stringify(oNodeProperties)),
						writeToDom: false
					})
				);
			}
		});
		
	};
	
	/*
	 * Called from this js file.
	 * this refers to the current js file
	 * oParentController refers to UIPropertiesDetails controller
	 * function for use. 
	 * 
	 * 1. Get the rule details
	 * 2. Get the rule expression details in JSON format
	 */
	oSingleValueUIDetailsObject._getSingleValueUIConfiguration = function(oController, oFieldPathInfo) {
		
		let promise;
		
		let sFilterCriteria = "usmdmodel eq '" + oFieldPathInfo.DataModel + "' and usmdcreqtype  eq '" + oFieldPathInfo.CRName;
		sFilterCriteria += "' and apptype eq '" + oFieldPathInfo.AppType + "' and usmdentity eq '" + oFieldPathInfo.EntityName;
		sFilterCriteria += "' and appname eq '" + oFieldPathInfo.AppName + "'";

		if (oFieldPathInfo.AppType === "1") {

			let oUrlParameters = {
				"$filter": sFilterCriteria
			};

			promise = new Promise(function(resolve, reject) {
				(new DMRDataService(
					oController,
					"GET_UI_PROPERTY",
					"/readrulesSet"
				)).getData({
					success: {
						fCallback: function (oParams, oData) {
							resolve(oData.results[0]);
						},
						oParam: {
							"controller": oController
						}
					},
					error: {
						fCallback: function (oParams, oErrorData) {
							reject(oErrorData);
						},
						oParam: null
					}
				}, oUrlParameters);
			});
		} 
		return promise;
		
	};
	
	// Task 10260 - Provide a better User interface for the SIngle value derivation (Check Expression)
	// If user selected the user-defined option, we need to show the fragment for building the custom built expression
	oSingleValueUIDetailsObject.onChangeCheckExpression = function(oEvent) {
		let oSelectedItem = oEvent.getSource().getSelectedItem();
		
		if (oSelectedItem) {
			let sSelectedKey = oSelectedItem.getProperty("key");
			
			if (sSelectedKey === "99") {
				let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
				oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpression", "");
				oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpressionValueState", "None");
				oFieldPropertiesDetailsModel.setProperty("/singleValueUIDetails/userDefinedExpressionValueStateText", "");
				
				// Open the User-Defined Expression dialog
				let oUserDefinedExpressionDialog = this._getUserDefinedExpressionFragment();
				oUserDefinedExpressionDialog.open();
			}
		}
	};
	
	return oSingleValueUIDetailsObject;
});