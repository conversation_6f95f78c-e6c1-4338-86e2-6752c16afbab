sap.ui.define([
	//"sap/ui/core/mvc/Controller",
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
	"sap/ui/model/json/JSONModel",
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"./SingleValueUIDetails",
	"./NodeEdit",
	"./ProcessFlowLibrary",
	"./MultiValueUIDetails",
	"./SelectAttribute",
	"./UserDefinedExpression",
	"sap/m/MessageBox",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"dmr/mdg/supernova/SupernovaFJ/model/ModelMessages",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService"
], function (BaseController, JSONModel, GetListsData, SingleValueUIDetailsDialog, NodeEditModel, ProcessFlowLibraryModel,
	MultiValueDetailsModel, SelectAttributeModel, UserDefinedExpressionModel, MessageBox, Utilities, ModelMessages, DMRDataService) {
	"use strict";
	let oUIPropertiesDetailsModel = {};

	oUIPropertiesDetailsModel.onInit = function () {
		let oView = this.getView();

		// Initialize the model 
		let oFieldPropertiesDetailsModel = oView.getModel("fieldPropertiesDetails");
		if (!oFieldPropertiesDetailsModel) {
			oView.setModel(new JSONModel(), "fieldPropertiesDetails");
			oFieldPropertiesDetailsModel = oView.getModel("fieldPropertiesDetails");
		}

		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		// Load the parameters received into the model
		oRouter.getRoute("UIProperties").attachPatternMatched(this, this._onRouteMatched);

		this.SingleValueUIDetailsDialog = SingleValueUIDetailsDialog;
		this.NodeEditModel = NodeEditModel;
		this.ProcessFlowLibraryModel = ProcessFlowLibraryModel;
		this.GetListData = GetListsData;
		this.MultiValueDetailsModel = MultiValueDetailsModel;
		this.SelectAttributeDialog = SelectAttributeModel;
		this.SingleValueUIPropertyFragmentId = "SingleValuePropertyFragmentID";
		this.SelectAttributeFragmentId = "SelectAttributeFragmentId";
		this.FieldPropertyNodeEditFragmentId = "UIPropertyNodeEditFragmentID";
		this.ModelMessages = ModelMessages;
		
		// Task 10260 - Provide a better User interface for the SIngle value derivation (Check Expression)
		this.UserDefinedExpressionModel = UserDefinedExpressionModel;
		this.UserDefinedExpressionFragmentID = "UserDefinedExpressionFragmentID";
		
		// Create the placeholder component to hold all the Fragments 
		this._placeHolderVBox = new sap.m.VBox({
			id: "vboxUIPropertiesPlaceHolder",
			//alignItems: "Center",
			width: "100%",
			fitContainer: true
		});

		this._placeHolderVBox.addItem(this._getUnidentifiedFielPropertyFragment());

		this.getView().byId("idUIPropertiesPlaceHolder").addItem(this._placeHolderVBox);
		//this.attachUIPropertyCreated(this.onUIPropertyCreated, this);
	};

	oUIPropertiesDetailsModel.onUIPropertyCreated = function () {
		sap.ui.getCore().getEventBus().publish("rdgChannel", "UIPropertyCreated");
	};

	oUIPropertiesDetailsModel._getUnidentifiedFielPropertyFragment = function () {
		if (!this._oUnidentifiedFieldPropertyFrgmt) {
			this._oUnidentifiedFieldPropertyFrgmt = sap.ui.xmlfragment(
				"BRFUnidentifiedPropertyFragmentID",
				"dmr.mdg.supernova.SupernovaFJ.view.UIProperties.UnidentifiedUIDetails",
				this
			);

			this._placeHolderVBox.addDependent(this._oUnidentifiedFieldPropertyFrgmt);
		}

		return this._oUnidentifiedFieldPropertyFrgmt;
	};

	oUIPropertiesDetailsModel._getSingleValueFielPropertyFragment = function () {
		if (!this._oSingleValueFieldPropertyFrgmt) {

			this._oSingleValueFieldPropertyFrgmt = sap.ui.xmlfragment(
				this.SingleValueUIPropertyFragmentId,
				"dmr.mdg.supernova.SupernovaFJ.view.UIProperties.SingleValueUIDetails",
				this
			);

			this._placeHolderVBox.addDependent(this._oSingleValueFieldPropertyFrgmt);
		}

		return this._oSingleValueFieldPropertyFrgmt;
	};

	oUIPropertiesDetailsModel._getNodeEditFragment = function () {
		if (!this._oFieldPropertyNodeEditFrgmt) {

			this._oFieldPropertyNodeEditFrgmt = sap.ui.xmlfragment(
				this.FieldPropertyNodeEditFragmentId,
				"dmr.mdg.supernova.SupernovaFJ.view.UIProperties.NodeEdit",
				this
			);

			this._placeHolderVBox.addDependent(this._oFieldPropertyNodeEditFrgmt);
		}

		return this._oFieldPropertyNodeEditFrgmt;
	};

	oUIPropertiesDetailsModel._getMultiValueFielPropertyFragment = function () {
		if (!this._oMultiValueFieldPropertyFrgmt) {
			this._oMultiValueFieldPropertyFrgmt = sap.ui.xmlfragment(
				"MultiValuePropertyFragmentID",
				"dmr.mdg.supernova.SupernovaFJ.view.UIProperties.MultiValueUIDetails",
				this
			);

			this._placeHolderVBox.addDependent(this._oMultiValueFieldPropertyFrgmt);
		}

		return this._oMultiValueFieldPropertyFrgmt;
	};

	oUIPropertiesDetailsModel._getSelectAttributeFragment = function () {
		if (!this._oSelectAttributeFrgmt) {
			this._oSelectAttributeFrgmt = sap.ui.xmlfragment(
				"SelectAttributeFragmentId",
				"dmr.mdg.supernova.SupernovaFJ.view.UIProperties.SelectAttribute",
				this
			);

			this._placeHolderVBox.addDependent(this._oSelectAttributeFrgmt);
		}

		return this._oSelectAttributeFrgmt;
	};

	/**
	 * Task 10260 - Provide a better User interface for the SIngle value derivation (Check Expression)
	 * Load the fragment for User Defined Expression Dialog
	 */
	oUIPropertiesDetailsModel._getUserDefinedExpressionFragment = function () {
		if (!this._oUserDefinedExpressionFrgmt) {
			this._oUserDefinedExpressionFrgmt = sap.ui.xmlfragment(
				"UserDefinedExpressionFragmentID",
				"dmr.mdg.supernova.SupernovaFJ.view.UIProperties.UserDefinedExpression",
				this
			);

			this._placeHolderVBox.addDependent(this._oUserDefinedExpressionFrgmt);
		}

		return this._oUserDefinedExpressionFrgmt;
	};

	oUIPropertiesDetailsModel._onRouteMatched = function (oEvent, oController) {
		// 	// Get the parameter and store to the model
		let oRouteParams = oEvent.getParameter("arguments");

		// 	// Store the params to the model 
		let oFieldPropertiesDetailsModel = oController.getView().getModel("fieldPropertiesDetails");
		oFieldPropertiesDetailsModel.setProperty("/AttributePath", oRouteParams);

		// Remove all components loaded (if any). //TO DO: Except transport dialog 
		let arrItems = oController._placeHolderVBox.getItems();
		arrItems.forEach(function (item) {
			oController._placeHolderVBox.removeItem(item);
		});

		if (!(!oRouteParams.AppId && !oRouteParams.AppName && !oRouteParams.AppType)) {
			oFieldPropertiesDetailsModel.setProperty("/CreateNew", false);

			switch (oRouteParams.AppType) {
			case "1": //Single Value UI Property
				oController._placeHolderVBox.addItem(oController._getSingleValueFielPropertyFragment());
				oController.SingleValueUIDetailsDialog.setPropertyDetails(oController);
				break;
			case "2": //MultiValue UI Property
				oController._placeHolderVBox.addItem(oController._getMultiValueFielPropertyFragment());
				oController.MultiValueDetailsModel.setFieldPropertyDetails(oController);
				break;
			default:
				oController._placeHolderVBox.addItem(oController._getUnidentifiedFielPropertyFragment());
				break;
			}

		} else {
			oController._placeHolderVBox.addItem(oController._getUnidentifiedFielPropertyFragment());
		}

		if (oRouteParams.CRName && oRouteParams.AppType) {
			oController._getBackgroundJobStatus(oRouteParams.CRName);
		} else if(!oRouteParams.CRName) {
			oController._getBackgroundJobStatus();
		}

	};

	oUIPropertiesDetailsModel._getBackgroundJobStatus = async function(sCRType, type) {
		let oFieldPropertiesDetailsModel = this.getView().getModel("fieldPropertiesDetails");
		let bNewRule = oFieldPropertiesDetailsModel.getProperty("/CreateNew");
		let oStatus;
		if (sCRType && type === "Import") {
			oFieldPropertiesDetailsModel = this.getView().getModel("fieldPropertiesDetails");
			oStatus = await GetListsData.getBackgroundJobStatus(this, undefined, "2", undefined, sCRType);
			if (oStatus.Restype.includes("S")) {
				let sMessage = oStatus.JOB2MESSAGENAV.results[0].Message + " " + oStatus.JOB2MESSAGENAV.results[0].Msgv1;
				Utilities.showPopupAlert(sMessage, MessageBox.Icon.INFORMATION, "Import not allowed");
				oFieldPropertiesDetailsModel.setProperty("/importAllowed", false);
			}else{
				oFieldPropertiesDetailsModel.setProperty("/importAllowed", true);
			}
		}else if(sCRType){
			oStatus = await GetListsData.getBackgroundJobStatus(this, undefined, "2", undefined, sCRType);
			oFieldPropertiesDetailsModel.setProperty("/lockedActions", oStatus.Restype);
			if ((oStatus.Restype.includes("S") && bNewRule) || (oStatus.Restype.includes("E") && !bNewRule)) {
				let sMessage = oStatus.JOB2MESSAGENAV.results[0].Message + " " + oStatus.JOB2MESSAGENAV.results[0].Msgv1;
				Utilities.showPopupAlert(sMessage, MessageBox.Icon.INFORMATION, "Locked Actions");
			}
		}else{
			oStatus = await GetListsData.getBackgroundJobStatus(this, undefined, "2");
			let aMessages = oStatus.JOB2MESSAGENAV.results;
			for (let i = 0; i < aMessages.length; i++) {
				let sMessageType = Utilities.getMessageIconCoreForType(aMessages[i].MessageType);
				Utilities.sendNotification(
					this, sMessageType,  
					aMessages[i].Message, 
					aMessages[i].Msgv1, 
					"Field Properties"
				);
			}
		}
	};

	oUIPropertiesDetailsModel.onEntityMenuAction = function (oEvent) {
		let oController = this;
		let sSelectedText = oEvent.getParameters().item.getText();

		let oFieldPropertiesDetailsModel = oController.getView().getModel("fieldPropertiesDetails");
		oFieldPropertiesDetailsModel.setProperty("/CreateNew", true);
		let sCRType = oFieldPropertiesDetailsModel.getProperty("/AttributePath/CRName");
		// Remove all components loaded (if any). //TO DO: Except transport dialog 
		let arrItems = oController._placeHolderVBox.getItems();
		arrItems.forEach(function (item) {
			oController._placeHolderVBox.removeItem(item);
		});

		switch (sSelectedText) {
		case "Single Value Field Property":
			oController._placeHolderVBox.addItem(oController._getSingleValueFielPropertyFragment());
			oController.SingleValueUIDetailsDialog.setPropertyDetails(oController);
			break;
		case "Multi Value Field Property":
			oController._placeHolderVBox.addItem(oController._getMultiValueFielPropertyFragment());
			oController.MultiValueDetailsModel.setFieldPropertyDetails(oController);
			break;
		default:
			oController._placeHolderVBox.addItem(oController._getUnidentifiedFielPropertyFragment());
			break;
		}

		oController._getBackgroundJobStatus(sCRType);
	};

	oUIPropertiesDetailsModel.onBADISelectionDialogCreated = function (oEvent) {
		let comp = oEvent.getParameter("component");
		// store the component handle 
		this._BADISelectionDialog = comp;

		this._BADISelectionDialog.attachBADISelected(this.onBADISelected, this);
	};

	oUIPropertiesDetailsModel.onBADISelected = function (oBADISelected) {		
		let sSelectedBADI = oBADISelected.getParameters();

		// Store the BAdI to the model
		this.getView()
			.getModel("fieldPropertiesDetails").setProperty("/BAdISelected", sSelectedBADI);

		this._BADISelectionDialogPromiseResolveFunction(sSelectedBADI);
	};

	oUIPropertiesDetailsModel.getSelectedBADI = function () {
		let oController = this;

		if (this._BADISelectionDialogPromiseRejectFunction) {
			this._BADISelectionDialogPromiseResolveFunction = undefined;
			this._BADISelectionDialogPromiseRejectFunction({});
			this._BADISelectionDialogPromiseRejectFunction = undefined;
		}

		this._BADISelectionDialogPromise = new Promise(function (resolve, reject) {
			let sBAdISelected = oController.getView()
				.getModel("fieldPropertiesDetails")
				.getProperty("/BAdISelected");

			if (sBAdISelected) {
				resolve(sBAdISelected);
			}else{
				oController._BADISelectionDialogPromiseResolveFunction = resolve;
				oController._BADISelectionDialogPromiseRejectFunction = reject;
				oController._BADISelectionDialog.open();
			}

		});

		return this._BADISelectionDialogPromise;
	};

	oUIPropertiesDetailsModel.getSelectedBADIDialog = function (oComponent, oRulePathInfo) {
		let BADIDialog = oComponent.byId("BADISelectDialog").getComponentInstance();
		BADIDialog.setRuleDetails({
			ruleType: (oRulePathInfo.RuleType) ? oRulePathInfo.RuleType : oRulePathInfo.AppType,
			usmdModel: oRulePathInfo.DataModel,
			usmdEntity: oRulePathInfo.EntityName,
			usmdCrtype: oRulePathInfo.CRName,
			featureType: "2",
			crossEntity: ""
		});
		return oComponent.getSelectedBADI();
		
	};

	oUIPropertiesDetailsModel.onTransportPackageDialogCreated = function (oEvent) {
		let comp = oEvent.getParameter("component");
		// store the component handle 
		this._transportPackageDialog = comp;
		
		this.getUsername()
			.then(function (oSapUserInfo) {
				let sUsername = oSapUserInfo.Sapname;
				if (!sUsername) {
					Utilities.showPopupAlert("Please login to continue", MessageBox.Icon.ERROR, "Logged out");
				} else {
					comp.setUser(sUsername);
				}
			});
	};

	oUIPropertiesDetailsModel._getSelectedTransportPackage = function(bCustomizing, bWorkbench, bPackage){
		let oModel = this.getView().getModel("fieldPropertiesDetails");
		let oSelectedTransportPackage = oModel.getProperty("/selectedTransportPackage");
		if(!oSelectedTransportPackage){
			oSelectedTransportPackage = {
				customizingTransport: undefined,
				workbenchTransport: undefined, 
				package: undefined
			};
		}

		let promise = this._transportPackageDialog.open(
			bCustomizing, oSelectedTransportPackage.customizingTransport, 
			bWorkbench, oSelectedTransportPackage.workbenchTransport, 
			bPackage, oSelectedTransportPackage.package);
		let returnPromise = promise.then(function(oResponseSelection){
			oSelectedTransportPackage.customizingTransport = oResponseSelection.customizingTransport;
			oSelectedTransportPackage.workbenchTransport = oResponseSelection.workbenchTransport;
			oSelectedTransportPackage.package = oResponseSelection.package;
			oModel.setProperty("/selectedTransportPackage", oSelectedTransportPackage);

			return oResponseSelection;
		});

		return returnPromise;
	};

	/**
	 * fire the businessRuleSaved event to be handled by the parent component 
	 */
	oUIPropertiesDetailsModel.notifyUIPropertySaved = function () {
		sap.ui.getCore().getEventBus().publish("rdgChannel", "UIPropertyUpdated"); //Task 12487 - Add User Rule Name to Rules/Properties
	};

	/**
	 * fire the businessRuleDeleted event to be handled by the parent component 
	 */
	oUIPropertiesDetailsModel.notifyUIPropertyDeleted = function () {
		let oModel = this.getModel("fieldPropertiesDetails");
		let sDeleteRule = oModel.getProperty("/deleteRule");
		if (sDeleteRule === "X") {
			// Fire the event to the calling component
			sap.ui.getCore().getEventBus().publish("rdgChannel", "UIPropertyUpdated");
		}

	};
	
		/**
		 * On press Import Rules
		 * Open the ImportRules Fragment
		 * Fetch all Change Requests filtered by Data Model. Filter out the selected Change Request type from the list
		 */
	oUIPropertiesDetailsModel.onClickImportUIProperties = function() {
		let oController = this;
		let oView = oController.getView();
		let oFieldPropertiesDetailsModel = oView.getModel("fieldPropertiesDetails");
		let sDataModel = oFieldPropertiesDetailsModel.getProperty("/AttributePath").DataModel;
		let sCRType = oFieldPropertiesDetailsModel.getProperty("/AttributePath").CRName;
		oFieldPropertiesDetailsModel.setProperty("/importUIProperties", {});
		oFieldPropertiesDetailsModel.setProperty("/CreateNew", true);
		if (!oController.ImportUIPropertiesDialog) {
			oController.ImportUIPropertiesDialog =
				sap.ui.xmlfragment(
					"ImportRules",
					"dmr.mdg.supernova.SupernovaFJ.view.UIProperties.ImportRules",
					oController);
			oController.getView().addDependent(oController.ImportUIPropertiesDialog);
		}
		
		let promiseCRList = GetListsData.getChangeRequestsList(this);
		promiseCRList.then(function(arrCrList){
			let arrFilteredCRList = arrCrList.results.filter((oChangeRequest) => {
				return oChangeRequest.UsmdModel === sDataModel &&
						oChangeRequest.UsmdCreqType !== sCRType;
			});
			/*Added the method setSizeLimit*/
			oFieldPropertiesDetailsModel.setSizeLimit(arrFilteredCRList.length);
			oFieldPropertiesDetailsModel.setProperty("/importUIProperties/changeRequestList", arrFilteredCRList);
		});
		
		// Open the dialog
		oController.ImportUIPropertiesDialog.open();
	};
		
	/**
	 * On selection Change for ComboBox
	 * Fetch all rules grouped by entity name
	 * Currently limited to Type 1,2,3
	 */
	oUIPropertiesDetailsModel.onSelectCRType = function() {
		let oController = this;
		let oView = oController.getView();
		let oFieldPropertiesDetailsModel = oView.getModel("fieldPropertiesDetails");
		
		let sDataModel = oFieldPropertiesDetailsModel.getProperty("/AttributePath").DataModel;
		let sSelectedCRType = oFieldPropertiesDetailsModel.getProperty("/importUIProperties/selectedCRType");
		let sTargetCRType = oFieldPropertiesDetailsModel.getProperty("/AttributePath/CRName");

		oController._getBackgroundJobStatus(sTargetCRType, "Import");
		
		// Bug 11585 - Selected imported rules not de-selected when cr type is changed
		oController.ImportUIPropertiesList.removeMultiSelectedItems();

		let oFieldPropertiesPromise = 
			GetListsData.getUIPropertiesList(oController, sDataModel, "", sSelectedCRType);
		
		oFieldPropertiesPromise.then((arrRules) => {
			// Filter the data with the CR Type selected 
			arrRules.sort((a, b) => (a.usmdentity - b.usmdentity || a.appdesc.localeCompare(b.appdesc)));
			let arrFieldPropertiesList = arrRules.filter((oRule) => {
				// return oRule.RuleDesc === "Mandatory" ||
				// 		oRule.RuleDesc === "Single Value Derivation" ||
				// 		oRule.RuleDesc === "Single Value Validation";
				// Now allowing all rule types to import, hence commented above conditions
				return oRule;
			});
			oController.ImportUIPropertiesList.setListData(arrFieldPropertiesList);
		}); 
	};
		
	oUIPropertiesDetailsModel.onImportUIPropertiesListCreated = function(oEvent) {
		let oController = this;

		let comp = oEvent.getParameter("component");
		oController.ImportUIPropertiesList = comp;

		// update the mapping to the component 
		oController.ImportUIPropertiesList.setDataMapping({
			"title": "userrulename", //Task 12487 - Add User Rule Name to Rules/Properties
			"description": undefined,
			"info": "appdesc"
		});
		oController.ImportUIPropertiesList.setMode("MultiSelect");
		oController.ImportUIPropertiesList.setRememberSelections(true);

		oController.ImportUIPropertiesList.setHeaderText("UI Properties");
		oController.ImportUIPropertiesList.setListGroupingPath("usmdentity");
	};
		
	oUIPropertiesDetailsModel.onImportUiPropertiesSave = function() {
		let oController = this;
		let oView = oController.getView();
		let oFieldPropertiesDetailsModel = oView.getModel("fieldPropertiesDetails");
		let oRulePath = oFieldPropertiesDetailsModel.getProperty("/AttributePath");
		let sSelectedCRType = oFieldPropertiesDetailsModel.getProperty("/importUIProperties/selectedCRType");
		let promiseShowPopupAlert;
		let arrSelectedItems = oController.ImportUIPropertiesList.getMultiSelectedItems();
		
		if(!sSelectedCRType) {
			promiseShowPopupAlert =
				Utilities.showPopupAlert("Change Request Type is mandatory",
					MessageBox.Icon.ERROR,
					"Missing mandatory details"
				);
			promiseShowPopupAlert.then(function () {
			});
			return;
		}
		
		if(arrSelectedItems.length === 0) {
			promiseShowPopupAlert =
				Utilities.showPopupAlert("Please select the UI properties to be imported.",
					MessageBox.Icon.ERROR,
					"No records selected"
				);
			promiseShowPopupAlert.then(function () {
			});
			return;
		}
		
		let oDataToWrite = {
			UsmdModel: oRulePath.DataModel,
			SourceCr: sSelectedCRType,
			TargetCr: oRulePath.CRName,
			Featuretype: "2",
			NAV_CRTORULES: [],
			NAV_CRTOMSGDET: []
		};
		
		arrSelectedItems.forEach(oSelectedItem => {
			oDataToWrite.NAV_CRTORULES.push({
				UsmdModel: oSelectedItem.data.usmdmodel,
				UsmdEntity: oSelectedItem.data.usmdentity,
				RuleId: oSelectedItem.data.appid,
				RuleName: oSelectedItem.data.appname
			});
		});
		
		let oWriteParameterObject = {
			// Bug 13573: added callbacks
			success: { 
				fCallback: function (oThisObject, oDataResult) {
					let arrMessages = [];
					let sSaveError = "";
					jQuery.extend(true, arrMessages, oDataResult.NAV_CRTOMSGDET.results);
					// Sort the messages
					arrMessages.sort(function (m1) {
						if (m1.MessageType === "E")
						{
							sSaveError = "Yes";
							return -1;
						}
						if (m1.MessageType === "W") { return 1; }
						return 0;
					});
					if (sSaveError === "")
					{
						oController.notifyUIPropertySaved();
						oController.ImportUIPropertiesList.removeMultiSelectedItems();
						oController.ImportUIPropertiesList.setListData([]);
						oController.ImportUIPropertiesDialog.close();
					}
					ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
					sap.ui.core.BusyIndicator.hide();
				},
				oParam:oController
			},
			error: {
				fCallback: function () {
					sap.ui.core.BusyIndicator.hide();
					promiseShowPopupAlert =
						Utilities.showPopupAlert("Selected UI Properties could not be imported", MessageBox.Icon.INFORMATION, "Import UI Properties Failed");
					promiseShowPopupAlert.then(function () {
					});	
				}
			}
		};
	
		oController._getSelectedTransportPackage(true, true, true)
		.then(function(oSelectedResponse){
			oDataToWrite.CustTransport = oSelectedResponse.customizingTransport;
			oDataToWrite.WbTransport = oSelectedResponse.workbenchTransport;
			oDataToWrite.Package = oSelectedResponse.package;

			(new DMRDataService(
				oController,
				"COPY_BUSINESS_RULES",
				"/CRTYPESSet",
				undefined,
				"/", // Root of the received data
				""
			))
			.saveData(false, oDataToWrite, null, oWriteParameterObject);
		});
	};
		
	oUIPropertiesDetailsModel.onImportUiPropertiesCancel = function() {
		let oController = this;
		oController.ImportUIPropertiesList.removeMultiSelectedItems();
		oController.ImportUIPropertiesList.setListData([]);
		
		oController.ImportUIPropertiesDialog.close();
	};

	return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.UIProperties.UIPropertiesDetails", oUIPropertiesDetailsModel);

});