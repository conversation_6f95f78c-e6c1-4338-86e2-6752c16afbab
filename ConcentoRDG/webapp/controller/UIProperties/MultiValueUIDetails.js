sap.ui.define([
	"jquery.sap.global",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
	"sap/m/MessageToast",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"sap/m/MessageBox"
], function (jQuery, DMRDataService, MessageToast, Utilities, MessageBox) {
	"use strict";
	let oMultiValueUIObject = {};

	oMultiValueUIObject.setFieldPropertyDetails = function (oController) {
		this.ParentController = oController;
		// Pointer to the select attribute dialog 
		this.SelectAttributeDialog = oController.SelectAttributeDialog;
		this.JSONViewModel = this.ParentController.getModel("fieldPropertiesDetails");
		let oFieldPropertiesDetailsModel = this.JSONViewModel;
		// Initialize mandatory properties 
		oFieldPropertiesDetailsModel.setProperty("/multiValueUI", {});
		oFieldPropertiesDetailsModel.setProperty("/multiValueUI/tableMode", sap.m.ListMode.SingleSelectMaster);
		oFieldPropertiesDetailsModel.setProperty("/multiValueUI/lists", {});
		oFieldPropertiesDetailsModel.setProperty("/multiValueUI/tableHasColumns", false);
		oFieldPropertiesDetailsModel.setProperty("/multiValueUI/data", []);
		oFieldPropertiesDetailsModel.setProperty("/BAdISelected", null);

		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		let oMultiUITable = sap.ui.getCore().byId("MultiValuePropertyFragmentID" + "--" + "idMultiValueUITable");

		// Remove all columns 
		oMultiUITable.removeAllColumns();

		// Set the binding data. If a binding already exists .. destroy 
		let oTableBindingInfo = oMultiUITable.getBindingInfo("items");
		if (oTableBindingInfo && oTableBindingInfo.template) {
			oTableBindingInfo.template.destroy();
		}

		// Set the binding info
		let oColumnItems = new sap.m.ColumnListItem("colItemsMultiUI", {
			type: "Active"
		});
		oMultiUITable.bindItems({
			path: "/multiValueUI/data",
			model: "fieldPropertiesDetails",
			template: oColumnItems,
			templateShareable: true
		});

		let oFieldPathInfo = oFieldPropertiesDetailsModel.getProperty("/AttributePath");
		if (oFieldPathInfo.EntityName) {
			let promiseAttrbList =
				this.ParentController.GetListData.getEntityAttributeList(oController, oFieldPathInfo.DataModel, oFieldPathInfo.EntityName, undefined);
			promiseAttrbList.then(function (arrAttributes) {
				oFieldPropertiesDetailsModel.setProperty("/attributeList", arrAttributes);
			});
		}

		// Return if it is new property
		if (oFieldPropertiesDetailsModel.getProperty("/CreateNew") === true) {
			oFieldPropertiesDetailsModel.setProperty("/AttributePath/AppName", undefined);
			oFieldPropertiesDetailsModel.setProperty("/AttributePath/AppType", undefined);
			oFieldPropertiesDetailsModel.setProperty("/AttributePath/AppId", undefined);
			oFieldPropertiesDetailsModel.setProperty("/AttributePath/UserRuleName", undefined); //Task 12487 - Add User Rule Name to Rules/Properties
			return;
		}

		// Read the Property data  
		let oMultiPropertyDetails = oFieldPropertiesDetailsModel.getProperty("/selectedUIProperty");
		// Read the decision table details and initialize the data to be displayed into the page 
		let promiseDecisionTable = oController.MultiValueDetailsModel._getMultiValueUIConfiguration(oController, oFieldPathInfo,
			oMultiPropertyDetails);

		promiseDecisionTable.then(async function (oDecisionTableDetails) {
			let i = 0;
			let arrAttributes = [];
			oFieldPathInfo.Transport = oDecisionTableDetails.transport;
			// Read the Driving, Deriving and Message Attribute and create the columns
			let json = JSON.parse(oDecisionTableDetails.ruleexpr);
			oDecisionTableDetails.modeltodriving = json.modeltodriving;
			oFieldPropertiesDetailsModel.setProperty("/multiValueUI/userRuleName", oFieldPathInfo.UserRuleName); //Task 12487 - Add User Rule Name to Rules/Properties
			oDecisionTableDetails.modeltoderiving = json.modeltoderiving;
			oDecisionTableDetails.modeltomessage = json.modeltomessage;
			oDecisionTableDetails.modeltoattrvalues = json.modeltoattrvalues;
			//Check whether the returned value is "INIT". If yes, replace it with ""
			oDecisionTableDetails.modeltoattrvalues.map(function (attr) {
				if (attr.colvalue === "INIT") {
					attr.colvalue = "";
				}
				return attr;
			});
			// String lat = location.getString("latitude");
			// String long = location.getString("longitude");

			// Add the Driving columns to the table 
			arrAttributes = oDecisionTableDetails.modeltodriving;
			if (arrAttributes) {
				oFieldPropertiesDetailsModel.setProperty("/multiValueUI/tableHasRules", true);
			}

			// Read the attribute values and load the model
			let arrAttrValuesSource = oDecisionTableDetails.modeltoattrvalues;

			if (arrAttrValuesSource) {
				oFieldPropertiesDetailsModel.setProperty("/multiValueUI/tableHasRules", true);
			}
			// Sort the data by row numbers 
			arrAttrValuesSource.sort(function (a, b) {
				return parseInt(a.rownum, 10) - parseInt(b.rownum, 10);
			});
			let arrAttrValues = [];
			for (i = 0; i < arrAttrValuesSource.length; i++) {
				let iRowNum = parseInt(arrAttrValuesSource[i].rownum, 10) - 1;
				// initialize the object at the row number if it has not already been
				if (!arrAttrValues[iRowNum]) {
					arrAttrValues[iRowNum] = {};
				}
				if (arrAttrValuesSource[i].colname.includes("___")) {
					arrAttrValuesSource[i].colname = arrAttrValuesSource[i].colname + "_Deriving";
				} else {
					arrAttrValuesSource[i].colname = arrAttrValuesSource[i].colname + (arrAttrValuesSource[i].colname === "WF__STEP" ? "" :
						"_Driving");
						
					if(arrAttrValuesSource[i].operator){
						arrAttrValues[iRowNum][arrAttrValuesSource[i].colname + "-Operator"] = arrAttrValuesSource[i].operator;
					}
				}

				if (arrAttrValuesSource[i].colname !== "WF__STEP" && arrAttrValuesSource[i].colname.includes("_Driving") && arrAttrValuesSource[i].operator !== "CP" && arrAttrValuesSource[i].operator !== "NP" && arrAttrValuesSource[i].colvalue) {
					let entity = arrAttrValuesSource[i].colname.split("__")[0];
					let attribute = arrAttrValuesSource[i].colname.split("__")[1].split("_Driving")[0];
					if (!arrAttrValuesSource[i].colvaluedescr) {
						let oValueList =
							await oController.GetListData.getAttributeValuesList(oController, oFieldPathInfo.DataModel, entity, attribute, arrAttrValuesSource[i].colvalue, true);
						let arrValueList = oValueList;
						/**
						 * Some functions return the data in oData -> results ... check if "results" exist and then point the model to that
						*/
						if(oValueList.results){
							arrValueList = oValueList.results;
						}
						
						if (arrValueList.length > 0) {
							arrAttrValuesSource[i].colvalue = arrValueList[0].Key + (arrValueList[0].Ddtext.length > 0 ? " - " : "") + arrValueList[0].Ddtext;
						}
					}else{
						arrAttrValuesSource[i].colvaluedescr = arrAttrValuesSource[i].colvaluedescr.replace("-BLANK-", "");
						arrAttrValuesSource[i].colvalue += (arrAttrValuesSource[i].colvaluedescr ? " - " + arrAttrValuesSource[i].colvaluedescr: "");
					}
				}

				arrAttrValues[iRowNum][arrAttrValuesSource[i].colname.replace("___", "__")] = arrAttrValuesSource[i].colvalue;
			}

			for (i = 0; i < arrAttributes.length; i++) {
				if (arrAttributes[i].usmdentity !== "WF") {
					/**
					 * Check whether datatype, length, decimals and kind is send as part of read
					 * Changes for task 10874, 10870. 
					 * All save services should send datatype, length, decimals and kind as properties on create Multi Value Rule. Hence, it should be available on read
					 * If details not available, i.e. rule was created before Iteration-9 changes
					 */
					if(arrAttributes[i].attrdatatype && arrAttributes[i].lenghttype && arrAttributes[i].decimals && arrAttributes[i].kind) {
						oController.MultiValueDetailsModel.returnSelectedAttribute(oController, {
							entity: arrAttributes[i].usmdentity,
							attributeDataType: arrAttributes[i].attrdatatype,
							attributeDataLenght: (arrAttributes[i].lenghttype) ? Number(arrAttributes[i].lenghttype) : 0,
							attribute: arrAttributes[i].usmdattribute,
							attributeDecimals: arrAttributes[i].decimals,
							attributeKind: arrAttributes[i].kind,
							attributeType: (arrAttributes[i].usmdattribute.includes("__LENGTH")) ? "Driving-Length" : "Driving"
						});
					} else {
						let promiseAttributeList = 
						oController.GetListData.getEntityAttributeList(oController, oDecisionTableDetails.usmdmodel, arrAttributes[i].usmdentity, arrAttributes[i].usmdattribute);
							
						promiseAttributeList.then(function(arrAttributesList) {
							let oAttributeDetails = arrAttributesList[0];
							let arrMultiValueData = oFieldPropertiesDetailsModel.getProperty("/multiValueUI/data");
							if(arrMultiValueData.length > 0) {
								if(oAttributeDetails.Kind === "C") {
									arrMultiValueData.forEach(oAttributeValue => {
										let sCellValue = oAttributeValue[oAttributeDetails.UsmdEntity + "__" + oAttributeDetails.UsmdAttribute + "_Driving"];
										if (sCellValue) {
											oAttributeValue[oAttributeDetails.UsmdEntity + "__" + oAttributeDetails.UsmdAttribute] = sCellValue.toUpperCase();
										}
									});
								} else if(oAttributeDetails.Kind === "N") {
									arrMultiValueData.forEach(oAttributeValue => {
										let sCellValue = oAttributeValue[oAttributeDetails.UsmdEntity + "__" + oAttributeDetails.UsmdAttribute + "_Driving"];
										if (sCellValue) {
											oAttributeValue[oAttributeDetails.UsmdEntity + "__" + oAttributeDetails.UsmdAttribute] = sCellValue.padStart(Number(oAttributeDetails.Length), 0);
										}
									});
								} else if(oAttributeDetails.Kind === "P") {
									arrMultiValueData.forEach(oAttributeValue => {
										let sCellValue = oAttributeValue[oAttributeDetails.UsmdEntity + "__" + oAttributeDetails.UsmdAttribute + "_Driving"];
										let iCellValue = Number(sCellValue);
										if (sCellValue) {
											oAttributeValue[oAttributeDetails.UsmdEntity + "__" + oAttributeDetails.UsmdAttribute] = iCellValue.toFixed(Number(oAttributeDetails.Decimals));
										}
									});
								}
							}
							oController.MultiValueDetailsModel.returnSelectedAttribute(oController, {
								entity: oAttributeDetails.UsmdEntity,
								attributeDataType: oAttributeDetails.DATATYPE,
								attributeDataLenght: (oAttributeDetails.Length) ? Number(oAttributeDetails.Length) : 0,
								attribute: oAttributeDetails.UsmdAttribute,
								attributeDecimals: oAttributeDetails.Decimals,
								attributeKind: oAttributeDetails.Kind,
								attributeType: (oAttributeDetails.UsmdAttribute.includes("__LENGTH")) ? "Driving-Length" : "Driving"
							});
						});
					}
				}
			}

			// Get Deriving Attributes
			arrAttributes = oDecisionTableDetails.modeltoderiving;
			for (i = 0; i < arrAttributes.length; i++) {
				oController.MultiValueDetailsModel.returnSelectedAttribute(oController, {
					entity: arrAttributes[i].usmdentity,
					attributeDataType: arrAttributes[i].attrdatatype,
					attributeDataLenght: (arrAttributes[i].lenghttype) ? Number(arrAttributes[i].lenghttype) : 0,
					attribute: arrAttributes[i].usmdattribute,
					attributeType: "Deriving"
				});
			}

			// Add the Message columns to the table 
			arrAttributes = oDecisionTableDetails.modeltomessage;
			oFieldPropertiesDetailsModel.setProperty("/multiValueUI/data", arrAttrValues);
		});
	};

	/**
	 * 
	 * Open a popup to choose an Entity and Attribute and then add it to the table. 
	 * 
	 * TODO : Add column count limitations
	 *		> Driving Attributes : Max 5
	 *		> Deriving Attributes : Max 3
	 */
	oMultiValueUIObject.addAttributeColumn = function (oEvent) {
		// Get the event source
		//The Entity should be editable when Attribute Type is Driving
		let sAttributeType = (oEvent.getSource().getId().includes("btnAddDrivingAttribute") ||
			oEvent.getSource().getId().includes("btnAddValidatingAttribute")) ? "Driving" : "Deriving";
		let oFieldPropertiesDetailsModel = this.getView().getModel("fieldPropertiesDetails");
		let sEntityName = oFieldPropertiesDetailsModel.getProperty("/AttributePath").EntityName;
		this.SelectAttributeDialog.openSelectAttributeDialog(this, sAttributeType, sEntityName);
	};

	oMultiValueUIObject.returnSelectedAttribute = function (oController, oColumnInfo) {

		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		let oMultiUITable = sap.ui.getCore().byId("MultiValuePropertyFragmentID" + "--" + "idMultiValueUITable");

		// get the table bindings and update the same 
		// 1. Column bindings
		let arrColumnAggregation = oMultiUITable.getColumns();

		/**
		 * Task 11998 -> Multiple selection of Driving and Deriving ATtributes
		 * Add the first rule by default when user adds the first column
		 */
		//Add first row in table if empty when user selects attribute
		// Get model
		let oFieldPropertiesDetailsModel = this.JSONViewModel;
		let arrTableData = oFieldPropertiesDetailsModel.getProperty("/multiValueUI/data"); // Get the current table data 
		// if the table is null, initialize the array 
		if (!arrTableData || arrTableData.length === 0) {
			arrTableData = [{}];
			// Update the model with new data
			oFieldPropertiesDetailsModel.setProperty("/multiValueUI/data", arrTableData);
			oFieldPropertiesDetailsModel.setProperty("/multiValueUI/tableHasRules", true);
		}

		let oCheckAttributeDup = arrColumnAggregation.filter(function (oEntity) {
			return (oEntity.getCustomData()[0].getValue().entity === oColumnInfo.entity && oEntity.getCustomData()[0].getValue().attribute ===
				oColumnInfo.attribute);
		});

		if (oCheckAttributeDup.length) {
			let promiseShowPopupAlert = Utilities.showPopupAlert(
				"Cannot add attribute " + oColumnInfo.entity + "-> " + oColumnInfo.attribute + ". Attribute already exists.",
				MessageBox.Icon.ERROR, "Duplicate Entry Error");
			promiseShowPopupAlert.then(function () {});
			return;
		}

		if(oColumnInfo.attributeType === "Deriving" && oColumnInfo.attribute === "**") {
			let oDerivingAttributeExists = arrColumnAggregation.filter((oDerivingAttribute) => {
				return (oDerivingAttribute.getCustomData()[0].getValue().entity === oColumnInfo.entity &&
						oDerivingAttribute.getCustomData()[0].getValue().attribute !== "**" &&
						oDerivingAttribute.getCustomData()[0].getValue().attributeType === oColumnInfo.attributeType);
			});
			if(oDerivingAttributeExists.length > 0) {
				oController.MultiValueDetailsModel.confirmAndRemoveDerivingAttributes(oColumnInfo, oDerivingAttributeExists);
			} else {
				oController.MultiValueDetailsModel.addColumnToTable(oController, oColumnInfo);
			}
		} else if(oColumnInfo.attributeType === "Deriving" && oColumnInfo.attribute !== "**") {
			let oDerivingEntityExists = arrColumnAggregation.filter((oDerivingAttribute) => {
				return (oDerivingAttribute.getCustomData()[0].getValue().entity === oColumnInfo.entity &&
						oDerivingAttribute.getCustomData()[0].getValue().attribute === "**" &&
						oDerivingAttribute.getCustomData()[0].getValue().attributeType === oColumnInfo.attributeType);
			});
			if(oDerivingEntityExists.length > 0) {
				oController.MultiValueDetailsModel.confirmAndRemoveDerivingAttributes(oColumnInfo, oDerivingEntityExists);
			} else {
				oController.MultiValueDetailsModel.addColumnToTable(oController, oColumnInfo);
			}
		} else{
			//Returns the count value of all driving columns. Including Workflow Step
			let iDrivingColumnsCount = oController.MultiValueDetailsModel.addColumnToTable(oController, oColumnInfo);
			if (iDrivingColumnsCount > 1) {
				return;
			} else {
				oController.MultiValueDetailsModel.addColumnToTable(
					oController, {
						entity: "WF",
						attribute: "STEP",
						attributeType: "Workflow"
					});
			}
		}		
	};

	/**
	 * Task 11998 - Multiple selection of driving and deriving attributes
	 * Called from SelectAttribute.js on close of fragment
	 * Consists of all multiple attributes and the the duplicate attributes
	 * Enter all unique attributes after promise resolve
	 */
	oMultiValueUIObject.addMultipleAttributes = function(oController, oMultipleAttributesInfo) {
		let oMultiUITable = sap.ui.getCore().byId("MultiValuePropertyFragmentID" + "--" + "idMultiValueUITable");
		let arrColumnAggregation = oMultiUITable.getColumns();
		let oFieldPropertiesDetailsModel = this.JSONViewModel;
		let arrTableData = oFieldPropertiesDetailsModel.getProperty("/multiValueUI/data"); // Get the current table data 
		// if the table is null, initialize the array 
		if (!arrTableData || arrTableData.length === 0) {
			arrTableData = [{}];
			// Update the model with new data
			oFieldPropertiesDetailsModel.setProperty("/multiValueUI/data", arrTableData);
			oFieldPropertiesDetailsModel.setProperty("/multiValueUI/tableHasRules", true);
		}
		if(oMultipleAttributesInfo.arrAttributes && oMultipleAttributesInfo.arrAttributes.length > 0) {
			let oDerivingEntityExists = arrColumnAggregation.filter(oDerivingAttribute => {
				return (oDerivingAttribute.getCustomData()[0].getValue().entity === oMultipleAttributesInfo.arrAttributes[0].entity &&
						oDerivingAttribute.getCustomData()[0].getValue().attribute === "**" &&
						oDerivingAttribute.getCustomData()[0].getValue().attributeType === "Deriving");
			});
			if(oDerivingEntityExists.length > 0 && oMultipleAttributesInfo.sAttributeType === "Deriving") {
				oController.MultiValueDetailsModel.confirmAndRemoveDerivingAttributes(undefined, oDerivingEntityExists, oMultipleAttributesInfo.arrAttributes);
			} else {
				oMultipleAttributesInfo.arrAttributes.forEach(oColumnInfo => {
					oController.MultiValueDetailsModel.addColumnToTable(oController, oColumnInfo);
				});
			}
			/**
			 * Task 11998 - Multiple selection of driving and deriving attributes
			 * Add WF__STEP column upon adding the first set of driving attributes
			 * Add WF__STEP if it has not been added already
			 */
			if(oMultipleAttributesInfo.sAttributeType === "Driving") {
				arrColumnAggregation = oMultiUITable.getColumns();
				let oWFStep = arrColumnAggregation.find(oColumn => {
					let oCustomData = oColumn.getCustomData()[0].getValue();
					if(oCustomData.entity === "WF" && oCustomData.attribute === "STEP" && oCustomData.attributeType === "Workflow") {
						return true;
					}
					return false;
				});
				if(!oWFStep) {
					oController.MultiValueDetailsModel.addColumnToTable(
						oController, {
							entity: "WF",
							attribute: "STEP",
							attributeType: "Workflow"
						});

				}
			}
		}		
	};

	oMultiValueUIObject.addColumnToTable = function (oController, oColumnInfo) {
		// Get model
		let oFieldPropertiesDetailsModel = this.JSONViewModel;

		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		let oMultiUITable = sap.ui.getCore().byId("MultiValuePropertyFragmentID" + "--" + "idMultiValueUITable");

		// get the table bindings and update the same 
		// 1. Column bindings
		let arrColumnAggregation = oMultiUITable.getColumns();

		// Create a new column to be added to the aggregation
		let oColumn = oController.MultiValueDetailsModel._createColumnForTable(oController, oColumnInfo);
		oColumnInfo = oColumn.getCustomData()[0].getValue();

		/** 
		 * if the column is type Driving, insert the column at the end of the driving columns
		 * If the column is type Deriving, insert the column at the end
		 */
		let iInsertIndex = arrColumnAggregation.length;
		if (oColumnInfo.attributeType === "Driving" || oColumnInfo.attributeType === "Driving-Length" || oColumnInfo.attributeType === "Workflow") {
			// find the last driving column 
			for (let i = 0; i < arrColumnAggregation.length; i++) {
				if (arrColumnAggregation[i].getCustomData().length > 0 && arrColumnAggregation[i].getCustomData()[0].getValue().attributeType !==
					"Driving" && arrColumnAggregation[i].getCustomData()[0].getValue().attributeType !== "Driving-Length") {
					iInsertIndex = i;
					break;
				}
			}
		}
		oMultiUITable.insertColumn(oColumn, iInsertIndex);

		// Add cell to the columns based on Index number
		oController.MultiValueDetailsModel.addCellToColumn(oController, oColumnInfo, iInsertIndex);

		oFieldPropertiesDetailsModel.setProperty("/multiValueUI/tableHasColumns", oMultiUITable.getColumns().length > 0);
		let arrColumnAggregationCheck = oMultiUITable.getColumns();
		let oCountDrivingAttr = arrColumnAggregationCheck.filter(function (oEntity) {
			return (oEntity.getCustomData()[0].getValue().attributeType === "Driving" || oEntity.getCustomData()[0].getValue().attributeType === "Driving-Length" ||
					 oEntity.getCustomData()[0].getValue().attributeType === "Workflow");
		});
		oFieldPropertiesDetailsModel.setProperty("/multiValueUI/tableHasDrivingColumns", oCountDrivingAttr.length > 0);

		return oCountDrivingAttr.length;
	};

	oMultiValueUIObject.addCellToColumn = function (oController, oColumnInfo, iInsertIndex) {
		let oComponent = this;
		// Get model
		let oFieldPropertiesDetailsModel = this.JSONViewModel;
		let oFieldPathInfo = oFieldPropertiesDetailsModel.getProperty("/AttributePath");

		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		let oMultiUITable = sap.ui.getCore().byId("MultiValuePropertyFragmentID" + "--" + "idMultiValueUITable");

		// 1. item / cell binding
		let oColumnListItems = oMultiUITable.getBindingInfo("items").template;
		let sValueVariable = oController.MultiValueDetailsModel._getBindingVariableForColumn(oColumnInfo);

		let oHBox = new sap.m.HBox();
		if(!sap.ui.getCore().byId(sValueVariable)){
			oHBox.addItem(new sap.ui.core.InvisibleText({
				text: "Column for: " + oColumnInfo.entity + " - " + oColumnInfo.attribute
			}).toStatic());
		}

		let sValueBinding = "{fieldPropertiesDetails>" + sValueVariable + "}";
		let sOperatorBinding = "{fieldPropertiesDetails>" + sValueVariable + "-Operator}";
		oFieldPropertiesDetailsModel.setProperty("/multiValueUI/lists/" + sValueVariable, []);

		let promiseValueList = undefined;
		let sItemKey = "";
		let sItemText = "";
		let bComboBoxRequired = false;
		let bSelectBoxRequired = false;
		let oInputComponent;
		if (oColumnInfo.attributeType === "Deriving") {
			sItemKey = "statusKey";
			sItemText = "statusText";
			bSelectBoxRequired = true;
			promiseValueList =
				this.ParentController.GetListData.getUIPropertyStatusList(this.ParentController, oColumnInfo.attribute);

		} else if (oColumnInfo.attributeType === "Driving" || oColumnInfo.attributeType === "Driving-Length") {
			sItemKey = "Key";
			sItemText = "Ddtext";
			if (oColumnInfo.attributeType !== "Driving-Length") {
				promiseValueList =
					this.ParentController.GetListData.getAttributeValuesList(
						this.ParentController,
						oFieldPathInfo.DataModel,
						oColumnInfo.entity,
						oColumnInfo.attribute,
						undefined);
			}
			if (oColumnInfo.attributeDataType === "DATS") {

				/* Task 10758 - editable properties were removed from the controls */
				oInputComponent = new sap.m.DatePicker({
					width: "100%",
					value: sValueBinding,
					displayFormat: "M/d/yyyy",
					valueFormat: "yyyyMMdd",
					visible: "{= ${fieldPropertiesDetails>/multiValueUI/lists/" + sValueVariable + "}.length === 0}"
				}).attachChange(oController.MultiValueDetailsModel.changeDatePickerValue, oComponent);

			} else {
				oInputComponent = new sap.m.Input({
					width: "100%",
					fieldWidth: "100%",
					value: sValueBinding,
					valueLiveUpdate: true,					
					// Task 12467 - Add support to entering patterns when CP and NP are selected for Multi Value UI Properties and Business Rules
					// Feature/12185 - Property was commented forr the temporary solution to allow showing a insput field instead combobox
					visible: "{= ${fieldPropertiesDetails>" + sValueVariable + "-Operator} === 'CP' || ${fieldPropertiesDetails>" + sValueVariable + "-Operator} === 'NP' || ${fieldPropertiesDetails>/multiValueUI/lists/" + sValueVariable + "}.length === 0}",
					maxLength: (oColumnInfo.attributeType !== "Driving-Length") ? oColumnInfo.attributeDataLenght : 0,
					/* Bug 11218 - The field type was changed depending on the attribute kind */
					type: (oColumnInfo.attributeType === "Driving-Length") ? "Number" : "Text"
				}).attachChange(oColumnInfo, oController.MultiValueDetailsModel._changeCellValue, oComponent);
}
			
			oInputComponent.addAriaLabelledBy(sValueVariable);
			oInputComponent.setLayoutData(new sap.m.FlexItemData({growFactor: 4}));

			//Feature/12334 - CP and NP were added
			let oSelOperatorComponent = new sap.m.Select({
				autoAdjustWidth: true,
				selectedKey: sOperatorBinding,
				change: function(oEvent){
					// Bug 12606 - Removing value when operator is changed
					let sPath = oEvent.getSource().getParent().getParent().getBindingContext("fieldPropertiesDetails").getPath();
					oFieldPropertiesDetailsModel.setProperty(sPath + "/" + oColumnInfo.entity + "__" + oColumnInfo.attribute + "_" + oColumnInfo.attributeType, "");
				},
				items: [
					new sap.ui.core.Item({
						key: "EQ",
						text: "="
					}),
					new sap.ui.core.Item({
						key: "NE",
						text: "!="
					}),
					new sap.ui.core.Item({
						key: "GT",
						text: ">"
					}),
					new sap.ui.core.Item({
						key: "GE",
						text: ">="
					}),
					new sap.ui.core.Item({
						key: "LT",
						text: "<"
					}),
					new sap.ui.core.Item({
						key: "LE",
						text: "<="
					}),
					new sap.ui.core.Item({
						key: "CP",
						text: "CP"
					}),
					new sap.ui.core.Item({
						key: "NP",
						text: "NP"
					})
				]
			});

			oHBox.addItem(oSelOperatorComponent);
			oHBox.addItem(oInputComponent);
			bComboBoxRequired = (oColumnInfo.attributeType !== "Driving-Length");

		} else if (oColumnInfo.attributeType === "Workflow") {
			sItemKey = "UsmdCreqStep";
			sItemText = "Txtmi";
			bSelectBoxRequired = true;

			//Task 12508 Filter CR Step type properties
			promiseValueList =
				this.ParentController.GetListData.getWorkflowStepList(this.ParentController, oFieldPathInfo.CRName, undefined);
		}

		if (bComboBoxRequired) {
			// Feature/12451 - sOperatorBinding was added to the parameters to obtain the operator
			let oComboBox = oController.MultiValueDetailsModel._createComboBoxForTableRow(sValueBinding, sValueVariable, sItemKey,
				sItemText, oController, oColumnInfo);
				oComboBox.setLayoutData(new sap.m.FlexItemData({growFactor: 4}));
				oComboBox.addAriaLabelledBy(sValueVariable);
				oHBox.addItem(oComboBox);
			
			if (promiseValueList) {
				promiseValueList.then(function (oValueList) {
					let arrValueList = oValueList;
					/**
					 * Some functions return the data in oData -> results ... check if "results" exist and then point the model to that
					 */
					if (oValueList.results) {
						arrValueList = oValueList.results;
					}
					oFieldPropertiesDetailsModel.setProperty("/multiValueUI/lists/" + sValueVariable, arrValueList);
				});
			}
		}

		/**
		 * Bug 12189 - Restrict user to enter free text for Workflow Step and Deriving Attribute
		 * Add UI Component Select Box instead of ComboBox to restrict user entry
		 */
		if (bSelectBoxRequired) {
			let oSelectBox = oController.MultiValueDetailsModel._createSelectBoxForTableRow(sValueBinding, sValueVariable, sItemKey,
				sItemText, oColumnInfo.attributeType);

			oSelectBox.addAriaLabelledBy(sValueVariable);

			oHBox.addItem(oSelectBox);

			if (promiseValueList) {
				promiseValueList.then(function (oValueList) {
					let arrValueList = oValueList;
					/**
					 * Some functions return the data in oData -> results ... check if "results" exist and then point the model to that
					 */
					if (oValueList.results) {
						arrValueList = oValueList.results;
					}
					// Defect 12615 Show Wfstep as 00 instead of 0
					if(oColumnInfo.attributeType === "Workflow") {
						arrValueList.forEach(step => {
							if(step.UsmdCreqStep.length < 2){
								step.UsmdCreqStep = "0" + step.UsmdCreqStep;
							}
						});
					}
					oFieldPropertiesDetailsModel.setProperty("/multiValueUI/lists/" + sValueVariable, arrValueList);
				});
			}
		}

		oColumnListItems.insertCell(oHBox, iInsertIndex);

		oMultiUITable.bindItems({
			path: "/multiValueUI/data",
			model: "fieldPropertiesDetails",
			template: oColumnListItems,
			templateShareable: true
		});
	};

	oMultiValueUIObject._createComboBoxForTableRow = function (sValueBinding, sListDataProperty, sItemKey, sItemText, oComponent, oColumnInfo) {
		let oControl;
		if (oColumnInfo.attributeType === "Driving") {
			oControl = new sap.m.Input({
				width: "100%",
				fieldWidth: "100%",
				value: sValueBinding,
				valueLiveUpdate: true,
				autocomplete: false,
				showSuggestion: true,
				//Feature 12559 - Properties added to show suggestions and to differentiate the input fields
				startSuggestion: 0,
				valueState: "Information",
				valueStateText: "Type to refresh list",
				// Task 12467 - Add support to entering patterns when CP and NP are selected for Multi Value UI Properties and Business Rules
				visible: "{= ${fieldPropertiesDetails>" + sListDataProperty + "-Operator} !== 'CP' && ${fieldPropertiesDetails>" + sListDataProperty + "-Operator} !== 'NP' && ${fieldPropertiesDetails>/multiValueUI/lists/" + sListDataProperty + "}.length !== 0}",
				type: "Text"
			}).attachLiveChange(oColumnInfo, oComponent.MultiValueDetailsModel._liveChangeCellValue, oComponent)
				//Feature 12559 - Event added to fire the liveChange event method
				.addEventDelegate({
					onfocusin: function(oEvent) {
						let sValue = oEvent.srcControl.getValue();
						let bPopoverOpened = oEvent.srcControl._isSuggestionsPopoverOpen();
						//Fix 12562 - Condition was added to not fire the liveChange event when there is a value
						if (!sValue && !bPopoverOpened) {
							oEvent.srcControl.fireLiveChange();
						}
					}
				})
				.attachChange(oComponent.MultiValueDetailsModel.drivingAttributeChangeHandler, oComponent);
		}else{
			/* Add a list box to the cell. Map the data to the global list items */
			oControl = new sap.m.ComboBox({
				selectedKey: sValueBinding,
				width: "100%"
				// Task 12467 - Add support to entering patterns when CP and NP are selected for Multi Value UI Properties and Business Rules
				// visible: "{= ${fieldPropertiesDetails>" + sListDataProperty + "-Operator} !== 'CP' && ${fieldPropertiesDetails>" + sListDataProperty + "-Operator} !== 'NP' && ${fieldPropertiesDetails>/multiValueUI/lists/" + sListDataProperty + "}.length !== 0}"
				// Feature/12185 - visible property was changed for the temporary solution to change combobox to an input field
			});
			
			let sKeyMapping = "{fieldPropertiesDetails>" + sItemKey + "}";
			let sTextMapping = "{fieldPropertiesDetails>" + sItemKey + "}" +
				"{= ${fieldPropertiesDetails>" + sItemText + "}.length > 0 ? ' - ' : ''}" +
				"{fieldPropertiesDetails>" + sItemText + "}";
			let oItemTemplate = new sap.ui.core.Item({
				key: sKeyMapping,
				text: sTextMapping
			});
	
			oControl.bindItems({
				path: "/multiValueUI/lists/" + sListDataProperty,
				model: "fieldPropertiesDetails",
				template: oItemTemplate,
				templateShareable: false,
				length: 2000
			});
		}


		return oControl;
	};

	/**
		 * Bug 12189 - Restrict user to enter free text for Workflow Step and Deriving Attribute
		 * Add UI Component Select Box instead of ComboBox to restrict user entry
	*/
	oMultiValueUIObject._createSelectBoxForTableRow = function (sValueBinding, sListDataProperty, sItemKey, sItemText) {
		/* Add a list box to the cell. Map the data to the global list items */
		let oSelectBox = new sap.m.Select({
			selectedKey: sValueBinding,
			visible: "{= ${fieldPropertiesDetails>/multiValueUI/lists/" + sListDataProperty + "}.length !== 0}"
		}).setLayoutData(new sap.m.FlexItemData({growFactor: 4}));

		let sKeyMapping = "{fieldPropertiesDetails>" + sItemKey + "}";
		let sTextMapping = "{fieldPropertiesDetails>" + sItemKey + "}" +
			"{= ${fieldPropertiesDetails>" + sItemText + "}.length > 0 ? ' - ' : ''}" +
			"{fieldPropertiesDetails>" + sItemText + "}";
		let oItemTemplate = new sap.ui.core.Item({
			key: sKeyMapping,
			text: sTextMapping
		});

		oSelectBox.bindItems({
			path: "/multiValueUI/lists/" + sListDataProperty,
			model: "fieldPropertiesDetails",
			template: oItemTemplate,
			templateShareable: false,
			length: 2000
		});

		return oSelectBox;
	};
	/**
	 * Called with same file. this refers to MultiValueUIDetails.js
	 * Show pop up and delete columns in arrColumnToDelete from the table
	 * Add new column to the table
	 */
	oMultiValueUIObject.confirmAndRemoveDerivingAttributes = function(oColumnInfo, arrColumnToDelete, arrAttributes) {
		let oController = this;

		let promise = new Promise(function (resolve, reject) {
			let sMessage = "";
			if(arrAttributes) {
				sMessage = "Column for all attributes exist. For all attributes(**) will be deleted to add the selected attribute. Continue?";
			} else {
				if(oColumnInfo.attribute === "**") {
					sMessage = "Column for deriving attributes exist. All existing deriving attributes will be deleted to add for all attributes(**). Continue?";
				} else {
					sMessage = "Column for all attributes exist. For all attributes(**) will be deleted to add the selected attribute. Continue?";
				}
			}
			
			// Show a message asking the user to confirm deletion. Exit if cancel 
			let promiseShowPopupAlert = Utilities.showPopupAlert(sMessage, MessageBox
				.Icon.WARNING,
				"Remove Attribute/s?", [MessageBox.Action.YES, MessageBox.Action.NO]);
			promiseShowPopupAlert.then(function () {
				resolve();
			}, function () {
				reject();
			});
		});

		/**
		 * Find the table, iterate through the columns and remove the deriving attribute columns
		 */
		promise.then(function () {
			// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
			let oMultiUITable = sap.ui.getCore().byId("MultiValuePropertyFragmentID" + "--" + "idMultiValueUITable");
			// Get the item definitions 	
			let oColumnListItems = oMultiUITable.getBindingInfo("items").template;
			// Get model 
			let oFieldPropertiesDetailsModel = oController.JSONViewModel;
			let arrMultiValueData = oFieldPropertiesDetailsModel.getProperty("/multiValueUI/data");
			let arrColumnAggregation = oMultiUITable.getColumns();
			/**
			 * Task 11998 - Multiple selection of driving and deriving attributes
			 * Added a reverse Iteration counter to make sure that all deriving attribute columns are deleted when ** is added and confirmed
			 */
			for(let iColumn = arrColumnAggregation.length - 1; iColumn >= 0; iColumn--) {
				if(arrColumnToDelete.includes(arrColumnAggregation[iColumn])) {
					oMultiUITable.removeColumn(iColumn);
					oColumnListItems.removeCell(iColumn);
					let sValueVariable = oController._getBindingVariableForColumn({
						entity: arrColumnAggregation[iColumn].getCustomData()[0].getValue().entity,
						attribute: arrColumnAggregation[iColumn].getCustomData()[0].getValue().attribute,
						attributeType: arrColumnAggregation[iColumn].getCustomData()[0].getValue().attributeType
					});
					//Clear all cell values for the deleted column
					arrMultiValueData.forEach(oData => {
						delete oData[sValueVariable];
					});
				}
			}
			/**
			 * Task 11998 - Multiple selection of driving and deriving attributes
			 * On promise resolve, add all the deriving attributes to the table
			 * 		Show error message for duplicate attributes added by the user
			 */
			if(arrAttributes) {
				arrAttributes.forEach(oAttrInfo => {
					oController.addColumnToTable(oController.ParentController, oAttrInfo);
				});
			} else {
				oController.addColumnToTable(oController.ParentController, oColumnInfo);
			}
			
		});
	};

	/**
	 * Executed in Parent controller context. this = UIPropertiesDetailsController 
	 * 
	 * Delete the selected column from the table.
	 */
	oMultiValueUIObject.removeAttributeColumn = function (oEvent, oColumnInfo) {

		let oController = this;

		let promise = new Promise(function (resolve, reject) {
			// Show a message asking the user to confirm deletion. Exit if cancel 
			let promiseShowPopupAlert = Utilities.showPopupAlert("The attribute will be deleted from the decision table. Continue?", MessageBox
				.Icon.WARNING,
				"Remove Attribute?", [MessageBox.Action.YES, MessageBox.Action.NO]);
			promiseShowPopupAlert.then(function () {
				resolve();
			}, function () {
				reject();
			});
		});

		/**
		 * Find the table, iterate through the columns and remove the matched column
		 */
		promise.then(function () {
			// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
			let oMultiUITable = sap.ui.getCore().byId("MultiValuePropertyFragmentID" + "--" + "idMultiValueUITable");

			// Get the item definitions 	
			let oColumnListItems = oMultiUITable.getBindingInfo("items").template;
			// Get model 
			let oFieldPropertiesDetailsModel = oController.getView().getModel("fieldPropertiesDetails");

			// get the table bindings and update the same 
			// 1. Column bindings
			let arrColumnAggregation = oMultiUITable.getColumns();
			let sValueVariable = oController.MultiValueDetailsModel._getBindingVariableForColumn(oColumnInfo);
			let arrMultiValueData = oFieldPropertiesDetailsModel.getProperty("/multiValueUI/data");
			for (let iColumn = 0; iColumn < arrColumnAggregation.length; iColumn++) {
				let oCustomData = arrColumnAggregation[iColumn].getCustomData();
				if (oCustomData.length > 0 &&
					(oCustomData[0].getValue().attributeType === oColumnInfo.attributeType) &&
					(oCustomData[0].getValue().entity === oColumnInfo.entity) &&
					(oCustomData[0].getValue().attribute === oColumnInfo.attribute)
				) {
					oMultiUITable.removeColumn(iColumn);
					oColumnListItems.removeCell(iColumn);

					//Clear all cell values for the deleted column
					arrMultiValueData.forEach(oData => {
						delete oData[sValueVariable];
					});

					let arrColumnAggregationCheck = oMultiUITable.getColumns();
					let oDrivingAttr = arrColumnAggregationCheck.filter(function (oEntity) {
						return (oEntity.getCustomData()[0].getValue().attributeType === "Driving" || oEntity.getCustomData()[0].getValue().attributeType === "Driving-Length"); //|| oEntity.getCustomData()[0].getValue().attributeType === "Workflow"
					});

					oFieldPropertiesDetailsModel.setProperty("/multiValueUI/tableHasDrivingColumns", oDrivingAttr.length > 0);

					//If no more driving attributes exists, then remove all attributes except for Deriving Attribute (i.e. Workflow Step)
					if (!oDrivingAttr || oDrivingAttr.length === 0) {
						arrColumnAggregationCheck.forEach((oEntity) => {
							let oCustData = oEntity.getCustomData();
							if (oCustData[0].getValue().attributeType === "Workflow") {
								oMultiUITable.removeColumn(0);
								oColumnListItems.removeCell(0);

								sValueVariable = oController.MultiValueDetailsModel.
								_getBindingVariableForColumn({
									entity: oCustData[0].getValue().entity,
									attribute: oCustData[0].getValue().attribute,
									attributeType: oCustData[0].getValue().attributeType
								});

								//Clear all cell values for the deleted column
								arrMultiValueData.forEach(oData => {
									delete oData[sValueVariable];
								});
							}
						});
					}

					if (oMultiUITable.getColumns().length === 0) {
						oFieldPropertiesDetailsModel.setProperty("/multiValueUI/data", []);
					}

					oMultiUITable.bindItems({
						path: "/multiValueUI/data",
						model: "fieldPropertiesDetails",
						template: oColumnListItems,
						templateShareable: true
					});

					oFieldPropertiesDetailsModel.setProperty("/multiValueUI/tableHasColumns", oMultiUITable.getColumns().length > 0);
					break;
				}
			}
		});
	};

	/* Bug 11218 - 	Condition "N" was modified to not allow letters and spetial characters
					Condition "P" was modified to not allow NaN value
					Condition "I" was added to rounded the Int values
					Condition "F" was added to compare the value with a regular expression 
					Message was added to inform about wrong value and when this occurs the field is cleaned*/
	oMultiValueUIObject._changeCellValue = function (oEvent, oColumnInfo) {
		let oFieldPropertiesDetailsModel = this.JSONViewModel;
		let sPath = oEvent.getSource().getParent().getParent().getBindingContext("fieldPropertiesDetails").getPath();
		// Bug 12606 - Operator is obtained to evaluate which regex needs to be used
		let sOperator = oFieldPropertiesDetailsModel.getProperty(sPath + "/" + oColumnInfo.entity + "__" + oColumnInfo.attribute + "_" + oColumnInfo.attributeType + "-Operator");
		let regexNum = "^[0-9]+$";
		let regexFLTP = "^([+-]?\\.?[0-9]+([+-]?(e|E))?)+$";
		// Bug 12606 - Regex for Pattern were added
		let regexNumPattern = "^(\\*?[0-9]+\\*?)+$";
		let regexDecPattern = "^(\\*?\\.?[0-9]+\\.?\\*?)+$";
		let regexFLTPPattern = "^(\\*?[+-]?\\.?[0-9]*\\*?([+-]?(e|E))?\\*?)+$";
		let sCellValue = oEvent.getParameter("value");
		let iCellValue = Number(sCellValue);
		let flagInvalid = false;
		if(oColumnInfo.attributeKind === "C") {
			oEvent.getSource().setValue(sCellValue.toUpperCase());
		} else if(oColumnInfo.attributeKind === "N") {
			// Bug 12606 - Conditions were added to evaluate if the selected operator was CP or NP and use other regex which allows asterisks
			if (sOperator === "CP" || sOperator === "NP") {
				if(!sCellValue.match(regexNumPattern) && sCellValue.trim() !== ""){
					oEvent.getSource().setValue();
					flagInvalid = true;
				}
			}else{
				if(iCellValue.toFixed(0).toString().match(regexNum) && sCellValue.trim() !== ""){
					oEvent.getSource().setValue(iCellValue.toFixed(0).toString().padStart(Number(oColumnInfo.attributeDataLenght), 0));
				}else{
					oEvent.getSource().setValue();
					flagInvalid = true;
				}
			}
		} else if(oColumnInfo.attributeKind === "P") {
			if (sOperator === "CP" || sOperator === "NP") {
				if(!sCellValue.match(regexDecPattern) && sCellValue.trim() !== ""){
					oEvent.getSource().setValue();
					flagInvalid = true;
				}
			}else{
				if(isNaN(iCellValue) || sCellValue.trim() === ""){
					oEvent.getSource().setValue();
					flagInvalid = true;
				}else{
					let cellValueFixed = iCellValue.toFixed(Number(oColumnInfo.attributeDecimals)).toString().replace(".", "");
					// Task 11217 - Value Conversion and Data Check for Single value rules and properties
					// This fixed the bug for BP_SALES.ANTLF Attribute, which is Decimal with lenght 1
					if (cellValueFixed.length === 1 && Number(oColumnInfo.attributeDataLenght) === 1) {
						oEvent.getSource().setValue(Number(iCellValue));
					} else if(cellValueFixed.length >= Number(oColumnInfo.attributeDataLenght)) {
						// Bug 12157 - Input validation for BP_COMPNY - KULTG
						// Only apply this formula if the data type is decimal and oColumnInfo.attributeDecimals is greater than zero
						if (Number(oColumnInfo.attributeDecimals) > 0) {
							let newValue = cellValueFixed.slice(0, (oColumnInfo.attributeDataLenght - (Number(oColumnInfo.attributeDecimals) + 1))) + "."
											+ cellValueFixed.slice((oColumnInfo.attributeDataLenght - (Number(oColumnInfo.attributeDecimals) + 1)), oColumnInfo.attributeDataLenght - 1);
							oEvent.getSource().setValue(Number(newValue).toFixed(Number(oColumnInfo.attributeDecimals)));
						}
					}else{
						oEvent.getSource().setValue(iCellValue.toFixed(Number(oColumnInfo.attributeDecimals)));
					}
				}
			}
		} else if(oColumnInfo.attributeKind === "I" || oColumnInfo.attributeKind === "D" || oColumnInfo.attributeKind === "T") {
			if(iCellValue.toFixed(0).toString().match(regexNum) && sCellValue.trim() !== ""){
				oEvent.getSource().setValue(iCellValue.toFixed(0));
			}else{
				oEvent.getSource().setValue();
				flagInvalid = true;
			}
		} else if(oColumnInfo.attributeKind === "F"){
			if (((sOperator === "CP" || sOperator === "NP") && sCellValue.match(regexFLTPPattern)) || sCellValue.match(regexFLTP) && sCellValue.trim() !== "") {
				oEvent.getSource().setValue(sCellValue.toUpperCase());
			}else{
				oEvent.getSource().setValue();
				flagInvalid = true;
			}
		}

		if(flagInvalid){			
			MessageToast.show("Enter a valid value", {
				at: "center top",
				offset: "0 200"
			});
		}
		
	};

	// Feature/12185 - _liveChangeCellValue created to request new suggestion values in the momment that the value is changed
	oMultiValueUIObject._liveChangeCellValue = function (oEvent, oColumnInfo) {
		let oInput = oEvent.getSource();
		let sCellValue = oEvent.getParameter("value");
		clearTimeout(this.timeout);
		this.timeout = setTimeout(() => {			
			let oFieldPropertiesDetailsModel = this.getView().getModel("fieldPropertiesDetails");
			let oFieldPathInfo = oFieldPropertiesDetailsModel.getProperty("/AttributePath");
			oInput.removeAllSuggestionItems();
			let promiseValueList =
						this.GetListData.getAttributeValuesList(
							this,
							oFieldPathInfo.DataModel,
							oColumnInfo.entity,
							oColumnInfo.attribute,
							sCellValue);
			
			promiseValueList.then(function (oValueList) {
				let arrValueList = oValueList;
				/**
				 * Some functions return the data in oData -> results ... check if "results" exist and then point the model to that
				 */
				if(oValueList.results){
					arrValueList = oValueList.results;
				}
				arrValueList.forEach(item => {
					oInput.addSuggestionItem(new sap.ui.core.Item({
						key: item.Key,
						text: item.Key + (item.Ddtext.length > 0 ? " - " : "") + item.Ddtext
					}));
				});

				// Bug 13190 - Execute change event once the suggestion list is ready
				oInput.fireChange({value: sCellValue});
			});
		}, 300);
	};

	oMultiValueUIObject.drivingAttributeChangeHandler = function(oEvent) {
		let oValidatedComboBox = oEvent.getSource();
		let aSuggestionItems = oValidatedComboBox.getSuggestionItems();
		let sValue = oValidatedComboBox.getValue();
		let oSelectedItem = aSuggestionItems.find(item => item.getText() === sValue);
		// Fix 12574 - Condition was changed to validate the selected value
		if (sValue && !oSelectedItem) {
			oValidatedComboBox.setValueState("Warning");
			oValidatedComboBox.setValueStateText("The selected value is not in the list");
		}else{
			oValidatedComboBox.setValueState("Information");
			oValidatedComboBox.setValueStateText("Type to refresh list");
		}
	};

	oMultiValueUIObject.changeDatePickerValue = function (oEvent) {
		let oDatePicker = oEvent.getSource();
		let flagValid = oDatePicker.isValidValue();
		if (!flagValid) {
			oDatePicker.setValue();
			MessageToast.show("Enter correct format for dates", {
				at: "center top",
				offset: "0 200"
			});
		}
	};

	oMultiValueUIObject._getBindingVariableForColumn = function (oColumnInfo) {
		let sValueVariable;

		if (oColumnInfo.attributeType === "Workflow") {
			sValueVariable = oColumnInfo.entity + "__" + oColumnInfo.attribute;
		} else if (oColumnInfo.attributeType === "Driving" || oColumnInfo.attributeType === "Driving-Length" || oColumnInfo.attributeType === "Deriving") {
			sValueVariable = oColumnInfo.entity + "__" + oColumnInfo.attribute + ((oColumnInfo.attributeType === "Driving" || oColumnInfo.attributeType === "Driving-Length") 
																						? "_Driving" : "_Deriving");
		}

		return sValueVariable;
	};

	/**
	 * Called in Parent controller Context. 
	 * 
	 * Return the number of columns in the table. 
	 */
	oMultiValueUIObject.getRuleButtonState = function (bTableHasData, bTableHasDrivingColumns, bTableHasRules) {
		let oFieldPropertiesDetailsModel = this.getView().getModel("fieldPropertiesDetails");
		let sLockedActions = oFieldPropertiesDetailsModel.getProperty("/lockedActions");
		let bNewRule = oFieldPropertiesDetailsModel.getProperty("/CreateNew");
		if ((sLockedActions?.includes("E") && !bNewRule) || (sLockedActions?.includes("S") && bNewRule)) {
			return false;
		}else if (bTableHasRules === undefined) {
			bTableHasRules = false;
		}
		let bState = (bTableHasData) && (bTableHasDrivingColumns) && (bTableHasRules);
		return bState;
	};

	/**
	 * Internal function to create a column that will be added to the table. 
	 * 
	 * The controller handle and the column info are sent to this function as parameters. 
	 * Return : sap.m.Column with required data set
	 */
	oMultiValueUIObject._createColumnForTable = function (oController, oColumnInfo) {
		// Create a new column to be added to the aggregation

		let oHBox = new sap.m.HBox({
			alignItems: "Center",
			justifyContent: "SpaceBetween"
		});
		let oColumnLabel;
		oColumnLabel = new sap.m.Label({
			text: oColumnInfo.entity + " - " + oColumnInfo.attribute,
			wrapping: true,
			width: "100%",
			textAlign: sap.ui.core.TextAlign.Center,
			design: (oColumnInfo.attributeType === "Driving" || oColumnInfo.attributeType === "Driving-Length") ? sap.m.LabelDesign.Bold : sap.m.LabelDesign.Standard,
			required: (oColumnInfo.entity === "WF" ? true : false),
			tooltip: "Column for: " + oColumnInfo.entity + " - " + oColumnInfo.attribute
		}).addStyleClass("tableColumnHeader" + oColumnInfo.attributeType);

		oHBox.addItem(oColumnLabel);

		// add the column delete button if the column is of type Driving or Deriving 
		if ((oColumnInfo.attributeType === "Driving" || oColumnInfo.attributeType === "Driving-Length" || oColumnInfo.attributeType === "Deriving") && (oColumnInfo.entity !== "WF")) {
			let oDeleteColBtn = new sap.m.Button({
				icon: "sap-icon://sys-cancel",
				type: "Transparent",
				tooltip: "Remove Column and it's contents"
			}).attachPress(oColumnInfo, oController.MultiValueDetailsModel.removeAttributeColumn, oController);

			oHBox.addItem(oDeleteColBtn);
		}

		let oColumn = new sap.m.Column({
			demandPopin: true,
			popinDisplay: "Inline",
			minScreenWidth: "Large",
			importance: "Medium",
			width: "20em",
			styleClass: "tableColumn" + oColumnInfo.attributeType,
			hAlign: "Center",
			header: oHBox
		});

		// Add the column custom data that differentiates the column type (Deriving / Driving)
		oColumn.addCustomData(new sap.ui.core.CustomData({
			key: "ColumnType",
			value: oColumnInfo
		}));

		return oColumn;
	};

	/**
	 * Executed in controller context. this = UIDetailsController
	 * 
	 * 1. Disable edit on all rows 
	 * 2. Identify the selected row
	 * 3. Enable editing on current row
	 */
	oMultiValueUIObject.selectionChanged = function (oEvent) {
		// Get the selected item 
		let oSelectedRow = oEvent.getParameters().listItem;
		let iSelectedIndex = oEvent.getSource().indexOfItem(oSelectedRow);

		// Get the data model
		let oFieldPropertiesDetailsModel = this.getView().getModel("fieldPropertiesDetails");
		// get the data for the table
		let arrTableData = oFieldPropertiesDetailsModel.getProperty("/multiValueUI/data");

		// Loop through the data and set editable to false 
		arrTableData.forEach(function (rowData) {
			rowData.editable = false;
		});

		// If nothing is selected, return (unlikely scenario)
		if (iSelectedIndex === -1) {
			return;
		}

		// Set the selected row to editable
		arrTableData[iSelectedIndex].editable = true;

		//Get attribute List to check for entity qualifying key
		let attrList = oFieldPropertiesDetailsModel.getProperty("/attributeList");

		//Get entity List to check for type 4 entity
		let entityList = oFieldPropertiesDetailsModel.getProperty("/SelectAttributeDialog/entityList");

		let isType4;
		let isKey;
		let sValueVariable;
		let orgList = {};
		let entArr = [];

		jQuery.extend(true, orgList, oFieldPropertiesDetailsModel.getProperty("/multiValueUI/lists/"));

		//Copy the orginial selection values in case row is deleted
		if (!oFieldPropertiesDetailsModel.getProperty("/multiValueUI/org_lists/")) {
			oFieldPropertiesDetailsModel.setProperty("/multiValueUI/org_lists/", orgList);
		}

		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		let oMultiUITable = sap.ui.getCore().byId("MultiValuePropertyFragmentID" + "--" + "idMultiValueUITable");

		// get the table bindings and update the same 
		// 1. Column bindings
		let arrColumnAggregation = oMultiUITable.getColumns();
		for (let iColumn = 0; iColumn < arrColumnAggregation.length; iColumn++) {
			let oCustomData = arrColumnAggregation[iColumn].getCustomData();
			if (oCustomData.length > 0) {
				if (!entArr.includes(oCustomData[0].getValue().entity)) {
					let currAttr = attrList.find(function (attr) {
						return attr.UsmdEntity === oCustomData[0].getValue().entity;
					});
					if (!currAttr) {
						entArr.push(oCustomData[0].getValue().entity);
					}
				}
			}
		}
		if (entityList) {
			entArr.some(function (entName) {
				isType4 = entityList.findIndex(function (entity) {
					return entity.UsmdEntity === entName && entity.UsageType === "4";
				}) > -1;

				return isType4 === true;
			});
		}

		if (isType4) {
			arrTableData.forEach(function (rowData) {
				//get all the property names from the TableRow
				let tableKeyArr = [];
				for (let propertyName in rowData) {
					tableKeyArr.push(propertyName);
				}

				//Check if the deriving attribute is an entity qualifying key
				tableKeyArr.forEach(function (rowKey) {
					if (rowKey !== "editable") {
						let rowKeyArr = rowKey.split("__");
						sValueVariable = rowKey;
						isKey = attrList.find(function (attr) {
							return attr.IsKey === "X" && attr.UsmdAttribute === rowKeyArr[1] && attr.UsmdEntity === rowKeyArr[0];
						});
					}
				});
			});

			//Remove selected entries from previous rows from available entries for qualifying entities
			if (isKey) {
				let dataExist;

				let attrArrData = oFieldPropertiesDetailsModel.getProperty("/multiValueUI/org_lists/" + sValueVariable);

				let attrAry = attrArrData.filter(function (el) {
					dataExist = false;
					arrTableData.forEach(function (rowData) {
						if (rowData[sValueVariable] === el.Key) {
							dataExist = true;
						}
					});
					return !dataExist;
				});
				oFieldPropertiesDetailsModel.setProperty("/multiValueUI/lists/" + sValueVariable, attrAry);
			} else {
				//Set the selected list back to original if no key is found
				oFieldPropertiesDetailsModel.setProperty("/multiValueUI/lists/" + sValueVariable, oFieldPropertiesDetailsModel.getProperty(
					"/multiValueUI/org_lists/" +
					sValueVariable));
			}
		} else {
			//Set the selected list back to original if no key is found
			oFieldPropertiesDetailsModel.setProperty("/multiValueUI/lists/", oFieldPropertiesDetailsModel.getProperty("/multiValueUI/org_lists/"));
		}
		oFieldPropertiesDetailsModel.setProperty("/multiValueUI/data", arrTableData);

	};


	/**
	 * Executed in Parent controller context. this = UIPropertiesDetails
	 * 
	 * Add a new row to the rules table with no content. 
	 */
	oMultiValueUIObject.addNewRule = function () {
		// Get model 
		let oFieldPropertiesDetailsModel = this.getView().getModel("fieldPropertiesDetails");

		// Get the current table data 
		let arrTableData = oFieldPropertiesDetailsModel.getProperty("/multiValueUI/data");

		// if the table is null, initialize the array 
		if (!arrTableData) {
			arrTableData = [];
		}

		arrTableData.push({});

		// Update the model with new data
		oFieldPropertiesDetailsModel.setProperty("/multiValueUI/data", arrTableData);
		oFieldPropertiesDetailsModel.setProperty("/multiValueUI/tableHasRules", arrTableData.length > 0);
	};


	/* Task 10759 - enableDeleteRows method was removed, now all the time deletion is enabled */


	/**
	 * Executed in parent controller context. this = UIPropertiesDetailsController
	 * 
	 * Confirm if the row needs to be delete and delete if YES
	 */
	oMultiValueUIObject.deleteRow = function (oEvent) {
		let oController = this;

		/**
		 * Task 11998 - Multiple Selection of Driving and Deriving Attribues
		 * After addition of Table popin, at least 1 rule should be added to the table for all columns to be visible
		 * If user deletes the last row, show appropriate warning message, delete row and add a blank row to the table
		 */

		// Get model 
		let oFieldPropertiesDetailsModel = oController.getView().getModel("fieldPropertiesDetails");
		let arrTableData = oFieldPropertiesDetailsModel.getProperty("/multiValueUI/data");
		let oTable = oEvent.getSource();
		let oClickedItem = oEvent.getParameters().listItem;

		let promise = new Promise(function (resolve, reject) {
			let sMessage = "";
			let sTitle = "";
			if(arrTableData.length === 1) {
				sMessage = "Deletion of the rule is not possible since table contents cannot be empty. Do you want to refresh the rule?";
				sTitle = "Clear Rule";
			} else {
				sMessage = "Rule will be deleted. Continue?";
				sTitle = "Delete Rule";
			}
			let promiseShowPopupAlert = Utilities.showPopupAlert(sMessage, MessageBox.Icon.WARNING, sTitle, [sap.m.MessageBox.Action.YES, sap.m.MessageBox.Action.NO]);
			promiseShowPopupAlert.then(function () {
				resolve();
			}, function () {
				reject();
			});
		});

		/**
		 * Get the index of the row being deleted and remove from the model. 
		 */
		promise.then(function () {

			// Get the index clicked
			let iClickedIndex = oTable.getItems().indexOf(oClickedItem);
			arrTableData.splice(iClickedIndex, 1);

			// if the table is null, initialize the array 
			if (!arrTableData || arrTableData.length === 0) {
				arrTableData = [{}];
			}

			oFieldPropertiesDetailsModel.setProperty("/multiValueUI/data", arrTableData);
			oFieldPropertiesDetailsModel.setProperty("/multiValueUI/tableHasRules", arrTableData.length > 0);
		});
	};

	/**
	 * Executed in parent controller context. this = UIPropertiesDetailsController
	 * 
	 * Delete function runs on click of Delete
	 */
	oMultiValueUIObject.onRuleDeletePressed = function () {

		let oController = this;
		let promiseShowPopupAlert;

		// Server data objects 
		let oServerData = {};

		// Get model 
		let oFieldPropertiesDetailsModel = oController.getView().getModel("fieldPropertiesDetails");
		let oFieldPathInfo = oFieldPropertiesDetailsModel.getProperty("/AttributePath");

		//Display Error Message as User is trying to delete a rule which has not been created
		if (oFieldPathInfo.AppName === undefined) {
			Utilities.showPopupAlert("Error: Cannot delete an UI property which has not been created.", MessageBox.Icon.ERROR,
				"Action not possible");

			return;
		}

		// The Write parameter object ... Callbacks and error management
		let oWriteParameterObject = {
			success: function (oDataResult) {
				let arrResults = oDataResult.modeltomessage.results;
				// Check the message status and notify the result 
				if (arrResults[0].MessageType === "S") // Success
				{
					promiseShowPopupAlert = Utilities.showPopupAlert(arrResults[0].Message, MessageBox.Icon.SUCCESS, "Delete UI Property");
					promiseShowPopupAlert.then(function () {});
					// Notify the delete status to parent component
					oController.notifyUIPropertyDeleted();
					oController.getView()
						.getModel("fieldPropertiesDetails")
						.setProperty("/transport", null);
				} else // Error
				{
					promiseShowPopupAlert =
						Utilities.showPopupAlert(arrResults[0].Message, MessageBox.Icon.ERROR, "Delete UI Property");
					promiseShowPopupAlert.then(function () {});
				}

				sap.ui.core.BusyIndicator.hide();
			},
			error: function () {
				sap.ui.core.BusyIndicator.hide();
				promiseShowPopupAlert =
					Utilities.showPopupAlert("Property could not be deleted.", MessageBox.Icon.ERROR, "Delete UI Property");
				promiseShowPopupAlert.then(function () {});
			}
		};
		//Indicator to refresh the field properties list
		oFieldPropertiesDetailsModel.setProperty("/deleteRule", "X");

		oController._getSelectedTransportPackage(true, false, false)
		.then(function(oSelectedResponse){

			// Fill the data into the target object to be sent to the server 
			oServerData.usmdmodel = oFieldPathInfo.DataModel;
			oServerData.usmdcreqtype = oFieldPathInfo.CRName;
			//oServerData.usmddevclass = sSelectedPackage;
			oServerData.usmdentity = oFieldPathInfo.EntityName;
			oServerData.delete = "X";
			oServerData.appname = oFieldPathInfo.AppName;
			oServerData.CustTransport = oSelectedResponse.customizingTransport;
			oServerData.WbTransport ="";

			oServerData.modeltomessage = [];
			sap.ui.core.BusyIndicator.show(300);

			let oDataModel = oController.getModel("CREATE_MULTI_UI_PROPERTY");
			oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
			oDataModel.create("/modelSet", oServerData, oWriteParameterObject);

		});
	};

	/**
	 * Executed in parent Controller context. this = UIDetailsController
	 * 
	 * 1. Get the table component 
	 * 2. Retrieve the list of columns and fill their data into an array 
	 * 3. Retrieve the data from the table, 
	 * 4. Map the column info and data into the target json
	 */
	oMultiValueUIObject.saveMultiUIValueConfiguration = function () {
		let oController = this;
		let iColumn = 0;
		let promiseShowPopupAlert;
		// Server data objects 
		let oServerData = {};

		/** Array to hold the column information 
		 * 1. attributeType: "Driving"
		 * 2. entity: "AD_EMAIL"
		 * 3. attribute: "E_ADDRESS"
		 */
		let arrColumnInfo = [];

		// Get model 
		let oFieldPropertiesDetailsModel = oController.getView().getModel("fieldPropertiesDetails");
		let oFieldPathInfo = oFieldPropertiesDetailsModel.getProperty("/AttributePath");

		// get the data from the model 
		let arrTableData = oFieldPropertiesDetailsModel.getProperty("/multiValueUI/data");

		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		let oMultiUITable = sap.ui.getCore().byId("MultiValuePropertyFragmentID" + "--" + "idMultiValueUITable");

		// Get the columns 
		let arrColumns = oMultiUITable.getColumns();

		// loop through the columns and fill the data array 
		for (iColumn = 0; iColumn < arrColumns.length; iColumn++) {
			let oCustomData = arrColumns[iColumn].getCustomData()[0].getValue();

			arrColumnInfo[iColumn] = {
				attributeType: oCustomData.attributeType,
				entity: oCustomData.entity,
				attribute: oCustomData.attribute,
				attributeDataType: oCustomData.attributeDataType,
				attributeDataLenght: oCustomData.attributeDataLenght,
				attributeDecimals: oCustomData.attributeDecimals,
				attributeKind: oCustomData.attributeKind
			};
		}

		// The Write parameter object ... Callbacks and error management
		let oWriteParameterObject = {
			success: function (oDataResult) {
				let arrMessages = [];
				let sSaveError = "";
				jQuery.extend(true, arrMessages, oDataResult.modeltomessage.results);
				// Sort the messages
				arrMessages.sort(function (m1) {
					if (m1.MessageType === "E") {
						sSaveError = "Yes";
						return -1;
					}
					if (m1.MessageType === "W") {
						return 1;
					}
					return 0;
				});
				if (sSaveError === "") {
					// Loop through the data and set editable to false 
					oFieldPropertiesDetailsModel.setProperty("/multiValueUI/data", arrTableData);
					// Notify the create status to the parent component
					oController.notifyUIPropertySaved();
				}
				oController.ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
				sap.ui.core.BusyIndicator.hide();
			},
			error: function () {
				sap.ui.core.BusyIndicator.hide();
				promiseShowPopupAlert =
					Utilities.showPopupAlert("Property could not be saved/updated.", MessageBox.Icon.INFORMATION, "Multi Value UI Property");
				promiseShowPopupAlert.then(function () {});
			}
		};

		//Proceed to next step after user selects the Transport Request
		oFieldPathInfo.RuleType = "2";
		let promiseBAdI = oController.getSelectedBADIDialog(oController, oFieldPathInfo);
		promiseBAdI.then(function(oSelectedBAdI){
			oServerData.badiimpl  = oSelectedBAdI.BADISelected;
			oServerData.newbadi  = oSelectedBAdI.isNewBADI;
			return oController._getSelectedTransportPackage(true, true, true);
		})
		.then(function(oSelectedResponse){
			oServerData.CustTransport = oSelectedResponse.customizingTransport;
			oServerData.WbTransport = oSelectedResponse.workbenchTransport;
			oServerData.usmddevclass = oSelectedResponse.package;

			// Fill the data into the target object to be sent to the server 
			oServerData.usmdmodel = oFieldPathInfo.DataModel;
			oServerData.usmdcreqtype = oFieldPathInfo.CRName;
			oServerData.usmdentity = oFieldPathInfo.EntityName;
			oServerData.appname = oFieldPathInfo.AppName;
			let sUserRuleName = oFieldPropertiesDetailsModel.getProperty("/multiValueUI/userRuleName"); //Task 12487 - Add User Rule Name to Rules/Properties
			oServerData.userrulename = sUserRuleName ? sUserRuleName : undefined;

			// Fill the Driving and Deriving Column details 
			oServerData.modeltodriving = [];
			oServerData.modeltoderiving = [];
			let arrColumnModel;
			for (iColumn = 0; iColumn < arrColumnInfo.length; iColumn++) {
				arrColumnModel =
					arrColumnInfo[iColumn].attributeType === "Driving" ||
					arrColumnInfo[iColumn].attributeType === "Driving-Length" ||
					arrColumnInfo[iColumn].attributeType === "Workflow" ?
					oServerData.modeltodriving : oServerData.modeltoderiving;
				arrColumnModel.push({
					usmdentity: arrColumnInfo[iColumn].entity,
					usmdattribute: arrColumnInfo[iColumn].attribute,
					colnum: (iColumn + 1).toString(),
					Attrdatatype: arrColumnInfo[iColumn].attributeDataType,
					Lenghttype: ((arrColumnInfo[iColumn].attributeDataLenght) ? (arrColumnInfo[iColumn].attributeDataLenght).toString() : ""),
					Decimals: arrColumnInfo[iColumn].attributeDecimals,
					Kind: arrColumnInfo[iColumn].attributeKind
				});
			}

			// Fill in the data to be sent 
			oServerData.modeltoattrvalues = [];
			for (let iRows = 0; iRows < arrTableData.length; iRows++) {
				for (iColumn = 0; iColumn < arrColumnInfo.length; iColumn++) {
					let sColName;
					if (arrColumnInfo[iColumn].entity !== "WF") {
						sColName = arrColumnInfo[iColumn].entity + "__" + arrColumnInfo[iColumn].attribute + (arrColumnInfo[iColumn].attributeType ===
							"Driving" || arrColumnInfo[iColumn].attributeType === "Driving-Length" ? "_Driving" : "_Deriving");
					} else {
						sColName = arrColumnInfo[iColumn].entity + "__" + arrColumnInfo[iColumn].attribute;
					}

					let sOperatorValue = "EQ";
					let sColValue = "INIT";
					if (arrTableData[iRows][sColName]) {
						sColValue = arrTableData[iRows][sColName];
					}
					if (arrColumnInfo[iColumn].entity === "WF") {
						if (arrTableData[iRows][sColName] === undefined || arrTableData[iRows][sColName] === "") {
							Utilities.showPopupAlert("Mandatory fields are empty. Please enter a value in all the mandatory fields", MessageBox.Icon.ERROR,
							"Data Invalid");
							return;
						}
					}

					let aValues = oFieldPropertiesDetailsModel.getProperty("/multiValueUI/lists/" + sColName);
					sOperatorValue = arrTableData[iRows][sColName+"-Operator"] ? arrTableData[iRows][sColName+"-Operator"] : "EQ";
					let sODataColValue = sColValue;
					let sODataColDesc = "";
					// Feature/12185 - Condition added to obtain only the key if it is the case
					// Bug 12846 - changing the split to (" - ") so it won't get truncated
					if(aValues.length > 0) {
						sODataColValue = sColValue.includes(" - ")? sColValue.split(" - ")[0] : sColValue;
						sODataColDesc = sColValue.includes(" - ")? sColValue.split(" - ")[1] : "-BLANK-";
					}
					oServerData.modeltoattrvalues.push({
						colname: (arrColumnInfo[iColumn].attributeType === "Deriving" ? arrColumnInfo[iColumn].entity + "___" + arrColumnInfo[
							iColumn].attribute : arrColumnInfo[iColumn].entity + "__" + arrColumnInfo[iColumn].attribute),
						colvalue: sODataColValue,
						colvaluedescr: sODataColDesc,
						colnum: (iColumn + 1).toString(),
						rownum: (iRows + 1).toString(),
						Operator: sOperatorValue
					});
				}
			}

			oServerData.modeltomessage = [];
			oServerData.modeltomessage.push({
				usmdmodel: "",
				MessageType: "",
				Message: ""
			});

			sap.ui.core.BusyIndicator.show(300);
			let oDataModel = oController.getModel("CREATE_MULTI_UI_PROPERTY");
			oDataModel.setHeaders({"Application-Interface-Key": "l6z1vjte"});
			oDataModel.create("/modelSet", oServerData, oWriteParameterObject);
		});
	};

	/**
	 * Called from function within this fragment code. The Component handle is passed to this 
	 * function for use. 
	 * 
	 * 1. Get the Property details 
	 * 2. Read the Column list from the data and create the columns 
	 * 3. Read the data and store the data model 
	 */
	oMultiValueUIObject._getMultiValueUIConfiguration = function (oController, oFieldPathInfo) {

		//	“YGW_ZEUS_BRF_DERIVE_N_SRV” – Through this service we can read the rules created in decision table  using get method.
		// URL: /sap/opu/odata/sap/YGW_ZEUS_BRF_DERIVE_N_SRV/MODELSet
		// $expand=MODELTODRIVING,MODELTODERIVING,MODELTOATTRVALUES,MODELTOMESSAGE
		// $filter=UsmdEntity eq 'ADDRESS' and UsmdModel eq 'BP' and UsmdCreqType eq 'ZAJ_TSQ3' and AppType eq '3' and AppName eq 'ZAP_BP_ADDRESS_ZAJ_TSQ3_DER'&$format=json

		// Note: 1)Two new fields are added in MODELSet entity. These are used in reading decision table.

		// 	AppName : For the application name
		// 	AppType: 1 for Single value UI Property. 2 for Multi value Ui Property

		// 2) UsmdCreqType is mandatory as a filter to be given. AppType is optional.
		let promise;
		let sFilterCriteria = "usmdmodel eq '" + oFieldPathInfo.DataModel + "' and usmdcreqtype  eq '" + oFieldPathInfo.CRName;
		sFilterCriteria += "' and apptype eq '" + oFieldPathInfo.AppType + "' and usmdentity eq '" + oFieldPathInfo.EntityName;
		sFilterCriteria += "' and appname eq '" + oFieldPathInfo.AppName + "'";

		if (oFieldPathInfo.AppType === "2") {

			let oUrlParameters = {
				"$filter": sFilterCriteria
			};

			promise = new Promise(function(resolve, reject) {
				(new DMRDataService(
					oController,
					"GET_UI_PROPERTY",
					"/readrulesSet"
				)).getData({
					success: {
						fCallback: function (oParams, oData) {
							resolve(oData.results[0]);
						},
						oParam: {
							"controller": oController
						}
					},
					error: {
						fCallback: function (oParams, oErrorData) {
							reject(oErrorData);
						},
						oParam: null
					}
				}, oUrlParameters);
			});
		}
		return promise;
	};

	return oMultiValueUIObject;
});