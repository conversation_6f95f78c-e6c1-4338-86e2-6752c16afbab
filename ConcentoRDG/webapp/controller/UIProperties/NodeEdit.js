sap.ui.define([
	"sap/m/MessageToast"
], function (MessageToast) {
	"use strict";
	let NodeEdit = {};
	
	/**
	 * Open the Node Edit dialog with the initial data
	 * 
	 * Called from SingleValueUIDetails file
	 * this refers to Current file
	 * oParentController refers to UIPropertiesDetails controller
	 */
	NodeEdit.openNodeEditDialog = function (oParentController, oNodeEditDetails) {
		let oFieldPropertiesDetailsModel = oParentController.getModel("fieldPropertiesDetails");
		let sDataModel = oFieldPropertiesDetailsModel.getProperty("/AttributePath/DataModel");
		let sCrType = oFieldPropertiesDetailsModel.getProperty("/AttributePath/CRName");
		let sModelOrEntityType = "UsmdModel";

		// Initialize the Node Edit Model Data
		oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails", {});
		oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData", oNodeEditDetails);		

		// Fill the entity list
		// Bug 12199 - Field Property Shows Entities which are not part of the CR type - 0G
		let promiseEntityList = oParentController.GetListData.getModelEntityList(oParentController, sDataModel, sModelOrEntityType, sCrType, "UI Properties");
		promiseEntityList.then(function (oEntityList) {
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditEntityList", oEntityList);

			/* If there is a previous selection for Entity, trigger the selection Change event on the combobox */
			let oEntityComboBox = sap.ui.getCore().byId(oParentController.FieldPropertyNodeEditFragmentId + "--idPropertiesCBoxEntities");
			// Force Display the list content with the updated data
			oEntityComboBox.syncPickerContent();
			// trigger the selection change event if an item was selected
			let oSelectedEntity = oEntityComboBox.getSelectedItem();
			if (oSelectedEntity) {
				oEntityComboBox.fireSelectionChange({
					selectedItem: oSelectedEntity,
					entitySelectionChange: false
				});
			}

			sap.ui.getCore().byId(oParentController.FieldPropertyNodeEditFragmentId + "--idPropertiesInputFromValueSugg").setValueState("Information");
			sap.ui.getCore().byId(oParentController.FieldPropertyNodeEditFragmentId + "--idPropertiesInputFromValueSugg").setValueStateText("Type to refresh list");
			sap.ui.getCore().byId(oParentController.FieldPropertyNodeEditFragmentId + "--idPropertiesInputToValueSugg").setValueState("Information");
			sap.ui.getCore().byId(oParentController.FieldPropertyNodeEditFragmentId + "--idPropertiesInputToValueSugg").setValueStateText("Type to refresh list");
		});

		// Fill the operand list 
		let arrBRFOperands = oParentController.GetListData.getFieldPropertyOperands(oParentController, "OrdinalComparisons");
		oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditComparatorList", arrBRFOperands);

		// Open the Node dialog
		let oNodeEditDialog = oParentController._getNodeEditFragment();
		oNodeEditDialog.open();
	};

	/**
	 * Invoved as a handler to the Node Edit Dialog - Entity selection change event
	 * 
	 * INVOKED IN THE CONTEXT OF THE PARENT CONTROLLER. this = UIPropertiesDetails controller
	 */
	NodeEdit.onNodeEditEntitySelectionChange = function (oEvent) {
		let sSelectedEntity = oEvent.getSource().getSelectedKey();
		let bentitySelectionChange = oEvent.getParameters().entitySelectionChange;
		let oController = this;
		
		let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
		let sDataModel = oFieldPropertiesDetailsModel.getProperty("/AttributePath/DataModel");

		let promiseAttributeList =
			this.GetListData.getEntityAttributeList(this, sDataModel, sSelectedEntity, undefined);
		promiseAttributeList.then(function (oAttributes) {
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditAttributeList", oAttributes);
			if (bentitySelectionChange === undefined) {
				oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/selectedAttribute", "");
			}

			/* If there is a previous selection for Attribute, trigger the selection Change event on the combobox */
			let oAttributeComboBox = sap.ui.getCore().byId(oController.FieldPropertyNodeEditFragmentId + "--idPropertiesCBoxAttributes");
			// Force Display the list content with the updated data
			oAttributeComboBox.syncPickerContent();
			// trigger the selection change event if an item was selected
			let oSelectedAttribute = oAttributeComboBox.getSelectedItem();
			oAttributeComboBox.fireSelectionChange({
				selectedItem: oSelectedAttribute,
				attributeSelectionChange: false
			});
		});
	};

	/**
	 * Invoved as a handler to the Node Edit Dialog - Attribute selection change event
	 * 
	 * INVOKED IN THE CONTEXT OF THE PARENT CONTROLLER. this = UIPropertiesDetails controller
	 */
	NodeEdit.onNodeEditAttributeSelectionChange = function (oEvent) {
		sap.ui.getCore().byId(this.FieldPropertyNodeEditFragmentId + "--save").setEnabled(true);
		let sSelectedAttribute = oEvent.getSource().getSelectedKey();
		let bAttributeSelectionChange = oEvent.getParameters().attributeSelectionChange;
		let oController = this;
		//Get Model
		let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
		let oAttributes = oFieldPropertiesDetailsModel.getProperty("/NodeEditDetails/nodeEditAttributeList");
		if(bAttributeSelectionChange === undefined){
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValue", "");
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputToValue", "");
		}
		let sDataModel = oFieldPropertiesDetailsModel.getProperty("/AttributePath/DataModel");
		
		let sEntity = oFieldPropertiesDetailsModel.getProperty("/NodeEditDetails/nodeEditData/selectedEntity");

		if (sSelectedAttribute) {
			let {Length} = oAttributes.find(attr => attr.UsmdAttribute === sSelectedAttribute);
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/ElementLength", (Length) ? Number(Length) : 0);
			
			// Bug 11300 - List items for MARCATP - MTVFP incorrect in MG1
			sap.ui.core.BusyIndicator.show();
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditToValueList", "");
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditFromValueList", "");
			let promiseValueList = oController.GetListData.getAttributeValuesList(oController, sDataModel, sEntity, sSelectedAttribute, undefined);
			promiseValueList.then(function (oValueList) {
				oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditToValueList", oValueList);
				oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditFromValueList", oValueList);
				sap.ui.core.BusyIndicator.hide();
			});
			
			let promiseElementType = 
				oController.GetListData.getElementType(oController, sDataModel, sEntity, sSelectedAttribute);
			promiseElementType.then(function(oElementType) {
				oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/ElementType", oElementType.Elementtype);
				// Fill the operand list 
				let arrFieldPropertyOperands = oController.GetListData.getFieldPropertyOperands(oController, "OrdinalComparisons");
				oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditComparatorList", arrFieldPropertyOperands);

				if(oElementType.Elementtype === "Q" || oElementType.Elementtype === "A"){
					arrFieldPropertyOperands = arrFieldPropertyOperands.filter(operand => (operand.id === "NE" || operand.id === "LE" || operand.id === "LT"));
				}
				oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditComparatorList", arrFieldPropertyOperands);
				if(oElementType.Elementtype === "Q") {
					let promiseDimensionsList = oController.GetListData.getDimensionsList(oController);
					promiseDimensionsList.then(function(oDimensionsList) {
						oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/dimensionsList", oDimensionsList);
						/* If there is a previous selection for Attribute, trigger the selection Change event on the combobox */
						let oDimensionComboBox = sap.ui.getCore().byId(oController.FieldPropertyNodeEditFragmentId  + "--idDimensionUIProp");
						// Force Display the list content with the updated data
						oDimensionComboBox.syncPickerContent();
						// trigger the selection change event if an item was selected
						let oSelectedDimension = oDimensionComboBox.getSelectedItem();

						//10927 - Take care of units in badi
						//Find a match between the dimension on the list and the one in the API response, 
						//and setting to be displayed automatically in the field.
						let oMatchedDimension = oDimensionsList.find((oMatchDimension => {
							return oMatchDimension.Dimension === oElementType.Dimension;
						}));

						if(oMatchedDimension) {
							
							let oSelectedDimen = oMatchedDimension.Dimension;
							oDimensionComboBox.setSelectedKey(oSelectedDimen);
							oDimensionComboBox.fireSelectionChange({
								selectedItem: oSelectedDimen
							});	
							oDimensionComboBox.setEnabled(false);	
						} else {

							oDimensionComboBox.fireSelectionChange({
								selectedItem: oSelectedDimension
							});	
							oDimensionComboBox.setEnabled(true);
						}
					});
				} else if(oElementType.Elementtype === "A") {
					let promiseUnitsList = oController.GetListData.getUnitsList(oController, undefined, oElementType.Elementtype);
					promiseUnitsList.then(function(oUnitsList) {
						oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/UnitsList", oUnitsList);	
					});
				}
			});
		}
		else
		{
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValue", "");
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputToValue", "");
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditToValueList", "");
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditFromValueList", "");
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/selectedDimension", "");
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/selectedUnit", "");
		}

	};
	
	/**
	 * Invoved as a handler to the Node Edit Dialog - Comparision change event to clear From Value and To Value
	 * 
	 * INVOKED IN THE CONTEXT OF THE PARENT CONTROLLER. this = UIPropertiesDetails controller
	 */
	NodeEdit.onNodeEditComparisionChange = function () {
		let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
		oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputFromValue", "");
		oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/inputToValue", "");
	};
	
	/**
	 * Invoved as a handler to the Node Edit Dialog - To Value Live Change event to refresh the list on user input
	 * 
	 * INVOKED IN THE CONTEXT OF THE PARENT CONTROLLER. this = UIPropertiesDetails controller
	 */
	NodeEdit.onLiveChangeToValue = function (oEvent) {
		let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
		let sDataModel = oFieldPropertiesDetailsModel.getProperty("/AttributePath/DataModel");
		let sEntity = oFieldPropertiesDetailsModel.getProperty("/NodeEditDetails/nodeEditData/selectedEntity");
		let sAttribute = oFieldPropertiesDetailsModel.getProperty("/NodeEditDetails/nodeEditData/selectedAttribute");
		let sKey = oEvent.getParameter("value");
		let aList = oFieldPropertiesDetailsModel.getProperty("/NodeEditDetails/nodeEditToValueList");
		if (aList.length > 0) {
			let promiseValueList =
				this.GetListData.getAttributeValuesList(this, sDataModel, sEntity, sAttribute, sKey);
			promiseValueList.then(function (oValueList) {
				if (oValueList.length > 0) {
					oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditToValueList", oValueList);
				}
			});
		}
	};
	
	/**
	 * Invoved as a handler to the Node Edit Dialog - From Value Live Change event to refresh the list on user input
	 * 
	 * INVOKED IN THE CONTEXT OF THE PARENT CONTROLLER. this = UIPropertiesDetails controller
	 */
	NodeEdit.onLiveChangeFromValue = function (oEvent) {
		let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
		let sDataModel = oFieldPropertiesDetailsModel.getProperty("/AttributePath/DataModel");
		let sEntity = oFieldPropertiesDetailsModel.getProperty("/NodeEditDetails/nodeEditData/selectedEntity");
		let sAttribute = oFieldPropertiesDetailsModel.getProperty("/NodeEditDetails/nodeEditData/selectedAttribute");
		let sKey = oEvent.getParameter("value");
		let aList = oFieldPropertiesDetailsModel.getProperty("/NodeEditDetails/nodeEditFromValueList");
		if (aList.length > 0) {
			let promiseValueList =
				this.GetListData.getAttributeValuesList(this, sDataModel, sEntity, sAttribute, sKey);
			promiseValueList.then(function (oValueList) {
				if (oValueList.length > 0) {
					oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditFromValueList", oValueList);
				}
			});
		}
	};

	/**
	 * Task 11217 - Value Conversion and Data Check for Single value rules and properties
	 * This function gets called on change event of Value field and validates the input types
	 */
	NodeEdit.onChangeValue = function (oEvent) {
		let oFieldPropertiesDetailsModel = oEvent.getSource().getModel("fieldPropertiesDetails");
		let sAttribute = oFieldPropertiesDetailsModel.getProperty("/NodeEditDetails/nodeEditData/selectedAttribute");
		//Bug 12571 - Get the attribute list for the entity which has been selected by the user
		let arrAttribiteList = oFieldPropertiesDetailsModel.getProperty("/NodeEditDetails/nodeEditAttributeList");
		let oSelectedAttributeDetails = arrAttribiteList.find((oAttribute => {
			return oAttribute.UsmdAttribute === sAttribute;
		}));
		
		let oColumnInfo = {
			"entity": oSelectedAttributeDetails.UsmdEntity,
			"SelectedLengthComparison": false,
			"attribute": oSelectedAttributeDetails.UsmdAttribute,
			"attributeType": "Driving",
			"attributeDataType": oSelectedAttributeDetails.DATATYPE,
			"attributeKind": oSelectedAttributeDetails.Kind,
			"attributeDecimals": oSelectedAttributeDetails.Decimals,
			"attributeDataLenght": oSelectedAttributeDetails.Length,
			// Bug 12606 - Comparator was added to be avaluated in _changeCellValue
			"comparator": oFieldPropertiesDetailsModel.getProperty("/NodeEditDetails/nodeEditData/selectedComparator")
		};
		
		NodeEdit._changeCellValue(oEvent, oColumnInfo);
	};

	/** 
	 * Task 11217 - Value Conversion and Data Check for Single value rules and properties
	 * Condition "N" was modified to not allow letters and spetial characters
	 * Condition "P" was modified to not allow NaN value
	 * Condition "I" was added to rounded the Int values
	 * Condition "F" was added to compare the value with a regular expression 
	 * Message was added to inform about wrong value and when this occurs the field is cleaned
	 */
	NodeEdit._changeCellValue = function (oEvent, oColumnInfo) {
		let oInput = oEvent.getSource();
		let sValue = oInput.getValue();
		let aSuggestionItems = oInput.getSuggestionItems();
		// Bug 13336 - Check if field has suggestions to decide which validation corresponds
		if (oInput.getValueState() !== "None" && aSuggestionItems.length > 0) {
			let oSelectedItem = aSuggestionItems.find(item => item.getText() === sValue);
			// Fix 12574 - Condition was changed to validate the selected value
			if (sValue && !oSelectedItem) {
				oInput.setValueState("Warning");
				oInput.setValueStateText("The selected value is not in the list");
			}else{
				oInput.setValueState("Information");
				oInput.setValueStateText("Type to refresh list");
			}
			
		}else{
			let sOperator = oColumnInfo.comparator;
			let regexNum = "^[0-9]+$";
			let regexFLTP = "(\\+|\\-|\\.|[0-9]|(e|E))(\\+|\\-|\\.|[0-9]|(e|E))*";
			// Bug 12606 - Regex for Pattern were added
			let regexNumPattern = "^(\\*?[0-9]+\\*?)+$";
			let regexDecPattern = "^(\\*?\\.?[0-9]+\\.?\\*?)+$";
			let regexFLTPPattern = "^(\\*?[+-]?\\.?[0-9]*\\*?([+-]?(e|E))?\\*?)+$";
			let sCellValue = oEvent.getParameter("value");
			let iCellValue = Number(sCellValue);
			let flagInvalid = false;
			if(oColumnInfo.attributeKind === "C") {
				oEvent.getSource().setValue(sCellValue.toUpperCase());
			} else if(oColumnInfo.attributeKind === "N") {
				// Bug 12606 - Conditions were added to evaluate if the selected operator was CP or NP and use other regex which allows asterisks
				if (sOperator === "CP" || sOperator === "NP") {
					if(!sCellValue.match(regexNumPattern) && sCellValue.trim() !== ""){
						oEvent.getSource().setValue();
						flagInvalid = true;
					}
				}else{
					if(iCellValue.toFixed(0).toString().match(regexNum) && sCellValue.trim() !== ""){
						oEvent.getSource().setValue(iCellValue.toFixed(0).toString().padStart(Number(oColumnInfo.attributeDataLenght), 0));
					}else{
						oEvent.getSource().setValue();
						flagInvalid = true;
					}
				}
			} else if(oColumnInfo.attributeKind === "P") {
				if (sOperator === "CP" || sOperator === "NP") {
					if(!sCellValue.match(regexDecPattern) && sCellValue.trim() !== ""){
						oEvent.getSource().setValue();
						flagInvalid = true;
					}
				}else{
					if(isNaN(iCellValue) || sCellValue.trim() === ""){
						oEvent.getSource().setValue();
						flagInvalid = true;
					}else{
						let cellValueFixed = iCellValue.toFixed(Number(oColumnInfo.attributeDecimals)).toString().replace(".", "");
						// Task 11217 - Value Conversion and Data Check for Single value rules and properties
						// This fixed the bug for BP_SALES.ANTLF Attribute, which is Decimal with lenght 1
						if (cellValueFixed.length === 1 && Number(oColumnInfo.attributeDataLenght) === 1) {
							oEvent.getSource().setValue(Number(iCellValue));
						} else if(cellValueFixed.length >= Number(oColumnInfo.attributeDataLenght)) {
							// Bug 12157 - Input validation for BP_COMPNY - KULTG
							// Only apply this formula if the data type is decimal and oColumnInfo.attributeDecimals is greater than zero
							if (Number(oColumnInfo.attributeDecimals) > 0) {
								let newValue = cellValueFixed.slice(0, (oColumnInfo.attributeDataLenght - (Number(oColumnInfo.attributeDecimals) + 1))) + "."
												+ cellValueFixed.slice((oColumnInfo.attributeDataLenght - (Number(oColumnInfo.attributeDecimals) + 1)), oColumnInfo.attributeDataLenght - 1);
								oEvent.getSource().setValue(Number(newValue).toFixed(Number(oColumnInfo.attributeDecimals)));
							}
						}else{
							oEvent.getSource().setValue(iCellValue.toFixed(Number(oColumnInfo.attributeDecimals)));
						}
					}
				}
			} else if(oColumnInfo.attributeKind === "I" || oColumnInfo.attributeKind === "D" || oColumnInfo.attributeKind === "T") {
				if(iCellValue.toFixed(0).toString().match(regexNum) && sCellValue.trim() !== ""){
					oEvent.getSource().setValue(iCellValue.toFixed(0));
				}else{
					oEvent.getSource().setValue();
					flagInvalid = true;
				}
			} else if(oColumnInfo.attributeKind === "F"){
				if(((sOperator === "CP" || sOperator === "NP") && sCellValue.match(regexFLTPPattern)) || sCellValue.match(regexFLTP) && sCellValue.trim() !== ""){
					oEvent.getSource().setValue(sCellValue.toUpperCase());
				}else{
					oEvent.getSource().setValue();
					flagInvalid = true;
				}
			}
			
			// Bug 13336 - Removed setValueState methods to avoid setting wrong states
			if (flagInvalid && oEvent.getParameter("value")) {
				oEvent.getSource().setValue("");
				// Bug 13336 - Added MessageToast to tell the user when a value is incorrect 
				MessageToast.show("Enter a valid value", {
					at: "center center"
				});
			}
		}
	};
	
	NodeEdit.onChangeDate = function (oEvent) {
		let	oModel = this.getModel("fieldPropertiesDetails"),
			selectedComparator = oModel.getProperty("/NodeEditDetails/nodeEditData/selectedComparator"),
			inpDPFrom = sap.ui.getCore().byId(this.FieldPropertyNodeEditFragmentId + "--idPropertiesDatePFromValue"),
			inpDPTo = sap.ui.getCore().byId(this.FieldPropertyNodeEditFragmentId + "--idPropertiesDatePToValue"),
			btnSave = sap.ui.getCore().byId(this.FieldPropertyNodeEditFragmentId + "--save"),
			oSource = oEvent.getSource();
			
			oSource.setValueState((oSource.isValidValue()) ? "None" : "Error");
		if(selectedComparator === "BT"){
			btnSave.setEnabled((inpDPFrom.isValidValue() && inpDPTo.isValidValue()));
		}else{
			btnSave.setEnabled(inpDPFrom.isValidValue());
		}
	};

	/**
	 * Invoved as a handler to the Node Edit Dialog - Save Dialog Operation
	 * 
	 * INVOKED IN THE CONTEXT OF THE PARENT CONTROLLER. this= UIPropertiesDetails controller
	 */
	NodeEdit.onNodeEditSave = function () {
		
		let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");

		// Initialize the Node Edit Model Data 
		let oNodeDetails = oFieldPropertiesDetailsModel.getProperty("/NodeEditDetails/nodeEditData");

		// Update the details to the node / process flow 
		this.SingleValueUIDetailsDialog.updateNodeData(this, oNodeDetails);

		// Close the dialog 
		let oNodeEditDialog = this._getNodeEditFragment();
		oNodeEditDialog.close();
	};

	/**
	 * Invoved as a handler to the Node Edit Dialog - Cancel Dialog Operation
	 * 
	 * INVOKED IN THE CONTEXT OF THE PARENT CONTROLLER. this = UIPropertiesDetails Controller
	 */
	NodeEdit.onNodeEditCancel = function () {
		let oNodeEditDialog = this._getNodeEditFragment();
		oNodeEditDialog.close();
	};

	NodeEdit.onNodeEditDimensionChange = function (oEvent) {
		let oFieldPropertiesDetailsModel = this.getModel("fieldPropertiesDetails");
		
		let sSelectedDimension = oEvent.getSource().getSelectedKey();
		let sElementType = oFieldPropertiesDetailsModel.getProperty("/NodeEditDetails/nodeEditData/ElementType");
		
		if(sSelectedDimension) {
			// oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/selectedUnit", "");	
			let promiseUnitsList = this.GetListData.getUnitsList(this, sSelectedDimension, sElementType);
			promiseUnitsList.then(function(oUnitsList) {
				oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/UnitsList", oUnitsList);	
			});
		} else {
			oFieldPropertiesDetailsModel.setProperty("/NodeEditDetails/nodeEditData/selectedUnit", "");	
		}
		
	};


	return NodeEdit;
});
