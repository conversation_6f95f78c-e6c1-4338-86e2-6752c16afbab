sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"sap/m/MessageBox"
], function (Utilities, MessageBox) {
	"use strict";
	let SelectAttribute = {};

	/**
	 * Open the Select Attribute dialog with the initial data
	 * 
	 * CALLED FROM DIFFERENT MODULE. RUNS IN ITS OWN CONTEXT
	 */
	 SelectAttribute.openSelectAttributeDialog = function(oController, sAttributeType, sEntityName)
	 {
	 	//let thisObject = this;
	 	// Get model 
		let oFieldPropertiesDetailsModel = oController.getView().getModel("fieldPropertiesDetails");

		// Retrieve the data model from the component
		let sDataModel = oFieldPropertiesDetailsModel.getProperty("/AttributePath").DataModel;
		let sCrType = oFieldPropertiesDetailsModel.getProperty("/AttributePath").CRName;

		//let oSelectAttributeDialog = this.getAttributeSelectDialog(thisObject);
		// Initialize the Node Edit Model Data
		oFieldPropertiesDetailsModel.setProperty("/SelectAttributeDialog", {});
		oFieldPropertiesDetailsModel.setProperty("/SelectAttributeDialog/attributeType", sAttributeType);
		oFieldPropertiesDetailsModel.setProperty("/SelectAttributeDialog/selected", {type: 0, forAllAttributes: 0});
		oFieldPropertiesDetailsModel.setProperty("/SelectAttributeDialog/selected/entity", sEntityName);

		// Fill the entity list
		// Bug 12199 - Field Property Shows Entities which are not part of the CR type - 0G
		let promiseEntityList = oController.GetListData.getModelEntityList(oController, sDataModel, "UsmdModel", sCrType, "UI Properties");
		promiseEntityList.then(function(oEntityList){
			oFieldPropertiesDetailsModel.setProperty("/SelectAttributeDialog/entityList", oEntityList);
			
			/* If there is a previous selection for Entity, trigger the selection Change event on the combobox */
			let oEntityComboBox = sap.ui.getCore().byId(oController.SelectAttributeFragmentId + "--idCBoxEntities");
			// Force Display the list content with the updated data
			oEntityComboBox.syncPickerContent();
			// trigger the selection change event if an item was selected
			let oSelectedEntity = oEntityComboBox.getSelectedItem();
			if(oSelectedEntity) {
				oEntityComboBox.fireSelectionChange({selectedItem: oSelectedEntity});
			}
		});
		
		
		// Open the attribute dialog
		let oSelectAttributeDialog = oController._getSelectAttributeFragment();
		oSelectAttributeDialog.open();
	 };
	 
	/**
	 * Invoved as a handler to the Attribute Selection Dialog - Entity selection change event
	 * 
	 * INVOKED IN THE CONTEXT OF THE COMPONENT. this = oComponent
	 */
	SelectAttribute.onEntitySelectionChange = function(oEvent)
	{
		let sSelectedEntity = oEvent.getSource().getSelectedKey();
		// Get model 
		let oFieldPropertiesDetailsModel = this .getView().getModel("fieldPropertiesDetails");
		// Retrieve the data model from the component
		let sDataModel = oFieldPropertiesDetailsModel.getProperty("/AttributePath").DataModel;
		let promiseAttributeList = 
			this.GetListData.getEntityAttributeList(this, sDataModel, sSelectedEntity, undefined);
		promiseAttributeList.then(function(arrAttributes){
			oFieldPropertiesDetailsModel.setProperty("/SelectAttributeDialog/attributeList", arrAttributes);
			/**
			 * Task 11998 - Multiple Selection of Driving and Deriving Attributes
			 * Add 2 types - Value and Length
			 * Based on type radio button selected, the attribute list must be filtered
			 */
			if(oFieldPropertiesDetailsModel.getProperty("/SelectAttributeDialog/selected/type") === 0) {
				oFieldPropertiesDetailsModel.setProperty("/SelectAttributeDialog/attributeListFiltered", arrAttributes);
			} else {
				let arrFilteredAttributes = arrAttributes.filter(oAttribute => {
					return oAttribute.Kind === "C";
				});
				oFieldPropertiesDetailsModel.setProperty("/SelectAttributeDialog/attributeListFiltered", arrFilteredAttributes);
			}
		});
	};

	/**
	 * Task 11998 - Multiple Selection of Driving and Deriving Attributes
	 * Add 2 types - Value and Length as radio buttons
	 * Based on type radio button selected, the attribute list must be filtered
	 */
	SelectAttribute.onSelectAttrType = function(oEvent) {
		let sSelectedIndex = oEvent.getParameters().selectedIndex;
		// Get model 
		let oFieldPropertiesDetailsModel = this.getView().getModel("fieldPropertiesDetails");
		let arrAttributesList = oFieldPropertiesDetailsModel.getProperty("/SelectAttributeDialog/attributeList");
		if(sSelectedIndex === 0) {
			oFieldPropertiesDetailsModel.setProperty("/SelectAttributeDialog/attributeListFiltered", arrAttributesList);
		} else {
			let arrFilteredAttributes = arrAttributesList.filter(oAttribute => {
				return oAttribute.Kind === "C";
			});
			oFieldPropertiesDetailsModel.setProperty("/SelectAttributeDialog/attributeListFiltered", arrFilteredAttributes);
		}
		oFieldPropertiesDetailsModel.setProperty("/SelectAttributeDialog/selected/attributes", []);
	};

	SelectAttribute.onSelectForAllAttributes = function(oEvent) {
		let sSelectedIndex = oEvent.getParameters().selectedIndex;
		// Get model 
		let oFieldPropertiesDetailsModel = this.getView().getModel("fieldPropertiesDetails");
		if(sSelectedIndex === 1) {
			oFieldPropertiesDetailsModel.setProperty("/SelectAttributeDialog/selected/attributes", []);
		}
	};
	
	
	/**
	 * Invoved as a handler to the Attribute Selection Dialog - Save Dialog Operation
	 * 
	 * INVOKED IN THE CONTEXT OF THE COMPONENT. this = oComponent
	 */
	SelectAttribute.onAddColumnConfirm = function(){
		// Get model 
		let oFieldPropertiesDetailsModel = this.getView().getModel("fieldPropertiesDetails");

		/**
		 * Task 11998 - Multiple selection of Driving and Deriving Attributes
		 * Show error message if mandatory details are not added
		 * Show information message with all duplicate attributes that will not be added to derivation table
		 * Call function returnSelectedAttribute with details to add column to derivation table
		 * Close the fragment
		 */
		// Initialize the Node Edit Model Data 
		let oAttributeDetails = oFieldPropertiesDetailsModel.getProperty("/SelectAttributeDialog/selected");		
		let sAttributeType =	oFieldPropertiesDetailsModel.getProperty("/SelectAttributeDialog/attributeType");
		
		if(!(oAttributeDetails.entity && (oAttributeDetails.forAllAttributes === 1 || (oAttributeDetails.forAllAttributes === 0 && oAttributeDetails.attributes && oAttributeDetails.attributes.length > 0)))) {
			Utilities.showPopupAlert("Please fill in the required fields and try again.",
										MessageBox.Icon.ERROR,
										"Mandatory fields are empty");
			return;
		}
		
		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		let oMultiUITable = sap.ui.getCore().byId("MultiValuePropertyFragmentID" + "--" + "idMultiValueUITable");
		let arrColumnAggregation = oMultiUITable.getColumns();
		let arrDuplicateAttributes = [];
		if(oAttributeDetails.forAllAttributes === 0) {
			let arrAttributes = [];
			oAttributeDetails.attributes.forEach(sAttribute => {
				let oAttribute = oFieldPropertiesDetailsModel.getProperty("/SelectAttributeDialog/attributeList")
					.find(oAttr => oAttr.UsmdAttribute === sAttribute);
				if(oAttribute) {
					let oMatchedAttribute = arrColumnAggregation.find(oColumn => {
						let oCustomData = oColumn.getCustomData()[0].getValue();
						// 12472 - Cannot add both attribute value and length comparison as driving attributes in multivalue business rules
						if((oAttributeDetails.type === 0) && (oCustomData.entity === oAttributeDetails.entity) && (oCustomData.attribute === sAttribute)) {
							return true;
						}
						if((oAttributeDetails.type === 1) && (oCustomData.entity === oAttributeDetails.entity) && (oCustomData.attribute === sAttribute + "__LENGTH")) {
							return true;
						}
						return false;
					});
					if(oMatchedAttribute) {
						arrDuplicateAttributes.push(oMatchedAttribute.getCustomData()[0].getValue());
					} else {
						arrAttributes.push({
							entity: oAttributeDetails.entity,
							attributeDataType: oAttribute.DATATYPE,
							attributeDataLenght: (oAttribute.Length) ? Number(oAttribute.Length) : 0,
							attribute: (oAttributeDetails.type === 1) ? sAttribute + "__LENGTH" : sAttribute,
							attributeDecimals: oAttribute.Decimals,
							attributeKind: oAttribute.Kind,
							attributeType: (oAttributeDetails.type === 1) ? sAttributeType + "-Length"  : sAttributeType
						});
					}
				}
			});
			this.MultiValueDetailsModel.addMultipleAttributes(this, {arrAttributes, arrDuplicateAttributes, sAttributeType});
		} else {
			let oMatchedAttribute = arrColumnAggregation.find(oColumn => {
				let oCustomData = oColumn.getCustomData()[0].getValue();
				if((oCustomData.entity === oAttributeDetails.entity) && (oCustomData.attribute === "**")) {
					return true;
				}
				return false;
			});
			if(oMatchedAttribute) {
				Utilities.showPopupAlert("Cannot add duplicated attribute " + oAttributeDetails.entity + "->**. Attribute already exist.", MessageBox.Icon.INFORMATION, "Cannot add duplicate attribute");
			} else {
				this.MultiValueDetailsModel.returnSelectedAttribute(this, {
					entity: oAttributeDetails.entity,
					attribute: "**",
					attributeType: (oAttributeDetails.type === 1) ? sAttributeType + "-Length"  : sAttributeType
				});
			}
		}
		
		// Close the dialog 
		let oSelectAttributeDialog = this._getSelectAttributeFragment();
		oSelectAttributeDialog.close();
	};
	
	/**
	 * Invoved as a handler to the Attribute Selection Dialog - Cancel Dialog Operation
	 * 
	 * INVOKED IN THE CONTEXT OF THE COMPONENT. this = oComponent
	 */
	SelectAttribute.onAddColumnCancel = function(){
	//	Open the attribute dialog
		let oSelectAttributeDialog = this._getSelectAttributeFragment();
		oSelectAttributeDialog.close();
	}; 
	return SelectAttribute;
});