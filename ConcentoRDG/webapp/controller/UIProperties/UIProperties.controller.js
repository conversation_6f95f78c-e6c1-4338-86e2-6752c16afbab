sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
	"sap/ui/model/json/JSONModel",
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"sap/m/MessageBox"
], function (BaseController, JSONModel, GetListsData, Utilities, MessageBox) {
	"use strict";
	
	let oUIPropertiesModel = {};

	oUIPropertiesModel.onBeforeRendering = function () {
		let oFieldPropertiesModel = this.getView().getModel("fieldPropertiesModel");
		let oURLParams = this.oRouteParams;

		let promiseCRList = GetListsData.getChangeRequestsList(this);
		promiseCRList.then(function (arrCrList) {
			/*Added the method setSizeLimit*/
			oFieldPropertiesModel.setSizeLimit(arrCrList.results.length);
			oFieldPropertiesModel.setProperty("/changeRequestList", arrCrList.results);
			if (oURLParams && oURLParams.CRName) {
				oFieldPropertiesModel.setProperty("/selectedCRType", oURLParams.CRName);
			}
		});
	};

	oUIPropertiesModel.onInit = function () {
		this._cleanupView(this);
		this.getView().getModel("fieldPropertiesModel").setProperty("/selectedCheck", "");

		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		// Load the parameters received into the model
		oRouter.getRoute("UIProperties").attachPatternMatched(this, this._onRouteMatched.bind(this));

		// Create the searchlist component for entity List  to be placed in the view 
		let entitySearchList =
			new sap.ui.core.ComponentContainer("fpentitySearchListComponent", {
				name: "dmr.components.SearchList",
				componentCreated: this.onEntityListComponentCreated.bind(this),
				async: false
			});

		this.getView().byId("idFieldPropertiesEntitiesList").addItem(entitySearchList);

		// Create the searchlist component for UI Properties List  to be placed in the view 
		let fieldPropertiesList =
			new sap.ui.core.ComponentContainer("fpSearchListComponent", {
				name: "dmr.components.SearchList",
				componentCreated: this.onFieldPropertiesListComponentCreated.bind(this),
				async: false
			});

		this.getView().byId("idFieldPropertiesContainer").addItem(fieldPropertiesList);

		// Create the searchlist component for rule List  to be placed in the view 
		let stepsSearchListContainer = 
			new sap.ui.core.ComponentContainer("stepsSearchListComponent", {
				name: "dmr.components.SearchList",
				componentCreated: this.onStepsListComponentCreated.bind(this),
				async: false
			});
	
		this.getView().byId("idStepsList").addItem(stepsSearchListContainer);

		// Register the new UI property created event from the details controller
		sap.ui.getCore().getEventBus().subscribe("rdgChannel", "UIPropertyUpdated", this.onNewUIPropertyUpdation, this);

		// Reset all selections 
		this.resetSelectedPropetries(this);
		// Notify the change 
		this.notifySelectionChange();

	};

	oUIPropertiesModel._cleanupView = function (oController) {
		let oFieldPropertiesModel = oController.getView().getModel("fieldPropertiesModel");
		if (!oFieldPropertiesModel) {
			oFieldPropertiesModel = new JSONModel();
			oController.getView().setModel(oFieldPropertiesModel, "fieldPropertiesModel");
		}

		oFieldPropertiesModel.setProperty("/", {});
	};
	
	oUIPropertiesModel.onResizeScreen = function(oEvent){
		let oSplitter = oEvent.getSource();
		let zoom = ((window.outerWidth) / window.innerWidth) * 100;
		if(screen.width < 1600){
			if(zoom >= 150){
				oSplitter.setOrientation("Vertical");
				oSplitter.setHeight("400%");
			}else{
				oSplitter.setOrientation("Horizontal");
				oSplitter.setHeight("100%");
			}
		}else{
			if(zoom >= 200){
				oSplitter.setOrientation("Vertical");
				oSplitter.setHeight("400%");
			}else{
				oSplitter.setOrientation("Horizontal");
				oSplitter.setHeight("100%");
			}
		}
	};

	oUIPropertiesModel._onRouteMatched = function (oEvent, oController) {
		// Get the parameter and store to the model
		oController.oRouteParams = oEvent.getParameter("arguments");
		if (oController.oRouteParams.CRName === undefined) {
			if(oController.entitySearchList){
				oController.entitySearchList.clearSearchField();
			}
			oController._UIPropetriesResetScreen(oController);
		}

	};

	oUIPropertiesModel._UIPropetriesResetScreen = function (oController) {
		let oView = oController.getView();
		let oFieldPropertiesModel = oView.getModel("fieldPropertiesModel");

		let selectedUIPropertyData = oFieldPropertiesModel.getProperty("/selectedUIProperty");
		if (selectedUIPropertyData !== undefined) {
			oController.fieldPropertiesList.setListData([]);
			oController.byId("idChangeRequestComboBox").setValue("");
		}

		let entListData = oFieldPropertiesModel.getProperty("/entList");
		if (entListData !== undefined) {
			oController.entitySearchList.setListData([]);
		}

		oController.resetSelectedPropetries(oController);
		oFieldPropertiesModel.setProperty("/entList", undefined);
	};

	oUIPropertiesModel.onEntityListComponentCreated = function (oEvent) {
		let comp = oEvent.getParameter("component");

		// store the component handle 
		this.entitySearchList = comp;

		// update the mapping to the component 
		this.entitySearchList.setDataMapping({
			"title": "UsmdEntity",
			"description": "Txtlg",
			"info": "Count"
		});

		// Task 12413 - Filter rules, field properties if any rules are created for the crtype( UI5 Only task )
		this.entitySearchList.setButtonInfo({
			icon: "sap-icon://add-filter",
			toolTip: "Activate Filter"
		});
		this.entitySearchList.attachActionPressed(this.onEntityFilter, this);
		
		// this.entitySearchList.setUnreadPath("RulesExist");
		this.entitySearchList.setHeaderText("Entities");
		this.entitySearchList.attachSelectionChange(this.onEntityPressed, this);
	};

	oUIPropertiesModel.onFieldPropertiesListComponentCreated = function (oEvent) {
		let comp = oEvent.getParameter("component");

		// store the component handle 
		this.fieldPropertiesList = comp;

		this.fieldPropertiesList.setDataMapping({
			"title": "userrulename", //Task 12487 - Add User Rule Name to Rules/Properties
			"description": undefined,
			"info": undefined
		});

		this.fieldPropertiesList.setHeaderText("Field Properties");
		this.fieldPropertiesList.setListGroupingPath("appdesc");
		this.fieldPropertiesList.attachSelectionChange(this.onSelectFieldProperty, this);

	};

	oUIPropertiesModel.onStepsListComponentCreated = function(oEvent){
		let comp = oEvent.getParameter("component");
		
		// store the component handle 
		this.stepsSearchList = comp;
		
		// update the mapping to the component 
		this.stepsSearchList.setDataMapping({
			"title": "Txtmi",
			"description": "UsmdCreqStep",
			"info": "Uicounter"
		});
		
		// Task 12413 - Filter rules, field properties if any rules are created for the crtype( UI5 Only task )
		this.stepsSearchList.setButtonInfo({
			icon: "sap-icon://add-filter",
			toolTip: "Activate Filter"
		});
		this.stepsSearchList.attachActionPressed(this.onStepTypeFilter, this);

		this.stepsSearchList.setHeaderText("Steps");
		this.stepsSearchList.attachSelectionChange(this.onStepTypeSelected, this);
		this.getView().byId("splitterByCRStep").setVisible(false);
	};

	oUIPropertiesModel.onSelectCRType = function (oEvent) {
		let oView = this.getView();
		let oController = this;
		let oFieldPropertiesModel = oView.getModel("fieldPropertiesModel");

		// Get the data model name 
		let sModelName = oEvent ? oEvent.getSource().getSelectedItem().getAdditionalText() : undefined;
		let sCRType = oEvent ? oEvent.getSource().getSelectedItem().getKey() : undefined;

		oFieldPropertiesModel.setProperty("/selectedModel", sModelName);
		oFieldPropertiesModel.setProperty("/selectedCRType", sCRType);
		oFieldPropertiesModel.setProperty("/selectedStep", undefined);

		// Get the Tree Data for the Model 
		let oDataModelPromise = GetListsData.getModelEntityList(this, sModelName, "UsmdModel", sCRType, "UI Properties");
		oDataModelPromise.then(function (oData) {
			// Bug 12569 - Filter button does not reset when switching cr types
			if (oController.entitySearchList.getTooltip() === "Clear Filter") {
				if (oData && oData.length) {
					oData = oData.filter(oEntity => {
						return oEntity.Count !== "";
					});
				}
			}
			// End Bug 12569 - Filter button does not reset when switching cr types

			oFieldPropertiesModel.setProperty("/selectedUIProperty", {});
			oController.fieldPropertiesList.setListData([]);

			// Store the data after skipping the first element which the data model itself
			oFieldPropertiesModel.setProperty("/entList", oData);
			oController.entitySearchList.setListData(oData);
			oController.updateStepTypeList();

			sap.ui.core.UIComponent
				.getRouterFor(oController)
				.navTo("UIProperties", {
					CRName: sCRType,
					DataModel: sModelName,
					EntityName: undefined,
					AppId: undefined,
					AppName: undefined,
					UserRuleName: undefined, //Task 12487 - Add User Rule Name to Rules/Properties
					AppType: undefined,
					timeStamp: Date.now()
				},
				true
			);
		});
	};

	oUIPropertiesModel.onSelectRuleType = function(oEvent){
		let sSelectedRuleType = oEvent.getSource().getSelectedKey();
		if(sSelectedRuleType === "ByEntity"){
			this.getView().byId("splitterByEntity").setVisible(true);
			this.getView().byId("splitterByCRStep").setVisible(false);
		}else{
			this.getView().byId("splitterByEntity").setVisible(false);
			this.getView().byId("splitterByCRStep").setVisible(true);
		}
	};

	oUIPropertiesModel.onSelectFieldProperty = function (oEvent) {

		let oFieldPropertiesModel = this.getView().getModel("fieldPropertiesModel");

		let sSelectedAppId = oEvent.getParameters().data.appid;
		let sSelectedAppName = oEvent.getParameters().data.appname;
		let sSelectedUserRuleName = oEvent.getParameters().data.userrulename; //Task 12487 - Add User Rule Name to Rules/Properties
		let sSelectedAppType = oEvent.getParameters().data.apptype;

		// Store the attribute to the model 
		oFieldPropertiesModel.setProperty("/selectedUIProperty", {
			RuleId: sSelectedAppId,
			RuleName: sSelectedAppName,
			UserRuleName: sSelectedUserRuleName, //Task 12487 - Add User Rule Name to Rules/Properties
			AppType: sSelectedAppType
		});

		// Trigger the notification
		this.notifySelectionChange();
	};

	oUIPropertiesModel.onStepTypeSelected = function(oEvent){
		let oView = this.getView();
		let oFieldPropertiesModel = oView.getModel("fieldPropertiesModel");
		let sSelectedStep = oEvent.getParameters().selectedItem.getDescription();
		oFieldPropertiesModel.setProperty("/selectedStep", sSelectedStep);
		
		this.notifySelectionChange();
	};

	oUIPropertiesModel.onNewUIPropertyUpdation = function () {
		// 1. Get the selected item from the entity list 
		// 2. trigger the selected entity back to trigger an update on the UI Properties list 

		let oController = this;
		let oFieldPropertiesModel = this.getView().getModel("fieldPropertiesModel");

		let sModelName = oFieldPropertiesModel.getProperty("/selectedModel");
		let sCRType = oFieldPropertiesModel.getProperty("/selectedCRType");

		// Get the selected item from the entity list
		let sSelectedEntityTitle = this.entitySearchList.getSelectedItemTitle();

		// Get the Tree Data for the Model 
		let oDataModelPromise = GetListsData.getModelEntityList(this, sModelName, "UsmdModel", sCRType, "UI Properties");

		oDataModelPromise.then(function (arrUpdatedEntityList) {
			// Bug 12543 - Filter icon isn't refreshed on refresh of entity screen
			if (oController.entitySearchList.getTooltip() === "Clear Filter") {
				if (arrUpdatedEntityList && arrUpdatedEntityList.length) {
					arrUpdatedEntityList = arrUpdatedEntityList.filter(oEntity => {
						return oEntity.Count !== "";
					});
				}
			}
			// End Bug 12543 - Filter icon isn't refreshed on refresh of entity screen

			// Store the data after skipping the first element which the data model itself
			oFieldPropertiesModel.setProperty("/entList", arrUpdatedEntityList);
			oController.entitySearchList.setListData(arrUpdatedEntityList);

			if(sSelectedEntityTitle !== undefined) {
				// Select the entity again
				oController.entitySearchList.setSelectedItemByTitle(sSelectedEntityTitle);
			}
			
		});
	};

	oUIPropertiesModel.resetSelectedPropetries = function (oController) {
		let oView = oController.getView();
		let oFieldPropertiesModel = oView.getModel("fieldPropertiesModel");

		oFieldPropertiesModel.setProperty("/selectedCRType", undefined);
		oFieldPropertiesModel.setProperty("/selectedModel", undefined);
		oFieldPropertiesModel.setProperty("/selectedEnt", undefined);
		oFieldPropertiesModel.setProperty("/selectedUIProperty", undefined);
	};

	oUIPropertiesModel.notifySelectionChange = function () {
		let oView = this.getView();
		let oFieldPropertiesModel = oView.getModel("fieldPropertiesModel");

		let sCRType = oFieldPropertiesModel.getProperty("/selectedCRType");
		let sDataModel = oFieldPropertiesModel.getProperty("/selectedModel");
		let sEntityName = oFieldPropertiesModel.getProperty("/selectedEnt");
		let oSelectedUIProperty = oFieldPropertiesModel.getProperty("/selectedUIProperty");
		let sDateValue = Date.now();

		sap.ui.core.UIComponent
			.getRouterFor(this)
			.navTo("UIProperties", {
					CRName: sCRType,
					DataModel: sDataModel,
					EntityName: sEntityName,
					AppId: oSelectedUIProperty ? oSelectedUIProperty.RuleId : undefined,
					AppName: oSelectedUIProperty ? oSelectedUIProperty.RuleName : undefined,
					UserRuleName: oSelectedUIProperty ? oSelectedUIProperty.UserRuleName : undefined, //Task 12487 - Add User Rule Name to Rules/Properties
					AppType: oSelectedUIProperty ? oSelectedUIProperty.AppType : undefined,
					timeStamp: sDateValue
				},
				true
			);
	};

	oUIPropertiesModel.onEntityPressed = function (oEvent) {
		let sSelEnt = oEvent.getParameters().selectedItem.getTitle();
		let oFieldPropertiesModel = this.getView().getModel("fieldPropertiesModel");

		// Store the selected entity 
		oFieldPropertiesModel.setProperty("/selectedEnt", sSelEnt);

		// Update the UI Properties list 
		this.updateFieldPropertiesList(sSelEnt);
	};

	// Task 12413 - Filter rules, field properties if any rules are created for the crtype( UI5 Only task )
	oUIPropertiesModel.onEntityFilter = async function () {
		let oController = this;
		let oFieldPropertiesModel = oController.getView().getModel("fieldPropertiesModel");
		let sSelectedDataModel = oFieldPropertiesModel.getProperty("/selectedModel");
		let sSelectedCRType = oFieldPropertiesModel.getProperty("/selectedCRType");
		let arrEntitiesList = oFieldPropertiesModel.getProperty("/entList");
		let oFilterButton = {};
		let arrRefreshedEntitiesList = [];

		if (!sSelectedCRType) {
			Utilities.showPopupAlert("Select the Change Request Type first", MessageBox.Icon.INFORMATION, "Mandatory Fields Required");
		}

		if (oController.entitySearchList.getTooltip() === "Activate Filter") {
			oFilterButton = {
				icon: "sap-icon://clear-filter",
				toolTip: "Clear Filter"
			};

			if (arrEntitiesList && arrEntitiesList.length) {
				arrRefreshedEntitiesList = arrEntitiesList.filter(oEntity => {
					return oEntity.Count !== "";
				});
			}
		} else {
			oFilterButton = {
				icon: "sap-icon://add-filter",
				toolTip: "Activate Filter"
			};

			arrRefreshedEntitiesList = await GetListsData.getModelEntityList(oController, sSelectedDataModel, "UsmdModel", sSelectedCRType, "UI Properties");
		}
		
		oFieldPropertiesModel.setProperty("/selectedUIProperty", {});
		oFieldPropertiesModel.setProperty("/entList", arrRefreshedEntitiesList);
		oController.entitySearchList.updateButtonInfo(oFilterButton);
		oController.entitySearchList.setListData(arrRefreshedEntitiesList);
		oController.fieldPropertiesList.setListData([]);
		oController.updateStepTypeList();
		oController.notifySelectionChange();
	};

	// Task 12413 - Filter rules, field properties if any rules are created for the crtype( UI5 Only task )
	oUIPropertiesModel.onStepTypeFilter = async function() {
		let oController = this;
		let oFieldPropertiesModel = this.getView().getModel("fieldPropertiesModel");
		let sSelectedCRType = oFieldPropertiesModel.getProperty("/selectedCRType");
		let arrWFStepList = oFieldPropertiesModel.getProperty("/WFStepList");
		let oFilterButton = {};
		let arrRefreshedWFStepList = [];
		let sCountType = "UI";

		if (!sSelectedCRType) {
			Utilities.showPopupAlert("Select the Change Request Type first", MessageBox.Icon.INFORMATION, "Mandatory Fields Required");
		}

		if (oController.stepsSearchList.getTooltip() === "Activate Filter") {
			oFilterButton = {
				icon: "sap-icon://clear-filter",
				toolTip: "Clear Filter"
			};

			if (arrWFStepList && arrWFStepList.length) {
				arrRefreshedWFStepList = arrWFStepList.filter(oEntity => {
					return oEntity.Uicounter > 0;
				});
			}
		} else {
			oFilterButton = {
				icon: "sap-icon://add-filter",
				toolTip: "Activate Filter"
			};

            //Task 12508 Filter CR Step type properties
			arrRefreshedWFStepList = await GetListsData.getWorkflowStepList(oController, sSelectedCRType, sCountType);
		}

		arrRefreshedWFStepList.forEach(step => {
			step.Uicounter = parseInt(step.Uicounter);

			if(step.UsmdCreqStep.length < 2){
				step.UsmdCreqStep = "0" + step.UsmdCreqStep;
			}
		});
		
		oFieldPropertiesModel.setProperty("/selectedUIProperty", {});
		oFieldPropertiesModel.setProperty("/WFStepList", arrRefreshedWFStepList);
		oController.stepsSearchList.updateButtonInfo(oFilterButton);
		oController.stepsSearchList.setListData(arrRefreshedWFStepList);
		oController.notifySelectionChange();
	};

	oUIPropertiesModel.updateFieldPropertiesList = function () {
		let oController = this;

		let oFieldPropertiesModel = this.getView().getModel("fieldPropertiesModel");

		// Reset the Property selection 
		oFieldPropertiesModel.setProperty("/selectedUIProperty", {});

		let sSelectedDataModel = oFieldPropertiesModel.getProperty("/selectedModel");
		let sSelectedEntity = oFieldPropertiesModel.getProperty("/selectedEnt");
		let sSelectedCRType = oFieldPropertiesModel.getProperty("/selectedCRType");

		// Get the UI Properties list for the selected Data model and entity 
		let oUIPropertiesListPromise =
			GetListsData.getUIPropertiesList(this, sSelectedDataModel, sSelectedEntity, sSelectedCRType);

		oUIPropertiesListPromise.then(function (oData) {

			let arrUIPropertiesList = oData;
			oFieldPropertiesModel.setProperty("/UIPropertiesList", arrUIPropertiesList);
			oController.fieldPropertiesList.setListData(arrUIPropertiesList);
		});

		this.notifySelectionChange();
	};

	oUIPropertiesModel.updateStepTypeList = function (){
		let oController = this;
		let oFieldPropertiesModel = this.getView().getModel("fieldPropertiesModel");
		let sSelectedCRType = oFieldPropertiesModel.getProperty("/selectedCRType");
		let sCountType = "UI";
        
		//Task 12508 Filter CR Step type properties
		let oPromiseWFStepList = 
		GetListsData.getWorkflowStepList(
			oController, 
			sSelectedCRType,
			sCountType
		);
		
		oPromiseWFStepList.then(function(arrWFStepList){
			arrWFStepList.forEach(step => {
				if(step.UsmdCreqStep.length < 2){
					step.UsmdCreqStep = "0" + step.UsmdCreqStep;
				}

				// Task 12413 - Filter rules, field properties if any rules are created for the crtype( UI5 Only task )
				step.Uicounter = parseInt(step.Uicounter);
			});
			
			// Bug 12569 - Filter button does not reset when switching cr types
			if (oController.stepsSearchList.getTooltip() === "Clear Filter") {
				if (arrWFStepList && arrWFStepList.length) {
					arrWFStepList = arrWFStepList.filter(oEntity => {
						return oEntity.Uicounter > 0;
					});
				}
			}
			// End Bug 12569 - Filter button does not reset when switching cr types

			// Task 12413 - Filter rules, field properties if any rules are created for the crtype( UI5 Only task )
			oFieldPropertiesModel.setProperty("/WFStepList", arrWFStepList);
			oController.stepsSearchList.setListData(arrWFStepList);
		});
	};

	/**
	 * Called when the Controller is destroyed. Use this one to free resources and finalize activities.
	 * @memberOf dmr.mdg.supernova.SupernovaFJ.view.UIProperties
	 */
	oUIPropertiesModel.onExit = function () {};

	return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.UIProperties.UIProperties", oUIPropertiesModel);
});