sap.ui.define([
	"sap/ui/core/mvc/Controller",
	"sap/ui/model/json/JSONModel",
	"sap/ui/core/routing/History",
	"sap/ui/core/routing/HashChanger",
	"sap/ui/core/BusyIndicator",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"sap/m/MessageBox",
	"dmr/mdg/supernova/SupernovaFJ/model/models"
], function (Controller, JSONModel, History, HashChanger, BusyIndicator, Utilities, MessageBox, RDGModel) {
	"use strict";
	
	let oHashChanger = HashChanger.getInstance();
    oHashChanger.attachEvent("hashChanged", function(oEvent) {
    	let objHash = {
    		"dataModel": "Manage Data Model",
    		"Workflow": "Manage Rule-based Workflow",
			"BusinessRules": "Manage Business Rules",
			"UIProperties": "Field Properties Configuration",
			"Interfaces": "Interfaces"
    	};
    	let hash = oEvent.getSource().getHash().split("/")[0];
    	if(objHash[hash]){
    		document.title = objHash[hash];
    	}else{
    		document.title = "DMR Rapid Data Governance";
    	}
	});	

	return Controller.extend("dmr.mdg.supernova.SupernovaFJ.controller.BaseController", {

		_aObjectTypes: [{
			key: "BusinessPartner",
			text: "Business Partner"
		}, {
			key: "Customer",
			text: "Customer"
		}, {
			key: "Material",
			text: "Material"
		}, {
			key: "Vendor",
			text: "Vendor"
		}],

		getUsername: function() {
			
			let oPromise = RDGModel
				.getSapUserName(this);
				
			oPromise.then(function(){	
			});
			
			return oPromise;
		},
		
		getCurrentSystem: function() {
			let oPromise = RDGModel.getCurrentSystem(this);
			
			oPromise.then(function() {	
			});
			return oPromise;
		},            

		/**
		 * Convenience method for accessing the router.
		 * @public
		 * @returns {sap.ui.core.routing.Router} the router for this component
		 */
		getRouter: function () {
			return sap.ui.core.UIComponent.getRouterFor(this);
		},

		newJSONModel: function () {
			return new JSONModel();
		},

		/**
		 * Convenience method for getting the view model by name.
		 * @public
		 * @param {string} [sName] the model name
		 * @returns {sap.ui.model.Model} the model instance
		 */
		getModel: function (sName) {
			return this.getView().getModel(sName);
		},

		/**
		 * Convenience method for setting the view model.
		 * @public
		 * @param {sap.ui.model.Model} oModel the model instance
		 * @param {string} sName the model name
		 * @returns {sap.ui.mvc.View} the view instance
		 */
		setModel: function (oModel, sName) {
			return this.getView().setModel(oModel, sName);
		},

		/**
		 * Getter for the resource bundle.
		 * @public
		 * @returns {sap.ui.model.resource.ResourceModel} the resourceModel of the component
		 */
		getResourceBundle: function () {
			return this.getOwnerComponent().getModel("i18n").getResourceBundle();
		},

		/**
		 * Event handler for navigating back.
		 * It there is a history entry or an previous app-to-app navigation we go one step back in the browser history
		 * If not, it will replace the current entry of the browser history with the master route.
		 * @public
		 */
		// Task 12346 - Navigation back to main page is not working from MDC
		// Made the back navigation based on the Interface back navigation
		onNavBack: function () {
			let oHistory = History.getInstance();
			let sPreviousHash = oHistory.getPreviousHash();
			let oRouter = sap.ui.core.UIComponent.getRouterFor(this);

			if (sPreviousHash) {
				history.go(-1);
			} else {
				oRouter.navTo("TargetHome", {}, true);
			}
		},

		/**
		 * Show/Hide global busy indicator
		 * @public
		 */
		hideBusy: function () {
			BusyIndicator.hide();
		},
		showBusy: function () {
			BusyIndicator.show(0);
		},
		getObjectTypes: function () {
			return this._aObjectTypes;
		},
		onBackPress: function () {
			let oCurrentView = this.getView();
			let oController = this;

			let promise = new Promise(function(resolve, reject){
				if (oController.isPageEdit() === true) {
					let promiseShowPopupAlert = Utilities.showPopupAlert("Exiting this page may cause loss of data. Do you want to continue?", 
						MessageBox.Icon.ERROR, "Leave Page?", [MessageBox.Action.OK, MessageBox.Action.CANCEL]);
						promiseShowPopupAlert.then(function (){
							resolve();
						}, function() {
                        	reject();
                        });
				} else {
					resolve();
				}
			});

			promise.then(function(){
				//Implement the back navigation
				let oRouter = sap.ui.core.UIComponent.getRouterFor(oController);
				let oHistory = History.getInstance();
				let sPreviousHash = oHistory.getPreviousHash();
				
				// Bug 12446 - Check whether all back buttons to the left of title takes the user back to the previous page
				if (sPreviousHash) {
					window.history.go(-1);
				} else if (sPreviousHash === "") {
					oRouter.navTo("TargetHome", {}, undefined, true);
				} else {
					let arrCurrentRoute = oHistory.aHistory[oHistory.aHistory.length - 1].split("*");
					if(arrCurrentRoute[0].includes("EntityWizard")) {
						oRouter.navTo("dataModel", {}, undefined, true);
						oHistory.aHistory = ["dataModel"];
					} else {
						// We got here with a deeplink so we don't know how to go back
						// Just return to your homepage or whatever route you want to!
						// Replace HomeRoute with your current route ;)
						oRouter.navTo("TargetHome", {}, undefined, true);
					}
				}
				
				oCurrentView.removeContent();
			});
		},
		
		isPageEdit: function () {
			return true;
		},

		/**
		 * Get the string associated with the key provided. If the keys are provided the string will be 
		 * updated with the parameters.
		 * To user parameters store the string in the i18n files as below 
		 *    > The parameter value has been modified from {0} to {1}
		 * 
		 * if the arrParameters is provide as ['1', 'Ten']. A call to the geti18nText will return the following 
		 *    > The parameter value has been modified from 1 to Ten
		 * @param {String} i18nKey 
		 * @param {Array} arrParameters 
		 * @returns 
		 */
		geti18nText: function(i18nKey, arrParameters) {
			// If the resource bundle has not been read already, read it 
			if(this.i18nResourceBundle === undefined) {
				this.i18nResourceBundle = this.getOwnerComponent().getModel("i18n").getResourceBundle();
			}

			let sTextToDisplay = this.i18nResourceBundle.getText(i18nKey, arrParameters, false);
			return sTextToDisplay;
		}
	});
});