/* eslint-disable default-case */
sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
	"jquery.sap.global",
	"sap/ui/core/mvc/Controller",
	"sap/ui/model/Filter",
	"sap/ui/model/json/JSONModel",
	"sap/m/MessageBox",
	"sap/m/MessageToast",
	"sap/ui/core/routing/History",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"dmr/mdg/supernova/SupernovaFJ/model/DmAction",
	"dmr/mdg/supernova/SupernovaFJ/model/models",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"dmr/mdg/supernova/SupernovaFJ/model/ModelMessages"
], function (BaseController, jQuery, Controller, Filter, JSONModel, MessageBox, MessageToast, History,
	DMRDataService, GetListsData, DmAction, RDGModels, Utilities, ModelMessages) {
	"use strict";
	let IdocExtensionWizardController = {};
	IdocExtensionWizardController.onInit = function () {
		this._wizard = this.byId("idocExtensionWizard");
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		// Load the parameters received into the model 
		oRouter.getRoute("IdocExtensionWizard").attachMatched(this._onRouteMatched, this);
		this.ModelMessages = ModelMessages;
	};

	/*
	 * Add button has been clicked on Entity Tree page
	 * Node Level is set based on the row item level in tree that has been clicked
	 */
	IdocExtensionWizardController._onRouteMatched = function (oEvent) {

		let sParSeg = oEvent.getParameter("arguments").segmentType;
		let Outbimpl = oEvent.getParameter("arguments").Outbimpl;
		let Interface = oEvent.getParameter("arguments").Interface;
		let Commchannel = oEvent.getParameter("arguments").Commchannel;
		let BusinessSysMap = oEvent.getParameter("arguments").BusinessSysMap;
		let idocAction = oEvent.getParameter("arguments").idocAction;
		let RefSegment = oEvent.getParameter("arguments").RefSegment;
		let selectedSegmentRecord;
		let oIdocExtensionWizardDetailsModel = this.getView().getModel("idocExtensionWizardDetails");

		if (!oIdocExtensionWizardDetailsModel) {
			oIdocExtensionWizardDetailsModel = new JSONModel();
			this.getView().setModel(oIdocExtensionWizardDetailsModel, "idocExtensionWizardDetails");
		}
		let oIdocDetailsModel = this.getView().getModel("idocDetails");

		if (!oIdocDetailsModel) {
			oIdocDetailsModel = new JSONModel();
			this.getView().setModel(oIdocDetailsModel, "idocDetails");
		}

		this.refreshWizard();
		oIdocExtensionWizardDetailsModel.setProperty("/RefSegment", RefSegment);
		oIdocExtensionWizardDetailsModel.setProperty("/Interface", Interface);
		oIdocExtensionWizardDetailsModel.setProperty("/BusinessSysMap", BusinessSysMap);
		oIdocExtensionWizardDetailsModel.setProperty("/idocAction", idocAction);
		let promiseIdocStructure =
			GetListsData.getIdocStructureList(this,
				Interface,
				Outbimpl,
				Commchannel,
				BusinessSysMap);

		promiseIdocStructure.then((arrIdocStructureList) => {
			oIdocDetailsModel.setProperty("/", arrIdocStructureList[0]);
			oIdocExtensionWizardDetailsModel.setProperty("/Extension/idocExtension", arrIdocStructureList[0].Idocextension);
			oIdocExtensionWizardDetailsModel.setProperty("/Idoctyp", arrIdocStructureList[0].Idoctyp);
			oIdocExtensionWizardDetailsModel.setProperty("/WbTransport", arrIdocStructureList[0].WbTransport);
			oIdocExtensionWizardDetailsModel.setProperty("/CustTransport", arrIdocStructureList[0].CustTransport);
			oIdocExtensionWizardDetailsModel.setProperty("/Appl", arrIdocStructureList[0].Appl);
			oIdocExtensionWizardDetailsModel.setProperty("/Outbimpl", arrIdocStructureList[0].Outbimpl);
			oIdocExtensionWizardDetailsModel.setProperty("/Msgtyp", arrIdocStructureList[0].Msgtyp);
			oIdocExtensionWizardDetailsModel.setProperty("/Extension/basicType", arrIdocStructureList[0].Idoctyp);
			let arrParentSegmentList = arrIdocStructureList[0].MAIN2IDOCSTRUCTNAV.results.filter(function (idocItem) {
				return idocItem.IDOCSTRUCT2SYNTAXATTRIBNAV.results[0].Parseg === "";
			});
			oIdocExtensionWizardDetailsModel.setProperty("/ParentSegmentSuggestions", arrParentSegmentList);
			if (sParSeg) {
				selectedSegmentRecord = arrIdocStructureList[0].MAIN2IDOCSTRUCTNAV.results.filter(function (idocItem) {
					return idocItem.Segmenttype === sParSeg;
				});

				oIdocExtensionWizardDetailsModel.setProperty("/ParentSegment/isCustomSegment", selectedSegmentRecord[0].Segext);
				oIdocExtensionWizardDetailsModel.setProperty("/ParentSegment/Parseg", sParSeg);
				oIdocExtensionWizardDetailsModel.setProperty("/ParentSegment/Hlevel", selectedSegmentRecord[0].IDOCSTRUCT2SYNTAXATTRIBNAV.results[
					0].Hlevel);
			}
			if (selectedSegmentRecord && idocAction === "editSegment") {
				oIdocExtensionWizardDetailsModel.setProperty("/sSelectedExtnType", "EDIT");
				this.byId("rbGroupSelectExtensionType").setSelectedIndex(-1);
				this.setIdocExtensionWizardDetailProperties(-1);
				this.byId("SelectExtensionTypeStep").setNextStep(this.getView().byId("CreateExtensionStep"));
				this.byId("CreateExtensionStep").setNextStep(this.getView().byId("CreateEditSegmentStep"));
				this._wizard.validateStep(this.byId("SelectExtensionTypeStep"));
				this.byId("CreateExtensionStep").setTitle("Extension Details");
				this._wizard.nextStep("");

				this._wizard.validateStep(this.byId("CreateExtensionStep"));
				this.byId("CreateEditSegmentStep").setTitle("Edit Segment");
				this._wizard.nextStep("");
				let promiseSegmentDetails =
					GetListsData.getInterfaceIdocSegmentDetails(this, sParSeg);
				promiseSegmentDetails.then(function (oSegmentData) {
					oSegmentData.results[0].Qualifier = oSegmentData.Qualifier === "X" ? true : false;
					oSegmentData.results[0].MAINIDOCEXT2SEGFIELDSNAV.results.forEach((oSegmentField) => {

						oSegmentField.Isocode = oSegmentField.Isocode === "X" ? true : false;

					});
					oIdocExtensionWizardDetailsModel.setProperty("/Segment", oSegmentData.results[0]);
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/segmentFieldList", oSegmentData.results[0].MAINIDOCEXT2SEGFIELDSNAV.results);
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/Occmin", selectedSegmentRecord[0].IDOCSTRUCT2SYNTAXATTRIBNAV.results[0].Occmin);
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/Occmax", selectedSegmentRecord[0].IDOCSTRUCT2SYNTAXATTRIBNAV.results[0].Occmax);
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/Segtyp", sParSeg);
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueState", "None");
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/refTableValueState", "None");					
					oIdocExtensionWizardDetailsModel.setProperty("/WbTransport", oSegmentData.results[0].WbTransport);
					oIdocExtensionWizardDetailsModel.setProperty("/PackageSelected", oSegmentData.results[0].Usmddevclass);
					oIdocExtensionWizardDetailsModel.setProperty("/bEnableSave", true);
				});
				oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionName", arrIdocStructureList[0].Idocextension);
				oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionDescription", arrIdocStructureList[0].Extdescrp);
			} else if (idocAction === "addSegment") {
				oIdocExtensionWizardDetailsModel.setProperty("/sSelectedExtnType", "EDIT");
				this.byId("rbGroupSelectExtensionType").setSelectedIndex(-1);
				this.setIdocExtensionWizardDetailProperties(-1);
				this.byId("SelectExtensionTypeStep").setNextStep(this.getView().byId("CreateExtensionStep"));
				this.byId("CreateExtensionStep").setNextStep(this.getView().byId("CreateEditSegmentStep"));
				this._wizard.validateStep(this.byId("SelectExtensionTypeStep"));
				this.byId("CreateExtensionStep").setTitle("Extension Details");
				this._wizard.nextStep("");

				this._wizard.validateStep(this.byId("CreateExtensionStep"));
				this.byId("CreateEditSegmentStep").setTitle("Add Segment");
				this._wizard.nextStep("");
				oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionName", arrIdocStructureList[0].Idocextension);
				oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionDescription", arrIdocStructureList[0].Extdescrp);
				oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueState", "Error");
				oIdocExtensionWizardDetailsModel.setProperty("/Segment/refTableValueState", "Error");				
				oIdocExtensionWizardDetailsModel.setProperty("/Segment/Occmin", 0);
				oIdocExtensionWizardDetailsModel.setProperty("/Segment/Occmax", 1);
			} else if(idocAction === "addExtension"){
				oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionNameValueState", "Error");
				oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionNameValueStateText", "Extension Name must not be empty");	
			}

			let promiseExtensionsList =
				GetListsData.getInterfaceIdocExtensionsList(this, arrIdocStructureList[0].Idoctyp);

			promiseExtensionsList.then(function (oData) {
				oIdocExtensionWizardDetailsModel.setProperty("/extensionList", oData.results[0].IDOCEXTNSRCHHLPNAV.results);
			});

		});
	};

	IdocExtensionWizardController.onTransportPackageSelectionDialogCreated = function(oEvent){
		let comp = oEvent.getParameter("component");
		// store the component handle 
		this._transportPackageSelectionDialog = comp;

		this.getUsername()
		.then(function (oSapUserInfo) {
			let sUsername = oSapUserInfo.Sapname;
			if (!sUsername) {
				Utilities.showPopupAlert("Please login to continue.", MessageBox.ERROR, "Logged out");
			} else {
				comp.setUser(sUsername);
			}

		});
	};

	IdocExtensionWizardController._selectTransportPackage = function (bSelectCustomizing, bSelectWorkbench, bSelectPackage) {
		if(!this._transportPackageSelectionDialog){
			// If the component is not yet available, return a rejected promise
			return Promise.reject("Transport Package Selection Dialog is not ready.");
		}

		const oView = this.getView();
		const oViewIdocExtensionWizardModel = oView.getModel("idocExtensionWizardDetails");

		// Retrieve necessary properties from the model
		const sPreselectCustomizingTr = oViewIdocExtensionWizardModel.getProperty("/CustTransport");
		const sPreselectWorkbenchTr = oViewIdocExtensionWizardModel.getProperty("/WbTransport");
		const sPreselectPackage = oViewIdocExtensionWizardModel.getProperty("/PackageSelected");

		let promise = this._transportPackageSelectionDialog.open(
			bSelectCustomizing, sPreselectCustomizingTr, 
			bSelectWorkbench, sPreselectWorkbenchTr,
			bSelectPackage, sPreselectPackage
		);

		// Wait for the dialog to close and store the selection
		let responsePromise = promise.then(function(oResponseObject){
			oViewIdocExtensionWizardModel.setProperty("/CustTransport", oResponseObject.customizingTransport);
			oViewIdocExtensionWizardModel.setProperty("/WbTransport", oResponseObject.workbenchTransport);
			oViewIdocExtensionWizardModel.setProperty("/PackageSelected", oResponseObject.package);

			return oResponseObject;
		});

		return responsePromise;
	};

	/*
	 *	Refresh all details in the wizard.
	 *	Called when Entity Wizard is called from Entity Controller (onRouteMatched) or Add New button is clicked
	 *	Also called when user selects a different radio button after entering any details
	 */
	IdocExtensionWizardController.refreshWizard = function () {
		let oIdocExtensionWizardDetailsModel = this.getView().getModel("idocExtensionWizardDetails");
		let sSelectedExtnType = oIdocExtensionWizardDetailsModel.getProperty("/sSelectedExtnType");
		let sSegTypeValueState = "Error";
		let srefTableValueState = "Error";

		// Bug 11159 - Extension "COPY" option doesn't work
		// If Extension Type is "Copy", then Segment Type must not be required
		// If Extension Type is "Copy", then Reference Table must not be required
		if (sSelectedExtnType === "COPY") {
			sSegTypeValueState = "None";
			srefTableValueState = "None";
		}
		
		oIdocExtensionWizardDetailsModel.setProperty("/Extension", {
			extensionNameValueState: "Error",
			extensionNameValueStateText: "Extension Name must not be empty",
			extensionName: "",
			extensionDescription: "",
			basicType: oIdocExtensionWizardDetailsModel.getProperty("/Extension/basicType"),
			Firsttyp: "",
			Pretyp: ""
		});
		
		oIdocExtensionWizardDetailsModel.setProperty("/Segment", {
			Segtyp: "",
			Descrp: "",
			sSelectedSegment: "",
			segmentFieldList: [],
			SegTypeValueState: sSegTypeValueState,
			SegTypeValueStateText: "Segment Name must not be empty",
			Occmin: 0,
			OccMax: 1,
			refTableValueState: srefTableValueState,
			refTableValueStateText: "Reference Table must not be empty"
		});

		oIdocExtensionWizardDetailsModel.setProperty("/ParentSegment", {
			Hlevel: "",
			Mustfl: "",
			Nr: "",
			Occmax: "",
			Occmin: "",
			Outbimpl: "",
			Parflg: "",
			Parpno: "",
			Parseg: "",
			isCustomSegment: ""
		});

		oIdocExtensionWizardDetailsModel.setProperty("/bEnableSave", false);
		
		//No Radio button must be selected by default
		this.byId("rbGroupSelectExtensionType").setSelectedIndex(-1);

		//Next Step button should be disabled untill an entry is selected
		this._wizard.discardProgress(this.byId("SelectExtensionTypeStep"));
		this._wizard.invalidateStep(this.byId("SelectExtensionTypeStep"));
	};

	IdocExtensionWizardController.setIdocExtensionWizardDetailProperties = function (iSelectedIndex) {
		let oIdocExtensionWizardDetailsModel = this.getView().getModel("idocExtensionWizardDetails");
		// let sParentName = oIdocExtensionWizardDetailsModel.getProperty("/ParentName");
		switch (iSelectedIndex) {
		case 0:
			this.byId("CreateExtensionStep").setTitle("Create Extension");
			//this.byId("SelectSegmentTypeStep").setTitle("Select Segment Type");
			this.byId("SelectExtensionTypeStep").setNextStep(this.getView().byId("CreateExtensionStep"));
			oIdocExtensionWizardDetailsModel.setProperty("/sSelectedExtnType", "NEW");
			break;
		case 1:
			this.byId("CreateExtensionStep").setTitle("Create Extension");
			// this.byId("SelectSegmentTypeStep").setTitle("Select Segment Type");
			this.byId("SelectExtensionTypeStep").setNextStep(this.getView().byId("CreateExtensionStep"));
			oIdocExtensionWizardDetailsModel.setProperty("/sSelectedExtnType", "COPY");
			break;
		case 2:
			this.byId("CreateExtensionStep").setTitle("Create Extension");
			this.byId("SelectExtensionTypeStep").setNextStep(this.getView().byId("CreateExtensionStep"));
			oIdocExtensionWizardDetailsModel.setProperty("/sSelectedExtnType", "SUCCESSOR");
			break;
		}
	};

	IdocExtensionWizardController.onWizardSelectExtensionType = function (oEvent) {
		let iSelectedIndex = oEvent.getParameters().selectedIndex;
		let thisObject = this;
		let oIdocExtensionWizardDetailsModel = this.getView().getModel("idocExtensionWizardDetails");

		// Bug 11159 - Extension "COPY" option doesn't work
		// Whenever this method gets triggered, it means the user is currently navigating in Step 1
		// In Step 1, next button must always be visible
		this.getView().byId("idocExtensionWizard").setShowNextButton(true);
		
		// If user selected "NEW" or "SUCCESSOR" as Extension Type, then we should show the wizard with 3 steps
		// If user selected "COPY" as Extension Type, then we should show the wizard with only 2 steps
		switch (iSelectedIndex) {
			case 0: 
				oIdocExtensionWizardDetailsModel.setProperty("/sSelectedExtnType", "NEW");
				if (oIdocExtensionWizardDetailsModel.getProperty("/Segment/Segtyp") === "") {
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueState", "Error");
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueStateText", "Segment Name must not be empty");
				}
				if (this.byId("CreateExtensionStep").getNextStep() === null) {
					this.byId("CreateExtensionStep").setNextStep(this.getView().byId("CreateEditSegmentStep"));
				}
				break;
			case 1:
				oIdocExtensionWizardDetailsModel.setProperty("/sSelectedExtnType", "COPY");
				oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueState", "None");
				oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueStateText", "");
				if (this.byId("CreateExtensionStep").getNextStep() !== null) {
					this.byId("CreateExtensionStep").setNextStep("");
				}
				break;
			case 2:
				oIdocExtensionWizardDetailsModel.setProperty("/sSelectedExtnType", "SUCCESSOR");
				if (oIdocExtensionWizardDetailsModel.getProperty("/Segment/Segtyp") === "") {
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueState", "Error");
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueStateText", "Segment Name must not be empty");
				}
				if (this.byId("CreateExtensionStep").getNextStep() === null) {
					this.byId("CreateExtensionStep").setNextStep(this.getView().byId("CreateEditSegmentStep"));
				}
				break;
			default:
				oIdocExtensionWizardDetailsModel.setProperty("/sSelectedExtnType", "");
				if (oIdocExtensionWizardDetailsModel.getProperty("/Segment/Segtyp") === "") {
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueState", "Error");
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueStateText", "Segment Name must not be empty");
				}
				if (this.byId("CreateExtensionStep").getNextStep() === null) {
					this.byId("CreateExtensionStep").setNextStep(this.getView().byId("CreateEditSegmentStep"));
				}
		}
		
		let iPreviousSelectedIndex = oIdocExtensionWizardDetailsModel.getProperty("/iSaveSelectedIndex");
		let promiseShowPopupAlert;
		let extensionList = oIdocExtensionWizardDetailsModel.getProperty("/extensionList");
		
		if (iSelectedIndex === 1 || iSelectedIndex === 2) {
			if (!extensionList || extensionList.length === 0) {
				//No Radio button must be selected by default
				this.byId("rbGroupSelectExtensionType").setSelectedIndex(-1);

				//Next Step button should be disabled untill an entry is selected
				this._wizard.discardProgress(this.byId("SelectExtensionTypeStep"));
				this._wizard.invalidateStep(this.byId("SelectExtensionTypeStep"));
				promiseShowPopupAlert =
					Utilities.showPopupAlert("There are no extensions in this Idoc to create a Copy or Successor", MessageBox.Icon.INFORMATION,
						"Extension");
				promiseShowPopupAlert.then(function () {});
				return;
			}
		}
		if (this._wizard.getProgressStep() !== this.byId("SelectExtensionTypeStep")) {
			promiseShowPopupAlert = Utilities.showPopupAlert("You will lose all the changes in this type",
				MessageBox.Icon.WARNING, "Idoc Extension Wizard", [sap.m.MessageBox.Action.OK, sap.m.MessageBox.Action.CANCEL]);
			promiseShowPopupAlert.then(function () {
				thisObject.refreshWizard();
				thisObject.byId("rbGroupSelectExtensionType").setSelectedIndex(iSelectedIndex);
				thisObject.setIdocExtensionWizardDetailProperties(iSelectedIndex);
				thisObject._wizard.validateStep(thisObject.byId("SelectExtensionTypeStep"));
				oIdocExtensionWizardDetailsModel.setProperty("/iSaveSelectedIndex", iSelectedIndex);
			}, function () {
				thisObject.byId("rbGroupSelectExtensionType").setSelectedIndex(iPreviousSelectedIndex);
				oIdocExtensionWizardDetailsModel.setProperty("/iSaveSelectedIndex", iPreviousSelectedIndex);
				iSelectedIndex = iPreviousSelectedIndex;
				thisObject.setIdocExtensionWizardDetailProperties(iSelectedIndex);
			});
		} else {
			thisObject.setIdocExtensionWizardDetailProperties(iSelectedIndex);
			oIdocExtensionWizardDetailsModel.setProperty("/iSaveSelectedIndex", iSelectedIndex);
			thisObject._wizard.validateStep(thisObject.byId("SelectExtensionTypeStep"));
		}
	};

	/*
	 * Bug 11159 - Extension "COPY" option doesn't work
	 * Triggered every time the user reacher step 2 of the wizard
 	 * If Extension Type is "COPY", then we need to hide the Review button 
 	 * If Extension Type is "NEW", then we need to show the button to go to step 3
	 */
	IdocExtensionWizardController.onActivateStep2 = function () {
		let oIdocExtensionWizardDetailsModel = this.getView().getModel("idocExtensionWizardDetails");
		let sSelectedExtnType = oIdocExtensionWizardDetailsModel.getProperty("/sSelectedExtnType"); 
		
		if (sSelectedExtnType === "COPY") {
			this.getView().byId("idocExtensionWizard").setShowNextButton(false);
		} else {
			this.getView().byId("idocExtensionWizard").setShowNextButton(true);
		}
	};

	IdocExtensionWizardController.onLiveChangeExtension = function (oEvent) {
		let oIdocExtensionWizardDetailsModel = this.getView().getModel("idocExtensionWizardDetails");

		// Setting the input value as Parent Entity of Qualifying Entity, Key Attribute and Attribute
		let sExtensionName = oEvent.getSource().getValue().toUpperCase();

		if (sExtensionName.length === 0) {
			oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionNameValueState", "Error");
			oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionNameValueStateText", "Extension Name must not be empty");
		} else if (!/^[Z]/.test(sExtensionName)) {
			oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionNameValueState", "Error");
			oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionNameValueStateText", "Extension Name must start with Z");
		} else {
			// Bug 11193 - Edit IDOC - Copy Extension Issue
			// Check if Extension Name entered already exists
			let oController = this;
			let promiseExtensionByName = GetListsData.getExtensionByName(this, sExtensionName);
			promiseExtensionByName.then(function (oData) {
				if (oData[0].IDOCEXTNSRCHHLPNAV.results && oData[0].IDOCEXTNSRCHHLPNAV.results.length > 0) {
					oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionNameValueState", "Error");
					oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionNameValueStateText", "Extension Name already exists");
				} else {
					oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionNameValueState", "None");
					oIdocExtensionWizardDetailsModel.setProperty("/Extension/extensionNameValueStateText", "");
				}
				oController.enableSave();
			});
		}

		this.onActivateCreateExtensionStep(oEvent);
	};

	IdocExtensionWizardController.onActivateCreateExtensionStep = function (oEvent) {
		let oIdocExtensionWizardDetailsModel = this.getView().getModel("idocExtensionWizardDetails");
		let oExtension = oIdocExtensionWizardDetailsModel.getProperty("/Extension");
		let sSelectedExtnType = oIdocExtensionWizardDetailsModel.getProperty("/sSelectedExtnType");
		if (oEvent) {
			if (oEvent.getParameters().hasOwnProperty("value")) { //Change to uppercase if value property exists. Do not do anything if selectedItem exists
				let iInput = oEvent.getSource();
				if (iInput) {
					iInput.setValue(iInput.getValue().toUpperCase());
				}
			}
		}
		if (sSelectedExtnType === "NEW") {
			if (oExtension.extensionNameValueState === "None" && oIdocExtensionWizardDetailsModel.getProperty("/Extension/extensionName") &&
				oIdocExtensionWizardDetailsModel.getProperty("/Extension/extensionDescription") && oIdocExtensionWizardDetailsModel.getProperty(
					"/Extension/basicType")) {
				this._wizard.validateStep(this.byId("CreateExtensionStep"));
			} else {
				this._wizard.invalidateStep(this.byId("CreateExtensionStep"));
			}
		} else if (sSelectedExtnType === "COPY") {
			if (oExtension.extensionNameValueState === "None" && oIdocExtensionWizardDetailsModel.getProperty("/Extension/extensionDescription") &&
				oIdocExtensionWizardDetailsModel.getProperty("/Extension/basicType") && oIdocExtensionWizardDetailsModel.getProperty(
					"/Extension/Firsttyp")) {
				this._wizard.validateStep(this.byId("CreateExtensionStep"));
			} else {
				this._wizard.invalidateStep(this.byId("CreateExtensionStep"));
			}
		} else if (sSelectedExtnType === "SUCCESSOR") {
			if (oExtension.extensionNameValueState === "None" && oIdocExtensionWizardDetailsModel.getProperty("/Extension/extensionDescription") &&
				oIdocExtensionWizardDetailsModel.getProperty("/Extension/Pretyp")) {
				this._wizard.validateStep(this.byId("CreateExtensionStep"));
			} else {
				this._wizard.invalidateStep(this.byId("CreateExtensionStep"));
			}
		}
		this.enableSave();
	};
	
	/*
	 *	Live Change Event Handler for Data Element Input Field with Suggestions
	 *	Refresh the suggestions list for Data Element
	 *	Data Element Suggestion list manually restricted to 20 entries
	 */
	 IdocExtensionWizardController.onLiveChangeDataElement = function (oEvent) {
		// Bug 11213 - Make Data Element mandatory for new Segment Fields
		let sTerm = oEvent.getParameter("value");
		let oIdocExtensionWizardDetailsModel = this.getView().getModel("idocExtensionWizardDetails");
		let oTempArr = [];

		oTempArr = oIdocExtensionWizardDetailsModel.getProperty("/DataElementSuggestions");
		if (oTempArr) {
			let sTermCaps = sTerm.toUpperCase();
			oTempArr = oTempArr.filter(function (sValue) {
				return (sValue.Rollname.includes(sTermCaps) === true ? true : false);
			});

			if (oTempArr.length > 10) {
				oIdocExtensionWizardDetailsModel.setProperty("/DataElementSuggestions", oTempArr);
				oIdocExtensionWizardDetailsModel.refresh();
				return;
			}
		}

		sTerm = sTerm.toUpperCase();
		let promiseElementLookup = GetListsData.getElementLookup(this, sTerm, 20);

		promiseElementLookup.then(function (oData) {
			if (oData.results.length > 0) {
				oIdocExtensionWizardDetailsModel.setProperty("/DataElementSuggestions", oData.results);
				oIdocExtensionWizardDetailsModel.refresh();
			}
		});

	};

	IdocExtensionWizardController.onLiveChangeBasicType = function (oEvent) {
		let sTerm = oEvent.getParameter("suggestValue");
		let oIdocExtensionWizardDetailsModel = this.getView().getModel("idocExtensionWizardDetails");
		let sMsgtyp = oIdocExtensionWizardDetailsModel.getProperty("/Msgtyp");
		let oTempArr = [];
		oTempArr = oIdocExtensionWizardDetailsModel.getProperty("/basicTypeSuggestions");
		if (oTempArr) {
			let sTermCaps = sTerm.toUpperCase();
			oTempArr = oTempArr.filter(function (sValue) {
				return (sValue.Idoctyp.includes(sTermCaps) === true ? true : false);
			});

			if (oTempArr.length > 10) {
				oIdocExtensionWizardDetailsModel.setProperty("/basicTypeSuggestions", oTempArr);
				oIdocExtensionWizardDetailsModel.refresh();
				return;
			}
		}
		let promiseBasicTypeLookup =
			GetListsData.getInterfaceIdocBasicTypesList(this, sTerm.toUpperCase(), 20, sMsgtyp);

		promiseBasicTypeLookup.then(function (oData) {
			if (oData) {
				oIdocExtensionWizardDetailsModel.setProperty("/basicTypeSuggestions", oData);
				oIdocExtensionWizardDetailsModel.refresh();
			}
		});
	};

	IdocExtensionWizardController.onLiveChangeSegmentType = function (oEvent) {
		let sTerm = oEvent.getParameter("suggestValue");
		let oIdocExtensionWizardDetailsModel = this.getView().getModel("idocExtensionWizardDetails");
		let oIdocDetails = this.getView().getModel("idocDetails");
		let oCommChannel = oIdocDetails.getProperty("/Commchannel");
		let oTempArr = [];
		oTempArr = oIdocExtensionWizardDetailsModel.getProperty("/segmentSuggestions");
		if (oTempArr) {
			let sTermCaps = sTerm.toUpperCase();
			oTempArr = oTempArr.filter(function (sValue) {
				return (sValue.Tabname.includes(sTermCaps) === true ? true : false);
			});

			if (oTempArr.length > 10) {
				oIdocExtensionWizardDetailsModel.setProperty("/segmentSuggestions", oTempArr);
				oIdocExtensionWizardDetailsModel.refresh();
				return;
			}
		}
		let promiseSegmentLookup =
			GetListsData.getInterfaceMappingTablesList(this, sTerm.toUpperCase(), oCommChannel, 20, true);

		promiseSegmentLookup.then(function (oData) {
			if (oData.results.length > 0) {
				oIdocExtensionWizardDetailsModel.setProperty("/segmentSuggestions", oData.results);
				oIdocExtensionWizardDetailsModel.refresh();
			}
		});
	};

	IdocExtensionWizardController.onSelectSegmentType = function (oEvent) {
		let sTerm = oEvent.getParameter("value").toUpperCase();
		oEvent.getSource().setValue(sTerm);
		
		let thisObject = this;
		let oIdocExtensionWizardDetailsModel = thisObject.getView().getModel("idocExtensionWizardDetails");
		let oIdocDetails = thisObject.getView().getModel("idocDetails");
		let oCommChannel = oIdocDetails.getProperty("/Commchannel");
		
		if (sTerm.length === 0) {
			oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueState", "Error");
			oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueStateText", "Segment Name must not be empty");
			thisObject.enableSave();
		} else if (!/^[zZ]/.test(sTerm)) {
			oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueState", "Error");
			oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueStateText", "Segment Name must start with Z");
			thisObject.enableSave();
		} else {
			// Bug 11290: Do not allow user to add duplicate segments
			// Added new validation that checks if segment already exists in this extension
			let oMatch = oIdocDetails.oData.MAIN2IDOCSTRUCTNAV.results.find(oAttribute => {
				return oAttribute.Segmenttype === sTerm;
			});
			if (oMatch) {
				oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueState", "Error");
				oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueStateText", "Segment already exists");
				thisObject.enableSave();
			} else {
				/**
				 * Task 11530 - Do not allow to add segment which already exists in Successor Extension - UI
				 * We need to check if the segment provided by the user already exists in the successor extension
				 * If so, we need show a pop up with a validation message and stop the user from adding it again
				 * If not, proceed with already existing business logic
				 */
				let sSuccessorExtension = oIdocExtensionWizardDetailsModel.getProperty("/Extension/Pretyp");
				let promiseExtensionSegmentsLookup = GetListsData.getSegmentsByExtension(thisObject, sSuccessorExtension);
				promiseExtensionSegmentsLookup.then(function (arrSegments) {
					let oMatchSuccessor = arrSegments.find(oSegment => {
						return oSegment.SEGMENT === sTerm;
					});
					if (oMatchSuccessor) {
						oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueState", "Error");
						oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueStateText", "Segment already exists in successor extension");
						thisObject.enableSave();
					} else {
						// Bug 11290: Do not allow user to add duplicate segments
						// Added "true" as fifth parameter to getInterfaceMappingTablesList function
						let promiseSegmentLookup = GetListsData.getInterfaceMappingTablesList(thisObject, sTerm, oCommChannel, 20, true);
						promiseSegmentLookup.then(function (oData) {
							if (oData.results.length > 0) {
								// Bug 11290: Do not allow user to add duplicate segments
								// Added a pop up warning the user that changes made here may cause issues in other extensions
								let promise = new Promise(function (resolve, reject) {
									let promiseShowPopupAlert = Utilities.showPopupAlert("Changes made to this segment may cause issues in other extensions. Be careful!",
										MessageBox.Icon.WARNING, "Warning", [MessageBox.Action.OK]);
									promiseShowPopupAlert.then(function () {
										resolve();
									}, function () {
										reject();
									});
								});
								promise.then(function () {
									let promiseSegmentDetails =
										GetListsData.getInterfaceIdocSegmentDetails(thisObject, sTerm);
									promiseSegmentDetails.then(function (oSegmentData) {
										oSegmentData.results[0].Qualifier = oSegmentData.results[0].Qualifier === "X" ? true : false;
										oSegmentData.results[0].MAINIDOCEXT2SEGFIELDSNAV.results.forEach((oSegmentField) => {

											oSegmentField.Isocode = oSegmentField.Isocode === "X" ? true : false;

										});
										oIdocExtensionWizardDetailsModel.setProperty("/Segment", oSegmentData.results[0]);
										oIdocExtensionWizardDetailsModel.setProperty("/Segment/segmentFieldList", oSegmentData.results[0].MAINIDOCEXT2SEGFIELDSNAV.results);
										oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueState", "None");
										oIdocExtensionWizardDetailsModel.setProperty("/Segment/SegTypeValueStateText", "");
										oIdocExtensionWizardDetailsModel.setProperty("/Segment/refTableValueState", "None");
										oIdocExtensionWizardDetailsModel.setProperty("/Segment/refTableValueStateText", "");
										oIdocExtensionWizardDetailsModel.setProperty("/Segment/Occmin", 0);
										oIdocExtensionWizardDetailsModel.setProperty("/Segment/Occmax", 1);
										oIdocExtensionWizardDetailsModel.setProperty("/WbTransport", oSegmentData.results[0].WbTransport);
										oIdocExtensionWizardDetailsModel.setProperty("/PackageSelected", oSegmentData.results[0].Usmddevclass);
										thisObject.enableSave();
									});
								});
							} else {
								// Bug 11290: Do not allow user to add duplicate segments
								// Removed the previously existing pop up that warned the user that the segment didn't exist and asked if he wanted to create it
								oIdocExtensionWizardDetailsModel.setProperty("/WbTransport", "");
								oIdocExtensionWizardDetailsModel.setProperty("/PackageSelected", "");
								oIdocExtensionWizardDetailsModel.setProperty("/Segment", {
									Segtyp: sTerm,
									segmentFieldList: [],
									SegTypeValueState: "None",
									SegTypeValueStateText: "",
									Descrp: "",
									Occmin: 0,
									Occmax: 1,
									refTableValueState: "Error",
									refTableValueStateText: "Reference Table must not be empty"
								});
								thisObject.enableSave();
							}
						});
					}
				});
				// Task 11530 - Do not allow to add segment which already exists in Successor Extension - UI
				// Added an error handler in case the backend returns an error message
				promiseExtensionSegmentsLookup.catch(function (oResponseData) {
					try {
						let sCode = JSON.parse(oResponseData.responseText).error.innererror.errordetails[0].code;
						let sBusinessMessage = JSON.parse(oResponseData.responseText).error.innererror.errordetails[0].message;
						Utilities.showPopupAlert("Error code:\n" + sCode + "\n\n" + "Error message:\n" + sBusinessMessage, MessageBox.Icon.ERROR, "Error");
					} catch (error) {
						let sGenericMessage = oResponseData.statusCode + ": " + oResponseData.message;
						Utilities.showPopupAlert("Error " + sGenericMessage, MessageBox.Icon.ERROR, "Error");
					}
				});
			}
		}
	};

	/*
	 * Event is getting triggered on change, liveChange and suggest events
	 * What this function does?
	 * Checks if value provided is valid according to getInterfaceMappingTablesList
	 * Updates the suggestion box
	 * Checks whether the save button should be enabled or disabled
	 * Clears the input and clears the suggestion box if value provided is invalid
	 */
	IdocExtensionWizardController.onChangeRefTable = function (oEvent) {
		let sTerm = oEvent.getParameter("value").toUpperCase();
		let oController = this;
		let promiseTableLookup;
		let oIdocExtensionWizardDetailsModel = oController.getView().getModel("idocExtensionWizardDetails");
		let oIdocDetails = this.getView().getModel("idocDetails");
		let oCommChannel = oIdocDetails.getProperty("/Commchannel");
		oEvent.getSource().setValue(sTerm);
		if (sTerm) {
			promiseTableLookup = GetListsData.getInterfaceMappingTablesList(this, sTerm.toUpperCase(), oCommChannel, 20);
			promiseTableLookup.then(function (oData) {
				if (oData.results.length > 0) {
					let oMatchedTable = oData.results.find(function(oRefTable){
						return oRefTable.Tabname.toUpperCase() === sTerm.toUpperCase();
					});
					oIdocExtensionWizardDetailsModel.setProperty("/refTableSuggestions", oData.results);
					if (oMatchedTable) {
						oIdocExtensionWizardDetailsModel.setProperty("/Segment/refTableValueState", "None");
						oIdocExtensionWizardDetailsModel.setProperty("/Segment/refTableValueStateText", "");
					} else {
						oIdocExtensionWizardDetailsModel.setProperty("/Segment/refTableValueState", "Error");
						oIdocExtensionWizardDetailsModel.setProperty("/Segment/refTableValueStateText", "Select a table from the list");	
					}
				} else {
					oIdocExtensionWizardDetailsModel.setProperty("/refTableSuggestions", []);
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/refTableValueState", "Error");
					oIdocExtensionWizardDetailsModel.setProperty("/Segment/refTableValueStateText", "Select a table from the list");
				}
				oController.enableSave();
			});
		} else {
			oIdocExtensionWizardDetailsModel.setProperty("/refTableSuggestions", []);
			oIdocExtensionWizardDetailsModel.setProperty("/Segment/refTableValueState", "Error");
			oIdocExtensionWizardDetailsModel.setProperty("/Segment/refTableValueStateText", "Reference Table must not be empty");
			oController.enableSave();
		}
	};
	
	IdocExtensionWizardController.onChangeField = function (oEvent) {
		if (oEvent) {
			if (oEvent.getParameters().hasOwnProperty("value")) { //Change to uppercase if value property exists. Do not do anything if selectedItem exists
				let iInput = oEvent.getSource();
				if (iInput) {
					iInput.setValue(iInput.getValue().toUpperCase());
				}
			}
		}
		this.enableSave();
	};

	IdocExtensionWizardController.onChangeParentSegment = function (oEvent) {
		let sSelectedItem = oEvent.getParameter("selectedItem").getKey();
		let oIdocDetailsModel = this.getView().getModel("idocDetails");
		let arrAllSegmentsList = oIdocDetailsModel.getProperty("/MAIN2IDOCSTRUCTNAV");
		let oIdocExtensionWizardDetailsModel = this.getView().getModel("idocExtensionWizardDetails");
		let selectedSegmentRecord = arrAllSegmentsList.results.filter(function (idocItem) {
			return idocItem.Segmenttype === sSelectedItem;
		});

		oIdocExtensionWizardDetailsModel.setProperty("/ParentSegment/isCustomSegment", selectedSegmentRecord[0].Segext);
		oIdocExtensionWizardDetailsModel.setProperty("/ParentSegment/Hlevel", selectedSegmentRecord[0].IDOCSTRUCT2SYNTAXATTRIBNAV.results[0].Hlevel);
	};

	/*
	 * Async function to check if value provided as argument for Data Element is indeed a valid value
	 * To know if it's a valid value, we check in the backend by calling GetListsData.getElementLookup
	 */
	IdocExtensionWizardController.getValidDataElement = async function (sDataElement) {
		if (sDataElement === "" || sDataElement === undefined) {
			return undefined;
		}

		let oData = await GetListsData.getElementLookup(this, sDataElement, 5);
		
		return oData.results.find((oResult) => {
			return oResult.Rollname === sDataElement;
		});
	};

	IdocExtensionWizardController.onChangeDataElement = async function (oEvent) {
		let sInputValue = oEvent.getSource().getValue().toUpperCase();
		let oIdocExtensionWizardDetailsModel = this.getView().getModel("idocExtensionWizardDetails");
		let sPath = oEvent.getSource().getParent().getBindingContextPath();
		let oDataElementRow = oIdocExtensionWizardDetailsModel.getProperty(sPath);
		
		// Bug 11213 - Make Data Element mandatory for new Segment Fields
		let oDataElement = await this.getValidDataElement(sInputValue);
		if (oDataElement) {
			oDataElementRow.Rollname = oDataElement.Rollname;
			oDataElementRow.Expleng = oDataElement.Leng;
		} else {
			oDataElementRow.Rollname = "";
			oDataElementRow.Expleng = "";
		}
		
		this.enableSave();
	};

	IdocExtensionWizardController.onAddSegmentField = function () {
		let oController = this;
		let oIdocExtensionWizardDetailsModel = oController.getView().getModel("idocExtensionWizardDetails");
		let sSelectedSegment = oIdocExtensionWizardDetailsModel.getProperty("/Segment/Segtyp");
		let arrSegmentFieldList = oIdocExtensionWizardDetailsModel.getProperty("/Segment/segmentFieldList");
		if (sSelectedSegment && arrSegmentFieldList) {
			arrSegmentFieldList.push({});
			oIdocExtensionWizardDetailsModel.refresh();
		}
		oIdocExtensionWizardDetailsModel.setProperty("/Segment/segmentFieldList", arrSegmentFieldList);
		this.enableSave();
	};
	
	/*
	 *	Function to enable/disable Insert and Add New button after changing the required Input fields in any of the called Wizard Steps
	 *	Depends on the radio button selected by the user in Step 1
	 */
	IdocExtensionWizardController.enableSave = function () {
		let oIdocExtensionWizardDetailsModel = this.getView().getModel("idocExtensionWizardDetails");
		let sExtnType = oIdocExtensionWizardDetailsModel.getProperty("/sSelectedExtnType");
		let sExtentionNameValueState = oIdocExtensionWizardDetailsModel.getProperty("/Extension/extensionNameValueState");
		let sCopyFromExtention = oIdocExtensionWizardDetailsModel.getProperty("/Extension/Firsttyp");
		let sDescription = oIdocExtensionWizardDetailsModel.getProperty("/Extension/extensionDescription");
		let sSegmentTypeValueState = oIdocExtensionWizardDetailsModel.getProperty("/Segment/SegTypeValueState");
		let sRefTableValueState = oIdocExtensionWizardDetailsModel.getProperty("/Segment/refTableValueState");
		let iOccMax = oIdocExtensionWizardDetailsModel.getProperty("/Segment/Occmax");
		let iOccMin = oIdocExtensionWizardDetailsModel.getProperty("/Segment/Occmin");
		let sRefTable = oIdocExtensionWizardDetailsModel.getProperty("/Segment/Descrp");
		let sParentSegment = oIdocExtensionWizardDetailsModel.getProperty("/ParentSegment/Parseg");
		let arrSegmentFieldList = oIdocExtensionWizardDetailsModel.getProperty("/Segment/segmentFieldList");
		let isRequiredFieldsInSegmentMissing;

		// Bug 11428 - Field not required for segment 
		// At least one entry is required in the arrSegmentFieldList array to enable the save button
		if (arrSegmentFieldList.length === 0) {
			isRequiredFieldsInSegmentMissing = true;
		} else {
			// Bug 11213 - Make Data Element mandatory for new Segment Fields
			// Iterate through the fields in segment looking for at least one entry where field name or data element is blank or data element is invalid
			// If found, then it means that we have a problem, because these two fields are required, so, save button should be disabled
			// If not, then it means that everything is fine, and save button can be enabled (of course if all other validations are good too)
			isRequiredFieldsInSegmentMissing = arrSegmentFieldList.find((oField) => {
				return (oField.Fieldname === "" || oField.Fieldname === undefined ||  oField.Rollname === "" || oField.Rollname === undefined);
			});
		}
		
		// Bug 11428 - Field not required for segment 
		// Conditions for enabling Save button when adding or editing and extension
		if (this.byId("SelectExtensionTypeStep").getValidated() && this.byId("CreateExtensionStep").getValidated() &&
			sSegmentTypeValueState === "None" && sRefTableValueState === "None" && 
			sRefTable && sParentSegment && iOccMax >= iOccMin && arrSegmentFieldList.length > 0 && !isRequiredFieldsInSegmentMissing) {
			oIdocExtensionWizardDetailsModel.setProperty("/bEnableSave", true);
		
		// Bug 11159 - Extension "COPY" option doesn't work
		// New condition to make Save button enabled when selected extension type is "Copy"
		} else if (sExtnType === "COPY" && sExtentionNameValueState === "None" && sCopyFromExtention && sDescription) {
			oIdocExtensionWizardDetailsModel.setProperty("/bEnableSave", true);
		
		} else {
			oIdocExtensionWizardDetailsModel.setProperty("/bEnableSave", false);
		}
	};

	IdocExtensionWizardController.onSegmentSave = async function () {
		let oController = this;
		let oIdocExtensionWizardDetailsModel = oController.getView().getModel("idocExtensionWizardDetails");
		let sExtnType = oIdocExtensionWizardDetailsModel.getProperty("/sSelectedExtnType");
		let sSelectedSegment = oIdocExtensionWizardDetailsModel.getProperty("/Segment");


		oController._selectTransportPackage(false, true, true)
		.then(function(oResponseSelection){
			// Bug 11159 - Extension "COPY" option doesn't work
			// For extension type "COPY" we need to skip saving segment and only save extension
			if (sExtnType === "COPY") {
				oController.onExtensionSave(
					oResponseSelection.workbenchTransport,
					oResponseSelection.package);
				return;
			} 

			let oData = {
				Segtyp: sSelectedSegment.Segtyp,
				Qualifier: sSelectedSegment.Qualifier ? "X" : "",
				Generated: "",
				Descrp: sSelectedSegment.Descrp,
				WbTransport: oResponseSelection.workbenchTransport,
				// CustTransport: "", Should not be sent to the backend
				Usmddevclass: oResponseSelection.package,
				MAINIDOCEXT2SEGHEADNAV: [{
					Segtyp: sSelectedSegment.Segtyp,
					Version: sSelectedSegment.segVersion,
					Segdef: "",
					Released: "",
					Closed: "",
					Credate: "",
					Cretime: "",
					Ldate: "",
					Ltime: "",
					Fieldnum: "",
					Expleng: "",
					Applrel: ""
				}],
				MAINIDOCEXT2SEGFIELDSNAV: [],
				IDOCEXTSEG2MSGNAV: []
			};

			if (sSelectedSegment.segmentFieldList) {
				sSelectedSegment.segmentFieldList.forEach((oSegmentField) => {
					let oSegFieldDetails = {
						Segtyp: sSelectedSegment.Segtyp,
						Pos: oSegmentField.Pos ? oSegmentField.Pos : "",
						Fieldname: oSegmentField.Fieldname,
						Rollname: oSegmentField.Rollname,
						Isocode: oSegmentField.Isocode ? "X" : "",
						Expleng: oSegmentField.Expleng
					};
					oData.MAINIDOCEXT2SEGFIELDSNAV.push(oSegFieldDetails);
				});
			}
			(new DMRDataService(
				oController,
				"DRF_MODEL_LIST",
				"/MAINIDOCEXTSEGSet",
				"saveSegmentDetails",
				"/", // Root of the received data
				"Save Segment Details"
			))
			.saveData(
				false,
				oData,
				null, {
					success: {
						fCallback: function (oParams, oResponseData) {
							let sError = "";
							let arrMessages = [];
							jQuery.extend(true, arrMessages, oResponseData.IDOCEXTSEG2MSGNAV.results);
							arrMessages.every((oMessage) => {
								if (oMessage.MessageType === "E") {
									// If Error exists in list, set value and break
									sError = "X";
									return false;
								} else {
									return true;
								}
							});
							if (sError === "X") {
								ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
							} else {
								oController.onExtensionSave(
									oResponseSelection.workbenchTransport,
									oResponseSelection.package);
							}
						},
						oParam: this
					},
					error: {
						fCallback: function (oParams, oResponseData) {
							Utilities.showPopupAlert("Error: " + oResponseData.message, MessageBox.Icon.ERROR, "Error");
						}
					}
				}
			);
		});
	};

	IdocExtensionWizardController.onExtensionSave = async function (sWorkbenchTransportParam, sPackageParam) {
		let oController = this;
		let oIdocExtensionWizardDetailsModel = oController.getView().getModel("idocExtensionWizardDetails");
		let sSelectedSegment = oIdocExtensionWizardDetailsModel.getProperty("/Segment");
		let sReferSegment = oIdocExtensionWizardDetailsModel.getProperty("/ReferSegment");
		let sSelectedExtension = oIdocExtensionWizardDetailsModel.getProperty("/Extension");
		let sParentSegment = oIdocExtensionWizardDetailsModel.getProperty("/ParentSegment");
		let Msgtyp = oIdocExtensionWizardDetailsModel.getProperty("/Msgtyp");
		let Idoctyp = oIdocExtensionWizardDetailsModel.getProperty("/Idoctyp");
		let BusinessSysMap = oIdocExtensionWizardDetailsModel.getProperty("/BusinessSysMap");
		let Appl = oIdocExtensionWizardDetailsModel.getProperty("/Appl");
		let Outbimpl = oIdocExtensionWizardDetailsModel.getProperty("/Outbimpl");
		let sIdocAction= oIdocExtensionWizardDetailsModel.getProperty("/idocAction");
		let sHlevel = oIdocExtensionWizardDetailsModel.getProperty("/ParentSegment/Hlevel");
		let sExtnType = oIdocExtensionWizardDetailsModel.getProperty("/sSelectedExtnType");
		let isCustomSegment = oIdocExtensionWizardDetailsModel.getProperty("/ParentSegment/isCustomSegment");
	
		let sParentSeg;
		if (sIdocAction !== "editSegment")
		{
			if (isCustomSegment === "") {
				if (sSelectedSegment.Occmax > 1) {
					sHlevel = 2;
				} else {
					sHlevel = 1;
				}
				sReferSegment = sParentSegment.Parseg;
			} else {
				sHlevel = parseInt(sParentSegment.Hlevel, 10) - 1;
				sParentSeg = sParentSegment.Parseg;
				sReferSegment = sReferSegment;
			}
		}
		
		let oData = {
			Outbimpl: Outbimpl,
			WbTransport: sWorkbenchTransportParam,
			Usmddevclass: sPackageParam,
			Appl: Appl,
			Msgtyp: Msgtyp,
			Idoctyp: Idoctyp,
			Businesssystem: BusinessSysMap,
			MAIN2IDOCSTRUCTNAV: [{
				Outbimpl: Outbimpl,
				Idoctyp: sSelectedExtension.extensionName,
				Segmenttype: sSelectedSegment.Segtyp,
				IDOCSTRUCT2IDOCATTRIBNAV: [{
					Pretyp: sExtnType === "SUCCESSOR" ? sSelectedExtension.Pretyp : "",
					Firsttyp: sExtnType === "COPY" ? sSelectedExtension.Firsttyp : "",
					Basictype: sSelectedExtension.basicType,
					Outbimpl: "",
					Qualifier: "",
					Generated: "",
					Descrp: sSelectedExtension.extensionDescription,
					Presp: "",
					Pwork: "",
					Plast: ""
				}],
				IDOCSTRUCT2SYNTAXATTRIBNAV: [{
					Nr: sSelectedSegment.Nr,
					Outbimpl: "",
					Segtyp: sSelectedSegment.Segtyp,
					Parflg: sSelectedSegment.Parflg,
					Parseg: sParentSeg,
					Parpno: sSelectedSegment.Parpno,
					Mustfl: "",
					Occmin: sSelectedSegment.Occmin ? sSelectedSegment.Occmin.toString() : "",
					Occmax: sSelectedSegment.Occmax ? sSelectedSegment.Occmax.toString() : "",
					Hlevel: sHlevel.toString(),
					Refsegtyp: sReferSegment
				}]
			}],
			"MAIN2SEGMENTSNAV": [{
				"SEG2SEGATTRIBNAV": [

				],
				"SEG2SEGDEFATTRIBNAV": [

				]
			}],
			"MAIN2SEGSTRUCTNAV": [],
			"MAIN2MESSAGENAV": []
		};

		(new DMRDataService(
			oController,
			"DRF_MODEL_LIST",
			"/MAINIDOCSTRUCTURESet",
			"saveExtensionDetails",
			"/", // Root of the received data
			"Save Extension Details"
		)).saveData(
			false,
			oData,
			null, {
				success: {
					fCallback: function (oParams, oResponseData) {
						let sError;
						let arrMessages = [];
						jQuery.extend(true, arrMessages, oResponseData.MAIN2MESSAGENAV.results);
						arrMessages.every((oMessage) => {
							if (oMessage.MessageType === "E") {
								//If Error exists in list, set value and break
								sError = "X";
								return false;
							} else {
								return true;
							}
						});
						sap.ui.core.BusyIndicator.hide();
						ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
						// Bug 13082 - added the Model Messages call to in case the type is "InterfaceWizard" then return to the Interface page
						if (sError !== "X") {
							ModelMessages.type = "InterfaceWizard";
							ModelMessages.objectContext = this;
							ModelMessages.history = History.getInstance();
							ModelMessages.router = sap.ui.core.UIComponent.getRouterFor(oController);
						}
					},
					oParam: this
				},
				error: {
					fCallback: function (oParams, oResponseData) {
						Utilities.showPopupAlert("Error: " + oResponseData.message, MessageBox.Icon.ERROR, "Error");
					}
				}
			}
		);
	};
	IdocExtensionWizardController.onWizardCancel = function () {
		// this.refreshWizard();
		sap.ui.getCore().getEventBus().unsubscribe("rdgChannel", "showMapping", this.onWizardCancel, this);
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);

		let oHistory = History.getInstance();
		let sPreviousHash = oHistory.getPreviousHash();

		if (sPreviousHash && sPreviousHash !== "") {
			// We have a previus hash, so browser knows how to go back of a page
			window.history.go(-1);
		} else {
			let arrCurrentRoute = oHistory.aHistory[oHistory.aHistory.length - 1].split("*");
			if (arrCurrentRoute[0].includes("IdocExtensionWizard")) {
				this.getView().setModel("", "idocExtensionWizardDetails");
				oRouter.navTo("Interfaces");
			} else {
				// We got here with a deeplink so we don't know how to go back
				// Just return to your homepage or whatever route you want to!
				// Replace HomeRoute with your current route ;)
				oRouter.navTo("TargetHome", {}, true);
			}
		}

	};

	IdocExtensionWizardController.onDeleteSegmentField = function (oEvent) {
		let oController = this;
		let oIdocExtensionWizardDetailsModel = oController.getView().getModel("idocExtensionWizardDetails");
		let oTable = oEvent.getSource();
		let oClickedItem = oEvent.getParameters().listItem;

		let promise = new Promise(function (resolve, reject) {
			// Show a message asking the user to confirm deletion. Exit if cancel 
			let promiseShowPopupAlert = Utilities.showPopupAlert("The decision row will be deleted. Continue?", MessageBox.WARNING, "Delete?", [
				MessageBox.Action.YES, MessageBox.Action.NO
			]);
			promiseShowPopupAlert.then(function () {
				resolve();
			}, function () {
				reject();
			});
		});

		/**
		 * Get the index of the row being deleted and remove from the model. */

		promise.then(function () {
			// Get the index clicked
			let iClickedIndex = oTable.getItems().indexOf(oClickedItem);
			let arrSegmentFields = oIdocExtensionWizardDetailsModel.getProperty("/Segment/segmentFieldList");
			arrSegmentFields.splice(iClickedIndex, 1);
			oIdocExtensionWizardDetailsModel.setProperty("/Segment/segmentFieldList", arrSegmentFields);
			oController.enableSave();
		});
	};

	IdocExtensionWizardController.isPageEdit = function () {
		if (this._wizard.getProgressStep() !== this.byId("SelectExtensionTypeStep")) {
			return true;
		}
		return false;
	};

	/*
	 * Bug 11159 - Extension "COPY" option doesn't work
	 * If Extension Type is "New" or "Successor" or "blank" and Parent Segment is empty, then we get a value state message
	 * If Extension Type is "Copy", then we don't get a value state message even if Parent Segment is empty
	 */
	IdocExtensionWizardController.ParentSegmentValueState = function (sExtensionType, sParentSegment) {
		if ((sExtensionType === "" || sExtensionType === "NEW" || sExtensionType === "SUCCESSOR") && sParentSegment === "") {
			return "Error";
		}
		if (sExtensionType === "COPY") {
			return "None";
		}
		return "None";
	};

	/*
	 * Bug 11159 - Extension "COPY" option doesn't work
	 * If editing a segment, then Parent Segment must be disabled
	 * Also, If Extension Type is "Copy", then Parent Segment must be disabled
	 * Otherwise, it must be enabled
	 */
	IdocExtensionWizardController.ParentSegmentEnabled = function (sIdocAction, sExtensionType) {
		if (sIdocAction === "editSegment") {
			return false;
		}
		if (sExtensionType === "COPY") {
			return false;
		}
		return true;
	};

	/*
	 * Bug 11159 - Extension "COPY" option doesn't work
	 * If Extension Type is "Copy", then Segment Type is not required
	 * Otherwise, it is required
	 */
	IdocExtensionWizardController.SegmentTypeRequired = function (sExtensionType) {
		if (sExtensionType === "COPY") {
			return false;
		}
		return true;
	};

	/*
	 * Bug 11159 - Extension "COPY" option doesn't work
	 * If editing a segment, then Segment Type must be disabled
	 * Also, If Extension Type is "Copy", then Segment Type must be disabled
	 * Otherwise, it must be enabled
	 */
	IdocExtensionWizardController.SegmentTypeEnabled = function (sIdocAction, sExtensionType) {
		if (sIdocAction === "editSegment") {
			return false;
		}
		if (sExtensionType === "COPY") {
			return false;
		}
		return true;
	};

	/*
	 * Bug 11159 - Extension "COPY" option doesn't work
	 * If Extension Type is "Copy", then Reference Table is not required
	 * Otherwise, it is required
	 */
	IdocExtensionWizardController.ReferenceTableRequired = function (sExtensionType) {
		if (sExtensionType === "COPY") {
			return false;
		}
		return true;
	};

	/*
	 * Bug 11159 - Extension "COPY" option doesn't work
	 * If Extension Type is "Copy", then Reference Table must be disabled
	 * Otherwise, it must be enabled
	 */
	IdocExtensionWizardController.ReferenceTableEnabled = function (sExtensionType) {
		if (sExtensionType === "COPY") {
			return false;
		}
		return true;
	};

	/*
	 * Bug 11159 - Extension "COPY" option doesn't work
	 * If Extension Type is "Copy", then Qualified Segment must be disabled
	 * Otherwise, it must be enabled
	 */
	IdocExtensionWizardController.QualifiedSegmentEnabled = function (sExtensionType) {
		if (sExtensionType === "COPY") {
			return false;
		}
		return true;
	};

	/*
	 * Bug 11159 - Extension "COPY" option doesn't work
	 * If Minimum Value is lower than or equals to Maximum Value, then everything is fine
	 * Otherwise, ValueState should show an error message
	 */
	IdocExtensionWizardController.MinimumMaximumNumbersValueState = function (nMininumNumber, nMaximumNumber) {
		if (nMaximumNumber < nMininumNumber) {
			return "Error";
		}
		return "None";
	};

	/*
	 * Bug 11159 - Extension "COPY" option doesn't work
	 * If Extension Type is "Copy", then Minimum and Maximum Numbers must be disabled
	 * Otherwise, it must be enabled
	 */
	IdocExtensionWizardController.MinimumMaximumNumbersEnabled = function (sExtensionType) {
		if (sExtensionType === "COPY") {
			return false;
		}
		return true;
	};

	/*
	 * Bug 11159 - Extension "COPY" option doesn't work
	 * If Extension Type is "Copy", then Add Field button must be disabled
	 * Otherwise, it must be enabled
	 */
	IdocExtensionWizardController.AddFieldEnabled = function (sExtensionType) {
		if (sExtensionType === "COPY") {
			return false;
		}
		return true;
	};

	return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.Interfaces.IdocExtensionWizard", IdocExtensionWizardController);
});