sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/model/GetLists",
    "dmr/mdg/supernova/SupernovaFJ/libs/DataService",
    "dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "sap/m/MessageBox"
], function(GetListsData, DMRDataService, Utilities, MessageBox) {
    "use strict";
    
    let LogicalSystemCreate = {};
    
    LogicalSystemCreate.onCreateLogicalSystem = function() {
        let oController = this;
        let oInterfaceModel = oController.getView().getModel("viewInterface");
        let oLogicalSystem = oInterfaceModel.getProperty("/LogicalSysDetailsFragment");
    
        this._selectTransportPackage(false, true, false)
        .then(function(oResponseSelection){
            let oData = {
                Logsys: oLogicalSystem.LogicalSys,
                Message: "",
                Desc: oLogicalSystem.Desc,
                Messagetype: "",
                Transport: oResponseSelection.workbenchTransport
            };

            (new DMRDataService(
                oController,
                "BUSINESS_SYSTEM",
                "/LOGSYSSet",
                "CreateLogicalSystem",
                "/", // Root of the received data
                "Create Logical System"
            )).saveData(
                false,
                oData,
                null, {
                    success: {
                        fCallback: function (oParams, oResponseData) {
                            let sError = "";
                            if(oResponseData.Messagetype === "E") {
                                //If Error exists in list, set value and break
                                sError = "X";
                            } 
                            if(sError !== "X") {
                                let promiseInterfaceSearchHelps = 
                                    GetListsData.getInterfaceSearchHelps(oController);
                                
                                promiseInterfaceSearchHelps.then(function (oSearchResults) {
                                    oSearchResults[0].SERCHLP2LOGSYSNAV.results.push({
                                        Description: "Custom Logical System",
                                        Logsys: "Custom",
                                        Zkey: "1"
                                    });
                                    oInterfaceModel.setProperty("/SearchHelps/arrLogicalSystem", oSearchResults[0].SERCHLP2LOGSYSNAV.results);
                                });
                                oInterfaceModel.setProperty("/editBusSystems/Logsys", oLogicalSystem.LogicalSys);
                                // Close the dialog
                                oController.LogicalSystemCreateDialog.close();
                            }
                        },
                        oParam: this
                    },
                    error: function () {
                        Utilities.showPopupAlert("Logical System could not be created.", MessageBox.Icon.ERROR, "LOGICAL SYSTEM SAVE ERROR");
                    }
                }
            );
        });
    };

    LogicalSystemCreate.onCancelLogicalSystem = function() {
        let oController = this;
        let oInterfaceController = this.getView().getModel("viewInterface");
        oInterfaceController.setProperty("/LogicalSysDetailsFragment", {});
        oController.LogicalSystemCreateDialog.close();
    };

    return LogicalSystemCreate;
});