sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
    "dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "sap/m/MessageBox"
], function(BaseController, Utilities, MessageBox) {
    "use strict";

    let TableKeyValues = {};

    /**
	 * Task 11511 - Add Date and Time Picker for relevant key fields
	 * Added formatter function to determine which element should be made visible
	 * 		-> Combobox is visible when
	 * 				->	Key field is of type other than date and time
	 * 				->	Dropdown list items are available for selection
	 * 				->	User has not selected mapping key from table		
	 */
	TableKeyValues.isComboBoxKeyFieldVisible = function(arrKeyValues, oMappingKeyInfo, sKind) {
		if(sKind === "D" || sKind === "T") {
			return false;
		} else if(arrKeyValues.length === 0 || oMappingKeyInfo !== undefined) {
			return false;
		}
		return true;
	};
    
    /**
	 * Feature 11655 - Set Mapping Key from Mapping Table
	 * Formatter function to add complete relevant path of mapping key to tooltip when user selects mapping from table
	 */
	TableKeyValues.addMappingInputInformation = function(oMappingKeyInfo) {
		if(oMappingKeyInfo) {
			return oMappingKeyInfo.Parentpath + "-" + oMappingKeyInfo.Segmenttype + "-" + oMappingKeyInfo.Fieldname;
		} else {
			return undefined;
		}
	};

    /**
	 * Task 11511 - Add Date and Time Picker for relevant key fields
	 * Added formatter function to determine which element should be made visible
	 * 		-> Input Box is visible when
	 * 				->	Key field is of type other than date and time and user has not selected mapping key from table
	 * 				->	Dropdown list items are available for selection		
	 */
	TableKeyValues.isInputKeyFieldVisible = function(arrKeyValues, oMappingKeyInfo, sKind) {
		if((sKind === "D" || sKind === "T") && oMappingKeyInfo === undefined) {
			return false;
		}else if(arrKeyValues.length !== 0 && oMappingKeyInfo === undefined) {
			return false;
		}
		return true;
	};

    	/**
	 * Task 11511 - Add Date and Time Picker for relevant key fields
	 * Added formatter function to determine which element should be made visible
	 * 		-> Date Picker is visible when
	 * 				->	Key field is of type date
	 * 				->	User has not selected mapping key from table	
	 */
    TableKeyValues.isDateKeyFieldVisible = function(oMappingKeyInfo, sKind) {
		if(sKind !== "D") {
			return false;
		}else if(oMappingKeyInfo !== undefined) {
			return false;
		}
		return true;
	};

	/**
	 * Task 11511 - Add Date and Time Picker for relevant key fields
	 * Added formatter function to determine which element should be made visible
	 * 		-> Date Picker is visible when
	 * 				->	Key field is of type time
	 * 				->	User has not selected mapping key from table	
	 */
	TableKeyValues.isTimeKeyFieldVisible = function(oMappingKeyInfo, sKind) {
		if(sKind !== "T") {
			return false;
		}else if(oMappingKeyInfo !== undefined) {
			return false;
		}
		return true;
	};

    	/**
	 * Feature 11655 - Set Mapping Key from Mapping Table
	 * Event called on Key Value List Item Button to set Mapping key
	 * User is allowed to either select key value mapping from system provided drop down values or free text or other field value in mapping table
	 * Store the path of the Key Value list item for future Save and open SteMappingKeyValueDialog
	 */
    TableKeyValues.onClickSetMappingKey = function(oEvent) {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let sMappedKeyPath = oEvent.getSource().getBindingContext("viewInterface").getPath();
		oInterfaceModel.setProperty("/mappedValue/sMappedKeyPath", sMappedKeyPath );

		if (!oController.SetMappingKeyValueDialog) {
			oController.SetMappingKeyValueDialog =
				sap.ui.xmlfragment(
					"SetMappingKeyValue",
					"dmr.mdg.supernova.SupernovaFJ.view.Interfaces.SetMappingKeyValue",
					oController);
			oController.getView().addDependent(oController.SetMappingKeyValueDialog);
		}
		
		// Open the dialog
		oController.SetMappingKeyValueDialog.open();
	};

	TableKeyValues.onClickClearMappingKey = function(oEvent) {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let sMappedKeyPath = oEvent.getSource().getBindingContext("viewInterface").getPath();
		oInterfaceModel.setProperty(sMappedKeyPath + "/Value", undefined);
		oInterfaceModel.setProperty(sMappedKeyPath + "/oMappingKeyInfo", undefined);
	};

    	/**
	 * Event called on Button Save for KeyValue fragment
	 * Populate KeyValue Property of CurrentRow based on the details added on KeyValue Fragment
	 * 		->	Show Error Popup if all mandatory fields are not provided
	 * 		-> 	If mapping is selected from the mapping table,
	 * 				->	Add the complete relevant path (To be changed to relevant nodekey on Save Maping) to KeyValue
	 * 				->	Add details to navigation SEGSTRUCT2SOAKEYMAPNAV of the current row
	 * 		->	Else, append the field and selected value to KeyValue property
	 */
    TableKeyValues.onKeyValueSave = function() {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let oMappedValue = oInterfaceModel.getProperty("/mappedValue");
		let arrKeyValueList = oMappedValue.arrKeyValueList;
		let sPath = oMappedValue.sPath;
		let sKeyValue = "";
		let oIdocTable = oController.byId("IdocTree");
		let oCurrentRow = oIdocTable.getModel().getProperty(sPath);
		if(oCurrentRow.SEGSTRUCT2SOAKEYMAPNAV) {
			oCurrentRow.SEGSTRUCT2SOAKEYMAPNAV.results = [];
		}
		
		
		arrKeyValueList.every((oKeyValue) => {
			if(!oKeyValue.Value) {
				sKeyValue = "";
				let promiseShowPopupAlert =
					Utilities.showPopupAlert("Please fill in the required fields and try again.",
											MessageBox.Icon.ERROR,
											"Mandatory fields are empty");
				promiseShowPopupAlert.then(function () {
				});
				return false;
			} else {
				/**
				 * Feature 11655 - Set Mapping Key from Mapping Table
				 * If Mapping value is selected from the mapping table
				 * 		->	Add the Key field name and the complete relevant path (Parent_path-Segment_type-Field-name) to KeyValue 
				 * 		-> 	Add details to navigation SEGSTRUCT2SOAKEYMAPNAV with Node_key to be used to convert the value to its unique node identifier during save mapping
				 */
				if(oKeyValue.oMappingKeyInfo) {
					let oMappingKeyInfo = oKeyValue.oMappingKeyInfo;
					sKeyValue = sKeyValue === "" ? 
						oKeyValue.Text + ":{{" + oMappingKeyInfo.Parentpath + "-" + oMappingKeyInfo.Segmenttype + "-" + oMappingKeyInfo.Fieldname + "}}": 
						sKeyValue + "," + oKeyValue.Text + ":{{" + oMappingKeyInfo.Parentpath + "-" + oMappingKeyInfo.Segmenttype + "-" + oMappingKeyInfo.Fieldname + "}}";
					oCurrentRow.SEGSTRUCT2SOAKEYMAPNAV.results.push({
						Proxystructure: oInterfaceModel.getProperty("/selectedInterface/Msgtyp"),
						Outbimpl: oInterfaceModel.getProperty("/selectedInterface/Implementation"),
						Nodekey: oMappingKeyInfo.Nodekey,
						Fieldname: oMappingKeyInfo.Fieldname,
						Parentpath: oMappingKeyInfo.Parentpath,
						Parentseg: oMappingKeyInfo.Parentseg,
						Segmenttype: oMappingKeyInfo.Segmenttype,
						Tablekey: oKeyValue.Text,
						Fieldnodekey: oCurrentRow.Fieldnodekey.toString()
					});
				} else {
					sKeyValue = sKeyValue === "" ? oKeyValue.Text + ":" + oKeyValue.Value : 
											sKeyValue + "," + oKeyValue.Text + ":" + oKeyValue.Value;
				}
				return true;
			}
		});
		if(sKeyValue) {
			oCurrentRow.Keyvalue = sKeyValue;
			oInterfaceModel.setProperty("/mappedValue", {});
			oIdocTable.getModel().refresh();
			oController.TableKeyValuesDialog.close();
		}
	};
	
	TableKeyValues.onKeyValueCancel = function() {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let oMappedValue = oInterfaceModel.getProperty("/mappedValue");
		let sPath = oMappedValue.sPath;
		let oIdocTable = oController.byId("IdocTree");
		let oCurrentRow = oIdocTable.getModel().getProperty(sPath);
		
		let promiseShowPopupAlert =
		Utilities.showPopupAlert("Table Mapping will be removed. Do you want to continue", MessageBox.Icon.INFORMATION, "Possible Loss of Data", ["YES", "NO"]);
		promiseShowPopupAlert.then(function () {
			oCurrentRow.Table = "";
			oCurrentRow.Keyvalue = "";
			oCurrentRow.Tablefield = "";
			
			oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableValueState = "X";
			oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableFieldValueState = "X";
			oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableValueStateText = "Select a Table from the list";
			oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableFieldValueStateText = "Select a field from the list";
			
			oIdocTable.getModel().refresh();
			oInterfaceModel.setProperty("/mappedValue", {});
			
			oController.TableKeyValuesDialog.close();
		});
		
	};

	return TableKeyValues;
});