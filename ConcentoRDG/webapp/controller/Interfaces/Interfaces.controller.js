sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
	"jquery.sap.global",
	"sap/ui/model/json/JSONModel",
	"sap/m/MessageBox",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"dmr/mdg/supernova/SupernovaFJ/model/ModelMessages",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"dmr/mdg/supernova/SupernovaFJ/model/models",
	"./BusinessSystems",
	"./LogicalSystemCreate",
	"./SetMappingKeyValue",
	"./TableKeyValues"
], function (BaseController, jQuery, JSONModel, MessageBox,
	DMRDataService, GetListsData, ModelMessages, Utilities, RDGModels, BusinessSystems, LogicalSystemCreate, SetMappingKeyValue, TableKeyValues) {
	"use strict";

	let InterfacesController = {};

	InterfacesController.onInterfaceSearchListCreated = function (oEvent) {
		let oController = this;

		let comp = oEvent.getParameter("component");
		oController.InterfaceSearchList = comp;

		// Complete the mapping for data 
		oController.InterfaceSearchList.setDataMapping({
			"title": "UsmdInterface",
			"description": "Txtmi",
			"info": "Active"
		});

		oController.InterfaceSearchList.setHeaderText("Interfaces");
		oController.InterfaceSearchList.setButtonInfo({
			icon: "sap-icon://add",
			toolTip: "New interface"
		});
	
		oController.InterfaceSearchList.setListGroupingPath("UsmdInterfaceType");

		oController.InterfaceSearchList.attachSelectionChange(oController.onInterfaceSelect, oController);
		oController.InterfaceSearchList.attachActionPressed(oController.onCreateNewInterface, oController);
	};
	
	InterfacesController.onTransportPackageDialogCreated = function(oEvent){
		let comp = oEvent.getParameter("component");
		let oController = this;
		// store the component handle 
		this._transportPackageSelectionDialog = comp;

		this.getUsername()
			.then(function (oSapUserInfo) {
				let sUsername;
				sUsername = oSapUserInfo.Sapname;
				if (!sUsername) {
					Utilities.showPopupAlert(
						oController.geti18nText("common.noLogin.description"), 
						MessageBox.Icon.ERROR, 
						oController.geti18nText("common.noLogin.title"));
				} else {
					comp.setUser(sUsername);
				}

			});
	};

	InterfacesController._selectTransportPackage = function (bSelectCustomizing, bSelectWorkbench, bSelectPackage) {
		if(!this._transportPackageSelectionDialog){
			// If the component is not yet available, return a rejected promise
			return Promise.reject("Transport Package Selection Dialog is not ready.");
		}

		const oView = this.getView();
		const oViewInterfaceModel = oView.getModel("viewInterface");

		// Retrieve necessary properties from the model
		const sPreselectCustomizingTr = oViewInterfaceModel.getProperty("/customizingTransport");
		const sPreselectWorkbenchTr = oViewInterfaceModel.getProperty("/workbenchTransport");
		const sPreselectPackage = oViewInterfaceModel.getProperty("/packageSelected");

		let promise = this._transportPackageSelectionDialog.open(
			bSelectCustomizing, sPreselectCustomizingTr, 
			bSelectWorkbench, sPreselectWorkbenchTr,
			bSelectPackage, sPreselectPackage
		);

		// Wait for the dialog to close and store the selection
		let responsePromise = promise.then(function(oResponseObject){
			oViewInterfaceModel.setProperty("/customizingTransport", oResponseObject.customizingTransport);
			oViewInterfaceModel.setProperty("/workbenchTransport", oResponseObject.workbenchTransport);
			oViewInterfaceModel.setProperty("/packageSelected", oResponseObject.package);

			return oResponseObject;
		});

		return responsePromise;
	};
		
	InterfacesController.onBeforeRendering = function () {
		let oView = this.getView();
		let promiseDataModelsList =
			GetListsData.getDataModelsList(this);

		promiseDataModelsList.then(function (oData) {

			let oJsonModel = new JSONModel();
			// Set the data to the json model 
			oJsonModel.setData(oData);
			// Set the data model to the view.
			oView.setModel(oJsonModel, "dataModel");
		});
	};

	InterfacesController._onRouteMatched = function () {
		let oViewModel = this.getView().getModel("viewInterface");
		let oTreeModel = this.getView().getModel();
		if(oViewModel){
			oViewModel.setProperty("/editMode", false);
			oViewModel.setProperty("/editIdoc", false);
			oViewModel.setProperty("/enableProxyDetails", false);
			/* If there is a previous selection for Entity, trigger the selection Change event on the combobox */
			let oBusinessSystemComboBox = sap.ui.getCore().byId("container-SupernovaFJ---Interfaces--idIdocBusSystem");
			// trigger the selection change event if an item was selected
			let oSelectedBusSys = oBusinessSystemComboBox.getSelectedItem();
			if (oSelectedBusSys) {
					oBusinessSystemComboBox.fireChange({
					selectedItem: oSelectedBusSys
				});
			}
		}
		let oHistory = sap.ui.core.routing.History.getInstance();
		let sDirection = oHistory.getDirection();

		if (sDirection !== sap.ui.core.routing.HistoryDirection.Unknown &&
			sDirection !== sap.ui.core.routing.HistoryDirection.Backwards) {
		// Model for the CR Edit View

			if (!oViewModel) {
				oViewModel = new JSONModel();
				this.getView().setModel(oViewModel, "viewInterface");
			}
			this.byId("idInterfacePanel").setExpanded(false);
			this.byId("idIdocPanel").setExpanded(false);
			
			if(!oTreeModel) {
				let oTreeJSON = new JSONModel();
				this.getView().setModel(oTreeJSON);
			} else {
				//Clear Mapping Tree Table for the selected item
				oTreeModel.setProperty("/children", undefined);
			}
			oViewModel.setProperty("/", {});
			oViewModel.setProperty("/interface", undefined);
			oViewModel.setProperty("/SearchHelps", {});
			oViewModel.setProperty("/parameterTableMode", sap.m.ListMode.None);
			oViewModel.refresh();
	
			let oController = this;
			let arrOutputMode;
			let arrKeyHarm;
			let arrStorageLocation;
			let arrTimeDependency;
			//Refresh to get all Interfaces in the system
			oController.getInterfaceList(oController, undefined, undefined );
			
			let promiseOutboundImplList =
				GetListsData.getOutboundImplList(oController);
			
			promiseOutboundImplList.then((arrList) => {
				/*Added the method setSizeLimit*/
				oViewModel.setSizeLimit(arrList.length);
				oViewModel.setProperty("/SearchHelps/arrOutboundImplemenations", arrList);
				oViewModel.setProperty("/SearchHelps/iOutboundImplLength", arrList.length);
			});
			
			let promiseInterfaceSearchHelps = 
				GetListsData.getInterfaceSearchHelps(oController);
			
			promiseInterfaceSearchHelps.then(function (oSearchResults) {
				oViewModel.setProperty("/arrBusinessSystem", oSearchResults[0].SERCHLP2BUSSYSTEMNAV.results);
				oViewModel.setProperty("/SearchHelps/arrUnicode", oSearchResults[0].SEARCHLP2UNICODENAV.results);
				//Bug 11292 - Logical file path to be changed from input to select box
				let oMatchedFilePath = oSearchResults[0].SEARCHLP2FILEPATNAV.results.find(oFilePath => {
					return oFilePath.LogicalPath === "";
				});
				if(!oMatchedFilePath) {
					oSearchResults[0].SEARCHLP2FILEPATNAV.results.splice(0, 0, {
						LogicalPath: "",
						Description: "",
						Zkey: 1
					});
				}
				oViewModel.setProperty("/SearchHelps/arrLogicalFilePath", oSearchResults[0].SEARCHLP2FILEPATNAV.results);
				oViewModel.setProperty("/SearchHelps/arrCommChannel", oSearchResults[0].SERCHLP2COMMCHANNELNAV.results);
				oViewModel.setProperty("/SearchHelps/arrDomainRanges", oSearchResults[0].SERCHLP2DOMRANGESNAV.results);
				oViewModel.setProperty("/SearchHelps/arrKeyHarm", oSearchResults[0].SERCHLP2KEYHARMNAV.results);
				oSearchResults[0].SERCHLP2LOGSYSNAV.results.push({
											Description: "Custom Logical System",
											Logsys: "Custom",
											Zkey: "1"
										});
				oViewModel.setProperty("/SearchHelps/arrLogicalSystem", oSearchResults[0].SERCHLP2LOGSYSNAV.results);
				oViewModel.setProperty("/SearchHelps/arrOutputMode", oSearchResults[0].SERCHLP2OUTPUTMODENAV.results);
				//Bug 11291 - Add an empty item for Rfc Destination to dropdown list 
				let oMatchedRfcDest = oSearchResults[0].SERCHLP2RFCNAV.results.find(oRfcDest => {
					return oRfcDest.Rfc === "";
				});
				if(!oMatchedRfcDest) {
					oSearchResults[0].SERCHLP2RFCNAV.results.splice(0, 0, {
						Rfc: "",
						Description: "",
						Zkey: 1
					});
				}
				oViewModel.setProperty("/SearchHelps/arrRfcDestination", oSearchResults[0].SERCHLP2RFCNAV.results);
				oViewModel.setProperty("/SearchHelps/arrStorageLocation", oSearchResults[0].SERCHLP2STORAGENAV.results);
				oViewModel.setProperty("/SearchHelps/arrTimeDependency", oSearchResults[0].SERCHLP2TIMEDEPENNAV.results);
				arrOutputMode = oViewModel.getProperty("/SearchHelps/arrOutputMode");
				arrOutputMode.forEach(function(item, i) 
				{ 
					if (item.Outputmode === ""){ 
						arrOutputMode[i].Outputmode = "X";
					}
				});
				arrKeyHarm = oViewModel.getProperty("/SearchHelps/arrKeyHarm");
				arrKeyHarm.forEach(function(item, i) 
				{ 
					if (item.Keyharmvalue === ""){ 
						arrKeyHarm[i].Keyharmvalue = "X";
					}
				});
				arrStorageLocation = oViewModel.getProperty("/SearchHelps/arrStorageLocation");
				arrStorageLocation.forEach(function(item, i) 
				{ 
					if (item.Storagevalue === ""){ 
						arrStorageLocation[i].Storagevalue = "X";
					}
				});
				arrTimeDependency = oViewModel.getProperty("/SearchHelps/arrTimeDependency");
				arrTimeDependency.forEach(function(item, i) 
				{ 
					if (item.Timedepvalue === ""){ 
						arrTimeDependency[i].Timedepvalue = "X";
					}
				});
			});
		}
		
	};

	/**
	 * Called when a controller is instantiated and its View controls (if available) are already created.
	 * Can be used to modify the View before it is displayed, to bind event handlers and do other one-time initialization.
	 * @memberOf dmr.mdg.supernova.SupernovaFJ.view.Workflow
	 */
	InterfacesController.onInit = function () {
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		
		// Load the parameters received into the model 
		oRouter.getRoute("Interfaces").attachMatched(this._onRouteMatched, this);
		this.ModelMessages = ModelMessages;
		// task 12184 - Refactor Interfaces.controller
		this.BusinessSystems = BusinessSystems;
		this.LogicalSystemCreate = LogicalSystemCreate;
		this.SetMappingKeyValue = SetMappingKeyValue;
		this.TableKeyValues = TableKeyValues;
	};
	/*
		* On screen refresh. Fetch the rep models from the backend
		* On click on Application Save, Fetch the updated rep models and select the application
	*/
	InterfacesController.getInterfaceList = function(oController, sApplicationSaved, sOutbImplSaved) {
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let arrInterfaceSearchList = [];
		let promiseInterfaceModels = 
			GetListsData.getInterfaceModels(this);
			
		promiseInterfaceModels.then(function (arrInterfaceList) {
			oInterfaceModel.setProperty("/RepModels", arrInterfaceList);
			
			arrInterfaceList.forEach((oInterface) => {
				oInterface.REP2OUTBNAV.results.forEach((oOutbound) => {
					arrInterfaceSearchList.push({
						Active: ( oInterface.Active === "X" ) ? "Active" : "Inactive",
						UsmdInterfaceType: "Outbound",
						UsmdModel: oInterface.Usmdmodel,
						UsmdInterface: oInterface.Appl,
						UsmdInterfaceText: oInterface.Appltext,
						Implementation: oOutbound.Outbimpl,
						Txtmi: oOutbound.Outbimpl + " - " + oOutbound.Outbimpltext,
						Commchannel: oOutbound.Commchannel,
						Filtertp: oOutbound.Filtertp,
						Sequence: oOutbound.Sequence,
						arrBusinessSystem: oOutbound.OUTB2BSYSNAV.results,
						arrParametersTable: oOutbound.OUTB2PARAMNAV.results,
						arrVariants: oOutbound.OUTB2VARNAV.results,
						arrLanguages: oInterface.REP2LANG.results
					});
				});
			});
			// 8944 - More details on error page to tell user how to resolve them.
			if(!oController.getView().getModel("interfaces")) {
				let oView = oController.getView();
				let oJsonModel = new JSONModel();
				// Set the data to the json model 
				oJsonModel.setData(arrInterfaceSearchList);
				// Set the data model to the view.
				oView.setModel(oJsonModel, "interfaces");
			} else{
				oController.getView().getModel("interfaces").setProperty("/", arrInterfaceSearchList);
			}
			oController.InterfaceSearchList.setListData(arrInterfaceSearchList);
			
			if(sApplicationSaved) {
				if(!sOutbImplSaved) {
					let sSelectedOutbImpl = oInterfaceModel.getProperty("/selectedInterface/Implementation");
					let arrOutImpl = oInterfaceModel.getProperty("/SearchHelps/arrOutboundImplemenations");
					let oMatchedOutImpl = arrOutImpl.find((oImplementation) => {
						return sSelectedOutbImpl === oImplementation.ServImp;
					});
					
					sOutbImplSaved = oMatchedOutImpl.ServImp + " - " + oMatchedOutImpl.Description;
				}
				oController.InterfaceSearchList.setSelectedItemByTitleText(sApplicationSaved, sOutbImplSaved);
			}
		});
	};
	
	InterfacesController.onInterfaceChange = function(oEvent) {
		let oInterfaceModel = this.getView().getModel("viewInterface");
		let sValue = oEvent.getParameter("value");
		let bIsNewInterface = false;
		let arrRepModels = oInterfaceModel.getProperty("/RepModels");
		let sOutboundImpl = oInterfaceModel.getProperty("/selectedInterface/Implementation");
		let arrOutBoundImplementations = oInterfaceModel.getProperty("/SearchHelps/arrOutboundImplemenations");

		//Refresh the description. If match found, refill it with text. Else, keep it empty for user to fill 
		oInterfaceModel.setProperty("/selectedInterface/UsmdInterfaceText", undefined);
		//get the value of the input and transform into upper case, when oEvent is called(in this case oEvent is onInterfaceChange)
		oEvent.getSource().setValue(sValue.toUpperCase());
		
		arrRepModels.every((oRepModel) => {
			if(oRepModel.Appl === sValue) {
				//If Interface already exists in list, description should be fetched and disabled and break
				bIsNewInterface = false;
				arrOutBoundImplementations = oRepModel.REP2OUTBNAV.results;
				oInterfaceModel.setProperty("/selectedInterface/UsmdInterfaceText", oRepModel.Appltext);
				return false;
			}
			bIsNewInterface = true;
			return true;
		});
		oInterfaceModel.setProperty("/newInterface", bIsNewInterface);
		let sInterfaceDesc = oInterfaceModel.getProperty("/selectedInterface/UsmdInterfaceText");
		if(bIsNewInterface && !sInterfaceDesc)
		{
			oInterfaceModel.setProperty("/selectedInterface/interfaceDescValueState", sap.ui.core.ValueState.Error); 
		}
		else
		{
			oInterfaceModel.setProperty("/selectedInterface/interfaceDescValueState", sap.ui.core.ValueState.None); 
		}
		if(!bIsNewInterface){
			if(sOutboundImpl) {
				let oMatchedOutBoundImplementation = arrOutBoundImplementations.find((sObImplementation) => {
					return sObImplementation.Outbimpl === sOutboundImpl;
				});
				if(oMatchedOutBoundImplementation) {
					oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueState", sap.ui.core.ValueState.Error); 
					oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueStateText", "Selected replication model, outbound implementation combination is aleady existing");
				}
				else
				{
					oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueState", sap.ui.core.ValueState.None); 
					oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueStateText", "");
				}
			}
			else
			{
				oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueState", sap.ui.core.ValueState.None); 
				oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueStateText", "");
			}
		}
		else
		{
			oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueState", sap.ui.core.ValueState.None); 
			oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueStateText", "");
		}
	};

	InterfacesController.onInterfaceSelect = function (oEvent) {
		let oController = this;
		let oSelectedListObject = {};
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		oSelectedListObject = oEvent.getParameters().data;
		
		let oIdocTree = this.getView().byId("IdocTree");
		oIdocTree.setNoData("Select Business System for Mapping to view/edit details");
		
		if(oController.getView().getModel()) {
			//Clear Mapping Tree Table for the selected item
			oController.getView().getModel().setProperty("/children", undefined);
		}
		
		oInterfaceModel.setProperty("/WbTransport", null);
		oInterfaceModel.setProperty("/CustTransport", null);
		oInterfaceModel.setProperty("/parameterTableMode", sap.m.ListMode.None);
		oInterfaceModel.setProperty("/selectedBusSystemForMapping", undefined);
		oInterfaceModel.setProperty("/sSelectedSegmentForFilter", undefined);
		oInterfaceModel.setProperty("/sSelectedFieldForFilter", undefined);
		oInterfaceModel.setProperty("/sSelectedTransformationForFilter", undefined);
		oInterfaceModel.setProperty("/isBusSystemForMappingDisabled", false);
		oInterfaceModel.setProperty("/enableMappingDetailsTable", false);
		oInterfaceModel.setProperty("/editMode", false);
		oInterfaceModel.setProperty("/editIdoc", false);
		oInterfaceModel.setProperty("/enableProxyDetails", false);

		this.byId("idIdocPanel").setExpanded(false);

		// Bug 12971 - Added condition to remove the data when there is not selection
		if (!oSelectedListObject) {
			this.byId("idInterfacePanel").setExpanded(false);
			oInterfaceModel.setProperty("/selectedInterface", undefined);
			oInterfaceModel.setProperty("/interface", undefined);
			oInterfaceModel.setProperty("/parameterTableMode", sap.m.ListMode.None);
			oInterfaceModel.setProperty("/editMode", false);
			oInterfaceModel.setProperty("/editIdoc", false);
			oInterfaceModel.setProperty("/enableProxyDetails", false);
			oInterfaceModel.setProperty("/arrVariants", []);
			oInterfaceModel.setProperty("/arrParameters", []);
			oInterfaceModel.refresh();
			return;
		}
		this.byId("idInterfacePanel").setExpanded(true);

		let oSelectedObject = {};
		jQuery.extend(true, oSelectedObject, oSelectedListObject);
		              
		//Fill the selectedKey list for Business System MultiValueComboBox
		oSelectedObject.selectedBusinessSystems = [];
		oSelectedObject.arrBusinessSystem.forEach((oBusinessSystem) => {
			oSelectedObject.selectedBusinessSystems.push(oBusinessSystem.Businesssystem);
		});
		
		oSelectedObject.arrParametersTable.forEach((oParameter) => {
			oParameter.Mandatory = oParameter.Mandatory === "X" ? true : false;
		});
		
		//Fill the selectedKey list for Variants MultiValueComboBox
		oSelectedObject.selectedVariants = [];
		oSelectedObject.arrVariants.forEach((oVariant) => {
			oSelectedObject.selectedVariants.push(oVariant.Usmdtrvari);	
		});

		/**Bug 11138 - ConfigTp flag value to determine use of Filtertp value
		 * Store the value of Filter time flag in model. If set, enable Filter Time select box
		 * If blank, Clear stale Filtertp value
		 * 
		 */
		if(oSelectedObject.Implementation) {
			let arrOutboundImplemenations = oInterfaceModel.getProperty("/SearchHelps/arrOutboundImplemenations");
			let oMatchedImplementation = arrOutboundImplemenations.find((oOutboundImpl) => {
				return oOutboundImpl.ServImp === oSelectedObject.Implementation;
			});
			
			if(oMatchedImplementation) {
				oSelectedObject.ConfigTp = oMatchedImplementation.ConfigTp;
				if(oMatchedImplementation.ConfigTp === "") {
					oSelectedObject.Filtertp = undefined;
				}
			}
		}	
		
			
			
		// Store the selected Object to the Model 
		oInterfaceModel.setProperty("/selectedInterface", oSelectedObject);
		// Task 13334 - set the businessSystemChange as false
		oInterfaceModel.setProperty("/selectedInterface/businessSystemChange", false); 
		oInterfaceModel.setProperty("/interface", "existing");
		oInterfaceModel.refresh();
		
		let promiseInterfaceParameters = 
			GetListsData.getInterfaceParameters(oController, oSelectedObject.Implementation);
		
		promiseInterfaceParameters.then((arrParameters) => {
			oInterfaceModel.setProperty("/arrParameters", arrParameters);
		});
		
		let promiseIntParamValues = 
			GetListsData.getIntParamValues(oController, oSelectedObject.Implementation);
		
		promiseIntParamValues.then((arrValues) => {
			// Bug 11149 - Outbound Parameter values are duplicated in RDG
			// Storing the arrValues in the correct place, one array for each row inside arrParametersTable
			let arrParametersTable = oInterfaceModel.getProperty("/selectedInterface/arrParametersTable");
			arrParametersTable.forEach((oParameter, index) => {
				let arrFilteredValues = arrValues.filter((oValue) => {
					return oValue.Outbparameter === oParameter.Outbparameter;
				});
				oInterfaceModel.setProperty(`/selectedInterface/arrParametersTable/${index}/arrValues`, arrFilteredValues);
			});
		});
		
		let promiseVariants = 
			GetListsData.getVariantsList(oController, oSelectedObject.UsmdModel);
			
		promiseVariants.then((arrVariants) => {
			oInterfaceModel.setProperty("/arrVariants", arrVariants);
		});
	};
	
	InterfacesController.makeListFromResponse = function (oController, arrIdocStructureList) {
		let arrIdocList = [];
		arrIdocStructureList[0].MAIN2SEGSTRUCTNAV.results.forEach((oIdocSegment) => {
			let iSegmentIndex;
			let oFixedValueProperties = oIdocSegment.SEGSTRUCT2ATTRIBNAV.results[0];
			//Modify the structure in a way that it can be readily used for fixed values
			oIdocSegment.SEGSTRUCT2ATTRIBNAV = oFixedValueProperties;
			//Change to int. To be used to modify properties of Fixed Values Input Control
			oFixedValueProperties.Decimals = parseInt(oFixedValueProperties.Decimals, 10);
			oFixedValueProperties.Intlen = parseInt(oFixedValueProperties.Intlen, 10);
			oFixedValueProperties.Extlen = parseInt(oFixedValueProperties.Extlen, 10);
			oFixedValueProperties.Offset = parseInt(oFixedValueProperties.Offset, 10);
			oFixedValueProperties.Position = parseInt(oFixedValueProperties.Position, 10);
			oFixedValueProperties.Intlen = parseInt(oFixedValueProperties.Intlen, 10);
			
			iSegmentIndex = arrIdocList.findIndex((oSegmentType) => {
				return oSegmentType.Segment === oIdocSegment.Segmenttype;
			});
			if(iSegmentIndex === -1) {
				// Add as first level node
				arrIdocList.push({
					bIsSegment: true,
					ParentSegment: oIdocSegment.Parentseg,
					Segment: oIdocSegment.Segmenttype,
					children: [oIdocSegment]
				});
			} else {
				//Add as child to first level node
				arrIdocList[iSegmentIndex].children.push(oIdocSegment);
			}
		});
		
		return arrIdocList;
	};
	
	InterfacesController.setChildInTree = function (oController, oTree, oChild) {
		if (!(typeof oTree.children === "undefined" || oTree.children === null) && oTree.children.length > 0 && oChild.ParentSegment !== "") { 
			for (let i = 0; i < oTree.children.length && oChild._hasBeenSetInTree !== true; i++) {
				if (oTree.children[i].bIsSegment === true && oTree.children[i].Segment === oChild.ParentSegment) {
					oChild._hasBeenSetInTree = true;
						oTree.children[i].children[oTree.children[i].children.length] = oChild;
					break;
				} else if (oTree.children[i].bIsSegment === true) {	
					let oMatchedChild = oTree.children[i].children.find((e1) => {
						return (e1.Segment === oChild.ParentSegment);
					});
					
					if(oMatchedChild) {
						oController.setChildInTree(oController, oMatchedChild, oChild);
					} else {
						oTree.children[i].children.forEach((e2) => {
							if(e2.bIsSegment === true) {
								oController.setChildInTree(oController, e2, oChild);
							}
						});
					}
				} else {
					continue;
				}
			}
		} else if(oChild.ParentSegment === "") {
			oChild._hasBeenSetInTree = true;
			if (oTree.children !== null && oTree.children) {
				oTree.children[oTree.children.length] = oChild;
			}	
		}
		
		if (!oChild._hasBeenSetInTree && oTree.Segment === oChild.ParentSegment) {
			oChild._hasBeenSetInTree = true;
			if (oTree.children !== null && oTree.children) {
				oTree.children[oTree.children.length] = oChild;
			}
		}
	};
	
	InterfacesController.makeListForServicesStructure = function (oController, arrIdocStructureList) {
		let arrIdocList = [];
		arrIdocStructureList[0].MAIN2SEGSTRUCTNAV.results.forEach((oIdocSegment) => {
			let iSegmentIndex;
			let oFixedValueProperties = oIdocSegment.SEGSTRUCT2ATTRIBNAV.results[0];
			
			oIdocSegment.Fieldnodekey = parseInt(oIdocSegment.Fieldnodekey, 10);
			oIdocSegment.Parentnodekey = parseInt(oIdocSegment.Parentnodekey, 10);
			
			//Modify the structure in a way that it can be readily used for fixed values
			oIdocSegment.SEGSTRUCT2ATTRIBNAV = oFixedValueProperties;
			//Change to int. To be used to modify properties of Fixed Values Input Control
			oFixedValueProperties.Decimals = parseInt(oFixedValueProperties.Decimals, 10);
			oFixedValueProperties.Intlen = parseInt(oFixedValueProperties.Intlen, 10);
			oFixedValueProperties.Extlen = parseInt(oFixedValueProperties.Extlen, 10);
			oFixedValueProperties.Offset = parseInt(oFixedValueProperties.Offset, 10);
			oFixedValueProperties.Position = parseInt(oFixedValueProperties.Position, 10);
			oFixedValueProperties.Intlen = parseInt(oFixedValueProperties.Intlen, 10);

			/**
			 * Feature 11655 - Set Mapping Key from Mapping Table
			 * On read mapping details, check whether the navigation SEGSTRUCT2SOAKEYMAPNAV consists of records for MMapped Value Transformation
			 * 		->	Convert the unique identifier (NodeKey) enclosed within {{ and }} to complete relevant path of the mapped row in mappinf table
			 */
			if(oIdocSegment.SEGSTRUCT2SOAKEYMAPNAV.results.length > 0 && oIdocSegment.Transformation === "M") {
				oIdocSegment.SEGSTRUCT2SOAKEYMAPNAV.results.forEach((oMappedKeyInfo) => {
					if(oIdocSegment.Keyvalue.includes("{{" + oMappedKeyInfo.Nodekey +"}}")) {
						oIdocSegment.Keyvalue = oIdocSegment.Keyvalue.replace("{{" + oMappedKeyInfo.Nodekey +"}}", "{{" + oMappedKeyInfo.Parentpath + "-" + oMappedKeyInfo.Segmenttype + "-" + oMappedKeyInfo.Fieldname + "}}");
					} 
				});
			}
			
			iSegmentIndex = arrIdocList.findIndex((oSegmentType) => {
				return oSegmentType.Fieldnodekey === oIdocSegment.Parentnodekey;
			});
			if(iSegmentIndex === -1) {
				// Add as first level node
				if(oIdocSegment.Fieldname === "" || !oIdocSegment.Fieldname) {
					arrIdocList.push({
						bIsSegment: true,
						ParentSegment: oIdocSegment.Parentseg,
						Segment: oIdocSegment.Segmenttype,
						Fieldnodekey: oIdocSegment.Fieldnodekey,
						Parentnodekey: oIdocSegment.Parentnodekey,
						Proxyenh: oIdocSegment.Proxyenh,
						children: []
					});
				} else {
					arrIdocList.push({
						bIsSegment: true,
						ParentSegment: oIdocSegment.Parentseg,
						Segment: oIdocSegment.Segmenttype,
						Fieldnodekey: oIdocSegment.Fieldnodekey,
						Parentnodekey: oIdocSegment.Parentnodekey,
						Proxyenh: oIdocSegment.Proxyenh,
						children: [oIdocSegment]
					});
				}
				
			} else {
				if(oIdocSegment.Fieldname === "" || !oIdocSegment.Fieldname) {
					arrIdocList.push({
						bIsSegment: true,
						ParentSegment: oIdocSegment.Parentseg,
						Segment: oIdocSegment.Segmenttype,
						Fieldnodekey: oIdocSegment.Fieldnodekey,
						Parentnodekey: oIdocSegment.Parentnodekey,
						Proxyenh: oIdocSegment.Proxyenh,
						children: []
					});
				} else {
					//Add as child to first level node
					arrIdocList[iSegmentIndex].children.push(oIdocSegment);
				}
			}
		});
		
		return arrIdocList;
	};
	
	InterfacesController.setChildInTreeServices = function (oController, oTree, oChild) {
		if (!(typeof oTree.children === "undefined" || oTree.children === null) && oTree.children.length > 0 && oChild.Parentnodekey !== "") { 
			for (let i = 0; i < oTree.children.length && oChild._hasBeenSetInTree !== true; i++) {
				if (oTree.children[i].bIsSegment === true && oTree.children[i].Fieldnodekey === oChild.Parentnodekey) {
					oChild._hasBeenSetInTree = true;
						oTree.children[i].children[oTree.children[i].children.length] = oChild;
					break;
				} else if (oTree.children[i].bIsSegment === true) {	
					let oMatchedChild = oTree.children[i].children.find((e1) => {
						return (e1.Fieldnodekey === oChild.Parentnodekey);
					});
					
					if(oMatchedChild) {
						oController.setChildInTreeServices(oController, oMatchedChild, oChild);
					} else {
						oTree.children[i].children.forEach((e2) => {
							if(e2.bIsSegment === true) {
								oController.setChildInTreeServices(oController, e2, oChild);
							}
						});
					}
				} else {
					continue;
				}
			}
		} else if(oChild.Parentnodekey === -1) {
			oChild._hasBeenSetInTree = true;
			if (oTree.children !== null && oTree.children) {
				oTree.children[oTree.children.length] = oChild;
			}	
		}
		
		if (!oChild._hasBeenSetInTree && oTree.Fieldnodekey === oChild.Parentnodekey) {
			oChild._hasBeenSetInTree = true;
			if (oTree.children !== null && oTree.children) {
				oTree.children[oTree.children.length] = oChild;
			}
		}
	};
	
	InterfacesController.onChangeDataModel = function() {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let sUsmdModel = oInterfaceModel.getProperty("/selectedInterface/UsmdModel");
		
		let promiseVariants = 
			GetListsData.getVariantsList(oController, sUsmdModel);
			
		promiseVariants.then((arrVariants) => {
			oInterfaceModel.setProperty("/arrVariants", arrVariants);
		});
	};
	
	InterfacesController.onChangeOutboundImpl = function() {
		let oController = this;
		let oInterfaceModel = this.getView().getModel("viewInterface");
		let sOutboundImpl = oInterfaceModel.getProperty("/selectedInterface/Implementation");
		let arrOutboundImplemenations = oInterfaceModel.getProperty("/SearchHelps/arrOutboundImplemenations");
		let sSelectedRepModel = oInterfaceModel.getProperty("/selectedInterface/UsmdInterface");
		let arrRepModels = oInterfaceModel.getProperty("/RepModels");
		let arrOutBoundImplementations;
		let promiseInterfaceParameters = 
			GetListsData.getInterfaceParameters(oController, sOutboundImpl);
		
		promiseInterfaceParameters.then((arrParameters) => {
			oInterfaceModel.setProperty("/arrParameters", arrParameters);
		});
		
		let promiseIntParamValues = 
			GetListsData.getIntParamValues(oController, sOutboundImpl);
		
		promiseIntParamValues.then((arrValues) => {
			// Bug 11149 - Outbound Parameter values are duplicated in RDG
			// Storing the arrValues in the correct place, one array for each row inside arrParametersTable
			let arrParametersTable = oInterfaceModel.getProperty("/selectedInterface/arrParametersTable");
			arrParametersTable?.forEach((oParameter, index) => {
				let arrFilteredValues = arrValues.filter((oValue) => {
					return oValue.Outbparameter === oParameter.Outbparameter;
				});
				oInterfaceModel.setProperty(`/selectedInterface/arrParametersTable/${index}/arrValues`, arrFilteredValues);
			});
		});
		
		let oMatchedImplementation = arrOutboundImplemenations.find((oOutboundImpl) => {
			return oOutboundImpl.ServImp === sOutboundImpl;
		});
		
		if(oMatchedImplementation) {
			oInterfaceModel.setProperty("/selectedInterface/Commchannel", oMatchedImplementation.CommChannel);
			/**Bug 11138 - ConfigTp flag value to determine use of Filtertp value
			 * Store the value of Filter time flag in model. If set, enable Filter Time select box
			 * If blank, Clear stale Filtertp value
			 */
			oInterfaceModel.setProperty("/selectedInterface/ConfigTp", oMatchedImplementation.ConfigTp);
			if(oMatchedImplementation.ConfigTp === "") {
				oInterfaceModel.setProperty("/selectedInterface/Filtertp", undefined);
			}
		}
		let oMatchedInterface = arrRepModels.find((oRepModel) => {
			return oRepModel.Appl === sSelectedRepModel;
		});
		if(sOutboundImpl){
			if(oMatchedInterface) {
				arrOutBoundImplementations = oMatchedInterface.REP2OUTBNAV.results;
				let oMatchedOutBoundImplementation = arrOutBoundImplementations.find((sObImplementation) => {
					return sObImplementation.Outbimpl === sOutboundImpl;
				});
				if(oMatchedOutBoundImplementation) {
					oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueState", sap.ui.core.ValueState.Error); 
					oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueStateText", "Selected replication model, outbound implementation combination is aleady existing");
				}
				else
				{
					oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueState", sap.ui.core.ValueState.None); 
					oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueStateText", "");
				}
			}
			else
			{
				oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueState", sap.ui.core.ValueState.None); 
				oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueStateText", "");
			}
		}
		else
		{
			oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueState", sap.ui.core.ValueState.Error); 
			oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueStateText", "Outbound Implementation must not be empty");
		}
	};
	
	
	InterfacesController.onChangeParameter = function(oEvent) {
		let oController = this;
		let sParameter = oEvent.getParameter("selectedItem").getKey();
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let sOutboundImpl = oInterfaceModel.getProperty("/selectedInterface/Implementation");
			
		let arrParameters = oInterfaceModel.getProperty("/arrParameters");
		let oMatchedParameter = arrParameters.find((oParameter) => {
			return oParameter.Outbparameter === sParameter;
		});
		if(oMatchedParameter) {
			let bMandatory = oMatchedParameter.Mandatory === "X" ? true : false;
			//Set the switch based on its value from service
			oEvent.getSource().getParent().getCells()[1].setState(bMandatory);
		}
		
		// Bug 11149 - Outbound Parameter values are duplicated in RDG
		// Get the complete path of the row that is being edited
		let sChangedLeadingEntityRow = oEvent.getSource().getBindingContext("viewInterface").getPath();
		oInterfaceModel.setProperty(sChangedLeadingEntityRow + "/arrValues", []);
		
		let promiseIntParamValues = GetListsData.getIntParamValues(oController, sOutboundImpl);
		promiseIntParamValues.then((arrValues) => {
			let arrFilteredValues = arrValues.filter(oValue => {
				return oValue.Outbparameter === sParameter;
			});
			
			// Storing the valid values for the given parameter as an array inside the array
			oInterfaceModel.setProperty(sChangedLeadingEntityRow + "/arrValues", arrFilteredValues);
		});
	};
	
	/*
		Add a row to Parameters table when user clicks on Add
	*/
	InterfacesController.onAddParameter = function() {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let oSelectedInterface = oInterfaceModel.getProperty("/selectedInterface");
		if(!oSelectedInterface.arrParametersTable) {
			oSelectedInterface.arrParametersTable = [];
		}
		oSelectedInterface.arrParametersTable.push({
			Appl: oSelectedInterface.UsmdInterface,
			Mandatory: false,
			Outbimpl: oSelectedInterface.Implementation,
			Outbparameter: undefined,
			Outbparametertext: undefined,
			Value: undefined,
			Valuetext: undefined
		});
		oInterfaceModel.refresh();
	};
	
	InterfacesController.onDeleteParameter = function(oEvent) {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let oTable = oEvent.getSource();
		let oClickedItem = oEvent.getParameters().listItem;
		
		let promise = new Promise(function (resolve, reject) {
			// Show a message asking the user to confirm deletion. Exit if cancel 
			let promiseShowPopupAlert = Utilities.showPopupAlert("The decision row will be deleted. Continue?", MessageBox.Icon.WARNING,
				"Delete?", [sap.m.MessageBox.Action.YES, sap.m.MessageBox.Action.NO]);
			promiseShowPopupAlert.then(function () {
				resolve();
			}, function () {
				reject();
			});
		});
		
		/**
		 * Get the index of the row being deleted and remove from the model. 
		 */
		promise.then(function () {
			// Get the index clicked
			let iClickedIndex = oTable.getItems().indexOf(oClickedItem);
			let arrParameterTable = oInterfaceModel.getProperty("/selectedInterface/arrParametersTable");
			arrParameterTable.splice(iClickedIndex, 1);
			oInterfaceModel.setProperty("/selectedInterface/arrParametersTable", arrParameterTable);
		});
	};

	InterfacesController.onCreateNewInterface = function () {
		let oInterfaceModel = this.getView().getModel("viewInterface");
		oInterfaceModel.setProperty("/selectedInterface", {
			Sequence: "00"
		});
		oInterfaceModel.setProperty("/arrParameters", []);
		oInterfaceModel.setProperty("/arrVariants", []);
		oInterfaceModel.setProperty("/interface", "new");
		oInterfaceModel.setProperty("/parameterTableMode", sap.m.ListMode.Delete);
		// Bug 11139 - Interface Configuration Create and Change Processes
		// New interfaces should be created as Active by default
		oInterfaceModel.setProperty("/selectedInterface/Active", "Active"); 
		oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueState", sap.ui.core.ValueState.Error); 
		oInterfaceModel.setProperty("/selectedInterface/interfaceOutbValueStateText", "Outbound Implementation must not be empty");
		oInterfaceModel.setProperty("/selectedInterface/interfaceBusisyValueState", sap.ui.core.ValueState.Error); 
		this.byId("idInterfacePanel").setExpanded(true);
		this.byId("idIdocPanel").setExpanded(false);
		
		this.InterfaceSearchList.removeSelectedItem();
		
		//Set no data text back to default 
		let oIdocTreeTable = this.getView().byId("IdocTree");
		oIdocTreeTable.setNoData("No Data");
		
		//Clear the mapping details tree table if already filled
		if(this.getView().getModel()) {
			//Clear Mapping Tree Table for the selected item
			this.getView().getModel().setProperty("/children", undefined);
		}
	};
	
	InterfacesController.onEditInterface = function () {
		this.getView().getModel("viewInterface").setProperty("/interface", "edit");
		this.getView().getModel("viewInterface").setProperty("/parameterTableMode", sap.m.ListMode.Delete);
	};
	
	InterfacesController.onDeleteInterface = async function() {
		let oController = this;
		let oInterfaceModel = this.getView().getModel("viewInterface");
		let oSelectedInterface = oInterfaceModel.getProperty("/selectedInterface");
		let arrRepModels = oInterfaceModel.getProperty("/RepModels");
		
		let oRepModel = arrRepModels.find(oModel => {
			return oModel.Appl === oSelectedInterface.UsmdInterface;	
		});
		
		let oOutboundImpl = oRepModel.REP2OUTBNAV.results.find(oInterface => {
			return oInterface.Outbimpl === oSelectedInterface.Implementation;
		});
		
		// return if either is invalid
		if(!oRepModel || !oOutboundImpl) {
			return;
		}

		// Prepare the oData information
		let oData = {
			Appl: oSelectedInterface.UsmdInterface,
			Appltext: oSelectedInterface.UsmdInterfaceText,
			Logdays: "00",
			Usmdmodel: oSelectedInterface.UsmdModel,
			// Bug 11139 - Interface Configuration Create and Change Processes
			Active: ( oSelectedInterface.Active === "Active" ? "X" : "" ),
			WbTransport: undefined,
			CustTransport: undefined,
			REP2OUTBNAV: [{
				Alemesstype: "",
				Usmdtrvari: "",
				Appl: oSelectedInterface.UsmdInterface,
				Outbimpl: oSelectedInterface.Implementation,
				Commchannel: oSelectedInterface.Commchannel,
				Sequence: oSelectedInterface.Sequence,
				Filtertp: oSelectedInterface.Filtertp,
				OUTB2BSYSNAV: [],
				OUTB2PARAMNAV: [],
				OUTB2VARNAV: []
			}],
			REP2MSGNAV: [],
			REP2LANG: []
		};

		oController._selectTransportPackage(true, true, false)
		.then(function(oResponseObject){
			oData.WbTransport = oResponseObject.workbenchTransport;
			oData.CustTransport = oResponseObject.customizingTransport;

			let oPromiseDelete = new Promise((resolve, reject) => {
				if(oRepModel.REP2OUTBNAV.results.length > 1) {
					oData.REP2OUTBNAV[0].Delete = "X";
					resolve(oData);
				} else {
					Utilities.showPopupAlert(
						"Replication Model " + oSelectedInterface.UsmdInterface + " will be deleted. Do you want to continue?",
						MessageBox.Icon.INFORMATION, 
						"Delete Replication Model", 
						["YES", "NO"])
					.then(() => {
							oData.Delete = "X";
							resolve(oData);
						})
					.catch(() => reject());
					return;
				}
			});

			return oPromiseDelete;
		})
		.then(function(oDataToSend){
			(new DMRDataService(
				oController,
				"DRF_MODEL_LIST",
				"/REPMODELSet",
				"saveRepModel",
				"/", // Root of the received data
				"Save Rep Model"
			))
			.showBusyIndicator(true)
			.saveData(
				false,
				oDataToSend,
				null, {
					success: {
						fCallback: function () {
							Utilities.showPopupAlert(
								"Interface "+ oSelectedInterface.Implementation +" of Replication Model " +oSelectedInterface.UsmdInterface+ " has been deleted",
								MessageBox.Icon.INFORMATION,
								"Interface Deleted")
							.then(function () {
								oController.getInterfaceList(oController, undefined, undefined);
								oController.InterfaceSearchList.removeSelectedItem();
							});
						},
						oParam: this
					},
					error: function () {
						Utilities.showPopupAlert("Interface could not be deleted.", MessageBox.Icon.ERROR, "INTERFACE DELETE ERROR");
					}
				}
			);
		});
	};
	
	InterfacesController.onManageBussinessSystem = function() {
		let oController = this;
		let oInterfaceModel = this.getView().getModel("viewInterface");
		if (!oController.BusSystemsDialog) {
			oController.BusSystemsDialog =
				sap.ui.xmlfragment(
					"BusinessSystems",
					"dmr.mdg.supernova.SupernovaFJ.view.Interfaces.BusinessSystems",
					oController);
			oController.getView().addDependent(oController.BusSystemsDialog);
		}
		
		oInterfaceModel.setProperty("/editBusSystems", {
			Bussystem: ""
		});
		
		let promiseBusObjectList = 
			GetListsData.getBusinessObjects(oController);
		
		promiseBusObjectList.then((arrBusinessObjects) => {
			arrBusinessObjects.sort(function(a, b) {
				return a.Busobject - b.Busobject;
			});
			oInterfaceModel.setProperty("/arrBusinessObjects", arrBusinessObjects);
		});
		
		// Open the dialog
		oController.BusSystemsDialog.open();
	};
	
	InterfacesController.onChangeMappingBusinessSystem = function(oEvent) {
		let sSelectedBusSystemForMapping=oEvent.getParameters().selectedItem.getKey();
		let oController = this;
		let promiseJobStatus = GetListsData.getBackgroundJobStatus(oController, "**");
		promiseJobStatus.then((oResponse) => {
			let arrMessages = [];
			jQuery.extend(true, arrMessages, oResponse.JOB2MESSAGENAV.results);
			if (oResponse.JOB2MESSAGENAV.results[0].MessageType === "W") {
				// Bug 12971 - Promise was added to remove selection in the list when proxy structure is being processed
				let promiseShowPopupAlert = Utilities.showPopupAlert("Proxy structure is still being processed. Please try again in a moment.", MessageBox.Icon.WARNING, "Mapping details loading");
				promiseShowPopupAlert.then(() => {
					oController.InterfaceSearchList.removeSelectedItem();
					oController.InterfaceSearchList.fireSelectionChange();
				});
			}
			else {
				let oInterfaceModel = oController.getView().getModel("viewInterface");
				let oIdocTreeTable = oController.getView().byId("IdocTree");
				let oSelectedObject = oInterfaceModel.getProperty("/selectedInterface");
				oInterfaceModel.setProperty("/sSaveValidationError", false);
				oInterfaceModel.setProperty("/selectedInterface/selectedBusinessSystemForMapping", sSelectedBusSystemForMapping);
				if(sSelectedBusSystemForMapping) {
					oController.byId("idInterfacePanel").setExpanded(false);
					oController.byId("idIdocPanel").setExpanded(true);
			
					//Set default value of ComboBox to be treated as placeholder value
					//Disale Input. User has to select from dropdown
					let oComboBox = oController.byId("idTransformationFilterSelect");
					oComboBox.setValue("Select Transformation");
					oComboBox.addEventDelegate({
						onAfterRendering: function() {
							oComboBox.$().find("input").attr("readonly", true);
						}
					});

			
		
					if(oSelectedObject.Commchannel === "1" || oSelectedObject.Commchannel === "2") {
						sap.ui.core.BusyIndicator.show();
						let promiseIdocStructure = 
						GetListsData.getIdocStructureList(oController, 
													oSelectedObject.UsmdInterface, 
													oSelectedObject.Implementation,
													oSelectedObject.Commchannel,
													sSelectedBusSystemForMapping);
				
						promiseIdocStructure.then((arrIdocStructureList) => {
							// Bug 12523 - Use transport sent in read response for mappings save of soa replications
							oSelectedObject.WbTransport = "";
							oSelectedObject.CustTransport = "";
							if (arrIdocStructureList[0].WbTransport) {
								oSelectedObject.WbTransport = arrIdocStructureList[0].WbTransport;
							}
							if (arrIdocStructureList[0].CustTransport) {
								oSelectedObject.CustTransport = arrIdocStructureList[0].CustTransport;
							}
							// End Bug 12523 - Use transport sent in read response for mappings save of soa replications
							oSelectedObject.WbtrRequired = arrIdocStructureList[0].WbtrRequired;
							oSelectedObject.Brfapplname = arrIdocStructureList[0].Brfapplname;
							oSelectedObject.Idoctyp = arrIdocStructureList[0].Idoctyp;
							oSelectedObject.Idocextension = arrIdocStructureList[0].Idocextension;
							oSelectedObject.Outbimpl = arrIdocStructureList[0].Outbimpl;
							oSelectedObject.arrIdocStructure = arrIdocStructureList[0];
							oSelectedObject.Msgtyp = arrIdocStructureList[0].Msgtyp;
							oSelectedObject.Successorflag = arrIdocStructureList[0].Successorflag;
							oSelectedObject.ExtIndex = Number(arrIdocStructureList[0].ExtIndex);
							let arrIdocList = [];
							let oIdocTree = {};
						
							if(oSelectedObject.Commchannel === "1") {
								arrIdocList = oController.makeListForServicesStructure(oController, arrIdocStructureList);
							} else {
								arrIdocList = oController.makeListFromResponse(oController, arrIdocStructureList);
							}
						
							if(arrIdocList.length === 0) {
								if(oSelectedObject.Commchannel === "1"){
									oIdocTreeTable.setNoData("SOA message type has not been configured in MDG system");
								} else if (oSelectedObject.Commchannel === "2"){
									oIdocTreeTable.setNoData("No partner profiles have been configured in MDG system ");
								}
							} else {
								oIdocTreeTable.setNoData("No Data for the selected filter"); //To be displayed for filter which do not yield records
								oIdocTree.children = [];
								if(oSelectedObject.Commchannel === "1") {
									arrIdocList.forEach((oSegment) => {
										oController.setChildInTreeServices(oController, oIdocTree, oSegment);
									});
								} else {
									arrIdocList.forEach((oSegment) => {
										oController.setChildInTree(oController, oIdocTree, oSegment);
									});
								}
							}
		
							let oJsonModel = new JSONModel();
							// Set the data to the json model
							oIdocTreeTable.setVisibleRowCount((screen.height > 900) ? 14 : 9);
							oJsonModel.setData(oIdocTree);
							oController.getView().setModel(oJsonModel);
							// Get the global model
							let rdgModel = RDGModels.getRDGGlobalModel();			
							// Set properties on the model
							rdgModel.setProperty("/InterfacesModelCheck", oJsonModel );
							oInterfaceModel.refresh();
							sap.ui.core.BusyIndicator.hide();
						});
					} else {
						oIdocTreeTable.setNoData("Data replication for selected Interface not yet supported on RDG");
					}
				}
			}							
		});		
	};
	
	/*
	*	Formatter function to set the type of the Input Control
	*	Text - Characters, Numbers, Special Characters
	*	Tel - Only Numbers and single period (.) - Indicator to determine only Numbers can be typed on LiveChange event
	*/
	InterfacesController.FixedValueType = function(sDataType) {
		if(sDataType === "CHAR" || sDataType === "CUKY" || sDataType === "LANG" || sDataType === "LCHR" || sDataType === "LRAW" || sDataType === "NUMC" ||
		   sDataType === "PREC" || sDataType === "RAW" || sDataType === "RSTR" || sDataType === "SSTR" || sDataType === "STRG" || sDataType === "UNIT") {
			return sap.m.InputType.Text;
		} else {
			//Cant use Type Number, because maxLength is ignored
			return sap.m.InputType.Tel;
		}
	};
	
	/*
	* Live Change Event on Fixed Value Input Control
	* Remove letter typed if Type = Tel
	*/
	InterfacesController.onFixedValueLiveChange = function (oEvent) {
		if (oEvent.getSource().getType() === "Tel" || oEvent.getSource().getType() === "Text") {

			//Handling maxLength for Fixed Value
			let oController = this;
			let oObject = oEvent.getSource().getBindingContext();
			let oIdocTable = oController.byId("IdocTree");
			let oCurrentRow = oIdocTable.getModel().getProperty(oObject.sPath);

			if (oEvent.getSource().getValue().replace(/\s/g, "").split("-")[0].length > oCurrentRow.SEGSTRUCT2ATTRIBNAV.Intlen && oEvent.getSource().getValue().length > 0) {
				/**
				 * Bug 11884 - No fixed value can be added for Data type String and length 0
				 * Data type String with length 0 indicates that there is no length restriction
				 */
				if(oCurrentRow.SEGSTRUCT2ATTRIBNAV.Datatype === "STRG" && oCurrentRow.SEGSTRUCT2ATTRIBNAV.Intlen === 0) {
					oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueState = "";
					oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueStateText = "";
				} else {
					oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueState = "X";
					oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueStateText = "Max Length Allowed - " + oCurrentRow.SEGSTRUCT2ATTRIBNAV.Intlen;
				}
				
			} else if (oEvent.getSource().getValue().length === 0) {
				// Bug 11175 - Able to save mapping without table field for mapped transformation, which results in replication error
				// If Transformation is equals to Fixed Value, then Fixed Value field becomes mandatory
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueState = "X";
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueStateText = "Fixed value is mandatory";
			} else {
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueState = "";
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueStateText = "";
			}

			if (oEvent.getSource().getType() === "Tel") {
				let sValue = oEvent.getSource().getValue();
				let bNan = isNaN(sValue);
				//If true, then current Live Value is a letter.
				if (bNan === true) {
					oEvent.getSource().setValue(sValue.substring(0, sValue.length - 1));
				}
			}

			oIdocTable.getModel().refresh();
		}
	};

	// Bug 12159 - Unable to save "Fixed Value" Transformation for 'Date' Fields - SOA Mapping
	InterfacesController.onFixedValueDateTimeChange = function(oEvent){
		let oController = this;
		let oObject = oEvent.getSource().getBindingContext();
		let oIdocTable = oController.byId("IdocTree");
		let oCurrentRow = oIdocTable.getModel().getProperty(oObject.sPath);
		let sValue = oEvent.getSource().getValue();
		let sId = oEvent.getSource().getId();

		let regexNum = "^[0-9]+$";

		if(sId.includes("idFixedValueDate")){
			if (sValue.match(regexNum) && sValue.length ===8) {
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueState = "";
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueStateText = "";
			} else {
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueState = "X";
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueStateText = "Fixed value is mandatory";
			}
		} else{
			if (sValue.length ===6) {
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueState = "";
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueStateText = "";
			} else {
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueState = "X";
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueStateText = "Fixed value is mandatory";
			}
		}
		
	};
	// End of bug 12159
	
	InterfacesController.FixedValueItemSelected = function(oEvent) {
		if(oEvent.getParameters().selectedItem) {
			let sKeyToValue = oEvent.getParameters().selectedItem.getKey();
			oEvent.getSource().setValue(sKeyToValue);
		}
	};
	
	InterfacesController.onSaveInterface = async function() {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let oSelectedInterface = oInterfaceModel.getProperty("/selectedInterface");
		let arrInterfaces = oController.getModel("interfaces").getData();
		let arrBusinessSystems = oSelectedInterface.selectedBusinessSystems;
		let arrRepModels = oInterfaceModel.getProperty("/RepModels");

		let arrMatchedSystems;
		let oMatchedSystem;
		let oMatchedBusinesSystem;
		let sBusinessSystem ="";
		// 8944 - More details on error page to tell user how to resolve them.
		// More details on the error message, and validation of Business system and outbound implemantatio if active.
		if(oSelectedInterface.Active === "Active"){
			arrMatchedSystems = arrInterfaces.filter((oInterface) => {
				return oInterface.Implementation === oSelectedInterface.Implementation && oInterface.Active === "Active"
				&& oInterface.UsmdInterface !== oSelectedInterface.UsmdInterface;
			});

			if(arrMatchedSystems){
				for(let j = 0; j < arrMatchedSystems.length; j++){
					for(let i = 0; i < arrBusinessSystems.length; i++){
						oMatchedBusinesSystem = arrMatchedSystems[j].arrBusinessSystem.find((arrBusSystem) => {
							return arrBusSystem.Businesssystem === arrBusinessSystems[i];
						});
						if(oMatchedBusinesSystem){
							sBusinessSystem = sBusinessSystem + oMatchedBusinesSystem.Businesssystem + ", ";
						}
						if(sBusinessSystem && arrBusinessSystems.length-1===i){
							oMatchedSystem = arrMatchedSystems[j];
							j=arrBusinessSystems.length;
						}
					}
				}
			}
			
			if (sBusinessSystem) {
				sBusinessSystem = sBusinessSystem.substring(0, sBusinessSystem.length-2);
				return Utilities.showPopupAlert("Interface " + oSelectedInterface.Implementation + " of replication model " 
				+ oSelectedInterface.UsmdInterface + " cannot be activated since an active interface " 
				+ oMatchedSystem.Implementation + "\n of replication model " 
				+ oMatchedSystem.UsmdInterface + " already exists for business system " 
				+ sBusinessSystem +"." + "\n Kindly inactivate " 
				+ oMatchedSystem.UsmdInterface + " -> " 
				+ oMatchedSystem.Implementation + " or choose a different business system and try again.", MessageBox.Icon.ERROR, "Validation Error");
			}
		}



		// Task 13334 - if we are saving a new interface set the variable "bIsNew" to true
		let bIsNew = (oInterfaceModel.getProperty("/interface") === "new");

		// Default to select both customzing and workbench
		let bSelectCustomzingTransport = true;
		let bSelectWorkbenchTransport = true;

		// If this not a new interface check to see if workbench is required for saved interfaces
		if(!bIsNew){
			// match the replication model
			let oRepModel = arrRepModels.find(oModel => {
				return oModel.Appl === oSelectedInterface.UsmdInterface;	
			});

			// match the outbound implementation
			let oOutboundImpl = oRepModel.REP2OUTBNAV.results.find(oInterface => {
				return oInterface.Outbimpl === oSelectedInterface.Implementation;
			});

			// if the outbound implementation requires the Workbench, set the variable "bWbtrRequired" as true
			let bWbtrRequired = (oOutboundImpl.WbtrRequired === "X");

			// if a busines system has been change, set the variable "bBusinessSystemChange" as true
			let bBusinessSystemChange = oSelectedInterface.businessSystemChange;

			// If either condition does not match, there is no need to select the workbench transport
			if(bWbtrRequired === false && bBusinessSystemChange === false) {
				bSelectWorkbenchTransport = false;
			}
		}

		let oData = {
			Appl: oSelectedInterface.UsmdInterface,
			Appltext: oSelectedInterface.UsmdInterfaceText,
			Logdays: "00",
			Usmdmodel: oSelectedInterface.UsmdModel,
			// Bug 11139 - Interface Configuration Create and Change Processes
			Active: ( oSelectedInterface.Active === "Active" ? "X" : "" ),
			CustTransport: undefined,
			WbTransport: undefined,
			REP2OUTBNAV: [{
				Alemesstype: "",
				Usmdtrvari: "",
				Appl: oSelectedInterface.UsmdInterface,
				Outbimpl: oSelectedInterface.Implementation,
				Commchannel: oSelectedInterface.Commchannel,
				Sequence: oSelectedInterface.Sequence,
				Filtertp: oSelectedInterface.Filtertp,
				OUTB2BSYSNAV: [],
				OUTB2PARAMNAV: [],
				OUTB2VARNAV: []
			}],
			REP2MSGNAV: [],
			REP2LANG: [],
		};


		oController._selectTransportPackage(bSelectCustomzingTransport, bSelectWorkbenchTransport, false)
		.then(function(oTransportPackageResponse){
			oData.CustTransport = oTransportPackageResponse.customizingTransport;
			oData.WbTransport = oTransportPackageResponse.workbenchTransport;

			if(oSelectedInterface.selectedBusinessSystems) {
				oSelectedInterface.selectedBusinessSystems.forEach((oBussSystem) => {
					oData.REP2OUTBNAV[0].OUTB2BSYSNAV.push({
						Appl: oSelectedInterface.UsmdInterface,
						Outbimpl: oSelectedInterface.Implementation,
						Businesssystem: oBussSystem
					});
				});
			}
	
			if(oSelectedInterface.arrParametersTable) {
				oSelectedInterface.arrParametersTable.forEach((oParameterRow) => {
					if(oParameterRow.Outbparameter) {
						oData.REP2OUTBNAV[0].OUTB2PARAMNAV.push({
							Appl: oSelectedInterface.UsmdInterface,
							Outbimpl: oSelectedInterface.Implementation,
							Mandatory: ( oParameterRow.Mandatory === true ) ? "X" : "",
							Outbparameter: oParameterRow.Outbparameter,
							Value: ( oParameterRow.Value === undefined ) ? "" : oParameterRow.Value
						});
					}
				});
			}
	
			if(oSelectedInterface.selectedVariants) {
				oSelectedInterface.selectedVariants.forEach((oVariant) => {
					oData.REP2OUTBNAV[0].OUTB2VARNAV.push({
						Appl: oSelectedInterface.UsmdInterface,
						Outbimpl: oSelectedInterface.Implementation,
						Usmdtrvari: oVariant
					});
				});
			}

			(new DMRDataService(
				oController,
				"DRF_MODEL_LIST",
				"/REPMODELSet",
				"saveRepModel",
				"/", // Root of the received data
				"Save Rep Model"
			))
			.saveData(
				false,
				oData,
				null, {
					success: {
						fCallback: function (oParams, oResponseData) {
							let arrMessages = [];
							jQuery.extend(true, arrMessages, oResponseData.REP2MSGNAV.results);
							oController.getInterfaceList(oController, oResponseData.Appl, oSelectedInterface.Txtmi);
							ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
						},
						oParam: this
					},
					error: function () {
						let promiseShowPopupAlert =
						Utilities.showPopupAlert("Interface could not be saved/updated.", MessageBox.Icon.ERROR, "INTERFACE SAVE ERROR");
						promiseShowPopupAlert.then(function () {
						});
					}
				}
			);
		});
	};
	
	// Task 12294 - Add all options for enhancement and open new Proxy Extension screen
	/**
	 * On click of Add Proxy button
	 * Get the current row details and validate whether enhancement can be added to the element
	 * Navigate to the Proxy Enhancement Wizard Screen with the routing properties
	*/
	InterfacesController.onAddProxyPress = function () {
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		let oTreeTable = this.byId("IdocTree");
		let aSelectedIndices = oTreeTable.getSelectedIndices();
		let oInterfaceModel = this.getView().getModel("viewInterface");
		let oSelectedObject = oInterfaceModel.getProperty("/selectedInterface");
		let promiseShowPopupAlert;
		let sPath;
		try{
			sPath = oTreeTable.getContextByIndex(aSelectedIndices[0]).sPath;
		} catch(e){
			promiseShowPopupAlert =
			Utilities.showPopupAlert("Please select the correct node to add enhancements", MessageBox.Icon.ERROR, "Unable to add Enhancement");
			promiseShowPopupAlert.then(function () {
			});
			return;
		}

		let oWizardData = {
				WizardType: "Add",
				Outbimpl: oInterfaceModel.getProperty("/selectedInterface/Implementation"),
				DataModel: oInterfaceModel.getProperty("/selectedInterface/UsmdModel"),
				Enhancement: "",
				Element: ""
			};

		let oCurrentRow = oTreeTable.getModel().getProperty(sPath);
		// if the selected row is at the root 
		if(oCurrentRow.Fieldnodekey === oSelectedObject.ExtIndex) {
			oRouter.navTo("ProxyEnhancementWizard", oWizardData);
		// if the selected row is an ENHANCEMENT
		} else if(oCurrentRow.bIsSegment && oCurrentRow.Proxyenh === "X"){
			oWizardData.Enhancement = oCurrentRow.Segment;
			oWizardData.WizardType = "AddData";

			oRouter.navTo("ProxyEnhancementWizard", oWizardData);
		// if the selected row is an ELEMENT
		} else if(oCurrentRow.bIsSegment && oCurrentRow.Proxyenh === "E"){
			let oParentRow = oTreeTable.getModel().getProperty(sPath.substring(0, sPath.lastIndexOf("/children")));
			oWizardData.Enhancement = oParentRow.Segment;
			oWizardData.WizardType = "AddData";
			oWizardData.Element = oCurrentRow.Segment;

			oRouter.navTo("ProxyEnhancementWizard", oWizardData);

		} else {
			promiseShowPopupAlert =
				Utilities.showPopupAlert("Please select a custom enhancement or element to add.", MessageBox.Icon.ERROR, "Unable to add here.");
			// promiseShowPopupAlert.then(function () {
			// 	resolve();
			// });
			return;
		}
	};

	/**
	 * On click of Edit Proxy button
	 * Get the current row details and validate whether enhancement can be edited
	 * Navigate to the Proxy Enhancement Wizard Screen with the routing properties
	 */
	InterfacesController.onEditProxyPress = function() {
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		let oTreeTable = this.byId("IdocTree");
		let aSelectedIndices = oTreeTable.getSelectedIndices();
		let promiseShowPopupAlert;
		let sPath;
		let oParentRow;
		try{
			sPath = oTreeTable.getContextByIndex(aSelectedIndices[0]).sPath;
		} catch(e){
			promiseShowPopupAlert =
			Utilities.showPopupAlert("Please select a custom enhancement to edit", MessageBox.Icon.ERROR, "Unable to edit Enhancement");
			promiseShowPopupAlert.then(function () {
			});
			return;
		}
		let sDataModel = this.getView().getModel("viewInterface").getProperty("/selectedInterface/UsmdModel") || "";
		let oCurrentRow = oTreeTable.getModel().getProperty(sPath);
		let oWizardData = {
			WizardType: "Edit",
			Outbimpl: this.getView().getModel("viewInterface").getProperty("/selectedInterface/Implementation"),
			DataModel: sDataModel,
			Enhancement: "",
			Element: ""
		};
		// if the selected row is an ENHANCEMENT
		if(oCurrentRow.Proxyenh === "X") {
			oWizardData.Enhancement = oCurrentRow.Segment;
			
			oRouter.navTo("ProxyEnhancementWizard", oWizardData);
		// if the selected row is an ELEMENT
		 } else if(oCurrentRow.bIsSegment && oCurrentRow.Proxyenh === "E"){
			oParentRow = oTreeTable.getModel().getProperty(sPath.substring(0, sPath.lastIndexOf("/children")));

			oWizardData.Enhancement = oParentRow.Segment;
			oWizardData.Element = oCurrentRow.Segment;

			oRouter.navTo("ProxyEnhancementWizard", oWizardData);
		// if the selected row is an ATTRIBUTE
		} else if(oCurrentRow.Proxyenh === "A"){
			oParentRow = oTreeTable.getModel().getProperty(sPath.substring(0, sPath.lastIndexOf("/children")));
			// if the selected row parent is an ENHANCEMENT
			if(oParentRow.Proxyenh ==="X"){
				oWizardData.Enhancement = oParentRow.Segment;

				oRouter.navTo("ProxyEnhancementWizard", oWizardData);
			}
			// if the selected row parent is an ELEMENT
			if(oParentRow.Proxyenh ==="E"){
				oWizardData.Enhancement = oParentRow.ParentSegment;
				oWizardData.Element = oParentRow.Segment;
				oRouter.navTo("ProxyEnhancementWizard", oWizardData);
			}
		} else {
			promiseShowPopupAlert =
				Utilities.showPopupAlert("Please select a custom enhancement, element or attribute to edit", MessageBox.Icon.ERROR, "Unable to edit here.");
			promiseShowPopupAlert.then(function () {
			});
			return;
		}
	};


	/** 
	 * On click of Edit Proxy button
	 * User to click on Row for Enhancement/Attribute that can be deleted
	 * Fetch the Enhancement details to get the workbench TR
	 * SHow the Model Messages screen
	 * If no error message, then refresh the mapping table on close of model messages screen
	*/
	InterfacesController.onDeleteProxyPress = async function() {
		let oController = this;
		let oInterfaceModel = this.getView().getModel("viewInterface");
		let oSelectedInterface = oInterfaceModel.getProperty("/selectedInterface");
		let oTreeTable = this.byId("IdocTree");
		let aSelectedIndices = oTreeTable.getSelectedIndices();
		let promiseShowPopupAlert;
		let sPath;
		try{
			sPath = oTreeTable.getContextByIndex(aSelectedIndices[0]).sPath;
		} catch(e){
			promiseShowPopupAlert =
			Utilities.showPopupAlert("Please select a custom enhancement to delete", MessageBox.Icon.ERROR, "Unable to edit Enhancement");
			promiseShowPopupAlert.then(function () {
			});
			return;
		}
		let sDataModel = oSelectedInterface.UsmdModel || "";
		let oCurrentRow = oTreeTable.getModel().getProperty(sPath);
		let oParentRow = oTreeTable.getModel().getProperty(sPath.substring(0, sPath.lastIndexOf("/children")));
		let oData = {};
		// Bug 12527 - Deleting not working for 986_3
		let sPrefix;
		if(oSelectedInterface.Implementation==="986_3"){
			sPrefix = "ZRDG_MDG_" + sDataModel + "_REL_";
		} else{
			sPrefix = "ZRDG_MDG_" + sDataModel + "_";
		}
		// End Bug 12527 - Deleting not working for 986_3
		//Delete Enhancement
		if(oCurrentRow.bIsSegment && oCurrentRow.Proxyenh === "X") {
			//if enhancement has an attribute as a child, then the enhancement type is attribute
			if(oCurrentRow.children[0].Proxyenh==="A"){
				oData = {
					Appl: oSelectedInterface.UsmdInterface,
					BusSys: oSelectedInterface.selectedBusinessSystemForMapping,
					Outbimpl: oSelectedInterface.Implementation,
					ExtensionName: oCurrentRow.Segment.replace(sPrefix, ""),
					ExtObjName: oCurrentRow.Segment,
					Prefix: sPrefix,
					ExtDelete: "X",
					SRVDETAILSTOATTRIBUTESNAV: [],
					SRVDETAILSTOMESSAGESNAV: []
				};
			//if enhancement has an element as a child, then the enhancement type is element
			} else if(oCurrentRow.children[0].Proxyenh==="E"){
				oData = {
					Appl: oSelectedInterface.UsmdInterface,
					BusSys: oSelectedInterface.selectedBusinessSystemForMapping,
					Outbimpl: oSelectedInterface.Implementation,
					ExtensionName: oCurrentRow.Segment.replace(sPrefix, ""),
					ExtObjName: oCurrentRow.Segment,
					Prefix: sPrefix,
					ExtDelete: "D",
					SRVDETAILSTOELEMENTSNAV: [{
						ELEMENTSTOATTRIBUTESNAV: []
					}],
					SRVDETAILSTOMESSAGESNAV: []
				};
			}
		//Delete ELEMENT
		// if the selected row is an ELEMENT, then we should delete the enhancement
		}else if(oCurrentRow.bIsSegment && oCurrentRow.Proxyenh === "E"){
			oData = {
				Appl: oSelectedInterface.UsmdInterface,
				BusSys: oSelectedInterface.selectedBusinessSystemForMapping,
				Outbimpl: oSelectedInterface.Implementation,
				ExtensionName: oCurrentRow.ParentSegment.replace(sPrefix, ""),
				ExtObjName: oCurrentRow.ParentSegment,
				Prefix: sPrefix,
				ExtDelete: "M",
				SRVDETAILSTOELEMENTSNAV: [{
					// Bug 12527 - Deleting not working for 986_3
					ElementName: oCurrentRow.Segment.replace(sPrefix, ""),
					// End Bug 12527 - Deleting not working for 986_3
					Action: "D",
					ELEMENTSTOATTRIBUTESNAV: []
				}],
				SRVDETAILSTOMESSAGESNAV: []
			};
		//Delete ATTRIBUTE
		} else if(oCurrentRow.Fieldname && oParentRow.bIsSegment && oCurrentRow.Proxyenh ==="A") {
			// if the direct parent of the attribute has an "X" as proxyenh then this enhancement type is ATTRIBUTE
			if(oParentRow.Proxyenh === "X"){
				oData = {
					Appl: oSelectedInterface.UsmdInterface,
					BusSys: oSelectedInterface.selectedBusinessSystemForMapping,
					Outbimpl: oSelectedInterface.Implementation,
					ExtensionName: oCurrentRow.Segmenttype.replace(sPrefix, ""),
					ExtObjName: oCurrentRow.Segmenttype,
					Prefix: sPrefix,
					ExtDelete: "",
					SRVDETAILSTOATTRIBUTESNAV: [
						{
							AttrName: oCurrentRow.Fieldname.replace(sPrefix, ""),
							AttrDelete: "X"
						}
					],
					SRVDETAILSTOMESSAGESNAV: []
				};
			// if the direct parent of the attribute has an "E" as proxyenh then this enhancement type is ELEMENT
			} else if(oParentRow.Proxyenh === "E"){
				// Bug 12527 - Deleting not working for 986_3
				let promiseProxy = GetListsData.getProxyDetails(this, oSelectedInterface.Implementation, oCurrentRow.Parentseg.replace(sPrefix, ""), oCurrentRow.Parentseg);
				await promiseProxy.then(function(proxyDetails){
					let oElementData = proxyDetails[0].SRVDETAILSTOELEMENTSNAV.results[0];
					//End  Bug 12527 - Deleting not working for 986_3
					oData = {
						Appl: oSelectedInterface.UsmdInterface,
						BusSys: oSelectedInterface.selectedBusinessSystemForMapping,
						Outbimpl: oSelectedInterface.Implementation,
						ExtensionName: oCurrentRow.Parentseg.replace(sPrefix, ""),
						ExtObjName: oCurrentRow.Parentseg,
						Prefix: sPrefix,
						ExtDelete: "M",
						SRVDETAILSTOELEMENTSNAV: [
							{
								// Bug 12527 - Deleting not working for 986_3
								ElementName: oParentRow.Segment.replace(sPrefix, ""),
								Action: "M",
								Outbimpl: "",
								ElementPrefix: oElementData.ElementPrefix,
								DefaultValue: oElementData.DefaultValue.trim(),
								MinOccurs: oElementData.MinOccurs.trim(),
								MaxOccurs: oElementData.MaxOccurs.trim(),
								Deletable: oElementData.Deletable,
								Nillable: oElementData.Nillable,
								GlobalRefType: "",
								GlobalRefName: "",
								GlobalRefNamespace: "",
								XSDType: oElementData.XSDType,
								ABAPType: oElementData.ABAPType,
								Pattern: oElementData.Pattern.trim(),
								Length: oElementData.Length.trim(),
								MinLength: oElementData.MinLength.trim(),
								MaxLength: oElementData.MaxLength.trim(),
								MinIncl: oElementData.MinIncl.trim(),
								MaxIncl: oElementData.MaxIncl.trim(),
								MinExcl: oElementData.MinExcl.trim(),
								MaxExcl: oElementData.MaxExcl.trim(),
								TotalDigits: oElementData.TotalDigits.trim(),
								FractionDigits: oElementData.FractionDigits.trim(),
								//End  Bug 12527 - Deleting not working for 986_3
								ELEMENTSTOATTRIBUTESNAV: [
									{
										AttrName: oCurrentRow.Fieldname.replace(sPrefix, ""),
										AttrDelete: "D"
									}
								]
							}
						],
						SRVDETAILSTOMESSAGESNAV: []
					};
				});
			}
		} else {
			promiseShowPopupAlert =
				Utilities.showPopupAlert("Please select a Custom Enhancement, Element or Attribute to delete", MessageBox.Icon.ERROR, "Unable to delete selected item");
			promiseShowPopupAlert.then(function () {
			});
			return;
		}
		if(oData) {
			let promiseProxy = GetListsData.getProxyDetails(oController, oData.Outbimpl, oData.ExtensionName, oData.ExtObjName);
			promiseProxy.then(oEnhDetails => {
				// Store the workbench transport to the model before launching the selection popup
				oInterfaceModel.setProperty("/workbenchTransport", oEnhDetails[0].Transport);
				oController._selectTransportPackage(true, true, true)
				.then(function(oTransportPackageResponse){
					oData.WbTransport = oTransportPackageResponse.workbenchTransport;
					oData.CustTransport = oTransportPackageResponse.customizingTransport;
					oData.Package = oTransportPackageResponse.package;

					(new DMRDataService(
						oController,
						"GET_PROXY_DETAILS",
						"/SRVDETAILSSet",
						"deleteEnhancement",
						"/", // Root of the received data
						"Delete Enhancement"
					)).saveData(
						false,
						oData,
						null, {
							success: {
								fCallback: function (oParams, oResponseData) {
									let sError = "";
									let arrMessages = [];
									jQuery.extend(true, arrMessages, oResponseData.SRVDETAILSTOMESSAGESNAV.results);
									arrMessages.every((oMessage) => {
										if(oMessage.MessageType === "E") {
											//If Error exists in list, set value and break
											sError = "X";
											return false;
										} else {
											return true;
										}
									});
									ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView())
									.then(function(){
										if(sError !== "X"){
											oController.fetchInterfaceMappingDetails();
										}
									});
								},
								oParam: this
							},
							error: function () {
								Utilities.showPopupAlert("Error when deleting Enhancement/Attribute ", MessageBox.Icon.ERROR, "ENHANCEMENT DELETE ERROR");
							}
						}
					);
				});
			});
		}
	};

	InterfacesController.fetchInterfaceMappingDetails = function() {
		let oBusinessSystemComboBox = sap.ui.getCore().byId("container-SupernovaFJ---Interfaces--idIdocBusSystem");
		// trigger the selection change event if an item was selected
		let oSelectedBusSys = oBusinessSystemComboBox.getSelectedItem();
		if (oSelectedBusSys) {
				oBusinessSystemComboBox.fireChange({
				selectedItem: oSelectedBusSys
			});
		}
	};

	// //**********************TEST END */
	InterfacesController.onEditProxy = function(){
		let oInterfaceModel = this.getView().getModel("viewInterface");
		oInterfaceModel.setProperty("/editMode", true);
		oInterfaceModel.setProperty("/enableProxyDetails", true);
	};
	// // end Task 12294
	
	InterfacesController.onEditMappingDetails = function() {
		let oInterfaceModel = this.getView().getModel("viewInterface");
		oInterfaceModel.setProperty("/enableMappingDetailsTable", true);
		oInterfaceModel.setProperty("/isBusSystemForMappingDisabled", true);
		oInterfaceModel.setProperty("/editIdoc", false);
		oInterfaceModel.setProperty("/editMode", true);
	};
	
	InterfacesController.FieldInverted = function(sTableValueState, sTableFieldValueState, sfixedValueState) {
		 if(sTableValueState === "X" || sTableFieldValueState === "X" || sfixedValueState === "X") {
		 	return true;
		 } else {
		 	return false;
		 }
	};
	
	InterfacesController.FieldState = function(sTableValueState, sTableFieldValueState, sfixedValueState) {
		 if(sTableValueState === "X" || sTableFieldValueState === "X" || sfixedValueState === "X") {
		 	return "Error";
		 } else {
		 	return "Information";
		 }
	};
	
	InterfacesController.onSaveMappingDetails = function() {
		let oController = this;
		let oIdocTable = oController.byId("IdocTree");
		let oIdocTree = oController.getView().getModel().getProperty("/");
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let oSelectedInterface = oInterfaceModel.getProperty("/selectedInterface");
		let promiseShowPopupAlert;
		let oData = {};
		oData.MAIN2SEGSTRUCTNAV = [];
		
		oInterfaceModel.setProperty("/sSaveValidationError", false);
		oController.makeRequestFromTree(oController, oIdocTree, oData.MAIN2SEGSTRUCTNAV);
		if(oInterfaceModel.getProperty("/sSaveValidationError")) {
			oInterfaceModel.setProperty("/sValidationText", "Show All");
			let arrFilters = [];
			
			arrFilters.push(					
				new sap.ui.model.Filter({
					path: "SEGSTRUCT2ATTRIBNAV/tableValueState", 
					operator: sap.ui.model.FilterOperator.Contains,
					value1: "X"})
				);
				
			arrFilters.push(					
				new sap.ui.model.Filter({
					path: "SEGSTRUCT2ATTRIBNAV/tableFieldValueState", 
					operator: sap.ui.model.FilterOperator.Contains,
					value1: "X"})
				);
				
			arrFilters.push(
				new sap.ui.model.Filter({
					path: "SEGSTRUCT2ATTRIBNAV/fixedValueState",
					operator: sap.ui.model.FilterOperator.Contains,
					value1: "X"
				})
			);
				
			let oFilter = new sap.ui.model.Filter({
				filters: arrFilters, 
				and: false
			});

			oIdocTable.getBinding("rows").filter(oFilter);
				
			promiseShowPopupAlert =
				Utilities.showPopupAlert("Please fill all mandatory fields and try again", MessageBox.Icon.ERROR, "IDOC SAVE ERROR");
			promiseShowPopupAlert.then(function () {
			});
			return;
		}

		oController._selectTransportPackage(true, true, true)
		.then((oSelectedTransport) => {

				oData.Appl = oSelectedInterface.UsmdInterface;
				oData.Outbimpl = oSelectedInterface.Implementation;
				oData.WbTransport = oSelectedTransport.workbenchTransport; 
				oData.CustTransport = oSelectedTransport.customizingTransport;
				oData.Usmddevclass = oSelectedTransport.package;
				oData.Idoctyp = oSelectedInterface.Idoctyp;
				oData.Msgtyp = oSelectedInterface.Msgtyp;
				oData.Idocextension = oSelectedInterface.Idocextension;
				oData.Businesssystem = oInterfaceModel.getProperty("/selectedBusSystemForMapping");
				
				// Task 10877 - Deletion of extensions/segments in Mapping Tree - UI5
				let arrSegmentsToDelete = oSelectedInterface.arrIdocStructure.MAIN2IDOCSTRUCTNAV.results.filter((oSegment) => {
					return oSegment.Delete === "X";
				});
				// If there is any segments to be removed, we need to send them as part of MAIN2IDOCSTRUCTNAV array
				if (arrSegmentsToDelete.length > 0) {
					oData.MAIN2IDOCSTRUCTNAV = arrSegmentsToDelete.map((oSegment) => {
						return {
							Outbimpl: oSegment.Outbimpl,
							Idoctyp: oSelectedInterface.Idocextension,
							Segmenttype: oSegment.Segmenttype,
							Delete: oSegment.Delete,
							Deleteext: ( oSegment.Deleteext ? oSegment.Deleteext : ""),
							IDOCSTRUCT2IDOCATTRIBNAV: [],
							IDOCSTRUCT2SYNTAXATTRIBNAV: []
						};
					});
				// If we don't want to delete anything, just send an empty array
				} else {
					oData.MAIN2IDOCSTRUCTNAV = [{
						IDOCSTRUCT2IDOCATTRIBNAV: [],
						IDOCSTRUCT2SYNTAXATTRIBNAV: []
					}];
				}

				oData.MAIN2SEGMENTSNAV = [{
					SEG2SEGATTRIBNAV: [],
					SEG2SEGDEFATTRIBNAV: []
				}];
				
				oData.MAIN2MESSAGENAV = [];						
				
				if(oData.MAIN2SEGSTRUCTNAV.length !== 0) {
					oData.MAIN2SEGSTRUCTNAV.forEach((oMappedField) => {
						/**
						 *  Bug 11173 - Allow user to save mapping details when no mappings are selected
						 *	Delete all additional information saved in array SEGSTRUCT2ATTRIBNAV only if mappings are selected for saving
						*/
						oMappedField.SEGSTRUCT2ATTRIBNAV = [];
						
						/**
						 * Feature 11655 - Set Mapping Key from Mapping Table
						 * If Navigation consists of records
						 * 		->	In KeyValue property, Check for the complete path of mapping value enclosed within {{ and }}
						 * 				->	Convert the value to its relevant node identifier (NodeKey)
						 * 		->	If path in navigation record doesnt exist in keyvalue, it consist of junk data which is not used for mapping. Hence, it can be removed
						 */
						// if(oSelectedInterface.Commchannel === "1" && oMappedField.Transformation === "M") {
						if(oMappedField.SEGSTRUCT2SOAKEYMAPNAV.results.length > 0) {
							oMappedField.SEGSTRUCT2SOAKEYMAPNAV.results.forEach((oMappedKeyInfo, iIndex, arrMappingKeyInfo) => {
								if(oMappedField.Keyvalue.includes("{{" + oMappedKeyInfo.Parentpath + "-" + oMappedKeyInfo.Segmenttype + "-" + oMappedKeyInfo.Fieldname +"}}")) {
									oMappedField.Keyvalue = oMappedField.Keyvalue.replace("{{" + oMappedKeyInfo.Parentpath + "-" + oMappedKeyInfo.Segmenttype + "-" + oMappedKeyInfo.Fieldname +"}}", "{{" + oMappedKeyInfo.Nodekey + "}}");
								} 
								else {
									arrMappingKeyInfo.splice(iIndex, 1);
								}
							});
						}
						
					});
				}
				oInterfaceModel.setProperty("/wbTransport", undefined);
				oInterfaceModel.setProperty("/PackageSelected", undefined);
				
				(new DMRDataService(
					oController,
					"DRF_MODEL_LIST",
					"/MAINIDOCSTRUCTURESet",
					"saveIdocDetails",
					"/", // Root of the received data
					"Save IDOC Mapping Details"
				)).saveData(
					false,
					oData,
					null, {
						success: {
							fCallback: function (oParams, oResponseData) {
								let sError = "";
								let arrMessages = [];
								jQuery.extend(true, arrMessages, oResponseData.MAIN2MESSAGENAV.results);
								arrMessages.every((oMessage) => {
									if(oMessage.MessageType === "E") {
										//If Error exists in list, set value and break
										sError = "X";
										return false;
									} else {
										return true;
									}
								});
								
								ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
								if(sError !== "X") {
									oController.getInterfaceList(oController, oResponseData.Appl, oSelectedInterface.Txtmi); //, oResponseData.Outbimpl
									oController.byId("idIdocPanel").setExpanded(false);
								}
							},
							oParam: this
						},
						error: function () {
							promiseShowPopupAlert =
								Utilities.showPopupAlert("Mapping details could not be saved/updated.", MessageBox.Icon.ERROR, "IDOC SAVE ERROR");
							promiseShowPopupAlert.then(function () {
							});
						}
					});
		});
	};
	
	InterfacesController.makeRequestFromTree = function(oController, oIdocParent, arrSegmentStructure) {
		if(oIdocParent.children) {
			oIdocParent.children.forEach((oIdocChild) => {
				if(oIdocChild.bIsSegment === true) {
					oController.makeRequestFromTree(oController, oIdocChild, arrSegmentStructure);
				} else {
					if(oIdocChild.SEGSTRUCT2ATTRIBNAV.tableValueState === "X" ||
					oIdocChild.SEGSTRUCT2ATTRIBNAV.tableFieldValueState === "X" || oIdocChild.SEGSTRUCT2ATTRIBNAV.fixedValueState === "X") {
						oController.getView().getModel("viewInterface").setProperty("/sSaveValidationError", true);
					}
					if(oIdocChild.Transformation) {
						if(oIdocChild.Parentnodekey) {
							oIdocChild.Parentnodekey = oIdocChild.Parentnodekey.toString();
						}
						if(oIdocChild.Fieldnodekey) {
							oIdocChild.Fieldnodekey = oIdocChild.Fieldnodekey.toString();
						}
						arrSegmentStructure.push(oIdocChild);
					}
				}
			});
		}
	};
	
	InterfacesController.onSaveValidationFilterChange = function(oEvent) {
		let oController = this;
		let oIdocTable = oController.byId("IdocTree");
        
		let sDisplayedText = oEvent.getSource().getText();
		let arrFilters = [];
		if(sDisplayedText === "Show All") {
			oIdocTable.getBinding("rows").filter(arrFilters);
			oEvent.getSource().setText("Show Errors");
		} else if (sDisplayedText === "Show Errors") {
			
			arrFilters.push(					
				new sap.ui.model.Filter({
					path: "SEGSTRUCT2ATTRIBNAV/tableValueState", 
					operator: sap.ui.model.FilterOperator.Contains,
					value1: "X"})
				);
				
			arrFilters.push(					
				new sap.ui.model.Filter({
					path: "SEGSTRUCT2ATTRIBNAV/tableFieldValueState", 
					operator: sap.ui.model.FilterOperator.Contains,
					value1: "X"})
				);
				
			arrFilters.push(
				new sap.ui.model.Filter({
					path: "SEGSTRUCT2ATTRIBNAV/fixedValueState",
					operator: sap.ui.model.FilterOperator.Contains,
					value1: "X"
				})
			);

			let oFilter = new sap.ui.model.Filter({
				filters: arrFilters, 
				and: false
			});
			
			oIdocTable.getBinding("rows").filter(oFilter);
			oEvent.getSource().setText("Show All");
		}
	};
	
	InterfacesController.onLiveChangeTable = function(oEvent){
		let sTerm = oEvent.getParameter("value");
		let oInterfaceModel = this.getView().getModel("viewInterface");
		let oIdocStructureList = oInterfaceModel.getProperty("/selectedInterface/arrIdocStructure");
		let oTempArr = [];
		let oObject;
		let oIdocTable;
		let oCurrentRow;
		if(sTerm === ""){
			oObject = oEvent.getSource().getBindingContext();
			oIdocTable = this.byId("IdocTree");
			oCurrentRow = oIdocTable.getModel().getProperty(oObject.sPath);
	    	oCurrentRow.Tablefield = "";
	    	oCurrentRow.Keyvalue = "";
	    	oIdocTable.getModel().refresh();
		}
		oTempArr = oInterfaceModel.getProperty("/TableSuggestions");
		if (oTempArr) {
			let sTermCaps = sTerm.toUpperCase();
			oTempArr = oTempArr.filter(function (sValue) {
				return (sValue.Tabname.includes(sTermCaps) === true ? true : false);
			});

			if (oTempArr.length > 10) {
				oInterfaceModel.setProperty("/TableSuggestions", oTempArr);
				oInterfaceModel.refresh();
				return;
			}
		}
		let promiseTableLookup =
			GetListsData.getInterfaceMappingTablesList(this, sTerm.toUpperCase(), oIdocStructureList.Outbimpl, 20);

		promiseTableLookup.then(function (oData) {
			if (oData.results.length > 0) {
				oData.results.forEach(item => { item.Tabledesc = "(" + item.Tabledesc + ")"; });
				oInterfaceModel.setProperty("/TableSuggestions", oData.results);
				oInterfaceModel.refresh();
			}
		});
	};
	
	InterfacesController.onSelectTable = function(oEvent) {
	    let sTerm = oEvent.getParameter("value").toUpperCase().split("(")[0].trim();
	    let oController = this;
    	let oInterfaceModel = oController.getView().getModel("viewInterface");
    	let oIdocStructureList = oInterfaceModel.getProperty("/selectedInterface/arrIdocStructure");
    	let oObject = oEvent.getSource().getBindingContext();
		let oIdocTable = oController.byId("IdocTree");
		let oCurrentRow = oIdocTable.getModel().getProperty(oObject.sPath);
		
    	oCurrentRow.Tablefield ="";
		// Bug 11175 - Able to save mapping without table field for mapped transformation, which results in replication error
		// Table field is mandatory, so if field is empty, valueState and valueStateText should be shown
		oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableFieldValueState", "X");
		oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableFieldValueStateText", "Select a field from the list");
    	oCurrentRow.Keyvalue = "";
		oEvent.getSource().setValue(sTerm);
    	let promiseTableLookup =
			GetListsData.getInterfaceMappingTablesList(this, sTerm, oIdocStructureList.Outbimpl, 20);
			promiseTableLookup.then(function (oData) {
			if (oData.results.length > 0) {
				oInterfaceModel.setProperty("/TableSuggestions", oData.results);
				let sMatchedTable = oData.results.find((tableRow)=>{
					return tableRow.Tabname === sTerm;
				});
				if(sMatchedTable){
					oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableValueState", undefined);
					oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableValueStateText", "");
					
					oInterfaceModel.setProperty("/mappedValue", {});
					oInterfaceModel.setProperty("/mappedValue/sPath", oObject.sPath );
					
					oController.setKeyValuesForMapping(oController, oCurrentRow);
				}
				else
				{
					oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableValueState", "X");
					oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableValueStateText", sTerm+ " is not a valid entry");
				}
			}
			else
			{
				oInterfaceModel.setProperty("/TableSuggestions", []);
				oCurrentRow.Table = "";
				oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableValueState", "X");
				oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableValueStateText", "Select a Table from the list");
			}
			oIdocTable.getModel().refresh();
		});
	};
    	
	InterfacesController.onLiveChangeTableField = function(oEvent) {
	    let oController = this;
	    let sTerm = oEvent.getParameter("value");
    	let oInterfaceModel = this.getView().getModel("viewInterface");
    	let oObject = oEvent.getSource().getBindingContext();
		let oIdocTable = oController.byId("IdocTree");
		let oCurrentRow = oIdocTable.getModel().getProperty(oObject.sPath);
		
    	let promiseTableFieldLookup =
			GetListsData.getInterfaceMappingTableFieldsList(this, 
															oCurrentRow.Table,
															sTerm.toUpperCase(),
															oCurrentRow.SEGSTRUCT2ATTRIBNAV.Datatype,
															oCurrentRow.SEGSTRUCT2ATTRIBNAV.Intlen.toString(),
															20);

		promiseTableFieldLookup.then(function (oData) {
			if (oData.results.length > 0) {
				oInterfaceModel.setProperty("/TablefieldSuggestions", oData.results);
			}
			else
			{
				oInterfaceModel.setProperty("/TablefieldSuggestions", []);
			}
		});
	};
    	
	InterfacesController.onSelectTableField = function(oEvent) {
	    let sTerm = oEvent.getParameter("value").toUpperCase();
    	let oInterfaceModel = this.getView().getModel("viewInterface");
    	let oObject = oEvent.getSource().getBindingContext();
		let oIdocTable = this.byId("IdocTree");
		let oCurrentRow = oIdocTable.getModel().getProperty(oObject.sPath);
		oEvent.getSource().setValue(sTerm);
	    	let promiseTableLookup =
				GetListsData.getInterfaceMappingTableFieldsList(this, 
																oCurrentRow.Table, 
																sTerm,
																oCurrentRow.SEGSTRUCT2ATTRIBNAV.Datatype,
																oCurrentRow.SEGSTRUCT2ATTRIBNAV.Intlen.toString(),
																20);
				promiseTableLookup.then(function (oData) {
				if (oData.results.length > 0) {
					oInterfaceModel.setProperty("/TablefieldSuggestions", oData.results);
					let sMatchedTableField = oData.results.find((fieldRow)=>{
						return fieldRow.Fieldname === sTerm;
					});
					if(sMatchedTableField){
						oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableFieldValueState", undefined);
						oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableFieldValueStateText", "");
					}
					else
					{
						oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableFieldValueState", "X");
						oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableFieldValueStateText", sTerm+ " is not a valid entry");
					}
				}
				else
				{
					oInterfaceModel.setProperty("/TablefieldSuggestions", []);
					oCurrentRow.Tablefield = "";
					oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableFieldValueState", "X");
					oIdocTable.getModel().setProperty(oObject.sPath + "/SEGSTRUCT2ATTRIBNAV/tableFieldValueStateText", "Select a field from the list");

					oIdocTable.getModel().refresh();
				}
			});
    	};
    	
	InterfacesController.onKeyValuePress = function(oEvent) {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let oObject = oEvent.getSource().getBindingContext();
		let oIdocTable = oController.byId("IdocTree");
		let oCurrentRow = oIdocTable.getModel().getProperty(oObject.sPath);
		
		oInterfaceModel.setProperty("/mappedValue", {});
		oInterfaceModel.setProperty("/mappedValue/sPath", oObject.sPath );
		
		oController.setKeyValuesForMapping(oController, oCurrentRow);
	};
	
	InterfacesController.setKeyValuesForMapping = function(oController, oMappingRow) {
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let sSelectedOutbImpl = oInterfaceModel.getProperty("/selectedInterface/Implementation");
		let sKeyValue = oMappingRow.Keyvalue;
		let arrMappingKeyValues = [];
		let arrKeyValuePair;
		if(sKeyValue) {
			arrKeyValuePair = sKeyValue.split(",");
		}

		let promiseTableKeyFields = GetListsData.getTableKeyFields(oController, oMappingRow.Table, oMappingRow.Outbimpl);
		promiseTableKeyFields.then((arrTableKeyFields) => {
			if(arrTableKeyFields.results.length > 0) {
				if (!oController.TableKeyValuesDialog) {
					oController.TableKeyValuesDialog =
						sap.ui.xmlfragment(
							"TableKeyValues",
							"dmr.mdg.supernova.SupernovaFJ.view.Interfaces.TableKeyValues",
							oController);
					oController.getView().addDependent(oController.TableKeyValuesDialog);
				}
				
				// Open the dialog
				oController.TableKeyValuesDialog.open();
				sap.ui.core.BusyIndicator.show();

				/**
				 * Bug 11351 - Single read service for all key fields drop down values
				 * For specific outbound implementations, we need to disable the Value Input and default it to *
				 * Defaultvalue to be sent as *
				 */
				let sDefaultFlag = false;
				arrTableKeyFields.results.forEach((oKeyField) => {
					if(oKeyField.Defaultvalue === "*") {
						sDefaultFlag = true;
						arrMappingKeyValues.push({
							Text: oKeyField.Fieldname,
							Value: "*",
							Kind: oKeyField.Kind,
							arrKeyValues: []
						});
						
					}
				});
				if(sDefaultFlag) {
					oInterfaceModel.setProperty("/mappedValue/arrKeyValueList", arrMappingKeyValues);
					sap.ui.core.BusyIndicator.hide();
				} else if(oMappingRow.Segmenttype.includes(oMappingRow.Table)) {
					/**
					 * Bug 11351 - Single read service for all key fields drop down values
					 * Check whether the mapped table is a part of segment name
					 * If yes, default value * to disable all key fields
					 */
					arrTableKeyFields.results.forEach((oKeyField) => {
						arrMappingKeyValues.push({
							Text: oKeyField.Fieldname,
							Value: "*",
							Kind: oKeyField.Kind,
							arrKeyValues: []
						});
					});
					oInterfaceModel.setProperty("/mappedValue/arrKeyValueList", arrMappingKeyValues);
					sap.ui.core.BusyIndicator.hide();
				} else {
					/**
					 * Bug 11351 - Single read service for all key fields drop down values
					 * Send all fields for read in single service instead of multiple service calls for each field
					 *		All Models, Tables, Key fields to be sent as comma seperated string
					 *		If Model is blank, then it is Table-Field entry
					 *		Else, Entity-Attribute entry
					 * Check whether the results are from Model-Entity or from Table-Table field
					 * getAttributeValuesList to contain all models, tables, fields comma seperated as parameters
					 */

					let sModels = "";
					let sFields = "";
					let sTables = "";
					arrTableKeyFields.results.forEach((oKeyField) => {
						let sTableOrEntity;
						if (oKeyField.Model) {
							sModels += oKeyField.Model + ",";
							sTableOrEntity = oKeyField.Entity;
						} else {
							sTableOrEntity = oMappingRow.Table;
						}
						sTables += sTableOrEntity + ",";
						sFields += oKeyField.Fieldname + ",";
					}); 
					
					sModels = sModels ? sModels.substring(0, sModels.length - 1) : undefined;
					sTables = sTables.substring(0, sTables.length - 1);
					sFields = sFields.substring(0, sFields.length - 1);

					// Bug 13385 - added a new argument that establishes the limit of data 
					let promiseTableKeyValues = 
						GetListsData.getAttributeValuesList(oController,
														sModels,
														sTables,
														sFields,
														undefined,
														undefined,
														"2000");
					
					promiseTableKeyValues.then((arrKeyValues) => {
						/**
						 * Bug 11351 - Single read service for all key fields drop down values
						 * arrKeyValues consists of all possible values for all key fields
						 * Filter key values for each Key fields and push to array
						 */
						let arrFieldValues = [];
						if(arrKeyValuePair && arrKeyValuePair.length > 0) { //Key fields already assigned 
							arrTableKeyFields.results.forEach((oKeyField) => {
								if(oMappingRow.Segmenttype.includes(oMappingRow.Table) || //Table and Segment mapped to same table
									(oKeyField.Fieldname === "PARTNER" && ( sSelectedOutbImpl === "986_5" || sSelectedOutbImpl === "986_4"))) {
									arrMappingKeyValues.push({
										Text: oKeyField.Fieldname,
										Value: "*",
										Kind: oKeyField.Kind,
										arrKeyValues: []
									});
								} else {
									for(let i = 0; i < arrKeyValuePair.length ; i++) {
										let arrKey = arrKeyValuePair[i].split(":");
										if(oKeyField.Fieldname === arrKey[0]) {
											arrFieldValues = arrKeyValues.filter(oValue => {
												return oValue.Tablefield === oKeyField.Fieldname;
											});
											/**
											 * Bug 11938 - Add * list to Key Field dropdown list which are a part of current segment
											 * If dropdown values are available for the key fields and Interface selected is replicaion via IDOC
											 * 		->	Loop through all fields of the current mapping segment which matches the key field
											 * 				-> If found, then add list item "*" to the ComboBox
											 */
											if(arrFieldValues.length > 0 && oInterfaceModel.getProperty("/selectedInterface/Commchannel") === "2") {
												let oIdocTree = oController.byId("IdocTree").getModel().getProperty("/");
												let arrMatchedKeyField = [];
												oController.getKeyFieldInSegment(oController, oIdocTree, oMappingRow.Segmenttype, oKeyField.Fieldname, arrMatchedKeyField);
												if(arrMatchedKeyField.length > 0) {
													arrFieldValues.unshift({
														Ddtext: oKeyField.Fieldname + " mapped value",
														Key: "*"
													});
												}
											}
											/**
											 * Feature 11655 - Set Mapping Key from Mapping Table
											 * If value is mapped from mapping table for a particular key field
											 * 		->	Show the Field Name as value in input box
											 * 		->	 Add relevant mapping information to oMappingKeyInfo
											 */
											let oMappedKeyInfo = oMappingRow.SEGSTRUCT2SOAKEYMAPNAV.results.find(oMappedKey => {
												return oMappedKey.Tablekey === arrKey[0] &&
														"{{" + oMappedKey.Parentpath + "-" + oMappedKey.Segmenttype + "-" + oMappedKey.Fieldname + "}}" === arrKey[1];
											});
											if(oMappedKeyInfo) {
												arrMappingKeyValues.push({
													Text: arrKey[0],
													Value: oMappedKeyInfo.Fieldname,
													arrKeyValues: arrFieldValues,
													Kind: oKeyField.Kind,
													oMappingKeyInfo: {
														Nodekey: oMappedKeyInfo.Nodekey.toString(),
														Fieldname: oMappedKeyInfo.Fieldname,
														Parentseg: oMappedKeyInfo.Parentseg,
														Parentpath: oMappedKeyInfo.Parentpath,
														Segmenttype: oMappedKeyInfo.Segmenttype
													}
												});
											} else {
												arrMappingKeyValues.push({
													Text: arrKey[0],
													Value: arrKey[1],
													Kind: oKeyField.Kind,
													arrKeyValues: arrFieldValues
												});
											}
											
										}
									}
								}
							});
							
						} else { //Key fields being assigned for the first time
							arrTableKeyFields.results.forEach((oKeyField) => {
								if(oMappingRow.Segmenttype.includes(oMappingRow.Table) || //Table and Segment mapped to same table
										(oKeyField.Fieldname === "PARTNER" && ( sSelectedOutbImpl === "986_5" || sSelectedOutbImpl === "986_4"))) { 
									arrMappingKeyValues.push({
										Text: oKeyField.Fieldname,
										Value: "*",
										Kind: oKeyField.Kind,
										arrKeyValues: []
									});
								} else {
									arrFieldValues = arrKeyValues.filter(oValue => {
										return oValue.Tablefield === oKeyField.Fieldname;
									});
									/**
									 * Bug 11938 - Add * list to Key Field dropdown list which are a part of current segment
									 * If dropdown values are available for the key fields and Interface selected is replicaion via IDOC
									 * 		->	Loop through all fields of the current mapping segment which matches the key field
									 * 				-> If found, then add list item "*" to the ComboBox
									 */
									if(arrFieldValues.length > 0 && oInterfaceModel.getProperty("/selectedInterface/Commchannel") === "2") {
										let oIdocTree = oController.byId("IdocTree").getModel().getProperty("/");
										let arrMatchedKeyField = [];
										oController.getKeyFieldInSegment(oController, oIdocTree, oMappingRow.Segmenttype, oKeyField.Fieldname, arrMatchedKeyField);
										if(arrMatchedKeyField.length > 0) {
											arrFieldValues.unshift({
												Ddtext: oKeyField.Fieldname + " mapped value",
												Key: "*"
											});
										}
									}
									arrMappingKeyValues.push({
										Text: oKeyField.Fieldname,
										Value: undefined,
										Kind: oKeyField.Kind,
										arrKeyValues: arrFieldValues
									});
								}
							});
						}
						oInterfaceModel.setProperty("/mappedValue/arrKeyValueList", arrMappingKeyValues);
						sap.ui.core.BusyIndicator.hide();
					});	
				}
			}
		});
	};

	/**
	 * Bug 11938 - Add * list to Key Field dropdown list which are a part of current segment
	 * Check all children of the object to find segment sSegmentType
	 * 		->	If found, check all children of the segment object to find field sKeyField
	 * 				-> If match found, push the child object to arrMatchedKeyField and exit the iteration
	 * 		->	Else, recursively call the same function for the child segment object until arrMatchedKeyField is populated with the matched record
	 */
	InterfacesController.getKeyFieldInSegment = function(oController, oIdocTree, sSegmentType, sKeyField, arrMatchedKeyField) {
		if(oIdocTree.children && oIdocTree.children.length > 0) {
			oIdocTree.children.some(oChild => {
				if(oChild.bIsSegment) {
					if(oChild.Segment === sSegmentType) {
						let oMatch = oChild.children.find(oField => {
							return !oField.bIsSegment &&
									oField.Fieldname === sKeyField;
						});
						if(oMatch) {
							arrMatchedKeyField.push(oMatch);
							return true;
						} else {
							return false;
						}
						
					} else {
						if(arrMatchedKeyField.length === 0) {
							oController.getKeyFieldInSegment(oController, oChild, sSegmentType, sKeyField, arrMatchedKeyField);
							return false;
						} else {
							return true;
						}	
					}
				}
				return false;
			});
		}

	};
        	
	InterfacesController.onTransformationChange = function(oEvent) {
		let sTerm = oEvent.getParameters().selectedItem? oEvent.getParameters().selectedItem.getKey() : oEvent.getSource().getProperty("value");
		let oInterfaceModel = this.getView().getModel("viewInterface");
		let arrIdocStructureList = oInterfaceModel.getProperty("/selectedInterface/arrIdocStructure");
		let oObject = oEvent.getSource().getBindingContext();
		let oIdocTable = this.byId("IdocTree");
		let oCurrentRow = oIdocTable.getModel().getProperty(oObject.sPath);
		
		oCurrentRow.Table="";
		oCurrentRow.Tablefield="";
		oCurrentRow.Keyvalue= "";
		oCurrentRow.Fixedvalue="";
		oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableValueState = "";
		oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableValueStateText = "";
		oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableFieldValueState = "";
		oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableFieldValueStateText = "";
		oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueState = "";
		oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueStateText = "";
		
		if (sTerm === "M") {
			if(oCurrentRow.Segext === "X") {
				let sSelectedSegment = arrIdocStructureList.MAIN2IDOCSTRUCTNAV.results.find((idocItem) => {
					return idocItem.Segmenttype === oCurrentRow.Segmenttype;
				});
				if(sSelectedSegment) {
					let sReferencedTable = sSelectedSegment.IDOCSTRUCT2IDOCATTRIBNAV.results[0].Descrp;
					let promiseTableLookup =
						GetListsData.getInterfaceMappingTablesList(this, sReferencedTable.toUpperCase(), arrIdocStructureList.Outbimpl, 20);
					promiseTableLookup.then(function (oData) {
						if (oData.results.length > 0) {
							oCurrentRow.Table = sReferencedTable.toUpperCase();
							oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableValueState = undefined;
							oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableValueStateText = "";
							oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableFieldValueState = "X";
							oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableFieldValueStateText = "Select a field from the list";
						} else {
							oCurrentRow.Transformation = "";
							let promiseShowPopupAlert =
								Utilities.showPopupAlert("Mapped value transformation is not possible since referencing table does not exist for custom segment " + oCurrentRow.Segmenttype + " .\n Kindly edit the segment and select a valid referencing table from the list.",
									MessageBox.Icon.INFORMATION,
									"Transformation not possible");
							promiseShowPopupAlert.then(function () {
							});
						}
						oIdocTable.getModel().refresh();
					});	
				}
			} else {
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableValueState = "X";
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableFieldValueState = "X";
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableValueStateText = "Select a Table from the list";
				oCurrentRow.SEGSTRUCT2ATTRIBNAV.tableFieldValueStateText = "Select a field from the list";
			}
		}
		
		// Bug 11175 - Able to save mapping without table field for mapped transformation, which results in replication error
		// If Transformation is equals to Fixed Value, then Fixed Value field becomes mandatory
		if (sTerm === "F") {
			oCurrentRow.Fixedvalue="";
			oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueState = "X";
			oCurrentRow.SEGSTRUCT2ATTRIBNAV.fixedValueStateText = "Fixed value is mandatory";
		}
		
		oIdocTable.getModel().refresh();
	};
	
	/*
		* Bug 11139 - Interface Configuration Create and Change Processes
		* Description field should be enabled when any of the following conditions is true:
		* --> Creating a new interface
		* --> Editing an existing interface
		*/
	InterfacesController.DescriptionEnabled = function(sOperation) {
		if (sOperation === "new" || sOperation === "edit") {
			return true;
		}
		return false;
	};

	/*
		* Bug 11139 - Interface Configuration Create and Change Processes
		* Statys field should be enabled when any of the following conditions is true:
		* --> Creating a new interface
		* --> Editing an existing interface
		*/
	InterfacesController.ActiveEnabled = function(sOperation) {
		if (sOperation === "new" || sOperation === "edit") {
			return true;
		}
		return false;
	};

	/*
		* Bug 11139 - Interface Configuration Create and Change Processes
		* Variants field should be enabled when any of the following conditions is true:
		* --> Creating a new interface AND there is information to show
		* --> Editing an existing interface and communication channel is 3 and there is information to show
		*/
	InterfacesController.VariantsEnabled = function(sOperation, sCommChannel, arrVariants) {
		if (sOperation === "new" && arrVariants.length > 0) {
			return true;
		}
		if (sOperation === "edit" && sCommChannel === "3" && arrVariants.length > 0) {
			return true;
		}
		return false;
	};
	
	/*
		* Bug 11139 - Interface Configuration Create and Change Processes
		* This function gets triggered every time user activates or deactivates the interface
		*/
	InterfacesController.onActiveChange = function(oEvent) {
		let bActiveState = oEvent.getSource().getProperty("state");
		let oInterfaceModel = this.getView().getModel("viewInterface");
		let oSelectedInterface = oInterfaceModel.getProperty("/selectedInterface");

		if (bActiveState) {
			oSelectedInterface.Active = "Active";
		} else {
			oSelectedInterface.Active = "Inactive";
		}
	};
	
	InterfacesController.createNewInterfaceEnabled= function (sInterface, sImplementation, sCommChannel, sDataModel, sBusinessSystem, sDescription, sInterfaceType, sError, sSequence) {
		// let sValueState = this.getView().getModel("viewWorkflow").getProperty("/crTypeValueState");
		if (!sInterface) {
			return false;
		} else if (!sImplementation) {
			return false;
		} else if (!sCommChannel) {
			return false;
		} else if(!sDataModel) {
			return false;
		} else if (!sBusinessSystem || sBusinessSystem.length === 0) {
			return false;
		} else if (sInterfaceType && (sDescription === "" || sDescription === undefined)) {
			return false;
		} else if (sError === "Error"){
			return false;
		} else if(!/^[0-9]+$/.test(sSequence) && sSequence !== "") { //Sequence can have only numbers or blank value
			return false;
		}
		return true;
	};

	InterfacesController.onDescriptionChange = function() {
		let oInterfaceModel = this.getView().getModel("viewInterface");
		let sInterfaceDesc = oInterfaceModel.getProperty("/selectedInterface/UsmdInterfaceText");
		let sSelectedInterface = oInterfaceModel.getProperty("/selectedInterface/UsmdInterface");
		let bIsNewInterface = oInterfaceModel.getProperty("/newInterface");
		if(sSelectedInterface && bIsNewInterface && !sInterfaceDesc) {
			oInterfaceModel.setProperty("/selectedInterface/interfaceDescValueState", sap.ui.core.ValueState.Error); 
		} else {
			oInterfaceModel.setProperty("/selectedInterface/interfaceDescValueState", sap.ui.core.ValueState.None); 
		}
	};

	InterfacesController.onBusinessSystemChange = function() {
		let oInterfaceModel = this.getView().getModel("viewInterface");
		let sSelectedBusinessSystem = oInterfaceModel.getProperty("/selectedInterface/selectedBusinessSystems");
		// Task 13334 - set the property businessSystemChange on adding or deleting a business system.
		oInterfaceModel.setProperty("/selectedInterface/businessSystemChange", true);

		if(!sSelectedBusinessSystem.length) {
			oInterfaceModel.setProperty("/selectedInterface/interfaceBusisyValueState", sap.ui.core.ValueState.Error); 
		} else {
			oInterfaceModel.setProperty("/selectedInterface/interfaceBusisyValueState", sap.ui.core.ValueState.None); 
		}
	};

	InterfacesController.onExtensionPress = function (oEvent) {
		let oSourceEvent = oEvent;
		let sSource = oEvent.getSource().getId();
		let oViewModel = this.getView().getModel("viewInterface");
		let arrMappingDetailsList = this.getView().getModel("viewInterface").getProperty("/selectedInterface/arrIdocStructure/MAIN2IDOCSTRUCTNAV");
		let promiseShowPopupAlert;
		let idocAction;
		oViewModel.setProperty("/sSelectedSegment", "");
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		let sSegment;
		let sRefSegment;
		if (sSource === "container-SupernovaFJ---Interfaces--addSegment") {
			idocAction = "addSegment";
		} else if (sSource === "container-SupernovaFJ---Interfaces--editSegment") {
			idocAction = "editSegment";
		// Task 10877 - Deletion of extensions/segments in Mapping Tree - UI5
		} else if (sSource === "container-SupernovaFJ---Interfaces--deleteSegment") {
			idocAction = "deleteSegment";
		} else if (sSource === "container-SupernovaFJ---Interfaces--addExtensionDetails") {
			idocAction = "addExtension";
		// Task 10877 - Deletion of extensions/segments in Mapping Tree - UI5
		} else if (sSource === "container-SupernovaFJ---Interfaces--deleteExtension") {
			idocAction = "deleteExtension";
		}
		let oTree = this.byId("IdocTree");
		let aSelectedIndices = oTree.getSelectedIndices();
		
		if((idocAction === "editSegment") && (!aSelectedIndices || !aSelectedIndices.length)){
			promiseShowPopupAlert =
				Utilities.showPopupAlert("Please select a custom segment", MessageBox.Icon.INFORMATION, "Segment");
				promiseShowPopupAlert.then(function () {
				});
				return;
		}
		if (aSelectedIndices.length) {
			let sPath = oTree.getContextByIndex(aSelectedIndices[0]).sPath;
			let oLength = sPath.replace(/[^/]/g, "").length;
			this.addSegmentExtensionDetails();
			sSegment = this.getView().getModel("viewInterface").getProperty("/SegmentType");
			sRefSegment = this.getView().getModel("viewInterface").getProperty("/RefSegment");
			let arrIdocStructureList = this.getView().getModel("viewInterface").getProperty("/selectedInterface/arrIdocStructure");
			let selectedSegmentRecord = arrIdocStructureList.MAIN2IDOCSTRUCTNAV.results.filter(function(idocItem){
				return idocItem.Segmenttype === sSegment;
			});
			if (oLength !== 2 && idocAction === "addSegment") {
				promiseShowPopupAlert =
					Utilities.showPopupAlert("You can not add segment here. Please select root parent to add", MessageBox.Icon.INFORMATION, "Segment");
					promiseShowPopupAlert.then(function () {
					});
					return;
			} else if (selectedSegmentRecord[0].Segext === "" && idocAction === "editSegment") {
				promiseShowPopupAlert =
					Utilities.showPopupAlert("Only custom segments can be edited", MessageBox.Icon.INFORMATION, "Segment");
					promiseShowPopupAlert.then(function () {
					});
					return;
			// Task 10877 - Deletion of extensions/segments in Mapping Tree - UI5
			} else if (selectedSegmentRecord[0].Segext === "" && idocAction === "deleteSegment") {
				promiseShowPopupAlert =
					Utilities.showPopupAlert("Only custom segments can be deleted", MessageBox.Icon.INFORMATION, "Segment");
					promiseShowPopupAlert.then(function () {
					});
					return;
			// Task 10877 - Deletion of extensions/segments in Mapping Tree - UI5
			} else if (oLength !== 2 && idocAction === "deleteExtension") {
				promiseShowPopupAlert =
					Utilities.showPopupAlert("You can not delete extension here. Please select root parent to delete", MessageBox.Icon.INFORMATION, "Extension");
					promiseShowPopupAlert.then(function () {
					});
					return;
			}
		} else {
			sSegment = "";
			sRefSegment = "";
		}

			/**
			 * Task 10877 - Deletion of extensions/segments in Mapping Tree - UI5
			 * If the user clicked Delete Segment button, we need to mark the selected segment as Deleted = "X" and call onSaveMapping Details
			 */
			if (idocAction === "deleteSegment") {
				let arrSelectedSegments = arrMappingDetailsList.results.filter(function(idocItem){
					return idocItem.Segmenttype === sSegment;
				});
				if (arrSelectedSegments.length > 0) {
					for (let oSelectedSegment of arrSelectedSegments) {
						oSelectedSegment.Delete = "X";
					}
				}
				this.onSaveMappingDetails(oSourceEvent);
			
			/**
			 * Task 10877 - Deletion of extensions/segments in Mapping Tree - UI5
			 * If the user clicked Delete Extension button, we need to mark all custom segments as Deleted = "X" and deleteext = "X" and call onSaveMapping Details
			 */
			} else if (idocAction === "deleteExtension") {
				let arrCustomSegments = arrMappingDetailsList.results.filter(function(idocItem){
					return idocItem.Segext !== "";
				});
				if (arrCustomSegments.length > 0) {
					for (let oCustomSegment of arrCustomSegments) {
						oCustomSegment.Delete = "X";
						oCustomSegment.Deleteext = "X";
					}
				}
				this.onSaveMappingDetails(oSourceEvent);
			}

		/*
			* Task 10877 - Deletion of extensions/segments in Mapping Tree - UI5
			* If user clicked any other button, then we need to redirect to IdocExtensionWizard page
			*/
		 else {
			oRouter.navTo("IdocExtensionWizard", {
				segmentType: sSegment,
				Outbimpl: this.getView().getModel("viewInterface").getProperty("/selectedInterface/Implementation"),
				Commchannel: this.getView().getModel("viewInterface").getProperty("/selectedInterface/Commchannel"),
				Interface: this.getView().getModel("viewInterface").getProperty("/selectedInterface/UsmdInterface"),
				BusinessSysMap: this.getView().getModel("viewInterface").getProperty("/selectedBusSystemForMapping"),
				idocAction: idocAction,
				RefSegment: sRefSegment
			});
		}
	};
	
	InterfacesController.addSegmentExtensionDetails = function () {
		let oViewModel = this.getView().getModel("viewInterface");
		oViewModel.setProperty("/sSelectedSegment", "");
		let oTree = this.byId("IdocTree");
		let aSelectedIndices = oTree.getSelectedIndices();
		let sPath = oTree.getContextByIndex(aSelectedIndices[0]).sPath;
		let sSegName;
		let sParSegment;

		let oParentNode = oTree.getModel().getProperty(sPath);

		// if this is one of the root nodes, the content is in the first child
		if (sPath.indexOf("children") === sPath.lastIndexOf("children")) {
			oParentNode = oParentNode.children[0];
		}
		if(oParentNode.Segment)
		{
			sSegName = oParentNode.Segment;
			sParSegment = oParentNode.ParentSegment;
		}else{
			sSegName = oParentNode.Segmenttype;
			sParSegment = oParentNode.Parentseg;
		}
		
		// Update the parent name to the model
		this.getView().getModel("viewInterface").setProperty("/SegmentType", sSegName);
		this.getView().getModel("viewInterface").setProperty("/RefSegment", sParSegment);
		oViewModel.refresh(true);
	};
	
	InterfacesController.onEditIdocDetails = function() {
		let oInterfaceModel = this.getView().getModel("viewInterface");
		oInterfaceModel.setProperty("/editIdoc", true);
		oInterfaceModel.setProperty("/editMode", true);
	};
	
	InterfacesController.onCancelDetails = function() {
		let oInterfaceModel = this.getView().getModel("viewInterface");
		oInterfaceModel.setProperty("/editMode", false);
		oInterfaceModel.setProperty("/enableProxyDetails", false);
		oInterfaceModel.setProperty("/editIdoc", false);
		oInterfaceModel.setProperty("/enableMappingDetailsTable", false);
		let oBusinessSystemComboBox = sap.ui.getCore().byId("container-SupernovaFJ---Interfaces--idIdocBusSystem");
		let oSelectedBusSys = oBusinessSystemComboBox.getSelectedItem();
		if (oSelectedBusSys) {
				oBusinessSystemComboBox.fireChange({
				selectedItem: oSelectedBusSys
			});
		}
	};
	
	InterfacesController.onClickFilter = function() {
		let oViewModel = this.getView().getModel("viewInterface");
		let sFilterSegment = oViewModel.getProperty("/sSelectedSegmentForFilter");
		let sFilterField = oViewModel.getProperty("/sSelectedFieldForFilter");
		let sFilterTransformation = oViewModel.getProperty("/sSelectedTransformationForFilter");
		let oIdocTable = this.byId("IdocTree");
		
		let arrOrFilter = [];
		let arrAndFilter = [];
		
		if(sFilterSegment) {
			arrOrFilter.push(					
				new sap.ui.model.Filter({
					path: "Segment", 
					operator: sap.ui.model.FilterOperator.Contains,
					value1: sFilterSegment
				})
			);
			// Bug 13004 - Added filter to include the children of the segment
			arrOrFilter.push(					
				new sap.ui.model.Filter({
					path: "Parentseg", 
					operator: sap.ui.model.FilterOperator.Contains,
					value1: sFilterSegment,
					and: false
				})
			);
			arrOrFilter.push(					
				new sap.ui.model.Filter({
					path: "Segmenttype", 
					operator: sap.ui.model.FilterOperator.Contains,
					value1: sFilterSegment,
					and: false
				})
			);
			arrAndFilter.push(new sap.ui.model.Filter({
				filters: arrOrFilter,
				and: false
			}));
		}
		
		if(sFilterField) {	
			arrAndFilter.push(					
				new sap.ui.model.Filter({
					path: "Fieldname", 
					operator: sap.ui.model.FilterOperator.Contains,
					value1: sFilterField
				})
			);
		}
		
		if(sFilterTransformation) {
			arrAndFilter.push(
				new sap.ui.model.Filter({
					path: "Transformation",
					operator: sap.ui.model.FilterOperator.Contains,
					value1: sFilterTransformation
				})
			);	
		}
		
		let oFilter = new sap.ui.model.Filter({
			filters: arrAndFilter, 
			and: true
		});
		
		oIdocTable.expandToLevel(10);
		oIdocTable.getBinding("rows").filter(oFilter);
	};
	
	InterfacesController.onClearFilter = function() {
		let oInterfaceModel = this.getView().getModel("viewInterface");
		let oIdocTable = this.byId("IdocTree");
		
		oInterfaceModel.setProperty("/sSelectedSegmentForFilter", undefined);
		oInterfaceModel.setProperty("/sSelectedFieldForFilter", undefined);
		oInterfaceModel.setProperty("/sSelectedTransformationForFilter", undefined);
		this.byId("idTransformationFilterSelect").setValue("Select Transformation");
		oInterfaceModel.setProperty("/sValidationText", "Show Errors");
		
		let arrFilter = [];
		
		//Get Tree Table to its original form
		oIdocTable.collapseAll();
		oIdocTable.expandToLevel(1);
		oIdocTable.getBinding("rows").filter(arrFilter);
	};
	
	InterfacesController.onClearOutbImpl = function(){
			let oViewModel = this.getView().getModel("viewInterface");
			oViewModel.setProperty("/selectedInterface/Implementation", "");
	};
	
	InterfacesController.onClearIntDataModel = function(){
		let oViewModel = this.getView().getModel("viewInterface");
		oViewModel.setProperty("/selectedInterface/UsmdModel", "");
		oViewModel.setProperty("/selectedInterface/selectedVariants", "");
	};
	
	InterfacesController.onClearFilterTime = function(){
		let oViewModel = this.getView().getModel("viewInterface");
		oViewModel.setProperty("/selectedInterface/Filtertp", "");
	};
			
	return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.Interfaces.Interfaces", InterfacesController);
});