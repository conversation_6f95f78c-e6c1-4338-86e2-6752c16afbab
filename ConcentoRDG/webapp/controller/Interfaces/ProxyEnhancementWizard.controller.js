sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
	"jquery.sap.global",
	"sap/ui/core/mvc/Controller",
	"sap/ui/core/Item",
	"sap/ui/model/Filter",
	"sap/ui/model/json/JSONModel",
	"sap/m/MessageBox",
	"sap/m/MessageToast",
	"sap/ui/core/routing/History",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"dmr/mdg/supernova/SupernovaFJ/model/DmAction",
	"dmr/mdg/supernova/SupernovaFJ/model/models",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"dmr/mdg/supernova/SupernovaFJ/model/ModelMessages"
], function (BaseController, jQ<PERSON>y, Controller, Item, Filter, JSONModel, MessageBox, MessageToast, History,
	DMRDataService, GetListsData, DmAction, RDGModels, Utilities, ModelMessages) {
	"use strict";

	let ProxyEnhancementWizardController = {};
    
	ProxyEnhancementWizardController.onInit = function () {
		let oComponent = this;
		let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
		
		oComponent._wizard = oComponent.byId("ProxyEnhancementWizard");
		oComponent.ModelMessages = ModelMessages;
		oRouter.getRoute("ProxyEnhancementWizard").attachMatched(this._onRouteMatched, this);
	};

	ProxyEnhancementWizardController._onRouteMatched = function (oEvent) {
		let oComponent = this;
		let oView = this.getView();
		let oProxyModel = oView.getModel("proxyEnhancementModel");
		let promiseAbap = GetListsData.getABAPDetails(oComponent);
		let sParSeg = oEvent.getParameter("arguments").WizardType;
		let sOutbImpl = oEvent.getParameter("arguments").Outbimpl;
		let sDataModel = oEvent.getParameter("arguments").DataModel;
		let sEnhancement = oEvent.getParameter("arguments").Enhancement;
		let sElement = oEvent.getParameter("arguments").Element;
		let promiseEnhancement = GetListsData.getEnhancementDetails(oComponent, sOutbImpl);
		let sPrefix;
		// Bug 12518 - Mapping Details name length limit on Interface
		if(sOutbImpl === "986_3"){
			sPrefix = "ZRDG_MDG_" + sDataModel + "_REL_";
		} else{
			sPrefix = "ZRDG_MDG_" + sDataModel + "_";
		}
		// END Bug 12518 - Mapping Details name length limit on Interface

		if(!oProxyModel) {
			oProxyModel = new JSONModel();
			this.getView().setModel(oProxyModel, "proxyEnhancementModel");
		}
		
		//Step 1
		oProxyModel.setProperty("/enhancementType", "");
		//Step 2
		oProxyModel.setProperty("/enhancementData", {});
		//Step 3
		oProxyModel.setProperty("/elementData", {});
		//Step 4
		oProxyModel.setProperty("/attributeData", []);
		oProxyModel.setProperty("/deletedAttributes", []);
		
		
		promiseAbap.then(function(arrAbapData){
			oProxyModel.setProperty("/abapData", arrAbapData);
			let proxyWizard =  oView.byId("proxyEnhancementWizard");
			let oTable;
			let promiseProxy;
			let oCreateEnhancementStep;
			let sEnhancementType;
			let arrAttributeData;
			let oElementNameInput;
			let oElementData;
			let btnAdd;
			let sEnhancementName;
			if(sEnhancement){
				// Bug 12518 - Mapping Details name length limit on Interface
				sEnhancementName = sEnhancement.replace(sPrefix, "");
				// End Bug 12518 - Mapping Details name length limit on Interface
			}
			if(sParSeg==="Edit"){

				btnAdd = oView.byId("addAttribute");
				btnAdd.setVisible(false);

				oElementNameInput = oView.byId("ElementName");
				oElementNameInput.setEnabled(false);

				oTable = oView.byId("attrTable");
				if(oTable.getMode()==="Delete"){
					oTable.setMode("None");
					oTable.detachDelete(oComponent.onDeleteAttribute);
				}


				promiseProxy = GetListsData.getProxyDetails(oComponent, sOutbImpl, sEnhancementName, sEnhancement);

				promiseProxy.then(function(proxyDetails){
					oCreateEnhancementStep = oComponent.byId("createEnhancement");

						if(sElement){
							sEnhancementType = "Element";
						}else{
							sEnhancementType = "Attribute";
						}
					
					if(sEnhancementType==="Element"){
						oView.byId("rbElement").setSelected(true);
						oView.byId("rbAttribute").setEnabled(false);
						oCreateEnhancementStep.setNextStep(oView.byId("createElement"));
						proxyWizard.nextStep();
						oElementData = proxyDetails[0].SRVDETAILSTOELEMENTSNAV.results.find((el)=>{
							return el.ElementPrefix === sElement;
						});
						arrAttributeData = oElementData.ELEMENTSTOATTRIBUTESNAV.results;

						oComponent.onInitFetchValues(oComponent, oElementData, arrAttributeData);

					} else {

						oView.byId("rbAttribute").setSelected(true);
						oView.byId("rbElement").setEnabled(false);
						arrAttributeData = proxyDetails[0].SRVDETAILSTOATTRIBUTESNAV.results;
						oComponent.onInitFetchValues(oComponent, null, arrAttributeData);
						
					}
					
					proxyWizard.nextStep();
					proxyWizard.nextStep();
					oProxyModel.setProperty("/enhancementType", sEnhancementType);

					// Bug 12512 - Edit button for proxy extension shows blank screen for creating attributes
					if(arrAttributeData.length){
						oComponent.onInitFetchValues(oComponent, null, arrAttributeData);
					} else{
						oTable = oView.byId("attrTable");
						if(oTable.getMode()===""||oTable.getMode()==="None"){
							oTable.setMode("Delete");
							oTable.attachDelete(oComponent, oComponent.onDeleteAttribute);
							btnAdd = oView.byId("addAttribute");
							btnAdd.setVisible(true);
						}
					}
					//End Bug 12512 - Edit button for proxy extension shows blank screen for creating attributes
					
					oProxyModel.setProperty("/transport", proxyDetails[0].Transport);
					oProxyModel.setProperty("/PackageSelected", proxyDetails[0].Package);
					oProxyModel.setProperty("/enhancementData/sEnhancementName", sEnhancementName);
					oProxyModel.setProperty("/enhancementData/sEnhancementDescription", proxyDetails[0].Description);
				});
				oComponent.validateWizardToSave();
			} else if(sParSeg==="AddData" && !sElement){
				// Bug 12512 - Edit button for proxy extension shows blank screen for creating attributes
				oTable = oView.byId("attrTable");
				if(oTable.getMode()===""||oTable.getMode()==="None"){
					oTable.setMode("Delete");
					oTable.attachDelete(oComponent, oComponent.onDeleteAttribute);
				}
				//End Bug 12512 - Edit button for proxy extension shows blank screen for creating attributes


				btnAdd = oView.byId("addAttribute");
				btnAdd.setVisible(true);

				sEnhancementType = "Attribute";	
				oProxyModel.setProperty("/enhancementType", sEnhancementType);

				oView.byId("rbElement").setEnabled(true);
				oView.byId("rbAttribute").setEnabled(true);
				oView.byId("ElementName").setEnabled(true);

				promiseProxy = GetListsData.getProxyDetails(oComponent, sOutbImpl, sEnhancementName, sEnhancement);
				promiseProxy.then(function(proxyDetails){
					oProxyModel.setProperty("/transport", proxyDetails[0].Transport);
					oProxyModel.setProperty("/PackageSelected", proxyDetails[0].Package);
					oProxyModel.setProperty("/enhancementData/sEnhancementName", sEnhancementName);
					oProxyModel.setProperty("/enhancementData/sEnhancementDescription", proxyDetails[0].Description);
					oCreateEnhancementStep = oView.byId("createEnhancement");
					oCreateEnhancementStep.setValidated(true);
					arrAttributeData = proxyDetails[0].SRVDETAILSTOATTRIBUTESNAV.results;
					oComponent.onInitFetchValues(oComponent, null, arrAttributeData);

				});
			} else if(sParSeg==="AddData" && sElement){
				oTable = oView.byId("attrTable");
				if(oTable.getMode()===""||oTable.getMode()==="None"){
					oTable.setMode("Delete");
					oTable.attachDelete(oComponent, oComponent.onDeleteAttribute);
				}

				btnAdd = oView.byId("addAttribute");
				btnAdd.setVisible(true);

				sEnhancementType = "Element";	
				oProxyModel.setProperty("/enhancementType", sEnhancementType);

				oElementNameInput = oView.byId("ElementName");
				oElementNameInput.setEnabled(false);
				
				promiseProxy = GetListsData.getProxyDetails(oComponent, sOutbImpl, sEnhancementName, sEnhancement);
				promiseProxy.then(function(proxyDetails){
					oProxyModel.setProperty("/transport", proxyDetails[0].Transport);
					oProxyModel.setProperty("/PackageSelected", proxyDetails[0].Package);
					oProxyModel.setProperty("/enhancementData/sEnhancementName", sEnhancementName);
					oProxyModel.setProperty("/enhancementData/sEnhancementDescription", proxyDetails[0].Description);
					oCreateEnhancementStep = oComponent.byId("createEnhancement");
					oView.byId("rbElement").setSelected(true);
					oView.byId("rbAttribute").setEnabled(false);
					
					oCreateEnhancementStep.setNextStep(oView.byId("createElement"));
					oElementData = proxyDetails[0].SRVDETAILSTOELEMENTSNAV.results.find((el)=>{
						return el.ElementPrefix === sElement;
					});

					proxyWizard.nextStep();
					proxyWizard.nextStep();
					// Bug 12484 - Max occurs eq Unbounded is not working for Proxy extension element
					proxyWizard.nextStep();
					// END Bug 12484 - Max occurs eq Unbounded is not working for Proxy extension element

					
					arrAttributeData = oElementData.ELEMENTSTOATTRIBUTESNAV.results;
					oComponent.onInitFetchValues(oComponent, oElementData, arrAttributeData);
			

				});
				oComponent.validateWizardToSave();

			}else {
				oTable = oView.byId("attrTable");
				if(oTable.getMode()===""||oTable.getMode()==="None"){
					oTable.setMode("Delete");
					oTable.attachDelete(oComponent, oComponent.onDeleteAttribute);
				}

				btnAdd = oView.byId("addAttribute");
				btnAdd.setVisible(true);
			}
		});

		promiseEnhancement.then(function(enhancementData){
			oProxyModel.setProperty("/arrEnhancements", enhancementData);
		});
		// Bug 12518 - Mapping Details name length limit on Interface
		oView.byId("ElementName").setMaxLength(30-sPrefix.length);
		oView.byId("enhanceName").setMaxLength(30-sPrefix.length);
		// End Bug 12518 - Mapping Details name length limit on Interface
		
		oProxyModel.setProperty("/parSegment", sParSeg);
		oProxyModel.setProperty("/dataModel", sDataModel);
		// Bug 12518 - Mapping Details name length limit on Interface
		oProxyModel.setProperty("/Prefix", sPrefix);
		// End Bug 12518 - Mapping Details name length limit on Interface
		oProxyModel.setProperty("/Outbimpl", sOutbImpl);
		oProxyModel.setProperty("/EbSaveButton", false);
	};

	ProxyEnhancementWizardController.onInitFetchValues = function(oComponent, oElementData, arrAttributeData){
		let sXSDType, oMatchedXSDType, sChangeRowPath, oABAPSelect;
		let oView = oComponent.getView();
		let oProxyModel = oView.getModel("proxyEnhancementModel");
		let arrAbapData = oProxyModel.getProperty("/abapData");

		if(oElementData){
			sXSDType = oElementData.XSDType.split(" ")[0];
			oMatchedXSDType = arrAbapData.find((oXSD)=>{
				return oXSD.XSDType === sXSDType;
			});
	
			if(oMatchedXSDType){
				oElementData.EbPattern = oMatchedXSDType.Pattern;
				oElementData.EbLength = oMatchedXSDType.Length;
				oElementData.EbMinLength = oMatchedXSDType.MinLength;
				oElementData.EbMaxLength = oMatchedXSDType.MaxLength;	
				oElementData.EbMinIncl = oMatchedXSDType.MinIncl;
				oElementData.EbMaxIncl = oMatchedXSDType.MaxIncl;
				oElementData.EbMinExcl = oMatchedXSDType.MinExcl;
				oElementData.EbMaxExcl = oMatchedXSDType.MaxExcl;
				oElementData.EbTotalDigits = oMatchedXSDType.TotalDigits;
				oElementData.EbFractionDigits = oMatchedXSDType.FractionDigits;
			}
			oElementData.MaxOccurs = oElementData.MaxOccurs.trim();
			oElementData.maxOccursState = oElementData.MaxOccurs!=="1" && oElementData.MaxOccurs!=="unbounded" ? "Error" : "";
			oElementData.maxOccursStateText = oElementData.MaxOccurs!=="1" && oElementData.MaxOccurs!=="unbounded" ? "Please select a value" : "";
			oElementData.MinOccurs = oElementData.MinOccurs.trim();
			oElementData.Pattern = oElementData.Pattern.trim();
			oElementData.Length = oElementData.Length.trim()==="0"?"":oElementData.Length.trim();
			oElementData.MinLength = oElementData.MinLength.trim()==="0"?"":oElementData.MinLength.trim();
			oElementData.MaxLength = oElementData.MaxLength.trim()==="0"?"":oElementData.MaxLength.trim();
			oElementData.MinIncl = oElementData.MinIncl.trim();
			oElementData.MaxIncl = oElementData.MaxIncl.trim();
			oElementData.MinExcl = oElementData.MinExcl.trim();
			oElementData.MaxExcl = oElementData.MaxExcl.trim();
			oElementData.TotalDigits = oElementData.TotalDigits.trim()==="0"?"":oElementData.TotalDigits.trim();
			oElementData.FractionDigits = oElementData.FractionDigits.trim()==="0"?"":oElementData.FractionDigits.trim();
			oElementData.elementNameState = "None";
			oElementData.abapTypeState="None";
			oElementData.lengthState="None";
			oElementData.MaxInclState="None";
			oElementData.MinInclState="None";
			oElementData.MaxExclState="None"; 
			oElementData.MinExclState="None"; 
			oElementData.fractionDigitsState="None";

			oProxyModel.setProperty("/elementData", oElementData);

			oABAPSelect = oView.byId("elAbapType");
			sChangeRowPath = "/elementData/";

			oComponent.dynamicABAPSelection(oABAPSelect, oElementData, sChangeRowPath);
			oComponent.elementStepValidation(oElementData);
		} 

		if(arrAttributeData){
			arrAttributeData.forEach((attrData)=>{
				sXSDType = attrData.XSDType.split(" ")[0];
				oMatchedXSDType = arrAbapData.find((oXSD)=>{
					return oXSD.XSDType === sXSDType;
				});
		
				if(oMatchedXSDType){
					attrData.EbName = false;
					attrData.EbPattern = oMatchedXSDType.Pattern;
					attrData.EbLength = oMatchedXSDType.Length;
					attrData.EbMinLength = oMatchedXSDType.MinLength;
					attrData.EbMaxLength = oMatchedXSDType.MaxLength;	
					attrData.EbMinIncl = oMatchedXSDType.MinIncl;
					attrData.EbMaxIncl = oMatchedXSDType.MaxIncl;
					attrData.EbMinExcl = oMatchedXSDType.MinExcl;
					attrData.EbMaxExcl = oMatchedXSDType.MaxExcl;
					attrData.EbTotalDigits = oMatchedXSDType.TotalDigits;
					attrData.EbFractionDigits = oMatchedXSDType.FractionDigits;
					attrData.attrNameValueState = "None";
					attrData.attrNameValueStateText = "";
					attrData.Pattern = attrData.Pattern.trim();
					attrData.Length = attrData.Length.trim()==="0"?"":attrData.Length.trim();
					attrData.MinLength = attrData.MinLength.trim()==="0"?"":attrData.MinLength.trim();
					attrData.MaxLength = attrData.MaxLength.trim()==="0"?"":attrData.MaxLength.trim();
					attrData.MinIncl = attrData.MinIncl.trim();
					attrData.MaxIncl = attrData.MaxIncl.trim();
					attrData.MinExcl = attrData.MinExcl.trim();
					attrData.MaxExcl = attrData.MaxExcl.trim();
					attrData.TotalDigits = attrData.TotalDigits.trim()==="0"?"":attrData.TotalDigits.trim();
					attrData.FractionDigits = attrData.FractionDigits.trim()==="0"?"":attrData.FractionDigits.trim();
				}
					if(attrData.Optional==="X"){
						attrData.Optional=true;
					} else {
						attrData.Optional=false;
					}
			});

			oProxyModel.setProperty("/attributeData", arrAttributeData);
			oProxyModel.setProperty("/tempAttributeData", arrAttributeData);
			for(let i=0; i<arrAttributeData.length; i++){
				oABAPSelect = oView.byId("attrTable").getItems()[i].getCells()[4];
				sChangeRowPath = "/attributeData/"+i;
				oComponent.dynamicABAPSelection(oABAPSelect, arrAttributeData[i], sChangeRowPath);
			}
		}
	};

	ProxyEnhancementWizardController.onTransportPackageSelectionDialogCreated = function (oEvent) {
		let comp = oEvent.getParameter("component");
		// store the component handle 
		this._transportSelectionDialog = comp;

		this.getUsername()
		.then(function (oSapUserInfo) {
			let sUsername = oSapUserInfo.Sapname;
			if (!sUsername) {
				Utilities.showPopupAlert("Please login to continue.", MessageBox.ERROR, "Logged out");
			} else {
				comp.setUser(sUsername);
			}

		});
	};

	ProxyEnhancementWizardController._selectTransportPackage = function (bSelectCustomizing, bSelectWorkbench, bSelectPackage) {
		if(!this._transportSelectionDialog){
			// If the component is not yet available, return a rejected promise
			return Promise.reject("Transport Package Selection Dialog is not ready.");
		}

		const oView = this.getView();
		const oViewInterfaceModel = oView.getModel("proxyEnhancementModel");

		// Retrieve necessary properties from the model
		const sPreselectCustomizingTr = oViewInterfaceModel.getProperty("/customizingTransport");
		const sPreselectWorkbenchTr = oViewInterfaceModel.getProperty("/workbenchTransport");
		const sPreselectPackage = oViewInterfaceModel.getProperty("/packageSelected");

		let promise = this._transportSelectionDialog.open(
			bSelectCustomizing, sPreselectCustomizingTr, 
			bSelectWorkbench, sPreselectWorkbenchTr,
			bSelectPackage, sPreselectPackage
		);

		// Wait for the dialog to close and store the selection
		let responsePromise = promise.then(function(oResponseObject){
			oViewInterfaceModel.setProperty("/customizingTransport", oResponseObject.customizingTransport);
			oViewInterfaceModel.setProperty("/workbenchTransport", oResponseObject.workbenchTransport);
			oViewInterfaceModel.setProperty("/packageSelected", oResponseObject.package);

			return oResponseObject;
		});

		return responsePromise;
	};

	ProxyEnhancementWizardController.resetWizard = function (){
		let oCurrentView = this.getView();
		let oProxyWizard = oCurrentView.byId("proxyEnhancementWizard");
		let oProxyModel = oCurrentView.getModel("proxyEnhancementModel");
		let arrTempAttributeData= oProxyModel.getProperty("/tempAttributeData");
		let sParSeg = oProxyModel.getProperty("/parSegment");

		oProxyWizard.goToStep(oProxyWizard.getSteps()[0]);
		oProxyWizard.discardProgress(oProxyWizard.getSteps()[0]);
		oProxyWizard.invalidateStep(oProxyWizard.getSteps()[0]);
		let enhancementStep = oCurrentView.byId("createEnhancement");
		enhancementStep.setNextStep(oProxyWizard.getSteps()[3]);
		enhancementStep.addSubsequentStep(oProxyWizard.getSteps()[2], oProxyWizard.getSteps()[3]);
		oCurrentView.removeContent();
		if(sParSeg === "Add"){
			//Step 2
			oProxyModel.setProperty("/enhancementData", {});
			//Step 3
			oProxyModel.setProperty("/elementData", {});
			//Step 4
			oProxyModel.setProperty("/attributeData", []);
		} else{
			oProxyWizard.validateStep(oProxyWizard.getSteps()[0]);
			oProxyWizard.validateStep(oProxyWizard.getSteps()[1]);
			oProxyModel.setProperty("/elementData", {});
			if(arrTempAttributeData){
				oProxyModel.setProperty("/attributeData", arrTempAttributeData);
			}
		}
		oProxyModel.setProperty("/EbSaveButton", false);
		this.validateWizardToSave();

	};

	ProxyEnhancementWizardController.onEnhancementTypeSelect = async function (oEvent){
		let oController = this;
		let oCurrentView = this.getView();
		let iSelectedIndex = oEvent.getParameters().selectedIndex;
		let oProxyModel = oCurrentView.getModel("proxyEnhancementModel");
		let oProxyWizard = oCurrentView.byId("proxyEnhancementWizard");
		let oEnhancementTypeStep = oCurrentView.byId("selectEnhancementTypeStep");
		let oCreateEnhancementStep = oController.byId("createEnhancement");
		let sEnhancementType;

		if(iSelectedIndex===0){
			sEnhancementType= "Element";
				
		} else{
			sEnhancementType= "Attribute";
		}

		if(oProxyWizard.getCurrentStep().split("--")[2]!=="selectEnhancementTypeStep"){
			await new Promise(function(resolve, reject){
					let promiseShowPopupAlert = Utilities.showPopupAlert("Changing the Enhancement Type will reset the progress and all the changes will be lost. Do you want to continue?", 
					MessageBox.Icon.ERROR, "Enhancement Type Change", [MessageBox.Action.OK, MessageBox.Action.CANCEL]);
					promiseShowPopupAlert.then(function (){
						resolve();
						oController.resetWizard();
						return;
					}, function() {
						reject();
						if(sEnhancementType==="Element"){
							oCurrentView.byId("rbAttribute").setSelected(true);
						} else {
							oCurrentView.byId("rbElement").setSelected(true);
						}
					});
			});
			this.validateWizardToSave();

		}	
			
		
		oCreateEnhancementStep.setNextStep(this.getView().byId(`create${sEnhancementType}`));

		oProxyModel.setProperty("/enhancementType", sEnhancementType);
		if(sEnhancementType==="Element"){
			let oElementData = oProxyModel.getProperty("/elementData");
			if(Object.keys(oElementData).length === 0){
				oElementData = this.createElementData();
				this.getView().byId("xsdElement").setSelectedKey("string");
				
			}
			let oABAPSelect = this.getView().byId("xsdElement");
			this.dynamicABAPSelection(oABAPSelect, oElementData);
		}

		// if(sEnhancementType && sParSeg==="Add"){
			oProxyWizard.validateStep(oEnhancementTypeStep);
		// } else {
		// 	oProxyWizard.invalidateStep(oEnhancementTypeStep);
		// }
	};

	ProxyEnhancementWizardController.createElementData = function (){
		let oProxyModel = this.getView().getModel("proxyEnhancementModel");
		let oElementData = oProxyModel.getProperty("/elementData");
		let oElementName = this.getView().byId("ElementName");
		// Bug 12518 - Mapping Details name length limit on Interface
		let sPrefix = oProxyModel.getProperty("/Prefix");
		// End Bug 12518 - Mapping Details name length limit on Interface

		oElementData = {
			Outbimpl: "",
			ElementName: "",
			elementNameState: "Error",
			elementNameStateText: "Element Name must start with Z or Y",
			Action: "I",
			DefaultValue: "",
			MinOccurs: "0",
			MaxOccurs: "",
			maxOccursState: "Error",
			maxOccursStateText: "Please select a value",
			Deletable: "",
			Nillable: "",
			GlobalRefType: "",
			GlobalRefName: "",
			GlobalRefNamespace: "",
			// Bug 12518 - Mapping Details name length limit on Interface
			ElementPrefix: sPrefix,
			// End Bug 12518 - Mapping Details name length limit on Interface
			XSDType: "string",
			ABAPType: "STRG",
			abapTypeState: "None",
			abapTypeStateText: "",
			arrAbapTypes: [],
			Pattern: "",
			EbPattern: "",
			Length: "",
			EbLength: "",
			lengthState: "None",
			lengthStateText: "",
			MaxLength: "",
			EbMaxLength: "",
			MaxLengthState: "None",
			MaxLengthStateText: "",
			MinLength: "",
			MinLengthState: "None",
			MinLengthStateText: "",
			EbMinLength: "",
			MinIncl: "",
			MinInclState: "None",
			MinInclStateText: "",
			EbMinIncl: "",
			MaxIncl: "",
			MaxInclState: "None",
			MaxInclStateText: "",
			EbMaxIncl: "",
			MinExcl: "",
			MinExclState: "None",
			MinExclStateText: "",
			EbMinExcl: "",
			MaxExcl: "",
			MaxExclState: "None",
			MaxExclStateText: "",
			EbMaxExcl: "",
			TotalDigits: "",
			EbTotalDigits: "",
			FractionDigits: "",
			EbFractionDigits: "",
			fractionDigitsState: "None",
			fractionDigitsStateText: ""
		};
		oProxyModel.setProperty("/elementData", oElementData);
		// Bug 12518 - Mapping Details name length limit on Interface
		oElementName.setMaxLength(30-oElementData.ElementPrefix.length);
		// End Bug 12518 - Mapping Details name length limit on Interface
		oProxyModel.setProperty("/attributeData", []);
		return oElementData;
	};

	ProxyEnhancementWizardController.onEnhanceNameLiveChange = function (oEvent){
		let input = oEvent.getSource();
		let oProxyModel = this.getView().getModel("proxyEnhancementModel");
		let oEnhanceCreateWizardStep = this.getView().byId("createEnhancement");
		let oProxyWizard = this.getView().byId("proxyEnhancementWizard");
		let arrEnhancements = oProxyModel.getProperty("/arrEnhancements");
		// Bug 12518 - Mapping Details name length limit on Interface
		let sPrefix = oProxyModel.getProperty("/Prefix");
		// End Bug 12518 - Mapping Details name length limit on Interface
	
		input.setValue(input.getValue().toUpperCase());
		let sInput = input.getValue();

		let oMatchedEnhancement = arrEnhancements.find((oEnhancement)=>{
		// Bug 12518 - Mapping Details name length limit on Interface
			let sEnhancementName = oEnhancement.Extensioname.split(sPrefix)[1];
		// End Bug 12518 - Mapping Details name length limit on Interface
			return sEnhancementName === sInput;
		});
		
		let firstLetter = sInput.slice(0, 1);
		if(firstLetter === "Z" || firstLetter === "Y"){

			if(!/^[A-Z0-9/_]*$/.test(input.getValue().toUpperCase())) {
				oProxyModel.setProperty("/enhancementData/enhancementNameState", sap.ui.core.ValueState.Error);
				oProxyModel.setProperty("/enhancementData/enhancementNameStateText", "Enhancement Name is of invalid format");
				oProxyWizard.invalidateStep(oEnhanceCreateWizardStep);

			} else if(oMatchedEnhancement){
				oProxyModel.setProperty("/enhancementData/enhancementNameState", sap.ui.core.ValueState.Error);
				oProxyModel.setProperty("/enhancementData/enhancementNameStateText", "Enhancement Name already exists");
				oProxyWizard.invalidateStep(oEnhanceCreateWizardStep);

			} else{
				if (oProxyModel.getProperty("/enhancementData/enhancementNameState") === sap.ui.core.ValueState.Error) {
					oProxyModel.setProperty("/enhancementData/enhancementNameState", sap.ui.core.ValueState.None);
					oProxyModel.setProperty("/enhancementData/enhancementNameStateText", "");
				}
				oProxyWizard.validateStep(oEnhanceCreateWizardStep);	
			}
		}else{
			oProxyModel.setProperty("/enhancementData/enhancementNameState", sap.ui.core.ValueState.Error);
			oProxyModel.setProperty("/enhancementData/enhancementNameStateText", "Enhancement Name must start with Z or Y");
			oProxyWizard.invalidateStep(oEnhanceCreateWizardStep);
		}
		this.validateWizardToSave();

	};


	ProxyEnhancementWizardController.onAttrNameLiveChange = function(oEvent){
		let oInput = oEvent.getSource(); 
		let sInput = oInput.getValue().toUpperCase();
		let oProxyModel = this.getView().getModel("proxyEnhancementModel");
		let sPrefix = oProxyModel.getProperty("/Prefix");
		let sChangeRowPath = oEvent.getSource().getBindingContext("proxyEnhancementModel").getPath();
		let oCurrentRow = oProxyModel.getProperty(sChangeRowPath);
		let sEnhancementName = oProxyModel.getProperty("/enhancementData/sEnhancementName");
		let arrAttrData = oProxyModel.getProperty("/attributeData");
		let bDuplicate;
		
		// Reset the error status
		oCurrentRow.attrNameValueState = sap.ui.core.ValueState.None;
		oCurrentRow.attrNameValueStateText = "";

		oInput.setValue(sInput);
		if(sInput && arrAttrData.length>1){
			for(let i = 0; i<arrAttrData.length;i++){
				if(sInput === arrAttrData[i].AttrName && Number(sChangeRowPath.split("/")[2])!==i){
					bDuplicate=true;
					i=arrAttrData.length;
				} else{
					bDuplicate=false;
				}
			}
		}
		let firstLetter = sInput.slice(0, 1);
		if(firstLetter !== "Z" && firstLetter !== "Y"){
			oCurrentRow.attrNameValueState = sap.ui.core.ValueState.Error;
			oCurrentRow.attrNameValueStateText = "Enhancement Name must start with Z or Y";
		} else if(!/^[A-Z0-9/_]*$/.test(sInput)) {
			oCurrentRow.attrNameValueState=sap.ui.core.ValueState.Error;
			oCurrentRow.attrNameValueStateText = "Attribute Name has an invalid format";
		} else if(sInput === sEnhancementName){
			oCurrentRow.attrNameValueState = sap.ui.core.ValueState.Error;
			oCurrentRow.attrNameValueStateText = "Attribute Name can't be the same as Enhancement Name";
		} else if(sInput === ""){
			oCurrentRow.attrNameValueState = sap.ui.core.ValueState.Error;
			oCurrentRow.attrNameValueStateText = "Attribute Name is Mandatory";
		} else if(bDuplicate){
			oCurrentRow.attrNameValueState = sap.ui.core.ValueState.Error;
			oCurrentRow.attrNameValueStateText = "Attribute Name duplicate";
		} else{
			// Access the global rdgModel
			let ModelCheck = RDGModels.getRDGGlobalModel().getProperty("/InterfacesModelCheck/oData/children");
			let sFlag = false;
			// Bug 13596: look for the same name on the fieldname property
			sFlag = this.checkFieldNames(ModelCheck, "A", sPrefix, sInput, undefined);

			if(sFlag === true){
				oCurrentRow.attrNameValueState = sap.ui.core.ValueState.Error;
				oCurrentRow.attrNameValueStateText = "An Attribute with the same name already exists";
			}
		}

		oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
		this.validateWizardToSave();
	};

	ProxyEnhancementWizardController.onMinMaxLiveChange = function(oEvent){
		this.inputNumberValidation(oEvent);
		let sInput = oEvent.getSource().getValue();
		let oProxyModel = this.getView().getModel("proxyEnhancementModel");
		let sFullId = oEvent.getSource().getId();
		let sType = sFullId.split("-")[7];
		let sChangeRowPath;
		let oABAPSelect;
		if(sType==="Attribute"){
			sChangeRowPath = oEvent.getSource().getBindingContext("proxyEnhancementModel").getPath();
			oABAPSelect = oEvent.getSource().getParent().getCells()[4];
		} else{
			sChangeRowPath = "/elementData";
			oABAPSelect = this.getView().byId("elAbapType");
		}
		let oCurrentRow = oProxyModel.getProperty(sChangeRowPath);
		let sId = sFullId.split("-")[6];
		let sIdMin;
		let sIdMax; 
		let sMin;
		let sMax; 

		if(!sId.search("Min")){
			sIdMin = sId;
			sMin = sInput;
			sIdMax = sId.replace("Min", "Max");
			sMax = oCurrentRow[sIdMax];
		} else{
			sIdMin = sId.replace("Max", "Min");
			sMin = oCurrentRow[sIdMin];
			sIdMax = sId;
			sMax = sInput;
		}

		// Bug 12531 - Validate min and max values for inclusion and exclusion in proxy
		// if either min or max are provided and the id has a "Length"
		if(sId.search("Length")>0){
			if(sMin||sMax){
				oCurrentRow.Length = "";
				oCurrentRow.EbLength = "";

				if(sId ==="MaxLength"){
					oCurrentRow.MaxLength = sMax;
					oABAPSelect.setSelectedKey();
					this.dynamicABAPSelection(oABAPSelect, oCurrentRow, sChangeRowPath);
				}
			} else{
				oCurrentRow.EbLength = "X";
			}
		}

		// if Min value exists but Max value doesn't and it does not have the word "Length" on the id 
		if(sMin && !sMax && sId.search("Length")<0){
			oCurrentRow[`${sIdMax}State`] = sap.ui.core.ValueState.Error;
			oCurrentRow[`${sIdMax}StateText`] = `Max ${sIdMax.split("Max")[1]} is mandatory if Min ${sIdMin.split("Min")[1]} is provided`;
			oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
		} else if(sMax && !sMin && sId.search("Length")<0){
			oCurrentRow[`${sIdMin}State`] = sap.ui.core.ValueState.Error;
			oCurrentRow[`${sIdMin}StateText`] = `Min ${sIdMin.split("Min")[1]} is mandatory if Max ${sIdMax.split("Max")[1]} is provided`;
			oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
		} else if(Number(sMin)>Number(sMax)){
			if(sId === sIdMin){
				oCurrentRow[`${sIdMin}State`] = sap.ui.core.ValueState.Error;
				oCurrentRow[`${sIdMin}StateText`] = `Min ${sIdMin.split("Min")[1]} can't be greater than Max ${sIdMax.split("Max")[1]}`;
				oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
			} else {
				oCurrentRow[`${sIdMax}State`] = sap.ui.core.ValueState.Error;
				oCurrentRow[`${sIdMax}StateText`] = `Max ${sIdMax.split("Max")[1]} can't be less than Min ${sIdMin.split("Min")[1]}`;
				oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
			}
		}else if(Number(sMax)<Number(sMin)){
			if(sId === sIdMin){
				oCurrentRow[`${sIdMin}State`] = sap.ui.core.ValueState.Error;
				oCurrentRow[`${sIdMin}StateText`] = `Min ${sIdMin.split("Min")[1]} can't be greater than Max ${sIdMax.split("Max")[1]}`;
				oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
			} else{
				oCurrentRow[`${sIdMax}State`] = sap.ui.core.ValueState.Error;
				oCurrentRow[`${sIdMax}StateText`] = `Max ${sIdMax.split("Max")[1]} can't be less than Min ${sIdMin.split("Min")[1]}`;
				oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
			}
			// End Bug 12531 - Validate min and max values for inclusion and exclusion in proxy
		} else {
			if (oCurrentRow[`${sIdMin}State`] === sap.ui.core.ValueState.Error) {
				oCurrentRow[`${sIdMin}State`] = sap.ui.core.ValueState.None;
				oCurrentRow[`${sIdMin}StateText`] = "";
				oCurrentRow.lengthState = "None";
				oCurrentRow.lengthStateText = "";
				oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
			} else if(oCurrentRow[`${sIdMax}State`] === sap.ui.core.ValueState.Error){
				oCurrentRow[`${sIdMax}State`] = sap.ui.core.ValueState.None;
				oCurrentRow[`${sIdMax}StateText`] = "";
				oCurrentRow.lengthState = "None";
				oCurrentRow.lengthStateText = "";
				oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
			}
		}
		
		if(sType ==="Element"){
			this.elementStepValidation(oCurrentRow);
		}
		this.validateWizardToSave();
	};

	ProxyEnhancementWizardController.onTotalDigitsLiveChange = function(oEvent){
		this.inputNumberValidation(oEvent);
		let oProxyModel = this.getView().getModel("proxyEnhancementModel");
		let sType = oEvent.getSource().getId().split("-")[7];
		let sChangeRowPath;
		let oABAPSelect;

		if(sType==="Attribute"){
			sChangeRowPath = oEvent.getSource().getBindingContext("proxyEnhancementModel").getPath();
			oABAPSelect = oEvent.getSource().getParent().getCells()[4];
		} else{
			sChangeRowPath = "/elementData";
			oABAPSelect = this.getView().byId("elAbapType");
		}

		let oCurrentRow = oProxyModel.getProperty(sChangeRowPath);
		oCurrentRow.TotalDigits = oEvent.getSource().getValue();
		oCurrentRow.fractionDigitsState = sap.ui.core.ValueState.None;
		oCurrentRow.fractionDigitsStateText = "";
		if(!oCurrentRow.TotalDigits){
			oCurrentRow.FractionDigits ="";
		}
		oABAPSelect.setSelectedKey();
		this.dynamicABAPSelection(oABAPSelect, oCurrentRow, sChangeRowPath);

		if(sType ==="Element"){
			this.elementStepValidation(oCurrentRow);
		}
		this.validateWizardToSave();
	};

	ProxyEnhancementWizardController.onFractionDigitsLiveChange = function(oEvent){
		this.inputNumberValidation(oEvent);
		let oProxyModel = this.getView().getModel("proxyEnhancementModel");
		let sType = oEvent.getSource().getId().split("-")[7];
		let sChangeRowPath;
		let oABAPSelect;

		if(sType==="Attribute"){
			sChangeRowPath = oEvent.getSource().getBindingContext("proxyEnhancementModel").getPath();
			oABAPSelect = oEvent.getSource().getParent().getCells()[4];
		} else{
			sChangeRowPath = "/elementData";
			oABAPSelect = this.getView().byId("elAbapType");
		}
		let oCurrentRow = oProxyModel.getProperty(sChangeRowPath);
		let nFractionDigit = Number(oEvent.getSource().getValue());

		if(oCurrentRow.TotalDigits){
			if(nFractionDigit > 2){
				oCurrentRow.fractionDigitsState = sap.ui.core.ValueState.Warning;
				oCurrentRow.fractionDigitsStateText = "Fraction Digits can't be greater than 2";
				oCurrentRow.FractionDigits ="";
				oEvent.getSource().setValue("");
				oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
				return;
			} else {
				if (oCurrentRow.fractionDigitsState === sap.ui.core.ValueState.Warning) {
				oCurrentRow.fractionDigitsState = sap.ui.core.ValueState.None;
				oCurrentRow.fractionDigitsStateText = "";
			}	
				oCurrentRow.FractionDigits = oEvent.getSource().getValue();
				oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
				oABAPSelect.setSelectedKey();

				this.dynamicABAPSelection(oABAPSelect, oCurrentRow, sChangeRowPath);
			}
		}else{
			oCurrentRow.fractionDigitsState = sap.ui.core.ValueState.Warning;
			oCurrentRow.fractionDigitsStateText = "Can't add 'Fraction Digits' if 'Total Digits' is empty";
			oCurrentRow.FractionDigits ="";
			oEvent.getSource().setValue("");
			oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
			return;
		}

		if(sType ==="Element"){
			this.elementStepValidation(oCurrentRow);
		}
		this.validateWizardToSave();
	};


	ProxyEnhancementWizardController.inputNumberValidation = function(oEvent){
		let regexNum = "^[0-9]+$";
		let oInput = oEvent.getSource();
		let sValue = oInput.getValue(); 

		if (sValue.match(regexNum) || sValue.length === 0) {
			oInput.setValueState("None");
			oInput.setValueStateText("");
		} else {
			oInput.setValueState("Error");
			oInput.setValueStateText("onlyNumbers");
		}
	};

	ProxyEnhancementWizardController.resetOnXSDTypeChange = function(oABAPSelect, oCurrentRow, sChangeRowPath, resetFields=false, sPattern){
		let oProxyModel = this.getView().getModel("proxyEnhancementModel");
		let arrAbapData = oProxyModel.getProperty("/abapData");
		let sAbapSelectId = oABAPSelect.getId().split("-")[6];

		
		let oMatchedXSDType = arrAbapData.find((oXSD)=>{
			return oXSD.XSDType === oCurrentRow.XSDType;
		});

		if(oMatchedXSDType){
			oCurrentRow.EbPattern = oMatchedXSDType.Pattern;
			oCurrentRow.EbLength = oMatchedXSDType.Length;
			oCurrentRow.EbMinLength = oMatchedXSDType.MinLength;
			oCurrentRow.EbMaxLength = oMatchedXSDType.MaxLength;
			oCurrentRow.EbMinIncl = oMatchedXSDType.MinIncl;
			oCurrentRow.EbMaxIncl = oMatchedXSDType.MaxIncl;
			oCurrentRow.EbMinExcl = oMatchedXSDType.MinExcl;
			oCurrentRow.EbMaxExcl = oMatchedXSDType.MaxExcl;
			oCurrentRow.EbTotalDigits = oMatchedXSDType.TotalDigits;
			oCurrentRow.EbFractionDigits = oMatchedXSDType.FractionDigits;
			
			if(resetFields===false){
				oCurrentRow.Pattern = sPattern ? sPattern : "";
				oCurrentRow.Length = "";
				oCurrentRow.MaxLength = "";
				oCurrentRow.MinLength = "";
				oCurrentRow.MinIncl = "";
				oCurrentRow.MinInclState = "None";
				oCurrentRow.MinInclStateText = "";
				oCurrentRow.MaxIncl = "";
				oCurrentRow.MinInclState = "None";
				oCurrentRow.MinInclStateText = "";
				oCurrentRow.MinExcl = "";
				oCurrentRow.MinExclState = "None";
				oCurrentRow.MinExclStateText = "";
				oCurrentRow.MaxExcl = "";
				oCurrentRow.MaxExclState = "None";
				oCurrentRow.MaxExclStateText = "";
				oCurrentRow.TotalDigits = "";
				oCurrentRow.FractionDigits = "";
				oCurrentRow.fractionDigitsState = "None";
				oCurrentRow.fractionDigitsStateText = "";
			}

			if(oCurrentRow.XSDType==="NMTOKENS"||oCurrentRow.XSDType==="IDREF"){
				oCurrentRow.MaxLengthState = "Error";
				oCurrentRow.MaxLengthStateText = "Length or Min/Max Length is Mandatory";
				oCurrentRow.MinLengthState = "Error";
				oCurrentRow.MinLengthStateText = "Length or Min/Max Length is Mandatory";
				oCurrentRow.lengthState = "Error";
				oCurrentRow.lengthStateText = "Length or Min/Max Length is Mandatory";
			} else {
				oCurrentRow.MaxLengthState = "None";
				oCurrentRow.MaxLengthStateText = "";
				oCurrentRow.MinLengthState = "None";
				oCurrentRow.MinLengthStateText = "";
				oCurrentRow.lengthState = "None";
				oCurrentRow.lengthStateText = "";
			}
			oCurrentRow.arrAbapTypes = oMatchedXSDType.XSDTOABAPNAV.results;
			if(sAbapSelectId==="attrAbapType"){
				oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
			} else{
				oProxyModel.setProperty("/elementData", oCurrentRow);
			}
		}
		this.validateWizardToSave();

	};

	ProxyEnhancementWizardController.onChangeXSDType = function(oEvent){
		let oProxyModel = this.getView().getModel("proxyEnhancementModel");
		let oABAPSelect;
		let sChangeRowPath;
		if(oEvent.getSource().getId().includes("xsdAttribute")){
			oABAPSelect = oEvent.getSource().getParent().getCells()[4];
			sChangeRowPath = oEvent.getSource().getBindingContext("proxyEnhancementModel").getPath();
		} else {
			oABAPSelect = this.getView().byId("elAbapType");
			sChangeRowPath = "/elementData";
		}
		let oCurrentRow = oProxyModel.getProperty(sChangeRowPath);

		this.resetOnXSDTypeChange(oABAPSelect, oCurrentRow, sChangeRowPath);
		oABAPSelect.setSelectedKey();
		this.abapTypeValidation(oABAPSelect, oCurrentRow, sChangeRowPath);

		if(sChangeRowPath==="/elementData"){
			this.elementStepValidation(oCurrentRow);
		}
		this.validateWizardToSave();

	};

	ProxyEnhancementWizardController.filterAttributeData = function(oEvent){
		let oController = this;
		let oProxyModel = oController.getView().getModel("proxyEnhancementModel");
		let sChangeRowPath = oEvent.getSource().getBindingContext("proxyEnhancementModel").getPath();
		let oCurrentRow = oProxyModel.getProperty(sChangeRowPath);
		let sValue = oEvent.getSource().getValue(); 
		let sFullId = oEvent.getSource().getId();
		let sId = sFullId.split("-")[6];
		let oABAPSelect = oEvent.getSource().getParent().getCells()[4];

		for (let key in oCurrentRow){
			if(key === sId){
				oCurrentRow[key] = sValue;
			}
		}
		oABAPSelect.setSelectedKey();
		oController.dynamicABAPSelection(oABAPSelect, oCurrentRow, sChangeRowPath);
		this.validateWizardToSave();
	};

	ProxyEnhancementWizardController.onElementNameChange = function(oEvent){
		let oController = this;
		let oProxyModel = oController.getView().getModel("proxyEnhancementModel");
		let sEnhancementName = oProxyModel.getProperty("/enhancementData/sEnhancementName");
		let sPrefix = oProxyModel.getProperty("/Prefix");
		let sChangeRowPath = "/elementData";
		let oCurrentRow = oProxyModel.getProperty(sChangeRowPath);
		let oValue = oEvent.getSource();
		let sValue = oValue.getValue().toUpperCase();
		// Bug 13596: create a variable that contains the full element name 
		let sFullElementName = sPrefix + sValue;
		// Access the global rdgModel
		let ModelCheck = RDGModels.getRDGGlobalModel().getProperty("/InterfacesModelCheck/oData/children");
		let sFlag = false;
		// Bug 13596: look for the same name on the segment property
		sFlag = this.checkFieldNames(ModelCheck, "E", sPrefix, sValue, undefined);

		oValue.setValue(sValue);
		let firstLetter = sValue.slice(0, 1);
		if(firstLetter !== "Z" && firstLetter !== "Y"){
			oProxyModel.setProperty("/elementData/elementNameState", sap.ui.core.ValueState.Error);
			oProxyModel.setProperty("/elementData/elementNameStateText", "Element Name must start with Z or Y");
			oController.elementStepValidation(oCurrentRow);

		} else if(!/^[A-Z0-9/_]*$/.test(sValue)) {
			oProxyModel.setProperty("/elementData/elementNameState", sap.ui.core.ValueState.Error);
			oProxyModel.setProperty("/elementData/elementNameStateText", "Element Name is of invalid format");
			oController.elementStepValidation(oCurrentRow);

		} else if(sValue === sEnhancementName){
			oProxyModel.setProperty("/elementData/elementNameState", sap.ui.core.ValueState.Error);
			oProxyModel.setProperty("/elementData/elementNameStateText", "Element Name can't be the same as Enhancement Name");
			oController.elementStepValidation(oCurrentRow);

		}else if(sFlag === true){
			oProxyModel.setProperty("/elementData/elementNameState", sap.ui.core.ValueState.Error);
			oProxyModel.setProperty("/elementData/elementNameStateText", "Name already exists");
			oController.elementStepValidation(oCurrentRow);

		} else if (oProxyModel.getProperty("/elementData/elementNameState") === sap.ui.core.ValueState.Error) {
			oProxyModel.setProperty("/elementData/elementNameState", sap.ui.core.ValueState.None);
			oProxyModel.setProperty("/elementData/elementNameStateText", "");
			oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
			oController.elementStepValidation(oCurrentRow);
		}
		
		oCurrentRow.ElementPrefix = sFullElementName;
		oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
		this.validateWizardToSave();

	};

	// Bug 13596: added new sField Parameter to look for an specific property
	ProxyEnhancementWizardController.checkFieldNames = function(children, sType, sPrefix, sVal, sParentType = undefined) {
		let sFieldToCheck = "Segment";
		if(sType === "A") { // Check Attribute 
			sFieldToCheck = "Fieldname";
		}

		// Iterate through the array of children
		if (Array.isArray(children)) {
			for (let i = 0; i < children.length; i++) {
				let child = children[i];
				let sValToCheck = sVal; 

				// Bug 13596: select child with the property that is passed on sField
				// Compare Field
				if((sParentType === "X" && sType === "A") || sType === "E") {  // if parent type = Enhancement, prefix the attribute name before compare 
					sValToCheck = sPrefix + sVal;
				}

				if (child[sFieldToCheck] === sValToCheck) {
					return true;
				}

				// Check if there are nested children and recurse if they exist
				if (child.children && Array.isArray(child.children)) {
					// Bug 13596: select child with the property that is passed on sField
					let sFlag = this.checkFieldNames(child.children, sType, sPrefix, sVal, child.Proxyenh);
					if( sFlag === true){
						return true;
					}
				}
			}
		}
	};
		


	ProxyEnhancementWizardController.onMaxOccursChange = async function(oEvent){
		let oController = this;
		let oProxyModel = oController.getView().getModel("proxyEnhancementModel");
		let sChangeRowPath = "/elementData";
		let oCurrentRow = oProxyModel.getProperty(sChangeRowPath);
		// Bug 12484 - Max occurs eq Unbounded is not working for Proxy extension element
		let sMaxOccursSelection = oEvent.getSource().getSelectedKey();
		// End Bug 12484 - Max occurs eq Unbounded is not working for Proxy extension element

		// Bug 12484 - Max occurs eq Unbounded is not working for Proxy extension element
		if(sMaxOccursSelection){
			// End Bug 12484 - Max occurs eq Unbounded is not working for Proxy extension element
			if(oCurrentRow.maxOccursState === sap.ui.core.ValueState.Error){
				oCurrentRow.maxOccursState = sap.ui.core.ValueState.None;
				oCurrentRow.maxOccursStateText = "";
			}
			// Bug 12484 - Max occurs eq Unbounded is not working for Proxy extension element
			oCurrentRow.maxOccurs = sMaxOccursSelection;
			// End Bug 12484 - Max occurs eq Unbounded is not working for Proxy extension element
			oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
		} else{
			oCurrentRow.maxOccursState = sap.ui.core.ValueState.Error;
			oCurrentRow.maxOccursStateText = "Please select a value";
			oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
		}

		this.elementStepValidation(oCurrentRow);
		this.validateWizardToSave();
	};

	ProxyEnhancementWizardController.elementStepValidation = function (oCurrentRow){
		let oProxyWizard = this.getView().byId("proxyEnhancementWizard");
		let oCreateElementStep = this.getView().byId("createElement");
			// Bug 12484 - Max occurs eq Unbounded is not working for Proxy extension element
		if(oCurrentRow.ElementName && oCurrentRow.XSDType && oCurrentRow.ABAPType && oCurrentRow.MaxOccurs && oCurrentRow.elementNameState==="None" &&
			// End Bug 12484 - Max occurs eq Unbounded is not working for Proxy extension element
			oCurrentRow.abapTypeState==="None" && oCurrentRow.lengthState==="None" && oCurrentRow.MaxLengthState==="None" && oCurrentRow.MinLengthState==="None"
			&& oCurrentRow.MaxInclState==="None" && oCurrentRow.MinInclState==="None" && oCurrentRow.MaxExclState==="None" && oCurrentRow.MinExclState==="None" 
			&& oCurrentRow.fractionDigitsState==="None" ){
				if(oCreateElementStep.getValidated()===false){
					oProxyWizard.validateStep(oCreateElementStep);
				}
		}else{
			oProxyWizard.invalidateStep(oCreateElementStep);
		}
	};

	ProxyEnhancementWizardController.filterElementData = function(oEvent){
		let oController = this;
		let oProxyModel = oController.getView().getModel("proxyEnhancementModel");
		let sChangeRowPath = "/elementData";
		let oCurrentRow = oProxyModel.getProperty(sChangeRowPath);
		let sValue = oEvent.getSource().getValue(); 
		let sFullId = oEvent.getSource().getId();
		let sId = sFullId.split("-")[6];
		let oABAPSelect = this.getView().byId("elAbapType");
		
		for (let key in oCurrentRow){
			if(key === sId){
				oCurrentRow[key] = sValue;
			}
		}
		oABAPSelect.setSelectedKey();
		oController.dynamicABAPSelection(oABAPSelect, oCurrentRow, sChangeRowPath);
		oController.elementStepValidation(oCurrentRow);
		oController.validateWizardToSave();

	};

	ProxyEnhancementWizardController.dynamicABAPSelection = function(oABAPSelect, oCurrentRow, sChangeRowPath){
		let oController = this;
		let oProxyModel = oController.getView().getModel("proxyEnhancementModel");

		let promiseAbapData;

			if(oCurrentRow.Length){

				oCurrentRow.MaxLength = "";
				oCurrentRow.EbMaxLength = "";
				oCurrentRow.MinLength = "";
				oCurrentRow.EbMinLength = "";
				oCurrentRow.MaxLengthState = "None";
				oCurrentRow.MaxLengthStateText = "";
				oCurrentRow.MinLengthState = "None";
				oCurrentRow.MinLengthStateText = "";
				oCurrentRow.lengthState = "None";
				oCurrentRow.lengthStateText = "";
				promiseAbapData = GetListsData.getABAPLengthData(oController, oCurrentRow.XSDType, oCurrentRow.Length); 

			} else if(oCurrentRow.MaxLength){
				oCurrentRow.Length = "";
				oCurrentRow.EbLength = "";
				promiseAbapData = GetListsData.getABAPMaxLengthData(oController, oCurrentRow.XSDType, oCurrentRow.MaxLength); 

			} else if(oCurrentRow.TotalDigits && oCurrentRow.FractionDigits){

				promiseAbapData = GetListsData.getABAPTotalFractionDigitsData(oController, oCurrentRow.XSDType, oCurrentRow.TotalDigits, oCurrentRow.FractionDigits); 

			} else if(oCurrentRow.TotalDigits){

				promiseAbapData = GetListsData.getABAPTotalDigitsData(oController, oCurrentRow.XSDType, oCurrentRow.TotalDigits); 

			} 

		if(promiseAbapData){
			promiseAbapData.then(function(arrABAPData){
				oCurrentRow.arrAbapTypes = arrABAPData[0].XSDTOABAPNAV.results;
				oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
				if(oCurrentRow.ABAPType){
					oABAPSelect.setSelectedKey(oCurrentRow.ABAPType);
				} else{
					oABAPSelect.setSelectedKey();
				}
			});
		} else{
			this.resetOnXSDTypeChange(oABAPSelect, oCurrentRow, sChangeRowPath, true);
		}
		oController.abapTypeValidation(oABAPSelect, oCurrentRow, sChangeRowPath);
		this.validateWizardToSave();
	};

	ProxyEnhancementWizardController.onABAPTypeChange = function(oEvent){
		let oProxyModel = this.getView().getModel("proxyEnhancementModel");
		let oABAPSelect = oEvent.getSource();
		let sAbapSelectId = oABAPSelect.getId().split("-")[6];
		let sChangeRowPath;
		if(sAbapSelectId==="attrAbapType"){
			sChangeRowPath = oEvent.getSource().getBindingContext("proxyEnhancementModel").getPath();
		} else{
			sChangeRowPath = "/elementData";
		}
		let oCurrentRow = oProxyModel.getProperty(sChangeRowPath);
		this.abapTypeValidation(oABAPSelect, oCurrentRow, sChangeRowPath);

		if(sAbapSelectId==="elAbapType"){
		this.elementStepValidation(oCurrentRow);
		}
		this.validateWizardToSave();

	};

	ProxyEnhancementWizardController.abapTypeValidation = function(oABAPSelect, oCurrentRow, sChangeRowPath){
		let oProxyModel = this.getView().getModel("proxyEnhancementModel");

		if(oABAPSelect.getSelectedKey()){
			if(oCurrentRow.abapTypeState === sap.ui.core.ValueState.Error){
				oCurrentRow.abapTypeState = sap.ui.core.ValueState.None;
				oCurrentRow.abapTypeStateText = "";
				oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
			}
		} else{
			oCurrentRow.abapTypeState = sap.ui.core.ValueState.Error;
			oCurrentRow.abapTypeStateText = "Please select an ABAP Type";
			oProxyModel.setProperty(sChangeRowPath, oCurrentRow);
		}
		
	};

	ProxyEnhancementWizardController.onAddAttribute = function() {
		let oProxyModel = this.getView().getModel("proxyEnhancementModel");
		let oAttributeData = oProxyModel.getProperty("/attributeData");
		let arrAbapData = oProxyModel.getProperty("/abapData");

		if(!oAttributeData) {
			oAttributeData = [];
		}
		let oAttrData = {
			insert: true,
			EbName: true,
			AttrName: "",
			attrNameValueState: "Error",
			attrNameValueStateText: "Attribute Name is mandatory",
			AttrDelete: "I",
			DefaultValue: "",
			Optional: true,
			XSDType: "string",
			ABAPType: "STRG",
			abapTypeState: "None",
			abapTypeStateText: "",
			Pattern: "",
			EbPattern: "",
			Length: "",
			EbLength: "",
			lengthState: "None",
			lengthStateText: "",
			MaxLength: "",
			EbMaxLength: "",
			MaxLengthState: "None",
			MaxLengthStateText: "",
			MinLength: "",
			MinLengthState: "None",
			MinLengthStateText: "",
			EbMinLength: "",
			MinIncl: "",
			MinInclState: "None",
			MinInclStateText: "",
			EbMinIncl: "",
			MaxIncl: "",
			MaxInclState: "None",
			MaxInclStateText: "",
			EbMaxIncl: "",
			MinExcl: "",
			MinExclState: "None",
			MinExclStateText: "",
			EbMinExcl: "",
			MaxExcl: "",
			MaxExclState: "None",
			MaxExclStateText: "",
			EbMaxExcl: "",
			TotalDigits: "",
			EbTotalDigits: "",
			FractionDigits: "",
			EbFractionDigits: "",
			fractionDigitsState: "None",
			fractionDigitsStateText: ""
		};
		let oMatchedXSDType = arrAbapData.find((oXSD)=>{
			return oXSD.XSDType === "string";
		});

		if(oMatchedXSDType){
			oAttrData.EbPattern = oMatchedXSDType.Pattern;
			oAttrData.EbLength = oMatchedXSDType.Length;
			oAttrData.EbMinLength = oMatchedXSDType.MinLength;
			oAttrData.EbMaxLength = oMatchedXSDType.MaxLength;
			oAttrData.EbMinIncl = oMatchedXSDType.MinIncl;
			oAttrData.EbMaxIncl = oMatchedXSDType.MaxIncl;
			oAttrData.EbMinExcl = oMatchedXSDType.MinExcl;
			oAttrData.EbMaxExcl = oMatchedXSDType.MaxExcl;
			oAttrData.EbTotalDigits = oMatchedXSDType.TotalDigits;
			oAttrData.EbFractionDigits = oMatchedXSDType.FractionDigits;
		}
		oAttributeData.push(oAttrData);
		oProxyModel.refresh();
		let iLastItem = this.getView().byId("attrTable").getItems().length-1;
		let oLastAbapSelect = this.getView().byId("attrTable").getItems()[iLastItem].getCells()[4];
		let sChangeRowPath = "/attributeData/"+iLastItem;
		this.dynamicABAPSelection(oLastAbapSelect, oAttrData, sChangeRowPath);
		oLastAbapSelect.setSelectedKey("STRG");
		this.abapTypeValidation(oLastAbapSelect, oAttrData, sChangeRowPath);
		this.validateWizardToSave();


	};

	ProxyEnhancementWizardController.onDeleteAttribute = function(oEvent, oController) {
		// let oController = this;
		let oProxyModel = oController.getModel("proxyEnhancementModel");
		let arrAttrData = oProxyModel.getProperty("/attributeData");
		let oTable = oEvent.getSource();
		let oClickedItem = oEvent.getParameters().listItem;
		let iClickedIndex = oTable.getItems().indexOf(oClickedItem);

		// if(oClickedItem.getCells()[0].getValue() && arrAttrData.length>1){
		// 	for(let i = 0; i<arrAttrData.length;i++){
		// 		if(oClickedItem.getCells()[0].getValue() === arrAttrData[i].AttrName && iClickedIndex!==i){
		// 			arrAttrData[i].attrNameValueState = "None";
		// 			arrAttrData[i].attrNameValueStateText = "None";
		// 			i=arrAttrData.length;
		// 		}
		// 	}
		// }
		
		let oTempAttr = arrAttrData[iClickedIndex];

		if(oTempAttr.insert){
			let promise = new Promise(function (resolve, reject) {
				// Show a message asking the user to confirm deletion. Exit if cancel 
				let promiseShowPopupAlert = Utilities.showPopupAlert("The attribute row will be deleted. Continue?", MessageBox.Icon.WARNING,
					"Delete?", [sap.m.MessageBox.Action.YES, sap.m.MessageBox.Action.NO]);
				promiseShowPopupAlert.then(function () {
					resolve();
				}, function () {
					reject();
				});
			});
			
			/**
			 * Get the index of the row being deleted and remove from the model. 
			 */
			promise.then(function () {
				// Get the index clicked
	
					arrAttrData.splice(iClickedIndex, 1);
					oProxyModel.setProperty("/attributeData", arrAttrData);
					oController.validateWizardToSave();
				});
			} else {
				let promiseShowPopupAlert =
					Utilities.showPopupAlert("Attribute already saved, delete only available from the mapping table", MessageBox.Icon.ERROR, "Unable to delete Attribute");
				promiseShowPopupAlert.then(function () {
				});
				return;
				
			}
	};

	ProxyEnhancementWizardController.validateWizardToSave = function(){
		let oController = this;
		let oProxyModel = oController.getView().getModel("proxyEnhancementModel");
		//Step 1
		let sEnhancementType = oProxyModel.getProperty("/enhancementType");
		//Step 2
		let oEnhancementData = oProxyModel.getProperty("/enhancementData");
		//Step 3
		let oElementData = oProxyModel.getProperty("/elementData");
		//Step 4
		let arrAttributeData = oProxyModel.getProperty("/attributeData");

		let oValidationResult;
		
		if(oEnhancementData.enhancementNameValueState==="Error"){
			oValidationResult = true;
		}
		if(sEnhancementType==="Element"){
			if(oElementData.elementNameState ==="Error"){
				oValidationResult = true;
			}else if(oElementData.maxOccursState ==="Error"){
				oValidationResult = true;
			}else if(oElementData.abapTypeState ==="Error"){
				oValidationResult = true;
			}else if(oElementData.lengthState ==="Error"){
				oValidationResult = true;
			}else if(oElementData.MaxLengthState ==="Error"){
				oValidationResult = true;
			}else if(oElementData.MinLengthState ==="Error"){
				oValidationResult = true;
			}else if(oElementData.MinInclState ==="Error"){
				oValidationResult = true;
			}else if(oElementData.MaxInclState ==="Error"){
				oValidationResult = true;
			}else if(oElementData.MinExclState ==="Error"){
				oValidationResult = true;
			}else if(oElementData.MaxExclState ==="Error"){
				oValidationResult = true;
			}else if(oElementData.fractionDigitsState ==="Error"){
				oValidationResult = true;
			}
		}
		// Bug 12484 - Max occurs eq Unbounded is not working for Proxy extension element
		if(sEnhancementType==="Element" && arrAttributeData.length === 0){
			oValidationResult = true;
		} else if(sEnhancementType==="Attribute" && arrAttributeData.length === 0){
			oValidationResult = true;
		} else {
			for(let arrAttrEl of arrAttributeData){
				if(!arrAttrEl.AttrName || arrAttrEl.attrNameValueState ==="Error"){
					oValidationResult = true;
				} else if(arrAttrEl.lengthState ==="Error"){
					oValidationResult = true;
				} else if(arrAttrEl.MaxLengthState ==="Error"){
					oValidationResult = true;
				} else if(arrAttrEl.MinLengthState ==="Error"){
					oValidationResult = true;
				} else if(arrAttrEl.MinInclState ==="Error"){
					oValidationResult = true;
				} else if(arrAttrEl.MaxInclState ==="Error"){
					oValidationResult = true;
				} else if(arrAttrEl.MinExclState ==="Error"){
					oValidationResult = true;
				} else if(arrAttrEl.MaxExclState ==="Error"){
					oValidationResult = true;
				} else if(arrAttrEl.abapTypeState ==="Error"){
					oValidationResult = true;
				} else if(arrAttrEl.fractionDigitsState ==="Error"){
					oValidationResult = true;
				} 
			}
		}
		if(oValidationResult){
			oProxyModel.setProperty("/EbSaveButton", false);
		} else {
			oProxyModel.setProperty("/EbSaveButton", true);
		}

	};

	ProxyEnhancementWizardController.onProxySave = function (){
		let oController = this;
		let oCurrentView = this.getView();
		let oProxyModel = oController.getView().getModel("proxyEnhancementModel");
		//Step 1
		let sEnhancementType = oProxyModel.getProperty("/enhancementType");
		//Step 2
		let oEnhancementData = oProxyModel.getProperty("/enhancementData");
		//Step 3
		let oElementData = oProxyModel.getProperty("/elementData");
		//Step 4
		let arrAttributeData = oProxyModel.getProperty("/attributeData");
		let arrAttributeProps = [];
		// Selected Interface
		// let arrDeletedAttributes = oProxyModel.getProperty("/deletedAttributes");

		let sParSeg = oProxyModel.getProperty("/parSegment");
		// 	Bug 12518 - Mapping Details name length limit on Interface
		let sOutbImpl = oProxyModel.getProperty("/Outbimpl");
		let sPrefix = oProxyModel.getProperty("/Prefix");
		// END Bug 12518 - Mapping Details name length limit on Interface

		let enhancementAction;
		let elementAction;
		let attributeAction;

		for(let arrAttrEl of arrAttributeData){
			let optionalAttr;
			if(arrAttrEl.Optional===true){
				optionalAttr="X";
			} else{
				optionalAttr="";
			}

			if (sParSeg==="Add"){
				attributeAction = arrAttrEl.AttrDelete;
			} else{
				if(sEnhancementType==="Element"){
					attributeAction = arrAttrEl.insert ? arrAttrEl.AttrDelete : "M";
				} else{
					attributeAction = arrAttrEl.insert ? arrAttrEl.AttrDelete : "" ;
				}
			}
			let arrTempAttributeProps = {
				Outbimpl: "",
				AttrName: arrAttrEl.AttrName,
				DefaultValue: arrAttrEl.DefaultValue,
				AttrDelete: attributeAction,
				Optional: optionalAttr,
				XSDType: arrAttrEl.XSDType,
				ABAPType: arrAttrEl.ABAPType,
				Pattern: arrAttrEl.Pattern,
				Length: arrAttrEl.Length,
				MinLength: arrAttrEl.MinLength,
				MaxLength: arrAttrEl.MaxLength,
				MinIncl: arrAttrEl.MinIncl,
				MaxIncl: arrAttrEl.MaxIncl,
				MinExcl: arrAttrEl.MinExcl,
				MaxExcl: arrAttrEl.MaxExcl,
				TotalDigits: arrAttrEl.TotalDigits,
				FractionDigits: arrAttrEl.FractionDigits
			};
			arrAttributeProps.push(arrTempAttributeProps);
		}

		if (sParSeg==="Add" ){
			enhancementAction="I";
			elementAction="I";
		} else{
			enhancementAction="M";
			if(Object.keys(oElementData).length && oElementData.Action ==="I"){
				elementAction="I";
			} else{
				elementAction="M";
			}
		}

		oController._selectTransportPackage(false, true, true)
		.then(function(oResponseSelection){
			let oData;
			if(sEnhancementType==="Attribute"){
				oData = {
					Outbimpl: sOutbImpl, 
					ExtensionName: oEnhancementData.sEnhancementName,
					// 	Bug 12518 - Mapping Details name length limit on Interface
					Prefix: sPrefix,
					ExtObjName: sPrefix + oEnhancementData.sEnhancementName,
					// END Bug 12518 - Mapping Details name length limit on Interface
					ExtDelete: "",
					Description: oEnhancementData.sEnhancementDescription,
					Package: oResponseSelection.package,
					WbTransport: oResponseSelection.workbenchTransport,
					SRVDETAILSTOATTRIBUTESNAV: arrAttributeProps,
					SRVDETAILSTOMESSAGESNAV: [{
						Outbimpl: "",
						Message: "",
						MessageType: "",
						MsgNo: ""
					}]
				};
			} else if(sEnhancementType==="Element"){
				oData = {
					Outbimpl: sOutbImpl,
					ExtensionName: oEnhancementData.sEnhancementName,
					// Bug 12518 - Mapping Details name length limit on Interface
					Prefix: sPrefix,
					ExtObjName: sPrefix + oEnhancementData.sEnhancementName,
					// END Bug 12518 - Mapping Details name length limit on Interface
					ExtDelete: enhancementAction,
					Description: oEnhancementData.sEnhancementDescription,
					Package: oResponseSelection.package,
					WbTransport: oResponseSelection.workbenchTransport,
					SRVDETAILSTOELEMENTSNAV: [
						{ 
							Outbimpl: "",
							ElementName: oElementData.ElementName,
							ElementPrefix: oElementData.ElementPrefix,
							Action: elementAction,
							DefaultValue: oElementData.DefaultValue,
							MinOccurs: oElementData.MinOccurs,
							MaxOccurs: oElementData.MaxOccurs,
							Deletable: oElementData.Deletable,
							Nillable: oElementData.Nillable,
							GlobalRefType: "",
							GlobalRefName: "",
							GlobalRefNamespace: "",
							XSDType: oElementData.XSDType,
							ABAPType: oElementData.ABAPType,
							Pattern: oElementData.Pattern,
							Length: oElementData.Length,
							MinLength: oElementData.MinLength,
							MaxLength: oElementData.MaxLength,
							MinIncl: oElementData.MinIncl,
							MaxIncl: oElementData.MaxIncl,
							MinExcl: oElementData.MinExcl,
							MaxExcl: oElementData.MaxExcl,
							TotalDigits: oElementData.TotalDigits,
							FractionDigits: oElementData.FractionDigits,
							ELEMENTSTOATTRIBUTESNAV: arrAttributeProps
						}
					],
					SRVDETAILSTOMESSAGESNAV: [{
						Outbimpl: "",
						Message: "",
						MessageType: "",
						MsgNo: ""
					}]
				};
			}

			(new DMRDataService(
				oController,
				"GET_PROXY_DETAILS",
				"/SRVDETAILSSet",
				"saveProxyEnhancement",
				"/", // Root of the received data
				"Save Proxy Enhancement"
			))
			.showBusyIndicator(true)
			.saveData(
				false,
				oData,
				null, {
					success: {
						fCallback: function (oParams, oResponseData) {
							let sError = "";
							let arrMessages = [];
							jQuery.extend(true, arrMessages, oResponseData.SRVDETAILSTOMESSAGESNAV.results);
							// oController.getInterfaceList(oController, oResponseData.Appl, oSelectedInterface.Txtmi);
							arrMessages = arrMessages.map((messageEl)=>{
								return {
									MessageType: messageEl.MessageType,
									MsgNo: messageEl.MsgNo,
									Message: messageEl.Message
								};
							});
							arrMessages.every((oMessage) => {
								if (oMessage.MessageType === "E") {
									//If Error exists in list, set value and break
									sError = "X";
									return false;
								} else {
									return true;
								}
							});
							ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
							if (sError !== "X") {
								// Bug 13082 - change the type to "InterfaceWizard" so it can be a general type between proxy and idoc wizards
								ModelMessages.type = "InterfaceWizard";
								ModelMessages.objectContext = this;
								ModelMessages.history = History.getInstance();
								ModelMessages.router = sap.ui.core.UIComponent.getRouterFor(oController);

								//reset the wizard
								oController.resetWizard();
								oCurrentView.byId("rbAttribute").setSelected(false);
								oCurrentView.byId("rbElement").setSelected(false);
							}
						},									
						oParam: this
					},
					error: function () {
						let promiseShowPopupAlert =
							Utilities.showPopupAlert("Proxy Enhancement could not be saved/updated.", MessageBox.Icon.ERROR, "Proxy Enhancement ERROR");
						promiseShowPopupAlert.then(function () {
						});
					}
				}
			);
		});
	};

	ProxyEnhancementWizardController.onWizardCancel = function (){
		let oCurrentView = this.getView();
		let oController = this;

		let promise = new Promise(function(resolve, reject){
			if (oController.isPageEdit() === true) {
				let promiseShowPopupAlert = Utilities.showPopupAlert("Exiting this page may cause loss of data. Do you want to continue?", 
					MessageBox.Icon.ERROR, "Leave Page?", [MessageBox.Action.OK, MessageBox.Action.CANCEL]);
					promiseShowPopupAlert.then(function (){
						resolve();
					}, function() {
						reject();
					});
			} else {
				resolve();
			}
		});

		promise.then(function(){

			//Implement the back navigation
			let oHistory = History.getInstance();
			let sPreviousHash = oHistory.getPreviousHash();
			
			if (sPreviousHash && sPreviousHash !== "") {
				// We have a previus hash, so browser knows how to go back of a page
				window.history.go(-1);
			} else {
				let arrCurrentRoute = oHistory.aHistory[oHistory.aHistory.length - 1].split("*");
				let oRouter = sap.ui.core.UIComponent.getRouterFor(oController);
				if(arrCurrentRoute[0].includes("ProxyEnhancementWizard")) {
					oRouter.navTo("Interfaces");
				} else {
					// We got here with a deeplink so we don't know how to go back
					// Just return to your homepage or whatever route you want to!
					// Replace HomeRoute with your current route ;)
					oRouter.navTo("TargetHome", {}, true);
				}
			}
			sap.ui.getCore().getEventBus().unsubscribe("rdgChannel", "showMapping", oController.onWizardCancel, this);
			oController.resetWizard();
			oCurrentView.byId("rbAttribute").setSelected(false);
			oCurrentView.byId("rbElement").setSelected(false);
			oCurrentView.byId("rbAttribute").setEnabled(true);
			oCurrentView.byId("rbElement").setEnabled(true);
		});
	};

    return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.Interfaces.ProxyEnhancementWizard", ProxyEnhancementWizardController);
});
