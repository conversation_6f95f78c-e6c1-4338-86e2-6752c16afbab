sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "sap/m/MessageBox",
    "dmr/mdg/supernova/SupernovaFJ/model/GetLists",
    "dmr/mdg/supernova/SupernovaFJ/libs/DataService",
    "dmr/mdg/supernova/SupernovaFJ/model/ModelMessages"
], function(Utilities, MessageBox, GetListsData, DMRDataService, ModelMessages) {
	"use strict";
    let BusinessSystem = {};

    BusinessSystem.onBusSystemChange = function(oEvent){
        let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let sSelectedBusSystem = oEvent.getParameter("value");
		let arrBusSystems = oInterfaceModel.getProperty("/arrBusinessSystem");
		let sSimilarBusinessSystemsString;
			
		let oMatchedBusSystem = arrBusSystems.find((oBusSystem) => {
			return oBusSystem.Bussystem === sSelectedBusSystem;
		});
		let oSimilarBusinessSystems = arrBusSystems.filter((oBusSystem) => {
			return (oBusSystem.Bussystem.toUpperCase() === sSelectedBusSystem.toUpperCase() && oBusSystem.Bussystem !== sSelectedBusSystem);
		});
		
		if(oSimilarBusinessSystems.length > 0 && !oMatchedBusSystem)
		{
			oSimilarBusinessSystems.forEach(sSystem => {
				sSimilarBusinessSystemsString = sSimilarBusinessSystemsString ? sSimilarBusinessSystemsString+", "+sSystem.Bussystem : sSystem.Bussystem; 
			});
			Utilities.showPopupAlert("Business Systems already existing similar to your selection : "+sSimilarBusinessSystemsString,
			MessageBox.Icon.INFORMATION, "Business System");
			}	
		
		if(oMatchedBusSystem) {
			let promiseBusSystemDetails =
				GetListsData.getBusSystemDetails(oController, oMatchedBusSystem.Bussystem);
				
			promiseBusSystemDetails.then((oBussSystem) => {
				oBussSystem.Defstorageserv = oBussSystem.Defstorageserv === "X" ? true : false;
				oBussSystem.Unicodesystem = oBussSystem.Unicodesystem === "X" ? true : false;
				oBussSystem.Disabled = oBussSystem.Disabled === "X" ? true : false;
				oBussSystem.arrBusObject = [];
				
				oBussSystem.BUSYS2BUSOBJNAV.results.sort(function(a, b) { 
					return a.Busobject - b.Busobject;
				});
			
				oBussSystem.BUSYS2BUSOBJNAV.results.forEach((oBusinessObject) => {
					oBusinessObject.BUSOBJ2COMMNAV.results.forEach((oBusinessObjectDetails) => {
						oBusinessObjectDetails.Mdgoutputmode = oBusinessObject.Mdgoutputmode === "" ? "X" : oBusinessObject.Mdgoutputmode;                    
						oBusinessObjectDetails.Sysfilterstat = oBusinessObject.Sysfilterstat === "X" ? true : false;
						oBusinessObjectDetails.Keymap = oBusinessObjectDetails.Keymap === "X" ? true : false;
						oBusinessObjectDetails.Mdcprocessreliable = oBusinessObjectDetails.Mdcprocessreliable === "X" ? true : false;
						oBusinessObjectDetails.Keyharm = oBusinessObjectDetails.Keyharm === "" ? "X" : oBusinessObjectDetails.Keyharm;                    
						oBusinessObjectDetails.Storageloc = oBusinessObjectDetails.Storageloc === "" ? "X" : oBusinessObjectDetails.Storageloc;                    
						oBusinessObjectDetails.Timedependency = oBusinessObjectDetails.Timedependency === "" ? "X" : oBusinessObjectDetails.Timedependency;                    
						oBussSystem.arrBusObject.push(oBusinessObjectDetails);
					});
				});
				oInterfaceModel.setProperty("/editBusSystems", oBussSystem);
			});
		} else {
			oInterfaceModel.setProperty("/editBusSystems", {
				Bussystem: sSelectedBusSystem,
				arrBusObject: []
			});
		}
    };

    BusinessSystem.onLogicalSystemChange = function(oEvent) {
        let oController = this;
        let oInterfaceModel = this.getView().getModel("viewInterface");
        let sSelectedKey = oEvent.getSource().getSelectedKey();
    
        if(sSelectedKey === "Custom"){
            oEvent.getSource().setSelectedKey("");
            if (!oController.LogicalSystemCreateDialog) {
                oController.LogicalSystemCreateDialog =
                    sap.ui.xmlfragment(
                        "LogicalSystemCreate",
                        "dmr.mdg.supernova.SupernovaFJ.view.Interfaces.LogicalSystemCreate",
                        oController);
                oController.getView().addDependent(oController.LogicalSystemCreateDialog);
            }
            
            oInterfaceModel.setProperty("/LogicalSysDetailsFragment", {
                LogicalSys: "",
                Desc: ""
            });
            // Open the dialog
            oController.LogicalSystemCreateDialog.open();
        }
    };

    BusinessSystem.onDeleteBusObject = function(oEvent) {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let oTable = oEvent.getSource();
		let oClickedItem = oEvent.getParameters().listItem;
		
		let promise = new Promise(function (resolve, reject) {
			// Show a message asking the user to confirm deletion. Exit if cancel 
			let promiseShowPopupAlert = Utilities.showPopupAlert("The decision row will be deleted. Continue?", MessageBox.Icon.WARNING,
				"Delete?", [sap.m.MessageBox.Action.YES, sap.m.MessageBox.Action.NO]);
			promiseShowPopupAlert.then(function () {
				resolve();
			}, function () {
				reject();
			});
		});
		
		/**
		 * Get the index of the row being deleted and remove from the model. 
		 */
		promise.then(function () {
			// Get the index clicked
			let iClickedIndex = oTable.getItems().indexOf(oClickedItem);
			let arrBusinessObject = oInterfaceModel.getProperty("/editBusSystems/arrBusObject");
			arrBusinessObject.splice(iClickedIndex, 1);
			oInterfaceModel.setProperty("/editBusSystems/arrBusObject", arrBusinessObject);
		});
	};

    BusinessSystem.onAddBusinessObject = function() {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let sSelectedBusinessSystem = oInterfaceModel.getProperty("/editBusSystems/Bussystem");
		let arrBusinessObject = oInterfaceModel.getProperty("/editBusSystems/arrBusObject");
		if(sSelectedBusinessSystem && arrBusinessObject) {
			// changed push to unshift so BusinessObject goes on top not below bug 11145 
			arrBusinessObject.unshift({Keyharm: "X", Storageloc: "X", Timedependency: "X"});
			oInterfaceModel.refresh();
		}
	};

    BusinessSystem.onChangeBusObject = function(oEvent) {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let arrBusObjectList = oInterfaceModel.getProperty("/editBusSystems/arrBusObject");
		let sSelectedBusObject = oEvent.getSource().getSelectedKey();
				
		let iMatchedRow = arrBusObjectList.findIndex((oBusObject) => {
			return oBusObject.Busobject === sSelectedBusObject;
		});
		
		let promiseMdcTemplateList =
			GetListsData.getMdcTemplateList(oController, sSelectedBusObject);
			
		promiseMdcTemplateList.then((arrTemplateList) => {
			arrBusObjectList[iMatchedRow].arrTemplate = arrTemplateList;
			oInterfaceModel.refresh();
		});
	};

    BusinessSystem.onBusSystemsSave = function() {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let arrBusinessSystems = oInterfaceModel.getProperty("/arrBusinessSystem");
		let oBusinessSystem = oInterfaceModel.getProperty("/editBusSystems");
		let promiseShowPopupAlert;
		let oBlankBusinessObjectType = oBusinessSystem.arrBusObject.find((oBusinessObject)=>{
			return !oBusinessObject.Busobject;
		});
		if(oBlankBusinessObjectType)
		{	
			promiseShowPopupAlert = Utilities.showPopupAlert(
					"Business Object Type Missing",
					MessageBox.Icon.ERROR, "Business Object Error");
				promiseShowPopupAlert.then(function () {
				});
			return;
		}
		let oBlankCommChannelObject = oBusinessSystem.arrBusObject.find((oBusinessObject)=>{
			return !oBusinessObject.Commchannel;
		});
		if(oBlankCommChannelObject)
		{	
			promiseShowPopupAlert = Utilities.showPopupAlert(
					"Please select a communication channel for the Business Object Type " + oBlankCommChannelObject.Busobject,
					MessageBox.Icon.ERROR, "Business Object Error");
				promiseShowPopupAlert.then(function () {
				});
			return;
		}
		
		let oMatchedSystem = arrBusinessSystems.find((oBusSystem) => {
			return oBusSystem.Logsys === oBusinessSystem.Logsys &&  oBusSystem.Bussystem !== oBusinessSystem.Bussystem;
		});
		
		if(oMatchedSystem) {
			promiseShowPopupAlert = Utilities.showPopupAlert(
					"Logical System " + oMatchedSystem.Logsys + " is unique for Business System " + oMatchedSystem.Bussystem + " Please select another Logical System",
					MessageBox.Icon.ERROR, "Logical System Error");
				promiseShowPopupAlert.then(function () {
					oInterfaceModel.setProperty("/editBusSystems/LogSys", undefined);
				});
			return;
		}
		
		this._selectTransportPackage(true, false, false)
		.then(function(oResponseSelection) {
			let oData = {
				Bussystem: oBusinessSystem.Bussystem,
				Logsys: oBusinessSystem.Logsys,
				Rfcdest: oBusinessSystem.Rfcdest,
				Filepath: oBusinessSystem.Filepath,
				Defstorageserv: oBusinessSystem.Defstorageserv === true ? "X" : "",
				Unicodesystem: oBusinessSystem.Unicodesystem === true ? "X" : "",
				Unicodecodepag: oBusinessSystem.Unicodesystem === true ? oBusinessSystem.Unicodecodepag : undefined,
				Disabled: oBusinessSystem.Disabled === true ? "X" : "",
				Transport: oResponseSelection.customizingTransport,
				BUSYS2BUSOBJNAV: [],
				BUSYS2MESSAGENAV: []
			};
			
			if(oBusinessSystem.arrBusObject) {
				oBusinessSystem.arrBusObject.forEach((oBusinessObject) => {
					let oBusObjDetails = {
						Bussystem: oBusinessSystem.Bussystem,
						Sysfilterstat: oBusinessObject.Sysfilterstat === true ? "X" : "",
						Mdgoutputmode: oBusinessObject.Mdgoutputmode === "X" ? "" : oBusinessObject.Mdgoutputmode,
						Busobject: oBusinessObject.Busobject,
						BUSOBJ2COMMNAV: [{
							Bussystem: oBusinessSystem.Bussystem,
							Busobject: oBusinessObject.Busobject,
							Commchannel: oBusinessObject.Commchannel,
							Keyharm: oBusinessObject.Keyharm === "X" ? "" : oBusinessObject.Keyharm,
							Keymap: oBusinessObject.Keymap === true ? "X" : "",
							Storageloc: oBusinessObject.Storageloc === "X" ? "" : oBusinessObject.Storageloc,
							Timedependency: oBusinessObject.Timedependency === "X" ? "" : oBusinessObject.Timedependency,
							Mdctemplatecreate: oBusinessObject.Mdctemplatecreate,
							Mdctemplatechange: oBusinessObject.Mdctemplatechange,
							Mdcprocessreliable: oBusinessObject.Mdcprocessreliable === true ? "X" : ""
						}]
					};
					
					oData.BUSYS2BUSOBJNAV.push(oBusObjDetails);
				});
			}
			
			(new DMRDataService(
				oController,
				"BUSINESS_SYSTEM",
				"/CREATEBUSYSSet",
				"saveBusinessSystem",
				"/", // Root of the received data
				"Save Business System"
			)).saveData(
				false,
				oData,
				null, {
					success: {
						fCallback: function (oParams, oResponseData) {
							let sError = "";
							let arrMessages = [];
							jQuery.extend(true, arrMessages, oResponseData.BUSYS2MESSAGENAV.results);
							arrMessages.every((oMessage) => {
								if(oMessage.MessageType === "E") {
									//If Error exists in list, set value and break
									sError = "X";
									return false;
								} else {
									return true;
								}
							});
							
							ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView())
							.then(function(){
								if(sError === "X") {
									return;
								}

								return GetListsData.getInterfaceSearchHelps(oController);
							})
							.then(function(oSearchResults){
								oInterfaceModel.setProperty("/arrBusinessSystem", oSearchResults[0].SERCHLP2BUSSYSTEMNAV.results);
							})
							.finally(function(){
								oController.BusSystemsDialog.close();
							});
						},
						oParam: this
					},
					error: function () {
						Utilities.showPopupAlert("Business System could not be saved/updated.", MessageBox.Icon.ERROR, "BUSINESS SYSTEM SAVE ERROR");
					}
				});

		});
		
	};

    BusinessSystem.onBusSystemsCancel = function() {
		let oController = this;
		let oInterfaceController = this.getView().getModel("viewInterface");
		oInterfaceController.setProperty("/editBusSystems", {});
		oController.BusSystemsDialog.close();
	};
	

	return BusinessSystem;
    
});