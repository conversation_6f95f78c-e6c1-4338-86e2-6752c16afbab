sap.ui.define([
	
], function () {
	"use strict";
	let ParseWsdl = {};
	
	ParseWsdl.init = function(oWsdlContent) {

		// The content is already an XML document, does not need to be parsed further.
		this.Content = oWsdlContent;
		
		// Deserialize the content and store as text .. 
		this.XMLStringDocument = (new XMLSerializer()).serializeToString(this.Content);

		// Create a new XPathEvaluator and resolver objects for use 
		this.XPathEvaluator = new XPathEvaluator();
		this.XPathResolver = this.XPathEvaluator.createNSResolver(this.Content);
		
		// Get the prefix for various namespaces 
		let oDefinitionsNode = this.XPathResolver.querySelector("definitions");
		this.DefinitionsNode = oDefinitionsNode;
		
		// wsdl 
		this.wsdlPrefix = oDefinitionsNode.prefix;
		this.soapPrefix = "soap";
		this.tnsPrefix = "tns";
		this.xmlnsPrefix = "xmlns";
		// iterate the properties and get the name space for wsdl 
		this.WsdlNS = this._getAttributeValue(oDefinitionsNode, this.xmlnsPrefix, this.wsdlPrefix);
		this.SoapNS = this._getAttributeValue(oDefinitionsNode, this.xmlnsPrefix, this.soapPrefix);
		this.TNS = this._getAttributeValue(oDefinitionsNode, this.xmlnsPrefix, this.tnsPrefix);

		/** Retrieve the
		 *		Service Node : Get the service address / url 
		 *		Binding Node : Get the operations and their URL
		 *		Port Type : Get the data types for each operations (Input and Output)
		 *		Message : Get the message exchange between client and service 
		 *		Types : Get the data types node
		 *		Documentation : If the wsdl has the description of the service, read it.
		 */
		// Documentation Node
		let sXPath = this._getNodeXPath(oDefinitionsNode, "documentation");
		let oEvaluateResult = this.Content.evaluate(sXPath, this.Content, this.XPathResolver, XPathResult.ANY_TYPE, null); 
		this.DocumentationNode = oEvaluateResult.iterateNext();
		
		// Service Node 
		sXPath = this._getNodeXPath(oDefinitionsNode, "service");
		oEvaluateResult = this.Content.evaluate(sXPath, this.Content, this.XPathResolver, XPathResult.ANY_TYPE, null); 
		this.ServiceNode = oEvaluateResult.iterateNext();

		// Bindings Node 
		sXPath = this._getNodeXPath(oDefinitionsNode, "binding");
		oEvaluateResult = this.Content.evaluate(sXPath, this.Content, this.XPathResolver, XPathResult.ANY_TYPE, null); 
		this.BindingsNode = oEvaluateResult.iterateNext();

		// PortType Node 
		sXPath = this._getNodeXPath(oDefinitionsNode, "portType");
		oEvaluateResult = this.Content.evaluate(sXPath, this.Content, this.XPathResolver, XPathResult.ANY_TYPE, null); 
		this.PortTypeNode = oEvaluateResult.iterateNext();

		// Message Elements 
		sXPath = this._getNodeXPath(oDefinitionsNode, "message");
		oEvaluateResult = this.Content.evaluate(sXPath, this.Content, this.XPathResolver, XPathResult.ANY_TYPE, null); 
		this.MessageElements = oEvaluateResult.iterateNext();

		// Types Node 
		sXPath = this._getNodeXPath(oDefinitionsNode, "types");
		oEvaluateResult = this.Content.evaluate(sXPath, this.Content, this.XPathResolver, XPathResult.ANY_TYPE, null); 
		this.TypesNode = oEvaluateResult.iterateNext();
	};
	
	/**
	 * Returns the node XPath with the name space appended appropriately
	 *	oParentNode : Apprend the parent node path before returning
	 *		if oParentNode is undefined or null, the returned path is simple the node name 
	 *		with the NS prefixed
	 *		e.g if the parent node is root, the return value would be /wsdl:defintions/nodeName
	 *	sNodeName : The name for which to return the XPath. The function assumes the the node 
	 *		is an immedeate child of the parent node specified (if any)
	 */
	ParseWsdl._getNodeXPath = function(oParentNode, sNodeName) {
		let sValue = "";
		let sPrefix = "";
		
		switch(sNodeName) {
			case "documentation":
			case "service":
			case "binding":
			case "portType":
			case "message":
			case "types":
			case "port":
			case "operation":
				sPrefix = this.wsdlPrefix;
				break;
			case "address":
				sPrefix = this.soapPrefix;
				break;
			default:
				sPrefix = this.wsdlPrefix;
				break;
		}
		
		let sParentNodePath = "";
		// Get the parent node name
		while(oParentNode && oParentNode.nodeType !== XMLDocument.DOCUMENT_NODE){
			let sParentNodeName = oParentNode.nodeName;
			// if the node has siblings, get the index 
			if(oParentNode.parentNode && oParentNode.parentNode.childElementCount > 1) {
				let iIndex = 0; 
				for(let iNodeIndex = 0; iNodeIndex < oParentNode.parentNode.children.length; iNodeIndex++ ){
					if(oParentNode.parentNode.children[iNodeIndex].nodeName !== oParentNode.nodeName){
						continue;
					}
					// Increment if the node type matches
					iIndex++;

					if(oParentNode.parentNode.children[iNodeIndex].isEqualNode(oParentNode)){
						break;
					}
				}
				sParentNodeName += "[" + iIndex + "]";
			}
			sParentNodePath = "/" + sParentNodeName + sParentNodePath;
			oParentNode = oParentNode.parentNode;
		}

		if(oParentNode) {
			sValue = sParentNodePath + "/";
		}

		if( sNodeName ) {
			sValue += ((sPrefix) ? (sPrefix + ":") : "") + sNodeName;
		}
		
		return sValue;
	};
	
	
	/**
	 * Retrieve the value of the attribute. 
	 *	oNode : the node in which to check the attribute
	 *	sNameSpace : The namespace prefix for the attribute. Could you empty or null. 
	 *	sAttributeName : The attribute to search for 
	 * 
	 * Returns : The value OR "" [Empty String], if not found
	 */
	ParseWsdl._getAttributeValue = function(oNode, sNameSpace, sAttributeName) {
		if(!oNode){
			return "";
		}

		let sValue = ""; 
		let sSearchName = ((sNameSpace)? sNameSpace + ":" : "") + sAttributeName;
		
		sValue = oNode.getAttribute(sSearchName);
		if(!sValue){
			sValue = "";
		}
		
		return sValue.trim();
	};
	
	/**
	 * Return the service details
	 *		Description (if any)
	 *		Name
	 *		BindingName
	 *		Address
	 */
	ParseWsdl.getServiceInfo = function(){
		let oServiceNode = this.ServiceNode; 
		let oServiceInfo = {};
		
		// Get the element by name documentation
		let sXPath = this._getNodeXPath(oServiceNode, "documentation");
		let oDocumentationNode = 
			this.Content.evaluate(sXPath, this.ServiceNode, this.XPathResolver, XPathResult.ANY_TYPE, null); 

		// Get the content of the document node 
		let sDocumentContent = "";
		if(oDocumentationNode){
			sDocumentContent = oDocumentationNode.iterateNext().textContent;
		}
		oServiceInfo.Description = sDocumentContent.trim();
		
		// Get the Service Name 
		oServiceInfo.ServiceName = this._getAttributeValue(oServiceNode, undefined, "name");
		
		sXPath = this._getNodeXPath(oServiceNode, "port");
		let oPortNode = 
			this.Content.evaluate(sXPath, this.ServiceNode, this.XPathResolver, XPathResult.ANY_TYPE, null).iterateNext(); 
		
		oServiceInfo.ServicePortName = this._getAttributeValue(oPortNode, undefined, "name");
		oServiceInfo.ServicePortBindingName = this._getAttributeValue(oPortNode, undefined, "binding");
		
		
		// get the address 
		sXPath = this._getNodeXPath(oPortNode, "address"); 
		let oAddressNode = 
			this.Content.evaluate(sXPath, this.ServiceNode, this.XPathResolver, XPathResult.ANY_TYPE, null).iterateNext();
		oServiceInfo.ServiceAddress = this._getAttributeValue(oAddressNode, undefined, "location");
		
		return oServiceInfo;
	};
	
	/**
	 * Return the raw content of the xml (Text)
	 */
	ParseWsdl.getContent = function(){
		return this.XMLStringDocument;	
	};
	
	/**
	 * Returns the list of function within the WSDL.
	 *	return value 
	 *			[
	 *				{
	 *					name: function name
	 *					description: function description
	 *				},
	 *				{
	 *					name: function name
	 *					description: function description
	 *				},
	 *			]
	 */
	 ParseWsdl.getFunctionList = function(){
	 	let arrFunctionList = [];
		let oBindingsNode = this.BindingsNode;
		
		// Get the operations List
		let sOperationsXPath = this._getNodeXPath(oBindingsNode, "operation");
		
		let oOperationsNodes = this.Content.evaluate(sOperationsXPath, this.Content, this.XPathResolver, XPathExpression.ANY_TYPE, null);

		// Iterate and get the function List
		let oOperationNode = oOperationsNodes.iterateNext();
		while(oOperationNode)
		{
			let oFunctionInfo = {};
			oFunctionInfo.Name = this._getAttributeValue(oOperationNode, undefined, "name");

			// Get the description if any
			let sXPathDoc = this._getNodeXPath(oOperationNode, "documentation");
			let oOperationDocNode = this.Content.evaluate(sXPathDoc, this.Content, this.XPathResolver, XPathExpression.ANY_TYPE, null).iterateNext();
			
			if(oOperationDocNode){
				oFunctionInfo.Description = oOperationDocNode.textContent.trim();
			}
			arrFunctionList.push(oFunctionInfo);
			oOperationNode = oOperationsNodes.iterateNext();
		}
		
		return arrFunctionList;
	 };
	
	/**
	 * Return the Object for the specified function. 
	 * 
	 * Parameters 
	 *		1. Function Name: Name of the funciton for which the object needs to be returned
	 * 
	 * Return Value 
	 *		Object Root 
	 *			Input Object
	 *				Object Type : Array, Object
	 *				Min Occurences : Int 
	 *				Max Occurences : Int (-1 for Infinity)
	 *				Object Definition
	 *			Output Object
	 *				Object Type : Array, Object
	 *				Min Occurences : Int 
	 *				Max Occurences : Int (-1 for Infinity)
	 *				Object Definition
	 */
	ParseWsdl.getStructures = function(sFunctionName){
		// Get the Type from Bindings Node. Split the name by : and get the last item in the array
		let sTypeName = this.BindingsNode.getAttribute("type").split(":").pop();
		
		let oStructure = {};
		
		// Get the operations List
		let sXPath = this._getNodeXPath(this.DefinitionsNode, "portType");
		sXPath += "[@name='" + sTypeName + "']";
		
		let sOperationXPath = sXPath + "/" + this._getNodeXPath(undefined, "operation");
		sOperationXPath += "[@name='" + sFunctionName + "']";

		let sInputXPath = sOperationXPath + "/" + this._getNodeXPath(undefined, "input");
		let sOutputXPath = sOperationXPath + "/" + this._getNodeXPath(undefined, "output");
		
		let oInputNode = this.Content.evaluate(sInputXPath, this.Content, this.XPathResolver, XPathExpression.ANY_TYPE, null).iterateNext();
		let oOutputNode = this.Content.evaluate(sOutputXPath, this.Content, this.XPathResolver, XPathExpression.ANY_TYPE, null).iterateNext();

		// dummy line to avoid lint error, Remove when implementation is complete.
		oStructure = oInputNode && oOutputNode ? {} : {a: 1};
		
		// // Get the messages for the input and output 
		// let sInputMessageName = oInputNode.getAttribute("message");
		// let sOuptuMessageName = oOutputNode.getAttribute("message");
		
		
		
		return oStructure;
	};
	 
	return ParseWsdl;
});