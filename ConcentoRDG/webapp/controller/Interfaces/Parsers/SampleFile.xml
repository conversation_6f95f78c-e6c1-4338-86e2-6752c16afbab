<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://www.cleverbuilder.com/BookService/"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  name="BookService"
                  targetNamespace="http://www.cleverbuilder.com/BookService/">
  <wsdl:documentation>Definition for a web service called BookService,
    which can be used to add or retrieve books from a collection.
  </wsdl:documentation>

  <!--
      The `types` element defines the data types (XML elements)
      that are used by the web service.
   -->
  <wsdl:types>
    <xsd:schema targetNamespace="http://www.cleverbuilder.com/BookService/">
      <xsd:element name="Book">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="ID" type="xsd:string" minOccurs="0"/>
            <xsd:element name="Title" type="xsd:string"/>
            <xsd:element name="Author" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="Books">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element ref="tns:Book" minOccurs="0" maxOccurs="unbounded"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>

      <xsd:element name="GetBook">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="ID" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="GetBookResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element ref="tns:Book" minOccurs="0" maxOccurs="1"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>

      <xsd:element name="AddBook">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element ref="tns:Book" minOccurs="1" maxOccurs="1"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="AddBookResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element ref="tns:Book" minOccurs="0" maxOccurs="1"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="GetAllBooks">
        <xsd:complexType/>
      </xsd:element>
      <xsd:element name="GetAllBooksResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element ref="tns:Book" minOccurs="0" maxOccurs="unbounded"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
    </xsd:schema>
  </wsdl:types>


  <!--
      A wsdl `message` element is used to define a message
      exchanged between a web service, consisting of zero
      or more `part`s.
   -->

  <wsdl:message name="GetBookRequest">
    <wsdl:part element="tns:GetBook" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="GetBookResponse">
    <wsdl:part element="tns:GetBookResponse" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="AddBookRequest">
    <wsdl:part name="parameters" element="tns:AddBook"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="AddBookResponse">
    <wsdl:part name="parameters" element="tns:AddBookResponse"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="GetAllBooksRequest">
    <wsdl:part name="parameters" element="tns:GetAllBooks"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="GetAllBooksResponse">
    <wsdl:part name="parameters" element="tns:GetAllBooksResponse"></wsdl:part>
  </wsdl:message>

  <!--
      A WSDL `portType` is used to combine multiple `message`s
      (e.g. input, output) into a single operation.

      Here we define three synchronous (input/output) operations
      and the `message`s that must be used for each.
   -->
  <wsdl:portType name="BookService">
  	<wsdl:documentation> Documentation about the port type </wsdl:documentation>
    <wsdl:operation name="GetBook">
  	  <wsdl:documentation> Documentation about the GetBook Service </wsdl:documentation>
      <wsdl:input message="tns:GetBookRequest"> 
  		<wsdl:documentation> Documentation about the GetBook Service Input </wsdl:documentation>
      </wsdl:input>
      <wsdl:output message="tns:GetBookResponse">
  		<wsdl:documentation> Documentation about the GetBook Service Output </wsdl:documentation>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddBook">
  	  <wsdl:documentation> Documentation about the AddBook Service </wsdl:documentation>
      <wsdl:input message="tns:AddBookRequest"> 
  		<wsdl:documentation> Documentation about the AddBook Service Input </wsdl:documentation>
      </wsdl:input>
      <wsdl:output message="tns:AddBookResponse">
  		<wsdl:documentation> Documentation about the AddBook Service Output </wsdl:documentation>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllBooks">
  	  <wsdl:documentation> Documentation about the GetAllBooks Service </wsdl:documentation>
      <wsdl:input message="tns:GetAllBooksRequest"> 
  		<wsdl:documentation> Documentation about the GetAllBooks Service Input </wsdl:documentation>
      </wsdl:input>
      <wsdl:output message="tns:GetAllBooksResponse">
  		<wsdl:documentation> Documentation about the GetAllBooks Service Output </wsdl:documentation>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:portType>

  <!--
      The `binding` element defines exactly how each
      `operation` will take place over the network.
      In this case, we are using SOAP.
   -->
  <wsdl:binding name="BookServiceSOAP" type="tns:BookService">
    <soap:binding style="document"
                  transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="GetBook">
      <soap:operation
              soapAction="http://www.cleverbuilder.com/BookService/GetBook"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddBook">
      <soap:operation
              soapAction="http://www.cleverbuilder.com/BookService/AddBook"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllBooks">
      <soap:operation
              soapAction="http://www.cleverbuilder.com/BookService/GetAllBooks"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>

  <!--
      The `service` element finally says where the service
      can be accessed from - in other words, its endpoint.
   -->
  <wsdl:service name="BookService">
  	<wsdl:documentation> Documentation about the service </wsdl:documentation>
    <wsdl:port binding="tns:BookServiceSOAP" name="BookServiceSOAP">
      <soap:address location="http://www.example.org/BookService"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>

