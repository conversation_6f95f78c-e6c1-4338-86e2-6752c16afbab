sap.ui.define([
	"sap/ui/base/Object",
	"sap/m/MessageBox",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"./WSDL"
], function (Object, MessageBox, Utilities, WSDL) {
	"use strict";
	let Parser = {};
	
	Parser.initializeWithContent = function(oContent, sContentType) {
		this.Content = oContent; 
		this.ContentType = sContentType;

		if(sContentType === "WSDL") {
			this.Parser = WSDL;
		} 

		this.Parser.init(this.Content);
	};

	/**
	 * Initialize the parser class with the URL. Load the content from the URL and if successful
	 * proceed with other operations.
	 */
	Parser.init = function(oUrl, sContentType) {
		
		let thisClassObject = this;
		
		$.ajax({
	        type: "GET",
	        url: oUrl,
	        dataType: "xml",
	        async: false,

	        error: function () {
	        	Utilities.showPopupAlert("An error occurred while reading the URL.", MessageBox.Icon.ERROR, "Load URL.");
	        },

	        success: function (response) {
	        	thisClassObject.initializeWithContent(response, sContentType);
	        }
		});
	};

	/**
	 * Return the service details 
	 *		Name
	 *		BindingName
	 *		Address
	 */
	Parser.getServiceInfo = function(){
		return 	this.Parser.getServiceInfo();	
	};
	
	/**
	 * Return the raw content as AS-IS
	 */
	Parser.getContent = function(){
		return 	this.Parser.getContent();	
	};
	
	/**
	 * Returns the list of function within the WSDL
	 */
	Parser.getFunctionList = function(){
		let arrFunctionList = [];

		if(typeof this.Parser.getFunctionList === "function"){
			arrFunctionList = this.Parser.getFunctionList();
		}
		
		return arrFunctionList;
	};

	/**
	 * Returns the data structure for the specified function
	 * null / undefined if the function does not exist
	 */	
	Parser.getStructures = function(sFunctionName){
		let oStructure = {};

		if(typeof this.Parser.getStructures === "function"){
			oStructure = this.Parser.getStructures(sFunctionName);
		}
		
		return oStructure;
	};
	
	/**
	 * Read the WSDL Data and return a JSON Formatted object
	 */
	 Parser.toJSON = function() {
		let bFound = false;
		
		if(this.Parser)
		{
			bFound = true;
		}

		return bFound;
	 };
	 
	return Parser;
});