sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
    "dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "sap/m/MessageBox"
], function(BaseController, Utilities, MessageBox) {
    "use strict";
    
    let SetMappingKeyValue = {};

    /**
	 * Feature 11655 - Set Mapping Key from Mapping Table
	 * Event called on Step Mapping Key Value Save
	 * Show Error Popup if user clicks on Save without selecting a row
	 * Show Error Popup if user clicks on Save after selecting a segment row
	 * Allow user to select a field from the Mapping Table as Mapping Value for the selected Key field
	 * 		=>	Retrieve the path of the list item to be changed 
	 * 		->	Add the relevant mapping details for Mapping key to List item
	 * 		->	Clear the List item path
	 * 		->	Get Mapping Tree Table to default state
	 * 		->	Close the Fragment
	 */
	SetMappingKeyValue.onMappingKeyValueSave = function() {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let oTable = sap.ui.getCore().byId("SetMappingKeyValue--MappingKeyValueTree");
		let promiseShowPopupAlert;

		let iSelectedIndex = oTable.getSelectedIndex();
		if(iSelectedIndex < 0) {
			promiseShowPopupAlert =
				Utilities.showPopupAlert("Please select a row for mapping", MessageBox.Icon.ERROR, "Invalid Mapping Value Selection");
			promiseShowPopupAlert.then(function () {
			});
			return;
		}
		let oTableContext = oTable.getContextByIndex(iSelectedIndex);
		let sSelectedRowPath = oTableContext.getPath();
		let oRowData = this.getView().getModel().getProperty(sSelectedRowPath);
		let sMappedKeyPath = oInterfaceModel.getProperty("/mappedValue/sMappedKeyPath");
		if(oRowData.bIsSegment) {
			promiseShowPopupAlert =
				Utilities.showPopupAlert("Please select a field for mapping.", MessageBox.Icon.ERROR, "Invalid Mapping Value Selection");
			promiseShowPopupAlert.then(function () {
			});
			return;
		}
		oInterfaceModel.setProperty(sMappedKeyPath + "/oMappingKeyInfo", {
			Nodekey: oRowData.Fieldnodekey.toString(),
			Fieldname: oRowData.Fieldname,
			Parentseg: oRowData.Parentseg,
			Parentpath: oRowData.Parentsegpath,
			Segmenttype: oRowData.Segmenttype
		});
		oInterfaceModel.setProperty(sMappedKeyPath + "/Value", oRowData.Fieldname);
		oInterfaceModel.setProperty("/mappedValue/sMappedKeyPath", undefined );
		oTable.setSelectedIndex(-1);
		oTable.collapseAll();
		oTable.expandToLevel(1);
		oController.SetMappingKeyValueDialog.close();
	};

    /**
	 * Feature 11655 - Set Mapping Key from Mapping Table
	 * on Button Cancel Press Event for SetMappingKeyValue fragment
	 * 		->	Clear the List item path
	 * 		->	Get Mapping Tree Table to default state
	 * 		->	Close the Fragment	
	 */
	SetMappingKeyValue.onMappingKeyValueCancel = function() {
		let oController = this;
		let oInterfaceModel = oController.getView().getModel("viewInterface");
		let oTable = sap.ui.getCore().byId("SetMappingKeyValue--MappingKeyValueTree");
		oInterfaceModel.setProperty("/mappedValue/sMappedKeyPath", undefined );
		oTable.setSelectedIndex(-1);
		oTable.collapseAll();
		oTable.expandToLevel(1);
		oController.SetMappingKeyValueDialog.close();
	};

    return SetMappingKeyValue;
});