sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
    "sap/ui/model/json/JSONModel",
    "sap/m/MessageToast",
    "sap/m/MessageView",
    "sap/m/MessageItem",
    "sap/m/Button",
    "sap/m/Bar",
    "sap/m/Title",
    "sap/ui/core/IconPool",
    "sap/m/ResponsivePopover",
    "sap/ui/core/ws/WebSocket",
    "sap/ui/core/format/DateFormat"
], function (BaseController, JSONModel, MessageToast, MessageView, MessageItem, Button, Bar, Title, IconPool, ResponsivePopover, WebSocket, DateFormat) {
    "use strict";
    let instance;
    let init;
    return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.Notifications.Notifications", {
        constructor: function () {
            if (instance){
                return instance;
            }
            instance = this;
            return instance;
        },
        onInit: function () {
            if (init){
                this._button.push(this.byId("openNotifications"));
                this._updateButton(0);
                return;
            }
            this._oModelMessages = new JSONModel();
            this._messages = [];
            this._oModelMessages.setData(this._messages);

            this._createMessageView();
            this._oMessageView.setModel(this._oModelMessages);

            this._reconnectionDelay = 1000;
            this._maxReconnectionDelay = 16000;

            this._createPopover();
            this._firstTryConnection = true;
            this._createWebSocket();

            this._pendingMessages = 0;
            this._buttonVisible = true;
            this._button = [];
            this._button.push(this.byId("openNotifications"));
            this._updateButton(0);

            init = true;

            // Subscribe for a the notification event 
            let oEventBus = this.getOwnerComponent().getEventBus();
            oEventBus.subscribe("onNotification", this.onNotificationReceived, this);
        },

        onExit: function() {
            this.getOwnerComponent().getEventBus().unsubscribe("onNotification", this.onNotificationReceived, this);
        },

        handleNotifications: function (oEvent) {
            if(this._oPopover.isOpen()) {
                this._oPopover.close();
            } else {
                this._oMessageView.navigateBack();
                this._oBackButton.setVisible(false);
                this._updateButton(this._pendingMessages * -1);
                this._oPopover.openBy(oEvent.getSource());
            }
        },
        _createWebSocket: function () {
            let url = "sap/bc/apc/rdg/push_channel";
            this._ws = new WebSocket(url);

            this._ws.attachError(() => {
                if (!this._firstTryConnection) {
                    this._internalNotification("Error", "Cannot connect to Websocket"); 
                }
            });

            this._ws.attachClose(() => {
                if (!this._firstTryConnection) {
                    this._internalNotification("Warning", "Websocket connection closed");
                    this._ws = null;
                    let that = this;
                    setTimeout(() => {
                        that._reconnect();
                    }, that._reconnectionDelay + Math.floor(Math.random() * 3000));
                }
            });

            this._ws.attachOpen(() => {
                if (this._firstTryConnection) {
                    this._firstTryConnection = false;
                    this._buttonVisible = true;
                    this._updateButton(0);
                } else {
                    this._internalNotification("Success", "Connected to Websocket");
                }
            });

            this._ws.attachMessage((oEvent) => {
                try {
                    let message = JSON.parse(oEvent.getParameters().data);
                    this._receiveNotification(message);
                } catch (err) {
                    return;
                }
            });

        },
        _reconnect: function () {
            if (this._reconnectionDelay < this._maxReconnectionDelay){
                this._reconnectionDelay *= 2;
            }
            this._internalNotification("Information", "Trying to reconnect to Websocket");
            this._createWebSocket();
        },
        _createMessageView: function () {
            let that = this;
            let oMessageTemplate = new MessageItem({
                type: "{type}",
                title: "{title}",
                description: "{description}",
                subtitle: "{subtitle}",
                counter: "{counter}",
                groupName: "{groupName}"
            });
            this._oBackButton = new Button({
                icon: IconPool.getIconURI("nav-back"),
                visible: false,
                press: function () {
                    that._oMessageView.navigateBack();
                    that._oPopover.focus();
                    this.setVisible(false);
                }
            });
            this._oMessageView = new MessageView({
                    showDetailsPageHeader: false,
                    groupItems: true,
                    itemSelect: function () {
                        that._oBackButton.setVisible(true);
                        if (that._oPopover.isOpen()){
                            that._oPopover.focus();
                        }
                    },
                    items: {
                        path: "/",
                        template: oMessageTemplate
                    }
            });
            
        },
        _createPopover: function () {
            let that = this;
            let oCloseButton =  new Button({
                text: "Close",
                press: function () {
                    that._oBackButton.setVisible(false);
                    that._oPopover.close();
                }
            }).addStyleClass("sapUiTinyMarginEnd");
            
            let oPopoverBar = new Bar({
                contentLeft: [this._oBackButton],
                contentMiddle: [
                    new Title({text: "Notifications"})
                ]
            });
            
            let oClearButton = new Button({
                text: "Clear",
                press: function () {
                    that._oMessageView.navigateBack();
                    that._oBackButton.setVisible(false);
                    that._messages = [];
                    that._oModelMessages.setData(that._messages);
                }
            }).addStyleClass("sapUiTinyMarginEnd");

            this._oPopover = new ResponsivePopover({
                customHeader: oPopoverBar,
                contentWidth: "30%",
                contentHeight: "40%",
                verticalScrolling: false,
                modal: false,
                placement: sap.m.PlacementType.Bottom,
                content: [this._oMessageView],
                beginButton: oClearButton,
                endButton: oCloseButton
            });
        },
        _updateButton: function(num) {
            this._pendingMessages += num;
            let that = this;
            this._button.forEach(e => {
                e.setVisible(this._buttonVisible);
                e.getBadgeCustomData().setValue(that._pendingMessages);
            });
        },

        // Receives the content of the message through the event 
        onNotificationReceived: function(oEvent, sNotificationType, oData){
            this._internalNotification(oData.type, oData.title, oData.description, oData.groupName);
        },

        _internalNotification: function(type, title, description = "", groupName = undefined) {
            let message = {
                type: type,
                title: title,
                description: description,
                groupName: groupName
            };
            this._receiveNotification(message);
        },
        _receiveNotification: function(message) {
            let date = message.subtitle === undefined ? new Date() : new Date(message.subtitle * 1000);
            message.subtitle = DateFormat.getDateTimeWithTimezoneInstance({showTimezone: false}).format(date);
            this._messages.unshift(message);
            this._oModelMessages.setData(this._messages);
            if (!this._oPopover.isOpen()){
                this._updateButton(1);
            } else {
                this._updateButton(1);
                this._oMessageView.navigateBack();
                this._oBackButton.setVisible(false);
                let that = this;
                setTimeout(() => {
                    that._updateButton(-1);
                }, 3000);
            }

            let sNotificationMessage = message.title;
            if(sNotificationMessage && message.description) {
                sNotificationMessage += ": ";
            }

            if(message.description){
                sNotificationMessage += message.description;
            }
            MessageToast.show(sNotificationMessage);
        }
    });
});