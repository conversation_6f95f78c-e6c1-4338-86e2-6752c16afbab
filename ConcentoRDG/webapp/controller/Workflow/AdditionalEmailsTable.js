sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
], function (Utilities) {
	"use strict";
	let AdditionalEmailsTable = {};
	
	/*
	*	Create and open the Edit Node Connections Fragment
	*	Called from ApproverAssignment controller
	
	*	this refers to the current js file
	*	oParentController refers to ApproverAssignment controller
	*/
	AdditionalEmailsTable.openDialog = function(oParentController, arrAdditionalEmailListDetails, sPath){
		
		if (!oParentController.AdditionalEmailsDialog) {
			oParentController.AdditionalEmailsDialog =
				sap.ui.xmlfragment(
					// "NodeConnectionEdit",
					"AdditionalEmailsTable",
					"dmr.mdg.supernova.SupernovaFJ.view.Workflow.AdditionalEmailsTable",
					oParentController);
			oParentController.getView().addDependent(oParentController.AdditionalEmailsDialog);
		}

		oParentController.AdditionalEmailsDialog.open();
		
		// store the parent controller handle 
		this.ParentController = oParentController;
		let oApproverModel = oParentController.getView().getModel("approverModel");
		oApproverModel.setProperty("/ConnectionEditor/connectionsList/addlEmailPath", sPath);
		//Fetch the entity list for additional email placeholders
		let oCRTypeDetails = oApproverModel.getProperty("/CRTypeDetails");
		this.fetchEntitiesList(this, oCRTypeDetails.DataModel, oCRTypeDetails.CrTypeName);
		
		// Add the rows to the table 
		this.addRows(oParentController, arrAdditionalEmailListDetails);
	};
	
	AdditionalEmailsTable.addRows = async function(oParentController, arrAddlEmailListDetails) {
		let oApproverModel = oParentController.getView().getModel("approverModel");
		let sDataModel = oApproverModel.getProperty("/CRTypeDetails/DataModel");
		let arrEmailList = arrAddlEmailListDetails.split(";"); 
		let arrFinalEmailList = [];
		let arrFinalEmailAttrList = [];
		let oEmail = {};
		let mailregex = /^\w+[\w-+\.]*\@\w+([-\.]\w+)*\.[a-zA-Z]{2,}$/;
		let mailAttrRegex = /^\{\{(.+?):(.+?)\}\}$/;
		// arrEmailList.forEach(function(sEmail){
		for (let i = 0; i < arrEmailList.length; i++) {
			let sEmail = arrEmailList[i];
			if(mailregex.test(sEmail)) {
				oEmail = {};
				oEmail.AddlEmail = sEmail;
				arrFinalEmailList.push(oEmail);
			} else if(mailAttrRegex.test(sEmail)) {
				let arrMatch = sEmail.match(mailAttrRegex);
				if(arrMatch) {
					oEmail = {};
					oEmail.AddlEmailEntity = arrMatch[1];
					oEmail.AddlEmailAttribute = arrMatch[2];
					let arrAttrList = await oParentController.GetListData.getEntityAttributeList(oParentController, sDataModel, oEmail.AddlEmailEntity, undefined);
					if (!arrAttrList) {
						arrAttrList = [];
					}
					oEmail.attributeList = arrAttrList;
					arrFinalEmailAttrList.push(oEmail);
				}
			}
		}
		// });
		oApproverModel.setProperty("/ConnectionEditor/connectionsList/addlEmailList", arrFinalEmailList);
		oApproverModel.setProperty("/ConnectionEditor/connectionsList/addlEmailAttrList", arrFinalEmailAttrList);
	};
	
	/*
	*	Button Click Event when user selects a row and clicks on Add button
	*	A new row gets inserted on the same level as the selected row index
	*	Called from the view as an Event handler
	
	*	this refers to the ApproverAssignment controller
	*/
	AdditionalEmailsTable.onAddRowPressed = function() {
		let oApproverModel = this.getView().getModel("approverModel");
		let sPath = oApproverModel.getProperty("/ConnectionEditor/connectionsList/addlEmailPath");
		let arrList = oApproverModel.getProperty("/ConnectionEditor/connectionsList/addlEmailList");
		if(arrList){
			arrList.push({});
			oApproverModel.refresh();
		}
		oApproverModel.setProperty("/ConnectionEditor/connectionsList/addlEmailList", arrList);
		let sAddlEmailListDetails = arrList.map(function(elem){
        					return elem.AddlEmail;
    						}).join(";");
		oApproverModel.setProperty(sPath, sAddlEmailListDetails);
	};

	AdditionalEmailsTable.onAddAttrRowPressed = function() {
		let oApproverModel = this.getView().getModel("approverModel");
		let arrList = oApproverModel.getProperty("/ConnectionEditor/connectionsList/addlEmailAttrList");
		if(arrList){
			arrList.push({});
			oApproverModel.refresh();
		}
		oApproverModel.setProperty("/ConnectionEditor/connectionsList/addlEmailAttrList", arrList);
	};
	
	/*
	*	Button Click Event when user selects a row and clicks on Delete button
	*	Delete the selected row from the List structure
	*	Called from the view as an Event handler
	
	*	this refers to the ApproverAssignment controller
	*/
	AdditionalEmailsTable.onDeleteRowPressed = function(oEvent) {
		let oApproverModel = this.getView().getModel("approverModel");
		let arrList = oApproverModel.getProperty("/ConnectionEditor/connectionsList/addlEmailList");
		let sPath = oApproverModel.getProperty("/ConnectionEditor/connectionsList/addlEmailPath");
		let sSelectedListItemPath = oEvent.getParameter("listItem").getBindingContext("approverModel").getPath();
		let iChildRow = parseInt(sSelectedListItemPath.substring(sSelectedListItemPath.lastIndexOf("/") + 1), 10);
		
		arrList.splice(iChildRow, 1);
		oApproverModel.setProperty("/ConnectionEditor/connectionsList/addlEmailList", arrList);
		let sAddlEmailListDetails = arrList.map(function(elem){
    					return elem.AddlEmail;
						}).join(";");
		oApproverModel.setProperty(sPath, sAddlEmailListDetails);
	};

	AdditionalEmailsTable.onDeleteAttrRowPressed = function(oEvent) {
		let oApproverModel = this.getView().getModel("approverModel");
		let arrList = oApproverModel.getProperty("/ConnectionEditor/connectionsList/addlEmailAttrList");
		let sSelectedListItemPath = oEvent.getParameter("listItem").getBindingContext("approverModel").getPath();
		let iSelectedRow = parseInt(sSelectedListItemPath.substring(sSelectedListItemPath.lastIndexOf("/") + 1), 10);

		arrList.splice(iSelectedRow, 1);
		oApproverModel.setProperty("/ConnectionEditor/connectionsList/addlEmailAttrList", arrList);
	};
	
	/*
	*	Button Click Event when user clicks on Save Button
	*	Validate the entries and return an error message if mandaory fields are missing
	*	Store the emails as string to node connection row model property
	*	Called from the view as an Event handler
	
	*	this refers to the ApproverAssignment controller
	*/
	AdditionalEmailsTable.additionalEmailsSaveClicked = function() {
		let oController = this;
		let oApproverModel = oController.getView().getModel("approverModel");
		let sPath = oApproverModel.getProperty("/ConnectionEditor/connectionsList/addlEmailPath");
		let arrAddlEmailListDetails = oApproverModel.getProperty("/ConnectionEditor/connectionsList/addlEmailList");
		let arrAddlEmailAttrListDetails = oApproverModel.getProperty("/ConnectionEditor/connectionsList/addlEmailAttrList");
		let arrFinalEmailList = [];
		let arrFinalEmailAttrList = [];

		//Validate the email address before adding to final array and converting to string
		if(arrAddlEmailListDetails && arrAddlEmailListDetails.length > 0) {
			let mailregex = /^\w+[\w-+\.]*\@\w+([-\.]\w+)*\.[a-zA-Z]{2,}$/;
			for (let i = 0; i < arrAddlEmailListDetails.length; i++) {
				if(!mailregex.test(arrAddlEmailListDetails[i].AddlEmail)) {
					Utilities.showPopupAlert(
						oController.geti18nText("additionalEmailDialog.invalidDetails.message"), 
						"Error",
						oController.geti18nText("additionalEmailDialog.invalidDetails.title"));
					return;
				}
			}

			arrFinalEmailList = arrAddlEmailListDetails.map(function(elem){
				return elem.AddlEmail;
			});
		}

		//Validate the email via attribute before adding to final array and converting to string
		if(arrAddlEmailAttrListDetails && arrAddlEmailAttrListDetails.length > 0) {
			for (let i = 0; i < arrAddlEmailAttrListDetails.length > 0; i++) {
				if(!(arrAddlEmailAttrListDetails[i].AddlEmailEntity && arrAddlEmailAttrListDetails[i].AddlEmailAttribute)) {
					Utilities.showPopupAlert(
						oController.geti18nText("additionalEmailDialog.invalidDetails.message"), 
						"Error",
						oController.geti18nText("additionalEmailDialog.invalidDetails.title"));
					return;
				}
			}

			arrFinalEmailAttrList = arrAddlEmailAttrListDetails.map(oAttrRow => {
				if(oAttrRow.AddlEmailEntity && oAttrRow.AddlEmailAttribute) {
					return `{{${oAttrRow.AddlEmailEntity}:${oAttrRow.AddlEmailAttribute}}}`;
				}
			});
		}

		//Merge both arrays, convert to string separated by ; and add to node connection row property
		let sAddlEmailListDetails = [...arrFinalEmailList, ...arrFinalEmailAttrList].join(";");
		
		oApproverModel.setProperty(sPath, sAddlEmailListDetails);
		oController.AdditionalEmailsDialog.close();
	};
	
	/*
	*	Button Click Event when user selects a row and clicks on Save Button
	*	Closes the Node Edit Connections Fragment without saving any changes
	*	Called from the view as an Event handler
	
	*	this refers to the ApproverAssignment controller
	*/
	AdditionalEmailsTable.additionalEmailsCancelClicked = function() {
		this.AdditionalEmailsDialog.close();
	};

	AdditionalEmailsTable.validateEmail = function(sEmail) {
		let mailregex = /^\w+[\w-+\.]*\@\w+([-\.]\w+)*\.[a-zA-Z]{2,}$/;
		if(!mailregex.test(sEmail)) {
			return "Error";
		} else {
			return "None";
		}
	};

	/**
	 * 
	 * @param {object} oController 
	 * @param {string} sDataModel Data Model for which the entity list list must be fetched 
	 * @param {string} sCRType CR Type for which the entity list must be fetched
	 */
	AdditionalEmailsTable.fetchEntitiesList = function(oController, sDataModel, sCRType) {
		let oApproverModel = oController.ParentController.getView().getModel("approverModel");
		// Bug 13281 - set the last parameter as true to add the CR_HEADER value as a part of the entity list.
		let promiseEntitiesList = oController.ParentController.GetListData.getModelEntityList(oController.ParentController, sDataModel, "UsmdModel", sCRType, undefined, true);
		promiseEntitiesList.then(function (arrEntitiesList) {
			if (!arrEntitiesList) {
				arrEntitiesList = [];
			}
			oApproverModel.setProperty("/ConnectionEditor/EntitiesList", arrEntitiesList);
		});
		return promiseEntitiesList;
	};

	/**
	 * 
	 * @param {*} oEvent Event for combobox
	 * this refers to Approver Assignment controller
	 * Fetch the list of Attributes for the selected entity per additional emails attribute row
	 */
	AdditionalEmailsTable.onEntitySelectionChange = async function(oEvent) {
		let oController = this;
		let sSelectedEntity = oEvent.getSource().getSelectedKey();
		let oApproverModel = oController.getView().getModel("approverModel");
		let sDataModel = oApproverModel.getProperty("/CRTypeDetails/DataModel");
		let sPath = oEvent.getSource().getBindingContext("approverModel").getPath();

		oApproverModel.setProperty(sPath + "/AddlEmailAttribute", undefined);
		let arrAttrList = await oController.GetListData.getEntityAttributeList(oController, sDataModel, sSelectedEntity, undefined);
		if (!arrAttrList) {
			arrAttrList = [];
		}
		oApproverModel.setProperty(sPath + "/attributeList", arrAttrList);
	};
	
	return AdditionalEmailsTable;
}); 