sap.ui.define([
	"jquery.sap.global",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"sap/ui/core/BusyIndicator",
	"sap/m/MessageToast",
	"./DMRNode",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"sap/m/MessageBox"
], function (jQuery, DMRDataService, GetListsData, BusyIndicator, MessageToast, DMRNode, Utilities, MessageBox) {
	"use strict";
	let oDynamicWorkflowObject = {};
	/**
	 * Handler for the node add for Dynamic and Parallel flow 
	 * The dynamic and parallel flow have the same screen handlers except for some restriction / check 
	 * for the parallel flow 
	 *		Parallel Flow
	 *		- Allow only Type 4 entities to be list for add 
	 *		- The user will not be allowed to change the entity once selected 
	 *		- Default row display is not shown 
	 *		- The flow cannot be saved unless at least 1 rule has been added 
	 *		- The step type for all rules must be the same
	 * 
	 * Inputs
	 *	oController : The controller object received from the mail controller 
	 *  oNode : The node to be edited 
	 *	oWorkFlowDetails : Null for a new node and the node Details for a node being edited
	 *  iWorkFlowType : The type of workflow to be created or edited (Dynamic / Parallel)
	 */
	oDynamicWorkflowObject.open = function(oController, oNode, oWorkFlowDetails, iWorkFlowType){

		// Store the controller and node data.
		this.ParentController = oController;
		this.Node = oNode;
		this.WorkflowType = iWorkFlowType;

		if (!this.DynamicNodeEditDialog) {
			this.DynamicWorkflowFragmentId = "DynamicWorkflowFragment";
			
			this.DynamicNodeEditDialog =
				sap.ui.xmlfragment(
					this.DynamicWorkflowFragmentId,
					"dmr.mdg.supernova.SupernovaFJ.view.Workflow.DynamicWorkflow",
					this.ParentController);
			this.ParentController.getView().addDependent(this.DynamicNodeEditDialog);
		}

		// Pointer to the select attribute dialog 
		this.SelectAttributeDialog = oController.SelectAttributeDialog;
		this.JSONViewModel = this.ParentController.getApproverModel();
		this.CRTypeDetails = this.ParentController.getCRDetails();
		
		// Call the init function to initialize dialog and the model details 
		this.initializeDialog(oWorkFlowDetails, iWorkFlowType);
		
		// Open the dialog
		this.DynamicNodeEditDialog.open();
	};
	
	/**
	 * Dynamic Workflow  builder. 
	 * Model Path Root: /dynamic
	 * 
	 */
	oDynamicWorkflowObject.initializeDialog = async function (oWorkFlowDetails, iWorkFlowType) {
		let oController = this;
		let sChangeDoc;
		if (oWorkFlowDetails) {
			oWorkFlowDetails.DTVALUESSet.map(dtValue => {
				dtValue.ColValue = ( dtValue.ColValue === "INIT" ) ? "" : dtValue.ColValue;
				return dtValue;
			});
			sChangeDoc = (oWorkFlowDetails.Changedoc ? "AC" : "AV");
		} else{
			sChangeDoc = "AV";
		}
		// Get model 
		let oDynamicWorkflowModel = this.JSONViewModel;

		// Initialize mandatory properties 
		oDynamicWorkflowModel.setProperty("/dynamic", {});
		oDynamicWorkflowModel.setProperty("/dynamic/parallelFlow", {});
		oDynamicWorkflowModel.setProperty("/dynamic/parallelFlow/StepType", "");
		oDynamicWorkflowModel.setProperty("/dynamic/workflowType", iWorkFlowType);
		oDynamicWorkflowModel.setProperty("/dynamic/tableMode", sap.m.ListMode.SingleSelectMaster);
		oDynamicWorkflowModel.setProperty("/dynamic/lists", {});
		oDynamicWorkflowModel.setProperty("/dynamic/tableHasColumns", false);
		oDynamicWorkflowModel.setProperty("/dynamic/data", []);
		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		oDynamicWorkflowModel.setProperty("/dynamic/Changedoc", sChangeDoc);
		oDynamicWorkflowModel.setProperty("/dynamic/ebChangedoc", true);
		
		let oDynamicTable = sap.ui.getCore().byId(this.DynamicWorkflowFragmentId + "--" + "idDerivationTable");
		
		// Remove all columns 
		oDynamicTable.removeAllColumns();

		// Set the binding data. If a binding already exists .. destroy 
		let oTableBindingInfo = oDynamicTable.getBindingInfo("items");
		if (oTableBindingInfo && oTableBindingInfo.template) {
			oTableBindingInfo.template.destroy();
		}

		// Set the binding info
		let oColumnItems = new sap.m.ColumnListItem("colItems", {
			type: "Active"
		});
		oDynamicTable.bindItems({
			path: "/dynamic/data",
			model: "workflow",
			template: oColumnItems,
			templateShareable: true
		});

		// Read the node data and load in into the table 
		if(oWorkFlowDetails){
			let i = 0;
			let arrAttributes = [];

			//ModelToValidation property is filled when the first driving attribute is filled
			// No Driving Attribute <-> No Validation ATtributes

			BusyIndicator.show(300);  
			// Read the attribute values and load the model
			let arrAttrValuesSource = oWorkFlowDetails.DTVALUESSet;
			// Sort the data by row numbers 
			arrAttrValuesSource.sort(function (a, b) {
				return parseInt(a.RowNum, 10) - parseInt(b.RowNum, 10);
			});

			let arrAttrValues = [];
			let oCrTypeDetails = oController.CRTypeDetails;
			
			arrAttrValues[0] = {};
			
			if(iWorkFlowType === DMRNode.Types.Dynamic){
				arrAttrValues[0].defaultStep = "X";
			}
				
			for (i = 0; i < arrAttrValuesSource.length; i++) {
				let iRowNum = parseInt(arrAttrValuesSource[i].RowNum, 10) - 1;
				// initialize the object at the row number if it has not already been
				if (!arrAttrValues[iRowNum]) {
					arrAttrValues[iRowNum] = {};
				}

				if(sChangeDoc === "AV"){
					if (arrAttrValuesSource[i].ColName.includes("__") && arrAttrValuesSource[i].Operator !== "CP" && arrAttrValuesSource[i].Operator !== "NP" && arrAttrValuesSource[i].ColValue) {
						let entity = arrAttrValuesSource[i].ColName.split("__")[0];
						let attribute = arrAttrValuesSource[i].ColName.split("__")[1];
						let sDescription = arrAttrValuesSource[i].ColValueDescr;
						/** Fix if value is not only with the key for dont call again backend, is not needed */
						if(!arrAttrValuesSource[i].ColValue.includes(" - ") && !sDescription){					
							let oValueList =
								await oController.ParentController.GetListData.getAttributeValuesList(oController.ParentController, oCrTypeDetails.DataModel, entity, attribute, arrAttrValuesSource[i].ColValue, true);
							let arrValueList = oValueList;
							/**
							 * Some functions return the data in oData -> results ... check if "results" exist and then point the model to that
							*/
							if(oValueList.results){
								arrValueList = oValueList.results;
							}
							
							if (arrValueList.length > 0) {
								arrAttrValuesSource[i].ColValue = arrValueList[0].Key + (arrValueList[0].Ddtext.length > 0 ? " - " : "") + arrValueList[0].Ddtext;
							}
						}else if(sDescription && !arrAttrValuesSource[i].ColValue.includes(" - ")) {
							sDescription = sDescription?.replace("-BLANK-", "");
							arrAttrValuesSource[i].ColValue += (sDescription ? " - " + sDescription: "");
						}
					}
				}

				arrAttrValues[iRowNum][arrAttrValuesSource[i].ColName] = arrAttrValuesSource[i].ColValue;
				arrAttrValues[iRowNum][arrAttrValuesSource[i].ColName + "-Operator"] = arrAttrValuesSource[i].Operator;
			}

			// Load the action data into the table data
			arrAttrValuesSource = oWorkFlowDetails.DTACTIONS;
			for(i = 0; i <  arrAttrValuesSource.length; i++) {
				let iActionRowNum = parseInt(arrAttrValuesSource[i].RowNum, 10) - 1;
				arrAttrValues[iActionRowNum].StepActions = arrAttrValuesSource[i].action;
			}
			
			oDynamicWorkflowModel.setProperty("/dynamic/data", arrAttrValues);
			oDynamicWorkflowModel.setProperty("/dynamic/Apprdesc", oWorkFlowDetails.Apprdesc);

			// Bug 12188 - Able to enter invalid data in Approver field for parallel & dynamic workflows
			arrAttrValues.forEach((oAttrValue, index) => {
					if(oAttrValue.USER_TYPE !== "NA"){
						let promise = this.ParentController.getApproverList(oAttrValue.USER_TYPE, oAttrValue.USER_VALUE.split(" - ")[0]);
						promise.then(function(arrApproverList) {
							// Task 11631 - System method correction for background step - UI
							let arrApproverSanitizedList = arrApproverList;
							if (oAttrValue.USER_TYPE === "BG") {
								arrApproverSanitizedList = arrApproverList.filter(oApprover => {
									return oApprover.key !== "SM";
								});
							}

							const firstApprover = arrApproverSanitizedList?.find(item => item?.key);
							if (oAttrValue.USER_TYPE === "SU" && firstApprover && !oAttrValue.USER_VALUE) {
								oAttrValue.USER_VALUE = firstApprover.key;
							}					

							// Bug 12778 - Description is obtained to be shown in the user value field
							let sCompleteApproverValue = arrApproverList.find(oApprover => oApprover.key === oAttrValue.USER_VALUE);
							if (sCompleteApproverValue) {
								oAttrValue.USER_VALUE = sCompleteApproverValue.key + (sCompleteApproverValue.text ? " - " : "") + sCompleteApproverValue.text;
							}
							oAttrValue.USER_VALID_VALUES = arrApproverSanitizedList;
							oDynamicWorkflowModel.setProperty("/dynamic/data/" + index, oAttrValue);
							
							BusyIndicator.hide();
						});
						oDynamicWorkflowModel.setProperty("/dynamic/data/" + index + "/ebApprover", true);
					} else {
						oDynamicWorkflowModel.setProperty("/dynamic/data/" + index + "/ebApprover", false);
						oAttrValue.USER_VALUE = "Target State Routing";
					}
				});

			// Read the Driving, Deriving and Message Attribute and create the columns

			// Add the Driving columns to the table 
			arrAttributes = oWorkFlowDetails.DRIVINGENTITYSet;
			for (i = 0; i < arrAttributes.length; i++) {
				/* Feature/12185 - function was placed into a promise to wait the creation of each column and be able to obtain them to set
					the complete value in their fields */
				await new Promise(function(resolve){
					oController.returnSelectedAttribute({
						entity: arrAttributes[i].UsmdEntity,
						attribute: arrAttributes[i].UsmdAttribute,
						attributeDataType: arrAttributes[i].Attrdatatype,
						attributeType: "Driving"
					}, resolve, true);
				});
			}
		}
        //If new, add a new rule as default Rule
		else {
			this.addNewRule();
		}
	};

	oDynamicWorkflowObject.onChangedocCBoxChange = function (oEvent){
		let oDynamicWorkflowModel = this.getModel("approverModel");
		let sChangedoc = oEvent.getSource().getSelectedKey();
		oDynamicWorkflowModel.setProperty("/dynamic/Changedoc", sChangedoc);
	};

	/**
	 * Executed in component context. this = Component
	 * 
	 * Open a popup to choose an Entity and Attribute and then add it to the table. 
	 * 
	 * TODO : Add column count limitations
	 *		> Driving Attributes : Max 5
	 *		> Deriving Attributes : Max 3
	 */
	oDynamicWorkflowObject.addAttributeColumnHandler = function (oEvent) {

		// Get the event source
		//The Entity should be editable when Attribute Type is Driving
		let sAttributeType = (oEvent.getSource().getId().includes("btnAddDrivingAttribute") ||
			oEvent.getSource().getId().includes("btnAddValidatingAttribute")) ? "Driving" : "Deriving";

		this.DynamicWorkflowDialog.addAttributeColumn(sAttributeType);                         
	};

	oDynamicWorkflowObject.addAttributeColumn = function(sAttributeType){
		/**
		 * Iteration 5. Date: 07/02/2022.  Task 8699
		 *  No difference between selecting entity/attributes for Dynamic or Parrallel workflow
		 * No limitations on entities displayed for entity combobox
		 * Show all entities for selection
		 */

		let sEntityName = this.CRTypeDetails.Entity;
		this.SelectAttributeDialog.openSelectAttributeDialog(
			this, this.JSONViewModel, sAttributeType, sEntityName, this.CRTypeDetails.DataModel);
	};

	/**
	 * Update the node details with the connection information. Important to know which action connects to which one when updating the graph.
	 * This function is designed to be called from the Controller when the connection edit sequence is compelted. 
	 * 
	 * The NodeDetails are updated with the conenction details. 
	 * ************** Since this call is made only called after NODE EDIT, it is assumed that all the columns and rows exist and only the data needs
	 * to be updated. ************
	 */
	oDynamicWorkflowObject.updateConnectionData = function(oController, oNode, oNodeDetails, oActionDetails, bReplaceContent){

		// Get the column number for the "Connections" columns
		let arrDerivngColumns = oNodeDetails.DERIVINGENTITYSet;
		
		// Loop through the array and get the index of "Connections" column
		let sConnectionsColumnIndex = ""; 
		for(let i = 0; i < arrDerivngColumns.length; i++) {
			if( arrDerivngColumns[i].ColName === "Connections" ) {
				sConnectionsColumnIndex = arrDerivngColumns[i].ColNum;
				break;
			}
		}
		
		// Go through the values array and identify the index of the element pointing to sRowNum and iConnectionsColumnIndex 
		let arrValues = oNodeDetails.DTVALUESSet;
		let iColumnValueIndex = -1; 
		for(let j = 0; j < arrValues.length; j++) {
			if ((arrValues[j].ColNum === sConnectionsColumnIndex.toString()) && (arrValues[j].RowNum === oActionDetails.RowNum.toString())){
				iColumnValueIndex = j;
				break;
			}
		}
		
		/** Read the ColValue and parse and update the same 
		 * 
		 * The column data is in a colon separated and comma separated value format
		 * E.g. action1:TargetNode1,action2:TargetNode2,actions3:TargetNode3
		 */
		let oValueObject = arrValues[iColumnValueIndex];
		let sColumnValue = oValueObject.ColValue;
		// If the content is to be reset ... empty it .. 
		if(bReplaceContent){
			sColumnValue = "";
		}
		
		// Parse the column value and create an array
		let arrActionPairs;
		try { 
			arrActionPairs = JSON.parse(sColumnValue);
		} catch (error) {
			// if the content cannot be parsed .. initialize
			arrActionPairs = [];
		}

		// If there nothing in the column value, initialize the array
		if(!arrActionPairs) {
			arrActionPairs = [];
		}
		 
		// Stringify the node details object and add it to the array. 
		let sActionDetails = JSON.stringify(oActionDetails);
		
		// If the Element was not updated  add the pair to the
		arrActionPairs.push(sActionDetails);

		// Update the data back into the node details 
		arrActionPairs = arrActionPairs.filter(function(a){
			// Filter out empty content
			return (a.length > 0);
		});
		oNodeDetails.DTVALUESSet[iColumnValueIndex].ColValue = JSON.stringify(arrActionPairs);
		
		return oNodeDetails;

	};
	
	/**
	 * Executed in component context. 
	 * 
	 * Delete the selected column from the table.
	 */
	oDynamicWorkflowObject.removeAttributeColumn = function (oEvent, oColumnInfo) {
		// Get model 
		let oDynamicWorkflowModel = this.JSONViewModel;
		let oController = this;

		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		let oDynamicWorkflowTable = sap.ui.getCore().byId(this.DynamicWorkflowFragmentId + "--" + "idDerivationTable");

		let promise = new Promise(function (resolve, reject) {
			// Show a message asking the user to confirm deletion. Exit if cancel 
			let promiseShowPopupAlert = Utilities.showPopupAlert("The attribute will be deleted from the decision table. Continue?", MessageBox.Icon.WARNING,
				"Remove Attribute?", [MessageBox.Action.YES, MessageBox.Action.NO]);
			promiseShowPopupAlert.then(function () {
				resolve();
			}, function () {
				reject();
			});
		});

		/**
		 * Find the table, iterate through the columns and remove the matched column
		 */
		promise.then(function () {

			// Get the item definitions 	
			let oColumnListItems = oDynamicWorkflowTable.getBindingInfo("items").template;

			// get the table bindings and update the same 
			// 1. Column bindings
			let arrColumnAggregation = oDynamicWorkflowTable.getColumns();
			// Bug 12906 - added the sChangeDoc variable for the new argument on the _getBindingVariableForColumn
			let sChangeDoc = oDynamicWorkflowModel.getProperty("/dynamic/Changedoc");
			let sValueVariable = oController._getBindingVariableForColumn(oColumnInfo, sChangeDoc);
			let arrDynamicData = oDynamicWorkflowModel.getProperty("/dynamic/data");
			
			for (let iColumn = 0; iColumn < arrColumnAggregation.length; iColumn++) {
				let oCustomData = arrColumnAggregation[iColumn].getCustomData();
				if (oCustomData.length > 0 &&
					(oCustomData[0].getValue().attributeType === oColumnInfo.attributeType) &&
					(oCustomData[0].getValue().entity === oColumnInfo.entity) &&
					(oCustomData[0].getValue().attribute === oColumnInfo.attribute)
				) {
					//oDerivationTable.removeColumn(arrColumnAggregation[0]);
					oDynamicWorkflowTable.removeColumn(iColumn);
					oColumnListItems.removeCell(iColumn);
					
					//Clear all cell values for the deleted column
					arrDynamicData.forEach(oData => {
						delete oData[sValueVariable];
					});

					// Check if any more driving columns exist 
					let arrDrivingAttributes = arrColumnAggregation.find(function(oColumn){
						let oCustData = oColumn.getCustomData()[0].getValue();
						if( (oCustData.attributeType === "Driving") && 
							!(
								oCustData.entity === oColumnInfo.entity && 
								oCustData.attribute === oColumnInfo.attribute 
							)
						)
						{
							return true;
						}
						return false;
					});
					
					//Delete the 3 approver columns if user deletes the last Driving Attribute. And refresh data in model
					if (arrDrivingAttributes === undefined) {
						let iRemainingColumnCount = oDynamicWorkflowTable.getColumns().length;
						for (let iValidationColumn = 0; iValidationColumn < iRemainingColumnCount; iValidationColumn++) {
							oDynamicWorkflowTable.removeColumn(0);
							oColumnListItems.removeCell(0);
						}
						
						// Remove all the data from the table 
						oDynamicWorkflowModel.setProperty("/dynamic/data", []);
					}

					oDynamicWorkflowTable.bindItems({
						path: "/dynamic/data",
						model: "approverModel",
						template: oColumnListItems,
						templateShareable: true
					});

					oDynamicWorkflowModel.setProperty("/dynamic/tableHasColumns", oDynamicWorkflowTable.getColumns().length > 0);

					break;
				}
			}
		});
	};

	/**
	 * Called in Component Context this = oController 
	 * 
	 * Return the number of columns in the table. 
	 */
	oDynamicWorkflowObject.getRuleButtonState = function (bTableHasData) {

		let bState = (bTableHasData);
		return bState;
	};

	/**
	 * Internal function to create a column that will be added to the table. 
	 * 
	 * The Component handle and the column info are sent to this function as parameters. 
	 * Return : sap.m.Column with required data set
	 */
	oDynamicWorkflowObject._createColumnForTable = function (oColumnInfo) {
		let bColumnMandatory = false; 
		let bColumnVisible = true;
		let sChangedoc = this.JSONViewModel.getProperty("/dynamic/Changedoc");
		
		// Create a new column to be added to the aggregation
		let oHBox = new sap.m.HBox({
			alignItems: "Center",
			justifyContent: "SpaceBetween"
		});

		let sStyle = oColumnInfo.attributeType === "Driving" ? "Driving" : "Deriving";
		let sColumnHeaderText;
		if(sChangedoc==="AV"){
			sColumnHeaderText = oColumnInfo.entity + " - " + oColumnInfo.attribute;
		} else{
			sColumnHeaderText = oColumnInfo.header;
		}

		//Custom Header for Validation Columns
		if (oColumnInfo.attributeType === "StepActions") {
			sColumnHeaderText = "Step Actions";
			bColumnVisible = false;
		} else if (oColumnInfo.attributeType === "AppStep") {
			sColumnHeaderText = "App Step";
			bColumnVisible = false;
		} else if (oColumnInfo.attributeType === "Deriving"  &&  oColumnInfo.entity === "USER_TYPE") {
			sColumnHeaderText = "Approver Type";
			bColumnMandatory = true;
		} else if (oColumnInfo.attributeType === "Deriving"  &&  oColumnInfo.entity === "USER_VALUE") {
			sColumnHeaderText = "Approver";
			bColumnMandatory = true;
		} else if (oColumnInfo.attributeType === "Deriving"  &&  oColumnInfo.entity === "STEP_TYPE") {
			sColumnHeaderText = "Step Type";
			bColumnMandatory = true;
		} else if (oColumnInfo.attributeType === "Deriving"  &&  oColumnInfo.entity === "CR_STATUS") {
			sColumnHeaderText = "CR Status";
			bColumnMandatory = false;			
		} else if(oColumnInfo.entity === "Connections") {
			bColumnVisible = false;
		}

		let oColumnLabel = new sap.m.Label({
			text: sColumnHeaderText,
			wrapping: true,
			width: "100%",
			textAlign: sap.ui.core.TextAlign.Center,
			design: sap.m.LabelDesign.Standard,
			required: bColumnMandatory
		}).addStyleClass("tableColumnHeader" + sStyle);

		oHBox.addItem(oColumnLabel);

		// add the column delete button if the column is of type Driving or Deriving 
		// Bug 12906 - added the sChangedoc variable to the if to eliminate the delete button on the "attribute change" mode for columns
		if (oColumnInfo.attributeType === "Driving" && sChangedoc==="AV") {
			
			let oDeleteColBtn = new sap.m.Button({
				icon: "sap-icon://sys-cancel",
				type: "Transparent",
				tooltip: "Remove Column and it's contents"
			}).attachPress(oColumnInfo, this.removeAttributeColumn, this);

			oHBox.addItem(oDeleteColBtn);
		}

		let oColumn = new sap.m.Column({
			demandPopin: true,
			styleClass: "tableColumn" + sStyle,
			hAlign: "Center",
			header: oHBox
		});

		oColumn.setVisible(bColumnVisible);

		/* Feature 11971 - Data added to CustomData control */
		// Add the column custom data that differentiates the column type (Deriving / Driving)
		oColumn.addCustomData(new sap.ui.core.CustomData({
			key: "ColumnType",
			value: {
				attributeType: oColumnInfo.attributeType,
				attributeKind: oColumnInfo.attributeKind,
				attributeDataLenght: oColumnInfo.attributeDataLenght,
				attributeDecimals: oColumnInfo.attributeDecimals,
				attributeDataType: oColumnInfo.attributeDataType,
				entity: oColumnInfo.entity,
				attribute: oColumnInfo.attribute,
				mandatory: bColumnMandatory,
				visible: bColumnVisible,
				header: oColumnInfo.header
			}
		}));

		return oColumn;
	};

	/**
	 * Login to create the binding variable based on the Column Info provided. 
	 */
	// Bug 12906 - if the "compare by" mode is set to "attribute changes" then it sends "entity" and "attribute", otherwise it sends <entity>__<attribute>
	oDynamicWorkflowObject._getBindingVariableForColumn = function(oColumnInfo, sChangeDoc){
		let sValueVariable;
		if(oColumnInfo.attributeType === "Driving"){
			if(sChangeDoc === "AV"){
				sValueVariable = oColumnInfo.entity + "__" + oColumnInfo.attribute;
			} else {
				if(oColumnInfo.header === "Entity"){
					sValueVariable = "entity";
				} else {
					sValueVariable = "attribute";
				}
			}
		} else {
			sValueVariable = oColumnInfo.entity;
		}
		return sValueVariable;
	};
	
	/**
	 * Add a column to the Table with te provided details. 
	 */
	oDynamicWorkflowObject.addColumnToTable = function(oColumnInfo) {
		// Get model 
		let oDynamicWorkflowModel = this.JSONViewModel;
		let oCrTypeDetails = this.CRTypeDetails;
		let oController = this;
		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		let oDynamicWorkflowTable = sap.ui.getCore().byId(this.DynamicWorkflowFragmentId + "--" + "idDerivationTable");

		// get the table bindings and update the same 
		// 1. Column bindings
		let arrColumnAggregation = oDynamicWorkflowTable.getColumns();

		// Create a new column to be added to the aggregation
		let oColumn = this._createColumnForTable(oColumnInfo);
		oColumnInfo = oColumn.getCustomData()[0].getValue();

		/** 
		 * if the column is type Driving, insert the column at the end of the driving columns
		 * If the column is type Deriving, insert the column at the end
		 */
		let iInsertIndex = arrColumnAggregation.length;
		if (oColumnInfo.attributeType === "Driving") {
			// find the last driving column 
			for (let i = 0; i < arrColumnAggregation.length; i++) {
				if (arrColumnAggregation[i].getCustomData().length > 0 && arrColumnAggregation[i].getCustomData()[0].getValue().attributeType !==
					"Driving") {
					iInsertIndex = i;
					break;
				}
			}
		}
		oDynamicWorkflowTable.insertColumn(oColumn, iInsertIndex);

		// 1. item / cell binding
		let oColumnListItems = oDynamicWorkflowTable.getBindingInfo("items").template;

		/**
		 * Insert the new item at the index calculated for the column
		 * 1. Add an Input component : to be shown when the number of items in the list is zero 
		 * 2. Add a Select component : to be shown when the number of items in the list is greater than zero 
		 */
		let oHBox = new sap.m.HBox();
		// Bug 12906 - added the sChangeDoc variable for the new argument on the _getBindingVariableForColumn
		let sChangeDoc = oDynamicWorkflowModel.getProperty("/dynamic/Changedoc");
		let sValueVariable = this._getBindingVariableForColumn(oColumnInfo, sChangeDoc);
		
		let sValueBinding = "{approverModel>" + sValueVariable + "}";
		let sOperatorBinding = "{approverModel>" + sValueVariable + "-Operator}";
		oDynamicWorkflowModel.setProperty("/dynamic/lists/" + sValueVariable, []);

		//Feature/12334 - Select created to be added in attribute columns and be able to choose an operator
		let oSelOperatorComponent = new sap.m.Select({
			autoAdjustWidth: true,
			selectedKey: sOperatorBinding,
			visible: "{= ${approverModel>defaultStep} !== 'X' && ${approverModel>/dynamic/Changedoc} === 'AV'}",
			change: function(oEvent){
				// Bug 12606 - Removing value when operator is changed
				let sPath = oEvent.getSource().getParent().getParent().getBindingContext("approverModel").getPath();
				oDynamicWorkflowModel.setProperty(sPath + "/" + oColumnInfo.entity + "__" + oColumnInfo.attribute, "");
			},
			items: [
				new sap.ui.core.Item({
					key: "EQ",
					text: "="
				}),
				new sap.ui.core.Item({
					key: "NE",
					text: "!="
				}),
				new sap.ui.core.Item({
					key: "GT",
					text: ">"
				}),
				new sap.ui.core.Item({
					key: "GE",
					text: ">="
				}),
				new sap.ui.core.Item({
					key: "LT",
					text: "<"
				}),
				new sap.ui.core.Item({
					key: "LE",
					text: "<="
				}),
				new sap.ui.core.Item({
					key: "CP",
					text: "CP"
				}),
				new sap.ui.core.Item({
					key: "NP",
					text: "NP"
				})
			]
		});

		// Add the input field only for Driving attributes. The deriving attributes in this case all have lists to select from.
		
		// Calculate the Attribute visible state. Driving Attribute and AppStep for the default row are not visible
		// Feature/12451 - Condition was added to show between an input and a combobox according to the selected operator
		let sColumnVisibleCondition = "(${approverModel>/dynamic/lists/" + sValueVariable + "}.length === 0 || $" + sOperatorBinding + " === 'CP' || $" + sOperatorBinding + " === 'NP')";

		// Feature/12185 - condition for binding expression was changed to allo showing the input field
		if (oColumnInfo.attributeType === "Driving" || oColumnInfo.attributeType === "AppStep") {
			sColumnVisibleCondition += "&& ${approverModel>defaultStep} !== 'X'";
			oHBox.addItem(oSelOperatorComponent);
		}
		sColumnVisibleCondition = "{= " + sColumnVisibleCondition + "}";

		let oInputComponent;
		if(oColumnInfo.attributeDataType === "DATS") {
			oInputComponent = new sap.m.DatePicker({
				width: "100%",
				value: sValueBinding,
				displayFormat: "M/d/yyyy",
				valueFormat: "yyyyMMdd",
				visible: sColumnVisibleCondition
			});
		} else if(oColumnInfo.entity === "USER_VALUE"){
			oInputComponent = new sap.m.Input({
				fieldWidth: "100%",
				value: sValueBinding,
				visible: sColumnVisibleCondition,
				maxLength: Number(oColumnInfo.attributeDataLenght),
				enabled: "{approverModel>ebApprover}"
			});
		} else if(oColumnInfo.header){
			let sProperty = (oColumnInfo.header.toLowerCase()==="entity" ? "entity" : "attribute");

			oInputComponent = new sap.m.Input({
				fieldWidth: "100%",
				value: "{approverModel>" + sProperty + "}",
				visible: "{= ${approverModel>/dynamic/Changedoc}==='AC' && ${approverModel>defaultStep} !== 'X'}",
				enabled: false
			});
 		} else{
			/* Feature 11971 - Property maxLength was added */
			// Feature/12185 - showSuggestion property was added
			oInputComponent = new sap.m.Input({
				fieldWidth: "100%",
				value: sValueBinding,
				visible: sColumnVisibleCondition,
				maxLength: Number(oColumnInfo.attributeDataLenght)
			});
			
		}
		
		// If the column is ApprVal, attach the live change and showSuggestions
		if(oColumnInfo.entity === "USER_VALUE") {
			oInputComponent
				// Bug 11381 - Remove duplicate users from ct user agent group for parallel workflow processing in dynamic badi	
				.attachChange(oInputComponent, this.approverChangeHandler, this)
				.attachLiveChange(oInputComponent, this.approverLiveChangeHandler, this)
				// enable suggestions and clear previous suggestions 
			 	.setShowSuggestion(true)
			 	.setAutocomplete(false)
				.setStartSuggestion(0);
		}else if(oColumnInfo.attributeDataType === "DATS"){
			oInputComponent.attachChange(oController.changeDatePickerValue, oController);
		}else{
			/* Feature 11971 - Function attached to the change event */
			oInputComponent.attachChange(oColumnInfo, this._changeCellValue, oController);
		}

		oInputComponent.setLayoutData(new sap.m.FlexItemData({growFactor: 4}));
		oHBox.addItem(oInputComponent);

		// Retreive and update the data list
		let promiseValueList = undefined;
		let sItemKey = "";
		let sItemText = "";
		if(oColumnInfo.attributeType === "Driving" && (!this.Node.getProperty("group").includes("parallel") || !this.Node.getProperty("group").includes("dynamic")) )
		{
			sItemKey = "Key";
			sItemText = "Ddtext";
			promiseValueList =
				this.ParentController.GetListData.getAttributeValuesList(
						this.ParentController, oCrTypeDetails.DataModel, oColumnInfo.entity, oColumnInfo.attribute, undefined);

		} else {
			// In case of the driving or StepActions attributes map with the appropriate retrieve calls 
			switch(oColumnInfo.entity) {
				case "USER_TYPE":
					sItemKey = "key";
					sItemText = "text";
					promiseValueList = this.ParentController.getApproverTypeList();

					for (let index = 0; index < iInsertIndex; index++) {						
						if(oDynamicWorkflowModel.getProperty("/dynamic/data/"+index+"/USER_TYPE")==="NA"){
							oColumnListItems.getCells()[index].getItems()[0].setEnabled(false);
						}
					}
				break;
					// Do not want to show the combox box, we want only the input to be visible. 
					// For ApprVal, the suggestion will allow the user to select
					// For StepActions, the data is set on steptype selection ... 
				case "STEP_TYPE":
					sItemKey = "UsmdCrStype";
					sItemText = "UsmdStDesc";
					promiseValueList = this.ParentController.GetListData.getWFConfig(this.ParentController, "/STEPTYPESet");
				break;
					// Need add 1 more in the case new column is added on the 
					// oDynamicWorkflowObject.derivingAttributeSelectionChange
				case "CR_STATUS":
					sItemKey = "UsmdCreqStatus";
					sItemText = "Txtmi";
					promiseValueList = this.ParentController.GetListData.getWFConfig(this.ParentController, "/CRSTATUSSet");
				break;		
							
				case "USER_VALUE":
				case "StepActions":
				case "Connections":
				case "APPSTEP":
				default:
					sItemKey = "key";
					sItemText = "text";
					promiseValueList = new Promise(function(resolve){
						resolve([
							]);
					});
				break;
				}
		}
		// Feature/12451 - sOperatorBinding was added to the parameters to obtain the operator
		let oSelectComponent = this._createComboBoxForTableRow(sValueBinding, sValueVariable, sItemKey, sItemText, this, oColumnInfo);
		
		// Task 12158 - Able to enter invalid values for attributes that have a combo box in parallel/dynamic workflow
		if (oColumnInfo.attributeType === "Driving") {
			oSelectComponent.attachChange(oColumnInfo.entity, this.drivingAttributeChangeHandler, this);
		// If the column is ApprType, attach the selection Change event to clear the approver value
	    } else if (oColumnInfo.entity === "USER_TYPE" || oColumnInfo.entity === "STEP_TYPE" || oColumnInfo.entity === "CR_STATUS" ) {
			oSelectComponent
				.attachSelectionChange(oColumnInfo.entity, this.derivingAttributeSelectionChange, this);
}
			oSelectComponent.setLayoutData(new sap.m.FlexItemData({growFactor: 4}));
			oHBox.addItem(oSelectComponent);
				
		promiseValueList.then(function (oValueList) {
			if(oColumnInfo.entity === "USER_TYPE") {
				if (oController.WorkflowType === DMRNode.Types.Dynamic) {
					oValueList.push({
						key: "BG",
						text: "Background Step"
					});

					// Task 11631 - System method correction for background step - UI
					oValueList.push({
						key: "SM",
						text: "System Method"
					});
				}
				
			}
				let arrValueList = oValueList;
				/**
				 * Some functions return the data in oData -> results ... check if "results" exist and then point the model to that
				 */
				if(oValueList.results){
					arrValueList = oValueList.results;
				}
				oDynamicWorkflowModel.setProperty("/dynamic/lists/" + sValueVariable, arrValueList);
		});
		
		oColumnListItems.insertCell(oHBox, iInsertIndex);

		oDynamicWorkflowTable.bindItems({
			path: "/dynamic/data",
			model: "approverModel",
			template: oColumnListItems,
			templateShareable: true
		});
		
		let iColumnCount = oDynamicWorkflowTable.getColumns().length;
		oDynamicWorkflowModel.setProperty("/dynamic/tableHasColumns", iColumnCount > 0);
		
		return iColumnCount;
	};

	/* Feature 11971 -	Condition "N" was modified to not allow letters and spetial characters
						Condition "P" was modified to not allow NaN value
						Condition "I" was added to rounded the Int values
						Condition "F" was added to compare the value with a regular expression 
						Message was added to inform about wrong value and when this occurs the field is cleaned*/
	oDynamicWorkflowObject._changeCellValue = function(oEvent, oColumnInfo){
		let oDynamicWorkflowModel = this.JSONViewModel;
		let sPath = oEvent.getSource().getParent().getParent().getBindingContext("approverModel").getPath();
		// Bug 12606 - Operator is obtained to evaluate which regex needs to be used
		let sOperator = oDynamicWorkflowModel.getProperty(sPath + "/" + oColumnInfo.entity + "__" + oColumnInfo.attribute + "-Operator");
		let regexNum = "^[0-9]+$";
		let regexFLTP = "^([+-]?\\.?[0-9]+([+-]?(e|E))?)+$";
		// Bug 12606 - Regex for Pattern were added
		let regexNumPattern = "^(\\*?[0-9]+\\*?)+$";
		let regexDecPattern = "^(\\*?\\.?[0-9]+\\.?\\*?)+$";
		let regexFLTPPattern = "^(\\*?[+-]?\\.?[0-9]*\\*?([+-]?(e|E))?\\*?)+$";
		let sCellValue = oEvent.getParameter("value");
		let iCellValue = Number(sCellValue);
		let flagInvalid = false;
		if(oColumnInfo.attributeKind === "C") {
			oEvent.getSource().setValue(sCellValue.toUpperCase());
		} else if(oColumnInfo.attributeKind === "N") {
			// Bug 12606 - Conditions were added to evaluate if the selected operator was CP or NP and use other regex which allows asterisks
			if (sOperator === "CP" || sOperator === "NP") {
				if(!sCellValue.match(regexNumPattern) && sCellValue.trim() !== ""){
					oEvent.getSource().setValue();
					flagInvalid = true;
				}
			}else{
				if(iCellValue.toFixed(0).toString().match(regexNum) && sCellValue.trim() !== ""){
					oEvent.getSource().setValue(iCellValue.toFixed(0).toString().padStart(Number(oColumnInfo.attributeDataLenght), 0));
				}else{
					oEvent.getSource().setValue();
					flagInvalid = true;
				}
			}
		} else if(oColumnInfo.attributeKind === "P") {
			if (sOperator === "CP" || sOperator === "NP") {
				if(!sCellValue.match(regexDecPattern) && sCellValue.trim() !== ""){
					oEvent.getSource().setValue();
					flagInvalid = true;
				}
			}else{
				if(isNaN(iCellValue) || sCellValue.trim() === ""){
					oEvent.getSource().setValue();
					flagInvalid = true;
				}else{
					let cellValueFixed = iCellValue.toFixed(Number(oColumnInfo.attributeDecimals)).toString().replace(".", "");
					// Task 11217 - Value Conversion and Data Check for Single value rules and properties
					// This fixed the bug for BP_SALES.ANTLF Attribute, which is Decimal with lenght 1
					if (cellValueFixed.length === 1 && Number(oColumnInfo.attributeDataLenght) === 1) {
						oEvent.getSource().setValue(Number(iCellValue));
					} else if(cellValueFixed.length >= Number(oColumnInfo.attributeDataLenght)) {
						// Bug 12157 - Input validation for BP_COMPNY - KULTG
						// Only apply this formula if the data type is decimal and oColumnInfo.attributeDecimals is greater than zero
						if (Number(oColumnInfo.attributeDecimals) > 0) {
							let newValue = cellValueFixed.slice(0, (oColumnInfo.attributeDataLenght - (Number(oColumnInfo.attributeDecimals) + 1))) + "."
											+ cellValueFixed.slice((oColumnInfo.attributeDataLenght - (Number(oColumnInfo.attributeDecimals) + 1)), oColumnInfo.attributeDataLenght - 1);
							oEvent.getSource().setValue(Number(newValue).toFixed(Number(oColumnInfo.attributeDecimals)));
						}
					}else{
						oEvent.getSource().setValue(iCellValue.toFixed(Number(oColumnInfo.attributeDecimals)));
					}
				}
			}
		} else if(oColumnInfo.attributeKind === "I" || oColumnInfo.attributeKind === "D" || oColumnInfo.attributeKind === "T") {
			if(iCellValue.toFixed(0).toString().match(regexNum) && sCellValue.trim() !== ""){
				oEvent.getSource().setValue(iCellValue.toFixed(0));
			}else{
				oEvent.getSource().setValue();
				flagInvalid = true;
			}
		} else if(oColumnInfo.attributeKind === "F"){
			if (((sOperator === "CP" || sOperator === "NP") && sCellValue.match(regexFLTPPattern)) || sCellValue.match(regexFLTP) && sCellValue.trim() !== "") {
				oEvent.getSource().setValue(sCellValue.toUpperCase());
			}else{
				oEvent.getSource().setValue();
				flagInvalid = true;
			}
		}

		if(flagInvalid){			
			MessageToast.show("Enter a valid value", {
				at: "center top",
				offset: "0 200"
			});
		}
	};

	// Feature/12185 - _liveChangeCellValue created to request new suggestion values in the momment that the value is changed
	oDynamicWorkflowObject._liveChangeCellValue = function(oEvent, oColumnInfo){
		let oInput = oEvent.getSource();
		let sValue = oEvent.getParameter("value");

		// Bug 12854 - added conditional to validate the value and the selected Item
		let aSuggestionItems = oInput.getSuggestionItems();
		let oSelectedItem = aSuggestionItems.find(item => item.getText() === sValue);
		if(!sValue){
			oInput.setValueState("Information");
			oInput.setValueStateText("Type to refresh list");
		}else if (sValue && !oSelectedItem) {
			oInput.setValueState("Warning");
			oInput.setValueStateText("The selected value is not in the list");
		} 
		clearTimeout(this.timeout);
		this.timeout = setTimeout(() => {
			oInput.removeAllSuggestionItems();
			let oCrTypeDetails = this.CRTypeDetails;
			let promiseValueList =
			this.ParentController.GetListData.getAttributeValuesList(
			this.ParentController, oCrTypeDetails.DataModel, oColumnInfo.entity, oColumnInfo.attribute, sValue);
			
			
			promiseValueList.then(function (oValueList) {
				let arrValueList = oValueList;
				/**
				 * Some functions return the data in oData -> results ... check if "results" exist and then point the model to that
				*/
				if(oValueList.results){
					arrValueList = oValueList.results;
				}
				
				arrValueList.forEach(item => {
					oInput.addSuggestionItem(new sap.ui.core.Item({
						key: item.Key,
						text: item.Key + (item.Ddtext.length > 0 ? " - " : "") + item.Ddtext
					}));
				});
				// Bug 13190 - Execute change event once the suggestion list is ready
				oInput.fireChange({value: sValue});
			});
		}, 300);
	};
	
	/*
	 * Task 12158 - Able to enter invalid values for attributes that have a combo box in parallel/dynamic workflow
	 * Check if the value provided in the driving attribute contains in the list
	 * If not, then show a value state text message
     */
	oDynamicWorkflowObject.drivingAttributeChangeHandler = function(oEvent) {
		let oValidatedComboBox = oEvent.getSource();
		let aSuggestionItems = oValidatedComboBox.getSuggestionItems();
		let sValue = oValidatedComboBox.getValue();
		let oSelectedItem = aSuggestionItems.find(item => item.getText() === sValue);
		// Fix 12574 - Condition was changed to validate the selected value
		if (sValue && !oSelectedItem) {
			oValidatedComboBox.setValueState("Warning");
			oValidatedComboBox.setValueStateText("The selected value is not in the list");
		}else{
			oValidatedComboBox.setValueState("Information");
			oValidatedComboBox.setValueStateText("Type to refresh list");
		}
	};

	oDynamicWorkflowObject.changeDatePickerValue = function (oEvent) {
		let oDatePicker = oEvent.getSource();
		let flagValid = oDatePicker.isValidValue();
		if (!flagValid) {
			oDatePicker.setValue();
			MessageToast.show("Enter correct format for dates", {
				at: "center top",
				offset: "0 200"
			});
		}
	};

	oDynamicWorkflowObject.derivingAttributeSelectionChange = function(oEvent, sAttributeType){
		let oDynamicWorkflowModel = this.JSONViewModel;
		// Get the binding path for the item. Path to the row on which the item exists 
		let sBindingPathContext = oEvent.getSource().getBindingContext("approverModel").getPath();
		let sourceComboBox = oEvent.getSource();
		let oColumnListItem = sourceComboBox.getParent().getParent();
		let iColumnIndex = oColumnListItem.indexOfCell(sourceComboBox.getParent());
		let sUserType = oDynamicWorkflowModel.getProperty(sBindingPathContext+"/USER_TYPE");

		let sSelectedStepType;
		switch(sAttributeType){
			case "USER_TYPE":
				// The first item in the vBox is the combobox
				let oApproverInput = oColumnListItem.getCells()[iColumnIndex + 1].getItems()[0];
				let oCbxStepType = oColumnListItem.getCells()[iColumnIndex + 2].getItems()[1];
				oApproverInput.setValue("");
				oApproverInput.removeAllSuggestionItems();
				// Bug 12188 - Able to enter invalid data in Approver field for parallel & dynamic workflows
				oDynamicWorkflowModel.setProperty(sBindingPathContext + "/USER_VALID_VALUES", []);
				if(sourceComboBox.getSelectedKey()!=="NA"){
					oDynamicWorkflowModel.setProperty(sBindingPathContext + "/ebApprover", true);
					// oApproverInput.setEnabled(true);
					let promise = this.ParentController.getApproverList(sourceComboBox.getSelectedKey(), "*");
					promise.then(function(arrApproverList){
						// Task 11631 - System method correction for background step - UI
						let arrApproverSanitizedList = arrApproverList;
						if (sourceComboBox.getSelectedKey() === "BG") {
							arrApproverSanitizedList = arrApproverList.filter(oApprover => {
								return oApprover.key !== "SM";
							});
						}
						
						arrApproverSanitizedList.forEach(function(apprVal){
						//  Change for remove additional text and add together key separated by -	
							oApproverInput.addSuggestionItem(new sap.ui.core.ListItem({ key: apprVal.key, text: apprVal.key + (apprVal.text ? " - " : "") + apprVal.text }));

						});
						
						// Bug 12188 - Able to enter invalid data in Approver field for parallel & dynamic workflows
						oDynamicWorkflowModel.setProperty(sBindingPathContext + "/USER_VALID_VALUES", arrApproverSanitizedList);
					});
					// Task 11631 - System method correction for background step - UI
					promise.catch(function(oError) {
						try {
							let sCode = JSON.parse(oError.responseText).error.innererror.errordetails[0].code;
							let sBusinessMessage = JSON.parse(oError.responseText).error.innererror.errordetails[0].message;
							Utilities.showPopupAlert("Error code:\n" + sCode + "\n\n" + "Error message:\n" + sBusinessMessage, MessageBox.Icon.ERROR, "Error");
						} catch (error) {
							let sGenericMessage = oError.statusCode + ": " + oError.message;
							Utilities.showPopupAlert("Error " + sGenericMessage, MessageBox.Icon.ERROR, "Error");
						}
					});

					//Bug 12817 - Added condition to fire the step type event when the approver type is changed
					if (oCbxStepType.getSelectedKey()) {
						oCbxStepType.fireSelectionChange();
					}

				} else {
					// Bug 12798 - Set step type value equals "2" in case the approver type is equals "NA"
					oCbxStepType.setSelectedKey("2");
					// Bug 12808 - Executing the event after changing the value
					oCbxStepType.fireSelectionChange();
					oDynamicWorkflowModel.setProperty(sBindingPathContext + "/ebApprover", false);
					oApproverInput.setValue("Target State Routing");
				}
				break;
				case "STEP_TYPE":
				sSelectedStepType = sourceComboBox.getSelectedKey();
				if(sSelectedStepType === "Custom") {
					oDynamicWorkflowModel
						.setProperty("/CreateStepTypeFragment", 
						{
							BindingPath: sBindingPathContext,
							stepType: undefined,
							stepTypeValueState: sap.ui.core.ValueState.Error,
							stepTypeValueStateText: "Step Type is mandatory",
							stepActions: []
						});
					oDynamicWorkflowModel.setProperty(sBindingPathContext + "/StepActions", undefined);
					oDynamicWorkflowModel.setProperty(sBindingPathContext + "/Connections", undefined);
					this.initializeCreateStepType();
				} else {
					let arrActionList;
					if(sSelectedStepType==="2" && sUserType==="NA"){
						arrActionList = this.ParentController.getStepActionsForType(sSelectedStepType)[0];
					} else{
						arrActionList = this.ParentController.getStepActionsForType(sSelectedStepType);
					}
					// Find the actions column and set the value to it.
					// Clear the "Connections" Column Data. Connections column is placed after AppStep Column
					// Need add 1 more in the case new column is added
					let nCola =  2;
					let nColb =  3;
					if(this.WorkflowType === DMRNode.Types.Dynamic){
						nCola++;
						nColb++; 
					}

					let oConnectionsInput = oColumnListItem.getCells()[iColumnIndex + nCola].getItems()[0];
					oConnectionsInput.setValue("");
					
					// Actions Column is 4 indices after Step Type
					// Need add 1 more in the case new column is added
					let oActionInput = oColumnListItem.getCells()[iColumnIndex + nColb].getItems()[0];
					oActionInput.setValue(arrActionList.toString());
					oActionInput.setEditable(false);
				}
				break;

				case "CR_STATUS":
					sSelectedStepType = sourceComboBox.getSelectedKey();
					if(sSelectedStepType === "Custom") {
						oDynamicWorkflowModel
							.setProperty("/StepStatusDetailsFragment", 
							{
								BindingPath: sBindingPathContext,
								stepType: undefined,
								stepTypeValueState: sap.ui.core.ValueState.Error,
								stepTypeValueStateText: "CR Status is mandatory",
								stepActions: []
							});
						oDynamicWorkflowModel.setProperty(sBindingPathContext + "CRStatus", undefined);
						oDynamicWorkflowModel.setProperty(sBindingPathContext + "CR_OBMAIN", undefined);
						this.initializeCRStatus();
					} 
				break;
				default:
					// No actions required
					break;		

		}
	};

	oDynamicWorkflowObject.initializeCreateStepType = function() {
		if (!this.CreateStepTypeDialog) {			
			this.CreateStepTypeDialog =
				sap.ui.xmlfragment(
					this.DynamicWorkflowFragmentId,
					"dmr.mdg.supernova.SupernovaFJ.view.Workflow.CreateStepType",
					this.ParentController);
			this.ParentController.getView().addDependent(this.CreateStepTypeDialog);
		}
		this.CreateStepTypeDialog.open();
	};


	oDynamicWorkflowObject.initializeCRStatus = function() {

		if (!this.CreateCRDialog) {			
			this.CreateCRDialog =
				sap.ui.xmlfragment(
					"StepStatusCreate",
					"dmr.mdg.supernova.SupernovaFJ.view.Workflow.StepStatusCreate",
					this.ParentController);
			this.ParentController.getView().addDependent(this.CreateCRDialog);
		}
		this.CreateCRDialog.open();

	};


	oDynamicWorkflowObject.onLiveChangeCreateStepType = function(oEvent) {
		let sSelectedStepType = oEvent.getSource().getValue();
		let oCreateStepTypeDetails = this.getApproverModel().getProperty("/CreateStepTypeFragment");
		let arrStepTypeList = this.getApproverModel().getProperty("/stepType/results");
		if(sSelectedStepType) {
			let oMatchedStepType = arrStepTypeList.find(oStepType => {
				return oStepType.UsmdCrStype === sSelectedStepType;
			});
			if(oMatchedStepType) {
				oCreateStepTypeDetails.stepTypeValueState = sap.ui.core.ValueState.Error;
				oCreateStepTypeDetails.stepTypeValueStateText = "Step Type already exists";
			} else {
				oCreateStepTypeDetails.stepTypeValueState = sap.ui.core.ValueState.None;
				oCreateStepTypeDetails.stepTypeValueStateText = undefined;
			}
		} else {
			oCreateStepTypeDetails.stepTypeValueState = sap.ui.core.ValueState.Error;
			oCreateStepTypeDetails.stepTypeValueStateText = "Step Type is mandatory";
		}	
		this.getApproverModel().refresh();
	};

	oDynamicWorkflowObject.stepTypeCreateEnabled = function(sValueState) {
		if(sValueState === sap.ui.core.ValueState.Error) {
			return false;
		} else {
			return true;
		}
		
	};

	oDynamicWorkflowObject.stepTypeCreateClicked = function() {
		let oController = this;
		let oApproverModel = oController.getApproverModel();
		let oCreateStepTypeDetails = oApproverModel.getProperty("/CreateStepTypeFragment");
		// Open the transport selection dialog
		let oTransportPromise = oController._selectTransportPackage(false, true, false, false);
		oTransportPromise.then(function (oResponseSelected) {

			// Create the service packet
			let oServiceData = {
				UsmdCrStype: oCreateStepTypeDetails.stepType,
				UsmdStDesc: oCreateStepTypeDetails.Desc,
				Transport: oResponseSelected.workbenchTransport,
				Message: "",
				Type: "",
				STEPTYPETOACTION: []
			};

			// Fill the actions into the service packet
			oCreateStepTypeDetails.stepActions.forEach(function (action) {
				let STEPTYPETOACTION = {};
				STEPTYPETOACTION.UsmdCrAction = action;
				STEPTYPETOACTION.UsmdSequenceNr = oServiceData.STEPTYPETOACTION.length.toString();
				STEPTYPETOACTION.UsmdCrStype = oCreateStepTypeDetails.stepType;
				oServiceData.STEPTYPETOACTION.push(STEPTYPETOACTION);
			});

			let promiseShowPopupAlert;

			(new DMRDataService(
				oController,
				"GETWFCFG",
				"/STEPTYPESet",
				"stepType",
				"/", // Root of the received data
				"Create Step Type"
			)).saveData(
				false,
				oServiceData,
				null, {
					success: {
						fCallback: function (oParams, oResponseData) {
							promiseShowPopupAlert =
								Utilities.showPopupAlert(oResponseData.Message, MessageBox.Icon.INFORMATION, "Step Type Created");
							promiseShowPopupAlert.then(function () {
								let promiseStepType = GetListsData.getWFConfig(oController, "/STEPTYPESet");
								promiseStepType.then(
									function (oData) {
										oApproverModel.setProperty("/stepType", oData);
										oApproverModel.setProperty("/dynamic/lists/STEP_TYPE", oData.results);
										oApproverModel.setProperty(oCreateStepTypeDetails.BindingPath + "/STEP_TYPE", oResponseData.UsmdCrStype);
										let arrActionList = oController.getStepActionsForType(oResponseData.UsmdCrStype);
										oApproverModel.setProperty(oCreateStepTypeDetails.BindingPath + "/StepActions", arrActionList.toString());							
										oApproverModel.setProperty("/CreateStepTypeFragment", {});
										oController.DynamicWorkflowDialog.CreateStepTypeDialog.close();
									}
								);
							});

						},
						oParam: oController
					},
					error: {
						fCallback: function (oParams, oErrorData) {
							promiseShowPopupAlert =
								Utilities.showPopupAlert(oErrorData.Message, MessageBox.Icon.ERROR, "Step Type Created");
							promiseShowPopupAlert.then(function () {
								oApproverModel.setProperty(oCreateStepTypeDetails.BindingPath + "/STEP_TYPE", undefined);
								oApproverModel.setProperty("/CreateStepTypeFragment", {});
							});
						}
					}
				});
		});
	};

	oDynamicWorkflowObject.stepTypeCancelClicked = function() {
		let oCreateStepTypeDetails = this.getApproverModel().getProperty("/CreateStepTypeFragment");
		this.getApproverModel().setProperty(oCreateStepTypeDetails.BindingPath + "/STEP_TYPE", undefined);
		this.getApproverModel().setProperty("/CreateStepTypeFragment", {});
		this.DynamicWorkflowDialog.CreateStepTypeDialog.close();
	};
	
	oDynamicWorkflowObject.approverChangeHandler = function(oEvent) {
		// Bug 11381 - Remove duplicate users from ct user agent group for parallel workflow processing in dynamic badi
		oEvent.getSource().setValue(oEvent.getSource().getValue().toUpperCase());
		
		let oDynamicWorkflowModel = this.JSONViewModel;
		let arrColumnInfo = this._workflowGetColumnsAndValues(oDynamicWorkflowModel);
		
		let bDuplicatedEntries = this._workflowCheckDuplicatedEntries(this.WorkflowType, arrColumnInfo, oDynamicWorkflowModel);
		
		if (bDuplicatedEntries) {
			MessageToast.show("Duplicated entries are not allowed", {
				at: "center center",
				offset: "0 -75"
			});
		}

		// Bug 12188 - Able to enter invalid data in Approver field for parallel & dynamic workflows
		let sBindingPathContext = oEvent.getSource().getBindingContext("approverModel").getPath();
		let sApprValInput = oEvent.getSource();
		let sApproverText = sApprValInput.getValue();
		let oColumnListItem = sApprValInput.getParent().getParent();
		let iIndexOfAppr = oColumnListItem.indexOfCell(sApprValInput.getParent());
		
		let oApproverTypeComboBox = oColumnListItem.getCells()[iIndexOfAppr - 1].getItems()[1];
		let sSelectedApproverType = oApproverTypeComboBox.getSelectedKey();
		
		if(sSelectedApproverType!=="NA"){
		sApprValInput.setValueState("Error");
		sApprValInput.setValueStateText("Please select a value from the list");
		oDynamicWorkflowModel.setProperty(sBindingPathContext + "/USER_VALID_VALUES", []);
			let promiseApproverList = this.ParentController.getApproverList(sSelectedApproverType, sApproverText.length === 0 ? "" : sApproverText.split(" - ")[0].toUpperCase(), 20);
			promiseApproverList.then(function(approverList) {
				 // Task 11631 - System method correction for background step - UI
				let arrApproverSanitizedList = approverList;
				if (sSelectedApproverType === "BG") {
					arrApproverSanitizedList = approverList.filter(oApprover => {
						return oApprover.key !== "SM";
					});
				}
				
				let oExactMatch = approverList.find(oApprover => {
					let aValue = sApproverText;
					if(sApproverText.length > 0) {
						aValue = sApproverText.includes(" - ")? sApproverText.split(" - ")[0].trim(): sApproverText;
					}
					return oApprover.key.toUpperCase() === aValue.toUpperCase();
				});
				if (oExactMatch) {
					sApprValInput.setValueState("None");
					sApprValInput.setValueStateText("");
					oDynamicWorkflowModel.setProperty(sBindingPathContext + "/USER_VALID_VALUES", arrApproverSanitizedList);
					oDynamicWorkflowModel.setProperty(sBindingPathContext + "/ebApprover", true);
				}
			});
		} else{
			oDynamicWorkflowModel.setProperty(sBindingPathContext + "/ebApprover", false);
		}
	};
	
	oDynamicWorkflowObject.approverLiveChangeHandler = function(oEvent) {
		/** Get the index of the cell 
		 *		This is the hierarchy for the approverType and Approver Cells 
		 *  		Table -> ColumnListItem -> vBox -> ComboBox
		 *  The Approver cell is 1 index after the ApproverType Cell
		 */
		let sApprValInput = oEvent.getSource();
		let sApproverText = sApprValInput.getValue();
		let oColumnListItem = sApprValInput.getParent().getParent();
		let iIndexOfAppr = oColumnListItem.indexOfCell(sApprValInput.getParent());
		
		let oApproverTypeComboBox = oColumnListItem.getCells()[iIndexOfAppr - 1].getItems()[1];
		let sSelectedApproverType = oApproverTypeComboBox.getSelectedKey();
		
		if(sSelectedApproverType!=="NA"){
		let promiseApproverList = this.ParentController.getApproverList(sSelectedApproverType, sApproverText.length === 0 ? "" : sApproverText.split(" - ")[0].toUpperCase(), 20);
			promiseApproverList.then(function(approverList) {
				 // Task 11631 - System method correction for background step - UI
				let arrApproverSanitizedList = approverList;
				if (sSelectedApproverType === "BG") {
					arrApproverSanitizedList = approverList.filter(oApprover => {
						return oApprover.key !== "SM";
					});
				}
	
				sApprValInput.removeAllSuggestionItems();
				arrApproverSanitizedList.forEach(function(apprVal) {
					//  Change for remove additional text and add together key separated by -
					sApprValInput.addSuggestionItem(new sap.ui.core.ListItem({ key: apprVal.key, text: apprVal.key + (apprVal.text ? " - " : "") +  apprVal.text }));
				 });
				 sApprValInput.setFilterSuggests(arrApproverSanitizedList.length > 20);
			});
		}
	};

	/**
	 * Create a combo box component and assign the appropriate binding 
	 */
	oDynamicWorkflowObject._createComboBoxForTableRow = function(sValueBinding, sListDataProperty, sItemKey, sItemText, oComponent, oColumnInfo){
		/* Add a list box to the cell. Map the data to the global list items */
		let oSelectComponent;
		
		if(oColumnInfo.attributeType === "Driving") {
			oSelectComponent = new sap.m.Input({
				width: "100%",
				fieldWidth: "100%",
				value: sValueBinding,
				valueLiveUpdate: true,
				autocomplete: false,
				showSuggestion: true,
				//Feature 12559 - Properties added to show suggestions and to differentiate the input fields
				startSuggestion: 0,
				valueState: "Information",
				valueStateText: "Type to refresh list",
				// Task 12467 - Add support to entering patterns when CP and NP are selected
				visible: "{= ${approverModel>/dynamic/lists/" + sListDataProperty + "}.length !== 0 && ${approverModel>/dynamic/Changedoc} === 'AV' && ${approverModel>defaultStep} !== 'X' && ${approverModel>" + sListDataProperty + "-Operator} !== 'CP' && ${approverModel>" + sListDataProperty + "-Operator} !== 'NP'}",
				type: "Text"
			}).attachLiveChange(oColumnInfo, oComponent._liveChangeCellValue, oComponent)
				//Feature 12559 - Event added to fire the liveChange event method
				.addEventDelegate({
					onfocusin: function(oEvent) {
						let sValue = oEvent.srcControl.getValue();
						let bPopoverOpened = oEvent.srcControl._isSuggestionsPopoverOpen();
						//Fix 12562 - Condition was added to not fire the liveChange event when there is a value
						if (!sValue && !bPopoverOpened) {
							oEvent.srcControl.fireLiveChange();
						}
					}
				});
		} else {

			// Set the default condition for the component visibility. List should not be empty.
			let sComponentVisible = "{= ${approverModel>/dynamic/lists/" + sListDataProperty + "}.length !== 0}";
			// bug/13592: Hide the CR_STATUS column for the default row. There is no impact of the selection. Avoid allowing the user to select
			if(oColumnInfo.entity === "CR_STATUS" && oColumnInfo.attributeType === "Deriving"){
				sComponentVisible = "{= ${approverModel>defaultStep} !== 'X'}";
			}

			oSelectComponent = new sap.m.ComboBox({
				selectedKey: sValueBinding,
				width: "100%",
				visible: sComponentVisible,
				// Bug 12798 - Disable the step type field in case the approver type is equals "NA"
				enabled: oColumnInfo.entity === "STEP_TYPE" ? "{approverModel>ebApprover}" : true
			});

			let sKeyMapping = "{approverModel>" + sItemKey + "}";
			let sTextMapping = "{approverModel>" + sItemKey + "}" + 
								"{= ${approverModel>" + sItemText + "}.length > 0 ? ' - ' : ''}" +
								"{approverModel>" + sItemText + "}";
			let oItemTemplate = new sap.ui.core.Item({ key: sKeyMapping, text: sTextMapping });
	
			oSelectComponent.bindItems({
				path: "/dynamic/lists/" + sListDataProperty,
				model: "approverModel",
				template: oItemTemplate,
				templateShareable: false,
				length: 2000
			});
		}
		
		
		return oSelectComponent;
	};
	
	/**
	 * Called from the Attribute selection fragment when the user presses OK/Submit 
	 *	> The Component handle and the selected column info is passed. 
	 *	> This API is also called from within this file to add static columns in some case Decision check columns for messages
	 * 
	 */
	oDynamicWorkflowObject.returnSelectedAttribute = function (oColumnData, promiseResolve, bRead) {
		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		let oDynamicWorkflowTable = sap.ui.getCore().byId(this.DynamicWorkflowFragmentId + "--" + "idDerivationTable");
		// Get model 
		let oDynamicWorkflowModel = this.JSONViewModel;
		let oDynamicWorkflowData = oDynamicWorkflowModel.getProperty("/dynamic/data");
		let sChangedoc = this.JSONViewModel.getProperty("/dynamic/Changedoc");

		// get the table bindings and update the same 
		// 1. Column bindings
		let arrColumnAggregation = oDynamicWorkflowTable.getColumns();
		let arrCheckAttributeDup = [];
		if(sChangedoc === "AV"){
			arrCheckAttributeDup = arrColumnAggregation.filter(function(oEntity){
				return (oEntity.getCustomData()[0].getValue().entity === oColumnData.entity && oEntity.getCustomData()[0].getValue().attribute === oColumnData.attribute && oEntity.getCustomData()[0].getValue().attributeType === oColumnData.attributeType);
			});
		// Bug 12800 - Check duplicated attributes only for Dynamic workflow in case it is compared by attribute changes
		} else if(arrColumnAggregation.length !== 0 && this.WorkflowType === DMRNode.Types.Dynamic){
			arrCheckAttributeDup = oDynamicWorkflowData.filter(function(oElement){
				if(oElement.entity === oColumnData.entity && oElement.attribute === oColumnData.attribute){
					return oElement.entity;
				}
			});
		}

		if(arrCheckAttributeDup.length > 0){
			// Bug 12800 - Title and message were changed
			let sDupMessage = "Attribute "+ oColumnData.entity + " - "+ oColumnData.attribute +" has already been added.";
			let promiseShowPopupAlert = Utilities.showPopupAlert(sDupMessage, MessageBox.Icon.INFORMATION, "Duplicated Attributes");
			promiseShowPopupAlert.then(function () {
			});

			if (promiseResolve) {
				return promiseResolve();
			}else{
				return;
			}
		}

		if((arrColumnAggregation.length === 0 && sChangedoc === "AC") || sChangedoc === "AV"){
			//Check whether datatype is set for the driving attribute
			//If not, fetch attribute details and set the attributeDataType property
			let oFetchAttributeDetailsPromise = new Promise((resolve, reject) => {
				if(oColumnData.attributeType === "Driving" && (!oColumnData.attributeDataType || !oColumnData.attributeKind || !oColumnData.attributeDataLenght || !oColumnData.attributeDecimals)
					&& !(this.Node.getProperty("group").includes("parallel") || this.Node.getProperty("group").includes("dynamic"))) {
					let promiseAttributeDetails = 
					this.ParentController.GetListData.getEntityAttributeList(this.ParentController, this.CRTypeDetails.DataModel, oColumnData.entity, oColumnData.attribute);
					promiseAttributeDetails.then(arrAttribute => {
						if(!arrAttribute || arrAttribute.length === 0) {
							reject(undefined);
						} else {
							oColumnData.attributeDataType = arrAttribute[0].DATATYPE;
							oColumnData.attributeKind = arrAttribute[0].Kind;
							oColumnData.attributeDataLenght = arrAttribute[0].Length;
							oColumnData.attributeDecimals = arrAttribute[0].Decimals;
							resolve(oColumnData);
						}
						
					});
				} else {
					resolve(oColumnData);
				}
			});
			oFetchAttributeDetailsPromise.then(
				oColumnInfo => {

					if(sChangedoc==="AC" && arrColumnAggregation.length===0){
						// Bug 12906 - added a new field "attributeType" with the value set as "Driving"
						this.addColumnToTable({
							header: "Entity",
							entity: oColumnInfo.entity,
							attributeType: "Driving"
						});

						this.addColumnToTable({
							header: "Attributes",
							entity: oColumnInfo.entity,
							attribute: oColumnInfo.attribute,
							attributeType: "Driving"
						});
					} else{
						// Add the column to the table and retrieve the number of columns in the table. 
						let iColumnCount = this.addColumnToTable(oColumnInfo);
						
						// If the number of columns in the table is 1, first column added. Add the standard columns to the table
						// If the columns are more than 1, return no further activities to perform
						if(iColumnCount > 1 ) { 
							if (promiseResolve) {
								return promiseResolve();
							}else{
								return;
							}
						}
					}

					
					this.addColumnToTable({
						entity: "USER_TYPE",
						attributeType: "Deriving"
					});

					this.addColumnToTable({
						entity: "USER_VALUE",
						attributeType: "Deriving"
					});

					this.addColumnToTable({
						entity: "STEP_TYPE",
						attributeType: "Deriving"
					});

					this.addColumnToTable({
						entity: "APPSTEP",
						attributeType: "AppStep"
					});

					if(this.WorkflowType === DMRNode.Types.Dynamic){ 
						this.addColumnToTable({
							entity: "CR_STATUS",
							attributeType: "Deriving"
						});	
					}					
					
					this.addColumnToTable({
						entity: "Connections",
						attributeType: "Deriving"
					});
					
					this.addColumnToTable({
						entity: "StepActions",
						attributeType: "StepActions"
					});
				

					if (promiseResolve) {
						promiseResolve();
					}
				},
				() => {
					Utilities.showPopupAlert("Error while performing action.", MessageBox.Icon.ERROR, "Error");
					if (promiseResolve) {
						promiseResolve();
					}
				}

			);

		}

		let nextAppStep;
		let arrData = oDynamicWorkflowModel.getProperty("/dynamic/data");

		if(sChangedoc==="AC"){
			if(!bRead){
				nextAppStep = this.getAppStep(this.ParentController, "Next AppStep");
				arrData.push({
					APPSTEP: nextAppStep,
					entity: oColumnData.entity,
					attribute: oColumnData.attribute
				});
			} else{
				arrData.forEach(element => {
					if(oColumnData.entity === "ENTITY"){
						element.entity = element[oColumnData.entity];
					} else{
						let sAttrVal = element.ATTRIBUTES.split("__")[1];
						element.attribute = sAttrVal;
	
					}
				});
			}
			oDynamicWorkflowModel.refresh();
		} else{
			if(!bRead && arrData.length===0){
				let oRowData = {};
				nextAppStep = this.getAppStep(this.ParentController, "Next AppStep");

				if(this.WorkflowType === DMRNode.Types.Dynamic){
					oRowData.defaultStep = "X";
					arrData.push(oRowData);
					oRowData = {};
				}
				
				oRowData.APPSTEP = nextAppStep;
				arrData.push(oRowData);
			
				// Update the model with new data
				oDynamicWorkflowModel.setProperty("/dynamic/data", arrData);
			}
		}
		oDynamicWorkflowModel.setProperty("/dynamic/ebChangedoc", false);
	};

	/**
	 * Called from  returnSelectedAttribute Method when the first driving attribute is populated
	 *	> The Component handle, Selected column info and the Index to which column needs to be added is passed.
	 * 
	 */
	oDynamicWorkflowObject._insertActionColumn = function (ValidationColumnInfo, iInsertIndex) {

		// Get model 
		let oDynamicWorkflowModel = this.JSONViewModel;

		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		let oDynamicWorkflowTable = sap.ui.getCore().byId(this.DynamicWorkflowFragmentId + "--" + "idDerivationTable");

		// Get the item definitions 	
		let oColumnListItems = oDynamicWorkflowTable.getBindingInfo("items").template;

		let oHBox = new sap.m.VBox();
		let sValueVariable = ValidationColumnInfo.entity; // + "_" + ValidationColumnInfo.attribute; 
		let sValueBinding = "{approverModel>" + sValueVariable + "}";
		oDynamicWorkflowModel.setProperty("/dynamic/lists/" + sValueVariable, []);

		/* Add a list box to the cell. Map the data to the global list items */
		let oSelectComponent = new sap.m.Select({
			/* Task 10758 - editable properties were removed from the controls  */
			forceSelection: false,
			selectedKey: sValueBinding,
			wrapItemsText: true,
			width: "100%",
			visible: "{= ${approverModel>/dynamic/lists/" + sValueVariable + "}.length !== 0}"
		});

		//Change Event is used to refresh the suggestion Items for the Message Numbers

		//Add the Item List and bind it with the Select Control
		let oItemTemplate = new sap.ui.core.Item({
			key: "{approverModel>UsmdCrAction}",
			text: "{approverModel>UsmdCrAction}" + "{= ${approverModel>UsmdActDesc}.length > 0 ? ' - ' : ''}" +
				"{approverModel>UsmdActDesc}"
		});

		oSelectComponent.bindItems({
			path: "/dynamic/lists/" + sValueVariable,
			model: "approverModel",
			template: oItemTemplate,
			templateShareable: false
		});

		oHBox.addItem(oSelectComponent);
		
		let promiseActType = this.ParentController.GetListData.getWFConfig(this.ParentController, "/ACTIONDETAILSSet");
		promiseActType.then(
			function (oData) {
				oDynamicWorkflowModel.setProperty("/dynamic/lists/" + sValueVariable, oData.results);
			}
		);

		//Insert the Cell in the respective position in the Column List
		oColumnListItems.insertCell(oHBox, iInsertIndex);

		oDynamicWorkflowTable.bindItems({
			path: "/dynamic/data",
			model: "approverModel",
			template: oColumnListItems,
			templateShareable: true
		});

		//Create a column for the Validation Entity
		let oValidationColumn = this._createColumnForTable(ValidationColumnInfo);

		//Insert the created column in the derivation table		
		oDynamicWorkflowTable.insertColumn(oValidationColumn, iInsertIndex);
	};

	/**
	 * Executed in component context. this = Component
	 * 
	 * 1. Disable edit on all rows 
	 * 2. Identify the selected row
	 * 3. Enable editing on current row
	 */
	oDynamicWorkflowObject.selectionChangedHandler = function (oEvent) {
		// Get the selected item 
		let oSelectedRow = oEvent.getParameters().listItem;
		let iSelectedIndex = oEvent.getSource().indexOfItem(oSelectedRow);
		
		this.DynamicWorkflowDialog.selectionChanged(oSelectedRow, iSelectedIndex);
	}; 
	 
	oDynamicWorkflowObject.selectionChanged = function (oSelectedRow, iSelectedIndex) {

		// Get model 
		let oDynamicWorkflowModel = this.JSONViewModel;
		
		// get the data for the table
		let arrTableData = oDynamicWorkflowModel.getProperty("/dynamic/data");

		// Loop through the data and set editable to false 
		arrTableData.forEach(function (rowData) {
			rowData.editable = false;
		});

		// If nothing is selected, return (unlikely scenario)
		if (iSelectedIndex === -1) { return; }

		// Set the selected row to editable
		arrTableData[iSelectedIndex].editable = true;
		
		//Get attribute List to check for entity qualifying key
		let attrList = oDynamicWorkflowModel.getProperty("/attributeList");
		attrList = [];

		//Get entity List to check for type 4 entity
		let entityList = oDynamicWorkflowModel.getProperty("/SelectAttributeDialog/entityList");

		let isType4;
		let isKey;
		let sValueVariable;
		let orgList = {};
		let entArr = [];

		jQuery.extend(true, orgList, oDynamicWorkflowModel.getProperty("/dynamic/lists/"));

		//Copy the orginial selection values in case row is deleted
		if (!oDynamicWorkflowModel.getProperty("/dynamic/org_lists/")) {
			oDynamicWorkflowModel.setProperty("/dynamic/org_lists/", orgList);
		}

		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		let oDynamicWorkflowTable = sap.ui.getCore().byId(this.DynamicWorkflowFragmentId + "--" + "idDerivationTable");

		// get the table bindings and update the same 
		// 1. Column bindings
		let arrColumnAggregation = oDynamicWorkflowTable.getColumns();
		for (let iColumn = 0; iColumn < arrColumnAggregation.length; iColumn++) {
			let oCustomData = arrColumnAggregation[iColumn].getCustomData();
			if (oCustomData.length > 0) {
				if (!entArr.includes(oCustomData[0].getValue().entity)) {
					let currAttr = attrList.find(function (attr) {
						return attr.UsmdEntity === oCustomData[0].getValue().entity;
					});
					if (!currAttr) {
						entArr.push(oCustomData[0].getValue().entity);
					}
				}
			}
		}
		if (entityList) {
			entArr.some(function (entName) {
				isType4 = entityList.findIndex(function (entity) {
					return entity.UsmdEntity === entName && entity.UsageType === "4";
				}) > -1;
	
				return isType4 === true;
			});
		}

		if (isType4) {
			arrTableData.forEach(function (rowData) {
				//get all the property names from the TableRow
				let tableKeyArr = [];
				for (let propertyName in rowData) {
					tableKeyArr.push(propertyName);
				}

				//Check if the deriving attribute is an entity qualifying key
				tableKeyArr.forEach(function (rowKey) {
					if (rowKey !== "editable") {
						let rowKeyArr = rowKey.split("__");
						sValueVariable = rowKey;
						isKey = attrList.find(function (attr) {
							return attr.IsKey === "X" && attr.UsmdAttribute === rowKeyArr[1] && attr.UsmdEntity === rowKeyArr[0];
						});
					}
				});
			});

			//Remove selected entries from previous rows from available entries for qualifying entities
			if (isKey) {
				let dataExist;

				let attrArrData = oDynamicWorkflowModel.getProperty("/dynamic/org_lists/" + sValueVariable);

				let attrAry = attrArrData.filter(function (el) {
					dataExist = false;
					arrTableData.forEach(function (rowData) {
						if (rowData[sValueVariable] === el.Key) {
							dataExist = true;
						}
					});
					return !dataExist;
				});
				oDynamicWorkflowModel.setProperty("/dynamic/lists/" + sValueVariable, attrAry);
			} else {
				//Set the selected list back to original if no key is found
				oDynamicWorkflowModel.setProperty("/dynamic/lists/" + sValueVariable, oDynamicWorkflowModel.getProperty("/dynamic/org_lists/" +
					sValueVariable));
			}
		} else {
			//Set the selected list back to original if no key is found
			oDynamicWorkflowModel.setProperty("/dynamic/lists/", oDynamicWorkflowModel.getProperty("/dynamic/org_lists/"));
		}
		oDynamicWorkflowModel.setProperty("/dynamic/data", arrTableData);

	};

	/**
	 * Executed in component context. this = Component
	 * 
	 * Add a new row to the rules table with no content. 
	 */
	oDynamicWorkflowObject.addNewRuleHandler = function () {
		this.DynamicWorkflowDialog.addNewRule(true);
	};

	oDynamicWorkflowObject.addNewRule = function(sEvent){

		// Get model 
		let oDynamicWorkflowModel = this.JSONViewModel;
		
		let sChangeDoc = oDynamicWorkflowModel.getProperty("/dynamic/Changedoc");

		// Get the current table data 
		let arrTableData = oDynamicWorkflowModel.getProperty("/dynamic/data");

		// if the table is null, initialize the array 
		if (!arrTableData) {
		arrTableData = [];
		}

		// If this is of type Dynamic add a  default row 
		let oRowData = {};
		let nextAppStep;

		

		if(sEvent === true){
			if(arrTableData.length === 0 && this.WorkflowType === DMRNode.Types.Dynamic){
				//The first row of Dynamic Workflow Decision Table is default
				oRowData.defaultStep = "X";
	
				arrTableData.push(oRowData);
			
				// Update the model with new data
				oDynamicWorkflowModel.setProperty("/dynamic/data", arrTableData);
			}
	
			if(sChangeDoc === "AC"){
				let sEntityName = this.CRTypeDetails.Entity;
	
				this.SelectAttributeDialog.openSelectAttributeDialog(
					this, this.JSONViewModel, "Driving", sEntityName, this.CRTypeDetails.DataModel);
			} else {
				nextAppStep = this.getAppStep(this.ParentController, "Next AppStep");
				oRowData.APPSTEP = nextAppStep;
				arrTableData.push(oRowData);
			
				// Update the model with new data
				oDynamicWorkflowModel.setProperty("/dynamic/data", arrTableData);
			}
		} else{
			
			// Update the model with new data
			oDynamicWorkflowModel.setProperty("/dynamic/data", arrTableData);
		}

	};
	
	oDynamicWorkflowObject.getAppStep = function (oController, sCurrentorNextAppStep) {
		
		if(sCurrentorNextAppStep === "Current AppStep") {
		
			let oGraph = oController.getView().byId("graph");
			
			let arrGraphLines = oGraph.getLines();
			
			let arrAppSteps = [];
			
			arrGraphLines.forEach(function(element) {
				if(element.getCustomData()[0].getValue().AppStep) {
					arrAppSteps.push(element.getCustomData()[0].getValue().AppStep);
				}
			});
			
			// Retrieve the next character prefix in the case of Node. Get the first char from the last node
			if(arrAppSteps.length > 0 )
			{
				arrAppSteps.sort();
				let sCurrenAppStep = arrAppSteps[arrAppSteps.length - 1];
				return sCurrenAppStep;
			} else {
				return undefined;
			}
			
		
		} else {
			let currentAppStep = oController.getApproverModel().getProperty("/currentAppStep");
			if ( !currentAppStep ) {
				//If there are no current App Step in properties, assign it as AA
				oController.getApproverModel().setProperty("/currentAppStep", "AA");
				return "AA";
			} else {
				//Increment the AppStep by 1 character [AA-AZ -> BA-BZ -> ....]
				let sPrefix = currentAppStep.substr(0, 1);
				let sSuffix = String.fromCharCode(currentAppStep.substr(1).charCodeAt() + 1);
				
				//If character code of suffix exceeds that of Z, increment prefix by 1 character and set suffix as "A"
				if ( sSuffix.charCodeAt() > "Z".charCodeAt() ) {
					sPrefix = String.fromCharCode(sPrefix.charCodeAt() + 1);
					sSuffix = "A";
				}
				
				oController.getApproverModel().setProperty("/currentAppStep", sPrefix + sSuffix);
				
				return sPrefix + sSuffix;
			}
		}
		
	};

	/* Task 10759 - enableDeleteRows method was removed, now all the time deletion is enabled */

	/**
	 * Executed in component context. this = Component
	 * 
	 * Confirm if the row needs to be delete and delete if YES
	 */
	oDynamicWorkflowObject.deleteRowHandler = function (oEvent) {
		let oTable = oEvent.getSource();
		let oClickedItem = oEvent.getParameters().listItem;

		this.DynamicWorkflowDialog.deleteRow(oTable, oClickedItem);
	};
	 
	oDynamicWorkflowObject.deleteRow = function (oTable, oClickedItem) {
		// Get model 
		let oDynamicWorkflowModel = this.JSONViewModel;
		
		// Get the index clicked
		let iClickedIndex = oTable.getItems().indexOf(oClickedItem);
		let oContext = oClickedItem.getBindingContext("approverModel");
		let oDeletedRowData = oDynamicWorkflowModel.getProperty(oContext.sPath);
		let arrTableData = oDynamicWorkflowModel.getProperty("/dynamic/data");
		
		// Show a message pop up stating the default step cannot be deleted
		if (arrTableData[iClickedIndex].defaultStep === "X") {
			Utilities.showPopupAlert("This decision row is mandatory", MessageBox.Icon.ERROR, "Cannot perform action");
			return;
		}
		
		// Show a message asking the user to confirm deletion. Exit if cancel 
		let promise = new Promise(function (resolve, reject) {
			let promiseShowPopupAlert = Utilities.showPopupAlert("The decision row will be deleted. Continue?", MessageBox.Icon.WARNING,
				"Delete?", [sap.m.MessageBox.Action.YES, sap.m.MessageBox.Action.NO]);
			promiseShowPopupAlert.then(function () {
				resolve();
			}, function () {
				reject();
			});
		});

		/**
		 * Remove row from the model. 
		 */
		promise.then(function () {
			/**
			 * Bug 11565 - Extra rows in connections table after parallel node table is modified
			 * Do not destroy or remove any lines
			 * Instead, we'll have a property with all the Appsteps marked for deletion (array)
			 * Map the original array to model property in case of Cancel changes later
			 * Temporarility, filter array with records not marked for deletion and map to dynamic/data property
			 */
			oDeletedRowData.DELETE = "X";
			oDynamicWorkflowModel.setProperty(oContext.sPath, oDeletedRowData);
			let arrTempData = oDynamicWorkflowModel.getProperty("/dynamic/tempData");
			
			//Copy the original array details when user clicks on delete row for the first time after fragment is displayed
			if(!arrTempData || arrTempData.length <= 0) {
				arrTempData = [];
				jQuery.extend(true, arrTempData, arrTableData);
			}
			// Initializing variable as 0 to avoid NaN value
			let iDeleteRowCounter = 0;
			arrTempData.forEach((el)=> {
				if(el.hasOwnProperty("DELETE")){
					iDeleteRowCounter = iDeleteRowCounter + 1;
				}
			});

			if(iDeleteRowCounter === arrTempData.length){
				oDynamicWorkflowModel.setProperty("/dynamic/ebChangedoc", true);
			}
			oDynamicWorkflowModel.setProperty("/dynamic/tempData", arrTempData);
			oDynamicWorkflowModel.setProperty("/dynamic/data", arrTempData.filter(oData => oData.DELETE !== "X"));
		});
	};

	/**
	 * Executed in component context. this = Component
	 * 
	 * Warns the user that changes could be lost and if accepted, closes the dialog without saving changes.
	 */
	oDynamicWorkflowObject.cancelDynamicWorkflowConfigurationHandler = function (oEvent) {
		let oController = this;
		let oDynamicWorkflowModel = oEvent.getSource().getParent().getParent().getModel("approverModel");
		
		// Show a message asking the user to confirm deletion. Exit if cancel
		let promise = new Promise(function (resolve, reject) {
			let promiseShowPopupAlert = Utilities.showPopupAlert("Any unsaved changes will be lost. Continue?", 
			MessageBox.Icon.WARNING, "Cancel and close?", [sap.m.MessageBox.Action.YES, sap.m.MessageBox.Action.NO]);
			promiseShowPopupAlert.then(function(){
				resolve();
			}, function() {
				reject();
			});
		});

		/**
		 * Find the table, iterate through the columns and remove the matched column
		 */
		promise.then(function () {
			/**
			 * Bug 11565 - Extra rows in connections table after parallel node table is modified
			 * Clear the array with all the rows marked for deletion
			 * Map the original array back to model property
			 */
			let arrTableData = oDynamicWorkflowModel.getProperty("/dynamic/tempData");
			if(arrTableData && arrTableData.length > 0) {
				arrTableData.forEach(oData => {
					if (oData.hasOwnProperty("DELETE")) {
						delete oData.DELETE;
					}
				});
				oDynamicWorkflowModel.setProperty("/dynamic/data", arrTableData);
				oDynamicWorkflowModel.setProperty("/dynamic/tempData", undefined);
			}
			
			
			// Close the dialog
			oController.DynamicWorkflowDialog.DynamicNodeEditDialog.close();
		});	
	};

	
	// Feature 12427 - Function hasInvalidDrivingAttributes was removed since it won't be used more

	// Bug 12188 - Able to enter invalid data in Approver field for parallel & dynamic workflows
	oDynamicWorkflowObject.hasInvalidDerivingAttributes = function(arrColumnInfo, oDynamicWorkflowModel) {
		let arrTableData = oDynamicWorkflowModel.getProperty("/dynamic/data");
		let arrColPaths = [];
		
		let arrDerivingAttributes = arrColumnInfo.filter(oColumn => {
			return oColumn.attributeType === "Deriving" && oColumn.visible === true && oColumn.mandatory;
		});

		// Bug 12906 - added the sChangeDoc variable for the new argument on the _getBindingVariableForColumn
		let sChangeDoc = oDynamicWorkflowModel.getProperty("/dynamic/Changedoc");

		arrDerivingAttributes.forEach(function(oColInfo){
			let sColPath = this._getBindingVariableForColumn(oColInfo, sChangeDoc);
			arrColPaths.push(sColPath);
		}, this);
		
		for (let i = 0; i < arrTableData.length; i++) {
			for (let j = 0; j < arrColPaths.length; j++) {
				let sColName = arrColPaths[j];
				let sColValue = arrTableData[i][sColName] ? arrTableData[i][sColName] : "";
				
				if(arrTableData[i].USER_TYPE !== "NA"){
					if (sColName === "USER_VALUE") {
						if(arrTableData[i].USER_VALID_VALUES) {
							let aValue = sColValue;
							if(sColValue.length > 0){
								aValue = sColValue.includes(" - ") ? sColValue.split(" - ")[0].trim() : sColValue;
							}
							let bMatchedValue = arrTableData[i].USER_VALID_VALUES.find((oValidValue => {
								return oValidValue.key.toUpperCase() === aValue.toUpperCase();
							}));
	
							if (!bMatchedValue) {
								return true;
							}

						} else {
							return true;
						}
					} else {
						if (!sColValue) {
							return true;
						}
					}
				}
			}
		}

		return false;
	};

	/**
	 * Clear the empty rows from the table. 
	 * Must be called in the context of this Object only.
	 * 
	 * 1. Read all the rows
	 * 2. Identify the visible columns 
	 * 3. Check to ensure all the visible columns have data in every row
	 * 4. If all the visible columns are empty .. clear that row
	 */
	oDynamicWorkflowObject._workflowRemoveEmptyRows = function(arrColumnInfo, oDynamicWorkflowModel) {
		// Get the table data from the model 
		let arrTableData = oDynamicWorkflowModel.getProperty("/dynamic/data");
		let arrCleanedData = [];
		let arrColPaths = [];
		// Bug 12906 - added the sChangeDoc variable for the new argument on the _getBindingVariableForColumn
		let sChangeDoc = oDynamicWorkflowModel.getProperty("/dynamic/Changedoc");
		
		// filter the columns by elements that are visible. Also calculate the binding variables 
		arrColumnInfo.forEach(function(oColInfo){
			if(oColInfo.visible){
				let sColPath = this._getBindingVariableForColumn(oColInfo, sChangeDoc);
				arrColPaths.push(sColPath);
			}
		}, this);

		// Go through all the rows of data and copy to the target arr if at least 1 of the columns is not empty
		for(let i = 0; i < arrTableData.length; i++){
			for(let j = 0; j < arrColPaths.length; j++) {
				let sColName = arrColPaths[j];
				let sColValue = arrTableData[i][sColName];
				if(sColValue || arrTableData[i].defaultStep === "X" ){
					arrCleanedData.push(arrTableData[i]);
					break;
				}
			}
		}
		
		oDynamicWorkflowModel.setProperty("/dynamic/data", arrCleanedData);
	};

	/**
	 * Check mandatory columns and notify the user if any of the mandatory columns has empty cells 
	 * Must be called in the context of this Object only.
	 * 
	 * 1. Read all the rows
	 * 2. Identify the mandatory columns 
	 * 3. Check to ensure all the mandatory columns have data in every row
	 * 4. If any of the mandatory cells are empty .. show an error message and return 
	 */
	oDynamicWorkflowObject._workflowCheckMandatoryCells = function(arrColumnInfo, oDynamicWorkflowModel) {
		// Get the table data from the model 
		let arrTableData = oDynamicWorkflowModel.getProperty("/dynamic/data");
		let bAllMandatoryCellsValid = true;
		let arrColPaths = [];

		// if there are no rows in the table ... show an error 
		if(arrTableData.length === 0){
			Utilities.showPopupAlert("No conditions specified. At least one condition must be specified to continue.", 
			MessageBox.Icon.ERROR, "Data invalid");
			return false;
		}
		// Bug 12906 - added the sChangeDoc variable for the new argument on the _getBindingVariableForColumn
		let sChangeDoc = oDynamicWorkflowModel.getProperty("/dynamic/Changedoc");

		// filter the columns by elements that are visible. Also calculate the binding variables 
		arrColumnInfo.forEach(function(oColInfo){
			if(oColInfo.mandatory){
				let sColPath = this._getBindingVariableForColumn(oColInfo, sChangeDoc);
				arrColPaths.push(sColPath);
			}
		}, this);

		// Go through all the rows of data and check if any of the cells are empty
		for(let i = 0; (i < arrTableData.length) && (bAllMandatoryCellsValid === true); i++){
			for(let j = 0; (j < arrColPaths.length) && (bAllMandatoryCellsValid === true); j++) {
				let sColName = arrColPaths[j];
				let sColValue = arrTableData[i][sColName];
				if(!sColValue && arrTableData[i].USER_TYPE!=="NA"){
					oDynamicWorkflowModel.setProperty("/dynamic/bMandatoryFieldsEmpty");
					bAllMandatoryCellsValid = false;
					break;
				}
			}
		}
		
		if(bAllMandatoryCellsValid === false) {
			Utilities.showPopupAlert("Mandatory fields are empty. Please enter a value in all the mandatory fields.",
			MessageBox.Icon.ERROR, "Data invalid");
		}
		return bAllMandatoryCellsValid;
	};
	
	/**
	 * Bug 11381 - Remove duplicate users from ct user agent group for parallel workflow processing in dynamic badi
     * Added New Validation
	 * If Parallel Flow, don't allow more than one row having the exact same combination of values for:
	 * --> Driving Attribute
	 * --> Approver Type
	 * --> Approver
	 */
	oDynamicWorkflowObject._workflowCheckDuplicatedEntries = function(sWorkflowType, arrColumnInfo, oDynamicWorkflowModel) {
		let bDuplicatedEntries = false;

		if (sWorkflowType !== DMRNode.Types.Parallel) {
			return bDuplicatedEntries;
		}

		// Get the table data from the model 
		let arrTableData = oDynamicWorkflowModel.getProperty("/dynamic/data");
		// Bug 12906 - added the sChangeDoc variable for the new argument on the _getBindingVariableForColumn
		let sChangeDoc = oDynamicWorkflowModel.getProperty("/dynamic/Changedoc");
		let arrColPaths = [];
		let arrUniqueRows = [];
		
		// filter the columns by elements that are visible. Also calculate the binding variables 
		arrColumnInfo.forEach(function(oColInfo) {
			if (oColInfo.visible) {
				let sColPath = this._getBindingVariableForColumn(oColInfo, sChangeDoc);
				arrColPaths.push(sColPath);
			}
		}, this);

		// remove column STEP_TYPE from the array, if it's in there
		let nIndex = arrColPaths.indexOf("STEP_TYPE");
		if (nIndex > 0) {
			arrColPaths.splice(nIndex, 1);
		}
		
		// Go through all the rows of data and check if any of the cells are empty
		for (let i = 0; i < arrTableData.length; i++) {
			let oRow = {};

			for (let j = 0; j < arrColPaths.length; j++) {
				let sColName = arrColPaths[j];
				// Bug 12906 - added a new conditional that checks what is selected on the "compare by" mode and sends different fields depending on it.
				let sColValue;

				if(sChangeDoc === "AV"){
					//Fix/12461 - sColValue was changed to determine duplicity by including operators
					sColValue = arrTableData[i][sColName] + ((arrTableData[i][sColName + "-Operator"]) ? (arrTableData[i][sColName + "-Operator"]) : "EQ");
				} else {
					sColValue = arrTableData[i][sColName];
				}
				if (sColValue) {
					oRow[sColName] = sColValue;
				}
			}

			// Bug 11564 - Rows removed from parallel/dynamic node table as information is added
			// If row is empty then ignore the row
			if (JSON.stringify(oRow) !== "{}") {
				// Bug 12236 - Parallel workflow giving error for all the values except some particular values for user and approver
				if (oRow.hasOwnProperty("USER_VALUE")) {
					if (this._isRowInArray(oRow, arrUniqueRows)) {
						bDuplicatedEntries = true;
						break;
					} else {
						arrUniqueRows.push(oRow);
					}
				}
			}
		}
		
		return bDuplicatedEntries;
	};

	/**
	 * This function receives an object and an array as parameters
	 * Then, checks if there's an exact match of that object in the array
	 */
	oDynamicWorkflowObject._isRowInArray = function(oRowToCheck, arrArrayWithAllRows) {
		if (arrArrayWithAllRows.length === 0) {
			return false;
		}
		
		let oMatch = arrArrayWithAllRows.find((oArrayRow) => {
			return JSON.stringify(oRowToCheck) === JSON.stringify(oArrayRow);
		});
		
		return (oMatch ? true : false);
	};

	/**
	 * Executed in component context. this = Component
	 * 
	 * 1. Get the table component 
	 * 2. Retrieve the list of columns and fill their data into an array 
	 * 3. Retrieve the data from the table, 
	 * 4. Map the column info and data into the target json
	 */
	oDynamicWorkflowObject.saveDynamicWorkflowConfigurationHandler = function () {
		this.DynamicWorkflowDialog.saveDynamicWorkflowConfiguration();
	};
	
	/*
	 * Bug 11381 - Remove duplicate users from ct user agent group for parallel workflow processing in dynamic badi
	 * This logic was encapsulated in a function to avoid code duplication, as code is being called in more than one place
	 */
	oDynamicWorkflowObject._workflowGetColumnsAndValues = function () {
		/** Array to hold the column information 
		 * 1. attributeType: "Driving"
		 * 2. entity: "AD_EMAIL"
		 * 3. attribute: "E_ADDRESS"
		 */
		let arrColumnInfo = [];
		
		// Get the table component. The table exits and should be return... null is possible if there are coding errors. No checks needed 
		let oDynamicWorkflowTable = sap.ui.getCore().byId(this.DynamicWorkflowFragmentId + "--" + "idDerivationTable");

		// Get the columns 
		let arrColumns = oDynamicWorkflowTable.getColumns();

		// loop through the columns and fill the data array 
		for (let iColumn = 0; iColumn < arrColumns.length; iColumn++) {
			let oCustomData = arrColumns[iColumn].getCustomData()[0].getValue();

			let oDataCopy = {};
			jQuery.extend(true, oDataCopy, oCustomData);

			arrColumnInfo.push(oDataCopy);
		}

		return arrColumnInfo;
	};

	oDynamicWorkflowObject.saveDynamicWorkflowConfiguration = function () {
		let oController = this;
		let oParentController = this.ParentController;
		let oDynamicWorkflowModel = this.JSONViewModel;

		let oNodeDetails = this.Node;

		/**
		 * Bug 11565 - Extra rows in connections table after parallel node table is modified
		 * Check if the Parallel Table has any record marked for deletion
		 * Update the model property and clear the table which holds records marked for deletion
		 */
		let arrAllRows = oDynamicWorkflowModel.getProperty("/dynamic/tempData");
		// Bug 13005 - Getting the real data to save
		let arrRowsToSave = oDynamicWorkflowModel.getProperty("/dynamic/data");
		let arrRowsToDelete = [];
		if(arrAllRows && arrAllRows.length > 0) {
			arrAllRows.forEach(oRow => {
				// Bug 13005 - Removed extra condition since we already have the data to save
				// Check if the row needs to be deleted or saved based on DELETE property
				if (oRow.hasOwnProperty("DELETE")) {
					let oGraph = oParentController.getView().byId("graph");
					let oRemLines = oGraph.getLines();
					oRemLines.forEach(function(o) {
						if (o.getCustomData()[0].getValue().AppStep === oRow.APPSTEP) {
							oGraph.removeLine(o);
							o.destroy();
						}
					});
					arrRowsToDelete.push(oRow);
				} 
			});
			
			oDynamicWorkflowModel.setProperty("/dynamic/data", arrRowsToSave);
			oDynamicWorkflowModel.setProperty("/dynamic/tempData", undefined);
		} 

		let arrColumnInfo = this._workflowGetColumnsAndValues(oDynamicWorkflowModel);

		// Feature 12427 - Condition was removed to avoid validation and allow blank values for attributes

		// Bug 12188 - Able to enter invalid data in Approver field for parallel & dynamic workflows
		let bHasInvalidDerivingAttributes = this.hasInvalidDerivingAttributes(arrColumnInfo, oDynamicWorkflowModel);

		if (bHasInvalidDerivingAttributes) {
			Utilities.showPopupAlert("Please make sure you have entered appropriate values for Approver Type, Approver and Step Type fields in all rows.",
			MessageBox.Icon.ERROR, "Empty or invalid entries found");
			return;
		}

		// Bug 11564 - Rows removed from parallel/dynamic node table as information is added
		// Clear empty rows from the table (only when saving)
		this._workflowRemoveEmptyRows(arrColumnInfo, oDynamicWorkflowModel);

		// Validate data in the table 
		// All Mandatory fields must be filled. 
		let bAllMandatoryCellsValid = this._workflowCheckMandatoryCells(arrColumnInfo, oDynamicWorkflowModel);
		if(!bAllMandatoryCellsValid) {
			return;
		}

		// Bug 11381 - Remove duplicate users from ct user agent group for parallel workflow processing in dynamic badi
		let bDuplicatedEntries = this._workflowCheckDuplicatedEntries(this.WorkflowType, arrColumnInfo, oDynamicWorkflowModel);
		
		if (bDuplicatedEntries) {
			Utilities.showPopupAlert("Duplicated entries were found. Please make sure not to enter the same combination of values in more than one row.",
			MessageBox.Icon.ERROR, "Duplicate Entries Error");
			return;
		}

		// get the data from the model 
		let oDynamicData = oDynamicWorkflowModel.getProperty("/dynamic");
		let arrTableData = oDynamicData.data;

		let arrDefaultRow = arrTableData.filter(function(rowData) {
    		return rowData.defaultStep === "X";
		})[0];
		
		// If there is no default row (PARALLEL FLOW), initialize the object 
		if (!arrDefaultRow) {
			arrDefaultRow = {};
		}

		let sRuleSet = oDynamicWorkflowModel.getProperty("/dynamic/Changedoc");
		
		// Fill the data into the target object to be sent to the server 
		// Get model 
		let oNodeData = {
			CrType: this.CRTypeDetails.CrTypeName,
			Row: "1",
			CrStep: this.Node.getTitle(),
			StepType: arrDefaultRow.STEP_TYPE,
			ApproverType: arrDefaultRow.USER_TYPE,
			ApproverValue: arrDefaultRow.USER_VALUE,
			Apprdesc: oDynamicData.Apprdesc,
			Changedoc: (sRuleSet==="AC" ? true : false)
		};

		// Fill the Driving and Deriving Column details 

		oNodeData.DRIVINGENTITYSet = [];
		oNodeData.DERIVINGENTITYSet = [];
			for (let iColumn = 0; iColumn < arrColumnInfo.length; iColumn++) {
				let arrColumnModel = []; 
				if (sRuleSet==="AV" && arrColumnInfo[iColumn].attributeType === "Driving") {
					arrColumnModel = oNodeData.DRIVINGENTITYSet;
					arrColumnModel.push({
						UsmdEntity: arrColumnInfo[iColumn].entity,
						UsmdAttribute: arrColumnInfo[iColumn].attribute,
						Attrdatatype: arrColumnInfo[iColumn].attributeDataType,
						ColNum: (iColumn + 1).toString()
					});
				} else if(sRuleSet==="AC" && arrColumnInfo[iColumn].hasOwnProperty("header")){
					arrColumnModel = oNodeData.DRIVINGENTITYSet;
					arrColumnModel.push({
						UsmdEntity: arrColumnInfo[iColumn].header.toUpperCase(),
						UsmdAttribute: arrColumnInfo[iColumn].header.toUpperCase(),
						ColNum: (iColumn + 1).toString()
					});
				} else{ 
					arrColumnModel = oNodeData.DERIVINGENTITYSet;
					arrColumnModel.push({
						ColName: arrColumnInfo[iColumn].entity,
						ColNum: (iColumn + 1).toString()
					});
				}
			}

		// Fill in the data to be sent 
		oNodeData.DTVALUESSet = [];
		oNodeData.DTACTIONS = [];
		
		for (let iRowsIndex = 0; iRowsIndex < arrTableData.length; iRowsIndex++) {
			let sTempEntity;
			for (let iColumnIndex = 0; iColumnIndex < arrColumnInfo.length; iColumnIndex++) {
				let sColName = arrColumnInfo[iColumnIndex].entity; 
				let sColValue;
				
				if((this.WorkflowType === DMRNode.Types.Dynamic && iRowsIndex > 0 && arrColumnInfo[iColumnIndex].hasOwnProperty("header")) 
				|| (this.WorkflowType === DMRNode.Types.Parallel && arrColumnInfo[iColumnIndex].hasOwnProperty("header"))){
					let bIsAttr = (arrColumnInfo[iColumnIndex].header === "Attributes");

					if(bIsAttr){
						sColValue = sTempEntity + "__" + arrTableData[iRowsIndex].attribute;
					} else{
						sTempEntity = arrTableData[iRowsIndex].entity;
						sColValue = sTempEntity;
					}

					oNodeData.DTVALUESSet.push({
						ColName: arrColumnInfo[iColumnIndex].header.toUpperCase(),
						ColValue: sColValue,
						ColNum: (iColumnIndex + 1).toString(),
						RowNum: (iRowsIndex + 1).toString(),
						Defaultstep: "",
						Operator: ""
					});
				} else if(this.WorkflowType === DMRNode.Types.Dynamic && iRowsIndex === 0 && arrColumnInfo[iColumnIndex].hasOwnProperty("header")) {
					oNodeData.DTVALUESSet.push({
						ColName: arrColumnInfo[iColumnIndex].header.toUpperCase(),
						ColValue: "",
						ColNum: (iColumnIndex + 1).toString(),
						RowNum: (iRowsIndex + 1).toString(),
						Defaultstep: "X",
						Operator: ""
					});
				} else{
					
					if( arrColumnInfo[iColumnIndex].attribute ) {
						sColName += "__" + arrColumnInfo[iColumnIndex].attribute;
					}
					//Feature/12334 - Value of the operator is obtained and save in the variable 
					let sOperatorValue = arrTableData[iRowsIndex][sColName + "-Operator"];
					
					sColValue = arrTableData[iRowsIndex][sColName];
					// Update the value to INIT for empty elements only for Driving Attribute. Set to Empty "" for all else
					if(!sColValue) {
						if(arrColumnInfo[iColumnIndex].attributeType === "Driving" && arrTableData[iRowsIndex].defaultStep !== "X") {
							sColValue = "INIT";
						} else {
							sColValue = "";
						}
					}
					oNodeData.DTVALUESSet.push({
						ColName: sColName,
						// Feature/12185 - Condition added to obtain only the keys if it is the case
						ColValue:(sColValue && typeof sColValue === "string" && sColValue.includes(" - ")) ? sColValue.split(" - ")[0] : sColValue,
						ColValueDescr:(sColValue && typeof sColValue === "string" && sColValue.includes(" - ")) ? sColValue.split(" - ")[1] : "-BLANK-",
						ColNum: (iColumnIndex + 1).toString(),
						RowNum: (iRowsIndex + 1).toString(),
						Defaultstep: ( arrTableData[iRowsIndex].defaultStep === undefined ) ? "" : arrTableData[iRowsIndex].defaultStep,
						Operator: sOperatorValue
					});
				} 


				// Store the actions to the Actions array to be used for editing
				if(sColName === "StepActions") {
					oNodeData.DTACTIONS.push({
						action: sColValue,
						RowNum: (iRowsIndex + 1).toString()
					});
				} 
			}
		}
		
		/**
		 * If NodeType is Parallel, be default, all lines must be connected to its equivalent Merge Node
		 * If a new rule has been added or an existing rule does not have any connections, then rule must have connections to Merge Node
		 */
		if(oController.WorkflowType === DMRNode.Types.Parallel) {
			// // Get the graph object 
			let oGraph = oController.ParentController.getView().byId("graph");
			let oGraphNodes = oGraph.getNodes();
			let oGraphLines = oGraph.getLines();
			// let oNodeDetails = this.Node;

			/**
			 * Bug 11565 - Extra rows in connections table after parallel node table is modified
			 * If array consists of records marked for deletion, update the line custom data with the correct RowNum as per DTVALUESSet
			 */
			if(arrRowsToDelete && arrRowsToDelete.length > 0) {
				oGraphLines.forEach(oLine => {
					if(oLine.getFrom() === oNodeDetails.getKey()) {
						let oAppStep = oNodeData.DTVALUESSet.find(oValue => {
							return oValue.ColName === "APPSTEP" &&
									oValue.ColValue === oLine.getCustomData()[0].getValue().AppStep;
						});
						if(oAppStep) {
							oLine.getCustomData()[0].getValue().RowNum = Number(oAppStep.RowNum);
						}
					}	
				});
			}

			let oMergeNode = oGraphNodes.find(oNode => {
				return oNode.getGroup() === oNodeDetails.getGroup() &&
						oNode.getIcon() === DMRNode.Icons.Merge;         
			});
			//Fetch the Merge Node Key which should be a part of the same group as the Parallel Node
			let sMergeNodeKey;
			if(oMergeNode) {
				sMergeNodeKey = oMergeNode.getKey();
			} else {
				//Every Parallel Node must have a Merge Node. Ideally, the code should never return
				return;
			}
			oNodeData.DTACTIONS.forEach((oAction) => {
				let arrActions = oAction.action.split(",");
				let oAppStepValue = oNodeData.DTVALUESSet.find(oValue => {
					return oValue.RowNum === oAction.RowNum &&
						(oValue.ColName === "APPSTEP");
				});
				if(oAppStepValue) {
					arrActions.forEach(sAction => {
						let oMatchedLine = oGraphLines.find(oLine => {
							return oLine.getFrom() === oNodeDetails.getKey() &&
								oLine.getCustomData()[0].getValue().AppStep === oAppStepValue.ColValue &&
								oLine.getCustomData()[0].getValue().RowNum.toString() === oAction.RowNum;

						});
						if(!oMatchedLine) {
							oController.ParentController.createNewLineForGraphNode(
								oGraph, oController.ParentController, undefined, undefined, oNodeDetails.getKey(), sMergeNodeKey, sAction,
								parseInt(oAction.RowNum, 10), undefined, undefined, true, undefined, undefined, undefined, undefined, oAppStepValue.ColValue, undefined, undefined, undefined);                                              
						}
					});
				}
			});	
		} else if(oController.WorkflowType === DMRNode.Types.Dynamic && arrRowsToDelete && arrRowsToDelete.length > 0) {
			/**
			 * Bug 11565 - Extra rows in connections table after dynamic node table is modified
			 * If array consists of records marked for deletion in a dynamic node, update the line custom data with the correct RowNum as per DTVALUESSet
			 */
			let oGraph = oController.ParentController.getView().byId("graph");
			let oGraphLines = oGraph.getLines();
			oGraphLines.forEach(oLine => {
				if(oLine.getFrom() === oNodeDetails.getKey()) {
					let oAppStep = oNodeData.DTVALUESSet.find(oValue => {
						return oValue.ColName === "APPSTEP" &&
								oValue.ColValue === oLine.getCustomData()[0].getValue().AppStep;
					});
					if(oAppStep) {
						oLine.getCustomData()[0].getValue().RowNum = Number(oAppStep.RowNum);
					}
				}	
			});
		}
		
		let oThisDynamicWorkflow = this;
		// Open the transport selection dialog
		let oTransportPromise = this.ParentController._selectTransportPackage(false, true, true, false);
		oTransportPromise.then(async function(oResponseSelected){
			oNodeData.WbTr = oResponseSelected.workbenchTransport;
			oNodeData.Package = oResponseSelected.package;
			// Store the data to the node 
			oThisDynamicWorkflow.ParentController.saveDynamicNodeContent(oThisDynamicWorkflow.Node, oNodeData);
			// Bug 12573 - Function updateConnectionsData is called when the nodes' information is changed
			await oParentController.updateConnectionsData(oThisDynamicWorkflow.ParentController, oThisDynamicWorkflow.Node);
			
			// Close the dialog
			oThisDynamicWorkflow.DynamicNodeEditDialog.close();
		});
	};
	
	oDynamicWorkflowObject.dynamicTableUpdatedHandler = function (oEvent) {
		let iItemCount = oEvent.getParameters().actual;
		this.DynamicWorkflowDialog.dynamicTableUpdated(iItemCount);
	};

	oDynamicWorkflowObject.dynamicTableUpdated = function(iItemCount){
		let oDynamicWorkflowModel = this.JSONViewModel;
		oDynamicWorkflowModel.setProperty("/dynamic/dataCount", iItemCount);
		if(iItemCount === 0){
			oDynamicWorkflowModel.setProperty("/dynamic/tableMode", sap.m.ListMode.SingleSelectMaster );
		}
	};

	return oDynamicWorkflowObject;
});