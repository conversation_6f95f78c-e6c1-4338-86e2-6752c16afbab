sap.ui.define([
	
], function () {
	"use strict";
	let SelectAttribute = {};
	
	SelectAttribute.getAttributeSelectDialog = function(thisFragment){
		// Open the attribute dialog
		if (!thisFragment.SelectAttributeDialog) {
			thisFragment.SelectAttributeFragmentID = "SelectAttributeFragmentID";
			
			thisFragment.SelectAttributeDialog =
				sap.ui.xmlfragment(
					this.SelectAttributeFragmentID,
					"dmr.mdg.supernova.SupernovaFJ.view.Workflow.SelectAttribute",
					thisFragment.ParentObject.ParentController);
			thisFragment.ParentObject.ParentController.getView().addDependent(thisFragment.SelectAttributeDialog);
		}
		
		return thisFragment.SelectAttributeDialog;
	};
	
	/**
	 * Open the Select Attribute dialog with the initial data
	 * 
	 * CALLED FROM DIFFERENT MODULE. RUNS IN ITS OWN CONTEXT
	 *  	oParentCaller: The parent object invoking this function
	 *		oViewDataModel: The data model for the component
	 *		sAttributeType: Driving or Deriving
	 *		sEntityName: Name of the entity to be selected by default 
	 *		sDataModel: The datamodel from which the entity / attribute are to be selected
	 */
	 SelectAttribute.openSelectAttributeDialog = function(
	 	oParentCaller, oViewDataModel, sAttributeType, sEntityName, sDataModel)
	 {
	 	// Get model 
		//let oBusinessRuleModel = oComponent.getComponentModel(oComponent);
		let thisObject = this;
		this.ParentObject = oParentCaller; 
		this.JSONViewModel = oViewDataModel;
		let oDynamicWorkflowModel = this.JSONViewModel;
		this.DataModel = sDataModel;
		
		let oSelectAttributeDialog = this.getAttributeSelectDialog(thisObject);
		
		// Initialize the Node Edit Model Data
		oDynamicWorkflowModel.setProperty("/SelectAttributeDialog", {});
		oDynamicWorkflowModel.setProperty("/SelectAttributeDialog/attributeType", sAttributeType);
		oDynamicWorkflowModel.setProperty("/SelectAttributeDialog/selected", {});
		oDynamicWorkflowModel.setProperty("/SelectAttributeDialog/selected/entity", sEntityName);

		// Fill the entity list
		// Bug 12199 - Field Property Shows Entities which are not part of the CR type - 0G
		let promiseEntityList = this.ParentObject.ParentController.GetListData.getModelEntityList(oParentCaller.ParentController, sDataModel, "UsmdModel", this.ParentObject.CRTypeDetails.CrTypeName, "Workflow", true);
		promiseEntityList.then(function(oEntityList){
			// If only key attributes are to be selected (for Parallel flow), the entities also need to be filtered to type 4
			let arrFilteredEntityList = oEntityList;
			
			oDynamicWorkflowModel.setProperty("/SelectAttributeDialog/entityList", arrFilteredEntityList);
			
			/* If there is a previous selection for Entity, trigger the selection Change event on the combobox */
			let oEntityComboBox = sap.ui.getCore().byId(thisObject.SelectAttributeFragmentID + "--idCBoxEntities");
			// Force Display the list content with the updated data
			oEntityComboBox.syncPickerContent();
			// trigger the selection change event if an item was selected
			let oSelectedEntity = oEntityComboBox.getSelectedItem();
			if(oSelectedEntity) {
				oEntityComboBox.fireSelectionChange({selectedItem: oSelectedEntity});
			}
		});
		
		// Open the attribute dialog
		oSelectAttributeDialog.open();
	 };
	 
	/**
	 * Invoved as a handler to the Attribute Selection Dialog - Entity selection change event
	 * 
	 * INVOKED IN THE CONTEXT OF THE COMPONENT. this = oController
	 */
	SelectAttribute.onEntitySelectionChangeHandler = function(oEvent)
	{
		let sSelectedEntity = oEvent.getSource().getSelectedKey();
		this.SelectAttributeDialog.onEntitySelectionChange(this, sSelectedEntity);
	};
	
	SelectAttribute.onEntitySelectionChange = function(oController, sSelectedEntity){
		// Get model 
		let oDynamicWorkflowModel = this.JSONViewModel;
		// Retrieve the data model from the component
		let sDataModel = this.DataModel;
		
		let promiseAttributeList = 
			oController.GetListData.getEntityAttributeList(oController, sDataModel, sSelectedEntity, undefined);
		promiseAttributeList.then(function(oAttributes){
			let oAttributeListFiltered = oAttributes; 
			
			oDynamicWorkflowModel.setProperty("/SelectAttributeDialog/selected/attribute", undefined);
			oDynamicWorkflowModel.setProperty("/SelectAttributeDialog/attributeList", oAttributeListFiltered);
		});
		
	};
	
	/**
	 * Called in Component Context this = oController 
	 * 
	 * Return the number of columns in the table. 
	 */
	SelectAttribute.getConfirmButtonState = function (sEntity, sAttribute) {

		if(!sEntity || !sAttribute) { return false; }
		
		if(sEntity.length > 0 && sAttribute.length > 0) { return true; }		

		return false;
	};
	
	/**
	 * Invoved as a handler to the Attribute Selection Dialog - Save Dialog Operation
	 * 
	 * INVOKED IN THE CONTEXT OF THE COMPONENT. this = oComponent
	 */
	SelectAttribute.onAddColumnConfirmHandler = function(){
		
		this.SelectAttributeDialog.onAddColumnConfirm(this);

	};
	
	SelectAttribute.onAddColumnConfirm = function(){
		// Get model 
		let oDynamicWorkflowModel = this.JSONViewModel;
		let oAttributeDetails = oDynamicWorkflowModel.getProperty("/SelectAttributeDialog/selected");
		let arrAttributeLList = oDynamicWorkflowModel.getProperty("/SelectAttributeDialog/attributeList");
		let oSelectedAttribute = arrAttributeLList.find(oAttribute => {
			return oAttribute.UsmdAttribute === oAttributeDetails.attribute;
		});

		if(oSelectedAttribute) {
			oAttributeDetails.attributeDataType = oSelectedAttribute.DATATYPE;
			oAttributeDetails.attributeKind = oSelectedAttribute.Kind;
			oAttributeDetails.attributeDataLenght = oSelectedAttribute.Length;
			oAttributeDetails.attributeDecimals = oSelectedAttribute.Decimals;
			oDynamicWorkflowModel.refresh();
		}
		
		oAttributeDetails.attributeType = oDynamicWorkflowModel.getProperty("/SelectAttributeDialog/attributeType");
		
		// Update the details to the node / process flow 
		this.ParentObject.returnSelectedAttribute(oAttributeDetails);
		
		// Close the dialog 
		let oSelectAttributeDialog = this.SelectAttributeDialog;
		oSelectAttributeDialog.close();
	};
	
	/**
	 * Invoved as a handler to the Attribute Selection Dialog - Cancel Dialog Operation
	 * 
	 * INVOKED IN THE CONTEXT OF THE COMPONENT. this = oComponent
	 */
	SelectAttribute.onAddColumnCancelHandler = function(){
		this.SelectAttributeDialog.onAddColumnCancel();
	}; 
	
	SelectAttribute.onAddColumnCancel = function(){
		this.SelectAttributeDialog.close();	
	};
	
	return SelectAttribute;
});