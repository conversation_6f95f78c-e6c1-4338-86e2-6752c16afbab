sap.ui.define([
		"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
		"sap/m/MessageBox",
		"sap/ui/model/json/JSONModel",
		"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
		"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
		"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
		"dmr/mdg/supernova/SupernovaFJ/model/ModelMessages"
	],
	function (
		BaseController, MessageBox, JSONModel, DMRDataService, 
		GetListsData, Utilities, ModelMessages ) 
	{
		"use strict";
		
		let WorkflowController = {};

		WorkflowController.onChangeRequestSearchListCreated = function (oEvent) {
			let oController = this;

			let comp = oEvent.getParameter("component");
			oController.ChangeRequestSearchList = comp;

			// Complete the mapping for data 
			oController.ChangeRequestSearchList.setDataMapping({
				"title": "UsmdCreqType",
				"description": "Txtmi",
				"info": "IsDraft",
				"infoState": "DraftState"
			});

			oController.ChangeRequestSearchList.setHeaderText("Change Requests");
			oController.ChangeRequestSearchList.setButtonInfo({
				icon: "sap-icon://add",
				toolTip: "New Change Request"
			});

			//Add details of a second button to be shown on search field
			oController.ChangeRequestSearchList.setSecondButtonInfo({
				icon: "sap-icon://copy",
				toolTip: "Copy Change Request"
			});

			oController.ChangeRequestSearchList.setListGroupingPath("UsmdModel");

			oController.ChangeRequestSearchList.attachSelectionChange(oController.onChangeRequestSelect, oController);
			oController.ChangeRequestSearchList.attachActionPressed(oController.onCreateNewCR, oController);
			oController.ChangeRequestSearchList.attachSecondActionPressed(oController.onPressCopyChangeRequestType, oController);

			// Get the CR list 
			let promiseChangeRequestsList =
				GetListsData.getChangeRequestsList(oController);

			promiseChangeRequestsList.then(function (oData) {

				let oView = oController.getView();
				let oJsonModel = new JSONModel();
				// Set the data to the json model 
				oJsonModel.setData(oData);
				// Set the data model to the view.
				oView.setModel(oJsonModel, "changeReqs");
				oData.results.forEach(oItem => {
					oItem.DraftState = oItem.IsDraft ? "Warning" : "None";
					oItem.IsDraft = oItem.IsDraft ? "Draft" : "";
				});
				oController.ChangeRequestSearchList.setListData(oData.results);
			});
		};


		// TransportPackage Componente Created
		WorkflowController.onTransportPackageComponentCreated = function(oEvent) {
			let comp = oEvent.getParameter("component");
			this._transportPackageDialog = comp;

			this.getUsername()
			.then(function (oSapUserInfo) {
				let sUsername = oSapUserInfo.Sapname;
				if (!sUsername) {
					Utilities.showPopupAlert("Please login to continue.", MessageBox.Icon.Error, "Logged out");
				} else {
					comp.setUser(sUsername);
				}
			});
		};

		WorkflowController._selectTransportPackage = function(bCustomizing, bWorkbench, bPackage) {
			let oModel = this.getView().getModel("viewWorkflow");

			// Read the previous selections (if any)
			let sCustomzingTransport = oModel.getProperty("/customzingTransport");
			let sWorkbenchTransprt = oModel.getProperty("/workbenchTransport");
			let sPackage = oModel.getProperty("/package");

			let promise = this._transportPackageDialog.open(
				bCustomizing, sCustomzingTransport, 
				bWorkbench, sWorkbenchTransprt,
				bPackage, sPackage
			);

			let promiseResponse = promise.then(function(oResponseSelection){
				if(bCustomizing){
					oModel.setProperty("/customzingTransport", oResponseSelection.customizingTransport);
				}

				if(bWorkbench){
					oModel.setProperty("/workbenchTransport", oResponseSelection.workbenchTransport);
				}

				if(bPackage) {
					oModel.setProperty("/package", oResponseSelection.package);	
				}

				return oResponseSelection;
			});

			return promiseResponse;
		};
		
		WorkflowController._onRouteMatched = function (oEvent) {

			// Get the parameter and store to the model
			let sChangeRequestName = oEvent.getParameter("arguments").changerequest;
			let sTransport = oEvent.getParameter("arguments").transport;

			let oViewModel = this.getView().getModel("viewWorkflow");

			// if the change is internal, return and do not reset
			if ((sChangeRequestName !== undefined || sTransport !== undefined) && oViewModel !== undefined) {
				return;
			}
			
			//Clear Change Request Search List selection if component is already created
			if(this.ChangeRequestSearchList) {
				this.ChangeRequestSearchList.removeSelectedItem();
			}

			// Model for the CR Edit View

			if (!oViewModel) {
				oViewModel = new JSONModel();
				this.getView().setModel(oViewModel, "viewWorkflow");
			}
			oViewModel.setProperty("/", {});
			oViewModel.setProperty("/selectedChangeRequest", {});
			oViewModel.setProperty("/oCopyChangeRequest", {});
			oViewModel.refresh();
			
			let promiseDataModelsList =
				GetListsData.getDataModelsList(this);

			promiseDataModelsList.then(function (oData) {
				oViewModel.setProperty("/arrDataModel", oData.results);
				oViewModel.refresh();
			});
			
			let promiseEditionType =
				GetListsData.getEditionTypeList(this);

			promiseEditionType.then(function (oData) {
				oViewModel.setProperty("/arrEditionType", oData.results);
				oViewModel.refresh();
			});

			// Model for selected group header 
			let oChangeRequestListModel = this.getView().getModel("changeRequestListModel");
			if (!oChangeRequestListModel) {
				oChangeRequestListModel = new JSONModel();
				this.getView().setModel(oChangeRequestListModel, "changeRequestListModel");
			}
			oChangeRequestListModel.setProperty("/selectedGroupHeader", "");
			oChangeRequestListModel.refresh();

			let oWorkFlowTableModel = this.getView().getModel("approverModel");
			if (!oWorkFlowTableModel) {
				oWorkFlowTableModel = new JSONModel();
				this.getView().setModel(oWorkFlowTableModel, "approverModel");
			}

			// set the table list model with an empty list 
			oWorkFlowTableModel.setProperty("/ApprList", {
				results: []
			});
			oWorkFlowTableModel.refresh();
		};
		/**
		 * Called when a controller is instantiated and its View controls (if available) are already created.
		 * Can be used to modify the View before it is displayed, to bind event handlers and do other one-time initialization.
		 * @memberOf dmr.mdg.supernova.SupernovaFJ.view.Workflow
		 */
		WorkflowController.onInit = function () {
			let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
			
			// Bug 12409 - Cannot import simple workflows in T42
			this.ModelMessages = ModelMessages;

			// Load the parameters received into the model 
			oRouter.getRoute("Workflow").attachMatched(this._onRouteMatched, this);
		};

		WorkflowController.onApproverTypeChange = function () {
			this._updatedApproverList("*"); // No filter just show everything
		};
		
		WorkflowController.approverValueChanged = function () {
			let sEnteredText = this.getView().getModel("approverModel").getProperty("/ApprDetails").ApprValDetailsNoUse;
			this._updatedApproverList(sEnteredText);
		};
		
		WorkflowController._updatedApproverList = function (sFilterText) {
			let sSelectedType = this.getView().getModel("approverModel").getProperty("/ApprDetails").ApprType;
			let oWorkFlowTableModel = this.getView().getModel("approverModel");

			let sFilterTextInput = (sFilterText === "*" ? "*" : sFilterText + "*");

			switch (sSelectedType) {
			case "AG": //AG - Security Role
				let promiseUserRoles = GetListsData.getUserRolesList(this, sFilterTextInput);
				promiseUserRoles.then(
					function (oData) {
						oWorkFlowTableModel.setProperty("/ApprRoleUserList", oData);
					}
				);
				break;
			case "US": //US - User
				let promiseUsers = GetListsData.getUserList(this, sFilterTextInput);
				promiseUsers.then(
					function (oData) {
						oWorkFlowTableModel.setProperty("/ApprRoleUserList", oData);
					}
				);
				break;
			case "SU": //SU - Special User
				oWorkFlowTableModel.setProperty("/ApprRoleUserList", {
					results: [{
						sukey: "SU",
						sutext: "INIT"
					}]
				});
				break;
			default:
				break;
			}

			// Set the item limit of the combobox to a larger number NOT A GOOD IDEA
			sap.ui
				.getCore().byId("ApproverSelectDialog--idApproverValue")
				.getModel("approverModel")
				.setSizeLimit(1000);
		};

		WorkflowController.onChangeRequestSelect = function (oEvent) {
			let oController = this;
			let oSelectedListObject = {};
			let oWorkFlowModel = this.getView().getModel("viewWorkflow");

			oController.byId("idChangeRequestPanel").setExpanded(false);
			oController.byId("idWorkflowGraphPanel").setExpanded(true);

			//If the user navigates to CR workflow from Add new CR after entering an existing CR
			if (oWorkFlowModel.getProperty("/crTypeValueState") === sap.ui.core.ValueState.Error) {
				oWorkFlowModel.setProperty("/crTypeValueState", sap.ui.core.ValueState.None);
				oWorkFlowModel.setProperty("/crTypeValueStateText", "");
			}
			oSelectedListObject = oEvent.getParameters().data;
			let oSelectedObject = {};
			jQuery.extend(true, oSelectedObject, oSelectedListObject);
			
			if (oSelectedObject.UsmdCreqType) {
				let promiseChangeRequestData = 
					GetListsData.getChangeRequestData(oController, oSelectedObject.UsmdCreqType);
					
				promiseChangeRequestData.then(oChangeRequestData => {
					if (!oChangeRequestData.UsmdModel &&
						!oChangeRequestData.UsmdEdtnType) {
						// Return is Model was not selected
						return;
					}
					
					let promiseChangeRequestSearchLists;
					if(oChangeRequestData.UsmdModel) {
						promiseChangeRequestSearchLists =
							GetListsData.getChangeRequestSearchLists(oController, oChangeRequestData.UsmdModel);
							
					} else if(oChangeRequestData.UsmdEdtnType) {
						let sUsmdModel;
						let oEditionList = oWorkFlowModel.getProperty("/arrEditionType");
						let oSelectedEditionType = oEditionList.find(element => {
							if (element.Usmdedtntype === oChangeRequestData.UsmdEdtnType) {
								return true;
							}
							return false;
						});
						sUsmdModel = oSelectedEditionType.Usmdmodel;
						promiseChangeRequestSearchLists =
							GetListsData.getChangeRequestSearchLists(oController, sUsmdModel);
					}
					
					promiseChangeRequestSearchLists.then(oData => {
						oWorkFlowModel.setSizeLimit(Math.max(oWorkFlowModel.iSizeLimit, oData.CR2BUSNAV.results.length, oData.CR2MSGNAV.results.length, oData.CR2TYPE1NAV.results.length));
						oWorkFlowModel.setProperty("/arrBusinessActivity", oData.CR2BUSNAV.results);
						oWorkFlowModel.setProperty("/arrEntityMessage", oData.CR2MSGNAV.results);
						oWorkFlowModel.setProperty("/arrType1Entity", oData.CR2TYPE1NAV.results);
						
						// Store the selected Object to the Model 
						oChangeRequestData.IsDraft = oSelectedListObject.IsDraft;
						oChangeRequestData.UsmdSingleObj = ( oChangeRequestData.UsmdSingleObj === "X" ) ? true : false;
						oChangeRequestData.UsmdTargetSys = ( oChangeRequestData.UsmdTargetSys === "X" ) ? true : false;
						oChangeRequestData.UsmdPcr = ( oChangeRequestData.UsmdPcr === "X" ) ? true : false;
						oChangeRequestData.SkipApprover = ( oChangeRequestData.SkipApprover === "X" ) ? true : false;
						oChangeRequestData.UsmdClfLean = ( oChangeRequestData.UsmdClfLean === "X" ) ? true : false;
						oChangeRequestData.FourEyesPrinciple = ( oChangeRequestData.FourEyes === "X" ) ? true : false;
						oChangeRequestData.DisableFieldProperties = ( oChangeRequestData.DisableProperties === "X" ) ? true : false;
						oChangeRequestData.CrequestToEntityNav = oChangeRequestData.CrequestToEntityNav.results;
						
						if(oChangeRequestData.CrequestToEntityNav.length > 0) {
							oChangeRequestData.CrequestToEntityNav.forEach(oLeadingEntity => {
								let oMatchedLeadingEntity = oWorkFlowModel.getProperty("/arrType1Entity").find(oType1Entity => {
									return oType1Entity.Type1entity === oLeadingEntity.UsmdEntity;
								});
								if(oMatchedLeadingEntity) {
									oLeadingEntity.Type4Entities = oMatchedLeadingEntity.TYPE12TYPE4NAV.results;
								}
								
								oLeadingEntity.UsmdScope = [];
								if(oLeadingEntity.EntityToScopeNav.results.length > 0) {
									oLeadingEntity.EntityToScopeNav.results.forEach(oScope => {
										oLeadingEntity.UsmdScope.push(oScope.UsmdEntity);
									});
								}
							});
						}
						
						oWorkFlowModel.setProperty("/selectedChangeRequest", oChangeRequestData);
						oWorkFlowModel.setProperty("/changeRequestType", "existing");
						oWorkFlowModel.refresh();
						
						oController._notifySelectionChange(undefined, oChangeRequestData);
					});
				});
			}
		};
		
		WorkflowController.comboBoxContentChanged = function () { // Unused
			// Search for the key within the list of change requests, if not 
		};

		WorkflowController.onLiveChangeCRElement = function (oEvent) {
			let input = oEvent.getSource();
			let oModelData = this.getView().getModel("changeReqs").getData().results;
			let oViewWorkflow = this.getView().getModel("viewWorkflow");

			input.setValue(input.getValue().toUpperCase());
			let firstLetter = input.getValue().slice(0, 1);
			let oSelectedObject = oModelData.find(function (element) {
				if (element.UsmdCreqType === input.getValue()) {
					return true;
				}
				return false;
			});
			if(firstLetter === "Z" || firstLetter === "Y"){
				if (oSelectedObject) {
					oViewWorkflow.setProperty("/crTypeValueState", sap.ui.core.ValueState.Error);
					oViewWorkflow.setProperty("/crTypeValueStateText", "Change Request already exists");
				} else if(!/^[A-Z0-9/_]*$/.test(input.getValue().toUpperCase())) {
					oViewWorkflow.setProperty("/crTypeValueState", sap.ui.core.ValueState.Error);
					oViewWorkflow.setProperty("/crTypeValueStateText", "Change Request is of invalid format");
				}else if (/_$/.test(input.getValue().toUpperCase())) {
					// Value ends with an underscore
					oViewWorkflow.setProperty("/crTypeValueState", sap.ui.core.ValueState.Error);
					oViewWorkflow.setProperty("/crTypeValueStateText", "Underscore at the end is not permitted");
				} else {
					if (oViewWorkflow.getProperty("/crTypeValueState") === sap.ui.core.ValueState.Error) {
						oViewWorkflow.setProperty("/crTypeValueState", sap.ui.core.ValueState.None);
						oViewWorkflow.setProperty("/crTypeValueStateText", "");
					}
				}
			}else{
				oViewWorkflow.setProperty("/crTypeValueState", sap.ui.core.ValueState.Error);
				oViewWorkflow.setProperty("/crTypeValueStateText", "Change Request Type must start with Z or Y");
			}
		};

		/**
		 * Filter the Change Request List of Copy/Import Popover
		 * For Import, only change request types which has workflow defined must be shown
		 */
		WorkflowController.onChangeNewCrTypeDataModel =   function(oEvent) {
			let sSelectedDataModel = oEvent.getSource().getSelectedKey();
			let oChangeReqsList = this.getView().getModel("changeReqs").getProperty("/results");
			let oCopyCrType = this.getView().getModel("viewWorkflow").getProperty("/oCopyChangeRequest");
			let arrFilteredCrList = [];
			if(oCopyCrType.action === "Copy") {
				arrFilteredCrList = oChangeReqsList.filter(oChangeReq => {
					return oChangeReq.UsmdModel === sSelectedDataModel;
				});
			} else {
				arrFilteredCrList = oChangeReqsList.filter(oChangeReq => {
					return oChangeReq.UsmdModel === sSelectedDataModel && oChangeReq.Iswf === "X";
				});
			}			
			this.getView()
				.getModel("viewWorkflow")
				.setProperty("/oCopyChangeRequest/arrCrList", arrFilteredCrList);
  		};

		WorkflowController.onCopyChangeRequestType = function() {
			let oController = this;
			let oWorkflowModel = this.getView().getModel("viewWorkflow");
			let oCopyCrType = oWorkflowModel.getProperty("/oCopyChangeRequest");
			let sCopyFromCrType = oWorkflowModel.getProperty("/oCopyChangeRequest/selectedCopyFrom");
			if(oCopyCrType.action === "Copy") {

				let promiseChangeRequestData = 
					GetListsData.getChangeRequestData(oController, sCopyFromCrType);
					
				promiseChangeRequestData.then(oChangeRequestData => {
					if (oChangeRequestData.IsDraft) {
						Utilities.showPopupAlert("The Change Request cannot be copied because its current workflow version is not active, please activate it.", MessageBox.Icon.ERROR, "Error");
						return;
					}
					oWorkflowModel.setProperty("/changeRequestType", "new");
					oController.byId("idChangeRequestPanel").setExpanded(true);
					oController.byId("idWorkflowGraphPanel").setExpanded(false);

					oChangeRequestData.UsmdCreqType = "";
					oChangeRequestData.Txtmi = "";
					oChangeRequestData.Sourcecrtype = sCopyFromCrType;
					
					if (!oChangeRequestData.UsmdModel &&
						!oChangeRequestData.UsmdEdtnType) {
						// Return is Model was not selected
						return;
					}
					
					let promiseChangeRequestSearchLists;
					if(oChangeRequestData.UsmdModel) {
						promiseChangeRequestSearchLists =
							GetListsData.getChangeRequestSearchLists(oController, oChangeRequestData.UsmdModel);
							
					} else if(oChangeRequestData.UsmdEdtnType) {
						let sUsmdModel;
						let oEditionList = oWorkflowModel.getProperty("/arrEditionType");
						let oSelectedEditionType = oEditionList.find(element => {
							if (element.Usmdedtntype === oChangeRequestData.UsmdEdtnType) {
								return true;
							}
							return false;
						});
						sUsmdModel = oSelectedEditionType.Usmdmodel;
						promiseChangeRequestSearchLists =
							GetListsData.getChangeRequestSearchLists(oController, sUsmdModel);
					}
					
					promiseChangeRequestSearchLists.then(oData => {
						oWorkflowModel.setSizeLimit(Math.max(oWorkflowModel.iSizeLimit, oData.CR2BUSNAV.results.length, oData.CR2MSGNAV.results.length, oData.CR2TYPE1NAV.results.length));
						oWorkflowModel.setProperty("/arrBusinessActivity", oData.CR2BUSNAV.results);
						oWorkflowModel.setProperty("/arrEntityMessage", oData.CR2MSGNAV.results);
						oWorkflowModel.setProperty("/arrType1Entity", oData.CR2TYPE1NAV.results);
						
						// Store the selected Object to the Model 
						oChangeRequestData.UsmdSingleObj = ( oChangeRequestData.UsmdSingleObj === "X" ) ? true : false;
						oChangeRequestData.UsmdTargetSys = ( oChangeRequestData.UsmdTargetSys === "X" ) ? true : false;
						oChangeRequestData.UsmdPcr = ( oChangeRequestData.UsmdPcr === "X" ) ? true : false;
						oChangeRequestData.SkipApprover = ( oChangeRequestData.SkipApprover === "X" ) ? true : false;
						oChangeRequestData.UsmdClfLean = ( oChangeRequestData.UsmdClfLean === "X" ) ? true : false;
						// Bug 13045 - Assigning missing values for 4 eyes and disable properties when copying
						oChangeRequestData.FourEyesPrinciple = ( oChangeRequestData.FourEyes === "X" ) ? true : false;
						oChangeRequestData.DisableFieldProperties = ( oChangeRequestData.DisableProperties === "X" ) ? true : false;
						oChangeRequestData.CrequestToEntityNav = oChangeRequestData.CrequestToEntityNav.results;
						
						if(oChangeRequestData.CrequestToEntityNav.length > 0) {
							oChangeRequestData.CrequestToEntityNav.forEach(oLeadingEntity => {
								let oMatchedLeadingEntity = oWorkflowModel.getProperty("/arrType1Entity").find(oType1Entity => {
									return oType1Entity.Type1entity === oLeadingEntity.UsmdEntity;
								});
								if(oMatchedLeadingEntity) {
									oLeadingEntity.Type4Entities = oMatchedLeadingEntity.TYPE12TYPE4NAV.results;
								}
								
								oLeadingEntity.UsmdScope = [];
								if(oLeadingEntity.EntityToScopeNav.results.length > 0) {
									oLeadingEntity.EntityToScopeNav.results.forEach(oScope => {
										oLeadingEntity.UsmdScope.push(oScope.UsmdEntity);
									});
								}
							});
						}
						
						oWorkflowModel.setProperty("/selectedChangeRequest", oChangeRequestData);
						oWorkflowModel.setProperty("/changeRequestType", "new");
						oWorkflowModel.refresh();
					});
				});
			} else {
				/**
				 * Invoke transportSelectDialog for Customizing TR 
				 * If sourceCrType has dynamic/parallel Workflow, then invoke transportSelectDialog and packageSelectDialog
				 * Call service to import workflow details from sourceCrType to targetCrType
				 */	
				let oSelectedCr = this.getView().getModel("viewWorkflow").getProperty("/selectedChangeRequest");
				
				let oData = {
					"UsmdModel": oCopyCrType.selectedDataModel,
					"SourceCr": oCopyCrType.selectedCopyFrom,
					"TargetCr": oSelectedCr.UsmdCreqType,
					"Featuretype": "3",
					"NAV_CRTORULES": [],
					"NAV_CRTOMSGDET": [] // Bug 12409 - Cannot import simple workflows in T42
				};

				//Proceed to next step after user selects the Transport and package
				oController._selectTransportPackage(true, true, true)
				.then(function(oResponseSelection){
					oData.CustTransport = oResponseSelection.customizingTransport;
					oData.WbTransport = oResponseSelection.workbenchTransport;
					oData.Package = oResponseSelection.package;

					oController.importChangeRequestTypeService(oController, oData);
				});
			}			
			this.CopyChangeRequestPopover.close();
		};

		WorkflowController.importChangeRequestTypeService = function(oController, oData) {
			let oChangeRequestData = this.getView().getModel("viewWorkflow").getProperty("/selectedChangeRequest");

			(new DMRDataService(
				oController,
				"COPY_BUSINESS_RULES",
				"/CRTYPESSet",
				"ImportWorkflow",
				"/", // Root of the received data
				"Import Workflow"
			)).saveData(
				false,
				oData,
				null, {
					success: {
						fCallback: function (oParams, oResponseData) {
							// Bug 12409 - Cannot import simple workflows in T42
							let bError = false;
							let arrMessages = [];
							
							jQuery.extend(true, arrMessages, oResponseData.NAV_CRTOMSGDET.results);

							arrMessages.sort(function (m1) {
								if (m1.MessageType === "E") {
									bError = true;
									return -1;
								}
								if (m1.MessageType === "W") {
									return 1;
								}
								return 0;
							});
							
							sap.ui.core.BusyIndicator.hide();
							ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
							
							if (!bError) {
								oParams.getView().getModel("viewWorkflow").setProperty("/changeRequestType", "existing");
								oController.ChangeRequestSearchList.setSelectedItemByTitle(oChangeRequestData.UsmdCreqType);
							}
						},
						oParam: oController
					},
					error: {
						fCallback: function (oParams, oResponseData) {
							let sMessage = oResponseData.statusCode + ": " + oResponseData.message;
							Utilities.showPopupAlert("Error " + sMessage, MessageBox.Icon.ERROR, "Error");
						}
					}
				}
			);
			oController.getView().getModel("viewWorkflow").setProperty("/customzingTransport", undefined);
			oController.getView().getModel("viewWorkflow").setProperty("/workbenchTransport", undefined);
			oController.getView().getModel("viewWorkflow").setProperty("/package", undefined);
		};

		WorkflowController.copyCrTypeEnabled = function(sDataModel, sChangeRequestType) {
			let oChangeReqList = this.getView().getModel("changeReqs").getProperty("/results");
			if(!sDataModel) {
				return false;
			} else if(!sChangeRequestType) {
				return false;
			} else {
				let oMatchedCrType = oChangeReqList.find(oCrType => {
					return oCrType.UsmdModel === sDataModel 
							&& oCrType.UsmdCreqType === sChangeRequestType;
				});
				if(!oMatchedCrType) {
					return false;
				}
			}
			return true;
			
		};

		WorkflowController.onAfterClosePopover = function() {
			this.getView()
				.getModel("viewWorkflow")
				.setProperty("/oCopyChangeRequest", {});
		};

		WorkflowController.onPressCopyChangeRequestType = function(oEvent) {
			let oCopyButton = oEvent.getSource()._secondActionButton;
			if (!this.CopyChangeRequestPopover) {
				this.CopyChangeRequestPopover =
					sap.ui.xmlfragment(
						"CopyChangeRequest",
						"dmr.mdg.supernova.SupernovaFJ.view.Workflow.CopyChangeRequest",
						this);
				this.getView().addDependent(this.CopyChangeRequestPopover);
			}
			this.CopyChangeRequestPopover.openBy(oCopyButton);
			this.getView()
				.getModel("viewWorkflow")
				.setProperty("/oCopyChangeRequest", {
					action: "Copy",
					popoverTitle: "Copy From"	
				});	
		};
		
		WorkflowController.onCreateNewCR = function () {
			this.getView().getModel("viewWorkflow").setProperty("/selectedChangeRequest", {
				UsmdSingleObj: true
			});
			this.getView().getModel("viewWorkflow").setProperty("/changeRequestType", "new");

			this.byId("idChangeRequestPanel").setExpanded(true);
			this.byId("idWorkflowGraphPanel").setExpanded(false);

			this.ChangeRequestSearchList.removeSelectedItem();

			// Update the Data model request 
			this.onSelDModel(null);
		};

		WorkflowController.onSelDModel = function () {
			let oChangeRequestModel = this.getView().getModel("viewWorkflow").getProperty("/selectedChangeRequest");
			let oWorkFlowModel = this.getView().getModel("viewWorkflow");
		
			// Clean up the lists 
			oWorkFlowModel.setProperty("/arrBusinessActivity", []);
			oWorkFlowModel.setProperty("/arrEntityMessage", []);
			oWorkFlowModel.setProperty("/arrType1Entity", []);
			oChangeRequestModel.UsmdEntityMain = undefined;
			oChangeRequestModel.UsmdProcess = undefined;
			oChangeRequestModel.UsmdClfLean = undefined;
			oChangeRequestModel.CrequestToEntityNav = [];

			if (!oChangeRequestModel.UsmdModel &&
				!oChangeRequestModel.UsmdEdtnType) {
				// Return is Model was not selected
				return;
			}
			
			let promiseChangeRequestSearchLists;
			if(oChangeRequestModel.UsmdModel) {
				promiseChangeRequestSearchLists =
					GetListsData.getChangeRequestSearchLists(this, oChangeRequestModel.UsmdModel);
				
				promiseChangeRequestSearchLists.then(oData => {
					oWorkFlowModel.setProperty("/arrBusinessActivity", oData.CR2BUSNAV.results);
					oWorkFlowModel.setProperty("/arrEntityMessage", oData.CR2MSGNAV.results);
					oWorkFlowModel.setProperty("/arrType1Entity", oData.CR2TYPE1NAV.results);
					let arrDefaultLeadingEntity = [];
					
					if(oChangeRequestModel.UsmdModel === "BP") {
						
						arrDefaultLeadingEntity = [{
							"Name": "ADDRNO"
						}, {
							"Name": "BP_HEADER"
						}, {
							"Name": "BP_REL"
						}];
						
					} else if(oChangeRequestModel.UsmdModel === "MM") {
						arrDefaultLeadingEntity =  [{
							"Name": "MATERIAL"
						}];
					}
					
					if(arrDefaultLeadingEntity.length > 0) {
						
						arrDefaultLeadingEntity.forEach(oEntity => {
							let oLeadingEntity = oWorkFlowModel.getProperty("/arrType1Entity").find(oType1Entity => {
								return oType1Entity.Type1entity === oEntity.Name;
							});
							if(oLeadingEntity) {
								oChangeRequestModel.CrequestToEntityNav.push({
									UsmdCreqType: oChangeRequestModel.UsmdCreqType,
									UsmdEntity: oEntity.Name,
									UsmdEntityMsg: "",
									UsmdScope: [],
									Type4Entities: oLeadingEntity.TYPE12TYPE4NAV.results
								});
							}
						});
					}
					
					oWorkFlowModel.refresh();
				});

			} else if(oChangeRequestModel.UsmdEdtnType) {
				let sUsmdModel;
				let oEditionList = oWorkFlowModel.getProperty("/arrEditionType");
				let oSelectedEditionType = oEditionList.find(element => {
					if (element.Usmdedtntype === oChangeRequestModel.UsmdEdtnType) {
						return true;
					}
					return false;
				});
				sUsmdModel = oSelectedEditionType.Usmdmodel;
				promiseChangeRequestSearchLists =
					GetListsData.getChangeRequestSearchLists(this, sUsmdModel);
				
				promiseChangeRequestSearchLists.then(oData => {
					oWorkFlowModel.setSizeLimit(Math.max(oWorkFlowModel.iSizeLimit, oData.CR2BUSNAV.results.length, oData.CR2MSGNAV.results.length, oData.CR2TYPE1NAV.results.length));
					oWorkFlowModel.setProperty("/arrBusinessActivity", oData.CR2BUSNAV.results);
					oWorkFlowModel.setProperty("/arrEntityMessage", oData.CR2MSGNAV.results);
					oWorkFlowModel.setProperty("/arrType1Entity", oData.CR2TYPE1NAV.results);
					oWorkFlowModel.refresh();
				});
			}
		};
		
		/*
			Add a row to Leading Entity table when user clicks on Add
		*/
		WorkflowController.onAddLeadingEntity = function() {
            let oController = this;
            let oWorkFlowModel = oController.getView().getModel("viewWorkflow");
            let oChangeRequestModel = oWorkFlowModel.getProperty("/selectedChangeRequest");
            let arrCrequestToEntityNav = oWorkFlowModel.getProperty("/selectedChangeRequest/CrequestToEntityNav");

            // Bug 12396 - Make Edition type editable and mandatory for 0G data Model
            let arrLeadingEntities = [];
            if (arrCrequestToEntityNav) {
                jQuery.extend(true, arrLeadingEntities, arrCrequestToEntityNav);    
            }

			arrLeadingEntities.push({
                UsmdCreqType: oChangeRequestModel.UsmdCreqType,
                UsmdEntity: undefined,
                UsmdEntityMsg: "",
                UsmdScope: [],
                Type4Entities: []
            });

            oWorkFlowModel.setProperty("/selectedChangeRequest/CrequestToEntityNav", arrLeadingEntities);
            oWorkFlowModel.refresh();
        };
		
		/**
		 * Delete a row from Leading Entity Table
		 */
		WorkflowController.onDeleteLeadingEntity = function(oEvent) {
            let oController = this;
            let oWorkFlowModel = oController.getView().getModel("viewWorkflow");
            let oTable = oEvent.getSource();
            let oClickedItem = oEvent.getParameters().listItem;
            let iClickedIndex = oTable.getItems().indexOf(oClickedItem);
            let arrCrequestToEntityNav = oWorkFlowModel.getProperty("/selectedChangeRequest/CrequestToEntityNav");

            // Bug 12396 - Make Edition type editable and mandatory for 0G data Model
            let arrLeadingEntities = [];
            if (arrCrequestToEntityNav) {
                jQuery.extend(true, arrLeadingEntities, arrCrequestToEntityNav);    
            }

            arrLeadingEntities.splice(iClickedIndex, 1);
            oWorkFlowModel.setProperty("/selectedChangeRequest/CrequestToEntityNav", arrLeadingEntities);
            oWorkFlowModel.refresh();
        };
		
		/**
		 * Event call when the leading entity is changed
		 * Clear all selected Scope 
		 * Populate the Scope Multi ComboBox drop down list on change of Leading Entity
		 */
		WorkflowController.onChangeLeadingEntity = function(oEvent) {
			let oController = this;
			let oWorkFlowModel = oController.getView().getModel("viewWorkflow");			
			let sLeadingEntity = oEvent.getSource().getSelectedKey();
			
			//Get the complete path of row on which the entity was changed 
			let sChangedLeadingEntityRow = oEvent.getSource().getBindingContext("viewWorkflow").getPath();
			
			oWorkFlowModel.setProperty(sChangedLeadingEntityRow + "/UsmdScope", []);
			let oMatchedLeadingEntity = oWorkFlowModel.getProperty("/arrType1Entity").find(oType1Entity => {
				return oType1Entity.Type1entity === sLeadingEntity;
			});
			if(oMatchedLeadingEntity) {
				oWorkFlowModel.setProperty(sChangedLeadingEntityRow + "/Type4Entities", oMatchedLeadingEntity.TYPE12TYPE4NAV.results);
			}
		};
		
		/**
		 * Change event on Business Activity ComboBox
		 * Clear and disable the Parallel Checkbox if selected Business Activity is of action CREATE
		 * Fire Select event on checkbox to clear the scope values if any
		 */
		WorkflowController.onChangeBusinessActivity = function(oEvent) {
			let oController = this;
			let oWorkflowModel = oController.getView().getModel("viewWorkflow");
			let sSelectedKey = oEvent.getSource().getSelectedKey();
			let oMatchedBusActivity = oWorkflowModel.getProperty("/arrBusinessActivity").find(oBusActivity => {
				return oBusActivity.Usmdprocess === sSelectedKey;
			});
			if(oMatchedBusActivity) {
				if(oMatchedBusActivity.Usmdaction === "CREATE") {
					oWorkflowModel.setProperty("/createBusActivity", true);
					oWorkflowModel.setProperty("/selectedChangeRequest/UsmdPcr", false);
					oController.byId("crParallel").fireSelect({
						selected: false
					});
				} else {
					oWorkflowModel.setProperty("/createBusActivity", false);
				}
				// Task 12654 - Show in both lists if CR type is single object and parallel
				oWorkflowModel.setProperty("/selectedChangeRequest/selectedBusinessActivity", oMatchedBusActivity);
				if (oMatchedBusActivity.Usmdaction === "MASS") {
					oWorkflowModel.setProperty("/selectedChangeRequest/UsmdSingleObj", false);
				}
			}
		};
		
		/**
		 * Select Event on Single Object CheckBox
		 * Clear Main Entity value on deselecting the checkbox
		 */
		WorkflowController.onChangeSingleObject = function(oEvent) {
			if(!oEvent.getParameters().selected) {
				this.getView().getModel("viewWorkflow").setProperty("/selectedChangeRequest/UsmdEntityMain", undefined);
			}
			if( this.getView().getModel("viewWorkflow").getProperty("/selectedChangeRequest/UsmdSingleObj") === true  ){
				this.getView().getModel("viewWorkflow").setProperty("/changeRequestTypeChg", "chg");
			}
		};
		
		/**
		 * Select Event on Parallel Checkbox
		 * Clear Scope Values in all Leading Entity Table rows
		 */
		WorkflowController.onChangeParallel = function(oEvent) {
			if(!oEvent.getParameters().selected) {
				let oController = this;
				let oWorkFlowModel = oController.getView().getModel("viewWorkflow");
				let arrLeadingEntityTable = oWorkFlowModel.getProperty("/selectedChangeRequest/CrequestToEntityNav");
				if(arrLeadingEntityTable && arrLeadingEntityTable.length > 0) {
					arrLeadingEntityTable.forEach(oLeadingEntity => {
						oLeadingEntity.UsmdScope = [];
					});
				}
			}
		};

		/**
		 * Select Event on Four Eyes Principle Checkbox
		 * Clear Disable Field Properties Checkbox 
		 */
		WorkflowController.onChangeFourEyesPrinciple = function(oEvent) {
			if(!oEvent.getParameters().selected) {
				this.getView().getModel("viewWorkflow").setProperty("/selectedChangeRequest/DisableFieldProperties", false);
			}
		};

		WorkflowController.createNewCrSaveEnabled = function (sUsmdCreqType, sUsmdEdtnType, sUsmdModel, sUsmdEntityMain, bUsmdSingleObj, arrLeadingEntities, sUsmdProcess, sTxtmi) {
			let sValueState = this.getView().getModel("viewWorkflow").getProperty("/crTypeValueState");
			if (sValueState === sap.ui.core.ValueState.Error) {
				return false;
			} else if (!sUsmdCreqType) {
				return false;
			} else if (!sUsmdModel && !sUsmdEdtnType) {
				return false;
			} else if (bUsmdSingleObj && !sUsmdEntityMain) {
				return false;
			} else if (!arrLeadingEntities || arrLeadingEntities.length === 0) {
				return false;
			}else if (!sUsmdProcess) {
				return false;
			} else if(!sTxtmi){
				return false;	
			//fix 12376 - Able to create new cr type without entering description
			} else if (sUsmdModel === "0G"){
				if(!sUsmdEdtnType){
					return false;
				}
			}
			return true;
		};

		WorkflowController.valueStateDescription = function(oEvent){ 
			// Bug 12434 - Errors when copying change request types (T42 and SD2)
			let sDescription = oEvent.getSource().getProperty("value");
			let oViewWorkflow = this.getView().getModel("viewWorkflow");

			if (!sDescription) {
				oViewWorkflow.setProperty("/TxtmiValueState", "Error");
				oViewWorkflow.setProperty("/TxtmiValueStateText", "Description Must be filled");
			} else {
				oViewWorkflow.setProperty("/TxtmiValueState", "None");
				oViewWorkflow.setProperty("/TxtmiValueStateText", "");
			}
		};

			
		WorkflowController.onSaveCRType = function () {

			//let oChangeReqList = this.getView().getModel("changeReqs").getProperty("/results");
			let oController = this;

			let oChangeRequestModel = this.getView().getModel("viewWorkflow").getProperty("/selectedChangeRequest");
			let leadingEntity = oChangeRequestModel.CrequestToEntityNav;
		

			//fix 12410 - Can save change request type without a leading entity
			if(leadingEntity.length > 0){
				for (let i = 0; i < leadingEntity.length; i++) {
					if (!leadingEntity[i].UsmdEntity) {
						Utilities.showPopupAlert("Please add at least one leading entity!", MessageBox.Icon.ERROR, "Mandatory fields missing");
						return;
					}
				}
			}

			//Fix 12396 - Make Edition type editable and mandatory for 0G data Model
			if(oChangeRequestModel.UsmdModel === "0G"){
				if(!oChangeRequestModel.UsmdEdtnType){
					Utilities.showPopupAlert("Please Select Edition Type!", MessageBox.Icon.ERROR);
					return;
				}
			}

			let oData = {
				"UsmdCreqType": oChangeRequestModel.UsmdCreqType,
				"Txtmi": oChangeRequestModel.Txtmi,
				"UsmdModel": oChangeRequestModel.UsmdModel,
				"UsmdProcess": oChangeRequestModel.UsmdProcess,
				"UsmdObjlistReq": undefined,
				"WbTransport": undefined,
				"Package": undefined,
				"FourEyes": ( oChangeRequestModel.FourEyesPrinciple === true ) ? "X" : undefined,
				"DisableProperties": ( oChangeRequestModel.DisableFieldProperties === true ) ? "X" : undefined,
				"UsmdEntityMain": oChangeRequestModel.UsmdEntityMain,
				"Sourcecrtype": oChangeRequestModel.Sourcecrtype,
				"Message": "",
				"UsmdEdtnType": oChangeRequestModel.UsmdEdtnType,
				"UsmdSingleObj": ( oChangeRequestModel.UsmdSingleObj === true ) ? "X" : undefined,
				"UsmdPcr": ( oChangeRequestModel.UsmdPcr === true ) ? "X" : undefined,
				"UsmdTargetSys": ( oChangeRequestModel.UsmdTargetSys === true ) ? "X" : undefined,
				"UsmdClfLean": ( oChangeRequestModel.UsmdClfLean === true ) ? "X" : undefined,
				"SkipApprover": ( oChangeRequestModel.SkipApprover === true ) ? "X" : undefined,
				"CrequestToEntityNav": []				
			};

			oChangeRequestModel.CrequestToEntityNav.forEach(oLeadingEntity => {
				let oRequestEntityData = {
					UsmdCreqType: oChangeRequestModel.UsmdCreqType,
					UsmdEntity: oLeadingEntity.UsmdEntity,
					UsmdEntityMsg: oLeadingEntity.UsmdEntityMsg,
					EntityToScopeNav: []
				};
				if(oLeadingEntity.UsmdScope.length > 0) {
					oLeadingEntity.UsmdScope.forEach(oScope => {
						oRequestEntityData.EntityToScopeNav.push({
							UsmdCreqType: oChangeRequestModel.UsmdCreqType,
							UsmdEntity: oScope
						});
					});
				}
				oData.CrequestToEntityNav.push(oRequestEntityData);
			});

			//Proceed to next step after user selects the Transport and package
			oController._selectTransportPackage(true, true, true)
			.then(function(oResponseSelection){
				oData.CustTransport = oResponseSelection.customizingTransport;
				oData.WbTransport = oResponseSelection.workbenchTransport;
				oData.Package = oResponseSelection.package;

				oController.createChangeRequestTypeService(oController, oData);
			});
		};

		WorkflowController.createChangeRequestTypeService = function(oController, oData) {
			let oChangeReqs = this.getView().getModel("changeReqs");
			let oChangeRequestModel = this.getView().getModel("viewWorkflow").getProperty("/selectedChangeRequest");

			(new DMRDataService(
				oController,
				"CREATECRTYPE",
				"/CREATECRSet",
				"CreateCR",
				"/", // Root of the received data
				"Create CR Type"
			)).saveData(
				false,
				oData,
				null, {
					success: {
						fCallback: function (oParams, oResponseData) {
							// Bug 12434 - Errors when copying change request types (T42 and SD2)
							oParams.getView().getModel("viewWorkflow").setProperty("/changeRequestType", "saved");
							oParams.getView().getModel("viewWorkflow").setProperty("/changeRequestTypeChg", undefined);

							Utilities.showPopupAlert(oResponseData.Message, MessageBox.Icon.INFORMATION, "Response");

							let promiseChangeRequestsList = GetListsData.getChangeRequestsList(oController);
							promiseChangeRequestsList.then(function (updatedList) {
								updatedList.results.forEach(oCrType => {
									oCrType.DraftState = oCrType.IsDraft ? "Warning" : "None";
									oCrType.IsDraft = oCrType.IsDraft ? "Draft" : "";
								});
								oChangeReqs.setProperty("/", updatedList);
								
								oController.ChangeRequestSearchList.setListData(updatedList.results);
								oController.ChangeRequestSearchList.setSelectedItemByTitle(oChangeRequestModel.UsmdCreqType);
								
								// 12434 - Errors when copying change request types (T42 and SD2)
								if (!oChangeRequestModel.Sourcecrtype) {
									// Bug 11442 - Workflow graph not getting refreshed when creating new cr type
									oController._notifySelectionChange(undefined, oChangeRequestModel);
								}
							});
						},
						oParam: oController
					},
					error: {
						fCallback: function (oParams, oResponseData) {
							let sMessage = oResponseData.statusCode + ": " + oResponseData.message;
							Utilities.showPopupAlert("Error " + sMessage, MessageBox.Icon.ERROR, "Error");
						}
					}
				}
			);
			oController.getView().getModel("viewWorkflow").setProperty("/customzingTransport", undefined);
			oController.getView().getModel("viewWorkflow").setProperty("/workbenchTransport", undefined);
			oController.getView().getModel("viewWorkflow").setProperty("/package", undefined);
		};

		/*
			Import button is visible when user clicks on an existing CR Type
			On click on Import Workflow button, the user can select the source CR Type from which workflow details are imported
		*/
		WorkflowController.onImportCRType = function (oEvent) {
			let oChangeRequestData = this.getView().getModel("viewWorkflow").getProperty("/selectedChangeRequest");
			let oImportButton = oEvent.getSource();
			if (!this.CopyChangeRequestPopover) {
				this.CopyChangeRequestPopover =
					sap.ui.xmlfragment(
						"CopyChangeRequest",
						"dmr.mdg.supernova.SupernovaFJ.view.Workflow.CopyChangeRequest",
						this);
				this.getView().addDependent(this.CopyChangeRequestPopover);
			}
			this.CopyChangeRequestPopover.openBy(oImportButton);
			this.getView()
				.getModel("viewWorkflow")
				.setProperty("/oCopyChangeRequest", {
					action: "Import",
					selectedDataModel: oChangeRequestData.UsmdModel,
					popoverTitle: "Import Workflow From"	
				});
			let oComboBoxDataModel = sap.ui.getCore().byId("CopyChangeRequest--idCopyCrTypeDataModel");
			let oSelectedDataModel = oComboBoxDataModel.getSelectedItem();
			oComboBoxDataModel.fireSelectionChange(oSelectedDataModel);
		};

		WorkflowController.saveWorkFlowApprovers = function () {
			let oViewWorkFlowModel = this.getView().getModel("viewWorkflow");
			let sCustomizingTransport = oViewWorkFlowModel.getProperty("/customzingTransport");
			let oChangeRequestModel = oViewWorkFlowModel.getProperty("/selectedChangeRequest");

			if (!sCustomizingTransport) {
				return;
			}

			// Retrieve the list of Approvers 
			let oModelApprover = this.getView().getModel("approverModel");
			// Get the model with the data
			let oApproverList = oModelApprover.getProperty("/ApprList").results;

			let oData = {
				CrType: oChangeRequestModel.UsmdCreqType,
				ApprovalSteps: oApproverList.length + "", // Needs to be text
				CustTransport: sCustomizingTransport,
				Message: "",
				APPROVALSet: []
			};

			// Add the approver data to the package
			oApproverList.forEach(function (oApprover, iApproverIndex) {
				oData.APPROVALSet[iApproverIndex] = {
					CrType: "",
					Row: (iApproverIndex + 1).toString(),
					ApproverType: oApprover.ApprType,
					ApproverValue: oApprover.ApprVal,
					ApprovalSteps: ""
				};
			});

			(new DMRDataService(
				this,
				"WORKFLOW",
				"/DECISION_TABLESet",
				"CreateWF",
				"/", // Root of the received data
				"Create WF Decision Table"
			)).saveData(
				false,
				oData,
				null, {
					success: {
						fCallback: function (oParams, oResponseData) {
							Utilities.showPopupAlert(oResponseData.Message, MessageBox.Icon.INFORMATION, "Response");
						},
						oParam: this
					}
				}
			);
		};
		
		WorkflowController._selectClickedIndex = function (oEvent, oController) {
			let oTable = oController.getView().byId("ApprTable");
			oTable.setSelectedItemById(oEvent.getSource().getParent().getId());
		};
		
		WorkflowController._getSelectedIndex = function (oController) {
			let oTable = oController.getView().byId("ApprTable");
			let iIndex = oTable.indexOfItem(oTable.getSelectedItem());
			return iIndex;
		};
		
		WorkflowController._getSelectedRow = function (oController) {
			let oModelApprover = oController.getView().getModel("approverModel");
			let oApproverList = oModelApprover.getProperty("/ApprList");

			let iIndex = oController._getSelectedIndex(oController);
			let oRowData = oApproverList.results[iIndex];

			return oRowData;
		};
		
		WorkflowController.onAddApprover = function () {
			// Create an Empty Approver info and load the dialog 
			let oApproverData = {
				ApprType: "",
				ApprVal: "",
				status: "new"
			};
			this._openWorkApproverDialog(this, oApproverData, -1);
		};
		
		WorkflowController.onDeleteApprover = function (oEvent) {
			this._selectClickedIndex(oEvent, this);

			let oModelApprover = this.getView().getModel("approverModel");
			// Get the model with the data
			let oApproverList = oModelApprover.getProperty("/ApprList");

			// get the table index
			let iSelectedIndex = this._getSelectedIndex(this);

			// Get the selected row 
			let oSelectedApproverData = this._getSelectedRow(this);

			// Check with the user if they really want to delete 
			let promiseShowPopupAlert =
				Utilities.showPopupAlert("Are you sure?" + "Delete " + oSelectedApproverData.ApprType + " - " + oSelectedApproverData.ApprType +
					"?", MessageBox.Icon.WARNING, "Remove the Approver", [MessageBox.Action.YES, MessageBox.Action.NO]);
			promiseShowPopupAlert.then(function () {
				if (oApproverList.results[iSelectedIndex].status === "new") { // need to verify how to convert this into working Promise
					oApproverList.results.splice(iSelectedIndex, 1);
				} else {
					oApproverList.results[iSelectedIndex].status = "delete";
				}
				oModelApprover.refresh();
			}, function() {
            });
		};
		
		WorkflowController.onApproverEditClicked = function (oEvent) {
			// Update the selected row 
			this._selectClickedIndex(oEvent, this);

			// Retrieve the selected row 
			let oSelectedApproverData = this._getSelectedRow(this);

			// make a copy of the data 
			let oDataToEdit = {};
			jQuery.extend(true, oDataToEdit, oSelectedApproverData);

			// Retrieve the selected index 
			let iSelectedIndex = this._getSelectedIndex(this);

			this._openWorkApproverDialog(this, oDataToEdit, iSelectedIndex);
		};
		
		WorkflowController._openWorkApproverDialog = function (oController, oApproverData, iTableIndex) {
			if (!oController.ApproverDataDialog) {
				oController.ApproverDataDialog =
					sap.ui.xmlfragment(
						"ApproverSelectDialog",
						"dmr.mdg.supernova.SupernovaFJ.view.Workflow.ApproverSelect",
						this);
				oController.getView().addDependent(oController.ApproverDataDialog);
			}

			this.getView().getModel("approverModel").setProperty("/ApprDetails", oApproverData);
			this.getView().getModel("approverModel").setProperty("/ApproverTableEditIndex", iTableIndex);

			// Open the dialog
			oController.ApproverDataDialog.open();
		};
		
		// Check if all the approvers edits or adds have been saved 
		WorkflowController._isApproversSaved = function (oController) {
			// Get the apporver model 
			let oModelApprover = oController.getView().getModel("approverModel");

			// Get the approver content
			let oApproverList = oModelApprover.getProperty("/ApprList");

			for (let i = 0; i < oApproverList.results.length; i++) {
				// If the status is not empty
				if (oApproverList.results[i].status) {
					return false;
				}
			}

			return true;
		};
		
		WorkflowController._clearApproverList = function (oController) {
			// Get the apporver model 
			let oModelApprover = oController.getView().getModel("approverModel");

			// Get the approver content
			let oApproverList = oModelApprover.getProperty("/ApprList");

			// Clear the approver data 
			oApproverList.results = [];

			oModelApprover.refresh();
		};
		
		WorkflowController.onApproverSave = function () {
			let oModelApprover = this.getView().getModel("approverModel");
			// Retrieve the edited details
			let oApproverDetails = oModelApprover.getProperty("/ApprDetails");

			// retrieve the edit index 
			let iEditIndex = oModelApprover.getProperty("/ApproverTableEditIndex");

			// If the edit index is valid set the editted flag
			if (!oApproverDetails.status || oApproverDetails.status.length === 0) {
				oApproverDetails.status = "edit";
			}

			// Update the model with the data
			let oApproverList = oModelApprover.getProperty("/ApprList");
			// If editing, replace 
			if (iEditIndex !== -1) {
				oApproverList.results[iEditIndex] = oApproverDetails;
			} else {
				oApproverList.results.push(oApproverDetails);
			}

			oModelApprover.refresh();

			// Close the dialog
			this.onApproverCancelled();
		};
		
		WorkflowController.onApproverCancelled = function () {
			if (this.ApproverDataDialog) {
				this.ApproverDataDialog.close();
			}
		};

		WorkflowController._notifySelectionChange = function (sTransport, oSelectedCR) {
			// Trigger the route change to open the graph view
			sap.ui.core.UIComponent
				.getRouterFor(this)
				.navTo("Workflow", {
						changerequest: oSelectedCR.UsmdCreqType,
						dataModel: oSelectedCR.UsmdModel,
						entity: oSelectedCR.UsmdEntityMain,
						random: Math.floor((Math.random() * 1000) + 1)
					},
					true);
		};

		WorkflowController.onClearEditionType = function () {
			let oViewModel = this.getView().getModel("viewWorkflow");
			let oSelectedChangeRequest = oViewModel.getProperty("/selectedChangeRequest");
			oSelectedChangeRequest.UsmdModel = undefined;
			oSelectedChangeRequest.UsmdEdtnType = "";
			oSelectedChangeRequest.UsmdEntityMain = undefined;
			oSelectedChangeRequest.UsmdProcess = undefined;
			oSelectedChangeRequest.CrequestToEntityNav = [];
			oViewModel.refresh();
		};

		WorkflowController.onClearDataModel = function () {
			let oViewModel = this.getView().getModel("viewWorkflow");
			let oSelectedChangeRequest = oViewModel.getProperty("/selectedChangeRequest");
			oSelectedChangeRequest.UsmdModel = "";
			oSelectedChangeRequest.UsmdEdtnType = undefined;
			oSelectedChangeRequest.UsmdEntityMain = undefined;
			oSelectedChangeRequest.UsmdProcess = undefined;
			oSelectedChangeRequest.UsmdClfLean = undefined;
			oSelectedChangeRequest.CrequestToEntityNav = [];
			oViewModel.refresh();
		};
		
		WorkflowController.onClearEntityType = function () {
			let oViewModel = this.getView().getModel("viewWorkflow");
			oViewModel.setProperty("/selectedChangeRequest/UsmdEntityMain", "");
		};

		WorkflowController.onClearBusinessActivity = function () {
			let oViewModel = this.getView().getModel("viewWorkflow");
			oViewModel.setProperty("/selectedChangeRequest/UsmdProcess", "");
		};

		WorkflowController.onEditCrType = function() {
			let oViewModel = this.getView().getModel("viewWorkflow");

			// Retrieve the selected change request's UsmdProcess using getProperty
			let sSelectedKey = oViewModel.getProperty("/selectedChangeRequest/UsmdProcess");


			let oMatchedBusActivity = oViewModel.getProperty("/arrBusinessActivity").find(oBusActivity => {
				return oBusActivity.Usmdprocess === sSelectedKey;
			});

			if(oMatchedBusActivity.Usmdaction === "CREATE") {
				oViewModel.setProperty("/createBusActivity", true);
			}else{
				oViewModel.setProperty("/createBusActivity", false);
			}
			// Toggle the isSectionEditable property
			oViewModel.setProperty("/isSectionEditable", true);

			if( oViewModel.getProperty("/selectedChangeRequest/UsmdSingleObj") === true  ){
				oViewModel.setProperty("/changeRequestTypeChg", "chg");
			}			
	
		};		
		
		WorkflowController.onDeleteCRType = function () {
			let oChangeReqs = this.getView().getModel("changeReqs");
			let oController = this;
			let oViewModel = this.getView().getModel("viewWorkflow");
			let oChangeRequestModel = oViewModel.getProperty("/selectedChangeRequest");

			let oPromise = new Promise(function (resolve, reject) {
				let promiseShowPopupAlert = Utilities.showPopupAlert("Are you sure you want to delete " + oChangeRequestModel.UsmdCreqType +
					" ?",
					MessageBox.Icon.WARNING, "Change Request Delete?", [MessageBox.Action.YES, MessageBox.Action.NO]);
				promiseShowPopupAlert.then(function () {
					resolve(null);
				},
				function () {
					reject(null);
				});
			});

			oPromise.then(function () {

				//Proceed to next step after user selects the Transport Request
				//Bug 12128 - Asking for workbench request when deleting CR even though there is no BAdI is involved
				oController._selectTransportPackage(true, false, false)
				.then(function(oResponseSelection){
				//end of Bug 12128
					let sSelectedTransport = oResponseSelection.customizingTransport;
					let oUrlParameters = "(UsmdCreqType='" + oChangeRequestModel.UsmdCreqType + "'" +
						",CustTransport='" + sSelectedTransport + "'" +
						",UsmdModel='" + oChangeRequestModel.UsmdModel + "')";

					(new DMRDataService(
						oController,
						"CREATECRTYPE",
						"/CREATECRSet" + oUrlParameters, 
						"CreateCR",
						"/", // Root of the received data
						"Create CR Type"
					)).deleteData(
						false,
						null,
						null, {
							success: {
								fCallback: function (oParams, oResponseData) {
									let promiseShowPopupAlert = Utilities.showPopupAlert(oResponseData.statusText, MessageBox.Icon.INFORMATION, "Response");
									promiseShowPopupAlert.then(function () {
										let promiseChangeRequestsList =
											GetListsData.getChangeRequestsList(oController);
											
										promiseChangeRequestsList.then(function (updatedList) {
											updatedList.results.forEach(oCrType => {
												oCrType.DraftState = oCrType.IsDraft ? "Warning" : "None";
												oCrType.IsDraft = oCrType.IsDraft ? "Draft" : "";
											});
											oChangeReqs.setProperty("/", updatedList);
											oController.ChangeRequestSearchList
												.setListData(updatedList.results);
											oController.ChangeRequestSearchList
												.setSelectedItemByTitle(oChangeRequestModel.UsmdCreqType);

										});
									});
									oViewModel.setProperty("/changeRequestType", "deleted");
								},
								oParam: oController
							},
							error: {
								fCallback: function (oParams, oResponseData) {
									// Bug 11368 - Change request types are not fully deleted in S41
									try {
										let sCode = JSON.parse(oResponseData.responseText).error.innererror.errordetails[0].code;
										let sBusinessMessage = JSON.parse(oResponseData.responseText).error.innererror.errordetails[0].message;
										Utilities.showPopupAlert("Error code:\n" + sCode + "\n\n" + "Error message:\n" + sBusinessMessage, MessageBox.Icon.ERROR, "Error");
									} catch (error) {
										let sGenericMessage = oResponseData.statusCode + ": " + oResponseData.message;
										Utilities.showPopupAlert("Error " + sGenericMessage, MessageBox.Icon.ERROR, "Error");
									}
								}
							}
						}
					);
					oViewModel.setProperty("/customzingTransport", undefined);
				});
			});
		};
		return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.Workflow.Workflow", WorkflowController);
	});