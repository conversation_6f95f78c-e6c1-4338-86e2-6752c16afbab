sap.ui.define([
		"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
		"sap/ui/model/json/JSONModel",
		"sap/m/Label",
		"sap/m/Button",
		"sap/suite/ui/commons/networkgraph/Node",
		"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
		"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
		"sap/suite/ui/commons/networkgraph/ElementAttribute",
		"sap/m/MessageBox",
		"./DMRNode",
		"./DynamicWorkflow",
		"./SelectAttribute",
		"./NodeConnectionsTable",
		"sap/suite/ui/commons/networkgraph/layout/SwimLaneChainLayout",
		//"dmr/mdg/supernova/SupernovaFJ/libs/CustomLayout"
		"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
		"./AdditionalEmailsTable",
		"sap/m/MessageToast",
		"dmr/mdg/supernova/SupernovaFJ/model/Operators"
	],
	function (BaseController, JSONModel, Label, Button, Node, DMRDataService,
		GetListsData, ElementAttribute, MessageBox, DMRNode, DynamicWorkflowDialog, SelectAttributeModel, NodeConnectionsTableModel, GraphLayout,
		Utilities, AdditionalEmailsTable, MessageToast, Operators) {

		return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.Workflow.ApproverAssignment", {

			/**
			 * Checks to see if the layout support hierarchy. Returns true if it does.
			 * Check the layout type and return the appropriate value. 
			 */
			_groupHierarchySupported: function (oController) {
				if (oController.layOutSupportsHierarchy === undefined) {
					oController.layOutSupportsHierarchy = false;
					//  Bug 12756 - Layout name is SwimLaneChainLayout for CF. SwimlaneChainLayout for Neo
                	//  Defaulting layOutSupportsHierarchy to false in all cases
					// let oLayout = new GraphLayout();
					// let sLayout = oLayout.getMetadata().getName().split(".").pop();

					// switch (sLayout) {
					// case "SwimLaneChainLayout":
					// case "LayeredLayout":
					// 	oController.layOutSupportsHierarchy = false;
					// 	break;

					// default:
					// 	oController.layOutSupportsHierarchy = true;
					// 	break;
					// }
				}

				return oController.layOutSupportsHierarchy; 
			},

			// Change the graph to the full screen mode
			// Before changing the graph to full screen, check whether customizing TR is availablle. If not open the TR dialog fragment
			_setGraphToFullScreen: function (oController) {

				let oCustomizingTransportPromise = oController._selectTransportPackage(true, false, false, true)
				.then(function (/* oResponseSelected */) {

					let oGraph = oController.getView().byId("graph");
					// If the graph is not in the full screen mode, set it to full screen
					if (!oGraph.isFullScreen()) {
						oGraph.toggleFullScreen();
						sap.ui.getCore().byId("resetButton").setIcon("sap-icon://sys-cancel");
						sap.ui.getCore().byId("resetButton").setTooltip("Cancel Changes");
					}
					// disable the exit full screen mode button
					//oGraph.getToolbar().setVisible(false);
					let oToolbar = oGraph.getToolbar();
					oGraph.getToolbar().setVisible(true);
					let arrContent = oToolbar.getContent();
					arrContent.forEach(function (control) {
						//Task 12336 - WF Graph Layout Changes - Add Full screen button to toolbar and make it visible
						if (control.getId() !== "saveButton" && control.getId() !== "resetButton" && control.getId() !== "activateButton" && control.getId() !== "fullScreenButton" &&
							control.getId() !== "title" && control.getId() !== "graphCheckButton" && control.getId() !== "draftButton" && control.getId() !== "draftLabel" && 
							control.getId() !== "btnShowLegend") {
							control.setVisible(false);
						}
					});

					return;
				});
				return oCustomizingTransportPromise;
			},

			// Change the graph from the full screen mode to the normal mode
			_setGraphNonFullScreen: function (oController) {

				let oGraph = oController.getView().byId("graph");
				// If the graph is in the full screen mode, move it to the normal mode. 
				if (oGraph.isFullScreen()) {
					oGraph.toggleFullScreen();
					sap.ui.getCore().byId("resetButton").setIcon("sap-icon://reset");
					sap.ui.getCore().byId("resetButton").setTooltip("Reset Workflow");
				}

				// enable the exit full screen mode button
				let oToolbar = oGraph.getToolbar();
				oToolbar.setVisible(false);
			},
			// Intialize the graph with the content for the new CR selected
			_onRouteMatched: function (oEvent, oController) {
				// Get the parameter and store to the model
				let oRouteParams = oEvent.getParameter("arguments");

				// Retrieve the view model and isSectionEditable property
				let oViewModelc = oController.getView().getModel("viewWorkflow");

							// Toggle the isSectionEditable property
				oViewModelc.setProperty("/isSectionEditable", false);		
							

				let oPromise = new Promise(function (resolve) {
					if (oController.GraphContentsUpdated && oController.GraphContentsUpdated === true) {
						let promiseShowPopupAlert = Utilities.showPopupAlert(
							"Save the changes? Select YES to save. Selecting NO will delete the changes.",
							MessageBox.Icon.WARNING, "Unsaved changes in the workflow", [MessageBox.Action.YES, MessageBox.Action.NO]);
						promiseShowPopupAlert.then(function () {
							
						}, function () {
							resolve(true);
						});
					} else {
						resolve(true);
					}
				});

				oPromise.then(
					// Go ahead and update the graph
					function () {

						let oViewModel = oController.getApproverModel();
						let oViewWorkflow = oController.getView().getModel("viewWorkflow").getData();
						if (oViewModel) {

							oViewModel.setProperty("/CRTypeDetails", {
								IsDraft: oViewWorkflow.selectedChangeRequest.IsDraft,
								CrTypeName: oRouteParams.changerequest,
								Transport: undefined, //User to choose TR once before making changes in the graph
								DataModel: oRouteParams.dataModel,
								Entity: oRouteParams.entity
							});

						}

						if (oRouteParams.changerequest) {
							if (oController.getView().getProperty("visible")) {
								oController._loadGraphData(oController, oRouteParams.changerequest);
							}
						}
					},
					// Save the changes to the graph and then update
					function () {}
				);
			},
			/**
			 * Retrieve the details of the CR Type selected
			 * Returna promise to the caller function.
			 */
			getCRDetails: function () {
				let oCrTypeDetails = this.getApproverModel().getProperty("/CRTypeDetails");
				return oCrTypeDetails;
			},
			/**
			 * Handler for the approverModel property changed handler.
			 */
			_modelPropertyChangeHandler: function (oPropChange, oController) {
				oController.GraphContentsUpdated = true;
			},
			_loadGraphData: function (oController) {
				let oModel = oController.getApproverModel();
				oModel.setProperty("/badiimpl", []);
				// Get the CR Type Name... 
				let sCRTypeName = oModel.getProperty("/CRTypeDetails").CrTypeName;

				// If no CR was selected .. return. Nothing to do or show
				if (!sCRTypeName) {
					return;
				}

				// get the graph object 
				let oGraph = oController.getView().byId("graph");
				oController.GraphContentsUpdated = false;

				// Remove all content from the graph and restart placing content
				let oRemLines = oGraph.getLines();
				if (oRemLines) {
					oRemLines.forEach(function (o) {
						oGraph.removeLine(o);
						o.destroy();
					});
				}

				let oRemNodes = oGraph.getNodes();
				if (oRemNodes) {
					oRemNodes.forEach(function (o) {
						oGraph.removeNode(o);
						o.destroy();
					});
				}

				let oRemAlg = oGraph.getLayoutAlgorithm();
				if (oRemAlg) {
					oRemAlg.destroy();
				}
				let oRemLegend = oGraph.getLegend();
				if (oRemLegend) {
					oRemLegend.destroy();
				}
				let oRemStatuses = oGraph.getStatuses();
				if (oRemStatuses) {
					oRemStatuses.forEach(function (o) {
						oGraph.removeStatus(o);
						o.destroy();
					});
				}

				let oGroups = oGraph.getGroups();
				if (oGroups) {
					oGroups.forEach(function (o) {
						oGraph.removeGroup(o);
						o.destroy();
					});
				}

				// Clear the graph model
				let graphModel = oGraph.getModel();
				if (!graphModel) {
					graphModel = new JSONModel();
					oGraph.setModel(graphModel);
				}
				graphModel.setProperty("/", null);
				oGraph.invalidate();
				oController._createGroupsInGraph(oGraph, oController);

				// get the Approval Details for the CRType
				let promiseGetCrType = oController._getApproverDetails(oController, sCRTypeName);
				let oVBox = new sap.m.VBox({
					height: "100%",
					justifyContent: "Start"
				});
				oVBox.bindAggregation("items", {
					path: "approverModel>/badiimpl",
					template: new sap.m.ObjectStatus({
						title: "{approverModel>badiKey}",
						text: "{approverModel>value}",
						state: "Information"
					})
					.addStyleClass("sapUiSmallMarginTop")
					.addStyleClass("sapMObjectStatusLongText")
				});

				let oMainVBox = new sap.m.VBox({
					height: "15vw",
					items: [
						new sap.m.ObjectIdentifier({
							title: "Selected BAdIs"
						}),
						oVBox
					]
				});

				oGraph.setLegend(oMainVBox);
				promiseGetCrType.then(
					// saved data from server
					async function () { 
						// Task 12336 - WF Graph Layout Changes - Hide all duplicate lines from FROM node to TO Node once the graph is fully rendered
						oGraph.attachGraphReady(oController, () => oController.hideDuplicateLines(oController));
						let aNodes = oGraph.getNodes();
						for(let i = 0; i < aNodes.length; i++){
							if (aNodes[i].getKey() !== DMRNode.IDs.INIT && aNodes[i].getKey() !== DMRNode.IDs.ACTIVATE && aNodes[i].getKey() !== DMRNode.IDs.DISCARD && aNodes[i].getKey() !== DMRNode.IDs.COMPLETE) {
								// Bug 12573 - Function updateConnectionsData is called to update all the connections' information which are already saved
								await oController.updateConnectionsData(oController, aNodes[i]);
							}
						}
					},
					// Get default data
					function () {
						let promiseInitData = oController._getGraphInitStatus();

						promiseInitData.then(function (graphData) {
							graphModel.setProperty("/", graphData);
							oGraph.setLayoutAlgorithm(new GraphLayout());
							oGraph.setOrientation(
								oController._groupHierarchySupported(oController) ?
								sap.suite.ui.commons.networkgraph.Orientation.LeftRight :
								sap.suite.ui.commons.networkgraph.Orientation.TopBottom);
							oGraph.getToolbar().setVisible(false);
							try {
								oGraph
									.detachAfterLayouting(oController.afterGraphReady);
							} finally {
								oGraph
									.attachAfterLayouting(oController, oController.afterGraphReady);
							}

						});
					}
				);

			},
			_createGroupsInGraph: function (oGraph, oController) {
				let bHierarchySupported = oController._groupHierarchySupported(oController);

				let arrGroupData = [{
					"key": DMRNode.Groups.Init,
					"title": "Initiation",
					"create": true
				}, {
					"key": DMRNode.Groups.Discard,
					"title": "Activate",
					"create": true
				}, {
					"key": DMRNode.Groups.WorkFlow,
					"title": "Workflow",
					"create": true
				}, {
					"key": DMRNode.Groups.Complete,
					"title": "Complete",
					"parentGroupKey": "03actv",
					"create": bHierarchySupported ? true : false
				}, {
					"key": DMRNode.Groups.Activate,
					"title": "Activate",
					"parentGroupKey": "03actv",
					"create": bHierarchySupported ? true : false
				}];

				arrGroupData.forEach(function (group) {
					oGraph.addGroup(new sap.suite.ui.commons.networkgraph.Group({
						key: group.key,
						title: group.title,
						parentGroupKey: (bHierarchySupported === true ? group.parentGroupKey : undefined)
					}));
				});

			},

			// Load the graph on after rendering
			onAfterRendering: function () {

			},

			/**
			 * Add the handlers for the primary nodes of the graph. 
			 * 
			 * Update the handlers for the Init and Activation Nodes as required 
			 */
			afterGraphReady: function (oGraph, oController) {
				let nodes = this.getNodes();

				// Detach the graph read handler 
				oGraph.getSource().detachAfterLayouting(oController.afterGraphReady);

				// Go through each node and attach handlers for each of the action buttons 
				for (let iNode = 0; iNode < nodes.length; iNode++) {
					nodes[iNode].setShowDetailButton(false);
					let arrActionButtons = nodes[iNode].getActionButtons();

					// For each action button, check the type and attach the correct handler 
					arrActionButtons.forEach(function (actionButton) {
						let sIcon = actionButton.getIcon();
						if (sIcon === "sap-icon://add") {
							try {
								actionButton.detachPress(oController.nodeAddButtonHandler, nodes[iNode]);
							} finally {
								actionButton.attachPress(oController, oController.nodeAddButtonHandler, nodes[iNode]);
							}
							actionButton.setTitle("Add");
						} else if (sIcon === "sap-icon://action-settings") {
							try {
								actionButton.detachPress(oController.nodeSettingButtonHandler, nodes[iNode]);
							} finally {
								actionButton.attachPress(oController, oController.nodeSettingButtonHandler, nodes[iNode]);
							}
							actionButton.setTitle("Edit or Delete Node");
						} else if (sIcon === "sap-icon://add-process") {
							try {
								actionButton.detachPress(oController.nodeEditConnectionHandler, nodes[iNode]);
							} finally {
								actionButton.attachPress(oController, oController.nodeEditConnectionHandler, nodes[iNode]);
							}
							actionButton.setTitle("Edit Connection");
						}
					});
				}

				let lines = this.getLines();
				for (let i in lines) {
					this.removeLine(lines[i]);

					oController.createNewLineForGraphNode(
						oGraph.getSource(), oController, undefined, undefined, lines[i].getFrom(), lines[i].getTo(), lines[i].getTitle(),
						1, lines[i].getStatus(), "", true, "", "", "", "", undefined, "", undefined);

					lines[i].destroy();
				}
				// Task 12336 - WF Graph Layout Changes - Hide all duplicate lines from FROM node to TO Node once the graph is fully rendered
				oGraph.getSource().attachGraphReady(oController, () => oController.hideDuplicateLines(oController));

			},
			/**
			 * Traverse the actions list and return the matching action details 
			 */
			searchActions: function (oController, sActionCode, sApproverType) {
				let stepsData = oController.getApproverModel().getProperty("/actionType/results");

				let matchedStepData = {
					UsmdActDesc: "Start",
					UsmdBtnTooltip: "Init",
					UsmdBtnTxt: "Init",
					UsmdCrAction: "Init",
					UsmdCrActionCheck: "X",
					UsmdCrActionNote: "",
					UsmdCrActionReasonRej: ""
				};
				if (sApproverType === DMRNode.Types.SystemMethod) { //TASK 9605
					let defaultStepAction = stepsData.find(function (stepData) {
						if (stepData.UsmdCrAction === "**") {
							return true;
						}
						return false;
					});
					matchedStepData = {
						UsmdActDesc: "For all step actions",
						UsmdBtnTooltip: "For all step actions",
						UsmdBtnTxt: "For all step actions",
						UsmdCrAction: "**",
						UsmdCrActionCheck: "X",
						UsmdCrActionNote: "",
						UsmdCrActionReasonRej: ""
					};
					if (!defaultStepAction) {
						stepsData.push(matchedStepData);
					} 
				} else if( sActionCode !== "**" ){ 
					stepsData = stepsData.filter(function (stepData){
						return 	stepData.UsmdCrAction !== "**";
					});
					 oController.getApproverModel().setProperty("/actionType/results", stepsData);
				}
				if (sActionCode !== "Init" && sActionCode !== "**") {
					matchedStepData = stepsData.find(function (stepData) {
						if (stepData.UsmdCrAction === sActionCode) {
							return true;
						}
						return false;
					});
				}

				return matchedStepData;
			},
			/**
			 * Search for the node with the specified key and return the same 
			 */
			getNodeWithKey: function (oController, sKey) {
				let oGraph = oController.getView().byId("graph");

				let arrNodes = oGraph.getNodes();
				let oNode = arrNodes.find(function (node) {
					return node.getKey() === sKey;
				});
				return oNode;
			},
			/**
			 * Get the list of nodes in the graphs, filtered by 
			 *	> Group Name (if provided)
			 *  > Node Type (if provided)
			 *  > ExcludeNodeKey (if provided)
			 */
			getFilteredNodeKeyList: function (oController, sExcludeNodeKey, sGroupName, iNodeType) {
				let arrNodeKeyList = oController.getNodeKeyList(oController, sExcludeNodeKey);

				// If the group name is provided, filter by the group name
				if (sGroupName) {
					arrNodeKeyList = arrNodeKeyList.filter(function (oNodeInfo) {
						if (oNodeInfo.group === sGroupName) {
							return true;
						}
						return false;
					});
				}

				/**
				 * If the Node Type is provided, filter by node type
				 *   > Get the node with the key
				 *   > Get type and compare 
				 */
				if (iNodeType) {
					arrNodeKeyList = arrNodeKeyList.filter(function (oNodeInfo) {
						// get node with specified key 
						let oNode = oController.getNodeWithKey(oController, oNodeInfo.key);
						let iCurrentNodeType = oController._getNodeType(oController, oNode);

						if (iCurrentNodeType === iNodeType) {
							return true;
						}

						return false;
					});
				}
				return arrNodeKeyList;
			},
			
			
			getFilteredNodeConnectionKeyList: function (oController, sExcludeNodeKey, sGroupName, iNodeType) {
				let arrNodeKeyList = oController.getNodeKeyList(oController, sExcludeNodeKey);
				let iIndex = sGroupName.lastIndexOf("_");
				let sGroupFilter = sGroupName.substr(0, iIndex);
				if(iNodeType === DMRNode.Types.Parallel){
					arrNodeKeyList = arrNodeKeyList.filter(function(oNodeInfo){
						return oNodeInfo.group.includes(sGroupFilter);
					});
				}
				
				if(iNodeType === DMRNode.Types.ParallelChild){
					arrNodeKeyList = arrNodeKeyList.filter(function(oNodeInfo){
						return (oNodeInfo.group.includes(sGroupFilter) && (oNodeInfo.nodeType === DMRNode.Types.Parallel || oNodeInfo.nodeType === DMRNode.Types.Merge)) || oNodeInfo.group === sGroupName ;
					});
				}
				
				if(iNodeType === DMRNode.Types.Merge){
					arrNodeKeyList = arrNodeKeyList.filter(function(oNodeInfo){
						return oNodeInfo.group === sGroupName || oNodeInfo.nodeType === DMRNode.Types.Parallel || !oNodeInfo.group.includes("parallel");
					});
				}
				
				if(iNodeType === DMRNode.Types.Normal){
					arrNodeKeyList = arrNodeKeyList.filter(function(oNodeInfo){
						return oNodeInfo.group === sGroupName || oNodeInfo.nodeType === DMRNode.Types.Parallel || oNodeInfo.nodeType === DMRNode.Types.Dynamic || !oNodeInfo.group.includes("parallel");
					});
				}
				if(iNodeType === DMRNode.Types.Init){
					arrNodeKeyList = arrNodeKeyList.filter(function(oNodeInfo){
						return oNodeInfo.group === sGroupName || oNodeInfo.nodeType === DMRNode.Types.Parallel || oNodeInfo.nodeType === DMRNode.Types.Dynamic || !oNodeInfo.group.includes("parallel");
					});
				}
				if(iNodeType === DMRNode.Types.Activate){
					arrNodeKeyList = arrNodeKeyList.filter(function(oNodeInfo){
						return oNodeInfo.group === sGroupName || oNodeInfo.nodeType === DMRNode.Types.Parallel || oNodeInfo.nodeType === DMRNode.Types.Dynamic || !oNodeInfo.group.includes("parallel");
					});
				}
				if(iNodeType === DMRNode.Types.Dynamic){
					arrNodeKeyList = arrNodeKeyList.filter(function(oNodeInfo){
						return oNodeInfo.group === sGroupName || oNodeInfo.nodeType === DMRNode.Types.Parallel || oNodeInfo.nodeType === DMRNode.Types.Dynamic || !oNodeInfo.group.includes("parallel");
					});
				}

				// // If the group name is provided, filter by the group name
				// if (sGroupName) {
				// 	arrNodeKeyList = arrNodeKeyList.filter(function (oNodeInfo) {
				// 		if (oNodeInfo.group === sGroupName) {
				// 			return true;
				// 		}
				// 		return false;
				// 	});
				// }

				// /**
				//  * If the Node Type is provided, filter by node type
				//  *   > Get the node with the key
				//  *   > Get type and compare 
				//  */
				// if (iNodeType) {
				// 	arrNodeKeyList = arrNodeKeyList.filter(function (oNodeInfo) {
				// 		// get node with specified key 
				// 		let oNode = oController.getNodeWithKey(oController, oNodeInfo.key);
				// 		let iCurrentNodeType = oController._getNodeType(oController, oNode);

				// 		if (iCurrentNodeType === iNodeType) {
				// 			return true;
				// 		}

				// 		return false;
				// 	});
				// }
				return arrNodeKeyList;
			},


			/**
			 * get the list of nodes in the graph. The npde specified in the sExCludeNodeKey is removed from the returned list
			 */
			getNodeKeyList: function (oController, sExcludeNodeKey) {
				let oGraph = oController.getView().byId("graph");

				let arrNodes = oGraph.getNodes();
				let arrNodeKeys = [];
				arrNodes.forEach(function (node) {
					if (node.getKey() !== sExcludeNodeKey) {
						let oNode = oController.getNodeWithKey(oController, node.getKey());
						let iNodeType = oController._getNodeType(oController, oNode);
						arrNodeKeys.push({
							key: node.getKey(),
							title: node.getTitle().includes(": ") ? node.getTitle().split(": ")[1] : node.getKey(),
							group: node.getGroup(),
							nodeType: iNodeType
						});
					}
				});
				return arrNodeKeys;
			},

			// Bug 12573 - Function was added to update the connections' information
			updateConnectionsData: async function (oController, nodeInfo) {
				await oController.nodeEditConnectionHandler(null, oController, nodeInfo);
				NodeConnectionsTableModel.editConnectionsSaveClicked(null, oController);
			},

			/**
			 * Handler to edit the node connections. Opens a popup with the actions and the selected target if any. 
			 * The user can assign the targets to the actions and save
			 */
			nodeEditConnectionHandler: async function (oEditConnectionEvent, oController, oSelectedNode) {
				let thisNode = oSelectedNode ? oSelectedNode : this;
				if (oEditConnectionEvent) {
					await oController._setGraphToFullScreen(oController);
				}
				// Get the node type 
				let iNodeType = oController._getNodeType(oController, thisNode);

				// get the Graph object 
				let oGraph = oController.getView().byId("graph");

				// get view model 
				let oApproverModel = oController.getApproverModel();

				// reset the connection editor model 
				oApproverModel.setProperty("/ConnectionEditor", {});

				// Store the Node being edited for future actions. 
				oController.nodeBeingEdited = thisNode;

				// Get the list of target Node 
				let arrTargetNodes = oController.getFilteredNodeConnectionKeyList(oController, thisNode.getKey(), thisNode.getGroup(), iNodeType);


				// Save the node list to the model 
				oApproverModel.setProperty("/ConnectionEditor/targetNodeList", arrTargetNodes);
				// Get the node details 
				let oNodeDetails = oController._getNodeApproverDetails(thisNode);
				/**
				 * For Parallel and Parallel Children, if target node is merge node, restrict the user to enter Email template and Additional Emails and clear the values
				 */
				let sMergeNodeKey;
				if(iNodeType === DMRNode.Types.Parallel || iNodeType === DMRNode.Types.ParallelChild) {
					// Get the graph object 
					oGraph = oController.getView().byId("graph");
					//Get the Nodes in the graph
					let oGraphNodes = oGraph.getNodes();
					let oMergeNode = oGraphNodes.find(oNode => {
						let sSwimLaneGroup = thisNode.getGroup();
						let iIndex = sSwimLaneGroup.lastIndexOf("_");
						let sGroupFilter = sSwimLaneGroup.substr(0, iIndex);
						return oNode.getGroup().includes(sGroupFilter) &&
								oNode.getIcon() === DMRNode.Icons.Merge;         
					});
					if(oMergeNode) {
						sMergeNodeKey = oMergeNode.getKey();
					}
				}
				/**
				 * Initialize the array to hold the Connection Edit data. 
				 * Depending on the node type start filling the node data 
				 * [
				 *		{
				 *			RowNum
					*			UsmdCrAction,
					*			UsmdActDesc,
					*			TargetNode, 
					*			CRStatus,
					*			CR_OBMAIN,
					*			ApproverValue,
					*			ApproverType,
					*			HeaderTextForList (Combination of Row Number and ApproverValue) e.g. 1 - ApproverName
					*      }
					* ]
					*/
				let arrActionList = [];
				let arrNodeConnectionListDetails = [];

				if (iNodeType === DMRNode.Types.Parallel) {
					oApproverModel.setProperty("/ConnectionEditor/nodeType", "Parallel");
				}

				/**
				 * When the node is not Dynamic, the actions is a comma separated list of actions 
				 * In case of the Dynamic Node, the actions is an array of comma separated list of actions
				 * 
				 * Create an initial array of data that will be iterated upon once more to create the target 
				 * data for the dialog.
				 */
				if (iNodeType !== DMRNode.Types.Dynamic && iNodeType !== DMRNode.Types.Parallel) {
					arrActionList = [{
						action: Array.isArray(oNodeDetails.StepActions) ? oNodeDetails.StepActions.toString() : oNodeDetails.StepActions,
						RowNum: "1",
						ApproverValue: oNodeDetails.ApprVal ? oNodeDetails.ApprVal : "",
						ApproverType: oNodeDetails.ApprType ? oNodeDetails.ApprType : ""
					}];
				} else {
					arrActionList = oNodeDetails.StepActions;
					if (oNodeDetails.DynamicRuleList && oNodeDetails.DynamicRuleList.DTVALUESSet) {
						// Iterate the DynamicRuleList to retrieve the approver for each row and populate.
						let arrValueList = oNodeDetails.DynamicRuleList.DTVALUESSet;
						// Filter the list by ApprValue or Approver Type
						arrValueList = arrValueList.filter(function (arrElement) {
							return ((arrElement.ColName === "USER_VALUE") ||
								(arrElement.ColName === "USER_TYPE") ||
								(arrElement.ColName === "APPSTEP"));
						});

						// Loop through the list and update the arrValueList with the ApproverValue and ApproverType
						arrValueList.forEach(function (arrElement) {
							let iIndex = parseInt(arrElement.RowNum, 10) - 1;
							if (arrElement.ColName === "USER_VALUE") {
								arrActionList[iIndex].ApproverValue = arrElement.ColValue;
							} else if (arrElement.ColName === "USER_TYPE") {
								arrActionList[iIndex].ApproverType = arrElement.ColValue;
							} else {
								arrActionList[iIndex].AppStep = arrElement.ColValue;
							}
						});
						
						// if (iNodeType === DMRNode.Types.Dynamic){
							let arrDrivingAttributeList = oNodeDetails.DynamicRuleList.DRIVINGENTITYSet;
							
							let arrAttributeValuesList = oNodeDetails.DynamicRuleList.DTVALUESSet.filter((arrElement)=>{
								return arrDrivingAttributeList.find(attribute => {
								return attribute.UsmdEntity+"__"+attribute.UsmdAttribute === arrElement.ColName;
								});
							});
							
							arrActionList.forEach(function (arrAction) {
								arrAction.AttributeValueList = "";
							});
							
							arrAttributeValuesList.forEach(function (arrElement) {
								let iIndex = parseInt(arrElement.RowNum, 10) - 1;
								let tmpOperator = Operators.find(operator => operator.key === arrElement.Operator);
								let tmpOperatorSymbol = "";
								if(arrElement.ColValue.split(" - ")[0]){
									tmpOperatorSymbol = tmpOperator? tmpOperator.text + " ": "= ";
								}
								let tmpValue = arrElement.ColValue.split(" - ")[0] ? arrElement.ColValue.split(" - ")[0] : arrElement.ColValue.split(" - ")[0];
								//Check if value is there if not take it to evit issues on text
								    // Check for empty operator symbol and empty value
								tmpOperatorSymbol = tmpOperatorSymbol || (tmpOperator ? tmpOperator.text + " " : "");
								if(arrElement.Defaultstep === "X"){
								tmpValue = tmpValue || " ";
								}else{
								tmpValue = tmpValue || "INIT";
								}
								// Bug 12573 - Only the key is taken to show it in the connections description
								arrActionList[iIndex].AttributeValueList = arrActionList[iIndex].AttributeValueList && arrActionList[iIndex].AttributeValueList.trim() 
								? arrActionList[iIndex].AttributeValueList + (tmpValue ? ", " + tmpOperatorSymbol + tmpValue : "") 
								: tmpOperatorSymbol + tmpValue;
							});
						// }
					}
				}

				/**
				 * Expand the action list to separate out each action into a separate array index, duplicate the 
				 * other data.
				 * Copy the expanded data into the target array 
				 */
				arrActionList.forEach(function (actionElement) {
					if (!actionElement) {
						return;
					}

					let arrActions = actionElement.action.split(",");
					// Remove INIT and empty from the array 
					arrActions = arrActions.filter(function (action) {
						return !((action === "INIT") || !(action));
					});
					arrActions.forEach(function (action) {
						let oNodeActionDetails = {};
						oNodeActionDetails.RowNum = actionElement.RowNum;
						oNodeActionDetails.ApproverValue = actionElement.ApproverValue;
						oNodeActionDetails.ApproverType = actionElement.ApproverType;
						oNodeActionDetails.AppStep = actionElement.AppStep;
						oNodeActionDetails.HeaderTextForList = actionElement.RowNum + "";
						oNodeActionDetails.AttributeValueList = actionElement.AttributeValueList;
						if (actionElement.ApproverValue) {
							oNodeActionDetails.HeaderTextForList += " - " + actionElement.ApproverValue;
						}

						// Get the action details for the action
						let stepData = oController.searchActions(oController, action, iNodeType );
						oNodeActionDetails.UsmdCrAction = stepData.UsmdCrAction;
						oNodeActionDetails.UsmdActDesc = stepData.UsmdActDesc;

						// Get the list of lines that orginate from this node and RowNum
						let oLinesFromNode = oGraph.getLines().filter(function (line) {
							if (line.getFrom() === thisNode.getKey() &&
								line.getCustomData()[0].getValue().RowNum.toString() === actionElement.RowNum) {
								return true;
							}
							return false;
						});

						// Get the line with action and default it to selection
						oLinesFromNode.find(function (line) {
							if (line.getTitle() === stepData.UsmdCrAction) {
								oNodeActionDetails.TargetNode = line.getTo();
							} else if (line.getTitle() === "") {
								line.setTitle("Init");
								oNodeActionDetails.TargetNode = line.getTo();
							}
						});

						// Get the Priority, Reason and Reason for Rejection if provided
						let sLineExists = ""; //Change the value if Cr Action Line is found. Else add a new item to the list 
						oLinesFromNode.find(function (line) {
							if (line.getTitle() === stepData.UsmdCrAction) {
								sLineExists = "X";
								oNodeActionDetails.TargetNode = line.getTo();

								//Add the Priority, CR Reason, CR Reason for Rejection if available in the line custom data
								if (line.getCustomData()[0].getValue().UsmdPriority) {
									oNodeActionDetails.CRPriority = line.getCustomData()[0].getValue().UsmdPriority;
								}

								if (line.getCustomData()[0].getValue().UsmdReason) {
									oNodeActionDetails.CRReason = line.getCustomData()[0].getValue().UsmdReason;
								}

								if (line.getCustomData()[0].getValue().UsmdReasonRej) {
									oNodeActionDetails.CRRejectionReason = line.getCustomData()[0].getValue().UsmdReasonRej;
								}

								if (line.getCustomData()[0].getValue().Sign) {
									oNodeActionDetails.Sign = line.getCustomData()[0].getValue().Sign;
								}

								//Do not show the email template value if node is parallel or parallel child and target node is merge node
								//Email Template field to be disabled on component level
								if (line.getCustomData()[0].getValue().Emailtemp) {
									if ( oNodeActionDetails.TargetNode === sMergeNodeKey && ( iNodeType === DMRNode.Types.Parallel || iNodeType === DMRNode.Types.ParallelChild ) ) {
										oNodeActionDetails.Emailtemp = undefined;
									} else {
										oNodeActionDetails.Emailtemp = line.getCustomData()[0].getValue().Emailtemp;
									}
									
								}

								//Do not show the additional emails value if node is parallel or parallel child and target node is merge node
								//Additional Emails field is disabled as the Email template value is cleared
								if (line.getCustomData()[0].getValue().AddlEmails) {
									if ( oNodeActionDetails.TargetNode === sMergeNodeKey && ( iNodeType === DMRNode.Types.Parallel || iNodeType === DMRNode.Types.ParallelChild ) ) {
										oNodeActionDetails.AddlEmails = undefined;
									} else {
										oNodeActionDetails.AddlEmails = line.getCustomData()[0].getValue().AddlEmails;
									}
								}
								
								oNodeActionDetails.CRStatus = line.getCustomData()[0].getValue().Status;
								oApproverModel.getProperty("/crStatus").results.find(function (el) {
									if (el.UsmdCreqStatus === line.getCustomData()[0].getValue().Status) {
										oNodeActionDetails.CR_OBMAIN = el.UsmdCreqObmain;
									}
								});
								if (iNodeType === DMRNode.Types.Dynamic || iNodeType === DMRNode.Types.Parallel){

									oNodeActionDetails.ListHeader =
									(oNodeActionDetails.AttributeValueList && oNodeActionDetails.AttributeValueList.trim() !== "" ? oNodeActionDetails.AttributeValueList + " - " : "") +
									(oNodeActionDetails.ApproverValue && oNodeActionDetails.ApproverValue.trim() !== "" ? oNodeActionDetails.ApproverValue + " - " : "") +
									oNodeActionDetails.UsmdActDesc + " ( " + oNodeActionDetails.UsmdCrAction + " )";									
								}
								else
								{

								oNodeActionDetails.ListHeader =
								(oNodeActionDetails.ApproverValue && oNodeActionDetails.ApproverValue.trim() !== "" ? oNodeActionDetails.ApproverValue + " - " : "") +
								oNodeActionDetails.UsmdActDesc + " ( " + oNodeActionDetails.UsmdCrAction + " )";
							
								}
								
								let oNodeActionDetailsC = {};
								jQuery.extend(true, oNodeActionDetailsC, oNodeActionDetails);
								arrNodeConnectionListDetails.push(oNodeActionDetailsC);

								//Clear Priority, Reason, RejectionReason for the next iteration
								oNodeActionDetails.CRPriority = undefined;
								oNodeActionDetails.CRReason = undefined;
								oNodeActionDetails.CRRejectionReason = undefined;
								oNodeActionDetails.CRStatus = undefined;
								oNodeActionDetails.CR_OBMAIN = undefined;
								oNodeActionDetails.TargetNode = undefined;
								oNodeActionDetails.Emailtemp = undefined;
								oNodeActionDetails.AddlEmails = undefined;
								
							} else if (line.getTitle() === "") {
								line.setTitle("Init");
								oNodeActionDetails.TargetNode = line.getTo();
							}
						});

						//Default connections need to added for a new node. Add a new item to the list based on ListHeader
						if (sLineExists === "") {
							if (iNodeType === DMRNode.Types.Dynamic || iNodeType === DMRNode.Types.Parallel){
								oNodeActionDetails.ListHeader =
									(oNodeActionDetails.AttributeValueList ? oNodeActionDetails.AttributeValueList + " - " : "") + oNodeActionDetails.ApproverValue + " - " + oNodeActionDetails.UsmdActDesc + " ( " + oNodeActionDetails.UsmdCrAction +
									" ) ";
							}
							else{
								oNodeActionDetails.ListHeader =
									oNodeActionDetails.ApproverValue + " - " + oNodeActionDetails.UsmdActDesc + " ( " + oNodeActionDetails.UsmdCrAction +
									" ) ";
							}
							arrNodeConnectionListDetails.push(oNodeActionDetails);
						}
					});
				});
				if(iNodeType === DMRNode.Types.Parallel){
					arrNodeConnectionListDetails.forEach(function(element){
						if(element.TargetNode === undefined){
							let oMatchingElement = arrNodeConnectionListDetails.find(function(matchElement){
								return element.UsmdCrAction === matchElement.UsmdCrAction && matchElement.TargetNode;
							});
							if(oMatchingElement){
								element.TargetNode = oMatchingElement.TargetNode;
							}
						}
					});
				}
				await oApproverModel.setProperty("/ConnectionEditor/connectionsList", arrNodeConnectionListDetails);
				//Bug 12573 - Condition added to know if the method is being called from a button or from other method
				if (oEditConnectionEvent) {
					oController.NodeConnectionsTableDialog.openDialog(oController, arrNodeConnectionListDetails, thisNode.getTitle(), iNodeType, sMergeNodeKey);
				}
			},

			/**
			 * 1. Create a line connecting the From and To Nodes. 
			 * 2. Assign the specified action as Description 
			 * 3. Add the row number to the custom
			 * 4. Assign the From Node Title to the description
			 * 
			 * When creating a new line ensure that there is no other line matching the criteria 
			 *   Same From Node, Same Action and Row Number
			 * If such a node exists ... repurpose the line else create a new one
			 * 
			 * Return the line to the caller. 
			 * 
			 * oController : The controller handler 
			 * oGraph : the handle to the graph
			 * sFromNode : The node name from which the line should originate 
			 * sToNode : The node name at which the line should terminate
			 * sAction : The action that links the nodes 
			 * iRowNumber : The row number in the list from which this originates (Is 1 for all nodes except Dyanmic)
			 * bAddToGraph : Defaults to True. If set to false, the line is created and returned to the caller, but not added to the graph
			 * 
			 * if the source or target node do not exist, do not create a line and return null to the caller. 
			 */
			createNewLineForGraphNode: function (
				oGraph, oController, sApproverType, sApproverValue, sFromNode, sToNode, sAction,
				iRowNumber, sStatus, sCreqObmain, bAddToGraph, sUsmdPriority, sUsmdReason,
				sUsmdReasonRej, sSign, sAppStep, sEmailTemp, AddlEmails, sApprdesc
			) {
				let oLine = undefined;
				let oLineProperties = {
					ApproverType: sApproverType,
					ApproverValue: sApproverValue,
					RowNum: iRowNumber,
					Action: sAction,
					Description: "",
					Title: sFromNode,
					Status: sStatus,
					CreqObmain: sCreqObmain,
					UsmdPriority: sUsmdPriority,
					UsmdReason: sUsmdReason,
					UsmdReasonRej: sUsmdReasonRej,
					Sign: sSign,
					AppStep: sAppStep,
					Apprdesc: sApprdesc,
					Emailtemp: sEmailTemp,
					AddlEmails: AddlEmails
				};

				// If the action is undefined, set it to empty 
				if (sAction === undefined || sAction === null) {
					sAction = "";
				}

				// Get the Nodes for with the specified key. If the node name is specified as "INIT", replace with AO (Expect to find in the FROM only)
				if (sFromNode === "Init") {
					sFromNode = DMRNode.IDs.INIT;
				}
				let oFromNode = oController.getNodeWithKey(oController, sFromNode);
				let oToNode = oController.getNodeWithKey(oController, sToNode);

				// if the from node or the to node do not exist, return without creating a line
				if (!oFromNode || !oToNode) {
					return null;
				}

				// Get the currently existing lines in the graph 
				let arrLines = oGraph.getLines();

				// Seach for a node with the nodes further and identify the right node
				if (sFromNode === "A0") {
					// Search for an existing line with the same, from and action setting and update the same.
					//There are two actions from A0 to first Approval -> Submit(Init) and Resubmit
					if (!oLine) {
						oLine = arrLines.find(function (line) {
							let oCustomData = line.getCustomData()[0]?.getValue();
							if ((line.getTo() === sToNode) &&
								(line.getFrom() === sFromNode) &&
								(line.getTitle() === sAction) &&
								(oCustomData.UsmdPriority === sUsmdPriority)) {
								return true;
							}
							return false;
						});
					}
				} else {
					arrLines.find(function (line) {
						// Bug 12698 - Duplicate rows on saving connections for Follw up CR. Rework on Bug 12664
						if ((line.getFrom() === sFromNode) && line.getTitle() === "Init") {
							oGraph.removeLine(line);
							line.destroy();
						}
					});
					//Search for an existing line with the same and from setting and update the same
				}

				// if a matching line was not found, create a new one
				if (!oLine) {
					oLine = oController.createNewLineForGraph(this, null);
				}

				// Clear all custom data and add new 
				//let iNodeType = oController._getNodeType(oController, this);
				let oActionData = oController.searchActions(oController, sAction, undefined);
				if (oActionData) {
					oLineProperties.Description = oActionData.UsmdActDesc;
				}

				oLine.removeAllCustomData();
				oLine.addCustomData(
					new sap.ui.core.CustomData({
						key: "LineData",
						value: JSON.parse(JSON.stringify(oLineProperties)),
						writeToDom: false
					})
				);

				oLine.setFrom(sFromNode);
				oLine.setTo(sToNode);
				oLine.setTitle(sAction);
				oLine.setDescription(oLineProperties.Description);
				oLine.setArrowPosition("Middle");

				if (bAddToGraph) {
					oGraph.addLine(oLine);
				}

				return oLine;
			},

			// Create a new line and attach all the necessary handlers. This is a short cut method for this screen
			createNewLineForGraph: function (oController, oLine) {
				// If a line was passed, attach the buttons and handlers to it, else create a new line and attach
				if (!oLine) {
					oLine = new sap.suite.ui.commons.networkgraph.Line();
				}
				oLine
					.setStretchToCenter(true)
					.addActionButton(
						new sap.suite.ui.commons.networkgraph.ActionButton({
							icon: "sap-icon://sys-cancel-2",
							title: "Remove Connection"
						})
						.attachPress(oController, oController.lineDeleteHandler, oLine)
					)
					.attachPress(oController, oController.showLineDetailsOnClick, oLine);

				return oLine;
			},

			stepActionsEnabled: function (sStepType, iNodeType) {
				if (iNodeType === DMRNode.Types.SystemMethod) {
					return true;
				} else if (sStepType === "Custom") {
					return true;
				} else if (sStepType && sStepType.length > 0 && iNodeType !== DMRNode.Types.Parallel) {
					return false;
				} else {
					return false;
				}
			},
			// Check / formatter function to check if the save button can be enabled on the approver create dialog
			approverDialogSaveEnabled: function (iNodeType, sApproverType, sApproverValToSave, sStepType, sCrType, sStepName, sStepDesc, sStepActions,
				sServiceName, sNewServiceName) {
				let oApproverModel = this.getApproverModel();
				if (iNodeType === DMRNode.Types.SystemMethod) {
					if (!sServiceName) {
						return false;
					} else if(!sStepActions || sStepActions.length === 0) {
						return false;
					} else if (sServiceName === "Custom" && !sNewServiceName) {
						return false;
					} else if (oApproverModel.getProperty("/serviceNameValueState") === sap.ui.core.ValueState.Error) {
						return false;
					}
				/** Task 12353 - Follow Up CR
				 * Show Save button only when user enters CR Type. Step action defaulted for user
				 */ 
				} else if (iNodeType === DMRNode.Types.FollowUpCr) {
					if(!sStepActions || sStepActions.length === 0) {
						return false;
					} else if(!sCrType || sCrType.length === 0) {
						return false;
					}
				} else {
					if (!sApproverType) {
						return false;
					}
					if (!sApproverValToSave) {
						return false;
					}
					if (!sStepType) {
						if (iNodeType === DMRNode.Types.Parallel) {
							return true;
						}
						return false;
					} else if ((sStepType === "Custom") && ((!sStepName || !sStepDesc))) {
						return false;
					} else if (oApproverModel.getProperty("/stepNameValueState") === sap.ui.core.ValueState.Error) {
						return false;
					}
					if (!sStepActions || sStepActions.length === 0) {
						if (iNodeType === DMRNode.Types.Parallel) {
							return true;
						}
						return false;
					}
				}

				return true;
			},

			onLiveChangeStepName: function (oEvent) {
				let input = oEvent.getSource();
				let oApproverModel = this.getApproverModel();
				let oModelData = oApproverModel.getProperty("/stepType/results");

				input.setValue(input.getValue().toUpperCase());
				let oSelectedObject = oModelData.find(function (element) {
					if (element.UsmdCrStype === input.getValue()) {
						return true;
					}
					return false;
				});
				if (oSelectedObject) {
					oApproverModel.setProperty("/stepNameValueState", sap.ui.core.ValueState.Error);
					oApproverModel.setProperty("/stepNameValueStateText", "Step Name already exists");

				} else {
					if (oApproverModel.getProperty("/stepNameValueState") === sap.ui.core.ValueState.Error) {
						oApproverModel.setProperty("/stepNameValueState", sap.ui.core.ValueState.None);
						oApproverModel.setProperty("/stepNameValueStateText", "");
					}
				}
			},
			onLiveChangeServiceName: function (oEvent) {
				let input = oEvent.getSource();
				let oApproverModel = this.getApproverModel();
				let oModelData = oApproverModel.getProperty("/serviceNames");

				input.setValue(input.getValue().toUpperCase());
				let oSelectedObject = oModelData.find(function (element) {
					if (element.UsmdSrvName.toUpperCase() === input.getValue()) {
						return true;
					}
					return false;
				});
				if (oSelectedObject) {
					oApproverModel.setProperty("/serviceNameValueState", sap.ui.core.ValueState.Error);
					oApproverModel.setProperty("/serviceNameValueStateText", "Service Name already exists");

				} else {
					if (oApproverModel.getProperty("/serviceNameValueState") === sap.ui.core.ValueState.Error) {
						oApproverModel.setProperty("/serviceNameValueState", sap.ui.core.ValueState.None);
						oApproverModel.setProperty("/serviceNameValueStateText", "");
					}
				}
			},
			// Function called when the save is pressed on the approver create / edit dialog 
			onApproverNodeSave: function () {
				//get the model for the approvel dialog 
				let oApproverModel = this.getApproverModel();
				let oApproverDetailsPopup = oApproverModel.getProperty("/ApprDetails");
				let iNodeType = oApproverModel.getProperty("/NodeType");

				if (!oApproverDetailsPopup.ApprValKey) {
					oApproverModel.setProperty("/ApprDetails/ApprValKey", oApproverDetailsPopup.ApprVal);
				}
				// The node must be edited if we are here 
				// Start adding attributes to the node 
				let oNode = this.nodeBeingEdited;
				let oThisObject = this;

				let oPromise = oThisObject.onSaveCustomDetails(oThisObject, oApproverDetailsPopup, iNodeType);
				oPromise.then(
					// Resolve 
					async function (oApproverDetails) {
						// Remove all the current attributes of the node 
						oNode.removeAllAttributes();
						// Add them back
						if (iNodeType === DMRNode.Types.SystemMethod) {
							// Bug 12474 - Error creating custom service name
							if(oApproverDetails.Servicename === "Custom"){
								oThisObject._addAttributeToNode(oThisObject, oNode, "Service Name", oApproverDetails.NewServiceName);
							}else {
								oThisObject._addAttributeToNode(oThisObject, oNode, "Service Name", oApproverDetails.Servicename);
							}
							// End Bug 12474 - Error creating custom service name
							oThisObject._addAttributeToNode(oThisObject, oNode, "Step Actions", oApproverDetails.StepActions);
						} else if(iNodeType === DMRNode.Types.FollowUpCr) {
							/** Task 12353 - Follow Up CR
							 * On Node Details Save, store the CR Type to model (To be changed for multi CR Types)
							 * Create connection from Follow Up CR Node to 99
							 */ 
							oThisObject._addAttributeToNode(oThisObject, oNode, "Change Request Type", (typeof oApproverDetails.CrType === "string" ? oApproverDetails.CrType : oApproverDetails.CrType.join(", ")));
							oThisObject._addAttributeToNode(oThisObject, oNode, "Step Actions", oApproverDetails.StepActions);
							oApproverModel.setProperty("/followUpCr", (typeof oApproverDetails.CrType === "string" ? [oApproverDetails.CrType] : oApproverDetails.CrType));
							// Get the graph object 
							let oGraph = oThisObject.getView().byId("graph");
							// Get the currently existing lines in the graph 
							let oGraphLines = oGraph.getLines();
							
							
							let oMatchedLine = oGraphLines.find(oLine => {
								return oLine.getFrom() === oNode.getKey() && oLine.getTo() === "99" && oLine.getTitle() === "31";
							});
							if(!oMatchedLine) {
								oThisObject.createNewLineForGraphNode(
									oGraph, oThisObject, undefined, undefined, oNode.getKey(), "99", "31",
									1, "", "", true, "", "", "", "", undefined, "", undefined, undefined);
							}
						} else {
							oThisObject._addAttributeToNode(oThisObject, oNode, "Approver Type", oApproverDetails.ApprType);
							oThisObject._addAttributeToNode(oThisObject, oNode, "Approver", oApproverDetails.ApprValKey);
							oThisObject._addAttributeToNode(oThisObject, oNode, "Step Type", oApproverDetails.StepType);
							oThisObject._addAttributeToNode(oThisObject, oNode, "Step Actions", oApproverDetails.StepActions);
						}
						let sNewTitle = oApproverDetails.Apprdesc ? oNode.getKey() + ": " + oApproverDetails.Apprdesc : oNode.getKey();
						oNode.setTitle(sNewTitle);
						oNode.setWidth(null);
						// Bug 12573 - Function updateConnectionsData is called when the approvers' information is changed
						await oThisObject.updateConnectionsData(oThisObject, oNode);
						oThisObject.closeNodeEditDialog();
					},
					function () {
						Utilities.showPopupAlert("Could not complete action.", MessageBox.Icon.ERROR, "Error");
					});
			},
			/**
			 * Helper function to add attributes to a node. Some nodes need additional logic to add 
			 * details and since this is being used in multiple locations, this helper function will 
			 * avoid code duplicacy. 
			 */
			_addAttributeToNode: function (oController, oNode, sAttributeName, oAttributeValue) {
				// Create the attribute as usual
				// Set the visibility to false by default, will be set to true if necessary
				let oAttribute = new ElementAttribute({
					label: sAttributeName+ ":",
					value: oAttributeValue,
					visible: false
				});

				// If the value is an object or array store it also as a custom attribute 
				if ($.type(oAttributeValue) === "object" || $.type(oAttributeValue) === "array") {
					let oCustomDataAttrVal = new sap.ui.core.CustomData({
						key: sAttributeName,
						value: JSON.parse(JSON.stringify(oAttributeValue)),
						writeToDom: false
					});
					oAttribute.setValue(undefined);
					oAttribute.addCustomData(oCustomDataAttrVal);
				}

				// Create visible attribute if necessary ... 
				let oAttributeVisible = null;

				// for attribute that need special behavior, add the logic 
				switch (sAttributeName) {
				case "Step Type":
					// Add the step type node Description [visible user friendly]
					let oSTypeArr =
						oController.getApproverModel().getProperty("/stepType/results");

					let oSelItem = oSTypeArr.find(function (arrD) {
						return (
							arrD.UsmdCrStype === oAttributeValue
						);
					});

					let oStepTypeDesc = oSelItem ? oSelItem.UsmdStDesc : oAttributeValue;
					oAttributeVisible = new ElementAttribute({
						label: sAttributeName + ":",
						value: oStepTypeDesc
					});
					break;
				case "Step Actions":
					// search for the step actions desc and add a new attribute with user 
					// friendly visible content. 

					// If the value is undefined or null, skip adding 
					if (!oAttributeValue) {
						return;
					}

					/**
					 * Check the node type and handle the actions accordingly.
					 * 
					 * For DynamicRuleList, actions are stored as is. Not used in anyway as the details are not visible to the user.
					 * For the rest of the node types, the data is stored as Comma Separated values. 
					 */
					let iNodeType = oController._getNodeType(oController, oNode);

					let arrStepActions = oAttributeValue;
					// Check if the data is not of type array .. if not use split to make an array 
					if (!Array.isArray(oAttributeValue)) {
						arrStepActions = oAttributeValue.split(",");
					}

					let sStepActionsDesc = oAttributeValue;
					if ((arrStepActions.length > 0) && (iNodeType !== DMRNode.Types.Dynamic) && (iNodeType !== DMRNode.Types.Parallel)) {
						let sTempStepActionsDesc = "";
						// Search the actions and get their details 
						arrStepActions.forEach(function (stepaction) {
							let stepData = oController.searchActions(oController, stepaction, iNodeType);
							if(stepData) {
								sTempStepActionsDesc += (sTempStepActionsDesc === "") ? "" : ", ";
								sTempStepActionsDesc += stepData.UsmdActDesc;
							}
							
						});
						sStepActionsDesc = sTempStepActionsDesc;
					}
					oAttributeVisible = new ElementAttribute({
						label: sAttributeName + ":",
						value: sStepActionsDesc
					});
					break;

					// Store the data as custom data for this type 
				case "DynamicRuleList":

					/* ************* Nothing to do, the default handling already adds the details at the top of the function *********** */

					break;

				default: 
					// No Actions 
					break;
				}

				// Delete any existing attributes with the same name from the node 
				oNode.getAttributes().forEach(function (attribute) {
					if (attribute.getLabel() === sAttributeName + ":") {
						oNode.removeAttribute(attribute);
					}
				});

				// if the second attribute is not null, add to the node
				if (oAttributeVisible !== null) {
					oNode.addAttribute(oAttributeVisible);
				} else {
					// If a second descritive attribute is not added, make the original attribute visible
					oAttribute.setVisible(true);
				}

				// Add the attribute to the node
				oNode.addAttribute(oAttribute);
			},
			/**
			 * When creating a new node, if there is a need to create a new action, save the same before 
			 * saving the node. 
			 * 1. Check if the step type is custom. 
			 * 2. If not custom resolve and return to continue execution
			 * 3. If the step type is custom
			 *		- Resolve once the step type is saved. Then set the Step type to the new step 
			 *      - IF the step create faile, reject the promise 
			 */
			onSaveCustomDetails: function (oController, oApproverDetails, iNodeType) {
				let oPromise = new Promise(function (resolve, reject) {
					let oApproverModel = oController.getApproverModel();
					let promiseShowPopupAlert;
					let oServiceData;
					// Bug 12474 - Error creating custom service name
					if (iNodeType === DMRNode.Types.Normal || iNodeType === DMRNode.Types.ParallelChild) {
					// End Bug 12474- Error creating custom service name
						// If the step type is not CUSTOM, nothing to save. Return with data
						if (oApproverDetails.StepType !== "Custom") {
							// Return the approver details as is. No Changes required
							resolve(oApproverDetails);
						} else {
							// Open the transport selection dialog
							oController._selectTransportPackage(false, true, false, false)
							.then(function (oResponseSelection) {
								let sTransport = oResponseSelection.workbenchTransport;
								let oStepName = oApproverDetails.StepName;
								let oStepDesc = oApproverDetails.StepDesc;
								let arrStepActions = oApproverDetails.StepActions;

								// Create the service packet
								oServiceData = {
									UsmdCrStype: oStepName,
									UsmdStDesc: oStepDesc,
									Transport: sTransport,
									Message: "",
									Type: "",
									STEPTYPETOACTION: []
								};

								// Fill the actions into the service packet
								arrStepActions.forEach(function (action) {
									let STEPTYPETOACTION = {};
									STEPTYPETOACTION.UsmdCrAction = action;
									STEPTYPETOACTION.UsmdSequenceNr = oServiceData.STEPTYPETOACTION.length.toString();
									STEPTYPETOACTION.UsmdCrStype = oStepName;
									oServiceData.STEPTYPETOACTION.push(STEPTYPETOACTION);
								});

								(new DMRDataService(
									oController,
									"GETWFCFG",
									"/STEPTYPESet",
									"stepType",
									"/", // Root of the received data
									"Create Step Type"
								)).saveData(
									false,
									oServiceData,
									null, {
										success: {
											fCallback: function (oParams, oResponseData) {
												promiseShowPopupAlert =
													Utilities.showPopupAlert(oResponseData.Message, MessageBox.Icon.INFORMATION, "Custom Step Type");
												promiseShowPopupAlert.then(function () {
													let promiseStepType = GetListsData.getWFConfig(oController, "/STEPTYPESet");
													promiseStepType.then(
														function (oData) {
															oApproverModel.setProperty("/stepType", oData);
															oApproverDetails.StepType = oResponseData.UsmdCrStype;
															resolve(oApproverDetails);
														}
													);
												});

											},
											oParam: oController
										},
										error: {
											fCallback: function (oParams, oErrorData) {
												reject(oErrorData);
											}
										}
									});
							});
						}
					} else if (iNodeType === DMRNode.Types.SystemMethod) {
						if (oApproverDetails.Servicename !== "Custom") {
							resolve(oApproverDetails);
						} else {
							// Open the transport selection dialog
							oController._selectTransportPackage(false, true, false, false)
							.then(function (oResponseSelection) {
								let sTransport = oResponseSelection.workbenchTransport;

								oServiceData = {
									UsmdSrvName: oApproverDetails.NewServiceName,
									Tr: sTransport
								};

								(new DMRDataService(
									oController,
									"GET_SERVICE_NAMES",
									"/SERVICENAMESet",
									"serviceName",
									"/", // Root of the received data
									"Create Service Name"
								)).saveData(
									false,
									oServiceData,
									null, {
										success: {
											fCallback: function (oParams, oResponseData) {
												promiseShowPopupAlert =
													Utilities.showPopupAlert(oResponseData.Message, MessageBox.Icon.INFORMATION, "Custom Step Type");
												promiseShowPopupAlert.then(function () {
													let promiseServiceNames = GetListsData.getServiceNamesList(oController);
													promiseServiceNames.then(
														function (oData) {
															oApproverModel.setProperty("/serviceNames", oData);
															oApproverDetails.Servicename = oResponseData.UsmdSrvName;
															resolve(oApproverDetails);
														}
													);
												});
											},
											oParam: oController
										},
										error: {
											fCallback: function (oParams, oErrorData) {
												reject(oErrorData);
											}
										}
									});
							});

						}
						// Bug 12603 - Condition added for Follow Up CR
					}else if(iNodeType === DMRNode.Types.FollowUpCr){
						resolve(oApproverDetails);
					}
				});

				return oPromise;
			},
			// Called when the cancel button is pressed on the dialog box. Also called from the save handler for cleanup
			closeNodeEditDialog: function () {
				this.nodeBeingEdited = null;
				this.ApproverDataDialog.close();

				this.nodeBeingEdited = null;
			},
			// Delete the line and cleans up 
			lineDeleteHandler: function (oEvent, oController) {
				// Go to full screen before editing
				let thisObject = this;

				let oTransportPromise = oController._setGraphToFullScreen(oController);
				oTransportPromise.then(function () {

					// Get the graph object 
					let oGraph = oController.getView().byId("graph");

					// Delete the line 
					oGraph.removeLine(thisObject);
					thisObject.destroy();
				});
			},

			// Display the line text when mouse hovers   REMOVE .. NOT REALLY REQUIRED
			/**
			 * 
			 * Task 12336 - WF Graph Layout Change
			 * Only 1 Line to be shown between two nodes
			 * Popover Changed to show details of all connections from FROM node to TO node
			 * Loop through all lines to find From and To property same as the current lines From and To property
			 * Ad connection details to HBox and add content to Popover 
			 */
			showLineDetailsOnClick: function (oEvent, oController) {
				oEvent.preventDefault();
				let oGraph = oController.getView().byId("graph");
				let arrGraphLines = oGraph.getLines();

				let popoverLineClick = new sap.m.Popover({});
				let oVBox = new sap.m.VBox({
					width: "90%"
				});

				let oTitleHBox = new sap.m.HBox({
					width: "90%",
					fitContainer: true,
					justifyContent: sap.m.FlexJustifyContent.SpaceBetween
				});
				oTitleHBox.addStyleClass("sapUiSmallMarginTop sapUiSmallMarginBottom");

				oTitleHBox.addItem(new Label({
					text: this.getFrom()
				}).addStyleClass("sapUiTinyMarginBegin sapUiTinyMarginEnd"));

				oTitleHBox.addItem(new sap.ui.core.Icon({
					src: "sap-icon://arrow-right"
				}));		

				oTitleHBox.addItem(new Label({
					text: this.getTo()
				}).addStyleClass("sapUiTinyMarginBegin"));

				oVBox.addItem(oTitleHBox);

				arrGraphLines.forEach(oLine => {
					if (oLine.getFrom() === this.getFrom() && oLine.getTo() === this.getTo()) {
						let oHBox = new sap.m.HBox({
							width: "90%",
							alignItems: "Center",
							fitContainer: true,
							justifyContent: sap.m.FlexJustifyContent.SpaceBetween
						});
						oHBox.addStyleClass("sapUiSmallMarginTop sapUiSmallMarginBottom");

						let ApprDesc = oLine.getCustomData()[0].getValue().Apprdesc;
						if(ApprDesc) {
							oHBox.addItem(new Label({
								text: oLine.getCustomData()[0].getValue().Apprdesc
							}).addStyleClass("sapUiTinyMarginBegin sapUiTinyMarginEnd"));
						} else {
							let sTitle = oLine.getTitle();
							if (oLine.getDescription()) {
								sTitle = oLine.getDescription() + " ( " + sTitle + " )";
							}
							sTitle = sTitle ? sTitle : "No action set";
							oHBox.addItem(new Label({
								text: sTitle
							}).addStyleClass("sapUiTinyMarginBegin sapUiTinyMarginEnd"));
						}

						oVBox.addItem(oHBox);
					}
				});

				// popoverLineClick.setTitle(this.getFrom() + "-->" + this.getTo());
				popoverLineClick.addContent(oVBox);

				// Add the end button 
				let oEndButton = new Button({
						icon: "sap-icon://decline",
						type: sap.m.ButtonType.Ghost
							//,width: "2em"
					})
					.attachPress(oController, () => popoverLineClick.close(), this);
				oEndButton.addStyleClass("sapUiNoMarginTop sapUiNoMarginBottom");
				popoverLineClick.setEndButton(oEndButton);

				// create a label content to display 
				popoverLineClick.attachAfterClose(oController, oController.onLineDetailsPopupClose, popoverLineClick);

				// open the popover
				popoverLineClick.openBy(this);
			},
			onLineDetailsPopupClose: function (oEvent, oController) {
				this.detachAfterClose(oController.onLineDetailsPopupClose, this);
				this.removeAllContent();
				this.destroy();
			},

			/**
			 * Return the node type to determine the behavior of the node. Differentiate between the following types of nodes. 
			 * 
			 * Types of Nodes 
			 * 1. Init (A0)
			 * 2. Normal : in Group 02proc (sap-icon://person)
			 * 3. Parallel Node (sap-icon://split) in Group 02procPL
			 * 4. Parallel Child : Child of a parallel node (sap-icon://person)
			 * 5. Decision Table
			 * 6. Activation (91)
			 * 7. Discard (92)
			 * 8. Complete (99)
			 * 
			 * The nodes have the following buttons based on their type
			 * 
			 *	> Add Button
			 *		~ Add Approver 
			 *		~ Add Parallel 
			 *		~ Add Decision Table
			 *  [ Node A0, Normal Node, Parallel Child, Parallel Node ]
			 *	
			 *	> Edit Connection
			 *	[Node A0, Normal Node, Parallel Child ]
			 * 
			 *	> Node Action
			 *		~ Edit Node
			 *		~ Delete Node
			 *	[Normal Node, Parallel Node, Parallel Child]
			 *	
			 */
			_getNodeType: function (oController, oNode) {
				let sNodeIcon = oNode.getIcon();
				let sNodeKey = oNode.getKey();
				let iNodeType = DMRNode.Types.Normal;

				switch (sNodeKey) {
				case DMRNode.IDs.INIT:
					iNodeType = DMRNode.Types.Init;
					break;

				case DMRNode.IDs.ACTIVATE:
					iNodeType = DMRNode.Types.Activate;
					break;

				case DMRNode.IDs.DISCARD:
					iNodeType = DMRNode.Types.Discard;
					break;

				case DMRNode.IDs.COMPLETE:
					iNodeType = DMRNode.Types.Complete;
					break;

					// If the node is not of the standard initial types, check other parameters 

				default:
					let sGroupName = oNode.getGroup();

					// Parallel Node
					if (sNodeIcon === DMRNode.Icons.Parallel) {
						iNodeType = DMRNode.Types.Parallel;
					}
					else if (sNodeIcon === DMRNode.Icons.ParallelChild){
						iNodeType = DMRNode.Types.ParallelChild;
					} 
					// Normal Node
					else if (sNodeIcon === DMRNode.Icons.Normal && sGroupName.startsWith(DMRNode.Groups.WorkFlow)) {
						iNodeType = DMRNode.Types.Normal;
					}
					// Decision Table Node
					else if (sNodeIcon === DMRNode.Icons.Dyamic) {
						iNodeType = DMRNode.Types.Dynamic;
					} else if (sNodeIcon === DMRNode.Icons.SystemMethod) {
						iNodeType = DMRNode.Types.SystemMethod;
					} else if(sNodeIcon === DMRNode.Icons.Merge) {
						iNodeType = DMRNode.Types.Merge;
					} else if(sNodeIcon === DMRNode.Icons.FollowUpCr) {
						iNodeType = DMRNode.Types.FollowUpCr;
					}

					break;
				}

				return iNodeType;
			},

			/**
			 * Handler for the node add for Dynamic and Parallel flow 
			 * The dynamic and parallel flow have the same screen handlers except for some restriction / check 
			 * for the parallel flow 
			 *		Parallel Flow
			 *		- Allow only Type 4 entities to be list for add 
			 *		- The user will not be allowed to change the entity once selected 
			 *		- Default row display is not shown 
			 *		- The flow cannot be saved unless at least 1 rule has been added 
			 *		- The step type for all rules must be the same
			 */
			_showDynamicWorkflowEditDialog: function (oController, oNode, oNodeData) {

				let oTransportPromise = oController._setGraphToFullScreen(oController);
				oTransportPromise.then(function () {
					let sNodeType = oController._getNodeType(oController, oNode);
					DynamicWorkflowDialog.open(oController, oNode, oNodeData, sNodeType);
				});
			},

			/**
			 * Open the node edit handler with the parameters provided. 
			 * This is called from the EDIT and ADD node handlers 
			 */
			_showNodeEditDialog: function (oController, oNode, oNodeData) {
				// Get ApproverModel 
				let oApproverModel = oController.getApproverModel();

				let oTransportPromise = oController._setGraphToFullScreen(oController);
				oTransportPromise.then(function () {
					if (!oController.ApproverDataDialog) {
						oController.ApproverDataDialog =
							sap.ui.xmlfragment(
								"ApproverSelection",
								"dmr.mdg.supernova.SupernovaFJ.view.Workflow.ApproverSelection",
								oController);
						oController.getView().addDependent(oController.ApproverDataDialog);
					}

					// Open the dialog
					oController.ApproverDataDialog.open();

					/** Get the node type
					 * Store the node type to approverMode>/NodeType
					 * 
					 * If the node type is ParallelChild,
					 *	> Retrieve the Node Details of the the parent Parallel Node
					 *  > Initlialize the ApproverType and Approver 
					 */
					let iNodeType = oController._getNodeType(oController, oNode);
					oApproverModel.setProperty("/NodeType", iNodeType);

					//Add ** Step action if Node Type is System Method
					if(iNodeType === DMRNode.Types.SystemMethod) {
						oController.searchActions(oController, undefined, iNodeType);
					} else if(iNodeType === DMRNode.Types.FollowUpCr) {
						/** Task 12353 - Follow Up CR
						 * Filter Change Request Type list in Approver Select Fragment by Data Model
						 * Remove the current CR Type from the list
						 */
						let arrChangeRequestList = oController.getView().getModel("changeReqs").getData().results;
						let arrFilteredChangeRequestList = [];
						jQuery.extend(true, arrFilteredChangeRequestList, arrChangeRequestList);
						let oSelectedChangeRequest = oController.getView().getModel("viewWorkflow").getProperty("/selectedChangeRequest");
						arrFilteredChangeRequestList = arrFilteredChangeRequestList.filter(oChangeRequest => {
							//Bug 12638 - The condition "oChangeRequest.UsmdEdtnType === oSelectedChangeRequest.UsmdEdtnType" was removed to avoid filter more change requests than the expected
							return oChangeRequest.UsmdCreqType !== oSelectedChangeRequest.UsmdCreqType && oChangeRequest.UsmdModel === oSelectedChangeRequest.UsmdModel;
						});
						oApproverModel.setProperty("/mainChangeReqList", arrFilteredChangeRequestList);
					    // For Single Object we are filtering the showing by "" / "S" / "SP"						
						if (!oNodeData.CrType) {
							oNodeData.CrType = [];
							oNodeData.CrKind = 0;
							arrFilteredChangeRequestList = arrFilteredChangeRequestList.filter(oCRequest => oCRequest.SingleParallel === "S" || oCRequest.SingleParallel === "SP" || oCRequest.SingleParallel === "");
						}else{
							let oCRT = arrFilteredChangeRequestList.find(oCRequest => oCRequest.UsmdCreqType === oNodeData.CrType.split(", ")[0]);
							oNodeData.CrKind = ( oCRT.SingleParallel === "P" || ( oCRT.SingleParallel === "SP"  && oNodeData.CrType.length > 1  ) ) ? 1 : 0;
							oNodeData.CrType = oNodeData.CrType.split(", ");
							arrFilteredChangeRequestList = arrFilteredChangeRequestList.filter(oCRequest => {
								if(oNodeData.CrKind === 0){
									return (oCRequest.SingleParallel === "S" || oCRequest.SingleParallel === "SP" || oCRequest.SingleParallel === "");
								}else{
									return (oCRequest.SingleParallel === "P" || oCRequest.SingleParallel === "SP");
								}
							});
								// oCRequest.SingleParallel === oCRT.SingleParallel);
						}
						oApproverModel.setProperty("/changeReqList", arrFilteredChangeRequestList);
					}

					oApproverModel.setProperty("/ApprDetails", oNodeData);

				});
			},

			/**
			 * Helper function to get the control object based on its id. 
			 * Combine the dialog id with the control id to retrieve the control 
			 */
			_nodeEditDialogGetComponent: function (oController, sControlName) {
				return sap.ui.getCore().byId("ApproverSelection--" + sControlName);
			},

			/**
			 * Read the attributes from the node and return the approver details for this node
			 */
			_getNodeApproverDetails: function (oNode) {
				let arrAttributes = oNode.getAttributes();

				// Get the model of the dialog
				let oApproverDetails = {};

				for (let i = 0; i < arrAttributes.length; i++) {
					let sLabelName = arrAttributes[i].getLabel().slice(0, -1);
					switch (sLabelName) {
					case "Approver Type":
						oApproverDetails.ApprType = arrAttributes[i].getValue();
						break;
					case "Approver":
						oApproverDetails.ApprVal = arrAttributes[i].getValue();
						break;
					case "Step Type":
						if (arrAttributes[i].getVisible() === false) {
							oApproverDetails.StepType = arrAttributes[i].getValue();
						}
						break;
					case "Step Name":
						oApproverDetails.StepName = arrAttributes[i].getValue();
						break;
					case "Service Name":
						oApproverDetails.Servicename = arrAttributes[i].getValue();
						break;
					case "Step Description":
						oApproverDetails.StepDesc = arrAttributes[i].getValue();
						break;
					case "Change Request Type":
						oApproverDetails.CrType = arrAttributes[i].getValue();
						break;
					case "Step Actions":
						if (arrAttributes[i].getVisible() === true) {
							break; // No need to read this
						}

						if (arrAttributes[i].getValue()) {
							oApproverDetails.StepActions = arrAttributes[i].getValue().split(",");
						} else if (arrAttributes[i].getCustomData()) {
							let oCustomData = arrAttributes[i].getCustomData().find(function (customData) {
								return customData.getKey() === sLabelName;
							});

							if (oCustomData) {
								oApproverDetails.StepActions = oCustomData.getValue(sLabelName);
							}
						}
						break;
					case "DynamicRuleList":
						// Get the Custom data for the attribute 
						let oCustomDynamicData = arrAttributes[i].getCustomData().find(function (customData) {
							return customData.getKey() === sLabelName;
						});

						// If the custom data was found, retrieve the value
						if (oCustomDynamicData) {
							oApproverDetails.DynamicRuleList = oCustomDynamicData.getValue(sLabelName);
						}

						break;

						default: 
						// No Actions 
						break;
					}
				}

				return oApproverDetails;
			},

			/**
			 * Save the node contents for Dynamic node, returned from the edit handler and store to the attributes.
			 */
			saveDynamicNodeContent: function (oNode, oNodeData) {
				this._addAttributeToNode(this, oNode, "DynamicRuleList", oNodeData);
				this._addAttributeToNode(this, oNode, "Approver Type", oNodeData.ApproverType);
				this._addAttributeToNode(this, oNode, "Approver", oNodeData.ApproverValue);
				this._addAttributeToNode(this, oNode, "Step Type", oNodeData.StepType);
				this._addAttributeToNode(this, oNode, "Step Actions", oNodeData.DTACTIONS);
				let sNewTitle = oNodeData.Apprdesc ? oNode.getKey() + ": " + oNodeData.Apprdesc : oNode.getKey();
				oNode.setTitle(sNewTitle);
			},

			/**
			 * Edit the Dynamic Decision node.
			 * 
			 * Open the dialong box to show the attribute selection table 
			 */
			nodeDynamicEditHandler: function (oEditButton, oController) {

				// get the model of the dialog
				// let oDynamicNodeDetails = oController._getCustomeDataValueForDynamicNode(oController, this);
				let oDynamicNodeDetails = oController._getNodeApproverDetails(this).DynamicRuleList;
				let sNodeType = oController._getNodeType(oController, this);

				oController._showDynamicWorkflowEditDialog(oController, this, oDynamicNodeDetails, sNodeType);
			},

			/**
			 * Edit the selected node
			 * Read all the node details from the attributes and open the dialog to edit. 
			 */
			nodeEditHandler: function (oEditButton, oController) {

				// Get the model of the dialog
				let oApproverDetails = oController._getNodeApproverDetails(this);

				// Open the Node data editor fragment so that the parameters can be set. 
				oController.nodeBeingEdited = this;
				oApproverDetails.ApprValToSave = oApproverDetails.ApprVal;
				oApproverDetails.Apprdesc = oController.nodeBeingEdited.getTitle().includes(": ") ? oController.nodeBeingEdited.getTitle().split(": ")[1] : undefined;
				// Open the node with Empty Data
				oController._showNodeEditDialog(oController, this, oApproverDetails);
			},
			/**
			 * Create a new node with empty data, add the connection to the new node 
			 * and open the edit dialog to edit the node properties 
			 * Add all the action buttons for the node
			 * 
			 * Runs in Node context (this = Node )
			 */
			nodeAddActionHandler: function (oAddNodeEvent, oController) {
				// In this context this is the node on which the action was triggered
				let oNode = this;
				let sActivatedBtnIcon = oAddNodeEvent.getSource().getIcon();

				// Get the graph object 
				let oGraph = oController.getView().byId("graph");

				let arrGraphNodes = oGraph.getNodes();
				/**
				 * Bug 12460 - Follow Up Change Request
				 * When user clicks on Add Follow Up Cr, check whether a Follow UP CR node exists.
				 * 		If true, show an error message and return
				 */
				if(sActivatedBtnIcon === DMRNode.Icons.FollowUpCr) {
					let oFollowUpCrNode = arrGraphNodes.find(oGraphNode => {
						return oGraphNode.getIcon() === DMRNode.Icons.FollowUpCr;
					});
					if(oFollowUpCrNode) {
						Utilities.showPopupAlert(
							"Cannot add more than 1 Follow Up Cr Node to the Workflow Graph",
							MessageBox.Icon.Error, "Duplicate Follow Up CR Node Error");
						return;
					}
				}

				//  Create a new node 
				let sNextNodeKey = oController._getKeyNameforGraphElement(oGraph, "Node", "A");

				let oNewNode = oController.createNodeForGraphWrapper(oController, oNode, sActivatedBtnIcon, sNextNodeKey);

				oController.getApproverModel().setProperty("/" + oNewNode.getKey() + "StepType", "");

				oGraph.addNode(oNewNode);
				
				let sNewNodeType = oController._getNodeType(oController, oNewNode);
				
				//Bug 9989 - Avoid adding duplicate line from A0 for INIT if connection was already created
				//Delete already existing line
				if(this.getKey() === "A0") {
					let oDuplicateLineToDelete = oGraph.getLines().find(oLine => {
						return oLine.getFrom() === this.getKey() &&
							oLine.getTitle() === "Init";
					});
					if(oDuplicateLineToDelete) {
						oGraph.removeLine(oDuplicateLineToDelete);
						oDuplicateLineToDelete.destroy();
					}
				}

				//Add Merge Node if New Node Type is Parallel Node
				if(sNewNodeType === DMRNode.Types.Parallel) {
					let sMergeNodeKey = oController._getKeyNameforGraphElement(oGraph, "Node", "A");
					let oMergeNode = oController.createNodeForGraphWrapper(oController, oNewNode, DMRNode.Icons.Merge, sMergeNodeKey);
					oController._addAttributeToNode(oController, oMergeNode, "Step Actions", ["03", "04"]);
					oMergeNode.setTitle(oMergeNode.getKey() + ": Merge Node for " + sNextNodeKey);
					oGraph.addNode(oMergeNode);
				
				} else if(sNewNodeType === DMRNode.Types.SystemMethod) {
					oController._addAttributeToNode(oController, oNewNode, "Step Actions", ["**"]);
				} else if(sNewNodeType === DMRNode.Types.FollowUpCr) {
					oController._addAttributeToNode(oController, oNewNode, "Step Actions", ["31"]);
				}
				
				// Create a new Line .. when a new node is added the action is not defined (a line is added just as a default)
				oController.createNewLineForGraphNode(
					oGraph, oController, undefined, undefined, this.getKey(), oNewNode.getKey(), this.getKey() === "A0" ? "Init" : "",
					1, "", "", true, "", "", "", "", undefined, "", undefined, undefined);
					
				// Open the Node data editor fragment so that the parameters can be set. 
				oController.nodeBeingEdited = oNewNode;

				if (sNewNodeType === DMRNode.Types.Dynamic || sNewNodeType === DMRNode.Types.Parallel) {
					oController._showDynamicWorkflowEditDialog(oController, oNewNode, undefined, sNewNodeType);
				} else {
					let oNodeData = oController._getNodeApproverDetails(oNewNode);
					// Open the node with Empty Data
					if(sNewNodeType === DMRNode.Types.Normal) {
						oController._showNodeEditDialog(oController, oNewNode, {});
					} else {
						//For System Method, open node with default step action **
						//For Follow UP CR, open node with step action set to 31
						oController._showNodeEditDialog(oController, oNewNode, oNodeData);
					}
					
				}
			},

			/**
			 * Called to create nodes of type normal, Dynamic or Parallel
			 * Called from 
			 *  1. nodeAddActionHandler
			 *  2. _transformWorkFlowData 
			 */
			createNodeForGraphWrapper: function (oController, oSourceNode, sActivatedBtnIcon, sNodeKey) {

				let sNodeType = DMRNode.Types.Init;
				let sNextGroupKey = DMRNode.Groups.WorkFlow;

				// If the source node is available (not undefined), retrive the information
				if (oSourceNode) {
					sNodeType = oController._getNodeType(oController, oSourceNode);
					sNextGroupKey = oSourceNode.getGroup();
				}

				// Get the graph object 
				let oGraph = oController.getView().byId("graph");

				// Default the GroupKey
				if (sNodeType === DMRNode.Types.Init) {
					sNextGroupKey = DMRNode.Groups.WorkFlow;
				}

				/**
				 * Check the button  from which the event has been generated 
				 * if ( Add Approver )
				 *		Create a normal node (Box, no icon)
				 * else ( Add Parallel Flow )
				 *		Create a parallel flow node (CIRCLE, Icon Split )
				 */
				let sNodeShape;
				
				if(sActivatedBtnIcon === DMRNode.Icons.Merge) {
					sNodeShape = sap.suite.ui.commons.networkgraph.NodeShape.Circle;
				} else {
					sNodeShape = sap.suite.ui.commons.networkgraph.NodeShape.Box;
				}
				
				//let sNodeIcon = undefined; 
				let sNodeIcon = sActivatedBtnIcon;

				if (sActivatedBtnIcon === DMRNode.Icons.Parallel || sActivatedBtnIcon === DMRNode.Icons.Dyamic) {
					sNodeShape = "Circle";
					sNodeIcon = sActivatedBtnIcon;
					sNextGroupKey =
						oController._getKeyNameforGraphElement(
							oGraph, "Group",
							(sActivatedBtnIcon === DMRNode.Icons.Parallel) ? DMRNode.Groups.Parallel + "_" + sNodeKey + "_" : DMRNode.Groups.Dynamic);

					oController._addGroupToGraph(
						oController, oGraph, sNextGroupKey,
						(sActivatedBtnIcon === DMRNode.Icons.Parallel) ? "Parallel " + sNodeKey : "Dynamic",
						DMRNode.Groups.WorkFlow);
				} else if (sActivatedBtnIcon !== DMRNode.Icons.Merge && sNodeType === DMRNode.Types.Parallel) {
					/** 
					 * Adding an Approver to a Parallel Node must be saved as a parallel child
					 * Fetch The Node Type of oSourceNode.
					 * If Parallel, then add the new node as parallel child in new Swimlane
					 * If the new node from Parallel Node is Merge Node, add it to same swimlane
					 */
					 sNodeShape = "Box";
					 sNodeIcon = DMRNode.Icons.ParallelChild;
					 sNextGroupKey =
						oController._getKeyNameforGraphElement(
							oGraph, "Group",
							DMRNode.Groups.Parallel + "_" + oSourceNode.getKey() + "_");

					oController._addGroupToGraph(
						oController, oGraph, sNextGroupKey,
						"Parallel Child " + oSourceNode.getKey(),
						DMRNode.Groups.WorkFlow);
					
				} else if(sNodeType === DMRNode.Types.Merge && !(sActivatedBtnIcon === DMRNode.Icons.Parallel || sActivatedBtnIcon === DMRNode.Icons.Dyamic)) {
					/**
					 * Adding a normal approver or system method to merge node
					 * Node has to be added to the next swimlane
					 */
					 sNodeShape = "Box";
					 sNodeIcon = sActivatedBtnIcon;
					 sNextGroupKey =
						oController._getKeyNameforGraphElement(
							oGraph, "Group",
							DMRNode.Groups.WorkFlow);
							
					oController._addGroupToGraph(
						oController, oGraph, sNextGroupKey,
						"Workflow", 
						DMRNode.Groups.WorkFlow);
				}
				else if(sNodeType === DMRNode.Types.ParallelChild) {
                	//Parallel Child can be added to Parallel Child only
					sNodeShape = "Box";
					sNodeIcon = DMRNode.Icons.ParallelChild;
				} else if(sNodeIcon === DMRNode.Icons.FollowUpCr) {
					sNodeShape = "Box";
					sNextGroupKey = DMRNode.Groups.Activate;
				}

				let oNewNode =
					oController._createNodeForGraph(oController, sNextGroupKey, sNodeShape, sNodeKey, sNodeIcon);

				// Default the attributes for the node
				if (sNodeIcon === DMRNode.Icons.SystemMethod) {
					oController._addAttributeToNode(oController, oNewNode, "Service Name", "");
					oController._addAttributeToNode(oController, oNewNode, "Step Actions", ["**"]); // TASK 9605
				} else if(sNodeIcon === DMRNode.Icons.FollowUpCr) {
					oController._addAttributeToNode(oController, oNewNode, "Change Request Type", "");
					oController._addAttributeToNode(oController, oNewNode, "Step Actions", ["31"]);
				} else {
					oController._addAttributeToNode(oController, oNewNode, "Approver Type", "");
					oController._addAttributeToNode(oController, oNewNode, "Approver", "");
					oController._addAttributeToNode(oController, oNewNode, "Step Type", "");
					oController._addAttributeToNode(oController, oNewNode, "Step Actions", []);
				}

				return oNewNode;
			},

			/**
			 * 1. Create a group with the provided details
			 * 2. Identify the location at which the group is to be inserted into the graph
			 * 3. Insert the group into the graph 
			 * 
			 * The standard Groups are created and added at the beginning, and those will not pass through this flow. 
			 * 
			 * The groups will be prioritised as following 
			 * 1. Workflow
			 * 2. Parallel 
			 * 3. Dyanmic 
			 * 
			 * When a group creation is done, the funtion will create the group look for the last occurence of this group kind
			 * and insert the group after that. If there is no such group existing, it will be inserted after the group of previous 
			 * highest priority. 
			 * 
			 * E.g. If a Parallel group is being added 
			 *			and there are no previous parallel groups, this new group will be added after the last workflow group
			 *			and if there are previous parallel groups, this new group will be added after the last group of the same kind
			 */
			_addGroupToGraph: function (oController, oGraph, sGroupKey, sGroupTitle, sParentGroupKey) {

				// Default to WorkFlow so that a group would be added after init by default
				let sGroupType = DMRNode.Groups.WorkFlow;
				let sPrevGroupType = DMRNode.Groups.Init;
				let iInsertIndex = 0;

				/* If the key contains DMRNode.Groups.WorkFlow, store it as such */
				if (sGroupKey.search(DMRNode.Groups.WorkFlow) !== -1) {
					sGroupType = DMRNode.Groups.WorkFlow;
					sPrevGroupType = DMRNode.Groups.Init;

					/* If the key contains DMRNode.Groups.Parallel, store it as such */
				} else if (sGroupKey.search(DMRNode.Groups.Parallel) !== -1) {
					sGroupType = DMRNode.Groups.Parallel;
					sPrevGroupType = DMRNode.Groups.WorkFlow;

					/* If the key contains DMRNode.Groups.Dynamic, store it as such */
				} else if (sGroupKey.search(DMRNode.Groups.Dynamic) !== -1) {
					sGroupType = DMRNode.Groups.Dynamic;
					sPrevGroupType = DMRNode.Groups.Parallel;

				} else { /* ERROR */ }

				/* find the index where this new group is to be inserted */
				let arrGroups = oGraph.getGroups();
				// Filter the groups by the ones that have a node in them 
				arrGroups = arrGroups.filter(function (group) {
					return (group.getNodes().length > 0);
				});
				let arrGroupKeys = arrGroups.map(function (el) {
					return el.getKey();
				});

				/* Searh for the last index of current group */
				iInsertIndex = arrGroupKeys.lastIndexOf(sGroupType);

				// If not found, search for the previous group
				if (iInsertIndex === -1) {
					iInsertIndex = arrGroupKeys.lastIndexOf(sPrevGroupType);
				}

				// If the group is not found at all search for the last group 
				if (iInsertIndex === -1) {
					iInsertIndex = arrGroupKeys.indexOf(DMRNode.Groups.Complete);
				} else {
					// Get the next index
					// iInsertIndex += 1;
				}

				// // Create a new group with the provided details. 
				let oNewGroup = new sap.suite.ui.commons.networkgraph.Group({
					key: sGroupKey,
					title: sGroupTitle,
					"parentGroupKey": (oController._groupHierarchySupported(oController) === true ? sParentGroupKey : undefined)
				});

				// Add a new group with this name 
				oGraph.insertGroup(oNewGroup, iInsertIndex);

			},

			/**
			 * Generate a node or group id based on the request parameters. 
			 *		Retrieve all the nodes / groups filtered by the specified filter criteria and and sort by their key. 
			 *		Parse the last key to retrieve the numeric part -> Increment -> Prepend with the search criteria text and return 
			 * 
			 *	oGraph: The graph handle 
			 *	sElementType: Node or Group
			 *	sPrefix: The text to be used to filter the graph elements. This is also expected to be the text prefixed to the key. 
			 *		The digits are appended to this prefix before being returned. 
			 * 
			 * All the parameters are Mandatory. 
			 */
			_getKeyNameforGraphElement: function (oGraph, sElementType, sPrefix) {
				// Based on the element type requested retrieve the list of graph elements 
				let arrGraphElements = undefined;

				if (sElementType === "Node") {
					arrGraphElements = oGraph.getNodes();
				} else if (sElementType === "Group") {
					arrGraphElements = oGraph.getGroups();
				}
				/** Do not filter the nodes with the prefix. For the Nodes filter by regex to start with an uppercase char. 
				 * The nodes all have an upper case Char as the first digit and this needs to be incremented once the second digit exceeds 9 
				 */
				if (sElementType !== "Node") {
					arrGraphElements = arrGraphElements.filter(function (element) {
						return element.getKey().startsWith(sPrefix);
					});
				} else {
					arrGraphElements = arrGraphElements.filter(function (element) {
						return element.getKey().match("[A-Z][0-9]"); // Filter by elements with an upper case at index zero and digit at index 1. 2 chars only
					});
				}

				// Sort the elements by the key 
				let iPrefixLength = sPrefix.length;
				arrGraphElements = arrGraphElements.sort(function (elementA, elementB) {
					// let iACount = parseInt(elementA.getKey().substr(iPrefixLength), 10);
					// let iBCount = parseInt(elementB.getKey().substr(iPrefixLength), 10);
					let iACount = elementA.getKey();
					let iBCount = elementB.getKey();
					if(iACount > iBCount){
						return 1;
					}
					if(iACount < iBCount)
					{
						return -1;
					}
					return 0;
				});

				// Retrieve the next character prefix in the case of Node. Get the first char from the last node
				if (arrGraphElements.length > 0 && sElementType === "Node") {
					sPrefix = arrGraphElements[arrGraphElements.length - 1].getKey().substr(0, 1);
				}

				let iNextDigit = 0;
				if (arrGraphElements.length > 0) {
					iNextDigit = parseInt(arrGraphElements[arrGraphElements.length - 1].getKey().substr(iPrefixLength), 10) + 1;
				}

				// This happens for the group id. THe first group is 02proc with nothing at the end. start with zero in that case.
				if (Number.isNaN(iNextDigit)) {
					iNextDigit = 0;
				}

				// In the case of Node, limit the keys to 2 digits if the next digit is greater than 9, reset to 0 and increment the prefix.
				if ((sElementType === "Node") && (iNextDigit > 9)) {
					sPrefix = String.fromCharCode(sPrefix.charCodeAt() + 1);
					iNextDigit = 0;
				}

				return sPrefix + iNextDigit;
			},

			/**
			 * Create a node based on the parameters provided. 
			 * The first four parameters are mandatory 
			 *  oController : The handle to the current controller 
			 *  sGroupID : The group to which the node is to be added 
			 *  sNodeShape : Use the default node types provided by the platform (Box or Circle)
			 *	sNodeKeyID : Unique key generated for this node 
			 * 
			 * The last 2 parameters are options and can change the behavior of the node 
			 *  sNodeIcon: THe icon to be assigned to the node 
			 *  	 > If the node type is circle and if the icon is sap-icon://split, create a circlural node of type PARALLEL. 
			 *			This will be the PARENT NODE with a unique group id. any child created in the down stream of this node shall
			 *			be added to the name group
			 *   sNodeTitle : The title to be shown for the node. Has no special impact on the behavior 
			 */
			_createNodeForGraph: function (oController, sGroupID, sNodeShape, sNodeKeyID, sNodeIcon, sNodeTitle) {
				let sNodeKey = sNodeKeyID;
				sNodeTitle = sNodeTitle ? sNodeTitle : sNodeKey;
				sNodeIcon = sNodeIcon ? sNodeIcon : DMRNode.Icons.Normal;

				// Add a new node and attach to this node
				let oNodeSettings = {
					"key": sNodeKey,
					"title": sNodeTitle,
					"icon": sNodeIcon,
					"status": "Success",
					"shape": sNodeShape,
					"group": sGroupID,
					"width": 200,
					"showActionLinksButton": false,
					"showDetailButton": true,
					"showExpandButton": false
				};

				// If the node icon is sap-icon://split, set the height of the node to 100
				if (sNodeIcon === DMRNode.Icons.Parallel || sNodeIcon === DMRNode.Icons.Dyamic || sNodeIcon === DMRNode.Icons.Merge) {
					oNodeSettings.height = 100;
					oNodeSettings.iconSize = 60;
				}

				// Create a new node 
				let oNode = new Node(oNodeSettings);
				// oNode.setContent(oSimpleForm);
				oNode.setShowActionLinksButton(false);
				oNode.setShowDetailButton(false);

				/**
				 * The nodes have the following buttons based on their type
				 * 
				 *	> Add Button
				 *		~ Add Approver 
				 *		~ Add Parallel 
				 *		~ Add Decision Table
				 *  [ Node A0, Generic Node, Parallel Child, Parallel Node ]
				 *	
				 *	> Edit Connection
				 *	[Node A0, Generic Node, Parallel Child ]
				 * 
				 *	> Node Action
				 *		~ Edit Node
				 *		~ Delete Node
				 *	[Generic Node, Parallel Node, Parallel Child]
				 *	
				 */

				/**
				 * If the node is of type 
				 *		Normal, Init, Parallel
				 * Then 
				 *		attach the Add Button (allow Add Approver, add Paraller and add decision table)
				 */
				let iNodeType = oController._getNodeType(oController, oNode);

				if (iNodeType === DMRNode.Types.Init ||
					iNodeType === DMRNode.Types.Normal ||
					iNodeType === DMRNode.Types.Activate && oController.getView().getModel("viewWorkflow").getProperty("/selectedChangeRequest/UsmdSingleObj") ||
					iNodeType === DMRNode.Types.SystemMethod ||
					iNodeType === DMRNode.Types.Parallel ||
					iNodeType === DMRNode.Types.ParallelChild ||
					iNodeType === DMRNode.Types.Merge ||
					iNodeType === DMRNode.Types.Dynamic // TASK 7312 [Details of change added to nodeAddButtonHandler]
				) {
					oNode
						.addActionButton(
							(new sap.suite.ui.commons.networkgraph.ActionButton({
								icon: "sap-icon://add",
								title: "Add"
							})).attachPress(oController, oController.nodeAddButtonHandler, oNode)
						);
				}

				/**
				 * If the node is of type 
				 *		Init, Normal, ParallelChild, Activate
				 * Then 
				 *		attach the Node Edit Button 
				 */
				if (iNodeType === DMRNode.Types.Init ||
					iNodeType === DMRNode.Types.Normal ||
					iNodeType === DMRNode.Types.SystemMethod ||
					iNodeType === DMRNode.Types.Parallel ||
					iNodeType === DMRNode.Types.ParallelChild ||
					iNodeType === DMRNode.Types.Merge ||
					iNodeType === DMRNode.Types.Activate ||
					iNodeType === DMRNode.Types.Dynamic) {
					oNode
						.addActionButton(
							(
								new sap.suite.ui.commons.networkgraph.ActionButton({
									icon: "sap-icon://add-process",
									title: "Edit Connection"
								})
							).attachPress(oController, oController.nodeEditConnectionHandler, oNode)
						);
				}

				/**
				 * If the node is of type 
				 *		Normal, Parallel, ParallelChild
				 * Then 
				 *		attach the Setting Button (allow edit, delete)
				 */
				if (iNodeType === DMRNode.Types.Parallel || 
					iNodeType === DMRNode.Types.Normal ||
					iNodeType === DMRNode.Types.SystemMethod ||
					iNodeType === DMRNode.Types.FollowUpCr || 
					iNodeType === DMRNode.Types.ParallelChild ||
					iNodeType === DMRNode.Types.Dynamic) {
					oNode
						.addActionButton(
							(
								new sap.suite.ui.commons.networkgraph.ActionButton({
									icon: "sap-icon://action-settings",
									title: "Edit or Delete Node",
									position: sap.suite.ui.commons.networkgraph.ActionButtonPosition.Left
								})
							).attachPress(oController, oController.nodeSettingButtonHandler, oNode)
						);
				}

				return oNode;
			},

			/**
			 * Task 12336 - WF Graph layout change
			 * Add button on toolbar to make the graph as full screen
			 */
			fullScreenGraph: function(oEvent, oController) {
				let oTransportPromise = oController._setGraphToFullScreen(oController);
				oTransportPromise.then(function () {
					//Nothing to do
				});
			},

			/**
			 * Open the settings menu to allow 
			 *		Edit Approver
			 *		Delete Approver
			 *	buttons
			 * 
			 * Runs is node context. this = node
			 */
			nodeSettingButtonHandler: function (oEvent, oController) {
				let oNode = this;
				let iNodeType = oController._getNodeType(oController, oNode);

				let oActionSheet = new sap.m.ActionSheet({
					showCancelButton: false,
					title: "Approver"
				});

				/**
				 * If node type is 
				 *		Parallel, Normal, ParallelChild, Dynamic
				 * 
				 * Then 
				 *		attach Edit Approver Button
				 *		attach Delete Approver Button
				 */
				if (iNodeType === DMRNode.Types.Parallel ||
					iNodeType === DMRNode.Types.Normal ||
					iNodeType === DMRNode.Types.SystemMethod ||
					iNodeType === DMRNode.Types.FollowUpCr ||
					iNodeType === DMRNode.Types.ParallelChild ||
					iNodeType === DMRNode.Types.Dynamic
				) {
					/* The edit handler for Dynamic node will need to open the table edit component for usage */
					oActionSheet.addButton(
						(new sap.m.Button({
							icon: "sap-icon://user-edit",
							text: "Edit",
							type: sap.m.ButtonType.Accept
						})).attachPress(
							oController,
							(iNodeType === DMRNode.Types.Dynamic || iNodeType === DMRNode.Types.Parallel) ?
							oController.nodeDynamicEditHandler : oController.nodeEditHandler, oNode)
					);

					oActionSheet.addButton(
						(new sap.m.Button({
							icon: "sap-icon://employee-rejections",
							text: "Delete",
							type: sap.m.ButtonType.Reject
						})).attachPress(oController, oController.nodeRemoveHandler, oNode)
					);
				}

				oActionSheet.openBy(oEvent.getSource());
			},

			/**
			 * Open the add menu to allow 
			 *		Add Approver 
			 *		Add System Method
			 *		Add Parallel 
			 *		Add Dynamic Table
			 *	buttons
			 * 
			 * 
			 * Add Normal Approver only for Parallel Node and Parallel Child Node
			 * 
			 * Runs is node context. this = node
			 */
			nodeAddButtonHandler: function (oEvent, oController) {
				let oNode = this;
				let iNodeType = oController._getNodeType(oController, oNode); 
				// let sNodeKey = oNode.getKey();

				// Get the graph object 
				// let oGraph = oController.getView().byId("graph");

				let oActionSheet = new sap.m.ActionSheet({
					showCancelButton: false,
					title: "Add"
				});
				
				/** Task 12353 - Follow Up CR
				 * On click on + button on Activate Node, add the button for Follow Up CR
				 */
				if(iNodeType === DMRNode.Types.Activate) {
					oActionSheet.addButton(
						(new sap.m.Button({
							icon: DMRNode.Icons.FollowUpCr,
							text: "Follow Up CR"
						})).attachPress(oController, oController.nodeAddActionHandler, oNode)
					);
					oActionSheet.openBy(oEvent.getSource());
					return;
				} else {
					oActionSheet.addButton(
						(new sap.m.Button({
							icon: "sap-icon://add-employee",
							text: "Approver"
						})).attachPress(oController, oController.nodeAddActionHandler, oNode)
					);
				}
				
				
				if(iNodeType !== DMRNode.Types.Parallel && iNodeType !== DMRNode.Types.ParallelChild) {
					oActionSheet.addButton(
						(new sap.m.Button({
							icon: DMRNode.Icons.SystemMethod,
							text: "System Method"
						})).attachPress(oController, oController.nodeAddActionHandler, oNode)
					);
				
					oActionSheet.addButton(
						(new sap.m.Button({
							icon: DMRNode.Icons.Parallel,
							text: "Parallel Flow",
							visible: true
						})).attachPress(oController, oController.nodeAddActionHandler, oNode)
					);

					oActionSheet.addButton(
						(new sap.m.Button({
							icon: DMRNode.Icons.Dyamic,
							text: "Dynamic Rules"
						})).attachPress(oController, oController.nodeAddActionHandler, oNode)
					);
				}
				
				// If no buttons are added, show a message stating that no actions are possible now 
				if (oActionSheet.getButtons().length === 0) {
					oActionSheet.addButton(
						(new sap.m.Button({
							icon: "sap-icon://decline",
							text: "No actions available",
							enabled: false
						}))
					);
				}

				oActionSheet.openBy(oEvent.getSource());
			},

			/**
			 * Delete the node from the graph. 
			 */
			nodeRemoveHandler: function (oDeleteButton, oController) {

				let thisNode = this;

				let oTransportPromise = oController._setGraphToFullScreen(oController);
				oTransportPromise.then(function () {
					let oPromiseDelete = new Promise(function (resolve) {

						let promiseShowPopupAlert = Utilities.showPopupAlert("Delete approver? This action cannot be undone.", MessageBox.Icon.WARNING,
							"Approver Delete", [
								MessageBox.Action.YES, MessageBox.Action.NO
							]);
						promiseShowPopupAlert.then(function () {
							resolve(true);
						}, function () {
						});
					});

					oPromiseDelete.then(function () {
						// Get the graph object 
						let oGraph = oController.getView().byId("graph");
						//Get the Nodes in the graph
						let oGraphNodes = oGraph.getNodes();
						// Get the lines attached to and from this node
						let oGraphLines = oGraph.getLines();
						
						/**
						 * If node to be deleted is a parallel node
						 *		Fetch all the nodes (merge and Parallel child) under selected node's parallel group
						 *		Remove to and from lines for each node
						 *		Remove merge node and parallel child nodes
						 *		Remove selected parallel Node
						 * 
						 * Else
						 *		Remove to and from lines for selected node
						 *		Remove selected node
						 */
						if(thisNode.getIcon() === DMRNode.Icons.Parallel) {
							
							let sGroupName = thisNode.getGroup();
							let sParallelNodeGroup = sGroupName.substr(0, sGroupName.lastIndexOf("_"));
							oGraphNodes.forEach(oNode => {
								if(oNode.getGroup().startsWith(sParallelNodeGroup)) {
									oGraphLines.forEach(function (el) {
										if (el.getFrom() === oNode.getKey() || el.getTo() === oNode.getKey()) {
											oGraph.removeLine(el);
											el.destroy();
										}
									});
									
									// Remove the node 
									oGraph.removeNode(oNode);
									oNode.destroy();
									
								}
							});
						} else if(thisNode.getIcon() === DMRNode.Icons.FollowUpCr) {
							oController.getApproverModel().setProperty("/followUpCr", undefined);
						}
						
						oGraphLines.forEach(function (el) {
							if (el.getFrom() === thisNode.getKey() || el.getTo() === thisNode.getKey()) {
								oGraph.removeLine(el);
								el.destroy();
							}
						});

						// Remove the node 
						oGraph.removeNode(thisNode);
						thisNode.destroy();
					});
				});
			},

			// Get apporver details from the service. Temporarily always returns false until the service ready
			_getApproverDetails: function (oController, sCRName) {

				let sUrlParameters = {
					"$filter": "CrType eq '" + sCRName + "'",
					"$format": "json",
					"$expand": "wfToPrstepNav,FOLLOWUPCRNAV,BADIIMPL"
				};

				let promise = new Promise(function (resolve, reject) {
					// Call the workflow service and retrieve the data for the graph
					(new DMRDataService(
						oController,
						"WORKFLOW",
						"/WFDETAILSSet",
						"CreateWF",
						"/", // Root of the received data
						"Create WF Decision Table"
					))
					//.setToastMessage("Workflow data received. Rendering workflow...")
					.getData({
							success: {
								fCallback: function (oParams, oData) {

									if (oData.results.length <= 0) {
										reject(null);
										return;
									}

									//Fetch the TRs which needs to be selected at all times for CR Type if any
									oController.getApproverModel()
										.setProperty("/CRTypeDetails/WorkbenchTr", oData.results[0].Workbenchtr);

									oController.getApproverModel()
										.setProperty("/CRTypeDetails/CustomizingTr", oData.results[0].Custtr);

									// Check to see if the data returned has DynamicWorkflow Information
									let arrWorkflowData = oData.results;

									// If the Dynamic Workflow Results are available, parse the content and use the same for 
									if (oData.results[0].DynWfExpr) {
										/**
										 * Copy the data after updating the naming conventions. The target format is as below (same as when not using Dynamic Data)
										 *	{
										 *		approverType -> ApproverType
										 *		approverValue -> ApproverVal
										 *		crStep -> CrStep
										 *		crType -> CrType
										 *		crStepName -> StepType
										 *		prStepActset -> wfToPrstepNav : {
										 *			crType -> CrType
										 *			prevAction -> PrevAction
										 *			prevStep -> PrevStep
										 *			stepStatus -> StepStatus
										 *		}
										 *		drivingentityset -> DRIVINGENTITYSet : {
										 *			colNum -> ColNum
										 *			usmdAttribute -> UsmdAttribute
										 *			usmdEntity -> UsmdEntity
										 *		}
										 *		derivingentityset -> DERIVINGENTITYSet : {
										 *			colName -> ColName
										 *			colNum -> colNum
										 *		}
										 *		dtvaluesset -> DTVALUESSet : {
										 *			colName -> ColName
										 *			colNum -> ColNum
										 *			colValue -> ColValue
										 *			rowNum -> RowNum
										 *		}
										 *	}
										 */
										let oParsedDynData = JSON.parse(oData.results[0].DynWfExpr);
										oController.getApproverModel()
										.setProperty("/CRTypeDetails/package", oParsedDynData.package);

										oController.getApproverModel()
										.setProperty("/badiimpl", oParsedDynData.badiimpl);
										let arrApprovalSets = oParsedDynData.approvalset;
										if (!Array.isArray(arrApprovalSets)) {
											// Should never reach this point ... but add the check anyways ... 
											reject(null);
										}

										arrWorkflowData = [];
										let oApproverSet = new Set();
										arrApprovalSets.forEach(function (approvalSet) {
											let oTempApprovalSet = {
												ApproverType: approvalSet.approverType,
												ApproverVal: approvalSet.approverValue,
												Apprdesc: approvalSet.apprdesc,
												CrStep: approvalSet.crStep,
												CrType: approvalSet.crType,
												Changedoc: approvalSet.changedoc ? true : false,
												StepType: approvalSet.stepType,
												Mergestep: approvalSet.mergestep,
												wfToPrstepNav: {
													results: []
												},
												Servicename: approvalSet.servicename
											};
											/** Task 12353 - Follow Up CR
											 * Add follow up cr details to the model (To be changed for multi CR types)
											 */
											if(approvalSet.followupcrnav && approvalSet.followupcrnav.length > 0) {
												let arrCrType = [];
												for(let i = 0; i < approvalSet.followupcrnav.length; i++){
													arrCrType.push({CrType: approvalSet.followupcrnav[i].crType});
												}
												oTempApprovalSet.FOLLOWUPCRNAV = {results: arrCrType};
											}
											
											//Add Cr Step to Set
											if(oTempApprovalSet.CrStep) {
												oApproverSet.add(oTempApprovalSet.CrStep);
											}

											// If step information is available map the information
											if (Array.isArray(approvalSet.prstepactset)) {
												approvalSet.prstepactset.forEach(function (stepAction) {
													let oPrevStepData = {
														CrType: stepAction.crType,
														PrevAction: stepAction.prevAction,
														PrevStep: stepAction.prevStep,
														StepStatus: stepAction.stepStatus,
														UsmdPriority: stepAction.usmdPriority,
														UsmdReason: stepAction.usmdReason,
														UsmdReasonRej: stepAction.rejectReason,
														Sign: stepAction.sign,
														AppStep: stepAction.appStep,
														Apprdesc: stepAction.apprdesc,
														AddlEmails: stepAction.addlEmails,
														Emailtemp: stepAction.emailtemp
													};
													oTempApprovalSet.wfToPrstepNav.results.push(oPrevStepData);
												});
											}
											/**
											* Parallel child information to be added under this Navigation for creating lines.
											* In save we removed these entries from PR_STEPACt_set navigation. We need to re add them here
											*/
											if (Array.isArray(approvalSet.parallelapprovalset)) {
												approvalSet.parallelapprovalset.forEach(function (stepAction) {
													if(stepAction.crStep === approvalSet.crStep){
														let oPrevStepData = {
															CrType: stepAction.crType,
															ApproverType: stepAction.approverType,
															ApproverVal: stepAction.approverValue,
															Apprdesc: stepAction.apprdesc,
															PrevAction: stepAction.paralleltoprstep[0].prevAction,
															PrevStep: stepAction.paralleltoprstep[0].prevStep,
															StepStatus: stepAction.paralleltoprstep[0].stepStatus,
															UsmdPriority: stepAction.paralleltoprstep[0].usmdPriority,
															UsmdReason: stepAction.paralleltoprstep[0].usmdReason,
															UsmdReasonRej: stepAction.paralleltoprstep[0].rejectReason,
															Sign: stepAction.paralleltoprstep[0].sign,
															AppStep: stepAction.appstep,
															AddlEmails: stepAction.paralleltoprstep[0].addlEmails,
															Emailtemp: stepAction.paralleltoprstep[0].emailtemp
														};
														oTempApprovalSet.wfToPrstepNav.results.push(oPrevStepData);
													}
												});
											}

											// Check for Dynamic or parallel and read the data if either is available 
											let oDrivingEntitySetData;
											if (Array.isArray(approvalSet.drivingentityset) && approvalSet.drivingentityset.length > 0) {
												oDrivingEntitySetData = approvalSet.drivingentityset;
												oTempApprovalSet.Dynamic = true;
											} else if (Array.isArray(approvalSet.paralleldrivingset) && approvalSet.paralleldrivingset.length > 0) {
												oDrivingEntitySetData = approvalSet.paralleldrivingset;
												oTempApprovalSet.Parallel = true;
											}

											// If driving entity information is available 
											if (Array.isArray(oDrivingEntitySetData) && oDrivingEntitySetData.length > 0) {
												oTempApprovalSet.DRIVINGENTITYSet = [];
												oDrivingEntitySetData.forEach(function (drivingEntity) {
													oTempApprovalSet.DRIVINGENTITYSet.push({
														ColNum: drivingEntity.colNum,
														UsmdAttribute: drivingEntity.usmdAttribute,
														UsmdEntity: drivingEntity.usmdEntity,
														Attrdatatype: drivingEntity.attrdatatype
													});
												});
											}

											let oDerivingEntitySetData;
											if (Array.isArray(approvalSet.derivingentityset) && approvalSet.derivingentityset.length > 0) {
												oDerivingEntitySetData = approvalSet.derivingentityset;
											} else if (Array.isArray(approvalSet.parallelderivingset) && approvalSet.parallelderivingset.length > 0) {
												oDerivingEntitySetData = approvalSet.parallelderivingset;
											}
											// If deriving entity information is available 
											if (Array.isArray(oDerivingEntitySetData) && oDerivingEntitySetData.length > 0) {
												oTempApprovalSet.DERIVINGENTITYSet = [];
												oDerivingEntitySetData.forEach(function (drivingEntity) {
													oTempApprovalSet.DERIVINGENTITYSet.push({
														ColName: drivingEntity.colName,
														ColNum: drivingEntity.colNum
													});
												});
											}
											
											/**
											 * Fetch the PARALLELAPPROVALSET and its connections navigation in the required format
											 */
											let oParallelApprovalSetData;
											if(Array.isArray(approvalSet.parallelapprovalset) && approvalSet.parallelapprovalset.length > 0) {
												oParallelApprovalSetData = approvalSet.parallelapprovalset;
											}
											// If deriving entity information is available 
											if (Array.isArray(oParallelApprovalSetData) && oParallelApprovalSetData.length > 0) {
												oTempApprovalSet.PARALLELAPPROVALSet = [];
												oParallelApprovalSetData.forEach(oParallelApprover => {
													let oTempParallelApprovalSet = {
														ApproverType: oParallelApprover.approverType,
														ApproverVal: oParallelApprover.approverValue,
														Apprdesc: oParallelApprover.apprdesc,
														CrStep: oParallelApprover.crStep === DMRNode.Types.Complete.toString() ? approvalSet.mergestep : oParallelApprover.crStep,
														CrType: oParallelApprover.crType,
														StepType: oParallelApprover.stepType,
														Row: oParallelApprover.row,
														AppStep: oParallelApprover.appstep,
														ParallelWf: oParallelApprover.parallelwf,
														ProcessPattern: oParallelApprover.processpattern,
														wfToPrstepNav: {
															results: []
														},
														Servicename: oParallelApprover.servicename
													};
													
													//Add Cr Step to Set
													if(oTempParallelApprovalSet.CrStep) {
														oApproverSet.add(oTempParallelApprovalSet.CrStep);
													}
													
													if (Array.isArray(oParallelApprover.paralleltoprstep)) {
														oParallelApprover.paralleltoprstep.forEach(function (stepAction) {
															let oParallelPrStepDataData = {
																CrType: stepAction.crType,
																PrevAction: stepAction.prevAction,
																PrevStep: stepAction.prevStep,
																StepStatus: stepAction.stepStatus,
																UsmdPriority: stepAction.usmdPriority,
																UsmdReason: stepAction.usmdReason,
																UsmdReasonRej: stepAction.rejectReason,
																Sign: stepAction.sign,
																Row: stepAction.row,
																AppStep: stepAction.appStep,
																Apprdesc: stepAction.apprdesc,
																AddlEmails: stepAction.addlEmails,
																Emailtemp: stepAction.emailtemp
															};
															oTempParallelApprovalSet.wfToPrstepNav.results.push(oParallelPrStepDataData);
														});
													}
													oTempApprovalSet.PARALLELAPPROVALSet.push(oTempParallelApprovalSet);
												});
											}

											let oDTValeuSetData;
											if (Array.isArray(approvalSet.dtvaluesset) && approvalSet.dtvaluesset.length > 0) {
												oDTValeuSetData = approvalSet.dtvaluesset;
											} else if (Array.isArray(approvalSet.paralleldtvaluesset) && approvalSet.paralleldtvaluesset.length > 0) {
												oDTValeuSetData = approvalSet.paralleldtvaluesset;
											}
											// If data values entity information is available 
											if (Array.isArray(oDTValeuSetData) && oDTValeuSetData.length > 0) {
												oTempApprovalSet.DTVALUESSet = [];
												oTempApprovalSet.DTACTIONS = [];
												//Feature/12334 - Operator added to DTVALUESSet to be retrieved after save it
												oDTValeuSetData.forEach(function (dataValue) {
													oTempApprovalSet.DTVALUESSet.push({
														ColName: dataValue.colName,
														ColNum: dataValue.colNum,
														ColValue: dataValue.colValue,
														ColValueDescr: dataValue.colValuedescr,
														RowNum: dataValue.rowNum,
														//Bug 12478 - Read back DefaultStep value from payload
														Defaultstep: dataValue.defaultstep,
														Operator: dataValue.operator
													});
												});

												// Filter the dtvalueset by StepActions column and sort by row num 
												let arrTempDtValuesSet = oTempApprovalSet.DTVALUESSet.filter(function (oDtValue) {
													if (oDtValue.ColName === "StepActions") {
														return true;
													}
													return false;
												});

												// Sort the values by Row Number
												arrTempDtValuesSet = arrTempDtValuesSet.sort(function (a, b) {
													return parseInt(a.RowNum, 10) - parseInt(b.RowNum, 10);
												});

												// Copy the actions to the DTACTIONS array 
												arrTempDtValuesSet.forEach(function (oStepActionValue) {
													oTempApprovalSet.DTACTIONS.push({
														action: oStepActionValue.ColValue,
														RowNum: oStepActionValue.RowNum
													});
												});
											}

											arrWorkflowData.push(oTempApprovalSet);
										});
										//Fetch all distinct approver node keys, convert it to Array and add it to model
										
										oController.getApproverModel()
											.setProperty("/approversList", Array.from(oApproverSet));
										
									}else{
										let aBadiList = arrWorkflowData[0].BADIIMPL.results?.map(oBadi => ({
											badiKey: oBadi.BadiKey,
											value: oBadi.Value
	
										}));
	
										oController.getApproverModel()
											.setProperty("/badiimpl", aBadiList);
									}

									arrWorkflowData.forEach(function(oApprovalSet){
										if(oApprovalSet.PARALLELAPPROVALSet){
											oApprovalSet.PARALLELAPPROVALSet.forEach(function(oStep, index){
												if (oApprovalSet.CrStep === oStep.CrStep){
													oApprovalSet.PARALLELAPPROVALSet.splice(index, 1);
												}
											});
										}
									});
									oParams.controller._transformWorkFlowData(oParams.controller, arrWorkflowData);
									resolve(true);
								},
								oParam: {
									"controller": oController
								}
							},
							error: {
								fCallback: function () {
									reject(null);
								},
								oParam: oController
							}
						},
						sUrlParameters);
				});

				return promise;
			},
			_transformWorkFlowData: function (oController, oWorkflowData) {
				let oGraph = oController.getView().byId("graph");
				let bHierarchySupported = oController._groupHierarchySupported(oController);
				let oSTypeArr = oController.getApproverModel().getProperty("/stepType/results");
				// let oSActArr = oController.getApproverModel().getProperty("/actionType/results");

				let bInitNode = false;
				let sEndNode = sap.suite.ui.commons.networkgraph.NodeShape.Circle;

				// Set the group and layout information
				oGraph.setLayoutAlgorithm(new GraphLayout());
				oGraph.setOrientation(
					oController._groupHierarchySupported(oController) ?
					sap.suite.ui.commons.networkgraph.Orientation.LeftRight :
					sap.suite.ui.commons.networkgraph.Orientation.TopBottom);
				// oGraph.getToolbar().setVisible(false);
				let oToolbar = oGraph.getToolbar();
				oGraph.getToolbar().setVisible(true);
				let arrContent = oToolbar.getContent();
				arrContent.forEach(function (control) {
					if (control.getId() !== "saveButton" && control.getId() !== "resetButton" && control.getId() !== "activateButton" && control.getId() !== "fullScreenButton" &&
						control.getId() !== "title" && control.getId() !== "graphCheckButton" && control.getId() !== "draftButton" && control.getId() !== "draftLabel" && 
						control.getId() !== "btnShowLegend") {
						control.setVisible(false);
					}
				});

				// Create the nodes 
				for (let i = 0; i < oWorkflowData.length; i++) {
					let oNode;

					switch (oWorkflowData[i].CrStep) {
					case "91":
						oNode = oController._createNodeForGraph(
							oController,
							DMRNode.Groups.Activate,
							sEndNode, oWorkflowData[i].CrStep,
							DMRNode.Icons.Activate,
							"91: Activation");
						//Task 12814 - Add step status 33 for 91
						oController._addAttributeToNode(oController, oNode, "Step Actions", "31,32,33");
						oGraph.addNode(oNode);
						break;
					case "92":
						oNode = oController._createNodeForGraph(
							oController,
							bHierarchySupported ? DMRNode.Groups.Discard : DMRNode.Groups.Activate,
							sEndNode, oWorkflowData[i].CrStep,
							DMRNode.Icons.Discard,
							"92: Withdraw/Discard");
						oGraph.addNode(oNode);
						break;
					case "99":
						oNode = oController._createNodeForGraph(
							oController,
							bHierarchySupported ? DMRNode.Groups.Complete : DMRNode.Groups.Activate,
							sEndNode,
							oWorkflowData[i].CrStep,
							DMRNode.Icons.Complete,
							"99: Complete");
						oGraph.addNode(oNode);
						break;
					case "A0":
						// Add a node with A0 as the key 
						bInitNode = true;
						oNode = oController._createNodeForGraph(
							oController,
							DMRNode.Groups.Init,
							sEndNode, "A0",
							DMRNode.Icons.Init,
							"A0: Requestor");
						oController._addAttributeToNode(oController, oNode, "Step Actions", "Init,07,08");
						oGraph.addNode(oNode);
						break;
					default:

						let sNodeIcon = DMRNode.Icons.Normal;
						/**
						 * Check if it is a system method node and update node icon accordingly
						 */
						if(oWorkflowData[i].FOLLOWUPCRNAV && oWorkflowData[i].FOLLOWUPCRNAV.results && oWorkflowData[i].FOLLOWUPCRNAV.results.length > 0) {
							sNodeIcon = DMRNode.Icons.FollowUpCr;
						}
						else if (oWorkflowData[i].Servicename) {
							sNodeIcon = DMRNode.Icons.SystemMethod;
						}
						/**
						 * Check the node type and update the node-icon to be used. Only need to check if the Driving Entity set is valid. 
						 * All others should be available if Driving Entity is available. Data could be there or not based on if the data was setup
						 */
						if (Array.isArray(oWorkflowData[i].DRIVINGENTITYSet) && oWorkflowData[i].DRIVINGENTITYSet.length > 0) {
							sNodeIcon = (oWorkflowData[i].Parallel !== true) ? DMRNode.Icons.Dyamic : DMRNode.Icons.Parallel;
						}

						// Create the node for the graph 
						oNode = oController.createNodeForGraphWrapper(oController, undefined, sNodeIcon, oWorkflowData[i].CrStep);

						//Add Service name Attribute to the node if it is a system method node
						if (sNodeIcon === DMRNode.Icons.FollowUpCr) {
							//Currently takes only 1 CR Type. To be changed to multiple cr types when needed
							oController._addAttributeToNode(oController, oNode, "Change Request Type", oWorkflowData[i].FOLLOWUPCRNAV.results.map(oCrType => oCrType.CrType).join(", "));
							oController.getApproverModel().setProperty("/followUpCr", oWorkflowData[i].FOLLOWUPCRNAV.results.map(oCrType => oCrType.CrType));
							if(oWorkflowData[i].Servicename) {
								oController._addAttributeToNode(oController, oNode, "Service Name", oWorkflowData[i].Servicename);
							}
						} else if(sNodeIcon === DMRNode.Icons.SystemMethod) {
							oController._addAttributeToNode(oController, oNode, "Service Name", oWorkflowData[i].Servicename);
						} else {
							// Add them back
							oController._addAttributeToNode(oController, oNode, "Approver Type", oWorkflowData[i].ApproverType);
							oController._addAttributeToNode(oController, oNode, "Approver", oWorkflowData[i].ApproverVal);
							oController._addAttributeToNode(oController, oNode, "Step Type", oWorkflowData[i].StepType);
						}
						let sNewTitle = oWorkflowData[i].Apprdesc ? oNode.getKey() + ": " + oWorkflowData[i].Apprdesc : oNode.getKey();
						oNode.setTitle(sNewTitle);

						let oStepAction = "";
						// If the node is of type Dynamic
						if (sNodeIcon === DMRNode.Icons.Dyamic || sNodeIcon === DMRNode.Icons.Parallel) {
							// Store the Dynamic Workflow Data 
							this._addAttributeToNode(this, oNode, "DynamicRuleList", oWorkflowData[i]);
							// oNode.setTitle(oNode.getKey());

							// Store the Actions 
							oController._addAttributeToNode(oController, oNode, "Step Actions", oWorkflowData[i].DTACTIONS);

						} else {
							if (!oWorkflowData[i].Servicename) {
								// ADd the step type node Description [visible user friendly]
								let oSelItem = oSTypeArr.find(function (arrD) {
									return (
										arrD.UsmdCrStype === oWorkflowData[i].StepType
									);
								});

								if (oSelItem && oSelItem.STEPTYPETOACTION.results) {
									for (let j in oSelItem.STEPTYPETOACTION.results) {
										// Append the step actions 
										oStepAction =
											oStepAction.length > 0 ?
											oStepAction + "," + oSelItem.STEPTYPETOACTION.results[j].UsmdCrAction :
											oSelItem.STEPTYPETOACTION.results[j].UsmdCrAction;
									}
								}
								oController._addAttributeToNode(oController, oNode, "Step Actions", oStepAction);
							} else {
								for (let l = 0; l < oWorkflowData.length; l++) {
									for (let k = 0; k < oWorkflowData[l].wfToPrstepNav.results.length; k++) {
										if (oWorkflowData[l].wfToPrstepNav.results[k].PrevStep === oWorkflowData[i].CrStep) {
											oStepAction =
												oStepAction.length > 0 ?
												oStepAction + "," + oWorkflowData[l].wfToPrstepNav.results[k].PrevAction :
												oWorkflowData[l].wfToPrstepNav.results[k].PrevAction;
											if (oStepAction === "") { //TASK 9605
												oStepAction = "**";
												oWorkflowData[l].wfToPrstepNav.results[k].PrevAction = "**";
											}
										}
									}
								}
								oController._addAttributeToNode(oController, oNode, "Step Actions", oStepAction);
							}
						}
						if (oNode) {
							oGraph.addNode(oNode);
						}
						
						/**
						 * If current node is Parallel Node
						 * Add Merge Node on same group as Parallel Node
						 * Add all parallel children with direct connection with Parallel Node to a new group
						 * Add all parallel children with no connection with Parallel Node to the same group as one of its sister nodes (Approvers within the same branch)
						 * Add all attribute details to created Node
						 * Add the created Node to the Graph
						 */
						let oMergeNode;
						if(sNodeIcon === DMRNode.Icons.Parallel) {
							if(!oWorkflowData[i].PARALLELAPPROVALSet || oWorkflowData[i].PARALLELAPPROVALSet.length === 0) {
								/**
								 * If Node is Parallel Node and it doesnt contain PARALLELAPPROVALSet object, 
								 *	it means that Workflow was created before introduction of merge node and parallel children concept.
								 * 
								 * Add a merge node to the same swimlane as parallel node with the next available node key number
								 * All connections from parallel node to next approver should be redirected through merge node
								 * 
								 * Before (Example)
								 * Parallel Node (A1) -> Activation (91)
								 * 
								 * Now
								 * Parallel Node (A1) -> Merge Node (A2) -> Activation (91)
								 */
								let sNodeKey;
								let arrApproverList = oController.getApproverModel().getProperty("/approversList");
								arrApproverList = arrApproverList.filter(oApproverNode => {
									return oApproverNode.match("[A-Z][0-9]"); // Filter by elements with an upper case at index zero and digit at index 1. 2 chars only
								});
								
								arrApproverList = arrApproverList.sort(function (elementA, elementB) {
									// let iACount = elementA;
									// let iBCount = elementB.getKey();
									if(elementA > elementB){
										return 1;
									}
									if(elementA < elementB)
									{
										return -1;
									}
									return 0;
								});
								
								if(arrApproverList.length > 0) {
									let sPrefix = arrApproverList[arrApproverList.length - 1].substr(0, 1);
									let iNextDigit = 0;
									iNextDigit = parseInt(arrApproverList[arrApproverList.length - 1].substr(-1), 10) + 1;
									
									// In the case of Node, limit the keys to 2 digits if the next digit is greater than 9, reset to 0 and increment the prefix.
									if (iNextDigit > 9) {
										sPrefix = String.fromCharCode(sPrefix.charCodeAt() + 1);
										iNextDigit = 0;
									}
									sNodeKey = sPrefix + iNextDigit;
									oMergeNode = oController.createNodeForGraphWrapper(oController, oNode, DMRNode.Icons.Merge, sNodeKey);
									 if(oMergeNode) {
										oController._addAttributeToNode(oController, oMergeNode, "Step Actions", ["03", "04"]);
										oMergeNode.setTitle(oMergeNode.getKey() + ": Merge Node for " + oNode.getKey());
										oGraph.addNode(oMergeNode);
										arrApproverList.push(sNodeKey);
										oController.getApproverModel().setProperty("/approversList", arrApproverList);
									}
								}
							} else {
								oWorkflowData[i].PARALLELAPPROVALSet.forEach((oParallelApprover) => {
									
									if(oParallelApprover.CrStep === oWorkflowData[i].Mergestep) {
										/**
										 * If CR Step Node key is the Merge Step, then create a node with icon Merge and source as Parallel Node
										 */
										 oMergeNode = oController.createNodeForGraphWrapper(oController, oNode, DMRNode.Icons.Merge, oParallelApprover.CrStep);
										 if(oMergeNode) {
											oController._addAttributeToNode(oController, oMergeNode, "Step Actions", ["03", "04"]);
											oMergeNode.setTitle(oMergeNode.getKey() + ": Merge Node for " + oNode.getKey());
											oGraph.addNode(oMergeNode);
										}
									} else {
										/** Parallel Children
										 * Check the prevStep of Child navigation. If parallel Node, then create a Node from parallel Node (sourceNode as parallel Node)
										 * If parallel Child, then fetch Node details of child node as sourceNode
										 */
										 let oParallelChildNode;
										 let bConnectionFromPrallelNode = oParallelApprover.wfToPrstepNav.results.some(oParallelChild => {
										 	if(oParallelChild.PrevStep === oWorkflowData[i].CrStep) {
												//ParallelChild Node is created under Parallel Node
												oParallelChildNode = oController.createNodeForGraphWrapper(oController, oNode, DMRNode.Icons.ParallelChild, oParallelApprover.CrStep);
												return true;
											}
											return false;
										});
										 
										if(!bConnectionFromPrallelNode) {
											/**
											 * Current Parallel Child does not have a connection from Parallel Node.
											 * Iterate through the prev steps of Parallel Child to find its sister parallel child node and check whether prev step Node is created
											 */
											oParallelApprover.wfToPrstepNav.results.some(oParallelChild => {
												if(oParallelChild.PrevStep !== oWorkflowData[i].CrStep) {
													let oGraphNodes = oGraph.getNodes();
													let oSisterNode = oGraphNodes.find(oChildNode => {
														return oChildNode.getKey() === oParallelChild.PrevStep;	
													});
													if(oSisterNode) {
														oParallelChildNode = oController.createNodeForGraphWrapper(oController, oSisterNode, DMRNode.Icons.ParallelChild, oParallelApprover.CrStep);
														return true;
													} else {
														return false;
													}
												}
												return false;
											});
										 }
										 if(oParallelChildNode) {
											oStepAction = "";
											// Add Node details for parallel child
											oController._addAttributeToNode(oController, oParallelChildNode, "Approver Type", oParallelApprover.ApproverType);
											oController._addAttributeToNode(oController, oParallelChildNode, "Approver", oParallelApprover.ApproverVal);
											oController._addAttributeToNode(oController, oParallelChildNode, "Step Type", oParallelApprover.StepType);
											
											let oParallelChildStepType = oSTypeArr.find(function (oStepType) {
												return (
													oStepType.UsmdCrStype === oParallelApprover.StepType
												);
											});
			
											if (oParallelChildStepType && oParallelChildStepType.STEPTYPETOACTION.results) {
												for (let stepAction in oParallelChildStepType.STEPTYPETOACTION.results) {
													// Append the step actions 
													oStepAction =
														oStepAction.length > 0 ?
														oStepAction + "," + oParallelChildStepType.STEPTYPETOACTION.results[stepAction].UsmdCrAction :
														oParallelChildStepType.STEPTYPETOACTION.results[stepAction].UsmdCrAction;
												}
											}
											oController._addAttributeToNode(oController, oParallelChildNode, "Step Actions", oStepAction);
											sNewTitle = oParallelApprover.Apprdesc ? oParallelChildNode.getKey() + ": " + oParallelApprover.Apprdesc : oParallelChildNode.getKey();
											oParallelChildNode.setTitle(sNewTitle);
											oGraph.addNode(oParallelChildNode);
										 }
									}
								});
							}
						}
						break;
					}

				}

				if (!bInitNode) {
					let oA0Node = oController._createNodeForGraph(
						oController,
						DMRNode.Groups.Init,
						sEndNode,
						"A0",
						DMRNode.Icons.Init, "Requestor");
					oController._addAttributeToNode(oController, oA0Node, "Step Actions", "Init,07,08");
					oGraph.addNode(oA0Node);
				}
				
				/**
				 **********************************************************************************************
				 * Date: 20-05-2020
				 * As part of Multi Level Parallel Workflow, we get all connections related to Parallel Node 
				 *		in PARALLELAPPROVALSET -> wfToPrstepNav
				 * 
				 * The connections include 
				 *			1. Parallel Node to Merge Node
				 *			2. Parallel Node to Parallel Child
				 *			3. Parallel Child to Parallel Child
				 * 
				 * Hence, for Parallel Workflow, we iterate through PARALLELAPPROVALSET -> wfToPrstepNav to 
				 *		create lines for already created nodes
				 * 
				 **********************************************************************************************
				/** Re-do the loop outside to ensure that the lines are drawn only to existing nodes 
				 * Build the lines in 2 different conditions 
				 *	1. If the node of all the node (for Dynamic) types and build the lines whose 
				 *		"From" is not a Dynamic or Parallel Node. 
				 * 		 While we are doing this, also store the Dynamic nodes out to a diff array for each handling in step 2
				 * 
				 *	2. If the node type is Dynamic, create line based on the data held in the node. All the 
				 *		necessary data should be in the node
				 * 
				 *	3. If node type is Parallel Node, iterate through PARALLELAPPROVALSET -> wfToPrstepNav to create 
				 *		the connections between nodes which must exist in the Graph
				 */
				// Nodes that are Dynamic or Parallel Type
				let arrDynamicNodes = [];
				let arrParallelNodes = [];
				oWorkflowData.forEach(function (workfFlowItem) {
					// Get the node type of this node 
					let oCurrentNode = oController.getNodeWithKey(oController, workfFlowItem.CrStep);
					let iCurrentNodeType = oController._getNodeType(oController, oCurrentNode);

					// If it is of type Dynamic, add to the list to run step 2 later.
					// If it is of type Parallel, add to the list to make connections later
					if (iCurrentNodeType === DMRNode.Types.Dynamic) {
						arrDynamicNodes.push(workfFlowItem.CrStep);
					} else if(iCurrentNodeType === DMRNode.Types.Parallel) {
						arrParallelNodes.push(workfFlowItem.CrStep);
					}

					// Loop through all node and build lines based on the wfToPrStepNav. Build lines that do not 
					//		start from a parallel or dynamic node
					if (workfFlowItem.wfToPrstepNav.results && workfFlowItem.wfToPrstepNav.results.length > 0) {
						let arrPreVStepResults = workfFlowItem.wfToPrstepNav.results;
						for (let kk in arrPreVStepResults) {
							// Check if the prev step is Dynamic or Parallel. If YES, just continue to the next step
							let oFrom = arrPreVStepResults[kk].PrevStep === "00" ? "A0" : arrPreVStepResults[kk].PrevStep;
							let oPrevNodeObject = oController.getNodeWithKey(oController, oFrom);
							if (!oPrevNodeObject) {
								continue; // This node does not exist continue. Probably error in data
							}
							let iPrevNodeType = oController._getNodeType(oController, oPrevNodeObject);
							if (iPrevNodeType === DMRNode.Types.Parallel || iPrevNodeType === DMRNode.Types.Dynamic) {
								continue;
							}

							let oPreviousAction = arrPreVStepResults[kk].PrevAction === "" ? "Init" : arrPreVStepResults[kk].PrevAction;
							let sStepStatus = arrPreVStepResults[kk].StepStatus === "" ? "Standard" : arrPreVStepResults[kk].StepStatus;

							// Create a new Line .. when a new node is added the action is not defined (a line is added just as a default)
							//  RowNumber is always 1 
							oController.createNewLineForGraphNode(
								oGraph, oController, arrPreVStepResults[kk].ApproverType ? arrPreVStepResults[kk].ApproverType : workfFlowItem.ApproverType, 
								arrPreVStepResults[kk].ApproverVal ? arrPreVStepResults[kk].ApproverVal : workfFlowItem.ApproverVal,
								oFrom, workfFlowItem.CrStep, oPreviousAction, 1,
								sStepStatus, "", true,
								arrPreVStepResults[kk].UsmdPriority, arrPreVStepResults[kk].UsmdReason,
								arrPreVStepResults[kk].UsmdReasonRej, arrPreVStepResults[kk].Sign, arrPreVStepResults[kk].AppStep, arrPreVStepResults[kk].Emailtemp,
								arrPreVStepResults[kk].AddlEmails, arrPreVStepResults[kk].Apprdesc);

						}
					}
				});

				// Run through the Dynamic nodes and build lines based on the info inside them (not the wfToPrstepNav info)
				// Start filling the array of lines and add them once all the data is available. This is a multistep process with the 
				//  	data and lines expanding every step 
				let arrLinesForGraphStep = [];
				let oLineInfo = {};
				for (let iIndex in arrDynamicNodes) {
					// Get the node 
					let oCurrentNodeObject = oController.getNodeWithKey(oController, arrDynamicNodes[iIndex]);
					if (!oCurrentNodeObject) {
						continue; // This node does not exist continue. Probably error in data
					}

					// Get the node RulesInformation
					let oCurrentNodeRuleList = oController._getNodeApproverDetails(oCurrentNodeObject).DynamicRuleList;

					// Total rows in the list 
					let iTotalRows = oCurrentNodeRuleList.DTACTIONS.length;

					// Got through each row and get the data 
					for (let iRowIndex = 1; iRowIndex <= iTotalRows; iRowIndex++) {
						oLineInfo = {};
						oLineInfo.RowNum = iRowIndex;
						oLineInfo.FromNode = arrDynamicNodes[iIndex];

						let arrValueSet = oCurrentNodeRuleList.DTVALUESSet;
						for (let iValueSetIndex = 0; iValueSetIndex < arrValueSet.length; iValueSetIndex++) {
							if (arrValueSet[iValueSetIndex].RowNum !== iRowIndex.toString()) {
								continue;
							}
							// Get the data as retrieved 
							switch (arrValueSet[iValueSetIndex].ColName) {
							case "USER_TYPE":
								oLineInfo.ApproverType = arrValueSet[iValueSetIndex].ColValue;
								break;
							case "USER_VALUE":
								oLineInfo.ApproverVal = arrValueSet[iValueSetIndex].ColValue;
								break;
							case "Connections":
								// Just store all the connections as-is. Expand later
								let sConnections = arrValueSet[iValueSetIndex].ColValue;
								try {
									oLineInfo.arrConnections = JSON.parse(sConnections);
								} catch (error) {
									oLineInfo.arrConnections = [];
								}
								break;

								default: 
								// No Actions 
								break;
							}
						}

						oLineInfo.arrConnections.forEach(function (connection) {
							try {
								let oActionDetails = JSON.parse(connection);
								oLineInfo.ToNode = oActionDetails.TargetNode;
								oLineInfo.Action = oActionDetails.UsmdCrAction;
								oLineInfo.StepStatus = oActionDetails.CRStatus;
								oLineInfo.CreqObmain = oActionDetails.CR_OBMAIN;
								oLineInfo.Priority = oActionDetails.CRPriority;
								oLineInfo.Reason = oActionDetails.CRReason;
								oLineInfo.RejectReason = oActionDetails.CRRejectionReason;
								oLineInfo.Sign = oActionDetails.Sign;
								oLineInfo.AppStep = oActionDetails.AppStep;
								oLineInfo.Apprdesc = oActionDetails.AppStep ? oActionDetails.Apprdesc : undefined;
								oLineInfo.Emailtemp = oActionDetails.Emailtemp;
								oLineInfo.AddlEmails = oActionDetails.AddlEmails;

								oController.createNewLineForGraphNode(
									oGraph, oController, oLineInfo.ApproverType, oLineInfo.ApproverVal,
									oLineInfo.FromNode, oLineInfo.ToNode, oLineInfo.Action, oLineInfo.RowNum,
									oLineInfo.StepStatus, oLineInfo.CreqObmain, true,
									oLineInfo.Priority, oLineInfo.Reason, oLineInfo.RejectReason,
									oLineInfo.Sign, oLineInfo.AppStep, oLineInfo.Emailtemp, oLineInfo.AddlEmails, oLineInfo.Apprdesc);
								arrLinesForGraphStep.push(oLineInfo);
							} catch (error) {
								// Nothing to do .. ideally should not reach here.
							}
						});
					}
				}
				
				/**
				 * For any parallel Node, use the parallel sub-workflow navigation to create connections
				 * If navigation does not exist, that means the workflow was created before multi level parallel workflow was introduced
				 * If so, show a popup message to the user stating that the workflow has to be manually readjusted
				 */
				let bParallelNavExists = true;
				for(let iParallelIndex in arrParallelNodes) {
					let oParallelNodeObject = oController.getNodeWithKey(oController, arrParallelNodes[iParallelIndex]);
					if (!oParallelNodeObject) {
						continue; // This node does not exist continue. Probably error in data
					}
					
					let oParallelNodeRuleList = oController._getNodeApproverDetails(oParallelNodeObject).DynamicRuleList;
					let arrParallelApprovalSet = oParallelNodeRuleList.PARALLELAPPROVALSet;
					let arrDtValuesSet = oParallelNodeRuleList.DTVALUESSet;
					if(!arrParallelApprovalSet || arrParallelApprovalSet.length === 0) {
						/**
						 * If arrParallelApprovalSet objects does not exists or it is empty, that means the workflow was created before the update
						 * Unset the flag and display a popup message for incident to the user
						 */
						bParallelNavExists = false;
					} else {
						arrParallelApprovalSet.forEach(oParallelApprover => {
							oParallelApprover.wfToPrstepNav.results.forEach(oParallelToPrStep => {
								try{
									oLineInfo = {};
									
									oLineInfo.ApproverType = oParallelApprover.ApproverType;
									oLineInfo.ApproverVal = oParallelApprover.ApproverVal;
									oLineInfo.ToNode = oParallelApprover.CrStep;
									
									//If AppStep is provided, fetch the RowNum based on AppStep in DTValuesSet
									let oAppStepColumn;
									if(oParallelToPrStep.AppStep) {
										oAppStepColumn = arrDtValuesSet.find(oDtValue => {
											return oDtValue.ColName === "APPSTEP" && oDtValue.ColValue === oParallelToPrStep.AppStep;
										});
										if(oAppStepColumn) {
											oParallelToPrStep.RowNum = oAppStepColumn.RowNum;
										} else {
											oParallelToPrStep.RowNum = 1;
										}
									} else {
										oParallelToPrStep.RowNum = 1;
									}
									
									oLineInfo.FromNode = oParallelToPrStep.PrevStep;
									oLineInfo.Action = oParallelToPrStep.PrevAction;
									oLineInfo.RowNum = oParallelToPrStep.RowNum;
									oLineInfo.StepStatus = oParallelToPrStep.StepStatus;
									oLineInfo.CreqObmain = undefined; //oParallelToPrStep.CR_OBMAIN;
									oLineInfo.Priority = oParallelToPrStep.UsmdPriority;
									oLineInfo.Reason = oParallelToPrStep.UsmdReason;
									oLineInfo.RejectReason = oParallelToPrStep.UsmdReasonRej;
									oLineInfo.Sign = oParallelToPrStep.Sign;
									oLineInfo.AppStep = oParallelToPrStep.AppStep;
									oLineInfo.Apprdesc = oParallelToPrStep.Apprdesc;
									oLineInfo.Emailtemp = oParallelToPrStep.Emailtemp;
									oLineInfo.AddlEmails = oParallelToPrStep.AddlEmails;
									
									oController.createNewLineForGraphNode(
										oGraph, oController, oLineInfo.ApproverType, oLineInfo.ApproverVal,
										oLineInfo.FromNode, oLineInfo.ToNode, oLineInfo.Action, oLineInfo.RowNum,
										oLineInfo.StepStatus, oLineInfo.CreqObmain, true,
										oLineInfo.Priority, oLineInfo.Reason, oLineInfo.RejectReason,
										oLineInfo.Sign, oLineInfo.AppStep, oLineInfo.Emailtemp, oLineInfo.AddlEmails, oLineInfo.Apprdesc);
									arrLinesForGraphStep.push(oLineInfo);
								} catch(error) {
									// Nothing to do .. ideally should not reach here.
								}
								
							});
						});
					}
				}
				
				if(!bParallelNavExists) {
					Utilities.showPopupAlert(
						"The workflow graph is outdated. Kindly readjust the workflow as per the new version. Please check the user guide for more details",
						MessageBox.Icon.Information, "Parallel Workflow Version Update");
				}
				
				

				let sCurrentAppStep = this.DynamicWorkflowDialog.getAppStep(this, "Current AppStep");
				oController.getApproverModel().setProperty("/currentAppStep", sCurrentAppStep);

				oGraph.detachAfterLayouting(oController.afterGraphReady);
			},
			// If there were no settings, return the default status of the graph. Intial view
			_getGraphInitStatus: function () {
				let oController = this;

				let promise = new Promise(function (resolve) {
					let oModel = new JSONModel();
					oModel.loadData("view/Workflow/graph.json", false);
					oModel.attachRequestCompleted(function (oEventModel) {
						// Read the data and move to the nodes and lines information 
						oController.nodesInitData = oEventModel.getSource().getData();

						oController.globalGraphInfo.nodes = [];
						oController.globalGraphInfo.lines = [];

						// Load the init data only
						for (let i = 0; i < oController.nodesInitData.nodes.length; i++) {
							let tempNode = oController.nodesInitData.nodes[i];
							// Copy the node data if not a sample node
							if (tempNode.key.indexOf("Sample") === -1) {
								let iGlobalNodeCount = oController.globalGraphInfo.nodes.length;
								oController.globalGraphInfo.nodes[iGlobalNodeCount] = {};
								let nodeGlobal = oController.globalGraphInfo.nodes[iGlobalNodeCount];

								nodeGlobal.key = tempNode.key;
								nodeGlobal.title = tempNode.title;
								nodeGlobal.icon = tempNode.icon;
								nodeGlobal.status = tempNode.status;
								nodeGlobal.group = tempNode.group;
								nodeGlobal.showActionLinksButton = tempNode.showActionLinksButton;
								nodeGlobal.showExpandButton = tempNode.showExpandButton;
								nodeGlobal.attributes = tempNode.attributes;

								if (tempNode.key === "A0") {
									nodeGlobal.actions = [{
										icon: "sap-icon://add"
									}, {
										icon: "sap-icon://add-process"
									}];
									nodeGlobal.actionType = [{
										"action": "08",
										"actdesc": "withdraw"
									}, {
										"action": "09",
										"actdesc": "activate"
									}];
									/*nodeGlobal.actions[0] = {
										icon: "sap-icon://add-employee",
										handler: oController.testhandler
									};*/
								} else if (tempNode.key === "91") {
									nodeGlobal.actions = [{
										icon: "sap-icon://add-process"
									}];
									if(oController.getView().getModel("viewWorkflow").getProperty("/selectedChangeRequest/UsmdSingleObj")) {
										nodeGlobal.actions.splice(0, 0, {
											icon: "sap-icon://add"
										});
									}
									nodeGlobal.actionType = [{
										"action": "31",
										"actdesc": "Success"
									}, {
										"action": "32",
										"actdesc": "Failed"
									}, {
										//Task 12814 - Add step status 33 for 91
										"action": "33",
										"actdesc": "Failed(Snapshot)"
									}];
								}
							}
						}

						for (let j = 0; j < oController.nodesInitData.lines.length; j++) {
							let iGlobalLines = oController.globalGraphInfo.lines.length;
							oController.globalGraphInfo.lines[iGlobalLines] = {};
							let lineGlobal = oController.globalGraphInfo.lines[iGlobalLines];
							let tempLine = oController.nodesInitData.lines[j];
							// Copy the node data
							lineGlobal.from = tempLine.from;
							lineGlobal.to = tempLine.to;
							lineGlobal.title = tempLine.title;
							if (tempLine.from === "A0") {
								lineGlobal.action = "08";
							}
						}

						resolve(oController.globalGraphInfo);
					});
				});

				return promise;
			},

			/**
			 * Task 12336 - WF Graph Layout Changes
			 * Hide All Duplicate Lines from FROM node to TO Node
			 * Only show one line and hide all the rest. 
			 * Instead of Showing the lines, the details of all lines will be shown when the user clicks the Line
			 */
			hideDuplicateLines: function(oController) {
				let oGraph = oController.getView().byId("graph");
				let arrGraphLines = oGraph.getLines();
				for(let i=0; i<arrGraphLines.length - 1; i++) {
					for (let j=i+1; j<arrGraphLines.length;j++) {
						if(arrGraphLines[i].getFrom() === arrGraphLines[j].getFrom() &&
						arrGraphLines[i].getTo() === arrGraphLines[j].getTo()) {
							arrGraphLines[j].setHidden(true);
						}
					}
				}
			},

			/**
			 * Get and create (if not existing) the approver model and return to the caller. 
			 */
			getApproverModel: function () {
				let oApproverModel = this.getView().getModel("approverModel");

				// Create the model if it does not exist
				if (!oApproverModel) {
					oApproverModel = new JSONModel();
					this.getView().setModel(oApproverModel, "approverModel");
				}

				return oApproverModel;
			},

			onTransportPackageSelectionDialogCreated: function(oEvent){
				let comp = oEvent.getParameter("component");
				// store the component handle 
				this._transportPackageSelectionDialog = comp;

				this.getUsername()
				.then(function (oSapUserInfo) {
					let sUsername = oSapUserInfo.Sapname;
					if (!sUsername) {
						Utilities.showPopupAlert("Please login to continue.", MessageBox.Icon.Error, "Logged out");
					} else {
						comp.setUser(sUsername);
					}

				});
			},

			/**
			 * 
			 * @param {*} bCustomizing 
			 * @param {*} bWorkbench 
			 * @param {*} bPackage 
			 * @param {*} bReturnPreselectedTransport : This is valid only if one of the transports is requested. If more than one of the customzing or workbench is requested, this paramter has not effect. 
			 * @returns 
			 */
			_selectTransportPackage : function(bCustomizing, bWorkbench, bPackage, bReturnPreselectedTransport) {
				let oModel = this.getApproverModel();
	
				// Read the previous selections (if any)
				let sCustomzingTransport = oModel.getProperty("/CRTypeDetails/customzingTransport");
				let sWorkbenchTransport = oModel.getProperty("/CRTypeDetails/workbenchTransport");
				let sPackage = oModel.getProperty("/CRTypeDetails/package");

				// Read any preselected transports .. these are higher priority
				let sPreSelectedCustomizingTransport = oModel.getProperty("/CRTypeDetails/CustomizingTr");
				let sPreSelectedWorkbenchTransport = oModel.getProperty("/CRTypeDetails/WorkbenchTr");

				// Priortize preselected
				if(sPreSelectedCustomizingTransport){
					sCustomzingTransport = sPreSelectedCustomizingTransport;
				}

				if(sPreSelectedWorkbenchTransport){
					sWorkbenchTransport = sPreSelectedWorkbenchTransport;
				}

				if(bReturnPreselectedTransport && sCustomzingTransport && bCustomizing && !bWorkbench && !bPackage){
					return Promise.resolve({
						customizingTransport: sCustomzingTransport
					});
				}

				if(bReturnPreselectedTransport && sWorkbenchTransport && !bCustomizing && bWorkbench && !bPackage){
					return Promise.resolve({
						workbenchTransport: sWorkbenchTransport
					});
				}

				let promise = this._transportPackageSelectionDialog.open(
					bCustomizing, sCustomzingTransport, 
					bWorkbench, sWorkbenchTransport,
					bPackage, sPackage
				);
	
				let promiseResponse = promise.then(function(oResponseSelection){
					if(bCustomizing){
						oModel.setProperty("/CRTypeDetails/customzingTransport", oResponseSelection.customizingTransport);
					}
	
					if(bWorkbench){
						oModel.setProperty("/CRTypeDetails/workbenchTransport", oResponseSelection.workbenchTransport);
					}
	
					if(bPackage) {
						oModel.setProperty("/CRTypeDetails/package", oResponseSelection.package);	
					}
	
					return oResponseSelection;
				});
	
				return promiseResponse;
			},

			resetTransportsAndPackage: function(oController) {
				let oModel = oController.getApproverModel();
	
				// customizing
				// oModel.setProperty("/CRTypeDetails/Transport", undefined);
				oModel.setProperty("/CRTypeDetails/customzingTransport", undefined);
				// Workbench
				//oModel.setProperty("/TransportSelected", undefined);
				oModel.setProperty("/CRTypeDetails/workbenchTransport", undefined);
				// oModel.setProperty("/CRTypeDetails/Package", undefined);
				oModel.setProperty("/CRTypeDetails/package", undefined);
			},

			onBADISelectionDialogCreated: function (oEvent) {
				let comp = oEvent.getParameter("component");
				// store the component handle 
				this._BADISelectionDialog = comp;
		
				this._BADISelectionDialog.attachBADISelected(this.onBADISelected, this);
			},

			onBADISelected: function (oBADISelected) {
				let oBadiSelections = oBADISelected.getParameters();
		
				/**
				 * Due to the design of the BADI selection component, it returns success even when nothing is 
				 * selected. Until we resolve that, we will be checking to see if anything was returned. 
				 * If everything is empty, stop processing. 
				 * 
				 * This is not a complete check, but a temporary resolution.
				 */
				if( oBadiSelections.BADISelectionNeeded &&
					!oBadiSelections.agentBADIName && 
					!oBadiSelections.dfpBADIName && 
					!oBadiSelections.dynamicBADIName && 
					!oBadiSelections.fourEyesBADIName && 
					!oBadiSelections.methodBADIName && 
					!oBadiSelections.parallelBADIName)
				{
					this._BADISelectionDialogPromiseRejectFunction();
					return;
				}

				// Store the BAdI to the model
				this.getApproverModel().setProperty("/CRTypeDetails/BAdISelected", oBadiSelections);
		
				this._BADISelectionDialogPromiseResolveFunction(oBadiSelections);
			},

			getSelectedBADI: function () {
				let oController = this;
		
				if (this._BADISelectionDialogPromiseRejectFunction) {
					this._BADISelectionDialogPromiseResolveFunction = undefined;
					this._BADISelectionDialogPromiseRejectFunction({});
					this._BADISelectionDialogPromiseRejectFunction = undefined;
				}
		
				this._BADISelectionDialogPromise = new Promise(function (resolve, reject) {
					let sBAdISelected = oController.getApproverModel().getProperty("/CRTypeDetails/BAdISelected");
		
					if (sBAdISelected) {
						resolve(sBAdISelected);
					}else{
						oController._BADISelectionDialogPromiseResolveFunction = resolve;
						oController._BADISelectionDialogPromiseRejectFunction = reject;
						oController._BADISelectionDialog.open();
					}
		
				});
		
				return this._BADISelectionDialogPromise;
			},

			/**
			 * Initialize the graph. Udpate the toolbar, create the modal
			 * Read all the lists used for editing 
			 */
			onInit: function () {
				// Initialize global data 
				this.nodesInitData = null;
				this.showLeyend = false;
				this.globalGraphInfo = {
					nodes: [],
					lines: []
				};

				let oController = this;
				let oToolbar;

				this.DynamicWorkflowDialog = DynamicWorkflowDialog;
				this.SelectAttributeDialog = SelectAttributeModel;
				this.NodeConnectionsTableDialog = NodeConnectionsTableModel;
				this.GetListData = GetListsData;
				this.AdditionalEmailsTableDialog = AdditionalEmailsTable;

				// Set the model for the view 
				let oApproverModel = this.getApproverModel();

				let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
				// Load the parameters received into the model
				oRouter.getRoute("Workflow").attachPatternMatched(this, this._onRouteMatched);

				let promiseApprType = GetListsData.getApprType(this);
				promiseApprType.then(
					function (oData) {
						oApproverModel.setProperty("/approverType", oData);
					}
				);

				GetListsData.getCR_OBMAIN(this)
				.then(
					function (oData) {
						oApproverModel.setProperty("/cr_OBMAIN", oData);
					}
				);

				let promiseStepType = GetListsData.getWFConfig(this, "/STEPTYPESet");
				promiseStepType.then(
					function (oData) {
						oApproverModel.setProperty("/stepType", oData);
					}
				);

				let promiseServiceNames = GetListsData.getServiceNamesList(this);
				promiseServiceNames.then(
					function (oData) {
						oApproverModel.setProperty("/serviceNames", oData);
					}
				);
				let promiseActType = GetListsData.getWFConfig(this, "/ACTIONDETAILSSet");
				promiseActType.then(
					function (oData) {
						oApproverModel.setProperty("/actionType", oData);
					}
				);

				let promiseStepStatus = GetListsData.getStepStatusList(this);
				promiseStepStatus.then(
					function (arrStepStatus) {
						oApproverModel.setProperty("/stepStatus", arrStepStatus);
					}
				);

				let promiseCRStatus = GetListsData.getWFConfig(this, "/CRSTATUSSet");
				promiseCRStatus.then(
					function (oData) {
						oApproverModel.setProperty("/crStatus", oData);
					}
				);

				oToolbar = this.getView().byId("graph").getToolbar();
				oToolbar.setVisible(false);
				oToolbar.insertContent(new Label("title", {
					text: "Change Request Type: {approverModel>/CRTypeDetails/CrTypeName}"
				}), 0);

				oToolbar.insertContent(new Button("btnShowLegend", {
					type: sap.m.ButtonType.Transparent,
					icon: "sap-icon://form"
				})
				.setTooltip("Show/Hide BAdIs list")
				.attachPress(this, () => {
					if (oController.showLeyend) {
						jQuery(".sapSuiteUiCommonsNetworkGraphLegend").hide();
					}else{
						jQuery(".sapSuiteUiCommonsNetworkGraphLegend").show();
					}
					oController.showLeyend = !oController.showLeyend;
				}), 0);

				//Task 12336 - WF Graph Layout Changes - Add Full screen button to the Workflow Graph
				oToolbar.insertContent(new Button("fullScreenButton", {
					type: sap.m.ButtonType.Transparent,
					icon: "sap-icon://full-screen"
				})
				.setTooltip("Full Screen")
				.attachPress(this, this.fullScreenGraph, this.getView().byId("graph")), 0);
				/*
				 * Refresh button - add to toolbar
				 */
				oToolbar.insertContent(new Button("resetButton", {
						type: sap.m.ButtonType.Transparent,
						icon: "sap-icon://reset"
					})
					.setTooltip("Reset Workflow")
					.attachPress(this, this.resetGraph, this.getView().byId("graph")), 0);

				oToolbar.insertContent(new Button("activateButton", {
						type: sap.m.ButtonType.Transparent,
						icon: "sap-icon://activate"
					})
					.setTooltip("Activate Workflow")
					.attachPress(this, this.saveGraph, this.getView().byId("graph")), 0);

				oToolbar.insertContent(new Button("saveButton", {
						type: sap.m.ButtonType.Transparent,
						icon: "sap-icon://save"
					})
					.setTooltip("Save Workflow")
					.attachPress(this, this.saveGraph, this.getView().byId("graph")), 0);

				oToolbar.insertContent(new Button("graphCheckButton", {
						type: sap.m.ButtonType.Transparent,
						icon: "sap-icon://validate"
					})
					.setTooltip("Validate Workflow")
					.attachPress(this, this.checkGraphValidityHandler, this), 0);
				
				// Bug 13207 - Changed InfoLabel to ObjectStatus
				oToolbar.insertContent(new sap.m.ObjectStatus("draftLabel", {
						state: "Warning",
						icon: "sap-icon://request",
						inverted: true,
						text: "Draft",
						visible: "{= ${approverModel>/CRTypeDetails/IsDraft} === 'Draft'}"
					})
					.setTooltip("Draft"));

			},

			/**
			 *	Reset the graph
			 */
			resetGraph: function (oButtonEvent, oController) {
				let oGraph = oController.getView().byId("graph");
				let oViewModel = oController.getApproverModel();
				let sIsDraft = oViewModel.getProperty("/CRTypeDetails").IsDraft;

				oController.resetTransportsAndPackage(oController);

				if (!oGraph.isFullScreen()) {
					if (sIsDraft === "Draft") {
						let promiseShowPopupAlert = Utilities.showPopupAlert(
							"Are you sure you want to reset the Workflow?",
							MessageBox.Icon.WARNING, "Reset Workflow", [MessageBox.Action.YES, MessageBox.Action.NO]);
						promiseShowPopupAlert.then(function () {
							oController.saveGraph(null, oController);
						}, function () {
							
						});
					}else{
						MessageToast.show("Nothing to reset", {
							at: "center top",
							offset: "0 200"
						});
					}	
				}else{
					let CrTypeName = oViewModel.getProperty("/CRTypeDetails").CrTypeName;
					oController._loadGraphData(oController, CrTypeName);
					// Move out of the full screen mode
					oController._setGraphNonFullScreen(oController);
				}
			},

			/**
			 * Handler for the check validity action. 
			 */
			checkGraphValidityHandler: function () {
				let oGraph = this.getView().byId("graph");
				let bIsGraphValid = this.checkGraphValidity(this, oGraph);

				if (bIsGraphValid === true) {
					Utilities.showPopupAlert(
						"Found at least one path from Requestor to either of Activation, Complete or Reject. This workflow meets minimum criteria for validity.",
						MessageBox.Icon.SUCCESS, "Workflow Valid");
				}
			},

			/**
			 * This function is called 
			 * 1. Before saving the graph and  
			 * 2. When the user clicks on the check graph button (to be added)
			 * 
			 * Run through the node list and check 
			 * 1. If there is atleast 1 path from start to finish (Either Activation, Complete or Reject)
			 *  Requestor	= A0
			 *  Withdraw	= 92
			 *  Activation	= 91
			 *  Complete	= 99
			 * 
			 * Return value 
			 * 1. List of node connected to the input node oNode
			 * 
			 */
			checkGraphValidity: function (oController, oGraph) {
				// List of nodes already validated, Empty to start and will be filled in each pass of _findTermnatingNode
				let arrValidatedNodes = [];

				// Check if all the nodes are valid 
				let bValid = oController.checkGraphNodeValidity(oController, oGraph);
				if (bValid === false) {
					return false; // Do not proceed. Message shown in the function 
				}

				// find the requestor node A0
				let oNode = oController.getNodeWithKey(oController, "A0");

				let bIsValidWorkflow = oController._findTerminatingNode(oController, oGraph, oNode, arrValidatedNodes);

				// Popup to show error 
				if (!bIsValidWorkflow) {
					Utilities.showPopupAlert(
						"The workflow needs to have at least one path that terminates at Withdraw or Complete or Activation to be valid.",
						MessageBox.Icon.ERROR, "Workflow Invalid. Cannot Save.");
				}

				return bIsValidWorkflow;
			},

			/**
			 * Expected to be called only from checkGraphValidity. This is a recursive function that 
			 * returns a true or false status based on whether a terminating node (Withdraw, Activation or Complete)
			 * was found. 
			 * 
			 * true : A terminating node was found 
			 * false : A terminating node was not found
			 */
			_findTerminatingNode: function (oController, oGraph, oNode, arrValidatedNodes) {
				let arrTerminatingNodes = ["92", "91", "99"];
				let bFoundTerminatingNode = false;
				let sNodeKey = oNode.getKey();

				if (arrValidatedNodes.includes(sNodeKey)) {
					// Already processed, return false;
					return false;
				}
				arrValidatedNodes.push(sNodeKey);

				// Get the list of nodes downstream of this node and check if any of those nodes is a terminating node 

				// get list of time starting from this node 
				let arrLines = oGraph.getLines();
				// get the list of node downstream of this node 
				let arrDownStreamNode = [];
				arrLines.forEach(function (oLine) {
					if (oLine.getFrom() === sNodeKey) {
						let sDownStreamNode = oLine.getTo();
						arrDownStreamNode.push(sDownStreamNode);
					}
				});

				// Go through the down stream nodes and check if it is a terminating node else .. check its downstream nodes 
				arrDownStreamNode.forEach(function (sNodeName) {
					if (arrTerminatingNodes.includes(sNodeName)) {
						bFoundTerminatingNode = true;
					} else { // Search deeper in the node 
						let oCurrentNode = oController.getNodeWithKey(oController, sNodeName);
						let bValidTermNodeFound = oController._findTerminatingNode(oController, oGraph, oCurrentNode, arrValidatedNodes);
						bFoundTerminatingNode = bFoundTerminatingNode || bValidTermNodeFound;
					}
				});

				return bFoundTerminatingNode;
			},

			/**
			 * This function is called 
			 * 1. Before saving the graph and 
			 * 2. When the user clicks on the check graph button (to be added)
			 * 
			 * Run through the node list and check if any node in the graph exists without a previous 
			 * node. (Except the Requestor node)
			 * Also changes the state of the node to error if the node has no prev nodes.
			 * 
			 * Return value 
			 * true: All nodes have a prev node 
			 * false: found some nodes without a prev node 
			 */
			checkGraphNodeValidity: function (oController, oGraph) {
				let arrLines = oGraph.getLines();
				let arrNodes = oGraph.getNodes();
				let bInvalidNodeFound = false;
				let bBlankNodeFound = false;
				let bError = false;
				let iCounterAgent = 0;
				let iCounterSMethod = 0;
				let iCounterParallel = 0;
				let iCounterDynamic = 0;
				let oWorkflowModel = oController.getView().getModel("viewWorkflow");
				let bSkipApprover = oWorkflowModel.getProperty("/selectedChangeRequest/SkipApprover");
				let oSelectedChangeRequest = oWorkflowModel.getProperty("/selectedChangeRequest");

				if (bSkipApprover) {
					iCounterSMethod++;
				}
				// Loop through the nodes and check if any node connects to it 
				arrNodes.forEach(function (oNode) {
					let sNodeKey = oNode.getKey();
					let sNodeIcon = oNode.getIcon();

					/***
					 * This check has been commented to allow for checks that look for BADI selection requirement.
					 * Having A0 break out check here blocked this capability. 
					 * 
					 * This check has now been moved to the end of this function.					 * 
						// The INIT / Requester node cannot have a prev node
						if (sNodeKey === "A0") {
							return;
						}
					*/

					let arrLinesConnected = arrLines.filter(function (oLine) {
						if (oLine.getTo() === sNodeKey) {
							return true;
						}
						return false;
					});

					if (iCounterAgent === 0) {
						for(let i = 0; i < arrLinesConnected.length; i++){
							let oCustomData = arrLinesConnected[i].getCustomData()[0];
							let oValue = oCustomData.getValue();
							if (oValue?.Emailtemp) {
								iCounterAgent++;
								break;
							}
						}
					}
					
					let oNodeDetails = oController._getNodeApproverDetails(oNode);
					let arrLinesConnectedFrom = arrLines.filter(function (oLine) {
						if (sNodeIcon === DMRNode.Icons.SystemMethod) {
							if (oLine.getFrom() === sNodeKey) {
								for (let i = 0; i < oNodeDetails.StepActions.length; i++) {
									if (oLine.getTitle() === oNodeDetails.StepActions[i]) {
										return true;
									}
								}
							}
						}
						return false;
					});
					

					// BUG 12635 - Saving workflow graph when some nodes are blank
					oController.getNodeWithKey(oController, sNodeKey).setStatus("Success");
					let bNASet=false;
					let sRowNum;

					// if the node is approver
					// Bug 12732 - Changed validations to know the node type by the value in the icon property
					if(sNodeIcon === DMRNode.Icons.Approver && Object.hasOwn(oNodeDetails, "ApprType")){
						if(!oNodeDetails.ApprType && !oNodeDetails.ApprVal && !oNodeDetails.StepType){
							bBlankNodeFound = true;
							// get the node and set the state to error 
							oController.getNodeWithKey(oController, sNodeKey).setStatus("Error");
						}

					// if the node is system method
					} else if(sNodeIcon === DMRNode.Icons.SystemMethod && Object.hasOwn(oNodeDetails, "Servicename")){
						iCounterSMethod++;
						if(!oNodeDetails.Servicename){
							bBlankNodeFound = true;
							// get the node and set the state to error 
							oController.getNodeWithKey(oController, sNodeKey).setStatus("Error");
						}

					// if the node is parallel
					} else if(sNodeIcon === DMRNode.Icons.Parallel && !oNode.getProperty("title").includes("Merge Node")){
						iCounterParallel++;
						if(!Object.hasOwn(oNodeDetails, "DynamicRuleList") && !oNodeDetails.ApprType){
							bBlankNodeFound = true;
							// get the node and set the state to error 
							oController.getNodeWithKey(oController, sNodeKey).setStatus("Error");
						} else if(Object.hasOwn(oNodeDetails, "DynamicRuleList")){
						
							oNodeDetails.DynamicRuleList.DTVALUESSet.forEach((el)=>{
								// if the iteration is on a new row change the boolean value to undefined
								if(sRowNum!==el.RowNum){
									sRowNum = el.RowNum;
									bNASet=false;
								}
								// if the col value is NA then set the boolean value to true
								if(el.ColValue==="NA"){
									bNASet = true;
								}
								if((el.ColName==="USER_TYPE" && !el.ColValue) || (el.ColName==="USER_VALUE" && !el.ColValue && !bNASet) 
									|| (el.ColName==="STEP_TYPE" && !el.ColValue) ){
									bBlankNodeFound = true;
									// get the node and set the state to error 
									oController.getNodeWithKey(oController, sNodeKey).setStatus("Error");
								}
						
							});
						
						// Bug 12699: Missing details error in saving parallel workflow
						} else if(oNode.getProperty("title").includes("child")){
							if(!oNodeDetails.ApprType && !oNodeDetails.ApprVal && !oNodeDetails.StepType){
								bBlankNodeFound = true;
								// get the node and set the state to error 
								oController.getNodeWithKey(oController, sNodeKey).setStatus("Error");
							}
						}
						// END Bug 12699: Missing details error in saving parallel workflow
					// if the node is dynamic
					} else if(sNodeIcon === DMRNode.Icons.Dyamic){
						iCounterDynamic++;
						if(!Object.hasOwn(oNodeDetails, "DynamicRuleList")){
							bBlankNodeFound = true;
							// get the node and set the state to error 
							oController.getNodeWithKey(oController, sNodeKey).setStatus("Error");
						} else {
							
							oNodeDetails.DynamicRuleList.DTVALUESSet.forEach((el)=>{
								// if the iteration is on a new row change the boolean value to undefined
								if(sRowNum!==el.RowNum){
									sRowNum = el.RowNum;
									bNASet=false;
								}
								// if the col value is NA then set the boolean value to true
								if(el.ColValue==="NA"){
									iCounterSMethod++;
									bNASet = true;
								}
								if((el.ColName==="USER_TYPE" && !el.ColValue) || (el.ColName==="USER_VALUE" && !el.ColValue && !bNASet) 
									|| (el.ColName==="STEP_TYPE" && !el.ColValue) ){
									bBlankNodeFound = true;
									// get the node and set the state to error 
									oController.getNodeWithKey(oController, sNodeKey).setStatus("Error");
								}
							
							});
							
						}
					//Bug 12635 - Added validation for Follow Up CR
					} else if(sNodeIcon === DMRNode.Icons.FollowUpCr){
						iCounterSMethod++;
						if (!oNodeDetails.CrType) {
							bBlankNodeFound = true;
							// get the node and set the state to error 
							oController.getNodeWithKey(oController, sNodeKey).setStatus("Error");
						}
					}
					// END BUG 12635 - Saving workflow graph when some nodes are blank

					if (sNodeIcon === DMRNode.Icons.SystemMethod) {
						if (arrLinesConnected.length === 0 || arrLinesConnectedFrom.length === 0) {
							bInvalidNodeFound = true;

							// get the node and set the state to error 
							oController.getNodeWithKey(oController, sNodeKey).setStatus("Error");
						}
					} else {
						if (arrLinesConnected.length === 0 && sNodeKey !== "A0" ) {
							bInvalidNodeFound = true;

							// get the node and set the state to error 
							oController.getNodeWithKey(oController, sNodeKey).setStatus("Error");
						}
					}
				});

				let oCrTypeDetails = oController.getApproverModel().getProperty("/CRTypeDetails");
				let sCrTypeName = oCrTypeDetails.CrTypeName;
				let sDataModel = oCrTypeDetails.DataModel;
				this._BADISelectionDialog.setRuleDetails({
					needFourEyesBadi: oSelectedChangeRequest.FourEyesPrinciple,
					needDfpBadi: oSelectedChangeRequest.DisableFieldProperties,
					needDynamicBadi: (iCounterDynamic > 0 || iCounterParallel > 0),
					needParallelBadi: (iCounterParallel > 0),
					needSMethodBadi: (iCounterSMethod > 0 || iCounterParallel > 0),
					needAgentBadi: (iCounterAgent > 0),
					usmdCrtype: sCrTypeName,
					usmdModel: sDataModel
				});

				if (bInvalidNodeFound) {
					Utilities.showPopupAlert("Some nodes cannot be reached. Please connect the identified nodes (marked red) to proceed.", 
					MessageBox.Icon.ERROR, "Workflow Invalid. Cannot Save.");

					bError = true;
				} else if(bBlankNodeFound){
					Utilities.showPopupAlert("Some mandatory fields are empty. Please filled the information on the identified nodes (marked red) to proceed.", 
					MessageBox.Icon.ERROR, "Workflow Invalid. Cannot Save.");

					bError = true;
				}

				return !bError;

				
			},

			/**
			 *	Save the graph and all its contents to the service 
			 */
			saveGraph: function (oButtonEvent, oController) {
				// Save the graph contnt 
				let oCrTypeDetails = oController.getApproverModel().getProperty("/CRTypeDetails");
				let CrTypeName = oCrTypeDetails.CrTypeName;
				let oGraph = oController.getView().byId("graph");
				let sSERVICEMODELNAME = "WORKFLOW";

				// Check if all the nodes have a valid connection 
				let bValid = oController.checkGraphNodeValidity(oController, oGraph);
				if (bValid === false) {
					return; // Do not proceed. Message shown in the function 
				}

				// Get the lines attached to and from this node
				let oGraphLines = oGraph.getLines();
				let oGraphNodes = oGraph.getNodes();
				let oData = {};

				oData.CrType = CrTypeName;
				oData.CustTransport = oCrTypeDetails.customzingTransport;
				if (!oData.CustTransport) {
					oData.CustTransport = oCrTypeDetails.CustomizingTr;
				}
				oData.ApprovalSteps = 0;
				oData.Message = "";
				oData.Messagetype = "";
				oData.APPROVALSet = [];
				oData.Package = "";
				oData.IsDraft = false;
					
				
				if (oButtonEvent) {
					oData.IsDraft = oButtonEvent.getSource().getId() === "saveButton" ? true : false;
					oGraphNodes.forEach(function (el) {
	
						// Get the node Type 
						let iNodeType = oController._getNodeType(oController, el);
	
						let APPROVALSet = {};
						APPROVALSet.CrType = CrTypeName;
						APPROVALSet.Row = "1";
						APPROVALSet.CrStep = el.getKey();
						APPROVALSet.Apprdesc = el.getTitle().includes(": ") ? el.getTitle().split(": ")[1] : undefined;
	
						// Ensure that the key name matches the REGEX 
						if ((new RegExp("[A-Z][0-9]")).test(el.getKey())) {
							oData.ApprovalSteps++;
						}
	
						// Get the node details 
						let oNodeDetails = oController._getNodeApproverDetails(el);
						// Read the details of the node into the server data format
						if (iNodeType === DMRNode.Types.SystemMethod) {
							APPROVALSet.Servicename = oNodeDetails.Servicename;
						} else {
							APPROVALSet.ApproverType = oNodeDetails.ApprType;
							// Bug 12778 - Description is removed before sending the value
							APPROVALSet.ApproverValue = oNodeDetails.ApprVal ? oNodeDetails.ApprVal.split(" - ")[0] : oNodeDetails.ApprVal;
							APPROVALSet.StepType = oNodeDetails.StepType;
							APPROVALSet.CrStepName = oNodeDetails.StepName;
						}
						// If the node type is dynamic set the appropriate service name and load the data
						
						if (iNodeType === DMRNode.Types.Dynamic || iNodeType === DMRNode.Types.Parallel) {
							sSERVICEMODELNAME = "WORKFLOWDYN";
							// Read the node dynamic rule details 
							let oDynamicRuleData = oNodeDetails.DynamicRuleList;
							oData.WbTransport = oDynamicRuleData.WbTr;
							oData.Package = oDynamicRuleData.Package;
							
							let oDrivingEntitySet = JSON.parse(JSON.stringify(oDynamicRuleData.DRIVINGENTITYSet));
							let oDTValueSet = JSON.parse(JSON.stringify(oDynamicRuleData.DTVALUESSet));
							let oDerivingEntitySet = JSON.parse(JSON.stringify(oDynamicRuleData.DERIVINGENTITYSet));
							APPROVALSet.Apprdesc = oDynamicRuleData.Apprdesc;
							
							if (oDTValueSet) {
								oDTValueSet.forEach(oValue => {
									if(oValue.ColName === "USER_VALUE" && oValue.ColValue === "Target State Routing"){
										oValue.ColValue = "";
									}else if (oValue.ColName.includes("__")) {
										oValue.ColValue = (oValue.ColValue 
															&& typeof oValue.ColValue === "string" 
															&& oValue.ColValue.includes(" - ")) 
															? oValue.ColValue.split(" - ")[0] 
															: oValue.ColValue;
									}
								});
							}

							// If Dynamic Update the Dynamic 
							if (iNodeType === DMRNode.Types.Dynamic) {
								APPROVALSet.DRIVINGENTITYSet = oDrivingEntitySet;
								APPROVALSet.DTVALUESSet = oDTValueSet;
								APPROVALSet.DERIVINGENTITYSet = oDerivingEntitySet;
							} else {
								/**A parallel node must have a merge node on the same group. 
								 * Fetch the merge node key and and pass it to service structure as Mergestep
								 */
								let oMergeNode = oGraphNodes.find(oNode => {
									return oNode.getGroup() === el.getGroup() &&
											oNode.getIcon() === DMRNode.Icons.Merge;         
								});
								if(oMergeNode) {
									APPROVALSet.Mergestep = oMergeNode.getKey();
								}
								APPROVALSet.PARALLELDRIVINGSet = oDrivingEntitySet;
								APPROVALSet.PARALLELDTVALUESSet = oDTValueSet;
								APPROVALSet.PARALLELDERIVINGSet = oDerivingEntitySet;
								APPROVALSet.PARALLELAPPROVALSet = [];
								APPROVALSet.DRIVINGENTITYSet = [];
								APPROVALSet.DTVALUESSet = [];
								APPROVALSet.DERIVINGENTITYSet = [];
							}
							APPROVALSet.Changedoc = oDynamicRuleData.Changedoc;
						}

						APPROVALSet.PR_STEP_ACTSet = [];
						oGraphLines.forEach(function (li) {
							if (li.getTo() === el.getKey()) {
								let PR_STEP_ACTSet = {};
								PR_STEP_ACTSet.CrType = CrTypeName;
	
								if (li.getFrom() === "A0" &&
									li.getTitle() === "Init") {
									PR_STEP_ACTSet.PrevStep = "00";
								} else {
									PR_STEP_ACTSet.PrevStep = li.getFrom();
								}
	
								// Identify the NodeType of current step. If Parallel, then set PrevStepType to P. Else I
								// Then, Modify the prev step type based on the Previous Nodes name, default to initial for Parallel, Dynamic, Merge Node [Task #7300]
								if(iNodeType === DMRNode.Types.Parallel) {
									PR_STEP_ACTSet.PrevStepType = "P";
								} else if(iNodeType === DMRNode.Types.FollowUpCr) {
									APPROVALSet.FOLLOWUPCRNAV = [];
									let arrFollowUpCr = oController.getApproverModel().getProperty("/followUpCr");
									for(let i = 0; i < arrFollowUpCr.length; i++){
										APPROVALSet.FOLLOWUPCRNAV.push({CrType: arrFollowUpCr[i]});
									}
									PR_STEP_ACTSet.PrevStepType = "I";
								} else {
									PR_STEP_ACTSet.PrevStepType = "I";
								}
								let oPrevNodeObject = oController.getNodeWithKey(oController, li.getFrom());
								let sPrevStepType = oController._getNodeType(oController, oPrevNodeObject);
								if (sPrevStepType === DMRNode.Types.Parallel) {
									PR_STEP_ACTSet.PrevStepType = "P";
								} else if (sPrevStepType === DMRNode.Types.Dynamic) {
									PR_STEP_ACTSet.PrevStepType = "D";
								} else if(sPrevStepType === DMRNode.Types.Merge) {
									PR_STEP_ACTSet.PrevStepType = "M";
								} else if(sPrevStepType === DMRNode.Types.FollowUpCr) {
									PR_STEP_ACTSet.PrevStepType = "F";
								}
								
								let oCustomData = li.getCustomData()[0].getValue();
								PR_STEP_ACTSet.StepStatus = oCustomData.Status === "Standard" ? "" : oCustomData.Status;
								PR_STEP_ACTSet.PrevAction = li.getTitle() === "Init" ? "" : li.getTitle();
								if (PR_STEP_ACTSet.PrevAction === "**") { //TASK 9605
									PR_STEP_ACTSet.PrevAction = "";
								}
	
								//Adding details for Priority, Reason, Reason of Rejection if provided by the user
								PR_STEP_ACTSet.UsmdPriority = oCustomData.UsmdPriority;
								PR_STEP_ACTSet.UsmdReason = oCustomData.UsmdReason;
								PR_STEP_ACTSet.UsmdReasonRej = oCustomData.UsmdReasonRej;
	
								//Adding details of Sign provided by the user
								PR_STEP_ACTSet.Sign = oCustomData.Sign;
	
								//Adding details of AppStep which auto generated for all Dynamic Workflow Rules 
								PR_STEP_ACTSet.AppStep = oCustomData.AppStep;
								if(oCustomData.AppStep) {
									PR_STEP_ACTSet.Apprdesc = oCustomData.Apprdesc;
								}
								PR_STEP_ACTSet.Emailtemp = oCustomData.Emailtemp;
								PR_STEP_ACTSet.AddlEmails = oCustomData.AddlEmails;
	
								APPROVALSet.PR_STEP_ACTSet.push(PR_STEP_ACTSet);
							}
						});
						if(iNodeType === DMRNode.Types.Merge || iNodeType === DMRNode.Types.ParallelChild) {
							/**
							 * Fetch the parallel Node under which all the parallel chidren or merge node connections have to be added
							 * For Merge Node as target node, send CrStep as Complete (99) instead of MergeNode Key
							 * Add the connection details to PARALLELAPPROVALSet navigation
							 */
							let sGroupKey = el.getGroup().split("_")[1];
							let iNodeIndex = oData.APPROVALSet.findIndex((oNode) => {
								return sGroupKey === oNode.CrStep;
							});
							let oLinesToParallelFromDirectChild;
							if(iNodeType === DMRNode.Types.ParallelChild){
								let oLinesToParallelFromChild = oGraphLines.filter((oLine)=>{
										   return oLine.getTo() === sGroupKey && oLine.getFrom() === el.getKey();
									});
								if(oLinesToParallelFromChild.length > 0){
									let oNodesToChild = oGraphNodes.filter(function(oNode){
										return oNode.getGroup() === el.getGroup();
									});
									
									oLinesToParallelFromDirectChild = oGraphLines.filter((oLine)=>{
										return oNodesToChild.find(oNode => {
											return oNode.getKey() === oLine.getTo() && oLine.getFrom() === sGroupKey;
										});
									});
								}
							}
							
							if(iNodeIndex > -1) {
								if(!oData.APPROVALSet[iNodeIndex].PARALLELAPPROVALSet) { //array should be available since it is parallel node
									oData.APPROVALSet[iNodeIndex].PARALLELAPPROVALSet = [];
								}
								
								if(iNodeType === DMRNode.Types.Merge) {
									APPROVALSet.CrStep = DMRNode.Types.Complete.toString();      
								}
								
								APPROVALSet.PARALLELTOPRSTEP = [];
								APPROVALSet.PARALLELTOPRSTEP = APPROVALSet.PR_STEP_ACTSet;
								APPROVALSet.PR_STEP_ACTSet = undefined;
								oData.APPROVALSet[iNodeIndex].PARALLELAPPROVALSet.push(APPROVALSet);
								if(oLinesToParallelFromDirectChild)
								{
									let approvalSetChild = {};
									approvalSetChild.CrType = CrTypeName;
									approvalSetChild.Row = "1";
									approvalSetChild.CrStep = sGroupKey;
									approvalSetChild.ApproverType = oLinesToParallelFromDirectChild[0].getCustomData()[0].getValue().ApproverType;
									approvalSetChild.ApproverValue = oLinesToParallelFromDirectChild[0].getCustomData()[0].getValue().ApproverValue;
									approvalSetChild.StepType = "";
									approvalSetChild.AppStep = oLinesToParallelFromDirectChild[0].getCustomData()[0].getValue().AppStep;
									if(approvalSetChild.AppStep) {
										approvalSetChild.Apprdesc = oLinesToParallelFromDirectChild[0].getCustomData()[0].getValue().Apprdesc;
									} 
									approvalSetChild.PARALLELTOPRSTEP = [];
									approvalSetChild.PR_STEP_ACTSet = oData.APPROVALSet[iNodeIndex].PR_STEP_ACTSet.filter(function(element, index){
										if(element.PrevStep === el.getKey()){
											oData.APPROVALSet[iNodeIndex].PR_STEP_ACTSet.splice(index, 1);
											return true;
										}
										return false;
									});
									approvalSetChild.PR_STEP_ACTSet[0].AppStep = "";
									approvalSetChild.PARALLELTOPRSTEP = approvalSetChild.PR_STEP_ACTSet;
									approvalSetChild.PR_STEP_ACTSet = undefined;
									oData.APPROVALSet[iNodeIndex].PARALLELAPPROVALSet.push(approvalSetChild);
								}
							}
							
						} else {
							oData.APPROVALSet.push(APPROVALSet);
						}
						
					});
				}
				oData.ApprovalSteps = oData.ApprovalSteps.toString();

				// If this workflow contained a dynamic agent node remap the PR_STEP_ACTSet to PRSTEPACTSet
				if (sSERVICEMODELNAME === "WORKFLOWDYN") {
					oData.APPROVALSet.forEach(function (approvalSet) {
						approvalSet.PRSTEPACTSet = approvalSet.PR_STEP_ACTSet;
						approvalSet.PR_STEP_ACTSet = undefined;
					});
				}
				// Check the service 
				oController.saveGraphService(oController, sSERVICEMODELNAME, oData);
			},

			getWorkbenchTransportAndPackage: function (oController, sServiceModelName, oData) {
				let oWorkflowModel = oController.getView().getModel("viewWorkflow");
				let oGraph = oController.getView().byId("graph");
				let arrGraphNodes = oGraph.getNodes();
				let oSelectedChangeRequestType = oWorkflowModel.getProperty("/selectedChangeRequest");
				let oResponseObject = {};

				let oPromise = new Promise(function (resolve, reject) {
					// The workbench TR and PCKG are required for Dynamic or parallel flow only
					// In all other case return an empty object

					let oEmailTempRec = oData.APPROVALSet.filter((approvalSet) => {
						return (approvalSet.PR_STEP_ACTSet ? approvalSet.PR_STEP_ACTSet : approvalSet.PRSTEPACTSet).find(PrStepSet => {
							return PrStepSet.Emailtemp !== undefined && PrStepSet.Emailtemp !== "";
						});
					});
					// Task 12353 - Follow Up Change Request
					// Workbench TR and Package required when User adds Follow Up CR
					let oFollowUpCrNode = arrGraphNodes.find(oNode => {
						return oNode.getIcon() === DMRNode.Icons.FollowUpCr;
					});   
					

					let oSystemMethodNode = arrGraphNodes.find(oNode => {
						return oNode.getIcon() === DMRNode.Icons.SystemMethod;
					});

					let bNeedWorkbench = true;
					if(!oEmailTempRec.length && sServiceModelName !== "WORKFLOWDYN" && !oSelectedChangeRequestType.FourEyesPrinciple 
						&& !oSelectedChangeRequestType.DisableFieldProperties && !oFollowUpCrNode && !oSystemMethodNode 
						&& !oSelectedChangeRequestType.SkipApprover ) {
							bNeedWorkbench = false; 
					}

					oController._selectTransportPackage(true, bNeedWorkbench, true)
					.then(function(oResponseSelection){
						oResponseObject.CustTransport = oResponseSelection.customizingTransport;
						oResponseObject.WbTransport = oResponseSelection.workbenchTransport;
						oResponseObject.Package = oResponseSelection.package;
						resolve(oResponseObject);
					})
					.catch(() => reject() );
				});
				return oPromise;
			},

			saveGraphService: async function (oController, sServiceModelName, oData) {
				if (!oData.IsDraft) {
					let oBadiSelections = await oController.getSelectedBADI();

					if (oBadiSelections.hasOwnProperty("dynamicBADIName")) {
						oData.BADIIMPL = [];
					}
					
					if (oBadiSelections.fourEyesBADIName) {
						oData.BADIIMPL.push({
							CrType: oData.CrType,
							BadiKey: "4eyes BAdI Implementation",
							Value: oBadiSelections.fourEyesBADIName,
							ClsName: "",
							NewBadi: oBadiSelections.isNewFourEyesBADI
						});
					}

					if (oBadiSelections.dfpBADIName) {
						oData.BADIIMPL.push({
							CrType: oData.CrType,
							BadiKey: "4eyes Dfp BAdI Implementation",
							Value: oBadiSelections.dfpBADIName,
							ClsName: "",
							NewBadi: oBadiSelections.isNewDfpBADI
						});
					}

					if (oBadiSelections.dynamicBADIName) {
						oData.BADIIMPL.push({
							CrType: oData.CrType,
							BadiKey: "Dynamic BAdI Implementation",
							Value: oBadiSelections.dynamicBADIName,
							ClsName: "",
							NewBadi: oBadiSelections.isNewDynamicBADI
						});
					}
	
					if (oBadiSelections.parallelBADIName) {
						oData.BADIIMPL.push({
							CrType: oData.CrType,
							BadiKey: "Parallel BAdI Implementation",
							Value: oBadiSelections.parallelBADIName,
							ClsName: "",
							NewBadi: oBadiSelections.isNewParallelBADI
						});
					}
	
					if (oBadiSelections.methodBADIName) {
						oData.BADIIMPL.push({
							CrType: oData.CrType,
							BadiKey: "System Method BAdI Implementation",
							Value: oBadiSelections.methodBADIName,
							ClsName: "",
							NewBadi: oBadiSelections.isNewSMethodBADI
						});
					}
	
					if (oBadiSelections.agentBADIName) {
						oData.BADIIMPL.push({
							CrType: oData.CrType,
							BadiKey: "Check Agent BAdI Implementation",
							Value: oBadiSelections.agentBADIName,
							ClsName: "",
							NewBadi: oBadiSelections.isNewAgentBADI
						});
					}
				}
				let oPromise = oController.getWorkbenchTransportAndPackage(oController, sServiceModelName, oData);
				oPromise
					.then(function (oResponse) {
						if (oResponse.CustTransport && oResponse.Package) {
							oData.CustTransport = oResponse.CustTransport;
							oData.WbTransport = oResponse.WbTransport;
							oData.Package = oResponse.Package;
						}
					})
					.then(function () {
						(new DMRDataService(
							oController,
							sServiceModelName,
							"/DECISION_TABLESet",
							"CreateWF",
							"/", // Root of the received data
							"Create WF Decision Table"
						)).saveData(
							false,
							oData,
							null, {
								success: {
									fCallback: function (oParams, oResponseData) {
										Utilities.showPopupAlert(oResponseData.Message, MessageBox.Icon.INFORMATION, "Response");
										let ochangeReqsModel = oController.getView().getModel("changeReqs");
										let oCrTypeDetails = oController.getApproverModel().getProperty("/CRTypeDetails");
										let oChangeReqList = ochangeReqsModel.getProperty("/results").find(CRequest => CRequest.UsmdCreqType === oResponseData.CrType);
										oChangeReqList.IsDraft = oResponseData.IsDraft ? "Draft" : "";
										oChangeReqList.DraftState = oResponseData.IsDraft ? "Warning" : "None";
										ochangeReqsModel.refresh();
										let oChangeRequestSearchList = sap.ui.getCore().byId("container-SupernovaFJ---Workflow--changeRequestSearchList").getComponentInstance();
										oChangeRequestSearchList.setListData(ochangeReqsModel.getProperty("/results"));
										oChangeRequestSearchList.setSelectedItemByTitle(oCrTypeDetails.CrTypeName);
										// Move out of the full screen mode
										oController._setGraphNonFullScreen(oController);
									},
									oParam: oController
								},
								error: {
									fCallback: function () {
										Utilities.showPopupAlert("Error in updating BRF Tables", MessageBox.Icon.ERROR, "Action not completed");

										// Move out of the full screen mode
										oController._setGraphNonFullScreen(oController);
									},
									oParam: oController
								}
							}
						);

					});
				
			},

			/**
			 * Return an array of step actions for the specified step Type
			 */
			getStepActionsForType: function (sStepType) {
				let oSTypeArr = this.getApproverModel().getProperty("/stepType/results");
				// Find the step type based on the key 
				let oSelItem = oSTypeArr.find(function (arrD) {
					return (
						arrD.UsmdCrStype === sStepType
					);
				});

				let arrActionArray = oSelItem.STEPTYPETOACTION.results;
				let arrActionList = [];
				// iterate through the array and 
				arrActionArray.forEach(function (action) {
					arrActionList.push(action.UsmdCrAction);
				});

				return arrActionList;
			},

		

			changeRequestKindSelected: function(oEvent){
				let sSelectedIndex = oEvent.getSource().getSelectedIndex();
				let oApproverModel = this.getApproverModel();
				oApproverModel.setProperty("/ApprDetails/CrType", []);
				let arrChangeRequestList = oApproverModel.getProperty("/mainChangeReqList");
				// Task 12654 - Show in both lists if CR type is single object and parallel
				let arrFilteredChangeRequestList = arrChangeRequestList.filter(oChangeRequest => {
					// For Single Object we are showing "" / "S" / "SP"
					if (sSelectedIndex === 0) {
						return oChangeRequest.SingleParallel === "" || oChangeRequest.SingleParallel === "S" || oChangeRequest.SingleParallel === "SP";
					}
					// For Parallel we are showing "P" / "SP"
					if (sSelectedIndex === 1) {
						return oChangeRequest.SingleParallel === "P" || oChangeRequest.SingleParallel === "SP";
					}
					return null;
				});
				oApproverModel.setProperty("/changeReqList", arrFilteredChangeRequestList);
			},

			/**
			 * Handler for the approver create dialog
			 */
			stepTypeSelectionChange: function (oEvent) {
				let oController = this;
				let oSelKey = oEvent.getSource().getSelectedItem().getProperty("key");
				let oApproverModel = oController.getApproverModel();
				let oSTypeArr = oApproverModel.getProperty("/stepType/results");

				let oSelItem = oSTypeArr.find(function (arrD) {
					return (
						arrD.UsmdCrStype === oSelKey
					);
				});

				let actionSelectedKeys = [];
				if (oSelKey !== "Custom") {
					for (let i in oSelItem.STEPTYPETOACTION.results) {
						actionSelectedKeys.push(oSelItem.STEPTYPETOACTION.results[i].UsmdCrAction);
					}
				} else // Clear the step name and step value 
				{
					oApproverModel.setProperty("/ApprDetails/StepName", null);
					oApproverModel.setProperty("/ApprDetails/StepDesc", null);
				}

				let controlStepActions = oController._nodeEditDialogGetComponent(oController, "idStepActions");
				controlStepActions.setSelectedKeys(actionSelectedKeys);
			},

			/** Return a list of approver types. Static list of data to be return to the called. 
			 * 
			 * Returns a promise
			 */
			getApproverTypeList: function () {
				let arrApproverTypes = [{
					key: "AG",
					text: "Security Role"
				}, {
					key: "US",
					text: "User"
				}, {
					key: "SU",
					text: "Special User"
				}, {
					key: "O",
					text: "Organizational Unit"
				}, {
					key: "C",
					text: "Job"
				}, {
					key: "S",
					text: "Position"
				}];

				if(this.DynamicWorkflowDialog.DynamicNodeEditDialog.getProperty("title").includes("Dynamic")){
					arrApproverTypes.push({
						// Task 12714: add new value to approver type on workflow
						key: "NA",
						text: "No Approval Required"
					});
				}

				let promise = new Promise(function (resolve) {
					resolve(arrApproverTypes);
				});
				return promise;
			},

			/** Return the approver list based on the approver type provided. 
			 * 
			 * Returns a promise that resolves to the approver list
			 */
			getApproverList: function (sApproverType, sFilterTextInput, iMaxItemsToGet) {
				if(sApproverType!=="NA"){

					let promiseApproverList = new Promise(function (resolve) {
						resolve(null);
					});
					switch (sApproverType) {
					case "AG": //AG - Security Role
						promiseApproverList = GetListsData.getUserRolesList(this, sFilterTextInput, iMaxItemsToGet);
						break;
					case "US": //US - User
						promiseApproverList = GetListsData.getUserList(this, sFilterTextInput, iMaxItemsToGet);
						break;
					case "SU": //SU - Special User
						promiseApproverList = new Promise(function (resolve) {
							resolve({
								results: [{
									sukey: "INIT",
									sutext: "INIT"
								}
								// , {
								// 	sukey: "LAST",
								// 	sutext: "LAST"
								// }
								]
							});
						});
						break;
					case "O": //O - Organizational Unit
					case "C": //C - Job
					case "S": //S - Position
						promiseApproverList = GetListsData.getApproverPPOMEPOsitionList(this, sApproverType);
						break;
					case "BG": //BG - Background Step
						promiseApproverList = GetListsData.getBackgroundStepList(this, sApproverType);
						break;
					// Task 11631 - System method correction for background step - UI
					case "SM": //SM - System Method
						promiseApproverList = GetListsData.getSystemMethodList(this);
						break;

					default: 
						// No Actions 
						break;
					}
	
					let oReturnPromise = new Promise(function (resolve, reject) {
						promiseApproverList.then(
							function (oData) {
								// key="{approverModel>Bname}{approverModel>sukey}{approverModel>AgrName}{approverModel>Objid}"
								// text="{approverModel>McNamelas}{approverModel>sutext}{approverModel>Text}{approverModel>Stext}"/>
	
								// Transform the data to match a standard format key = key, text = text 
								let sKey = "key";
								let sText = "text";
								let arrResults = [];
								if (oData.results.length > 0) {
									if (oData.results[0].Bname) {
										sKey = "Bname";
										sText = "McNamelas";
									} else if (oData.results[0].sukey) {
										sKey = "sukey";
										sText = "sutext";
									} else if (oData.results[0].AgrName) {
										sKey = "AgrName";
										sText = "Text";
									} else if (oData.results[0].Objid) {
										sKey = "Objid";
										sText = "Stext";
									} else if (oData.results[0].BackgroundStep) {
										sKey = "BackgroundStep";
										sText = "Zdesc";
									// Task 11631 - System method correction for background step - UI
									} else if (oData.results[0].UsmdSrvName) {
										sKey = "UsmdSrvName";
										sText = "Txtlg";
									}
									
									// Load the results into the output array
									oData.results.forEach(function (arrElement) {
										arrResults.push({
											"key": arrElement[sKey],
											"text": arrElement[sText]
										});
									});
								}
	
								resolve(arrResults);
							}
						);
						promiseApproverList.catch(function(oError) {
							reject(oError);
						});
					});
	
					return oReturnPromise;
				}
			},

			/**
			 * Handler for the approver create dialog
			 */
			onApproverTypeChange: function (oEvent) {

				let oApprDetails = this.getApproverModel();
				oApprDetails.setProperty("/ApprDetails/ApprVal", "");
				oApprDetails.setProperty("/ApprDetails/ApprValToSave", "");
				oApprDetails.setProperty("/ApprRoleUserList", "");
				this._updatedApproverList(oEvent, "*", this);
			},
			/**
			 * Handler for the approver create dialog
			 */
			approverValueChanged: function (oEvent) {
				let oController = this;
				let oApproverModel = this.getApproverModel();
				let sSelAType = oApproverModel.getProperty("/ApprDetails/ApprType");
				oApproverModel.setProperty("/ApprDetails/ApprValToSave", "");

				let input = oEvent.getSource();

				/* Fix 11842 -  The key of the value is saved in the variable sFilterTextInput to search the values correctly
								A Promise was added to wait the results for the list and then validate if the save button must be enabled or not */
				let sFilterTextInput = input.getValue().toUpperCase().split(" ")[0];

				let oPromiseList = new Promise(function(resolve){
					switch (sSelAType) {
					case "AG": //AG - Security Role
						let promiseUserRoles = GetListsData.getUserRolesList(oController, sFilterTextInput, 20);
						promiseUserRoles.then(
							function (oData) {
								oApproverModel.setProperty("/ApprRoleUserList", oData);
								resolve();
							}
						);
						break;
					case "US": //US - User
						let promiseUsers = GetListsData.getUserList(oController, sFilterTextInput, 20);
						promiseUsers.then(
							function (oData) {
								oApproverModel.setProperty("/ApprRoleUserList", oData);
								resolve();
							}
						);
						break;
					case "SU": //SU - Special User
						oApproverModel.setProperty("/ApprRoleUserList", {
							results: [{
								sukey: "INIT",
								sutext: "INIT"
							}
							// , {
							// 	sukey: "LAST",
							// 	sutext: "LAST"
							// }
							]
						});
						resolve();
						break;
					case "O": //O - Organizational Unit
					case "C": //C - Job
					case "S": //S - Position
						let promisePPOMEPosittions = GetListsData.getApproverPPOMEPOsitionList(oController, sSelAType);
						promisePPOMEPosittions.then(
							function (oData) {
								oApproverModel.setProperty("/ApprRoleUserList", oData);
							}
						);
						resolve();
						break;

						default: 
						// No Actions 
						break;
					}
				});

				oPromiseList.then(function(){
					/* Bug 11432 - Entered value is validated to activate the save button */
					let sSuggestionKey = input.getValue().toUpperCase().split(" ")[0];
					let sSuggestionText = input.getSuggestionItemByKey(sSuggestionKey) ? input.getSuggestionItemByKey(sSuggestionKey).getText() : "";
					if (sSuggestionText && sSuggestionText === input.getValue()) {
						oApproverModel.setProperty("/ApprDetails/ApprVal", input.getSuggestionItemByKey(sSuggestionKey).getText());
						oApproverModel.setProperty("/ApprDetails/ApprValToSave", input.getSuggestionItemByKey(sSuggestionKey).getText());
					}else{
						oApproverModel.setProperty("/ApprDetails/ApprValToSave", "");
					}
				});
			},

			approverValueSelected: function (oEvent) {

				let oApproverModel = this.getApproverModel();
				let oSelectedItem = oEvent.getParameters().selectedItem;
				if (oSelectedItem) {
					oApproverModel.setProperty("/ApprDetails/ApprVal", oSelectedItem.getText());
					oApproverModel.setProperty("/ApprDetails/ApprValToSave", oSelectedItem.getText());
				}
			},
			/**
			 * Handler for the approver create dialog
			 */
			crStatusChangeHandler: function (oEvent) {
				// Nothing to do...
				let oSelKey = oEvent.getSource().getSelectedItem().getProperty("key");

				if (oSelKey === "Custom") {
					sap.ui.getCore().byId("connSaveCRStatusBtn").setVisible(true);
				} else {
					sap.ui.getCore().byId("connSaveCRStatusBtn").setVisible(false);
				}
			},

			/**
			 * Handler for the approver create dialog
			 */
			_updatedApproverList: function (oEvent, sFilterText, oController) {

				let oApproverModel = oController.getApproverModel();
				oApproverModel.setSizeLimit(100);
				let oApproverDetails = oApproverModel.getProperty("/ApprDetails");
				let sSelAType = oApproverDetails.ApprType;

				let sFilterTextInput = (sFilterText === "*" ? "*" : sFilterText + "*");

				switch (sSelAType) {
				case "AG": //AG - Security Role
					let promiseUserRoles = GetListsData.getUserRolesList(oController, sFilterTextInput, 20);
					promiseUserRoles.then(
						function (oData) {
							oApproverModel.setProperty("/ApprRoleUserList", oData);
						}
					);
					break;
				case "US": //US - User
					let promiseUsers = GetListsData.getUserList(oController, sFilterTextInput, 20);
					promiseUsers.then(
						function (oData) {
							oApproverModel.setProperty("/ApprRoleUserList", oData);
						}
					);
					break;
				case "SU": //SU - Special User
					let bCheck = oController._CheckForDynParallelNodes(oController);
					if (bCheck) {
						oApproverModel.setProperty("/ApprRoleUserList", {
							results: [{
								sukey: "INIT",
								sutext: "INIT"
							}]
						});
					} else {
						oApproverModel.setProperty("/ApprRoleUserList", {
							results: [{
								sukey: "INIT",
								sutext: "INIT"
							}
							// , {
							// 	sukey: "LAST",
							// 	sutext: "LAST"
							// }
							]
						});
					}
					break;
				case "O": //O - Organizational Unit
				case "C": //C - Job
				case "S": //S - Position
					let promisePPOMEPosittions = GetListsData.getApproverPPOMEPOsitionList(oController, sSelAType);
					promisePPOMEPosittions.then(
						function (oData) {
							oApproverModel.setSizeLimit(Math.max(oData.results.length, 100));
							oApproverModel.setProperty("/ApprRoleUserList", oData);
						}
					);
					break;

					default: 
					// No Actions 
					break;
			}

			},

			_CheckForDynParallelNodes: function (oController) {
				let oGraph = oController.getView().byId("graph");
				let oGraphNodes = oGraph.getNodes();
				let iNodeTypeDetails;
				let oCheckDynParallelNodes;
				let oExistingNormalNodes;
				let oCheckIsCurrentNode;
				oGraphNodes.forEach(function (oNode) {
					let iNodeType = oController._getNodeType(oController, oNode);
					if (iNodeType === DMRNode.Types.Dynamic || iNodeType === DMRNode.Types.Parallel) {
						oCheckDynParallelNodes = oNode;
						return;
					} else if (iNodeType === DMRNode.Types.Normal) {
						iNodeTypeDetails = oController._getNodeApproverDetails(oNode);
						if (iNodeTypeDetails.ApprType === "") {
							oCheckIsCurrentNode = oNode;
						} else {
							oExistingNormalNodes = oNode;
						}
					}
				});

				if (oCheckDynParallelNodes) {
					return true;
				} else if (oExistingNormalNodes) {
					return false;
				} else if (oCheckIsCurrentNode && !oExistingNormalNodes) {
					return true;
				}
				return false;
			},

			/**
			 * Selection Change Event on STep Action for System Method Multi Combobox
			 * Step Action ** is seletced by default
			 * If user selects (** - For all step actions), all other selected items are de-selected
			 * If user selects any other step action apart from **, then remove ** item from selected items
			 */
			_onSelectionChangeStepAction: function(oEvent) {
				let oApproverModel = this.getView().getModel("approverModel");
				let sSelectedStepAction = oEvent.getParameters().changedItem.getKey();
				let bSelected = oEvent.getParameters().selected;
				let arrSelectedKeys = oApproverModel.getProperty("/ApprDetails/StepActions");
				
				if(!arrSelectedKeys || arrSelectedKeys.length === 0) {
					arrSelectedKeys = ["**"];
				} else if(sSelectedStepAction === "**" && bSelected) {
					arrSelectedKeys = ["**"];
				} else {
					arrSelectedKeys = arrSelectedKeys.filter(function (item) {
						return item !== "**";
					});
				}
				oApproverModel.setProperty("/ApprDetails/StepActions", arrSelectedKeys);
			}
		});
	});