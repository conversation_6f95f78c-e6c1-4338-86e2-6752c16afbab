sap.ui.define([], function () {
	"use strict";
	return {
		IDs: {
			INIT:	"A0",
			ACTIVATE:	"91",
			DISCARD:	"92",
			COMPLETE:	"99"
		},
		
		Types: {
			Init:	10,
			Normal: 11,
			SystemMethod: 12,
			FollowUpCr:	13,
			Parallel:	20,
			ParallelChild:	21,
			Merge:	22,
			Dynamic:	31,
			Activate:	91,
			Discard:	92,
			Complete:	99
		},
		
		Icons: {
			Init:	"sap-icon://begin",
			Normal: "sap-icon://person",
			Parallel:	"sap-icon://split",
			ParallelChild:	"sap-icon://person-placeholder",
			Merge:	"sap-icon://combine",
			Dyamic: "sap-icon://provision",
			Activate:	"sap-icon://activity-assigned-to-goal", 
			Discard:	"sap-icon://delete",
			Complete:	"sap-icon://complete",
			SystemMethod: "sap-icon://fallback",
			FollowUpCr:	"sap-icon://step",
			Approver:	"sap-icon://add-employee"
		}, 
		
		Groups: {
			Init:	"01init",
			Activate:	"05actv",
			Discard:	"03actv",
			Complete:	"04comp",

			WorkFlow:	"020proc", 
			Parallel:	"021parallel",
			Dynamic:	"022dynamic"
		}
	};
});