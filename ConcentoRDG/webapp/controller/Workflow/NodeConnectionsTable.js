sap.ui.define([
	"sap/m/MessageBox",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"./DMRNode"
], function (MessageBox, DMRDataService, GetListsData, Utilities, DMRNode) {
	"use strict";
	let NodeConnectionsTable = {};
	
	/*
	*	Create and open the Edit Node Connections Fragment
	*	Called from ApproverAssignment controller
	
	*	this refers to the current js file
	*	oParentController refers to ApproverAssignment controller
	*/
	NodeConnectionsTable.openDialog = function(oParentController, arrNodeConnectionListDetails, sDialogTitle, iNodeType, sMergeNodeKey){
		
		if (!oParentController.NodeEditConnectionDialog) {
			oParentController.NodeEditConnectionDialog =
				sap.ui.xmlfragment(
					// "NodeConnectionEdit",
					"NodeConnectionsTable",
					"dmr.mdg.supernova.SupernovaFJ.view.Workflow.NodeConnectionsTable",
					oParentController);
			oParentController.getView().addDependent(oParentController.NodeEditConnectionDialog);
		}

		oParentController.NodeEditConnectionDialog.open();
		
		// store the parent controller handle 
		this.ParentController = oParentController;
			// Store the node type to the model for access in the XML 
		let oApproverModel = oParentController.getView().getModel("approverModel");
		oApproverModel.setProperty("/ConnectionEditor/nodeTypeEdited", iNodeType);
		if(sMergeNodeKey) {
			oApproverModel.setProperty("/ConnectionEditor/mergeNodeForParallel", sMergeNodeKey);
		}
		oApproverModel.setProperty("/ConnectionEditor/dialogTitle", sDialogTitle ? "Edit Action Targets for " +sDialogTitle : "Edit Action Targets");
		
		// Load all the lists 
		this.populateLists(oParentController);
		
		// Add the rows to the table 
		this.addRows(oParentController, arrNodeConnectionListDetails);
	};
	
	NodeConnectionsTable.onSelectItemSelectHandler = function(oEvent, sAction, iNodeType) {
		if(iNodeType === DMRNode.Types.Parallel){
			let sSelectedKey = oEvent.getSource().getSelectedKey();
			// Get the variable that is being read from the model 
			let sBindingPath = oEvent.getSource().getBindingPath("selectedKey");
			let sBindingContextPath = oEvent.getSource().getBindingContext("approverModel").getPath();
			
			// Update the same column that matches the sAction with this values 
			this._updateRowsWithSimilarAction_ParallelFlow(sBindingPath, sSelectedKey, sBindingContextPath, sAction);
		}
		
		return; 	
	};
	
	// eslint-disable-next-line camelcase
	NodeConnectionsTable._updateRowsWithSimilarAction_ParallelFlow = function(sBindingPath, sSelectedKey, sBindingContextPath, sAction) {
		
		// Store the node type to the model for access in the XML 
		let oApproverModel = this.ParentController.getView().getModel("approverModel");
		let arrConnectionsList = oApproverModel.getProperty("/ConnectionEditor/connectionsList");
		
		for(let i = 0; i < arrConnectionsList.length; i++){
			let oConnection = arrConnectionsList[i];
			if(oConnection.UsmdCrAction === sAction){
				let sPath = "/ConnectionEditor/connectionsList" + "/" + i + "/" + sBindingPath;
				oApproverModel.setProperty(sPath, sSelectedKey);
			}
		}
		
		return;	
	};
	
	NodeConnectionsTable.getRowItemEnabledState = function( sListHeader, iNodeEditedType){
		let bEnabled = true;
		let oApproverModel = this.getView().getModel("approverModel");
		let arrConnectionsList = oApproverModel.getProperty("/ConnectionEditor/connectionsList");
		arrConnectionsList.sort(function(a, b){
		    let sListHeaderA=a.ListHeader.toLowerCase();
		    let sListHeaderB=b.ListHeader.toLowerCase();
		    if (sListHeaderA < sListHeaderB){ //sort string ascending
		        return -1 ;
		    }
		    if (sListHeaderA > sListHeaderB){
		        return 1;
		    }
		    return 0 ; //default return value (no sorting)
		});
       
		if((iNodeEditedType === DMRNode.Types.Parallel) && (arrConnectionsList[0].ListHeader !== sListHeader && arrConnectionsList[1].ListHeader !== sListHeader)){
			bEnabled = false;
		}
		return bEnabled;	
	};
	
	NodeConnectionsTable.getGroupHeader = function(oGroup) {
		return new sap.m.GroupHeaderListItem({
				title: oGroup.key,
				upperCase: true
			});
	};
	
	/*
	*	Form the list structure based on graph line details
	*	Called from ApproverAssignment controller
	
	*	this refers to the current js file
	*	oParentController refers to ApproverAssignment controller
	*   arrNodeConnectionListDetails - Array consisting of the graph line details to be put into list structure
	*/
	NodeConnectionsTable.addRows = function(oParentController, arrNodeConnectionListDetails) {
		let oApproverModel = oParentController.getView().getModel("approverModel");
	
		oApproverModel.setProperty("/ConnectionEditor/connectionsList", arrNodeConnectionListDetails);
	};
	
	/*
	*	Populate lists for Priority, CR Reason and CR Reason of Rejection
	*	Called from ApproverAssignment controller
	
	*	this refers to the current js file
	*	oParentController refers to ApproverAssignment controller
	*/
	NodeConnectionsTable.populateLists = function(oParentController) {
		let oApproverModel = oParentController.getView().getModel("approverModel");
		
		let sUsmdCreqType = oApproverModel.getProperty("/CRTypeDetails/CrTypeName");
		
		let promiseCRPriorityList = GetListsData.getCRPriorityList(oParentController);
		promiseCRPriorityList.then(
			function (arrCRPriorityList) {
				oApproverModel.setProperty("/ConnectionEditor/CRPriorityList", arrCRPriorityList);
			}
		);
		
		let promiseCRReasonList = GetListsData.getCRReasonList(oParentController, sUsmdCreqType);
		promiseCRReasonList.then(
			function (arrCRReasonList) {
				oApproverModel.setProperty("/ConnectionEditor/CRReasonList", arrCRReasonList);
			}
		);
		
		let promiseCRRejectReasonList = GetListsData.getCRRejectReasonList(oParentController, sUsmdCreqType); 
		promiseCRRejectReasonList.then(
			function (arrCRRejectReasonList) {
				oApproverModel.setProperty("/ConnectionEditor/CRRejectReasonList", arrCRRejectReasonList);
			}
		);
	};
	
	/*
	*	Selection change event when user changes the CR Status Select input
	*	Change the Status Description based on the CR Status selected
	*	Open Step Status Fragment if user selects on Custom Status
	*	Called from the view as an Event handler
	
	*	this refers to the ApproverAssignment controller
	*/
	NodeConnectionsTable.onStatusChange = function(oEvent, sAction, iNodeType) {
		// get the view 
		let oController = this;
		
		// Get the model 
		let oApproverModel = oController.getApproverModel();

		// Get the binding path for the item. Path to the row on which the item exists 
		let sBindingPathContext = oEvent.getSource().getBindingContext("approverModel").getPath();
		let sBindingPath = oEvent.getSource().getBindingPath("selectedKey");
		let sSelectedKey = oEvent.getSource().getSelectedKey();
		
		let oSelectedCrStatus = oApproverModel.getProperty("/crStatus").results.find(function (el) {
			return (el.UsmdCreqStatus === sSelectedKey);
		});
		// If nothing is selected 
		if(!oSelectedCrStatus){
			oSelectedCrStatus = {
				UsmdCreqStatus: "",
				UsmdCreqObmain: ""
			};
		}
		
		// Check if the selected key is Custom. Open the create status dialog ... else set the CreqObmain 
		if(oSelectedCrStatus.UsmdCreqStatus === "Custom"){
			if (!oController.StepStatusCreateDialog) {
				oController.StepStatusCreateDialog =
					sap.ui.xmlfragment(
						"StepStatusCreate",
						"dmr.mdg.supernova.SupernovaFJ.view.Workflow.StepStatusCreate",
							oController);
						oController.getView().addDependent(oController.StepStatusCreateDialog);
			}

			oController.StepStatusCreateDialog.open();
			oApproverModel.setProperty("/StepStatusDetailsFragment", {
				BindingPath: sBindingPathContext,
				ActionOnLine: sAction,
				NodeType: iNodeType 
			});

			oApproverModel.setProperty(sBindingPathContext + "/" + "CRStatus", "");
			oApproverModel.setProperty(sBindingPathContext + "/" + "CR_OBMAIN", "");
		} else {
			oApproverModel.setProperty(sBindingPathContext + "/" + "CR_OBMAIN", oSelectedCrStatus.UsmdCreqObmain);
			// if the type is Parallel .. update the rest of rows 
			if(iNodeType === DMRNode.Types.Parallel){
				this._updateRowsWithSimilarAction_ParallelFlow(sBindingPath, sSelectedKey, undefined, sAction);
				this._updateRowsWithSimilarAction_ParallelFlow("CR_OBMAIN", oSelectedCrStatus.UsmdCreqObmain, undefined, sAction);
			}
		}
	};
	
	/**
	 * When create button for STep Status is clicked
	 * Create a CR Status in the system
	 * Refresh the CR Status list to include the newly created Status
	 * Select the newly created Status as the status for the Action
	 * Close the Step Status Create Dialog
	 */
	NodeConnectionsTable.stepStatusCreateClicked = function() {
		let oController = this;
		let oApproverModel = this.getApproverModel();
		let oCrTypeDetails = oApproverModel.getProperty("/CRTypeDetails");
		
		let oStepStatusDetails = oApproverModel.getProperty("/StepStatusDetailsFragment");

		// Input validation .. 
		if( oStepStatusDetails.UsmdCreqStatus.length !== 2) {
			oApproverModel
				.setProperty("/StepStatusDetailsFragment/statusValueState", sap.ui.core.ValueState.Error);
			oApproverModel
				.setProperty("/StepStatusDetailsFragment/statusValueStateText", "Status should be of length 2");	
			return;
		} else if (oStepStatusDetails.statusValueState === sap.ui.core.ValueState.Error){
			oApproverModel
				.setProperty("/StepStatusDetailsFragment/statusValueState", sap.ui.core.ValueState.None);
			oApproverModel
				.setProperty("/StepStatusDetailsFragment/statusValueStateText", "");	
		}
		
		// Create the service packet
		let oServiceData = {
			UsmdCreqStatus: oStepStatusDetails.UsmdCreqStatus,
			UsmdCreqObmain: oStepStatusDetails.UsmdCreqObmain,
			UsmdCreqObmainTxt: "",
			Txtmi: oStepStatusDetails.Txtmi,
			Trkorr: oCrTypeDetails.Transport ? oCrTypeDetails.Transport : oCrTypeDetails.CustomizingTr
		};
		
		let oCustomizingTransportPromise = oController._selectTransportPackage(true, false, false, false);
		oCustomizingTransportPromise.then((oResponseSelected) => {
			oServiceData.Trkorr = oResponseSelected.customizingTransport;
		
			let promiseCreateStatus = new Promise(function(resolve, reject) {
				(new DMRDataService(
					oController,
					"WF_STEPSTATUS",
					"/CRSTATUSSet",
					"stepStatus",
					"/", // Root of the received data
					"Create Step Status"
				)).saveData(
					false,
					oServiceData,
					null, {
						success: {
							fCallback: function (oParams, oResponseData) {
								resolve(oResponseData);
							},
							oParams: oController
						},
						error: {
							fCallback: function (oParams, oErrorData) {
								reject(oErrorData);
							},
							oParams: oController
						}
					}
				);
			});
					
			promiseCreateStatus.then(
				function(oResponseData) {
					Utilities.showPopupAlert("Change Request Status "+oResponseData.UsmdCreqStatus+" created", MessageBox.Icon.INFORMATION, "Step status");
	
					// Refresh the step status items list to include the new status
					let promiseCRStatus = 
						GetListsData.getWFConfig(oController, "/CRSTATUSSet");
					promiseCRStatus.then(
						function (oData) {
							oApproverModel.setProperty("/dynamic/lists/CR_STATUS", oData ? oData.results : []);
							oApproverModel.setProperty("/crStatus", oData);
							oApproverModel.refresh();
						}									
					);

					//Validate if CR_STATUS property is ther if is ok, then remove entry from postion 0 after reload model
					let sStatus = oApproverModel.getProperty(oStepStatusDetails.BindingPath + "/" + "CR_STATUS");
					if(sStatus) {
						oApproverModel.setProperty(oStepStatusDetails.BindingPath + "/" + "CR_STATUS", undefined);
					}

					oApproverModel.setProperty(oStepStatusDetails.BindingPath + "/" + "CRStatus", oStepStatusDetails.UsmdCreqStatus);
					oApproverModel.setProperty(oStepStatusDetails.BindingPath + "/" + "CR_OBMAIN", oStepStatusDetails.UsmdCreqObmain);
					// if the type is Parallel .. update the rest of rows 
					if(oStepStatusDetails.NodeType === DMRNode.Types.Parallel){
						oController
							.NodeConnectionsTableDialog
							._updateRowsWithSimilarAction_ParallelFlow(
								"CRStatus", oStepStatusDetails.UsmdCreqStatus, undefined, oStepStatusDetails.ActionOnLine);
						oController
							.NodeConnectionsTableDialog
							._updateRowsWithSimilarAction_ParallelFlow(
								"CR_OBMAIN", oStepStatusDetails.UsmdCreqObmain, undefined, oStepStatusDetails.ActionOnLine);
					}
					if (oController.DynamicWorkflowDialog.CreateCRDialog){
						oController.DynamicWorkflowDialog.CreateCRDialog.close();
					}else{	
					oController.StepStatusCreateDialog.close();
				    }
				},
				function(oErrorData) {
					let message = jQuery.parseJSON(oErrorData.responseText).error.message.value;
					let promiseShowPopupAlert = Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");
					promiseShowPopupAlert.then(function() {
							oApproverModel.setProperty(oStepStatusDetails.BindingPath + "/" + "CRStatus", "");
							oApproverModel.setProperty(oStepStatusDetails.BindingPath + "/" + "CR_OBMAIN", "");
							oApproverModel.setProperty(oStepStatusDetails.BindingPath + "/" + "CRStatus", "");
							oApproverModel.refresh();
					});
					
				}
			);	
		});
	};
			
	/**
	 * When Step Status Creation Dialog is closed
	 * Refresh the CR Status - Custom selected for the action
	 * Close the Dialog
	 */
	NodeConnectionsTable.stepStatusCancelClicked = function() {
		if (this.DynamicWorkflowDialog.CreateCRDialog){
			this.DynamicWorkflowDialog.CreateCRDialog.close();
		}else{			
	 	this.StepStatusCreateDialog.close();
		}
	 };
	
	/*
	*	Selection change event when user changes the CR Reason Select input
	*	Open ReasonCreate Fragment if user selects on Custom Reason
	*	Called from the view as an Event handler
	
	*	this refers to the ApproverAssignment controller
	*/
	NodeConnectionsTable.onReasonChange = function(oEvent) {
		// get the view 
		let oController = this;
		
		// Get the model 
		let oApproverModel = oController.getApproverModel();
		
		oApproverModel.setProperty("/ReasonDetailsFragment", {});

		// Get the binding path for the item. Path to the row on which the item exists 
		let sBindingPathContext = oEvent.getSource().getBindingContext("approverModel").getPath();
		let sSelectedKey = oEvent.getSource().getSelectedKey();
		
		oApproverModel.setProperty("/ReasonDetailsFragment", {
			reasonType: "CRReason",	
			BindingPath: sBindingPathContext
		});
		
		// Check if the selected key is Custom. Open the create status dialog ... else set the CreqObmain 
		if(sSelectedKey === "Custom"){
			oController.NodeConnectionsTableDialog.openReasonCreateDialog(oController);

			oApproverModel.setProperty(sBindingPathContext + "/" + "CRReason", "");
		} else {
			oApproverModel.setProperty(sBindingPathContext + "/" + "CRReason", sSelectedKey);
		}
	};
	
	/*
	*	Selection change event when user changes the CR Rejection Reason Select input
	*	Open ReasonCreate Fragment if user selects on Custom Rejection Reason
	*	Called from the view as an Event handler
	
	*	this refers to the ApproverAssignment controller
	*/
	NodeConnectionsTable.onRejectionReasonChange = function(oEvent) {
		// get the view 
		let oController = this;
		
		// Get the model 
		let oApproverModel = oController.getApproverModel();
		
		oApproverModel.setProperty("/ReasonDetailsFragment", {});

		// Get the binding path for the item. Path to the row on which the item exists 
		let sBindingPathContext = oEvent.getSource().getBindingContext("approverModel").getPath();
		let sSelectedKey = oEvent.getSource().getSelectedKey();
		
		oApproverModel.setProperty("/ReasonDetailsFragment", {
			reasonType: "CRRejectReason",
			BindingPath: sBindingPathContext
		});
		
		if(sSelectedKey === "Custom"){
			oController.NodeConnectionsTableDialog.openReasonCreateDialog(oController);

			oApproverModel.setProperty(sBindingPathContext + "/" + "CRRejectionReason", "");
		} else {
			oApproverModel.setProperty(sBindingPathContext + "/" + "CRRejectionReason", sSelectedKey);
		}
	};
	
	/*
	*	Initialize the Reason Fragment when Custom Rejection Reason or Custom Reason is clicked
	
	*	oParentController refers to the ApproverAssignment controller
	*/
	NodeConnectionsTable.openReasonCreateDialog = function(oParentController) {
		if (!oParentController.ReasonCreateDialog) {
			oParentController.ReasonCreateDialog =
				sap.ui.xmlfragment(
					"StepStatusCreate",
					"dmr.mdg.supernova.SupernovaFJ.view.Workflow.ReasonCreate",
						oParentController);
					oParentController.getView().addDependent(oParentController.ReasonCreateDialog);
		}

		oParentController.ReasonCreateDialog.open();
	};
	
	/*
	*	Create Button Clicked by the user.
	*	Create the Reason/Reason for Rejection. 
	*	Call the lookup service to fetch the updated list
	*	Populate the UI field in the correct row with the created entry
	
	*	this refers to the ApproverAssignment controller
	*/
	NodeConnectionsTable.reasonCreateClicked = function() {
		
		let oController = this;
		let promiseShowPopupAlert;
		let cErrorFlag;
		let oServiceData;
		let oApproverModel = this.getApproverModel();
		let oCrTypeDetails = oApproverModel.getProperty("/CRTypeDetails");
		let sUsmdCreqType = oCrTypeDetails.CrTypeName;
		let oReasonDetails = oApproverModel.getProperty("/ReasonDetailsFragment");
		let sReasonType = oReasonDetails.reasonType;
		let sServiceName;
		let sEntityName;
		
		if (sReasonType === "CRReason") {
			
			if ( oReasonDetails.UsmdReason.length !== 2 ) {
				cErrorFlag = "X";
			}
			
			// Create the service packet for creation of CR Reason
			oServiceData = {
				Reason: oReasonDetails.UsmdReason,
				Usmdcreqtype: sUsmdCreqType,
				Desc: oReasonDetails.Desc,
				Trkorr: oCrTypeDetails.Transport ? oCrTypeDetails.Transport : oCrTypeDetails.CustomizingTr
			};
			
			
			sServiceName = "CRREASONLOOKUP";
			sEntityName = "/CRREASONSet";
			
		} else if (sReasonType === "CRRejectReason") {
			
			if ( oReasonDetails.UsmdReasonRej.length !== 2 ) {
				cErrorFlag = "X";
			}
			
			// Create the service packet for creation of CR Reason
			oServiceData = {
				Rejectreason: oReasonDetails.UsmdReasonRej,
				Usmdcreqtype: sUsmdCreqType,
				Desc: oReasonDetails.Desc,
				Trkorr: oCrTypeDetails.Transport ? oCrTypeDetails.Transport : oCrTypeDetails.CustomizingTr
			};
			
			sServiceName = "CRREJECTREASONLOOKUP";
			sEntityName = "/CR_REJECTREASONSet";
		}
		
		// Input validation .. 
		if( cErrorFlag === "X") {
			oApproverModel
				.setProperty("/ReasonDetailsFragment/reasonValueState", sap.ui.core.ValueState.Error);
			oApproverModel
				.setProperty("/ReasonDetailsFragment/reasonValueStateText", "Status should be of length 2");	
			return;
		} else if (oReasonDetails.reasonValueState === sap.ui.core.ValueState.Error){
			oApproverModel
				.setProperty("/ReasonDetailsFragment/reasonValueState", sap.ui.core.ValueState.None);
			oApproverModel
				.setProperty("/ReasonDetailsFragment/reasonValueStateText", "");
		}
		
		let oCustomizingTransportPromise = oController._selectTransportPackage(true, false, false, false); 
		oCustomizingTransportPromise.then((oResponseSelected) => {
			
			oServiceData.Trkorr = oResponseSelected.customizingTransport;
			let promiseCreateReason = new Promise(function(resolve, reject) {
				(new DMRDataService(
					oController,
					sServiceName,
					sEntityName,
					"ReasonCreate",
					"/", // Root of the received data
					"Create Reason"
				)).saveData(
					false,
					oServiceData,
					null, {
						success: {
							fCallback: function (oParams, oResponseData) {
								resolve(oResponseData);
							},
							oParams: oController
						},
						error: {
							fCallback: function (oParams, oErrorData) {
								reject(oErrorData);
							},
							oParams: oController
						}
					}
				);
			});
			
			promiseCreateReason.then(
				function(oResponseData) {
				
					if (sReasonType === "CRReason") {
						
						// Refresh the CR Reason items list to include the new status
						let promiseCRReasonList = GetListsData.getCRReasonList(oController, sUsmdCreqType);
						promiseCRReasonList.then(
							function (arrCRReasonList) {
								oApproverModel.setProperty("/ConnectionEditor/CRReasonList", arrCRReasonList);
								oApproverModel.refresh();
							}
						);
						
						oApproverModel.setProperty(oReasonDetails.BindingPath + "/" + "CRReason", oReasonDetails.UsmdReason);
						
						promiseShowPopupAlert =
						Utilities.showPopupAlert("Change Request Reason "+oResponseData.Reason+" created", MessageBox.Icon.INFORMATION, "Change Request Reason");
						promiseShowPopupAlert.then(function () {
						});
					} else if (sReasonType === "CRRejectReason") {
						// Refresh the CR Rejection Reason items list to include the new status
						let promiseCRRejectReasonList = GetListsData.getCRRejectReasonList(oController, sUsmdCreqType); 
						promiseCRRejectReasonList.then(
							function (arrCRRejectReasonList) {
								oApproverModel.setProperty("/ConnectionEditor/CRRejectReasonList", arrCRRejectReasonList);
								oApproverModel.refresh();
							}
						);
						
						
						oApproverModel.setProperty(oReasonDetails.BindingPath + "/" + "CRRejectionReason", oReasonDetails.UsmdReasonRej);
			
						
						promiseShowPopupAlert =
						Utilities.showPopupAlert("Change Request Reason for Rejection "+oResponseData.Rejectreason+" created", MessageBox.Icon.INFORMATION, "Change Request Reason");
						promiseShowPopupAlert.then(function () {
						});
					
					}
					oController.ReasonCreateDialog.close();
				},
				function(oErrorData) {
					let message = jQuery.parseJSON(oErrorData.responseText).error.message.value;
					promiseShowPopupAlert = Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");
					promiseShowPopupAlert.then(function() {
							if (sReasonType === "CRReason") {
								oReasonDetails.UsmdReason = "";
							} else if (sReasonType === "CRRejectReason") {
								oReasonDetails.UsmdReasonRej = "";
							}
							
							oReasonDetails.Desc = "";
							oApproverModel.refresh();
						}, function(){
							
						});
				}
			);
		});
	};
	
	/*
	*	Button Click Event when user creates on Cancel
	
	*	this refers to the ApproverAssignment controller
	*/
	NodeConnectionsTable.reasonCancelClicked = function() {
		this.ReasonCreateDialog.close();
	};
	
	/*
	*	Button Click Event when user selects a row and clicks on Add button
	*	A new row gets inserted on the same level as the selected row index
	*	Called from the view as an Event handler
	
	*	this refers to the ApproverAssignment controller
	*/
	NodeConnectionsTable.onAddRowPressed = function() {
		let oApproverModel = this.getView().getModel("approverModel");
		let oNodeConnectionList = sap.ui.getCore().byId("NodeConnectionsTable--nodeConnectionList");
		let arrList = oApproverModel.getProperty("/ConnectionEditor/connectionsList");
		
		let sSelectedListItemPath = oNodeConnectionList.getSelectedContextPaths()[0];
		
		let oSelectedItem = oApproverModel.getProperty(sSelectedListItemPath);
		
		if(!oSelectedItem){
			let promiseShowPopupAlert = Utilities.showPopupAlert("Please select a row.", MessageBox.Icon.ERROR, "Error");
			promiseShowPopupAlert.then(function() {
				return;
			}, function(){
				
			});
			
			return;
		}
		
		if(oSelectedItem.ApproverType === "BG"){
			Utilities.showPopupAlert("Rows can not be added under non user agent steps", MessageBox.Icon.ERROR, "Error");
			
			return;
		}
			
		let oNewChildRow = {
			ApproverType: oSelectedItem.ApproverType,
			ApproverValue: oSelectedItem.ApproverValue,
			AppStep: oSelectedItem.AppStep,
			CRPriority: undefined,
			CRReason: undefined,
			CRRejectionReason: undefined,
			CRStatus: undefined,
			CR_OBMAIN: undefined,
			RowNum: oSelectedItem.RowNum,
			HeaderTextForList: oSelectedItem.HeaderTextForList,
			ListHeader: oSelectedItem.ListHeader,
			TargetNode: undefined,
			UsmdActDesc: oSelectedItem.UsmdActDesc,
			UsmdCrAction: oSelectedItem.UsmdCrAction,
			Emailtemp: undefined,
			AddlEmails: undefined 
		};
		
		arrList.push(oNewChildRow);
		oApproverModel.setProperty("/ConnectionEditor/connectionsList", arrList);
			

	};
	
	/*
	*	Button Click Event when user selects a row and clicks on Delete button
	*	Delete the selected row from the List structure
	*	Called from the view as an Event handler
	
	*	this refers to the ApproverAssignment controller
	*/
	NodeConnectionsTable.onDeleteRowPressed = function() {
		let oApproverModel = this.getView().getModel("approverModel");
		let oNodeConnectionList = sap.ui.getCore().byId("NodeConnectionsTable--nodeConnectionList");
		let arrList = oApproverModel.getProperty("/ConnectionEditor/connectionsList");
		
		let sSelectedListItemPath = oNodeConnectionList.getSelectedContextPaths()[0];
		let iChildRow = parseInt(sSelectedListItemPath.substring(sSelectedListItemPath.lastIndexOf("/") + 1), 10);
		
		arrList.splice(iChildRow, 1);
		oApproverModel.setProperty("/ConnectionEditor/connectionsList", arrList);
	};
	
	/*
	*	Button Click Event when user selects a row and clicks on Save Button
	*	Delete all existing graph lines from the current node
	*	Create a graph line and add additional data (CR Priority, CR Reason, CR Reason for Rejection) and add it to the graph 
	*	Called from the view as an Event handler
	
	*	this refers to the ApproverAssignment controller
	*/
	NodeConnectionsTable.editConnectionsSaveClicked = function(oEvent, oParentController) {
		// Bug 12573 - Condition added to identify if the method is being called from other function or from a button
		let oController = oParentController ? oParentController : this;
		let oView = oController.getView();
		let oApproverModel = oController.getView().getModel("approverModel");
		let arrNodeActionListDetails = [];
		
		let arrChildren = oApproverModel.getProperty("/ConnectionEditor/connectionsList");
			
			arrChildren.forEach(function(child){ 
				let oNodeActionDetails = {
					ApproverType: child.ApproverType,
					ApproverValue: child.ApproverValue,
					AppStep: child.AppStep,
					Apprdesc: child.ListHeader,
					CRPriority: child.CRPriority,
					CRReason: child.CRReason,
					CRRejectionReason: child.CRRejectionReason,
					CRStatus: child.CRStatus,
					CR_OBMAIN: child.CR_OBMAIN,
					HeaderTextForList: child.HeaderTextForList,
					RowNum: child.RowNum,
					Sign: child.Sign,
					TargetNode: child.TargetNode,
					UsmdActDesc: child.UsmdActDesc,
					UsmdCrAction: child.UsmdCrAction,
					Emailtemp: child.Emailtemp,
					AddlEmails: child.AddlEmails
				};
				
				arrNodeActionListDetails.push(oNodeActionDetails);
			});
		
		oApproverModel.setProperty("/EditNodeActionsDialogList", arrNodeActionListDetails);

		// Get the graph object 
		let oGraph = oView.byId("graph");

		// Get the Edit Connections Dialog Model 
		let oEditNodeActionsDialogList =
			oController.getApproverModel().getProperty("/EditNodeActionsDialogList");

		// Get the key for the node being edited 
		let sNodeKey = oController.nodeBeingEdited.getKey();
		let iNodeType = oController._getNodeType(oController, oController.nodeBeingEdited);

		// Delete all the lines starting from this node
		let arrLinesFromNode = oGraph.getLines().filter(function(line){
			let sLineFrom = line.getFrom();
			if(sLineFrom === sNodeKey){
				return true;
			}
			return false;
		});

		// Bug 12698 - Duplicate rows on saving connections for Follw up CR. Rework on Bug 12664
		arrLinesFromNode.forEach(function(line){
			oGraph.removeLine(line);
			line.destroy();                         	
		});

		// Go through each action setting and store the same to the graph
		oEditNodeActionsDialogList.forEach(function (action, index, array) {
			// Perform an action only if a target node was selected 
			if (action.TargetNode) { 
				
				// Create and add/ replace a line in the graph
				oController.createNewLineForGraphNode(
					oGraph, oController, action.ApproverType, action.ApproverValue, sNodeKey, action.TargetNode, action.UsmdCrAction, 
					parseInt(action.RowNum, 10), action.CRStatus, action.CR_OBMAIN, true, 
					action.CRPriority, action.CRReason, action.CRRejectionReason, action.Sign, action.AppStep, action.Emailtemp, action.AddlEmails, action.Apprdesc);

				// Add the Action and Target Node Information to the Dynamic Node data if it is a dynamic node. 
				if(iNodeType === DMRNode.Types.Dynamic || iNodeType === DMRNode.Types.Parallel){
					let bReplaceContent = true;
					// Check if the current row number has been reached for the first time, if yes clean the connection info bReplaceContent = true
					for(let i = 0; i < array.length; i++){ 
						if((array[i].RowNum === action.RowNum) && (index > i)){
							bReplaceContent = false;
							break;
						}
					}
					// let oDynamicNodeDetails = oController._getCustomeDataValueForDynamicNode(oController, this);
					let oDynamicNodeDetails = oController._getNodeApproverDetails(oController.nodeBeingEdited).DynamicRuleList;
					// If the node is of type Dynamic, update the connection details.
					oDynamicNodeDetails = oController.DynamicWorkflowDialog.updateConnectionData(
							oController, oController.nodeBeingEdited, oDynamicNodeDetails, action, bReplaceContent);
					oController._addAttributeToNode(oController, oController.nodeBeingEdited, "DynamicRuleList", oDynamicNodeDetails);
				}
			}

		});
		// Task 12336 - WF Graph Layout Changes - Hide All Duplicate Lines from FROM node to TO Node on Save
		oController.hideDuplicateLines(oController);
		//Bug 12573 - Condition added to know if the method is being called from a button or from other method
		if (!oParentController) {
			oController.NodeEditConnectionDialog.close();
		}
	};
	
	/*
	*	Button Click Event when user selects a row and clicks on Save Button
	*	Closes the Node Edit Connections Fragment without saving any changes
	*	Called from the view as an Event handler
	
	*	this refers to the ApproverAssignment controller
	*/
	NodeConnectionsTable.editConnectionsCancelClicked = function() {
		this.NodeEditConnectionDialog.close();
	};
	
	NodeConnectionsTable.onTemplateSelectSearchListCreated = function(oEvent) {
		let oApproverModel = this.getView().getModel("approverModel");
		let dataModel = oApproverModel.getProperty("/CRTypeDetails/DataModel");
		let comp = oEvent.getParameter("component");	
		// comp.attachSelectionChange(this.NodeConnectionsTableDialog.onEmailTemplateSelected, this);
		comp.setDataModel(dataModel);
		// comp.setComboBoxData();
		let sPath = oEvent.getSource().getBindingContext("approverModel").getPath();
		comp.setBindingContext(new sap.ui.model.Context(oApproverModel, sPath));
	};
	
	NodeConnectionsTable.openAdditionalEmailsForm = function(oEvent){
		let oApproverModel = this.getView().getModel("approverModel");
		let sPath = oEvent.getSource().getBindingContext("approverModel").getPath();
		let arrNodeConnectionListDetails = oApproverModel.getProperty(sPath).AddlEmails;
		if(!arrNodeConnectionListDetails){
			oApproverModel.setProperty(sPath+"/AddlEmails", "");
		}
		arrNodeConnectionListDetails = oApproverModel.getProperty(sPath+"/AddlEmails");
		this.AdditionalEmailsTableDialog.openDialog(this, arrNodeConnectionListDetails, sPath+"/AddlEmails");
	};
	
	NodeConnectionsTable.onEmailTemplateSelected = function(oEvent){
		let sPath = oEvent.getSource().getBindingContext().getPath();
		let sSelectedKey = oEvent.getParameters().selectedItem.getKey();
		let oApproverModel = oEvent.getSource().getBindingContext().getModel();
		let iNodeTypeEdited =oApproverModel.getProperty("/ConnectionEditor/nodeTypeEdited");
		let sRowPath = sPath.substring(0, sPath.lastIndexOf("/") + 1);
		let sAction = oApproverModel.getProperty(sRowPath+"UsmdCrAction");
		if(iNodeTypeEdited === DMRNode.Types.Parallel){
			this.NodeConnectionsTableDialog._updateRowsWithSimilarAction_ParallelFlow(sPath, sSelectedKey, undefined, sAction);
		}
	};

	/**
	 * Change Event on Target Node
	 * If Nodetype is for Parallel or Parallel Child and Target Node is Merge Node, clear the email template and additional email for that row
	 * Email template field is disabled in the component
	 */
	NodeConnectionsTable.onTargetStateChange = function(oEvent) {
		let oApproverModel = this.getView().getModel("approverModel");
		let sPath = oEvent.getSource().getBindingContext("approverModel").getPath();
		let iNodeTypeEdited = oApproverModel.getProperty("/ConnectionEditor/nodeTypeEdited");
		let sMergeNodeKey = oApproverModel.getProperty("/ConnectionEditor/mergeNodeForParallel");
		let sTargetNode = oApproverModel.getProperty(sPath + "/TargetNode");
		if(sTargetNode === sMergeNodeKey && ( iNodeTypeEdited === DMRNode.Types.Parallel || iNodeTypeEdited === DMRNode.Types.ParallelChild )) {
			oApproverModel.setProperty(sPath + "/Emailtemp", undefined);
			oApproverModel.setProperty(sPath + "/AddlEmails", undefined);
		}
	};
	
	return NodeConnectionsTable;
}); 