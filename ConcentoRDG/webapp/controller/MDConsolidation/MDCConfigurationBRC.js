sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "dmr/mdg/supernova/SupernovaFJ/libs/DataService",
    "sap/m/MessageBox",
	"dmr/mdg/supernova/SupernovaFJ/model/ModelMessages"
], function (GetListsData, Utilities, DMRDataService, MessageBox, ModelMessages) {
	"use strict";

    let oMDCConfigurationBRC = {};

    oMDCConfigurationBRC.onTabChange = function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oTab = oEvent.getSource();
        let sSelectedTab = oTab.getSelectedKey();
        let oController = this;
        let arrConfigDetails = [];
        let oConfigDetails = {};
        let oNewConfigData = {};
        let oTemp;

        // Show a message asking the user to confirm tab change. Exit if cancel 
		let promise = new Promise(function (resolve, reject) {
			let promiseShowPopupAlert = Utilities.showPopupAlert("Any unsaved change will be discarded. Continue?", MessageBox.Icon.WARNING,
				"Continue?", [sap.m.MessageBox.Action.YES, sap.m.MessageBox.Action.NO]);
			promiseShowPopupAlert.then(function () {
				resolve();
			}, function () {
				reject();
			});
		});

        promise.then( async function () {

            if(sSelectedTab==="orderBRC"){
                let arrBRCRRules;

                try {
                    arrConfigDetails = await GetListsData.getConfigurationBRCDetails(oController, oConfigData.BoType, oConfigData.SelectedConfiguration);
    
                    if (arrConfigDetails && arrConfigDetails.length) {
                        oConfigDetails = arrConfigDetails[0];
                    }
    
                } catch (oError) {
                    if (oError.statusCode !== "404") {
                        Utilities.showPopupAlert(
                            "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                            MessageBox.Icon.ERROR, 
                            "Error"
                        );
                    }
                }
                // Bug 12943 - Added parallel properties for validation
                oNewConfigData = {
                    BoType: oConfigData.BoType,
                    OperationType: oConfigData.OperationType,
                    StepType: oConfigData.StepType,
                    SelectedRow: oConfigData.SelectedRow, 
                    SelectedConfiguration: oConfigData.SelectedConfiguration,
                    ConfigId: oConfigDetails.ConfigId,
                    ConfigIdValueState: ( oConfigDetails.ConfigId ? "None" : "Error" ),
                    ConfigIdValueStateText: ( oConfigDetails.ConfigId ? "" : "Configuration ID is mandatory" ),
                    Description: oConfigDetails.Description,
                    DescriptionValueState: ( oConfigDetails.Description ? "None" : "Error" ),
                    DescriptionValueStateText: ( oConfigDetails.Description ? "" : "Configuration Description is mandatory" ),
                    Parallel: oConfigDetails.Parallel,
                    ParallelValueState: "None",
                    ParallelValueStateText: "",
                    QueuePrefix: oConfigDetails.QueuePrefix,
                    BRCORDER2BRCSOURCESNAV: [],
                    BRCORDER2BRCTABLESNAV: [],
                    BRCORDER2BRCFIELDSNAV: [],
                    BRCORDERTABLE: [],
                    BRCORDERTABLEFIELDS: [],
                    BRCTableRules: [],
                    BRCFieldRules: [],
                    BRCAll: [],
                    visDeleteButton: oConfigData.OperationType === "C" ? false : true,
                    visChangesText: false

                };

                try {
                    let arrBRCAllData = await GetListsData.getConfigurationBRCDetails(oController);
                    oNewConfigData.BRCAll = arrBRCAllData;
    
                } catch (oError) {
                    if (oError.statusCode !== "404") {
                        Utilities.showPopupAlert(
                            "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                            MessageBox.Icon.ERROR, 
                            "Error"
                        );
                    }
                }
    
                // Bug 12943 - table dropdown for table should have the "*", for fields should not
                try {
                    let arrTableData = await GetListsData.getConfigurationBRCTableSelect(oController, oConfigData.BoType);
                    oNewConfigData.BRCORDERTABLEFIELDS.push(...arrTableData);
                    oNewConfigData.BRCORDERTABLE.push(...arrTableData);
                    oNewConfigData.BRCORDERTABLE.unshift({
                        TableName: "*",
                        TableDescription: "For all Tables"
                    });
    
                } catch (oError) {
                    if (oError.statusCode !== "404") {
                        Utilities.showPopupAlert(
                            "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                            MessageBox.Icon.ERROR, 
                            "Error"
                        );
                    }
                }

                try {
                    arrBRCRRules = await GetListsData.getBRCRuleSet(oController);
                    
                    let arrTableRules = arrBRCRRules.filter(element => {
                        return element.ForTable === true;
                    });
                    oNewConfigData.BRCTableRules = arrTableRules;
    
                    let arrFieldRules = arrBRCRRules.filter(element => {
                        return element.ForField === true;
                    });
                    oNewConfigData.BRCFieldRules = arrFieldRules;
                    
                } catch (oError) {
                    if (oError.statusCode !== "404") {
                        Utilities.showPopupAlert(
                            "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                            MessageBox.Icon.ERROR, 
                            "Error"
                        );
                    }
                }
                
                // Bug 12943 - check the condition for the table, if table is "" then change the value for "*" and add a description
                oConfigDetails.BRCORDER2BRCSOURCESNAV.results.forEach(element => {
                    oTemp = {...element,
                            TableSelectValueState: "None",
                            TableSelectValueStateText: "",
                            SourceSystemSelectValueState: ( element.SourceSystem ? "None" : "Error" ),
                            SourceSystemSelectStateText: ( element.SourceSystem ? "" : "Source System is Mandatory")
                    };

                    if(element.Table === ""){
                        oTemp.Table = "*";
                        oTemp.TableDescription = "For all Tables";
                    } else {
                        let oTempTableData = oNewConfigData.BRCORDERTABLE.find(el=>el.TableName===element.Table);
                        oTemp.TableDescription = oTempTableData.TableDescription;
                    }
    
    
                    oNewConfigData.BRCORDER2BRCSOURCESNAV.push(oTemp);
                });
    
                // Bug 12943 - check the condition for the table, if table is "" then change the value for "*" and add a description
                oConfigDetails.BRCORDER2BRCTABLESNAV.results.forEach(element => {
                    oTemp = {...element,
                            TableSelectValueState: "None",
                            TableSelectValueStateText: "",
                            RuleIdValueState: ( element.RuleId ? "None" : "Error" ),
                            RuleIdValueStateText: ( element.RuleId ? "" : "Rule ID is Mandatory")
                    };

                    if(element.Table === ""){
                        oTemp.Table = "*";
                        oTemp.TableDescription = "For all Tables";
                    } else {
                        let oTempTableData = oNewConfigData.BRCORDERTABLE.find(el=>el.TableName===element.Table);
                        oTemp.TableDescription = oTempTableData.TableDescription;
                    }
    
    
                    oNewConfigData.BRCORDER2BRCTABLESNAV.push(oTemp);
                });
    
                // Bug 12943 - on fields there is no condition for the table value
                for (let i = 0; i < oConfigDetails.BRCORDER2BRCFIELDSNAV.results.length; i++) {
                    const element = oConfigDetails.BRCORDER2BRCFIELDSNAV.results[i];
                    oTemp = {...element,
                        TableSelectValueState: "None",
                        TableSelectValueStateText: "",
                        FieldNameValueState: ( element.Field ? "None" : "Error" ),
                        FieldNameValueStateText: ( element.Field ? "" : "Field name is Mandatory"),
                        RuleIdValueState: ( element.RuleId ? "None" : "Error" ),
                        RuleIdValueStateText: ( element.RuleId ? "" : "Rule ID is Mandatory")
                    };

                        let oTempTableData = oNewConfigData.BRCORDERTABLEFIELDS.find(el=>el.TableName===element.Table);
                        oTemp.TableDescription = oTempTableData.TableDescription;
    
                    try {
                        let arrFieldData = await GetListsData.getConfigurationBRCFieldsSelect(oController, oConfigData.BoType, element.Table);
                        oTemp.BRCORDERFIELD = arrFieldData;
    
                    } catch (oError) {
                        if (oError.statusCode !== "404") {
                            Utilities.showPopupAlert(
                                "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                                MessageBox.Icon.ERROR, 
                                "Error"
                            );
                        }
                    }
    
                    let oTempFieldData = oTemp.BRCORDERFIELD.find(el=>el.Fieldname===element.Field);
                    oTemp.FieldDescription = oTempFieldData.Fieldtext;
    
                    oNewConfigData.BRCORDER2BRCFIELDSNAV.push(oTemp);
                }
                oMDConsolidationModel.setProperty("/configData", oNewConfigData);
    
            } else{

                try {
                    arrConfigDetails = await GetListsData.getBRCRuleSet(oController);
    
                } catch (oError) {
                    if (oError.statusCode !== "404") {
                        Utilities.showPopupAlert(
                            "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                            MessageBox.Icon.ERROR, 
                            "Error"
                        );
                    }
                }

                oNewConfigData = {
                    BoType: oConfigData.BoType,
                    OperationType: oConfigData.OperationType,
                    StepType: oConfigData.StepType,
                    SelectedRow: oConfigData.SelectedRow, 
                    SelectedConfiguration: oConfigData.SelectedConfiguration,
                    BRCRules: [],
                    DeleteRows: [],
                    visDeleteButton: false, 
                    visChangesText: false
                };

                if (arrConfigDetails && arrConfigDetails.length) {

                    for (let i = 0; i < arrConfigDetails.length; i++) {
                        const element = arrConfigDetails[i];

                        // Bug 13013 - added a "ebRule" for every rule and set it to false 
                        oTemp = { 
                            ...element,
                            RuleIdValueState: ( element.RuleId ? "None" : "Error" ),
                            RuleIdValueStateText: ( element.RuleId ? "" : "Rule ID is Mandatory"),
                            ebRule: false,
                            DescriptionValueState: ( element.Description ? "None" : "Error" ),
                            DescriptionValueStateText: ( element.Description ? "" : "Description is Mandatory"),
                            LabelValueState: ( element.Label ? "None" : "Error" ),
                            LabelValueStateText: ( element.Label ? "" : "Label is Mandatory"),
                            Operation: ""
                        };

                        // Bug 13013 - change the condition to identify the main rules
                        if(element.RuleId.substring(0, 1) === "Z" || element.RuleId.substring(0, 1) === "Y"){
                            oTemp.mainRule = false;
                        } else{
                            oTemp.mainRule = true;
                        }
                        
                        oNewConfigData.BRCRules.push(oTemp);
                    }
                }
            let oCreateButton = sap.ui.getCore().byId("MDCConfigurationBRCFragmentID--BtnSave");
            oCreateButton.setEnabled(false);

            oMDConsolidationModel.setProperty("/configData", oNewConfigData);

            }
        }, function () {
            if(sSelectedTab==="orderBRC"){
                oTab.setSelectedKey("rulesBRC");
            } else{
                oTab.setSelectedKey("orderBRC");
            }
        });
    };

    oMDCConfigurationBRC.onLiveChangeConfiguration = async function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sValue = oEvent.getSource().getValue().toUpperCase();
        oEvent.getSource().setValue(sValue);
        let sPath = "/configData/ConfigIdValueState";
        let sComponentName = "Configuration ID";
        let arrBRCAllData = oMDConsolidationModel.getProperty("/configData/BRCAll");
        let bRepeatedValue;

        bRepeatedValue = this.MDCConfigurationBRC._ValidateRepeatValue(arrBRCAllData, "ConfigId", sValue);
        this.MDCConfigurationBRC._ValueStateHandler(oMDConsolidationModel, sValue, sPath, sComponentName, true, bRepeatedValue);
        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationBRC.onLiveChangeDescription = function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sValue = oEvent.getSource().getValue();
        let sPath = "/configData/DescriptionValueState";
        let sComponentName = "Configuration Description";

        this.MDCConfigurationBRC._ValueStateHandler(oMDConsolidationModel, sValue, sPath, sComponentName, false, false);
        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };
    // Bug 12943 - Added function for validation of number on parallel field
    oMDCConfigurationBRC.onChangeParallel = function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let regexNum = "^[0-9]+$";
        let sValue = oEvent.getSource().getValue();
        if(sValue === ""){
            oMDConsolidationModel.setProperty("/configData/ParallelValueState", "None");
            oMDConsolidationModel.setProperty("/configData/ParallelValueStateText", "Text");
        } else if(!sValue.match(regexNum)){
            oMDConsolidationModel.setProperty("/configData/ParallelValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/ParallelValueStateText", "Only Numbers");
        } else {
            oMDConsolidationModel.setProperty("/configData/ParallelValueState", "None");
            oMDConsolidationModel.setProperty("/configData/ParallelValueStateText", "Text");
        }
        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationBRC.onLiveChangeSourceSystemOSSInput = function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sValue = oEvent.getSource().getValue();
        let sPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath() + "/SourceSystemSelectValueState";
        let sComponentName = "Source System";

        this.MDCConfigurationBRC._ValueStateHandler(oMDConsolidationModel, sValue, sPath, sComponentName, false, false);
        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationBRC.onLiveChangeRuleForBRCTableInput = async function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sValue = oEvent.getSource().getValue().toUpperCase();
        oEvent.getSource().setValue(sValue);
        let sValueStatePath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath() + "/RuleIdValueState";
        let sComponentName = "Rule ID";
        let sPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let oRowData = oMDConsolidationModel.getProperty(sPath);
        let arrBRCRules = oMDConsolidationModel.getProperty("/configData/BRCRules");
        let bRepeatedValue;
        let arrTemp = []; 
        
        if(oRowData.Operation!=="C"){
            oMDConsolidationModel.setProperty(sPath + "/Operation", "U");
        }
        arrBRCRules.forEach(element => {
            arrTemp.push({RuleId: element.RuleId});
        });
        arrTemp.splice(sPath.split("/")[3], 1);
        bRepeatedValue = this.MDCConfigurationBRC._ValidateRepeatValue(arrTemp, "RuleId", sValue);
        this.MDCConfigurationBRC._ValueStateHandler(oMDConsolidationModel, sValue, sValueStatePath, sComponentName, true, bRepeatedValue);
        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationBRC.onLiveChangeDescriptionForBRCTableInput = function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sValue = oEvent.getSource().getValue();
        let sValueStatePath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath() + "/DescriptionValueState";
        let sComponentName = "Rule Description";
        let sPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let oRowData = oMDConsolidationModel.getProperty(sPath);

        if(oRowData.Operation!=="C"){
            oMDConsolidationModel.setProperty(sPath + "/Operation", "U");
        }

        this.MDCConfigurationBRC._ValueStateHandler(oMDConsolidationModel, sValue, sValueStatePath, sComponentName, false, false);
        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationBRC.onLiveChangeLabelForBRCTableInput = function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sValue = oEvent.getSource().getValue();
        let sValueStatePath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath() + "/LabelValueState";
        let sComponentName = "Rule Label";
        let sPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let oRowData = oMDConsolidationModel.getProperty(sPath);

        if(oRowData.Operation!=="C"){
            oMDConsolidationModel.setProperty(sPath + "/Operation", "U");
        }

        this.MDCConfigurationBRC._ValueStateHandler(oMDConsolidationModel, sValue, sValueStatePath, sComponentName, false, false);
        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationBRC.onCheckboxSelect = function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let oRowData = oMDConsolidationModel.getProperty(sPath);

        if(oRowData.Operation!=="C"){
            oMDConsolidationModel.setProperty(sPath + "/Operation", "U");
        }
        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationBRC._ValidateRepeatValue = function(arrData, sFieldName, sValue){
        let oMatchedData = arrData.find(element=>{
            return element[sFieldName] === sValue;
        });

        return (oMatchedData?true:false);
    };

    oMDCConfigurationBRC._ValueStateHandler = function(oMDConsolidationModel, sValue, sPath, sComponentName, bZValidation, bRepeatedValue){
        if(sValue){
            if(bZValidation && sValue.substring(0, 1) !== "Z" && sValue.substring(0, 1) !== "Y"){
                oMDConsolidationModel.setProperty(sPath, "Error");
                oMDConsolidationModel.setProperty(sPath + "Text", sComponentName + " must start with Z or Y");
            } else if (bRepeatedValue){
                oMDConsolidationModel.setProperty(sPath, "Error");
                oMDConsolidationModel.setProperty(sPath + "Text", sComponentName + " already exists");
            } else{
                oMDConsolidationModel.setProperty(sPath, "None");
                oMDConsolidationModel.setProperty(sPath + "Text", "");
            } 
        } else {
            oMDConsolidationModel.setProperty(sPath, "Error");
            oMDConsolidationModel.setProperty(sPath + "Text", sComponentName + "is mandatory");
        }
    };

    oMDCConfigurationBRC._ValidateFragmentToSave = function(oMDConsolidationModel){
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oCreateButton = sap.ui.getCore().byId("MDCConfigurationBRCFragmentID--BtnSave");
        let oTab = sap.ui.getCore().byId("MDCConfigurationBRCFragmentID--MainTabBarBRC");
        let sSelectedTab = oTab.getSelectedKey();
        let bSourceSystemHasError;
        let bTableHasError;
        let bFieldHasError;

        if(sSelectedTab === "orderBRC"){
            
            for (let i = 0; i < oConfigData.BRCORDER2BRCSOURCESNAV.length; i++) {
                const element = oConfigData.BRCORDER2BRCSOURCESNAV[i];
                if(element.SourceSystemSelectValueState === "Error" || element.TableSelectValueState === "Error"){
                    bSourceSystemHasError = true;
                    i = oConfigData.BRCORDER2BRCSOURCESNAV.length;
                } else{
                    bSourceSystemHasError = false;
                }
            }

            for (let i = 0; i < oConfigData.BRCORDER2BRCTABLESNAV.length; i++) {
                const element = oConfigData.BRCORDER2BRCTABLESNAV[i];
                if(element.RuleIdValueState === "Error" || element.TableSelectValueState === "Error"){
                    bTableHasError = true;
                    i = oConfigData.BRCORDER2BRCTABLESNAV.length;
                } else{
                    bTableHasError = false;
                }  
            }
            
            for (let i = 0; i < oConfigData.BRCORDER2BRCFIELDSNAV.length; i++) {
                const element = oConfigData.BRCORDER2BRCFIELDSNAV[i];
                if(element.FieldNameValueState === "Error" || element.RuleIdValueState === "Error" || element.TableSelectValueState === "Error"){
                    bFieldHasError = true;
                    i = oConfigData.BRCORDER2BRCFIELDSNAV.length;
                } else{
                    bFieldHasError = false;
                }  
            }
            // Bug 12943 - added the parallel value state to the condition
            if(oConfigData.ConfigIdValueState === "None" && oConfigData.DescriptionValueState === "None" && oConfigData.ParallelValueState === "None" && !bSourceSystemHasError && !bTableHasError && !bFieldHasError){
                oCreateButton.setEnabled(true);
            } else{
                oCreateButton.setEnabled(false);
            }

        } else {

            for (let i = 0; i < oConfigData.BRCRules.length; i++) {
                const element = oConfigData.BRCRules[i];
                if(element.RuleIdValueState === "Error" || element.LabelValueState === "Error" || element.DescriptionValueState === "Error"){
                    bFieldHasError = true;
                    i = oConfigData.BRCRules.length;
                } else{
                    bFieldHasError = false;
                }  
            }

            let bChangeDetected = oConfigData.BRCRules.find(element => {
                return Boolean(element.Operation !== "");
            });

            if(!bFieldHasError && bChangeDetected){
                oCreateButton.setEnabled(true);
                oMDConsolidationModel.setProperty("/configData/visChangesText", true);
            } else{
                oCreateButton.setEnabled(false);
                oMDConsolidationModel.setProperty("/configData/visChangesText", false);
            }
        }
    };

    oMDCConfigurationBRC.onAddSourceSystems = function(){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oSourceSystemTable = sap.ui.getCore().byId("MDCConfigurationBRCFragmentID--idOSSTable");
        let sSelectedRowPath = oSourceSystemTable.getSelectedContextPaths()[0];
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let arrOrderSystemData = oMDConsolidationModel.getProperty("/configData/BRCORDER2BRCSOURCESNAV");

        if (!arrOrderSystemData) {
            arrOrderSystemData = [];
            oMDConsolidationModel.setProperty("/configData/BRCORDER2BRCSOURCESNAV", {});
        }

        let oNewRow = { BoType: oConfigData.BoType,
                        ConfigId: oConfigData.ConfigId,
                        Table: "",
                        TableSelectValueState: "Error",
                        TableSelectValueStateText: "Table is Mandatory",
                        SourceSystem: "",
                        SourceSystemSelectValueState: "Error",
                        SourceSystemSelectStateText: "Source System is Mandatory"
                    };


        if(sSelectedRowPath) {
            let oCurrentRow = oMDConsolidationModel.getProperty(sSelectedRowPath);

            if(oCurrentRow) {
                oNewRow.SeqNo = oCurrentRow.SeqNo + 1;
            } else {
                oNewRow.SeqNo = arrOrderSystemData.length + 1; 
            }
            
			//Get the row index selected
			let iSelectedIndex = parseInt(sSelectedRowPath.substring(sSelectedRowPath.lastIndexOf("/") + 1));
			arrOrderSystemData.splice(iSelectedIndex + 1, 0, oNewRow);
            //Rearranging all rows based on their sequence
            arrOrderSystemData.forEach((oTemplateStep, iIndex) => {
                oTemplateStep.SeqNo = iIndex + 1;
            });
		} else {
            oNewRow.SeqNo = arrOrderSystemData.length + 1; 
            arrOrderSystemData.push(oNewRow);
		}

        oMDConsolidationModel.setProperty("/configData/BRCORDER2BRCSOURCESNAV", arrOrderSystemData);

        oMDConsolidationModel.refresh();

        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);

    };

    oMDCConfigurationBRC.onAddRulesforTables = function(){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oSourceSystemTable = sap.ui.getCore().byId("MDCConfigurationBRCFragmentID--idORTTable");
        let sSelectedRowPath = oSourceSystemTable.getSelectedContextPaths()[0];
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let arrRulesForTableData = oMDConsolidationModel.getProperty("/configData/BRCORDER2BRCTABLESNAV");

        if (!arrRulesForTableData) {
            arrRulesForTableData = [];
            oMDConsolidationModel.setProperty("/configData/BRCORDER2BRCTABLESNAV", {});
        }

        let oNewRow = { BoType: oConfigData.BoType,
                        ConfigId: oConfigData.ConfigId,
                        Table: "",
                        TableSelectValueState: "Error",
                        TableSelectValueStateText: "Table is Mandatory",
                        RuleId: "",
                        RuleIdValueState: "Error",
                        RuleIdValueStateText: "Rule ID is Mandatory"
                    };


        if(sSelectedRowPath) {
            let oCurrentRow = oMDConsolidationModel.getProperty(sSelectedRowPath);

            if(oCurrentRow) {
                oNewRow.SeqNo = oCurrentRow.SeqNo + 1;
            } else {
                oNewRow.SeqNo = arrRulesForTableData.length + 1; 
            }
            
			//Get the row index selected
			let iSelectedIndex = parseInt(sSelectedRowPath.substring(sSelectedRowPath.lastIndexOf("/") + 1));
			arrRulesForTableData.splice(iSelectedIndex + 1, 0, oNewRow);
            //Rearranging all rows based on their sequence
            arrRulesForTableData.forEach((oTemplateStep, iIndex) => {
                oTemplateStep.SeqNo = iIndex + 1;
            });
		} else {
            oNewRow.SeqNo = arrRulesForTableData.length + 1; 
            arrRulesForTableData.push(oNewRow);
		}

        oMDConsolidationModel.setProperty("/configData/BRCORDER2BRCTABLESNAV", arrRulesForTableData);

        oMDConsolidationModel.refresh();

        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationBRC.onAddRulesforFields = function(){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oSourceSystemTable = sap.ui.getCore().byId("MDCConfigurationBRCFragmentID--idORFTable");
        let sSelectedRowPath = oSourceSystemTable.getSelectedContextPaths()[0];
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let arrRulesForFields = oMDConsolidationModel.getProperty("/configData/BRCORDER2BRCFIELDSNAV");

        if (!arrRulesForFields) {
            arrRulesForFields = [];
            oMDConsolidationModel.setProperty("/configData/BRCORDER2BRCFIELDSNAV", {});
        }

        let oNewRow = { BoType: oConfigData.BoType,
                        ConfigId: oConfigData.ConfigId,
                        Table: "",
                        TableSelectValueState: "Error",
                        TableSelectValueStateText: "Table is Mandatory",
                        FieldName: "",
                        FieldNameValueState: "Error",
                        FieldNameValueStateText: "Field name is Mandatory",
                        RuleId: "",
                        RuleIdValueState: "Error",
                        RuleIdValueStateText: "Rule ID is Mandatory"
                    };


        if(sSelectedRowPath) {
            let oCurrentRow = oMDConsolidationModel.getProperty(sSelectedRowPath);

            if(oCurrentRow) {
                oNewRow.SeqNo = oCurrentRow.SeqNo + 1;
            } else {
                oNewRow.SeqNo = arrRulesForFields.length + 1; 
            }
            
			//Get the row index selected
			let iSelectedIndex = parseInt(sSelectedRowPath.substring(sSelectedRowPath.lastIndexOf("/") + 1));
			arrRulesForFields.splice(iSelectedIndex + 1, 0, oNewRow);
            //Rearranging all rows based on their sequence
            arrRulesForFields.forEach((oTemplateStep, iIndex) => {
                oTemplateStep.SeqNo = iIndex + 1;
            });
		} else {
            oNewRow.SeqNo = arrRulesForFields.length + 1; 
            arrRulesForFields.push(oNewRow);
		}

        oMDConsolidationModel.setProperty("/configData/BRCORDER2BRCFIELDSNAV", arrRulesForFields);

        oMDConsolidationModel.refresh();

        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationBRC.onAddRulesforBRC = function(){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let arrRulesForBRC = oMDConsolidationModel.getProperty("/configData/BRCRules");

        if (!arrRulesForBRC) {
            arrRulesForBRC = [];
            oMDConsolidationModel.setProperty("/configData/BRCRules", {});
        }

        // Bug 13013 - everytime a row is added set enabled to true and the main rule to false
        let oNewRow = { 
            Operation: "C",
            RuleId: "",
            RuleIdValueState: "Error",
            RuleIdValueStateText: "Rule ID is Mandatory",
            Description: "",
            DescriptionValueState: "Error",
            DescriptionValueStateText: "Description is Mandatory",
            Label: "",
            LabelValueState: "Error",
            LabelValueStateText: "Label is Mandatory",
            mainRule: false,
            ebRule: true
        };

        arrRulesForBRC.push(oNewRow);
        oMDConsolidationModel.setProperty("/configData/BRCRules", arrRulesForBRC);

        oMDConsolidationModel.refresh();
        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };
    
    // Bug 12943 - on delete any row of the table, check the validation on the _deleteRow function, not on each function
    oMDCConfigurationBRC.deleteOSSRow = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oTable = oEvent.getSource();
		let oClickedItem = oEvent.getParameters().listItem;
        let sPath = "/configData/BRCORDER2BRCSOURCESNAV";
        this.MDCConfigurationBRC._deleteRow(oMDConsolidationModel, oTable, oClickedItem, sPath);
    };

    oMDCConfigurationBRC.deleteORTRow = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oTable = oEvent.getSource();
		let oClickedItem = oEvent.getParameters().listItem;
        let sPath = "/configData/BRCORDER2BRCTABLESNAV";
        this.MDCConfigurationBRC._deleteRow(oMDConsolidationModel, oTable, oClickedItem, sPath);
    };

    oMDCConfigurationBRC.deleteORFRow = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oTable = oEvent.getSource();
		let oClickedItem = oEvent.getParameters().listItem;
        let sPath = "/configData/BRCORDER2BRCFIELDSNAV";
        this.MDCConfigurationBRC._deleteRow(oMDConsolidationModel, oTable, oClickedItem, sPath);
    };

    oMDCConfigurationBRC.deleteRulesBRCRow = function(oEvent) {
        let oController = this;
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oTable = oEvent.getSource();
		let oClickedItem = oEvent.getParameters().listItem;
        let sPath = oClickedItem.getBindingContext("mdconsolidationModel").getPath();
        let iClickedIndex = oTable.getItems().indexOf(oClickedItem);
        let arrDelItems = oMDConsolidationModel.getProperty("/configData/DeleteRows");
        let arrBRCData = oMDConsolidationModel.getProperty("/configData/BRCRules");
        let oRowData = oMDConsolidationModel.getProperty(sPath);

        // Bug 13013 - saved the save button in a variable and changed the condition to check wether the rule is a main rule or not  
        let oCreateButton = sap.ui.getCore().byId("MDCConfigurationBRCFragmentID--BtnSave");

        if(!oRowData.mainRule){

            // Show a message asking the user to confirm deletion. Exit if cancel 
            let promise = new Promise(function (resolve, reject) {
                let promiseShowPopupAlert = Utilities.showPopupAlert("The decision row will be deleted. Continue?", MessageBox.Icon.WARNING,
                    "Delete?", [sap.m.MessageBox.Action.YES, sap.m.MessageBox.Action.NO]);
                promiseShowPopupAlert.then(function () {
                    resolve();
                }, function () {
                    reject();
                });
            });
    
            // Remove row from the model. 
            promise.then(async function () {
    
                try {
                    let arrBRCRules = await GetListsData.getConfigurationBRCDetails(oController);
    
                    let oBRCRuleOnField;
                    let oBRCRuleOnTable;
                    arrBRCRules.forEach(element => {
                        oBRCRuleOnField = element.BRCORDER2BRCFIELDSNAV.results.find(fieldElement => {
                            return fieldElement.RuleId === oRowData.RuleId;
                        });
                        oBRCRuleOnTable = element.BRCORDER2BRCTABLESNAV.results.find(tableElement =>{
                            return tableElement.RuleId === oRowData.RuleId;
                        });
                    });
    
                    if(oBRCRuleOnField){
                        Utilities.showPopupAlert("BRC Rule '" + oBRCRuleOnField.RuleId + "' is being used on Config '" + oBRCRuleOnField.ConfigId + "' on 'Order Rules for Fields' Table", MessageBox.Icon.ERROR, "Rule cannot be deleted");
                    } else if (oBRCRuleOnTable){
                        Utilities.showPopupAlert("BRC Rule '" + oBRCRuleOnTable.RuleId + "' is being used on Config '" + oBRCRuleOnTable.ConfigId + "' on 'Order Rules for Tables' Table", MessageBox.Icon.ERROR, "Rule cannot be deleted");
                    } else{
                        arrBRCData.splice(iClickedIndex, 1);
                        if(arrDelItems.length){
                            arrDelItems.push(oRowData);
                        } else {
                            arrDelItems = [{...oRowData}];
                        }
                        oMDConsolidationModel.setProperty("/configData/DeleteRows", arrDelItems);
                        oMDConsolidationModel.setProperty("/configData/BRCRules", arrBRCData);
                        oMDConsolidationModel.setProperty("/configData/visChangesText", true);
                    }
    
    
                } catch (oError) {
                    if (oError.statusCode !== "404") {
                        Utilities.showPopupAlert(
                            "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                            MessageBox.Icon.ERROR, 
                            "Error"
                        );
                    }
                }
                // Bug 13013 - set the save button to true so the user can save the changes
                oCreateButton.setEnabled(true);
            });
        } else{
            Utilities.showPopupAlert("Error: Main Rule System cannot be deleted", MessageBox.Icon.ERROR, "Error");

        }
    };

    // Bug 12943 - on delete any row of the table, check the validation on the _deleteRow function, not on each function, check the validation on if the user clicks on "yes"
    oMDCConfigurationBRC._deleteRow = function(oMDConsolidationModel, oTable, oClickedItem, sPath){
        let iClickedIndex = oTable.getItems().indexOf(oClickedItem);
        let oController = this;
        // Get the index clicked
		let arrTableData = oMDConsolidationModel.getProperty(sPath);

		// Show a message asking the user to confirm deletion. Exit if cancel 
		let promise = new Promise(function (resolve, reject) {
			let promiseShowPopupAlert = Utilities.showPopupAlert("The decision row will be deleted. Continue?", MessageBox.Icon.WARNING,
				"Delete?", [sap.m.MessageBox.Action.YES, sap.m.MessageBox.Action.NO]);
			promiseShowPopupAlert.then(function () {
				resolve();
			}, function () {
				reject();
			});
		});

        // Remove row from the model. 
        promise.then(function () {
            arrTableData.splice(iClickedIndex, 1);
            for (let i = 0; i < arrTableData.length; i++) {
                arrTableData[i].SeqNo = i+1;
            }
            oMDConsolidationModel.setProperty(sPath, arrTableData);
            oController._ValidateFragmentToSave(oMDConsolidationModel);
        });
    };

    oMDCConfigurationBRC.onChangeBRCTable = async function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let sRowPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let oSelectedTable = oEvent.getSource().getSelectedKey();
        let arrFieldData;

        let oTempTableData = oConfigData.BRCORDERTABLE.find(el=>el.TableName===oSelectedTable);
        oMDConsolidationModel.setProperty(sRowPath + "/TableDescription", oTempTableData.TableDescription);
        oMDConsolidationModel.setProperty(sRowPath + "/TableSelectValueState", "None");
        oMDConsolidationModel.setProperty(sRowPath + "/TableSelectValueStateText", "");

        if(oEvent.getSource().getId().includes("ORFTable")){
            try {
                arrFieldData = await GetListsData.getConfigurationBRCFieldsSelect(this, oConfigData.BoType, oSelectedTable);
                oMDConsolidationModel.setProperty(sRowPath + "/Field", "");
                oMDConsolidationModel.setProperty(sRowPath + "/FieldNameValueState", "Error");
                oMDConsolidationModel.setProperty(sRowPath + "/FieldNameValueStateText", "Field is mandatory");
            } catch (oError) {
                if (oError.statusCode !== "404") {
                    Utilities.showPopupAlert(
                        "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                        MessageBox.Icon.ERROR, 
                        "Error"
                    );
                }
            }
    
            oMDConsolidationModel.setProperty(sRowPath + "/BRCORDERFIELD", arrFieldData);
        }
        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationBRC.onChangeRuleID = function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sRowPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        oMDConsolidationModel.setProperty(sRowPath + "/RuleIdValueState", "None");
        oMDConsolidationModel.setProperty(sRowPath + "/RuleIdValueStateText", "");
        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationBRC.onChangeBRCField = function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sRowPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let oBRCRowData = oMDConsolidationModel.getProperty(sRowPath);
        let oSelectedField = oEvent.getSource().getSelectedKey();

        let oTempFieldData = oBRCRowData.BRCORDERFIELD.find(el=>el.Fieldname===oSelectedField);
        oMDConsolidationModel.setProperty(sRowPath + "/FieldDescription", oTempFieldData.Fieldtext);
        oMDConsolidationModel.setProperty(sRowPath + "/FieldNameValueState", "None");
        oMDConsolidationModel.setProperty(sRowPath + "/FieldNameValueStateText", "");
        this.MDCConfigurationBRC._ValidateFragmentToSave(oMDConsolidationModel);
    };
    
    oMDCConfigurationBRC._onOpen = function(){
        let oMainTabBar = sap.ui.getCore().byId("MDCConfigurationBRCFragmentID--MainTabBarBRC");
        let oBRCTabBar = sap.ui.getCore().byId("MDCConfigurationBRCFragmentID--iconTabBarBRCOrderRules");
        oMainTabBar.setSelectedKey("orderBRC");
        oBRCTabBar.setSelectedKey("OSS");
    };

    oMDCConfigurationBRC.onClickClose = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sSelectedRow = oMDConsolidationModel.getProperty("/configData/SelectedRow");
        let sOperationType = oMDConsolidationModel.getProperty("/configData/OperationType");

        if (sOperationType === "C") {
            oMDConsolidationModel.setProperty(sSelectedRow + "/ConfigId", undefined);
        }

        oMDConsolidationModel.setProperty("/configData", {});


        let oMDCConfigurationBRCFragment = this._getMDCConfigurationFragment("BRC");
        oMDCConfigurationBRCFragment.close();
    };

    oMDCConfigurationBRC.onClickDelete = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        this.MDCConfigurationBRC.saveConfiguration(this, oMDConsolidationModel, "D");
    };

    oMDCConfigurationBRC.onClickSave = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sOperationType = oMDConsolidationModel.getProperty("/configData/OperationType");
        this.MDCConfigurationBRC.saveConfiguration(this, oMDConsolidationModel, sOperationType);
    };

    oMDCConfigurationBRC.saveConfiguration = async function(oController, oMDConsolidationModel, sOperationType) {
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oTab = sap.ui.getCore().byId("MDCConfigurationBRCFragmentID--MainTabBarBRC");
        // Bug 13013 - added the save button in a variable
        let oCreateButton = sap.ui.getCore().byId("MDCConfigurationBRCFragmentID--BtnSave");
        let sSelectedTab = oTab.getSelectedKey();
        let oTempData;
        let promiseTransport;
        let oPayload;
        
        if(sSelectedTab==="orderBRC"){
            promiseTransport = oController._getSelectedTransportPackage(true, false, false);
                // Task 13213: make the function asyncronous so we can call the corresponding service name
                promiseTransport.then( async oSelectedTransport => {
        
                    sap.ui.core.BusyIndicator.show(300);

                    oPayload = {
                        Object: "BRC Order",
                        Key: oConfigData.ConfigId,
                        Key2: oConfigData.BoType,
                        Mode: sOperationType,
                        OPERATION2MESSAGENAV: [],
                        OPERATION2BRCORDERNAV: [
                            {
                                BoType: oConfigData.BoType,
                                ConfigId: oConfigData.ConfigId,
                                Description: oConfigData.Description,
                                Parallel: Number(oConfigData.Parallel),
                                QueuePrefix: oConfigData.QueuePrefix,
                                BRCORDER2BRCSOURCESNAV: [],
                                BRCORDER2BRCTABLESNAV: [],
                                BRCORDER2BRCFIELDSNAV: []
                            }
                        ],
                        CustTransport: oSelectedTransport.customizingTransport

                    };

                    oConfigData.BRCORDER2BRCSOURCESNAV.forEach(element => {
                        oTempData = {
                            BoType: element.BoType,
                            ConfigId: element.ConfigId,
                            Table: (element.Table === "*" ? " " : element.Table),
                            SeqNo: element.SeqNo,
                            SourceSystem: element.SourceSystem
                        };
                        oPayload.OPERATION2BRCORDERNAV[0].BRCORDER2BRCSOURCESNAV.push(oTempData);
                    });
        
                    oConfigData.BRCORDER2BRCTABLESNAV.forEach(element => {
                        oTempData = {
                            BoType: element.BoType,
                            ConfigId: element.ConfigId,
                            Table: (element.Table === "*" ? " " : element.Table),
                            SeqNo: element.SeqNo,
                            RuleId: element.RuleId
                        };
                        oPayload.OPERATION2BRCORDERNAV[0].BRCORDER2BRCTABLESNAV.push(oTempData);
                    });
        
                    oConfigData.BRCORDER2BRCFIELDSNAV.forEach(element => {
                        oTempData = {
                            BoType: element.BoType,
                            ConfigId: element.ConfigId,
                            Table: (element.Table === "*" ? " " : element.Table),
                            Field: element.Field,
                            SeqNo: element.SeqNo,
                            RuleId: element.RuleId
                        };
                        oPayload.OPERATION2BRCORDERNAV[0].BRCORDER2BRCFIELDSNAV.push(oTempData);
                    });

                    (new DMRDataService(
                        oController,
                        // Task 13213: call the corresponding service name depending on the system
                        await oController.serviceName,
                        "/OPERATIONSet",
                        "Save Configuration",
                        "/", // Root of the received data
                        "Save Configuration"
                    )).saveData(
                        false,
                        oPayload,
                        null, {
                            success: {
                                fCallback: function (oDataResult, oResponse) {
                                    sap.ui.core.BusyIndicator.hide();

                                    if (oResponse.OPERATION2MESSAGENAV.results[0].MessageType === "E") {
                                        Utilities.showPopupAlert("Error: " + oResponse.OPERATION2MESSAGENAV.results[0].Message, MessageBox.Icon.ERROR, "Error");
                                        return;
                                    }

                                    let arrConfigList = [];
                                    let promiseConfigurationList = GetListsData.getConfigurationBRCDetails(oController);
                                    promiseConfigurationList.then(oData => {
                                        let arrFilteredData = oData.filter(element => {
                                            return element.BoType === oConfigData.BoType;
                                        }) ;
                                        arrConfigList.push(oController._getCreateConfigurationObject());
                                        arrConfigList.push(oController._getEmptyConfigurationObject());
                                        arrFilteredData.forEach(oConfiguration => {
                                            arrConfigList.push({
                                                Zkey: "",
                                                Id: oConfiguration.ConfigId,
                                                Description: oConfiguration.Description
                                            });
                                        });

                                        oMDConsolidationModel.setProperty(oConfigData.SelectedRow + "/arrConfigList", arrConfigList);
                                        oMDConsolidationModel.setProperty(oConfigData.SelectedRow + "/ConfigId", ( sOperationType === "D" ? "" : oConfigData.ConfigId ));
                                        
                                        // Bug 12351 - Display/Edit/Delete/Add Configurations
                                        oController._refreshTemplateDropDownList(oController);

                                        if (oResponse.OPERATION2MESSAGENAV.results[0].MessageType === "S") {
                                            Utilities.showPopupAlert(oResponse.OPERATION2MESSAGENAV.results[0].Message, MessageBox.Icon.SUCCESS, "Success");
                                        }

                                    });
                                    promiseConfigurationList.catch(oError => {
                                        sap.ui.core.BusyIndicator.hide();
                                        let message = jQuery.parseJSON(oError.responseText).error.message.value;
                                        Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");
                                    });
                                },
                                oParam: this
                            },
                            error: function (oError) {
                                sap.ui.core.BusyIndicator.hide();
                                let message = jQuery.parseJSON(oError.responseText).error.message.value;
                                Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");
                            }
                        }
                    );
                });
            
            } else {
                let arrOData = [];
                let arrMessages = [];
                // Bug 13013 - store all of the rules in a variable
                let arrBRCRules = oMDConsolidationModel.getProperty("/configData/BRCRules");

                promiseTransport = oController._getSelectedTransportPackage(false, true, false);
                promiseTransport.then(async oSelectedTransport => {
        
                    sap.ui.core.BusyIndicator.show(300);
                
                    oConfigData.BRCRules.forEach( element =>{
                        if(element.Operation){
                            oTempData = {
                                RuleId: element.RuleId,
                                Description: element.Description,
                                Label: element.Label,
                                ForTable: (element.ForTable ? true : false),
                                ForField: (element.ForField ? true : false),
                                Operation: element.Operation,
                                WbTransport: oSelectedTransport.workbenchTransport
                            };
                            arrOData.push(oTempData);
                        }
                    });

                    oConfigData.DeleteRows.forEach(element =>{
                        oTempData = {
                            RuleId: element.RuleId,
                            Description: element.Description,
                            Label: element.Label,
                            ForTable: (element.ForTable ? true : false),
                            ForField: (element.ForField ? true : false),
                            Operation: "D",
                            WbTransport: oSelectedTransport.workbenchTransport
                        };
                        arrOData.push(oTempData);
                    });

                    for (let i = 0; i < arrOData.length; i++) {
                        const element = arrOData[i];
                        let arrPromiseMessages = await oController.MDCConfigurationBRC._saveBRCRules(oController, element);
                        arrMessages.push(arrPromiseMessages);

                        // Bug 13013 - if the message is saved and the operation is not Delete then set the rule id field to disabled and the operation to empty 
                        if(arrPromiseMessages.MessageType==="S" && element.Operation!=="D"){
                            let iElement =  arrBRCRules.findIndex(el => el.RuleId === element.RuleId);
                            oMDConsolidationModel.setProperty("/configData/BRCRules/" + iElement + "/ebRule", false);
                            oMDConsolidationModel.setProperty("/configData/BRCRules/" + iElement + "/Operation", "");
                        }
                    }
                    
                    // if no error then change the save button to disabled
                    if(!arrMessages.findIndex(el=> el.MessageType === "E")){
                        oCreateButton.setEnabled(true);
                        oMDConsolidationModel.setProperty("/configData/visChangesText", true);
                    } else {
                        oMDConsolidationModel.setProperty("/configData/DeleteRows", []);
                        oCreateButton.setEnabled(false);
                        oMDConsolidationModel.setProperty("/configData/visChangesText", false);
                    }

                    ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());
                });
            }

    };

    oMDCConfigurationBRC._saveBRCRules = function(oController, element){
        let promise = new Promise(async function(resolve, reject){
            (new DMRDataService(
                oController,
				await oController.serviceName,
                "/BRCRULESSet",
                "Save Configuration",
                "/", // Root of the received data
                "Save Configuration"
            )).saveData(
                false,
                element,
                null, {
                    success: {
                        fCallback: function (oDataResult, oResponse) {
                            sap.ui.core.BusyIndicator.hide();
                            
                            resolve({
                                MessageType: oResponse.MessageCode,
                                Message: oResponse.Message
                            });

                        },
                        oParam: this
                    },
                    error: function (oError) {
                        sap.ui.core.BusyIndicator.hide();
                        let message = jQuery.parseJSON(oError.responseText).error.message.value;

                        reject({
                            MessageType: "E",
                            Message: message
                        });
                    }
                }
            );
        });
        return promise;
    };

    return oMDCConfigurationBRC;
});