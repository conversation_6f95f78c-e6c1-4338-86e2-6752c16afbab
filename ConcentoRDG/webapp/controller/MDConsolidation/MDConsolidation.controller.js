sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
    "sap/ui/model/json/JSONModel",
    "dmr/mdg/supernova/SupernovaFJ/model/GetLists",
    "dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "dmr/mdg/supernova/SupernovaFJ/model/ModelMessages",
    "dmr/mdg/supernova/SupernovaFJ/libs/DataService",
    "sap/m/MessageBox",
    "dmr/mdg/supernova/SupernovaFJ/model/models",
    "./MDCAdapter",
    "./MDCConfigurationACT",
    "./MDCConfigurationFAR",
    "./MDCConfigurationEVA",
    "./MDCConfigurationBRC",
    "./MDCConfigurationMTC"
],
function (
    BaseController, JSONModel, GetListsData, Utilities, ModelMessages, DMRDataService, MessageBox, RDGModels, MDCAdapter, MDCConfigurationACT, MDCConfigurationFAR, 
    MDCConfigurationEVA, MDCConfigurationBRC, MDCConfigurationMTC) {

    "use strict";
    
    let MDConsolidationController = {};

    MDConsolidationController.onInit = function() {
        let oRouter = sap.ui.core.UIComponent.getRouterFor(this);

        // Task 12350 - Display/Edit/Delete/Add Adapter service
		this.MDCAdapter = MDCAdapter;
		this.MDCAdapterFragmentID = "MDCAdapterFragmentID";

        // Task 12351 - Display/Edit/Delete/Add Adapter service
		this.MDCConfigurationACT = MDCConfigurationACT;
		this.MDCConfigurationACTFragmentID = "MDCConfigurationACTFragmentID";

        this.MDCConfigurationFAR = MDCConfigurationFAR;
        this.MDCConfigurationFARFragmentID = "MDCConfigurationFARFragmentID";

        this.MDCConfigurationEVA = MDCConfigurationEVA;
        this.MDCConfigurationEVAFragmentID = "MDCConfigurationEVAFragmentID";

        this.MDCConfigurationBRC = MDCConfigurationBRC;
        this.MDCConfigurationBRCFragmentID = "MDCConfigurationBRCFragmentID";

        this.MDCConfigurationMTC = MDCConfigurationMTC;
        this.MDCConfigurationMTCFragmentID = "MDCConfigurationMTCFragmentID";

        // Task 13213: call the _getServiceName function that returns a string
        this.serviceName = this._getServiceName(this);

        // Load the parameters received into the model 
        oRouter.getRoute("MDConsolidation").attachMatched(this._onRouteMatched, this);
        this.byId("idMDConsolidationPanel").setExpanded(false);
        this.ModelMessages = ModelMessages;
    };

    MDConsolidationController._getServiceName = function (oController){
        let promise = new Promise((resolve) =>{
            // Task 13213: get the current system and add a new property calling the corresponding service
            let oCurrentSystemPromise = oController.getCurrentSystem();
            oCurrentSystemPromise.then(function(oSystem) {
                let sService;
                // if the current system is S4H and version No is less than 2022, then value of MDCService is MDC_LEGACY
                if(oSystem.IS_S4H === "S4H" && Number(oSystem.VersionNo) < 2022){
                    sService = "MDC_LEGACY";
                } else {
                    //else the value of MDCService is MDCONSOLIDATION
                    sService = "MDCONSOLIDATION";
                }
                
                resolve(sService);
            });
        });

        return promise;
    };

    MDConsolidationController._onRouteMatched = function () {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        if(!oMDConsolidationModel) {
            let oJSONModel = new JSONModel();
            this.getView().setModel(oJSONModel, "mdconsolidationModel");
            oJSONModel.setProperty("/selected", {});
            oJSONModel.setProperty("/selected/transports", {
                customizing: undefined,
                workbench: undefined
            });
            oJSONModel.setProperty("/selected/package", undefined);
        }
    };

    // Bug 12351 - Display/Edit/Delete/Add Configurations
    MDConsolidationController._refreshTemplateDropDownList = function (oController) {
        let promiseProcessTemplateDropDownLists = GetListsData.getProcessTemplateDropDownList(oController);
        promiseProcessTemplateDropDownLists.then((oData) => {
            let oDataResults = oData.results[0];
            oDataResults.MAIN2AUTHNAV.results.unshift({
                Zkey: "",
                AuthName: "Custom",
                Description: "Create Authorization Group"
            }, {
                Zkey: "",
                AuthName: "",
                Description: ""
            });
            oDataResults.MAIN2STRATEGYNAV.results.unshift({
                Zkey: "",
                Stratname: "",
                Description: ""
            });

            let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
            oMDConsolidationModel.setProperty("/arrActionList", oDataResults.MAIN2ACTIONNAV.results);
            oMDConsolidationModel.setProperty("/arrAuthGroupList", oDataResults.MAIN2AUTHNAV.results);
            oMDConsolidationModel.setProperty("/arrBoTypeList", oDataResults.MAIN2BONAV.results);
            oMDConsolidationModel.setProperty("/arrStrategyList", oDataResults.MAIN2STRATEGYNAV.results);
        });
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * Invoke Component SearchList to create List of Process Templates and set data
     * Fetch template details and save it in mdconsolidationModel
     */
    MDConsolidationController.onMDConsolidationSearchListCreated = function (oEvent) {
        let oController = this;

        let comp = oEvent.getParameter("component");
        oController.MDConsolidationSearchList = comp;

        // Complete the mapping for data 
        oController.MDConsolidationSearchList.setDataMapping({
            "title": "ProcessTemplate",
            "description": "Description",
            "info": undefined
        });

        oController.MDConsolidationSearchList.setHeaderText("Process Templates");
        oController.MDConsolidationSearchList.setButtonInfo({
            icon: "sap-icon://add",
            toolTip: "New Process Template"
        });

        oController.MDConsolidationSearchList.setListGroupingPath("BoTypeDesc");

        oController.MDConsolidationSearchList.attachSelectionChange(oController.onProcessTemplateSelect, oController);
        oController.MDConsolidationSearchList.attachActionPressed(oController.onCreateNewProcessTemplate, oController);

        // Get the CR list 
		let promiseProcessTemplateList =
            GetListsData.getProcessTemplateList(oController);

        promiseProcessTemplateList.then((oData) => {
            let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
            oMDConsolidationModel.setProperty("/TemplateList", oData.results);
            //Bug 12404 - Remove fields based on version related properties
            oMDConsolidationModel.setProperty("/ShowAuthGroup", oData.results[0].ShowAuthorizationGroup);
            oMDConsolidationModel.setProperty("/ShowProcessUsage", oData.results[0].ShowProcessUsage);
            oMDConsolidationModel.setProperty("/ShowScheduleThreshold", oData.results[0].ShowScheduleThreshold);
            oController.MDConsolidationSearchList.setListData(oData.results);
        });
        
        // Bug 12351 - Display/Edit/Delete/Add Configurations
        oController._refreshTemplateDropDownList(oController);

        // Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog
        let promiseConfigurationDropdownLists = GetListsData.getConfigurationDropdownLists(oController);
        promiseConfigurationDropdownLists.then((oData) => {
            let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
            oMDConsolidationModel.setProperty("/arrConfigurationDropdownLists", oData);
        });
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * Invoke Component TransportRequestDialog to show Cutomizing TR
     */

    MDConsolidationController.onTransportPackageDialogCreated = function(oEvent) {
		let oController = this;
		let comp = oEvent.getParameter("component");
        this._transportPackageSelectionDialog = comp;
        this.getUsername()
		.then(function (oSapUserInfo) {
			let sUsername = oSapUserInfo.Sapname;
			if (!sUsername) {
				Utilities.showPopupAlert(
					oController.geti18nText("common.noLogin.description"), 
					MessageBox.Icon.ERROR, 
					oController.geti18nText("common.noLogin.title"));
			} else {
				comp.setUser(sUsername);
			}
		});
    };
    
    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * Promise return for Transport selected
     */
    MDConsolidationController._getSelectedTransportPackage = function (bCustomizing, bWorkbench, bPackage) {
        let oModel = this.getView().getModel("mdconsolidationModel");

        // get stored package and transports from the model 
		let oSelectedTransportPackage = oModel.getProperty("/selected");

        let promise = 
        this._transportPackageSelectionDialog.open(
            bCustomizing, oSelectedTransportPackage?.transports?.customizing, 
            bWorkbench, oSelectedTransportPackage?.transports?.workbench, 
            bPackage, oSelectedTransportPackage?.package);

        let promiseReturn = promise.then(function(oTransporPackageResponse){
            if(bCustomizing){
                oModel.setProperty("/selected/transports/customizing", oTransporPackageResponse.customizingTransport);
            }

            if(bWorkbench){
				oModel.setProperty("/selected/transports/workbench", oTransporPackageResponse.workbenchTransport);
			}

            if(bPackage){
				oModel.setProperty("/selected/package", oTransporPackageResponse.package);
			}

			return oTransporPackageResponse;
        });

        return promiseReturn;
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * On click of Primary button on search list
     * Expand the panel and reset the model property to store template details
     */
    MDConsolidationController.onCreateNewProcessTemplate = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        oMDConsolidationModel.setProperty("/processTemplateType", "new");
        oMDConsolidationModel.setProperty("/selectedProcessTemplate", {});
        oMDConsolidationModel.setProperty("/arrProcessGoalsList", []);
        this.byId("idMDConsolidationPanel").setExpanded(true);

        this.byId("processGoal").setEnabled(false);

        this.MDConsolidationSearchList.removeSelectedItem();

        oMDConsolidationModel.setProperty("/selectedProcessTemplate/DescriptionValueState", "Error");
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/DescriptionValueStateText", "Description is Mandatory");
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/BoTypeValueState", "Error");
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/BoTypeValueStateText", "BO Type is Mandatory");
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ProcessGoalValueState", "Error");
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ProcessGoalValueStateText", "Please select a BO Type");
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ebAddBtn", false);
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ebDelBtn", false);
        oMDConsolidationModel.setProperty("/templateValueState", "Error");
        oMDConsolidationModel.setProperty("/templateStateText", "Process template is mandatory");
        this.validateFormToSave();
    };

    // Task 12350 - Display/Edit/Delete/Add Adapter service
    MDConsolidationController._getCreateAdapterObject = function() {
        return {
            // Bug 13216 - change the zkey to "Custom" 
            Zkey: "Custom",
            AdapterName: "Custom",
            Description: "Create Adapter"
        };
    };

    // Task 12350 - Display/Edit/Delete/Add Adapter service
    MDConsolidationController._getEmptyAdapterObject = function() {
        return {
            Zkey: "",
            AdapterName: "",
            Description: ""
        };
    };

    // Task 12351 - Display/Edit/Delete/Add Configurations
    MDConsolidationController._getCreateConfigurationObject = function() {
        return {
            Zkey: "Custom",
            Id: "Custom",
            Description: "Create Configuration"
        };
    };

    // Task 12351 - Display/Edit/Delete/Add Configurations
    MDConsolidationController._getEmptyConfigurationObject = function() {
        return {
            Zkey: "",
            Id: "",
            Description: ""
        };
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * On click of Process template from search list
     * Expand the panel, set the model property selectedProcessTemplate to the incoming date
     */
    MDConsolidationController.onProcessTemplateSelect = async function(oEvent) {
        // Bug 13216 - added the oController variable that stores "this"
        let oController = this;
		let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
        let oSelectedData = oEvent.getParameters().data;
        oMDConsolidationModel.setProperty("/processTemplateType", "edit");
        oController.byId("idMDConsolidationPanel").setExpanded(true);
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ebAddBtn", true);
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ebDelBtn", true);
        let oSelectedTemplate = {};
		jQuery.extend(true, oSelectedTemplate, oSelectedData);
        let oMatchedBoTypeInfo = oMDConsolidationModel.getProperty("/arrBoTypeList").find(oBoType => {
            return oBoType.BoType === oSelectedTemplate.BoType;
        });
        if(oMatchedBoTypeInfo) {
            oMDConsolidationModel.setProperty("/arrProcessGoalsList", oMatchedBoTypeInfo.BO2PROCESSGOALNAV.results);
            let oMatchedProcessGoal = oMatchedBoTypeInfo.BO2PROCESSGOALNAV.results.find(oProcessGoal =>{
                return oProcessGoal.Name === oSelectedTemplate.ProcessGoal;
            });
            if(oMatchedProcessGoal) {
                oMDConsolidationModel.setProperty("/oEnableProperties", {
                    DefaultTemplate: oMatchedProcessGoal.Defaultprocesstemplate,
                    DeleteSource: oMatchedProcessGoal.Deletesource,
                    Strategy: oMatchedProcessGoal.StrategyFlag
                });
                if(!oMatchedProcessGoal.StrategyFlag) {
                    if(oMatchedProcessGoal.StrategyProperty) {
                        oSelectedTemplate.Strategy = oMatchedProcessGoal.StrategyProperty;
                    } else {
                        oSelectedTemplate.Strategy = undefined;
                    }
                }
                oMDConsolidationModel.setProperty("/arrStepTypeList", oMatchedProcessGoal.PROGOAL2STEPNAV.results);
                oSelectedTemplate.TEMPLATE2STEPNAV.results.sort((oStep1, oStep2) => {
                    if ( Number(oStep1.StepNo) < Number(oStep2.StepNo) ) {
                        return -1;
                    }
                    if ( Number(oStep1.StepNo) < Number(oStep2.StepNo) ){
                        return 1;
                    }
                    return 0;
                });

                // Bug 12433 - Some process templates not displaying in RDG
                if (!oSelectedTemplate.hasOwnProperty("TEMPLATE2STEPNAV")) {
                    oSelectedTemplate.TEMPLATE2STEPNAV = { "results": [] };
                }
                if (!oSelectedTemplate.TEMPLATE2STEPNAV.hasOwnProperty("results")) {
                    oSelectedTemplate.TEMPLATE2STEPNAV.results = [];
                }
                for (let i = 0; i < oSelectedTemplate.TEMPLATE2STEPNAV.results.length; i++) {
                    const oSelectedTemplateStep = oSelectedTemplate.TEMPLATE2STEPNAV.results[i];
                    
                    // Bug 13216 - oController variable added instead of "this"
                    // Task 12350 - Display/Edit/Delete/Add Adapter service
                    let arrAdapterList = [];
                    arrAdapterList.push(oController._getCreateAdapterObject());
                    arrAdapterList.push(oController._getEmptyAdapterObject());

                    // Task 12351 - Display/Edit/Delete/Add Configurations
                    let arrConfigList = [];
                    arrConfigList.push(oController._getCreateConfigurationObject());
                    arrConfigList.push(oController._getEmptyConfigurationObject());
                    
                    oSelectedTemplateStep.StepNo = Number(oSelectedTemplateStep.StepNo);
                     
                    //Bug 12772 Fix adapter values                  
                    let oMatchedStep = oMatchedProcessGoal.PROGOAL2STEPNAV.results.find(oProgoalStep => {
                        return oMatchedProcessGoal.Name === oSelectedTemplate.ProcessGoal &&
                        oProgoalStep.StepName === oSelectedTemplateStep.StepType;
                    });

                    let arrAdapterListCopy = [];
                    if(oMatchedStep) {
                        // Bug 12433 - Some process templates not displaying in RDG
                        if (!oMatchedStep.hasOwnProperty("STEPTOADAPTERNAV")) {
                            oMatchedStep.STEPTOADAPTERNAV = { "results": [] };
                        }
                        if (!oMatchedStep.STEPTOADAPTERNAV.hasOwnProperty("results")) {
                            oMatchedStep.STEPTOADAPTERNAV.results = [];
                        }
                        // Task 12350 - Display/Edit/Delete/Add Adapter service
                        oMatchedStep.STEPTOADAPTERNAV.results.forEach(oAdapter => {
                            arrAdapterList.push(oAdapter);
                        });
                        jQuery.extend(true, arrAdapterListCopy, arrAdapterList);
                        oSelectedTemplateStep.arrAdapterList = arrAdapterListCopy;
                        
                        let oMatchedAdapter = oSelectedTemplateStep.arrAdapterList.find(oAdapter => {
                            return oAdapter.AdapterName === oSelectedTemplateStep.Adapter;
                        });
                        
                        // Task 12351 - Display/Edit/Delete/Add Configurations
                        let arrConfigListCopy = [];
                        if(oMatchedAdapter) {
                            // Bug 13216 - added the function that enables/disables the configuration select
                            oController._validateConfigurationField(oMDConsolidationModel, oMatchedAdapter, undefined, oSelectedTemplateStep);
                            
                            // Bug 12433 - Some process templates not displaying in RDG
                            if (!oMatchedAdapter.hasOwnProperty("ADAP2CONFIGNAV")) {
                                oMatchedAdapter.ADAP2CONFIGNAV = { "results": [] };
                            }
                            if (!oMatchedAdapter.ADAP2CONFIGNAV.hasOwnProperty("results")) {
                                oMatchedAdapter.ADAP2CONFIGNAV.results = [];
                            }

                            if(oSelectedTemplateStep.StepType === "MTC"){

                                try {
                                    // Bug 13216 - pass oController instead of this
                                    let arrOData = await GetListsData.getConfigurationMTCDetails(oController);
                    
                                    if (arrOData.length) {
                                        let arrFilteredOData = arrOData.filter((el)=> el.OTC === oMatchedAdapter.BoType);
                                        arrFilteredOData.forEach((element)=>{
                                            let oTemp = {
                                                Zkey: "",
                                                Id: element.Configuration,
                                                Description: element.Description
                                            };

                                            arrConfigList.push(oTemp);

                                        });
                                    }
                    
                                } catch (oError) {
                                    if (oError.statusCode !== "404") {
                                        Utilities.showPopupAlert(
                                            "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                                            MessageBox.Icon.ERROR, 
                                            "Error"
                                        );
                                    }
                                }

                            } else {
                                for (let j = 0; j < oMatchedAdapter.ADAP2CONFIGNAV.results.length; j++) {
                                    const oConfiguration = oMatchedAdapter.ADAP2CONFIGNAV.results[j];
                                    if (oConfiguration.Id) {
                                        arrConfigList.push(oConfiguration);
                                    }
                                }
                            }
                            jQuery.extend(true, arrConfigListCopy, arrConfigList);
                            oSelectedTemplateStep.arrConfigList = arrConfigListCopy;
                        } else {
                            jQuery.extend(true, arrConfigListCopy, arrConfigList);
                            oSelectedTemplateStep.arrConfigList = arrConfigListCopy;
                        }
                    } else {
                        // Task 12350 - Display/Edit/Delete/Add Adapter service
                        jQuery.extend(true, arrAdapterListCopy, arrAdapterList);
                        oSelectedTemplateStep.arrAdapterList = arrAdapterListCopy;
                    }
                }
            } else {
                oMDConsolidationModel.setProperty("/arrStepTypeList", undefined);
            }
        } else {
            oMDConsolidationModel.setProperty("/arrProcessGoalsList", undefined);
            oMDConsolidationModel.setProperty("/arrStepTypeList", undefined);
        }
        // Bug 12399 - Process Template name shows as missing when its not blank
        oMDConsolidationModel.setProperty("/selectedProcessTemplate", oSelectedTemplate);
        oMDConsolidationModel.setProperty("/templateValueState", "None");
        oMDConsolidationModel.setProperty("/templateStateText", "");

        // Bug 13216 - using oController instead of this
        oController.validateFormToSave();
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * On select of BO Type select box item
     * Based on the selection of bo type, fill the Process Goal list for select box
     */
    MDConsolidationController.onChangeBoType = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sSelectedBoType = oEvent.getParameters().selectedItem.getKey();
        this.byId("processGoal").setEnabled(true);
        let oMatchedBoTypeInfo = oMDConsolidationModel.getProperty("/arrBoTypeList").find(oBoType => {
            return oBoType.BoType === sSelectedBoType;
        });
        if(oMatchedBoTypeInfo) {
            oMDConsolidationModel.setProperty("/arrProcessGoalsList", oMatchedBoTypeInfo.BO2PROCESSGOALNAV.results);
        } else {
            oMDConsolidationModel.setProperty("/arrProcessGoalsList", undefined);
        }
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/TEMPLATE2STEPNAV/results", []);
        oMDConsolidationModel.setProperty("/arrStepTypeList", undefined);
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ProcessGoal", undefined);
        // Bug 12364 - Required information for process templates
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/Strategy", undefined);
        oMDConsolidationModel.setProperty("/oEnableProperties/Strategy", undefined);
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/BoTypeValueState", "None");
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/BoTypeValueStateText", "");
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ProcessGoalValueState", "Error");
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ProcessGoalValueStateText", "Process Goal is Mandatory");
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ebAddBtn", false);
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ebDelBtn", false);

        this.validateFormToSave();
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * On select of Process goal select box item
     * Based on the selection of bo type, fill the step type list for select box
     */
    MDConsolidationController.onChangeProcessGoal = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oSelectedProcessTemplate = oMDConsolidationModel.getProperty("/selectedProcessTemplate");
        let sStrategyValueState = oMDConsolidationModel.getProperty("/selectedProcessTemplate/StrategyValueState");
        let sSelectedProcessGoal = oEvent.getParameters().selectedItem.getKey();
        let sSelectedBoType = oSelectedProcessTemplate.BoType;
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ebAddBtn", true);
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/TEMPLATE2STEPNAV/results", []);
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ProcessGoalValueState", "None");
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/ProcessGoalValueStateText", "");

        if(sSelectedProcessGoal==="A"){
            oMDConsolidationModel.setProperty("/selectedProcessTemplate/StrategyValueState", "Error");
            oMDConsolidationModel.setProperty("/selectedProcessTemplate/StrategyValueStateText", "Strategy is mandatory when Process Goal is 'A'");
        } else{
            if(sStrategyValueState==="Error"){
                oMDConsolidationModel.setProperty("/selectedProcessTemplate/StrategyValueState", "None");
                oMDConsolidationModel.setProperty("/selectedProcessTemplate/StrategyValueStateText", "");    
            }
        }
        if (sSelectedBoType) {
            let oMatchedBoType = oMDConsolidationModel.getProperty("/arrBoTypeList").find(oBoType => {
                return oBoType.BoType === sSelectedBoType;
            });
            if(oMatchedBoType) {
                let oMatchedProcessGoal = oMatchedBoType.BO2PROCESSGOALNAV.results.find(oProcessGoal =>{
                    return oProcessGoal.Name === sSelectedProcessGoal;
                });
                if(oMatchedProcessGoal) {
                    oMDConsolidationModel.setProperty("/oEnableProperties", {
                        DefaultTemplate: oMatchedProcessGoal.Defaultprocesstemplate,
                        DeleteSource: oMatchedProcessGoal.Deletesource,
                        Strategy: oMatchedProcessGoal.StrategyFlag
                    });
                    oSelectedProcessTemplate.DefaultTemplate = oMatchedProcessGoal.Defaultprocesstemplate ? oSelectedProcessTemplate.DefaultTemplate : false;
                    oSelectedProcessTemplate.DeleteSource = oMatchedProcessGoal.Deletesource ? oSelectedProcessTemplate.DeleteSource : false;
                    if(!oMatchedProcessGoal.StrategyFlag) {
                        if(oMatchedProcessGoal.StrategyProperty) {
                            oSelectedProcessTemplate.Strategy = oMatchedProcessGoal.StrategyProperty;
                        } else {
                            oSelectedProcessTemplate.Strategy = undefined;
                        }
                    }
                    oMDConsolidationModel.setProperty("/arrStepTypeList", oMatchedProcessGoal.PROGOAL2STEPNAV.results);
                    oMDConsolidationModel.refresh();
                }
            }
        }

        this.validateFormToSave();

    };

    MDConsolidationController.onChangeStrategy = function(){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sStrategyValueState = oMDConsolidationModel.getProperty("/selectedProcessTemplate/StrategyValueState");

        if(sStrategyValueState==="Error"){
            oMDConsolidationModel.setProperty("/selectedProcessTemplate/StrategyValueState", "None");
            oMDConsolidationModel.setProperty("/selectedProcessTemplate/StrategyValueStateText", "");    
        }
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * On live change Process Template
     * Valiate the user entry for process template
     * Error if Process template does not start with Y or Z
     * Erro if process template already exists
     */
    MDConsolidationController.onLiveChangeProcessTemplate = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let arrTemplateList = oMDConsolidationModel.getProperty("/TemplateList");
        let sKey = oEvent.getParameter("value").toUpperCase();
        oEvent.getSource().setValue(sKey.toUpperCase());
        let oMatchedTemplate;
        try{
            oMatchedTemplate = arrTemplateList.find(oTemplate => {
                return oTemplate.ProcessTemplate.toUpperCase() === sKey;
            });
        }catch (e){
            oMatchedTemplate = arrTemplateList.results.find(oTemplate => {
                return oTemplate.ProcessTemplate.toUpperCase() === sKey;
            });
        }

        // Bug 12399 - Process Template name shows as missing when its not blank
        if (sKey) {
            if (oMatchedTemplate) {
                oMDConsolidationModel.setProperty("/templateValueState", "Error");
                oMDConsolidationModel.setProperty("/templateStateText", "Process template already exists");
            } else if (sKey.substring(0, 1) !== "Z" && sKey.substring(0, 1) !== "Y") {
                oMDConsolidationModel.setProperty("/templateValueState", "Error");
                oMDConsolidationModel.setProperty("/templateStateText", "Process template must start with Y or Z");
            } else {
                oMDConsolidationModel.setProperty("/templateValueState", undefined);
                oMDConsolidationModel.setProperty("/templateStateText", undefined);
            }
        } else {
            oMDConsolidationModel.setProperty("/templateValueState", "Error");
            oMDConsolidationModel.setProperty("/templateStateText", "Process template is mandatory");
        }

        this.validateFormToSave();
    };

    MDConsolidationController.onLiveChangeDescription = function (oEvent) {
        let oDescriptionInput = oEvent.getSource();
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        if(!oDescriptionInput.getValue()){
            oMDConsolidationModel.setProperty("/selectedProcessTemplate/DescriptionValueState", "Error");
            oMDConsolidationModel.setProperty("/selectedProcessTemplate/DescriptionValueStateText", "Description is Mandatory");
        } else{
            oMDConsolidationModel.setProperty("/selectedProcessTemplate/DescriptionValueState", "None");
            oMDConsolidationModel.setProperty("/selectedProcessTemplate/DescriptionValueStateText", "");
        }

        this.validateFormToSave();

    };

    MDConsolidationController.validateFormToSave = function(){
        let oController = this;
        let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
        let oProcessTemplate = oMDConsolidationModel.getProperty("/selectedProcessTemplate");
        let sTemplateValueState = oMDConsolidationModel.getProperty("/templateValueState");
        let saveButton = oController.getView().byId("saveProcessTemplate");
		let oValidationResult = true;

        // Bug 12399 - Process Template name shows as missing when its not blank
        if(!oProcessTemplate.ProcessTemplate || sTemplateValueState === "Error" || !oProcessTemplate.Description || 
            oProcessTemplate.DescriptionValueState === "Error" || !oProcessTemplate.BoType || oProcessTemplate.BoTypeValueState === "Error" 
            || !oProcessTemplate.ProcessGoal || oProcessTemplate.ProcessGoalValueState === "Error") {
                // Utilities.showPopupAlert("Please fill all mandatory details for process template and try again", 
                //                         MessageBox.Icon.ERROR, 
                //                         "Missing Mandatory Details");
            oValidationResult=false;
        }

        // Bug 12364 - Required information for process templates
        if (oProcessTemplate.ProcessGoal === "A" && !oProcessTemplate.Strategy) {
            // Utilities.showPopupAlert("Please define the Strategy as it is mandatory when Process Goal is 'A'", 
            //                             MessageBox.Icon.ERROR, 
            //                             "Missing Mandatory Details");
            oValidationResult=false;
        }

        // Bug 12403 - Process templates require steps for process goals except for B
        let arrTemplateStep = [];
        if (oProcessTemplate.hasOwnProperty("TEMPLATE2STEPNAV")) {
            if (oProcessTemplate.TEMPLATE2STEPNAV.hasOwnProperty("results")) {
                arrTemplateStep = oProcessTemplate.TEMPLATE2STEPNAV.results;
            }
        }

        // Bug 12403 - Process templates require steps for process goals except for B
        if (arrTemplateStep.length === 0 && oProcessTemplate.ProcessGoal !== "B") {
            // Utilities.showPopupAlert("Please enter at least one Process Template Step", 
            //                             MessageBox.Icon.ERROR, 
            //                             "Missing Mandatory Details");
                oValidationResult=false;
        }

        if(arrTemplateStep && arrTemplateStep.length > 0) {
            for(let i=0; i< arrTemplateStep.length; i++) {
                if(!arrTemplateStep[i].StepNo || !arrTemplateStep[i].StepType || !arrTemplateStep[i].StepDescription) {
                    // Utilities.showPopupAlert("Please fill all mandatory details for process template step and try again", 
                    //                     MessageBox.Icon.ERROR, 
                    //                     "Missing Mandatory Details");
                    oValidationResult=false;
                }
            }
        }

        if(oValidationResult){
            saveButton.setEnabled(true);
        }else{
            saveButton.setEnabled(false);
        }
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * On click of Add Template Step
     * Check if row has been selected
     *  If yes -> Add a row below the selected row
     *  If no -> Push row to the last
     */
    MDConsolidationController.onAddProcessTemplateStep = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oProcessTemplateStepTable = this.byId("idProcessTemplateStepTable");
        let sSelectedRowPath = oProcessTemplateStepTable.getSelectedContextPaths()[0];
        let arrTemplateStep = oMDConsolidationModel.getProperty("/selectedProcessTemplate/TEMPLATE2STEPNAV/results");
        let oDelBtn = oMDConsolidationModel.getProperty("/selectedProcessTemplate/ebDelBtn");
        let oNewRow = {};

        // Bug 12399 - Process Template name shows as missing when its not blank
        if (!arrTemplateStep) {
            arrTemplateStep = [];
            oMDConsolidationModel.setProperty("/selectedProcessTemplate/TEMPLATE2STEPNAV", {});
        }

        if(!oDelBtn){
            oMDConsolidationModel.setProperty("/selectedProcessTemplate/ebDelBtn", true);
        }

        if(sSelectedRowPath) {
            let oCurrentRow = oMDConsolidationModel.getProperty(sSelectedRowPath);
            if(oCurrentRow) {
                //Add the information to current model not only the position
                oNewRow = { StepNo: oCurrentRow.StepNo + 1,
                            StepDescValueState: "Error",
                            StepDescValueStateText: "Description is Mandatory",
                            processTempTypeValueState: "Error",
                            processTempTypeValueStateText: "Step Type is Mandatory"               
                };
            } else {
                oNewRow = { StepNo: arrTemplateStep.length + 1, 
                            StepDescValueState: "Error",
                            StepDescValueStateText: "Description is Mandatory",
                            processTempTypeValueState: "Error",
                            processTempTypeValueStateText: "Step Type is Mandatory"                 
                };
            }
            
			//Get the row index selected
			let iSelectedIndex = parseInt(sSelectedRowPath.substring(sSelectedRowPath.lastIndexOf("/") + 1));
			arrTemplateStep.splice(iSelectedIndex + 1, 0, oNewRow);
            //Rearranging all rows based on their sequence
            arrTemplateStep.forEach((oTemplateStep, iIndex) => {
                oTemplateStep.StepNo = iIndex + 1;
            });
		} else {
            oNewRow = { 
                StepNo: arrTemplateStep.length + 1,
                StepDescValueState: "Error",
                StepDescValueStateText: "Description is Mandatory",
                processTempTypeValueState: "Error",
                processTempTypeValueStateText: "Step Type is Mandatory"
            };
            arrTemplateStep.push(oNewRow);
		}
        
        oMDConsolidationModel.setProperty("/selectedProcessTemplate/TEMPLATE2STEPNAV/results", arrTemplateStep);
        oProcessTemplateStepTable.setSelectedContextPaths(); //Unselect row after adding
        oMDConsolidationModel.refresh();

        this.validateFormToSave();
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * On click of Delete Template Step
     * Selecting a row before click of Delete is mandatory
     * Delete the row from the template step table
     */
    MDConsolidationController.onDeleteProcessTemplateStep = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oProcessTemplateStepTable = this.byId("idProcessTemplateStepTable");
        let sSelectedRowPath = oProcessTemplateStepTable.getSelectedContextPaths()[0];
        let arrTemplateStep = oMDConsolidationModel.getProperty("/selectedProcessTemplate/TEMPLATE2STEPNAV/results");
        if(sSelectedRowPath) {
            let iSelectedIndex = parseInt(sSelectedRowPath.substring(sSelectedRowPath.lastIndexOf("/") + 1));
            
            // TASK 12627 - MDC - Add support to FAR (Filter and Remove)
            if(arrTemplateStep.length !== iSelectedIndex+1){
                let sNextRowStepType = arrTemplateStep[iSelectedIndex+1].StepType;
    
                if(sNextRowStepType==="FAR" && iSelectedIndex===0){
                    Utilities.showPopupAlert("The next step type is 'Filter and Remove' and cannot be the first step.", 
                    MessageBox.Icon.ERROR, 
                    "Action not possible");
                    return;
                }
            }
            //END  TASK 12627 - MDC - Add support to FAR (Filter and Remove)

            arrTemplateStep.splice(iSelectedIndex, 1);
            //Rearranging all rows based on their sequence
            arrTemplateStep.forEach((oTemplateStep, iIndex) => {
                oTemplateStep.StepNo = iIndex + 1;
            });
        } else {
            Utilities.showPopupAlert("Select a row to delete", 
            MessageBox.Icon.ERROR, 
            "Action not possible");
        }
        if(arrTemplateStep.length<1){
            oMDConsolidationModel.setProperty("/selectedProcessTemplate/ebDelBtn", false);
        }

        oProcessTemplateStepTable.setSelectedContextPaths(); //Unselect row after adding
        oMDConsolidationModel.refresh();
        this.validateFormToSave();
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * On select of Step type select box item
     * Based on the selection of step type, fill the Adapter select box items
     */
    MDConsolidationController.onChangeStepType = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sChangeRowPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let sSelectedBoType = oMDConsolidationModel.getProperty("/selectedProcessTemplate/BoType");
        let sSelectedProcessGoal = oMDConsolidationModel.getProperty("/selectedProcessTemplate/ProcessGoal");
        let sSelectedStepType = oEvent.getSource().getSelectedKey();

        oMDConsolidationModel.setProperty(sChangeRowPath + "/Adapter", undefined);
        oMDConsolidationModel.setProperty(sChangeRowPath + "/ConfigId", undefined);
        oMDConsolidationModel.setProperty(sChangeRowPath + "/processTempTypeValueState", "None");
        oMDConsolidationModel.setProperty(sChangeRowPath + "/processTempTypeValueStateText", "");

        let arrRowPath = sChangeRowPath.split("/");
        let nStepNo = arrRowPath[arrRowPath.length-1];
        
        if(sSelectedStepType === "FAR" && nStepNo === "0"){
            Utilities.showPopupAlert("Filter and Remove cannot be the first step", 
            MessageBox.Icon.ERROR, 
            "Action not possible");
            oMDConsolidationModel.setProperty(sChangeRowPath + "/processTempTypeValueState", "Error");
            oMDConsolidationModel.setProperty(sChangeRowPath + "/processTempTypeValueStateText", "Step Type is Mandatory");
            oEvent.getSource().setSelectedKey();
            this.validateFormToSave();
            return;
        }

        if (sSelectedBoType) {
            let oMatchedBoType = oMDConsolidationModel.getProperty("/arrBoTypeList").find(oBoType => {
                return oBoType.BoType === sSelectedBoType;
            });
            if(oMatchedBoType && sSelectedProcessGoal) {
                let oMatchedProcessGoal = oMatchedBoType.BO2PROCESSGOALNAV.results.find(oProcessGoal =>{
                    return oProcessGoal.Name === sSelectedProcessGoal;
                });
                if(oMatchedProcessGoal && sSelectedStepType) {
                    let oMatchedStepType = oMatchedProcessGoal.PROGOAL2STEPNAV.results.find(oStepType => {
                       return oStepType.StepName === sSelectedStepType;
                    });
                    if(oMatchedStepType) {
                        // Bug 12433 - Some process templates not displaying in RDG
                        if (!oMatchedStepType.hasOwnProperty("STEPTOADAPTERNAV")) {
                            oMatchedStepType.STEPTOADAPTERNAV = { "results": [] };
                        }
                        if (!oMatchedStepType.STEPTOADAPTERNAV.hasOwnProperty("results")) {
                            oMatchedStepType.STEPTOADAPTERNAV.results = [];
                        }
                        // Task 12350 - Display/Edit/Delete/Add Adapter service
                        let arrAdapterList = [];
                        arrAdapterList.push(this._getCreateAdapterObject());
                        arrAdapterList.push(this._getEmptyAdapterObject());
                        oMatchedStepType.STEPTOADAPTERNAV.results.forEach(oAdapter => {
                            arrAdapterList.push(oAdapter);
                        });
                        oMDConsolidationModel.setProperty(sChangeRowPath + "/arrAdapterList", arrAdapterList);
                        oMDConsolidationModel.setProperty(sChangeRowPath + "/Adapter", "");
                    }
                }
            }
            oMDConsolidationModel.refresh();
        }
        this.validateFormToSave();
    };

    MDConsolidationController.onLiveChangeDescStep = function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sChangeRowPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let sDescInput = oEvent.getSource().getValue();

        oMDConsolidationModel.setProperty(sChangeRowPath + "/StepDescription", sDescInput);

        if(!sDescInput){
            oMDConsolidationModel.setProperty(sChangeRowPath + "/StepDescValueState", "Error");
            oMDConsolidationModel.setProperty(sChangeRowPath + "/StepDescValueStateText", "Description is Mandatory");
        } else {
            oMDConsolidationModel.setProperty(sChangeRowPath + "/StepDescValueState", "None");
            oMDConsolidationModel.setProperty(sChangeRowPath + "/StepDescValueStateText", "");
        }

        this.validateFormToSave();
    };

    // Task 12350 - Display/Edit/Delete/Add Adapter service
    MDConsolidationController.openAdapterFragment = async function(oEvent, sSelectedAdapter) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sChangeRowPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let sSelectedBoType = oMDConsolidationModel.getProperty("/selectedProcessTemplate/BoType");
        let sSelectedStepType = oMDConsolidationModel.getProperty(sChangeRowPath + "/StepType");
        let sOperationType;

        if (sSelectedAdapter === "Custom") {
            sOperationType = "C";
        } else {
            sOperationType = "U";
        }

        sap.ui.core.BusyIndicator.show();

        let arrAdapterDetails = [];
        let oAdapterDetails = {};
        try {
            arrAdapterDetails = await GetListsData.getAdapterDetails(this, sSelectedBoType, sSelectedStepType, sSelectedAdapter);
            oAdapterDetails = arrAdapterDetails[0];
            if (!oAdapterDetails) {
                oAdapterDetails = { ProcessStepAdapter: "", AdapterDescription: "", Usage: "" };
            }
        } catch (oError) {
            Utilities.showPopupAlert(
                "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                MessageBox.Icon.ERROR, 
                "Error"
            );
        }

        let arrAdapterList = [];
        try {
            arrAdapterList = await GetListsData.getAdapterList(this);
            arrAdapterList.unshift({ Adapter: "", Description: ""});
        } catch (oError) {
            Utilities.showPopupAlert(
                "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                MessageBox.Icon.ERROR, 
                "Error"
            );
        }

        let arrUsageList = [];
        try {
            arrUsageList = await GetListsData.getUsageList(this);
        } catch (oError) {
            Utilities.showPopupAlert(
                "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                MessageBox.Icon.ERROR, 
                "Error"
            );
        }

        sap.ui.core.BusyIndicator.hide();

        let oMDCAdapterModel = {
            OperationType: sOperationType,
            SelectedRow: sChangeRowPath, 
            BOType: sSelectedBoType,
            StepType: sSelectedStepType,
            AdapterList: arrAdapterList,
            Adapter: ( oAdapterDetails.ProcessStepAdapter ? oAdapterDetails.ProcessStepAdapter : "" ),
            AdapterValueState: ( oAdapterDetails.ProcessStepAdapter ? "None" : "Error" ), 
            AdapterValueStateText: ( oAdapterDetails.ProcessStepAdapter ? "" : "Adapter is mandatory" ),
            Description: ( oAdapterDetails.AdapterDescription ? oAdapterDetails.AdapterDescription : "" ),
            DescriptionValueState: ( oAdapterDetails.AdapterDescription ? "None" : "Error" ), 
            DescriptionValueStateText: ( oAdapterDetails.AdapterDescription ? "" : "Description is mandatory" ),
            UsageList: arrUsageList,
            Usage: ( oAdapterDetails.Usage ? oAdapterDetails.Usage : "" )
        };

        oMDConsolidationModel.setProperty("/MDCAdapterModel", oMDCAdapterModel);
        let oMDCAdapterFragment = this._getMDCAdapterFragment();
        oMDCAdapterFragment.open();
    };
    
    // Task 12351 - Display/Edit/Delete/Add Configurations
    MDConsolidationController._getEmptyCrTypeObject = function() {
        return { CrType: "", CrDesc: "" };
    };

    // Task 12351 - Display/Edit/Delete/Add Configurations
    MDConsolidationController.openConfigurationFragment = async function(oEvent, sSelectedConfiguration) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sChangeRowPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let sSelectedBoType = oMDConsolidationModel.getProperty("/selectedProcessTemplate/BoType");
        let sStepType = oMDConsolidationModel.getProperty(sChangeRowPath + "/StepType");
        let sOperationType;
        let oMDCConfigFragment;

        if (sStepType !== "ACT" && sStepType !== "FAR" && sStepType !== "EVA" && sStepType !== "BRC" && sStepType !== "MTC") {
            Utilities.showPopupAlert(
                // Bug 13013 - added the "BRC" to the message
                "Manage Configurations is only available for 'Activation', 'Filter and Remove', 'Evaluation', 'Best Record Calculation' and 'Matching' with 'Fuzzy Ruleset' adapter Step Types", 
                MessageBox.Icon.ERROR, 
                "Feature not implemented"
            );
            // Bug 12582 - Process Steps allows to save 'Custom' config for type other than ACT
            oMDConsolidationModel.setProperty(sChangeRowPath + "/ConfigId", "");
            return;
        }

        // Bug 12510 - When I click "Custom - Create Configuration" it opens ZTESTACT instead of creating new entry
        if (sSelectedConfiguration === "Custom") {
            sOperationType = "C";
        } else {
            sOperationType = "U";
        }

        sap.ui.core.BusyIndicator.show();

        let arrConfigDetails = [];
        let oConfigDetails = {};
        let oConfigData = {};
        
        if(sStepType ==="ACT"){

            try {
                arrConfigDetails = await GetListsData.getConfigurationACTDetails(this, sSelectedBoType, sSelectedConfiguration);
                if (arrConfigDetails && arrConfigDetails.length) {
                    oConfigDetails = arrConfigDetails[0];
                }
            } catch (oError) {
                if (oError.statusCode !== "404") {
                    Utilities.showPopupAlert(
                        "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                        MessageBox.Icon.ERROR, 
                        "Error"
                    );
                }
            }
    
            //Bug 12473 - Do not allow user to create Configurations for BoType 986 and MDC_147
            if (sOperationType === "C" && oConfigDetails.NotAllowed === "X") {
                Utilities.showPopupAlert(
                    "Not allowed to create configurations for Process Template " + sSelectedBoType, 
                    MessageBox.Icon.ERROR, 
                    "Action not possible"
                );
                return;
            }

            // Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog
            let arrConfigurationDropdownLists = oMDConsolidationModel.getProperty("/arrConfigurationDropdownLists");
            let arrNewCrTypeList = arrConfigurationDropdownLists.filter(oElement => {
                return oElement.BoType === sSelectedBoType && oElement.Case === "N" && oElement.Target === oConfigDetails.NewTarget;
            });
            arrNewCrTypeList.unshift(this._getEmptyCrTypeObject());
            let arrNewCrTypeErrList = arrConfigurationDropdownLists.filter(oElement => {
                return oElement.BoType === sSelectedBoType && oElement.Case === "NI" && oElement.Target === oConfigDetails.NewTargetErr;
            });
            arrNewCrTypeErrList.unshift(this._getEmptyCrTypeObject());
            let arrUpdCrTypeList = arrConfigurationDropdownLists.filter(oElement => {
                return oElement.BoType === sSelectedBoType && oElement.Case === "U" && oElement.Target === oConfigDetails.UpdTarget;
            });
            arrUpdCrTypeList.unshift(this._getEmptyCrTypeObject());
            let arrUpdCrTypeErrList = arrConfigurationDropdownLists.filter(oElement => {
                return oElement.BoType === sSelectedBoType && oElement.Case === "UI" && oElement.Target === oConfigDetails.UpdTargetErr;
            });
            arrUpdCrTypeErrList.unshift(this._getEmptyCrTypeObject());
            let arrMtcCrTypeList = arrConfigurationDropdownLists.filter(oElement => {
                return oElement.BoType === sSelectedBoType && oElement.Case === "M" && oElement.Target === oConfigDetails.MtcTarget;
            });
            arrMtcCrTypeList.unshift(this._getEmptyCrTypeObject());
            let arrMtcCrTypeErrList = arrConfigurationDropdownLists.filter(oElement => {
                return oElement.BoType === sSelectedBoType && oElement.Case === "MI" && oElement.Target === oConfigDetails.MtcTargetErr;
            });
            arrMtcCrTypeErrList.unshift(this._getEmptyCrTypeObject());

            oConfigData = {
                // Bug 12733 - Assigning the received data to the object to be shown
                ...oConfigDetails,
                OperationType: sOperationType,
                StepType: sStepType,
                SelectedRow: sChangeRowPath, 
                ConfigIdValueState: ( oConfigDetails.ConfigId ? "None" : "Error" ),
                ConfigIdValueStateText: ( oConfigDetails.ConfigId ? "" : "Configuration ID is mandatory" ),
                DescValueState: ( oConfigDetails.Desc ? "None" : "Error" ),
                DescValueStateText: ( oConfigDetails.Desc ? "" : "Configuration description is mandatory" ),
                BoType: sSelectedBoType,
                NewTargetList: GetListsData.getNewRecordsTargetList(sSelectedBoType),
                NewTargetErrList: GetListsData.getNewRecordsErrTargetList(sSelectedBoType),
                UpdTargetList: GetListsData.getUpdatedRecordsTargetList(sSelectedBoType),
                UpdTargetErrList: GetListsData.getUpdatedRecordsErrTargetList(sSelectedBoType),
                MtcTargetList: GetListsData.getMatchGroupsTargetList(sSelectedBoType),
                MtcTargetErrList: GetListsData.getMatchGroupsErrTargetList(sSelectedBoType),
                // Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog
                NewCrTypeList: arrNewCrTypeList,
                NewCrTypeValueState: ( oConfigDetails.NewCrType ? "None" : "Error" ),
                NewCrTypeValueStateText: ( oConfigDetails.NewCrType ? "" : "CR Type is mandatory" ),
                NewCrTypeErrList: arrNewCrTypeErrList,
                NewCrTypeErrValueState: ( oConfigDetails.NewCrTypeErr ? "None" : "Error" ),
                NewCrTypeErrValueStateText: ( oConfigDetails.NewCrTypeErr ? "" : "CR Type is mandatory" ),
                UpdCrTypeList: arrUpdCrTypeList,
                UpdCrTypeValueState: ( oConfigDetails.UpdCrType ? "None" : "Error" ),
                UpdCrTypeValueStateText: ( oConfigDetails.UpdCrType ? "" : "CR Type is mandatory" ),
                UpdCrTypeErrList: arrUpdCrTypeErrList,
                UpdCrTypeErrValueState: ( oConfigDetails.UpdCrTypeErr ? "None" : "Error" ),
                UpdCrTypeErrValueStateText: ( oConfigDetails.UpdCrTypeErr ? "" : "CR Type is mandatory" ),
                MtcCrTypeList: arrMtcCrTypeList,
                MtcCrTypeValueState: ( oConfigDetails.MtcCrType ? "None" : "Error" ),
                MtcCrTypeValueStateText: ( oConfigDetails.MtcCrType ? "" : "CR Type is mandatory" ),
                MtcCrTypeErrList: arrMtcCrTypeErrList,
                MtcCrTypeErrValueState: ( oConfigDetails.MtcCrTypeErr ? "None" : "Error" ),
                MtcCrTypeErrValueStateText: ( oConfigDetails.MtcCrTypeErr ? "" : "CR Type is mandatory" ),
                ConfigurationDropdownLists: arrConfigurationDropdownLists
            };
            

        } else if(sStepType==="FAR"){

            try {
                arrConfigDetails = await GetListsData.getConfigurationFARDetails(this, sSelectedConfiguration);

                if (arrConfigDetails && arrConfigDetails.length) {
                    oConfigDetails = arrConfigDetails[0];
                }

            } catch (oError) {
                if (oError.statusCode !== "404") {
                    Utilities.showPopupAlert(
                        "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                        MessageBox.Icon.ERROR, 
                        "Error"
                    );
                }
            }

            oConfigData = {
                OperationType: sOperationType,
                StepType: oConfigDetails.StepType,
                SelectedRow: sChangeRowPath, 
                ConfigId: oConfigDetails.ConfigId,
                ConfigIdValueState: ( oConfigDetails.ConfigId ? "None" : "Error" ),
                ConfigIdValueStateText: ( oConfigDetails.ConfigId ? "" : "Configuration ID is mandatory" ),
                Description: oConfigDetails.Description,
                DescValueState: ( oConfigDetails.Description ? "None" : "Error" ),
                DescValueStateText: ( oConfigDetails.Description ? "" : "Configuration description is mandatory" ),
                PredecessorProcessValueState: ( oConfigDetails.StepType ? "None" : "Error" ),
                PredecessorProcessValueStateText: ( oConfigDetails.StepType ? "" : "Predecessor Process is mandatory" ),
                GroupsMtc: oConfigDetails.GroupsMtc,
                SingleBrc: oConfigDetails.SingleBrc,
                GroupsBrc: oConfigDetails.GroupsBrc,
                UpdateBrc: oConfigDetails.UpdateBrc,
                SingleWarVal: oConfigDetails.SingleWarVal,
                GroupsWarVal: oConfigDetails.GroupsWarVal,
                UpdateWarVal: oConfigDetails.UpdateWarVal,
                SingleActVal: oConfigDetails.SingleActVal,
                GroupsActVal: oConfigDetails.GroupsActVal
            };
        } else if(sStepType==="EVA"){
            try {
                arrConfigDetails = await GetListsData.getConfigurationEVADetails(this, sSelectedConfiguration);

                if (arrConfigDetails && arrConfigDetails.length) {
                    oConfigDetails = arrConfigDetails[0];
                }

            } catch (oError) {
                if (oError.statusCode !== "404") {
                    Utilities.showPopupAlert(
                        "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                        MessageBox.Icon.ERROR, 
                        "Error"
                    );
                }
            }

            oConfigData = {
                OperationType: sOperationType,
                StepType: oConfigDetails.StepType,
                SelectedRow: sChangeRowPath, 
                ConfigId: oConfigDetails.ConfigId,
                ConfigIdValueState: ( oConfigDetails.ConfigId ? "None" : "Error" ),
                ConfigIdValueStateText: ( oConfigDetails.ConfigId ? "" : "Configuration ID is mandatory" ),
                Description: oConfigDetails.Description,
                DescriptionValueState: ( oConfigDetails.Description ? "None" : "Error" ),
                DescriptionValueStateText: ( oConfigDetails.Description ? "" : "Configuration description is mandatory" ),
                Parallel: oConfigDetails.Parallel,
                PackageSize: oConfigDetails.PackageSize,
                QueuePrefix: oConfigDetails.QueuePrefix,
                ProvideScores: oConfigDetails.ProvideScores,
                DeleteResults: oConfigDetails.DeleteResults,
                EvaluationResults: oConfigDetails.EvaluationResults,
                DeleteScores: oConfigDetails.DeleteScores,
                EvaluationScores: oConfigDetails.EvaluationScores,
                GroupsWarVAL: oConfigDetails.GroupsWarVAL
            };

        }  else if(sStepType==="BRC"){
            let arrBRCRRules;
            try {
                arrConfigDetails = await GetListsData.getConfigurationBRCDetails(this, sSelectedBoType, sSelectedConfiguration);

                if (arrConfigDetails && arrConfigDetails.length) {
                    oConfigDetails = arrConfigDetails[0];
                }

            } catch (oError) {
                if (oError.statusCode !== "404") {
                    Utilities.showPopupAlert(
                        "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                        MessageBox.Icon.ERROR, 
                        "Error"
                    );
                }
            }
            // Bug 12943 - Added parallel properties for validation
            oConfigData = {
                BoType: sSelectedBoType,
                OperationType: sOperationType,
                StepType: sStepType,
                SelectedRow: sChangeRowPath, 
                SelectedConfiguration: sSelectedConfiguration,
                ConfigId: oConfigDetails.ConfigId,
                ConfigIdValueState: ( oConfigDetails.ConfigId ? "None" : "Error" ),
                ConfigIdValueStateText: ( oConfigDetails.ConfigId ? "" : "Configuration ID is mandatory" ),
                Description: oConfigDetails.Description,
                DescriptionValueState: ( oConfigDetails.Description ? "None" : "Error" ),
                DescriptionValueStateText: ( oConfigDetails.Description ? "" : "Configuration Description is mandatory" ),
                Parallel: oConfigDetails.Parallel,
                ParallelValueState: "None",
                ParallelValueStateText: "",
                QueuePrefix: oConfigDetails.QueuePrefix,
                BRCORDER2BRCSOURCESNAV: [],
                BRCORDER2BRCTABLESNAV: [],
                BRCORDER2BRCFIELDSNAV: [],
                BRCORDERTABLE: [],
                BRCORDERTABLEFIELDS: [],
                BRCTableRules: [],
                BRCFieldRules: [],
                BRCAll: [],
                visDeleteButton: sOperationType === "C" ? false : true,
                visChangesText: false
            };

            try {
                let arrBRCAllData = await GetListsData.getConfigurationBRCDetails(this);
                oConfigData.BRCAll = arrBRCAllData;

            } catch (oError) {
                if (oError.statusCode !== "404") {
                    Utilities.showPopupAlert(
                        "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                        MessageBox.Icon.ERROR, 
                        "Error"
                    );
                }
            }

            // Bug 12943 - table dropdown for table should have the "*", for fields should not
            try {
                let arrTableData = await GetListsData.getConfigurationBRCTableSelect(this, sSelectedBoType);
                oConfigData.BRCORDERTABLEFIELDS.push(...arrTableData);
                oConfigData.BRCORDERTABLE.push(...arrTableData);
                oConfigData.BRCORDERTABLE.unshift({
                    TableName: "*",
                    TableDescription: "For all Tables"
                });


            } catch (oError) {
                if (oError.statusCode !== "404") {
                    Utilities.showPopupAlert(
                        "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                        MessageBox.Icon.ERROR, 
                        "Error"
                    );
                }
            }

            try {
                arrBRCRRules = await GetListsData.getBRCRuleSet(this);
                
                let arrTableRules = arrBRCRRules.filter(element => {
                    return element.ForTable === true;
                });
                oConfigData.BRCTableRules = arrTableRules;

                let arrFieldRules = arrBRCRRules.filter(element => {
                    return element.ForField === true;
                });
                oConfigData.BRCFieldRules = arrFieldRules;

            } catch (oError) {
                if (oError.statusCode !== "404") {
                    Utilities.showPopupAlert(
                        "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                        MessageBox.Icon.ERROR, 
                        "Error"
                    );
                }
            }

            // Bug 12943 - check the condition for the table, if table is "" then change the value for "*" and add a description
            oConfigDetails.BRCORDER2BRCSOURCESNAV.results.forEach(element => {
                let oTemp = {...element,
                        TableSelectValueState: "None",
                        TableSelectValueStateText: "",
                        SourceSystemSelectValueState: ( element.SourceSystem ? "None" : "Error" ),
                        SourceSystemSelectStateText: ( element.SourceSystem ? "" : "Source System is Mandatory")
                };

                if(element.Table === ""){
                    oTemp.Table = "*";
                    oTemp.TableDescription = "For all Tables";
                } else {
                    let oTempTableData = oConfigData.BRCORDERTABLE.find(el=>el.TableName===element.Table);
                    oTemp.TableDescription = oTempTableData.TableDescription;
                }


                oConfigData.BRCORDER2BRCSOURCESNAV.push(oTemp);
            });

            // Bug 12943 - check the condition for the table, if table is "" then change the value for "*" and add a description
            oConfigDetails.BRCORDER2BRCTABLESNAV.results.forEach(element => {
                let oTemp = {...element,
                        TableSelectValueState: "None",
                        TableSelectValueStateText: "",
                        RuleIdValueState: ( element.RuleId ? "None" : "Error" ),
                        RuleIdValueStateText: ( element.RuleId ? "" : "Rule ID is Mandatory")
                };

                if(element.Table === ""){
                    oTemp.Table = "*";
                    oTemp.TableDescription = "For all Tables";
                } else {
                    let oTempTableData = oConfigData.BRCORDERTABLE.find(el=>el.TableName===element.Table);
                    oTemp.TableDescription = oTempTableData.TableDescription;
                }


                oConfigData.BRCORDER2BRCTABLESNAV.push(oTemp);
            });

            // Bug 12943 - on fields there is no condition for the table value
            for (let i = 0; i < oConfigDetails.BRCORDER2BRCFIELDSNAV.results.length; i++) {
                const element = oConfigDetails.BRCORDER2BRCFIELDSNAV.results[i];
                let oTemp = {...element,
                    TableSelectValueState: "None",
                    TableSelectValueStateText: "",
                    FieldNameValueState: ( element.Field ? "None" : "Error" ),
                    FieldNameValueStateText: ( element.Field ? "" : "Field name is Mandatory"),
                    RuleIdValueState: ( element.RuleId ? "None" : "Error" ),
                    RuleIdValueStateText: ( element.RuleId ? "" : "Rule ID is Mandatory")
                };

                let oTempTableData = oConfigData.BRCORDERTABLEFIELDS.find(el=>el.TableName===element.Table);
                oTemp.TableDescription = oTempTableData.TableDescription;

                try {
                    let arrFieldData = await GetListsData.getConfigurationBRCFieldsSelect(this, sSelectedBoType, element.Table);
                    oTemp.BRCORDERFIELD = arrFieldData;

                } catch (oError) {
                    if (oError.statusCode !== "404") {
                        Utilities.showPopupAlert(
                            "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                            MessageBox.Icon.ERROR, 
                            "Error"
                        );
                    }
                }

                let oTempFieldData = oTemp.BRCORDERFIELD.find(el=>el.Fieldname===element.Field);
                oTemp.FieldDescription = oTempFieldData.Fieldtext;

                oConfigData.BRCORDER2BRCFIELDSNAV.push(oTemp);
            }

        } else if(sStepType === "MTC"){
            let arrRuleSet;
            let MTCAll;
            let arrTableList = [];
            let arrTableData = [];

            try {
                let arrAllConfigDetails = await GetListsData.getConfigurationMTCDetails(this);

                if (arrAllConfigDetails && arrAllConfigDetails.length) {
                    MTCAll = arrAllConfigDetails;
                }

                arrTableList = await GetListsData.getConfigurationBRCTableSelect(this, sSelectedBoType);


            } catch (oError) {
                if (oError.statusCode !== "404") {
                    Utilities.showPopupAlert(
                        "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                        MessageBox.Icon.ERROR, 
                        "Error"
                    );
                }
            }

            // change the object 
            arrTableList.forEach(element => {
                let oTemp = {
                    key: element.TableName,
                    text: element.TableName + " - " + element.TableDescription
                };
                
                arrTableData.push(oTemp);
            });
            
            if(sSelectedConfiguration==="Custom"){
                oConfigDetails = {
                    Configuration: "",
                    Description:"",
                    ScoreSelection: "HIGHESTSCORE",
                    MTCUsage: "",
                    UseClassification: false,
                    DuplCheckDflt: false,
                    ApprScore: "0.00",
                };

                arrRuleSet = [];

            } else {

                try {
                    arrConfigDetails = await GetListsData.getConfigurationMTCDetails(this, sSelectedConfiguration);
    
                    if (arrConfigDetails && arrConfigDetails.length) {
                        oConfigDetails = arrConfigDetails[0];
                    }
    
                } catch (oError) {
                    if (oError.statusCode !== "404") {
                        Utilities.showPopupAlert(
                            "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                            MessageBox.Icon.ERROR, 
                            "Error"
                        );
                    }
                }
    
                arrRuleSet = this._fuzzyRuleSetParse(oConfigDetails.FuzzyRuleSet.results);
            }


            oConfigData = {
                BoType: sSelectedBoType,
                OperationType: sOperationType,
                StepType: sStepType,
                SelectedRow: sChangeRowPath, 
                SelectedConfiguration: sSelectedConfiguration,
                ConfigId: oConfigDetails.Configuration,
                ConfigIdValueState: ( oConfigDetails.Configuration ? "None" : "Error" ),
                ConfigIdValueStateText: (oConfigDetails.Configuration ? "" : "Config ID is Mandatory"),
                Description: oConfigDetails.Description,
                DescriptionValueState: ( oConfigDetails.Description ? "None" : "Error" ),
                DescriptionValueStateText: ( oConfigDetails.Description ? "" : "Configuration Description is mandatory" ),
                ScoreSelection: oConfigDetails.ScoreSelection,
                MTCUsage: oConfigDetails.MTCUsage,
                UseClassification: oConfigDetails.UseClassification,
                DuplCheckDflt: oConfigDetails.DuplCheckDflt,
                ApprScore: oConfigDetails.ApprScore,
                ApprScoreValueState: "None",
                ruleData: undefined,
                attributeData: undefined,
                conditionData:undefined,
                bIsCreating: false,
                children: [...arrRuleSet],
                MTCAll: MTCAll,
                TableData: arrTableData,
                PanelText: "Form",
                EbAddBtn: false,
                AddBtnText: "Add Attribute",
                EbAddRuleBtn: true,
                EbSaveBtn: false,
                VisCancelRuleBtn: false,
                CancelRuleBtnText: "Cancel",
                VisCancelConf: false,
                VisDeleteRuleBtn: false,
                eTag: (sOperationType === "C" ? undefined : oConfigDetails.__metadata.etag),
                LastChanged: oConfigDetails.LastChanged,
                ConfigEdited: false
            };
        }

        let tempConfigData = structuredClone(oConfigData);

        sap.ui.core.BusyIndicator.hide();

        oMDConsolidationModel.setProperty("/configData", oConfigData);
        oMDConsolidationModel.setProperty("/tempConfigData", tempConfigData);
        oMDCConfigFragment = this._getMDCConfigurationFragment(sStepType);
        oMDCConfigFragment.open();
        if(sStepType==="FAR"){

            this.MDCConfigurationFAR.validateFragmentToSave(oMDConsolidationModel);

            if(sOperationType!=="C"){

                this.MDCConfigurationFAR._changeSelectedStepTypeView(oConfigDetails.StepType, oMDConsolidationModel, this.MDCConfigurationFAR);

            }

        } else if(sStepType==="EVA"){

            this.MDCConfigurationEVA.validateFragmentToSave(oMDConsolidationModel);

        } else if(sStepType==="BRC"){

            this.MDCConfigurationBRC._onOpen();

        } else if(sStepType==="MTC"){
            // let oTreeTable = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--ruleTreeTable");

			// let oJsonModel = new JSONModel();
            // oJsonModel.setData(oConfigData);
            // oTreeTable.setModel(oJsonModel);
        }
    };

    MDConsolidationController._fuzzyRuleSetParse = function(arrRuleSet){
        let newArray = [];

        arrRuleSet?.forEach((ruleElement)=>{
            let oRuleData = {
                Configuration: ruleElement.Configuration,
                RuleId: ruleElement.RuleId,
                Name: ruleElement.RuleName,
                RuleName: ruleElement.RuleName,
                RuleNameValueState: ( ruleElement.RuleName ? "None" : "Error" ),
                RuleNameValueStateText: ( ruleElement.RuleName ? "" : "Field is mandatory"  ),
                MinScoreValueState: "None",
                MinScore: ruleElement.MinScore,
                children: [],
                RowStatus: "None",
                SequenceNo: ruleElement.SequenceNo
            };
            
            if(ruleElement.hasOwnProperty("FuzzyAttributeSet")){
                ruleElement.FuzzyAttributeSet = ruleElement.FuzzyAttributeSet.results;
                
                ruleElement.FuzzyAttributeSet.forEach((attributeElement)=>{
                    let oAttributeData =  {
                        Configuration: attributeElement.Configuration,
                        RuleId: attributeElement.RuleId,
                        AttributeId: attributeElement.AttributeId,
                        Name: attributeElement.TableName + "-" + attributeElement.FieldName,
                        EmptyMtcNull: attributeElement.EmptyMtcNull,
                        EmptyScore: attributeElement.EmptyScore,
                        EmptyScoreValueState: "None",
                        EmptyScoreValueStateText: "",
                        FieldData: [],
                        EbFieldName: false,
                        FieldName: attributeElement.FieldName,
                        FieldNameValueState: ( attributeElement.FieldName ? "None" : "Error" ),
                        FieldNameValueStateText: ( attributeElement.FieldName ? "" : "Field is mandatory"  ),    
                        Fuzziness: attributeElement.Fuzziness,
                        FuzzinessValueState: "None",
                        FuzzinessValueStateText: "",
                        ScFactor: attributeElement.ScFactor,
                        ScFactorValueState: "None",
                        ScFactorValueStateText: "",
                        SimCalcMode: attributeElement.SimCalcMode,
                        SequenceNo: attributeElement.SequenceNo,
                        TableName: attributeElement.TableName,
                        TableNameValueState: ( attributeElement.TableName ? "None" : "Error" ),
                        TableNameValueStateText: ( attributeElement.TableName ? "" : "Field is mandatory"  ),    
                        Weight: attributeElement.Weight,
                        WeightValueState: "None",
                        WeightValueStateText: "",
                        children: [],
                        RowStatus: "None"
                    };

                    oRuleData.children.push(oAttributeData);

                    if(attributeElement.hasOwnProperty("FuzzyConditionSet")){
                        attributeElement.FuzzyConditionSet = attributeElement.FuzzyConditionSet.results;
                        attributeElement.FuzzyConditionSet.forEach((conditionElement)=>{
                            let oConditionData =  {
                                Configuration: conditionElement.Configuration,
                                RuleId: conditionElement.RuleId,
                                AttributeId: conditionElement.AttributeId,
                                ConditionId: conditionElement.ConditionId,
                                Name: "Condition_" + oRuleData.children[oRuleData.children.length-1].children.length + 1,
                                Action: conditionElement.Action,
                                Condition: conditionElement.Condition,
                                ReplaceBy: conditionElement.ReplaceBy,
                                Value: conditionElement.Value,
                                RowStatus: "None",
                                SequenceNo: conditionElement.SequenceNo,
                            };
                            
                            oRuleData.children[oRuleData.children.length-1].children.push(oConditionData);
                        });
                    }
                });
            }
            newArray.push(oRuleData);
        });

        return (newArray);
    };

    // Task 12350 - Display/Edit/Delete/Add Adapter service
    MDConsolidationController.onClickViewAdapterDetails = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sChangeRowPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let sSelectedAdapter = oMDConsolidationModel.getProperty(sChangeRowPath + "/Adapter");
        if (!sSelectedAdapter || sSelectedAdapter === "Custom") {
            Utilities.showPopupAlert(
                "Please select an existing adapter", 
                MessageBox.Icon.ERROR, 
                "Missing Mandatory Details"
            );
            return;
        }
        this.openAdapterFragment(oEvent, sSelectedAdapter);
    };

    // Task 12351 - Display/Edit/Delete/Add Configurations
    MDConsolidationController.onClickViewConfigurationDetails = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sChangeRowPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let sSelectedConfiguration = oMDConsolidationModel.getProperty(sChangeRowPath + "/ConfigId");
        if (!sSelectedConfiguration || sSelectedConfiguration === "Custom") {
            Utilities.showPopupAlert(
                "Please select an existing configuration", 
                MessageBox.Icon.ERROR, 
                "Missing Mandatory Details"
            );
            return;
        }
        this.openConfigurationFragment(oEvent, sSelectedConfiguration);
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * On select of Aapter select box item
     * Based on the selection of adapter , fill the config ID list for select box
     */
    MDConsolidationController.onChangeAdapter = async function(oEvent) {
        // Bug 13216 - change the variable "this" using oController instead.
        let oController = this;
        let sSelectedAdapter = oEvent.getSource().getSelectedKey();
        
        // Task 12351 - Display/Edit/Delete/Add Configurations
        let arrConfigList = [];
        arrConfigList.push(oController._getCreateConfigurationObject());
        arrConfigList.push(oController._getEmptyConfigurationObject());

        // Task 12350 - Display/Edit/Delete/Add Adapter service
        if (sSelectedAdapter === "Custom") {
            oController.openAdapterFragment(oEvent, sSelectedAdapter);
            return;
        }

        
        let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
        let sChangeRowPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let sSelectedBoType = oMDConsolidationModel.getProperty("/selectedProcessTemplate/BoType");
        let sSelectedProcessGoal = oMDConsolidationModel.getProperty("/selectedProcessTemplate/ProcessGoal");
        let sSelectedStepType = oMDConsolidationModel.getProperty(sChangeRowPath + "/StepType");
        oMDConsolidationModel.setProperty(sChangeRowPath + "/ConfigId", undefined);
        if (sSelectedBoType) {
            let oMatchedBoType = oMDConsolidationModel.getProperty("/arrBoTypeList").find(oBoType => {
                return oBoType.BoType === sSelectedBoType;
            });
            if(oMatchedBoType && sSelectedProcessGoal) {
                let oMatchedProcessGoal = oMatchedBoType.BO2PROCESSGOALNAV.results.find(oProcessGoal =>{
                    return oProcessGoal.Name === sSelectedProcessGoal;
                });
                if(oMatchedProcessGoal && sSelectedStepType) {
                    let oMatchedStepType = oMatchedProcessGoal.PROGOAL2STEPNAV.results.find(oStepType => {
                       return oStepType.StepName === sSelectedStepType;
                    });
                    if(oMatchedStepType && sSelectedAdapter) {
                        let oMatchedAdapter = oMatchedStepType.STEPTOADAPTERNAV.results.find(oAdapter => {
                            return oAdapter.AdapterName === sSelectedAdapter;
                         });
                         if(oMatchedAdapter) {
                            //Bug 12772 fix filter to disable combo box based on rule

                            // Bug 13216 - call the function that enables/disables the configuration select
                            oController._validateConfigurationField(oMDConsolidationModel, oMatchedAdapter, sChangeRowPath);

                            // Bug 12433 - Some process templates not displaying in RDG
                            if (!oMatchedAdapter.hasOwnProperty("ADAP2CONFIGNAV")) {
                                oMatchedAdapter.ADAP2CONFIGNAV = { "results": [] };
                            }
                            if (!oMatchedAdapter.ADAP2CONFIGNAV.hasOwnProperty("results")) {
                                oMatchedAdapter.ADAP2CONFIGNAV.results = [];
                            }
                            // Task 12351 - Display/Edit/Delete/Add Configurations

                            if(oMatchedAdapter.StepName === "MTC"){

                                try {
                                    let arrOData = await GetListsData.getConfigurationMTCDetails(this);
                    
                                    if (arrOData.length) {
                                        let arrFilteredOData = arrOData.filter((el)=> el.OTC === oMatchedAdapter.BoType);
                                        arrFilteredOData.forEach((element)=>{
                                            let oTemp = {
                                                Zkey: "",
                                                Id: element.Configuration,
                                                Description: element.Description
                                            };

                                            arrConfigList.push(oTemp);

                                        });
                                    }
                    
                                } catch (oError) {
                                    if (oError.statusCode !== "404") {
                                        Utilities.showPopupAlert(
                                            "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                                            MessageBox.Icon.ERROR, 
                                            "Error"
                                        );
                                    }
                                }

                            } else {
                                oMatchedAdapter.ADAP2CONFIGNAV.results.forEach(oConfiguration => {
                                    if (oConfiguration.Id) {
                                        arrConfigList.push(oConfiguration);
                                    }
                                });
                            }
                            oMDConsolidationModel.setProperty(sChangeRowPath + "/arrConfigList", arrConfigList);
                            oMDConsolidationModel.setProperty(sChangeRowPath + "/ConfigId", "");
                         }
                    }
                }
            }
            oMDConsolidationModel.refresh();
        }
    };

    // Bug 13216 - updateAdapterList is a function that fires when any crud action is done on the adapter 
    // and updates the array of the values that the select box is going to have.
    MDConsolidationController._updateAdapterList = function(oController){

        let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
        let pUpdateList = new Promise((resolve) => {
            let oSelectedData = oMDConsolidationModel.getProperty("/selectedProcessTemplate");
            let oSelectedTemplate = {};

            let promiseProcessTemplateDropDownLists = GetListsData.getProcessTemplateDropDownList(oController);
            promiseProcessTemplateDropDownLists.then((oData) => {
                let oDataResults = oData.results[0];
                oMDConsolidationModel.setProperty("/arrBoTypeList", oDataResults.MAIN2BONAV.results);

                jQuery.extend(true, oSelectedTemplate, oSelectedData);
                let oMatchedBoTypeInfo = oMDConsolidationModel.getProperty("/arrBoTypeList").find(oBoType => {
                    return oBoType.BoType === oSelectedTemplate.BoType;
                });
                if(oMatchedBoTypeInfo) {
                    let oMatchedProcessGoal = oMatchedBoTypeInfo.BO2PROCESSGOALNAV.results.find(oProcessGoal =>{
                        return oProcessGoal.Name === oSelectedTemplate.ProcessGoal;
                    });
                    if(oMatchedProcessGoal) {
                        for (let i = 0; i < oSelectedTemplate.TEMPLATE2STEPNAV.results.length; i++) {
                            const oSelectedTemplateStep = oSelectedTemplate.TEMPLATE2STEPNAV.results[i];
                            
                            let arrAdapterList = [];
                            arrAdapterList.push(oController._getCreateAdapterObject());
                            arrAdapterList.push(oController._getEmptyAdapterObject());              
                            let oMatchedStep = oMatchedProcessGoal.PROGOAL2STEPNAV.results.find(oProgoalStep => {
                                return oMatchedProcessGoal.Name === oSelectedTemplate.ProcessGoal &&
                                oProgoalStep.StepName === oSelectedTemplateStep.StepType;
                            });

                            if(oMatchedStep) {

                                oMatchedStep.STEPTOADAPTERNAV.results.forEach(oAdapter => {
                                    arrAdapterList.push(oAdapter);
                                });
                            } 
                            oMDConsolidationModel.setProperty("/selectedProcessTemplate/TEMPLATE2STEPNAV/results/" + i + "/arrAdapterList", arrAdapterList);
                        }
                        resolve();
                    } 
                } 
            });

        });
        oMDConsolidationModel.refresh();

        return pUpdateList;
    };

    // Bug 13216 - validateConfigurationField is a function that validate, depending of the selection on the adapter, enables or disables the configuration select box.
    MDConsolidationController._validateConfigurationField = function(oMDConsolidationModel, oMatchedAdapter, sChangeRowPath, oSelectedProcessTemplate){
        let ebConfig;
        // If the condition is not met then unable combo box
        switch (oMatchedAdapter.StepName) {
            case "FAR":
                if (oMatchedAdapter.AdapterName === "CL_MDC_ADAPTER_FAR") {
                   ebConfig = true;
                } else {
                    ebConfig = false;
                }
                
                break;
            case "EVA":
                if (oMatchedAdapter.AdapterName === "CL_MDQ_RULE_EVALUATION_ADAPTER") {
                        ebConfig = true;
                } else {
                        ebConfig = false;
                }
                break;
            case "BRC":
                switch (oMatchedAdapter.BoType) {
                    case "147":
                        if (oMatchedAdapter.AdapterName === "CL_MDC_ADAPTER_BP_BRC") {
                           ebConfig = true;
                        } else {
                            ebConfig = false;
                        }
                        break;
                    case "1405":
                        if (oMatchedAdapter.AdapterName === "CL_MDC_ADAPTER_BPREL_BRC") {
                           ebConfig = true;
                        } else {
                            ebConfig = false;
                        }
                        break;
                    case "194":
                        if (oMatchedAdapter.AdapterName === "CL_MDC_ADAPTER_MAT_BRC") {
                           ebConfig = true;
                        } else {
                            ebConfig = false;
                        }
                        break;
                    default:
                       ebConfig = true;
                    break;
                }
                break;
            case "ACT":
                switch (oMatchedAdapter.BoType) {
                    case "147":
                        if (oMatchedAdapter.AdapterName === "CL_MDC_ADAPTER_BP_ACT") {
                           ebConfig = true;
                        } else {
                            ebConfig = false;
                        }
                    break;
                    case "1405":
                        if (oMatchedAdapter.AdapterName === "CL_MDC_ADAPTER_BPREL_ACT") {
                           ebConfig = true;
                        } else {
                            ebConfig = false;
                        }
                    break;
                    case "194":
                        if (oMatchedAdapter.AdapterName === "CL_MDC_ADAPTER_MAT_ACT") {
                           ebConfig = true;
                        } else {
                            ebConfig = false;
                        }
                    break;
                    default:
                       ebConfig = true;
                    break;
                }
            break;
            default:
                // Default case if currentStep doesn't match any of the defined steps
            break;
        }

        if(sChangeRowPath){
            oMDConsolidationModel.setProperty(sChangeRowPath + "/ebConfig", ebConfig);
        } else {
            oSelectedProcessTemplate.ebConfig = ebConfig;
        }
    };

    /**
     * Task 12351 - Display/Edit/Delete/Add Configurations
     * On select of Configuration select box item
     * Based on the selection of configuration, open MDCConfiguration dialog to create a new entry
     */
    MDConsolidationController.onChangeConfiguration = function(oEvent) {
        let sSelectedConfiguration = oEvent.getSource().getSelectedKey();
        
        // Bug 12510 - When I click "Custom - Create Configuration" it opens ZTESTACT instead of creating new entry
        if (sSelectedConfiguration === "Custom") {
            this.openConfigurationFragment(oEvent, sSelectedConfiguration);
            return;
        }
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * On live change of Authorization group in AuthGroupCreate fragment
     * Validate the user entry
     * Error if Authorization Group already exists
     */
    MDConsolidationController.onLiveChangeAuthGroup = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let arrAuthGroupList = oMDConsolidationModel.getProperty("/arrAuthGroupList");
        let sKey = oEvent.getParameter("value");
        oEvent.getSource().setValue(sKey.toUpperCase());
        let oMatchedAuthGroup = arrAuthGroupList.find(oAuthGroup => {
            return oAuthGroup.AuthName.toUpperCase() === sKey;
        });
        if(oMatchedAuthGroup) {
            oMDConsolidationModel.setProperty("/AuthGroupFragment/authGroupState", "Error");
            oMDConsolidationModel.setProperty("/AuthGroupFragment/authGroupStateText", "Authorization Group already exists");
        } else {
            oMDConsolidationModel.setProperty("/AuthGroupFragment/authGroupState", undefined);
            oMDConsolidationModel.setProperty("/AuthGroupFragment/authGroupStateText", undefined);
        }
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * On change of Authorization group in Template Step Table
     * If selected key is Custom, then open AuthGroupCreate fragment
     */
    MDConsolidationController.onChangeAuthGroup = function(oEvent) {
        let oController = this;
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sChangeRowPath = oEvent.getSource().getBindingContext("mdconsolidationModel").getPath();
        let sSelectedAuthGroup = oEvent.getSource().getSelectedKey();
        if(sSelectedAuthGroup === "Custom") {
            oMDConsolidationModel.setProperty("/AuthGroupFragment", {sSelectedRow: sChangeRowPath, authGroupState: "Error", authGroupStateText: "Authorization Group is mandatory"});
            if (!oController.AuthGroupCreateDialog) {
                oController.AuthGroupCreateDialog =
                    sap.ui.xmlfragment(
                        "AuthGroupCreate",
                        "dmr.mdg.supernova.SupernovaFJ.view.MDConsolidation.AuthGroupCreate",
                            oController);
                        oController.getView().addDependent(oController.AuthGroupCreateDialog);
            }    
            oController.AuthGroupCreateDialog.open();
        }
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * On click of Save in AuthGroupCreate fragment
     * Validate all mandatory details have been added and send details to backend
     */
    MDConsolidationController.onClickAuthGroupSave = async function() {
        let oController = this;
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sAuthGroupDetails = oMDConsolidationModel.getProperty("/AuthGroupFragment");
        if(!sAuthGroupDetails.AuthGroup || !sAuthGroupDetails.Description || sAuthGroupDetails.authGroupState) {
            Utilities.showPopupAlert("Please fill all mandatory details for Authorization group and try again", 
                                        MessageBox.Icon.ERROR, 
                                        "Missing Mandatory Details");
            return;
        }
        let promiseTransport = oController._getSelectedTransportPackage(true, false, false);
        promiseTransport.then(async oSelectedTransport => {

            let oData = {
                Langu: "E",
                AuthGroup: sAuthGroupDetails.AuthGroup,
                AuthDesc: sAuthGroupDetails.Description,
                CustTransport: oSelectedTransport.customizingTransport,
                Message: "",
                MessageType: ""
            };
            
            (new DMRDataService(
                oController,
                await oController.serviceName,
                "/AUTHGROUPSet",
                "Create Authorization Group",
                "/", // Root of the received data
                "Create Auth Group"
            )).saveData(
                false,
                oData,
                null, {
                    success: {
                        fCallback: function (oParams, oResponseData) {
                            if(oResponseData.MessageType === "S"){
                                let promiseProcessTemplateDropDownLists = GetListsData.getProcessTemplateDropDownList(oController);
                                promiseProcessTemplateDropDownLists.then((oDataResult) => {
                                    let oDataResults = oDataResult.results[0];
                                    oDataResults.MAIN2AUTHNAV.results.unshift({
                                        Zkey: "",
                                        AuthName: "Custom",
                                        Description: "Create Authorization Group"
                                    }, {
                                        Zkey: "",
                                        AuthName: "",
                                        Description: ""
                                    });
                                    oMDConsolidationModel.setProperty("/arrAuthGroupList", oDataResults.MAIN2AUTHNAV.results);
                                    oMDConsolidationModel.setProperty(sAuthGroupDetails.sSelectedRow + "/AuthGroup", sAuthGroupDetails.AuthGroup);
                                    oMDConsolidationModel.setProperty("/AuthGroupFragment", {});
                                    oController.AuthGroupCreateDialog.close();
                                    
                                    Utilities.showPopupAlert(oResponseData.Message, MessageBox.Icon.SUCCESS, "Authorization Group created");
                                });
                            } else {
                                Utilities.showPopupAlert(oResponseData.Message, MessageBox.Icon.ERROR, "Error");
                            }
                        },
                        oParam: oController
                    },
                    error: {
                        fCallback: function (oParams, oResponseData) {
                            let sMessage = oResponseData.statusCode + ": " + oResponseData.message;
                            Utilities.showPopupAlert("Error " + sMessage, MessageBox.Icon.ERROR, "Error");
                        }
                    }
                }
            );
        });
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * On click of Save in AuthGroupCreate fragment
     * Reset all model properties and close the fragment
     */
    MDConsolidationController.onClickAuthGroupCancel = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sSelectedRow = oMDConsolidationModel.getProperty("/AuthGroupFragment/sSelectedRow");
        oMDConsolidationModel.setProperty(sSelectedRow + "/AuthGroup", undefined);
        oMDConsolidationModel.setProperty("/AuthGroupFragment", {});
        this.AuthGroupCreateDialog.close();
    };

    /**
     * User Story 12293 Task 12308 - MDC Process Templates
     * Formater function to enable Delete Button
     */
    MDConsolidationController.DeleteButtonEnabled = function(sProcessTemplate) {
        if(/^[YZ]/.test(sProcessTemplate)) {
            return true;
        } else {
            return false;
        }
    };

    /** 
     * Bug 13446: validateAdapterConfig checkes on the process template steps if there is an invalid selection between adapter en config. 
     * returns a boolean. true if there is an invalid selection or false if not.
     */ 
    MDConsolidationController.validateAdapterConfig = function(oSelectedProcessTemplate, oTemplateStep){
        let bInvalidSelection = false;
        if(oTemplateStep.ConfigId==="Custom"){
            bInvalidSelection = true;
        } else if(oTemplateStep.Adapter){
            switch (oTemplateStep.StepType) {
                case "FAR":
                    if (oTemplateStep.Adapter !== "CL_MDC_ADAPTER_FAR" && oTemplateStep.ConfigId !== "") {
                        bInvalidSelection = true;
                    }
                    
                    break;
                case "EVA":
                    if (oTemplateStep.Adapter !== "CL_MDQ_RULE_EVALUATION_ADAPTER" && oTemplateStep.ConfigId !== "") {
                        bInvalidSelection = true;
                    }
                    break;
                case "BRC":
                    switch (oSelectedProcessTemplate.BoType) {
                        case "147":
                            if (oTemplateStep.Adapter !== "CL_MDC_ADAPTER_BP_BRC" && oTemplateStep.ConfigId !== "") {
                                bInvalidSelection = true;
                            }
                            break;
                        case "1405":
                            if (oTemplateStep.Adapter !== "CL_MDC_ADAPTER_BPREL_BRC" && oTemplateStep.ConfigId !== "") {
                                bInvalidSelection = true;
                            }
                            break;
                        case "194":
                            if (oTemplateStep.Adapter !== "CL_MDC_ADAPTER_MAT_BRC" && oTemplateStep.ConfigId !== "") {
                                bInvalidSelection = true;
                            }
                            break;
                        default:
                            bInvalidSelection = false;
                        break;
                    }
                    break;
                case "ACT":
                    switch (oSelectedProcessTemplate.BoType) {
                        case "147":
                            if (oTemplateStep.Adapter !== "CL_MDC_ADAPTER_BP_ACT" && oTemplateStep.ConfigId !== "") {
                                bInvalidSelection = true;
                            }
                        break;
                        case "1405":
                            if (oTemplateStep.Adapter !== "CL_MDC_ADAPTER_BPREL_ACT" && oTemplateStep.ConfigId !== "") {
                                bInvalidSelection = true;
                            }
                        break;
                        case "194":
                            if (oTemplateStep.Adapter !== "CL_MDC_ADAPTER_MAT_ACT" && oTemplateStep.ConfigId !== "") {
                                bInvalidSelection = true;
                            }
                        break;
                        default:
                            bInvalidSelection = false;
                        break;
                    }
                break;
                default:
                    // Default case if currentStep doesn't match any of the defined steps
                break;
            }
        }
        return bInvalidSelection;
    };

    MDConsolidationController.onSaveProcessTemplate = function() {
        let oController = this;
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oSelectedProcessTemplate = oMDConsolidationModel.getProperty("/selectedProcessTemplate");
        let arrTemplateStep;
        let bInvalidSelection = false;
        try{
            arrTemplateStep = oSelectedProcessTemplate.TEMPLATE2STEPNAV.results;
        } catch(e) {
            arrTemplateStep = [];
        }

        let oData = {
            Object: "Process Template",
            Key: oSelectedProcessTemplate.ProcessTemplate,
            Mode: "C",
            OPERATION2PROCESSTEMPLATENAV: [],
            OPERATION2MESSAGENAV: []
        };
        oData.OPERATION2PROCESSTEMPLATENAV.push({
            ProcessTemplate: oSelectedProcessTemplate.ProcessTemplate,
            Description: oSelectedProcessTemplate.Description,
            BoType: oSelectedProcessTemplate.BoType,
            ProcessWorkflow: "WS54500001",
            ProcessGoal: oSelectedProcessTemplate.ProcessGoal,
            DefaultTemplate: oSelectedProcessTemplate.DefaultTemplate === undefined ? false : oSelectedProcessTemplate.DefaultTemplate,
            DeleteSource: oSelectedProcessTemplate.DeleteSource === undefined ? false : oSelectedProcessTemplate.DeleteSource,
            Strategy: oSelectedProcessTemplate.Strategy,
            TEMPLATE2STEPNAV: []
        });

        // Bug 13446: call validateAdapterConfig. if there is an invalid selection show an error message en break the function. change the for each loop into a for loop
        for (let i = 0; i < arrTemplateStep.length; i++) {
            const oTemplateStep = arrTemplateStep[i];
            bInvalidSelection = this.validateAdapterConfig(oSelectedProcessTemplate, oTemplateStep);
            if(bInvalidSelection){
                Utilities.showPopupAlert("Error: adapter " + oTemplateStep.Adapter + " with configuration " + oTemplateStep.ConfigId + " not usable. \nPlease select a different adapter and configuration combination.", MessageBox.Icon.ERROR, "Error");
                return;
            }
            let oStepData = {
                ProcessTemplate: oSelectedProcessTemplate.ProcessTemplate,
                StepNo: oTemplateStep.StepNo.toString(),
                StepSequence: oTemplateStep.StepNo - 1,
                StepType: oTemplateStep.StepType,
                StepDescription: oTemplateStep.StepDescription,
                Adapter: oTemplateStep.Adapter,
                ConfigId: oTemplateStep.ConfigId,
                CheckPt: oTemplateStep.CheckPt  === undefined ? false : oTemplateStep.CheckPt,
                ActionCtrl: oTemplateStep.ActionCtrl,
                AuthGroup: oTemplateStep.AuthGroup
            };
            oData.OPERATION2PROCESSTEMPLATENAV[0].TEMPLATE2STEPNAV.push(oStepData);
        }

        let promiseTransport = this._getSelectedTransportPackage(true, false, false);
        // Task 13213: make the function asyncronous so we can call the corresponding service name
		promiseTransport.then(async oSelectedTransport => {
            oData.CustTransport = oSelectedTransport.customizingTransport;


            (new DMRDataService(
				oController,
                // Task 13213: call the corresponding service name depending on the system
                await oController.serviceName,
				"/OPERATIONSet",
				"Create Process Template",
				"/", // Root of the received data
				"Create Template"
			)).saveData(
				false,
				oData,
				null, {
					success: {
						fCallback: function (oParams, oResponseData) {
                            let sError = "";
                            let arrMessages = [];
                            jQuery.extend(true, arrMessages, oResponseData.OPERATION2MESSAGENAV.results);
                            arrMessages.every((oMessage) => {
                                if(oMessage.MessageType === "E") {
                                    //If Error exists in list, set value and break
                                    sError = "X";
                                    return false;
                                } else {
                                    return true;
                                }
                            });
                            ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());

                            if(!sError) {
                                let promiseChangeRequestsList = GetListsData.getProcessTemplateList(oController);
                                promiseChangeRequestsList.then(function (updatedList) {
                                    // Bug 12399 - Process Template name shows as missing when its not blank
                                    oMDConsolidationModel.setProperty("/TemplateList", updatedList.results);
                                    oMDConsolidationModel.setProperty("/processTemplateType", undefined);
                                    oMDConsolidationModel.setProperty("/selectedProcessTemplate", {});
                                    oController.MDConsolidationSearchList.setListData(updatedList.results);
                                    oController.MDConsolidationSearchList.setSelectedItemByTitle(oSelectedProcessTemplate.ProcessTemplate);
                                });
                            }
							
						},
						oParam: oController
					},
					error: {
						fCallback: function (oParams, oResponseData) {
							let sMessage = oResponseData.statusCode + ": " + oResponseData.message;
							Utilities.showPopupAlert("Error " + sMessage, MessageBox.Icon.ERROR, "Error");
						}
					}
				}
			);
        });
    };

    MDConsolidationController.onDeleteProcessTemplate = function() {
        let oController = this;
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oSelectedProcessTemplate = oMDConsolidationModel.getProperty("/selectedProcessTemplate");

        let oData = {
            Object: "Process Template",
            Key: oSelectedProcessTemplate.ProcessTemplate,
            Mode: "D",
            OPERATION2PROCESSTEMPLATENAV: [],
            OPERATION2MESSAGENAV: []
        };
        oData.OPERATION2PROCESSTEMPLATENAV.push({
            ProcessTemplate: oSelectedProcessTemplate.ProcessTemplate,
            Description: oSelectedProcessTemplate.Description,
            BoType: oSelectedProcessTemplate.BoType,
            ProcessWorkflow: "WS54500001",
            ProcessGoal: oSelectedProcessTemplate.ProcessGoal,
            DefaultTemplate: oSelectedProcessTemplate.DefaultTemplate === undefined ? false : oSelectedProcessTemplate.DefaultTemplate,
            DeleteSource: oSelectedProcessTemplate.DeleteSource === undefined ? false : oSelectedProcessTemplate.DeleteSource,
            Strategy: oSelectedProcessTemplate.Strategy,
            TEMPLATE2STEPNAV: []
        });
        
        // Bug 12433 - Some process templates not displaying in RDG
        if (!oSelectedProcessTemplate.hasOwnProperty("TEMPLATE2STEPNAV")) {
            oSelectedProcessTemplate.TEMPLATE2STEPNAV = { "results": [] };
        }
        if (!oSelectedProcessTemplate.TEMPLATE2STEPNAV.hasOwnProperty("results")) {
            oSelectedProcessTemplate.TEMPLATE2STEPNAV.results = [];
        }
        oSelectedProcessTemplate.TEMPLATE2STEPNAV.results.forEach(oTemplateStep => {
            let oStepData = {
                ProcessTemplate: oSelectedProcessTemplate.ProcessTemplate,
                StepNo: oTemplateStep.StepNo.toString(),
                StepSequence: oTemplateStep.StepNo - 1,
                StepType: oTemplateStep.StepType,
                StepDescription: oTemplateStep.StepDescription,
                Adapter: oTemplateStep.Adapter,
                ConfigId: oTemplateStep.ConfigId,
                CheckPt: oTemplateStep.CheckPt  === undefined ? false : oTemplateStep.CheckPt,
                ActionCtrl: oTemplateStep.ActionCtrl,
                AuthGroup: oTemplateStep.AuthGroup
            };
            oData.OPERATION2PROCESSTEMPLATENAV[0].TEMPLATE2STEPNAV.push(oStepData);
        });

        let promiseTransport = this._getSelectedTransportPackage(true, false, false);
		promiseTransport.then(async oSelectedTransport => {
            oData.CustTransport = oSelectedTransport.customizingTransport;

            (new DMRDataService(
				oController,
				await oController.serviceName,
				"/OPERATIONSet",
				"Create Process Template",
				"/", // Root of the received data
				"Create Template"
			)).saveData(
				false,
				oData,
				null, {
					success: {
						fCallback: function (oParams, oResponseData) {
                            let sError = "";
                            let arrMessages = [];
                            jQuery.extend(true, arrMessages, oResponseData.OPERATION2MESSAGENAV.results);
                            arrMessages.every((oMessage) => {
                                if(oMessage.MessageType === "E") {
                                    //If Error exists in list, set value and break
                                    sError = "X";
                                    return false;
                                } else {
                                    return true;
                                }
                            });
                            ModelMessages.showMessagesDialog(oController, arrMessages, oController.getView());

                            if(!sError) {
                                let promiseProcessTemplateList =
                                    GetListsData.getProcessTemplateList(oController);
                                promiseProcessTemplateList.then(updatedList => {
                                    oMDConsolidationModel.setProperty("/TemplateList", updatedList);
                                    oController.MDConsolidationSearchList.setListData(updatedList.results);
                                    oMDConsolidationModel.setProperty("/processTemplateType", undefined);
                                    oMDConsolidationModel.setProperty("/selectedProcessTemplate", {});
                                    oController.byId("idMDConsolidationPanel").setExpanded(false);
                                    oController.MDConsolidationSearchList.removeSelectedItem();
                                });
                            }
						},
						oParam: oController
					},
					error: {
						fCallback: function (oParams, oResponseData) {
							let sMessage = oResponseData.statusCode + ": " + oResponseData.message;
							Utilities.showPopupAlert("Error " + sMessage, MessageBox.Icon.ERROR, "Error");
						}
					}
				}
			);
        });
    };

    // Task 12350 - Display/Edit/Delete/Add Adapter service
	MDConsolidationController._getMDCAdapterFragment = function () {
		if (!this.oMDCAdapterFragment) {
			this.oMDCAdapterFragment = sap.ui.xmlfragment(
				this.MDCAdapterFragmentID,
				"dmr.mdg.supernova.SupernovaFJ.view.MDConsolidation.MDCAdapter",
				this
			);
			this.getView().addDependent(this.oMDCAdapterFragment);
		}

		return this.oMDCAdapterFragment;
	};
    
    // Task 12351 - Display/Edit/Delete/Add Configurations
	MDConsolidationController._getMDCConfigurationFragment = function (sStepType) {
        let oFragment;
        
        if(sStepType === "ACT"){
            if (!this.oMDCConfigurationACTFragment) {
                this.oMDCConfigurationACTFragment = sap.ui.xmlfragment(
                    this.MDCConfigurationACTFragmentID,
                    "dmr.mdg.supernova.SupernovaFJ.view.MDConsolidation.MDCConfigurationACT",
                    this
                );
                this.getView().addDependent(this.oMDCConfigurationACTFragment);
            }
    
            oFragment = this.oMDCConfigurationACTFragment;

        } else if(sStepType === "FAR"){
            if (!this.oMDCConfigurationFARFragment) {
                this.oMDCConfigurationFARFragment = sap.ui.xmlfragment(
                    this.MDCConfigurationFARFragmentID,
                    "dmr.mdg.supernova.SupernovaFJ.view.MDConsolidation.MDCConfigurationFAR",
                    this
                );
                this.getView().addDependent(this.oMDCConfigurationFARFragment);
            }
        
            oFragment = this.oMDCConfigurationFARFragment;
        } else if(sStepType === "EVA"){
            if (!this.oMDCConfigurationEVAFragment) {
                this.oMDCConfigurationEVAFragment = sap.ui.xmlfragment(
                    this.MDCConfigurationEVAFragmentID,
                    "dmr.mdg.supernova.SupernovaFJ.view.MDConsolidation.MDCConfigurationEVA",
                    this
                );
                this.getView().addDependent(this.oMDCConfigurationEVAFragment);
            }
        
            oFragment = this.oMDCConfigurationEVAFragment;
        } else if(sStepType === "BRC"){
            if (!this.oMDCConfigurationBRCFragment) {
                this.oMDCConfigurationBRCFragment = sap.ui.xmlfragment(
                    this.MDCConfigurationBRCFragmentID,
                    "dmr.mdg.supernova.SupernovaFJ.view.MDConsolidation.MDCConfigurationBRC",
                    this
                );
                this.getView().addDependent(this.oMDCConfigurationBRCFragment);
            }
        
            oFragment = this.oMDCConfigurationBRCFragment;
        } else if(sStepType==="MTC"){
            if (!this.oMDCConfigurationMTCFragment) {
                this.oMDCConfigurationMTCFragment = sap.ui.xmlfragment(
                    this.MDCConfigurationMTCFragmentID,
                    "dmr.mdg.supernova.SupernovaFJ.view.MDConsolidation.MDCConfigurationMTC",
                    this
                );
                this.getView().addDependent(this.oMDCConfigurationMTCFragment);
            }
        
            oFragment = this.oMDCConfigurationMTCFragment;
        }

        return oFragment;
	};

    return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.MDConsolidation.MDConsolidation", MDConsolidationController);
});