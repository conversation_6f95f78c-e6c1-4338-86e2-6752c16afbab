// Task 12351 - Display/Edit/Delete/Add Configurations
sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "dmr/mdg/supernova/SupernovaFJ/libs/DataService",
    "sap/m/MessageBox"
], function (GetListsData, Utilities, DMRDataService, MessageBox) {
	"use strict";

    let oMDCConfigurationFAR = {};

    oMDCConfigurationFAR.onLiveChangeConfiguration = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sConfigId = oEvent.getSource().getProperty("value").toUpperCase();
        oEvent.getSource().setValue(sConfigId);

        if (sConfigId) {
            if (sConfigId.substring(0, 1) !== "Z" && sConfigId.substring(0, 1) !== "Y") {
                oMDConsolidationModel.setProperty("/configData/ConfigIdValueState", "Error");
                oMDConsolidationModel.setProperty("/configData/ConfigIdValueStateText", "Configuration ID must start with Z or Y");
            } else {
                oMDConsolidationModel.setProperty("/configData/ConfigIdValueState", "None");
                oMDConsolidationModel.setProperty("/configData/ConfigIdValueStateText", "");
            }
        } else {
            oMDConsolidationModel.setProperty("/configData/ConfigIdValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/ConfigIdValueStateText", "Configuration ID is mandatory");
        }
        oMDCConfigurationFAR.validateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationFAR.onLiveChangeDescription = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        if (oEvent.getSource().getProperty("value")) {
            oMDConsolidationModel.setProperty("/configData/DescValueState", "None");
            oMDConsolidationModel.setProperty("/configData/DescValueStateText", "");
        } else {
            oMDConsolidationModel.setProperty("/configData/DescValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/DescValueStateText", "Configuration description is mandatory");
        }

        oMDCConfigurationFAR.validateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationFAR.validateFragmentToSave = function(oMDConsolidationModel){
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oCreateButton = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--BtnSave");

        if(oConfigData.ConfigIdValueState === "None" && oConfigData.DescValueState === "None" && oConfigData.PredecessorProcessValueState === "None"){
            oCreateButton.setEnabled(true);
        } else{
            oCreateButton.setEnabled(false);
        }
    };

    oMDCConfigurationFAR.onChangeProcessStepType = function (oEvent){
        let oComponent = this;
        let oSelectProcessStep = oEvent.getSource();
        let sSelectedValue = oSelectProcessStep.getSelectedKey();
        let oMDConsolidationModel = oComponent.getView().getModel("mdconsolidationModel");
        let oProcessStepTypeView = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeView");

        if(oSelectProcessStep.getValueState() === "Error"){
            oMDConsolidationModel.setProperty("/configData/PredecessorProcessValueState", "None");
            oMDConsolidationModel.setProperty("/configData/PredecessorProcessValueStateText", "");
            oMDCConfigurationFAR.validateFragmentToSave(oMDConsolidationModel);
        }

        oProcessStepTypeView.destroyItems();
        this.MDCConfigurationFAR._resetStepTypeData(oMDConsolidationModel);
        this.MDCConfigurationFAR._changeSelectedStepTypeView(sSelectedValue, oMDConsolidationModel, this.MDCConfigurationFAR);

    };

    oMDCConfigurationFAR._changeSelectedStepTypeView = function(sSelectedValue, oMDConsolidationModel, oComponent){
        let oProcessStepTypeTag = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeTag");
        if(!oComponent){
            oComponent = this;
        } 

        if(!oProcessStepTypeTag.getVisible()){
            oProcessStepTypeTag.setVisible(true);
        }

        if(sSelectedValue==="MTC"){

            oComponent._createMtcView(oMDConsolidationModel);

        }else if(sSelectedValue==="BRC"){

            oComponent._createBrcView(oMDConsolidationModel);

        }else if(sSelectedValue==="VAL"){

            oComponent._createValView(oMDConsolidationModel);
        }
    };

    oMDCConfigurationFAR._resetStepTypeData = function(oMDConsolidationModel){
        oMDConsolidationModel.setProperty("/configData/GroupsMtc", false);
        oMDConsolidationModel.setProperty("/configData/SingleBrc", false);
        oMDConsolidationModel.setProperty("/configData/GroupsBrc", false);
        oMDConsolidationModel.setProperty("/configData/UpdateBrc", false);
        oMDConsolidationModel.setProperty("/configData/SingleWarVal", false);
        oMDConsolidationModel.setProperty("/configData/GroupsWarVal", false);
        oMDConsolidationModel.setProperty("/configData/UpdateWarVal", false);
        oMDConsolidationModel.setProperty("/configData/SingleActVal", "");
        oMDConsolidationModel.setProperty("/configData/GroupsActVal", "");
        oMDConsolidationModel.setProperty("/configData/UpdateActVal", "");
    };

    oMDCConfigurationFAR._createLabel = function(sLabelFor, sText){
        let oLabel = new sap.m.Label({
            labelFor: sLabelFor,
            required: false,
            text: sText
        });
        return oLabel;
    };

    oMDCConfigurationFAR._createCheckbox = function(oMDConsolidationModel, id){
        let oCheckbox = new sap.m.CheckBox({
            id: id,
            textAlign: "Left",
            editable: true,
            selected: "{= ${mdconsolidationModel>/configData/" + id +"}}",
            select: function(oEvent){
                if (oEvent.getParameter("selected")) {
                    oMDConsolidationModel.setProperty("/configData/" + id, true);
                } else {
                    oMDConsolidationModel.setProperty("/configData/" + id, false);
                }
            }
        });

        return oCheckbox;
    };

    oMDCConfigurationFAR._createGenericTag = function(text){
        let oGenericTag = new sap.m.GenericTag({
            text: text,
            status: "None",
            design: "StatusIconHidden"
        }).addStyleClass("sapUiSmallMarginBottom sapUiSmallMarginBegin"); 

        return oGenericTag;
    };
    
    oMDCConfigurationFAR._createGrid = function(){
        let oGrid = new sap.ui.layout.Grid({
            containerQuery: true,
            // Bug 12695: Complete text not readable for Matching Predecessor Step Type
            defaultSpan: "XL4 L4 M4 S6"
        });
        
        return oGrid;
    };

    oMDCConfigurationFAR._createSelect = function(id){
        let oSelect = new sap.m.Select({
            id: id,
            editable: true,
            selectedKey: "{mdconsolidationModel>/configData/" + id + "}"
        });
        let arrItems = [{
            key: "",
            text: "No Action"
        }, {
            key: "S",
            text: "Move records to new duplicate process"
        }, {
            key: "D",
            text: "Remove and Delete Source Records"
        }];
        
        arrItems.forEach((el) => {
            oSelect.addItem(new sap.ui.core.Item(el));
        });

        return oSelect;
    };

    oMDCConfigurationFAR._createMtcView = function(oMDConsolidationModel){
        let oProcessStepTypeView = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeView");
        let oGroupsMtcGrid = this._createGrid();
        
        //Set the tag text
        let oProcessStepTypeTag = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeTag");
        oProcessStepTypeTag.setText("Matching - Move records to new duplicate process for");

        //Create a VBox, Label and Checkbox for Open Match Groups
        let oMatchingVBox = new sap.m.VBox();
        let oMatchingLabel = this._createLabel("GroupsMtc", "Open Match Groups");
        let oMatchingCheckbox = this._createCheckbox(oMDConsolidationModel, "GroupsMtc");

        //add the Label and Checkbox to the Matching VBox
        oMatchingVBox.addItem(oMatchingLabel);
        oMatchingVBox.addItem(oMatchingCheckbox);

        //add the Single Records, New Match Groups and Updates VBoxes to the Matching Grid
        oGroupsMtcGrid.addContent(oMatchingVBox);

        //add the VBox to the ProcessStepTypeView VBox
        oProcessStepTypeView.addItem(oGroupsMtcGrid);
    };

    oMDCConfigurationFAR._createBrcView = function(oMDConsolidationModel){

        let oProcessStepTypeView = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeView");
        let oBrcViewGrid = this._createGrid();
        
        //Set the tag text
        let oProcessStepTypeTag = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeTag");
        oProcessStepTypeTag.setText("Best Record Calculation - Move records to new duplicate process for");

        //Create a VBox, Label and Checkbox for Single Records
        let oSingleBrcVBox = new sap.m.VBox();
        let oSingleBrcLabel = this._createLabel("SingleBrc", "Single Records");
        let oSingleBrcCheckbox = this._createCheckbox(oMDConsolidationModel, "SingleBrc");
        
        //Create a VBox, Label and Checkbox for New Match Groups
        let oGroupsBrcVBox = new sap.m.VBox();
        let oGroupsBrcLabel = this._createLabel("GroupsBrc", "New Match Groups");
        let oGroupsBrcCheckbox = this._createCheckbox(oMDConsolidationModel, "GroupsBrc");

        //Create a VBox, Label and Checkbox for Updates
        let oUpdateBrcVBox = new sap.m.VBox();
        let oUpdateBrcLabel = this._createLabel("UpdateBrc", "Updates");
        let oUpdateBrcCheckbox = this._createCheckbox(oMDConsolidationModel, "UpdateBrc");

        //add the Label and Checkbox to the Single Records VBox
        oSingleBrcVBox.addItem(oSingleBrcLabel);
        oSingleBrcVBox.addItem(oSingleBrcCheckbox);
        
        //add the Label and Checkbox to the New Match Groups VBox
        oGroupsBrcVBox.addItem(oGroupsBrcLabel);
        oGroupsBrcVBox.addItem(oGroupsBrcCheckbox);
        
        //add the Label and Checkbox to the Updates VBox
        oUpdateBrcVBox.addItem(oUpdateBrcLabel);
        oUpdateBrcVBox.addItem(oUpdateBrcCheckbox);

        //add the Single Records, New Match Groups and Updates VBoxes to the Brc Grid
        oBrcViewGrid.addContent(oSingleBrcVBox);
        oBrcViewGrid.addContent(oGroupsBrcVBox);
        oBrcViewGrid.addContent(oUpdateBrcVBox);
        
        //add the Single Records, New Match Groups and Updates VBoxes to the ProcessStepTypeView VBox
        oProcessStepTypeView.addItem(oBrcViewGrid);
    };

    oMDCConfigurationFAR._createValView = function(oMDConsolidationModel){
        this._createMoveRecordsSection(this._createCheckbox, oMDConsolidationModel);
        this._createActionForRecordsSection();
    };

    oMDCConfigurationFAR._createMoveRecordsSection = function(createCheckbox, oMDConsolidationModel) {
        let oProcessStepTypeView = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeView");
        
        //Set the tag text
        let oProcessStepTypeTag = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeTag");
        oProcessStepTypeTag.setText("Validation");

        //Create Tag and Grid for "Move records with warnings"
        let oMoveRecordsTag = this._createGenericTag("Move records with warnings to new duplicate process for");
        let oMoveRecordsGrid = this._createGrid();

        //Create a VBox, Label and Checkbox for Single Records
        let oSingleWarValVBox = new sap.m.VBox();
        let oSingleWarValLabel = this._createLabel("SingleWarVal", "Single Records");
        let oSingleWarValCheckbox = this._createCheckbox(oMDConsolidationModel, "SingleWarVal");
        
        //Create a VBox, Label and Checkbox for New Match Groups
        let oGroupsWarValVBox = new sap.m.VBox();
        let oGroupsWarValLabel = this._createLabel("GroupsWarVal", "New Match Groups");
        let oGroupsWarValCheckbox = this._createCheckbox(oMDConsolidationModel, "GroupsWarVal");

        //Create a VBox, Label and Checkbox for Updates
        let oUpdateWarValVBox = new sap.m.VBox();
        let oUpdateWarValLabel = this._createLabel("UpdateWarVal", "Updates");
        let oUpdateWarValCheckbox = this._createCheckbox(oMDConsolidationModel, "UpdateWarVal");
        
        //add the Label and Checkbox to the Single Records VBox
        oSingleWarValVBox.addItem(oSingleWarValLabel);
        oSingleWarValVBox.addItem(oSingleWarValCheckbox);
        
        //add the Label and Checkbox to the New Match Groups VBox
        oGroupsWarValVBox.addItem(oGroupsWarValLabel);
        oGroupsWarValVBox.addItem(oGroupsWarValCheckbox);
        
        //Create a VBox, Label and Checkbox for Updates
        oUpdateWarValVBox.addItem(oUpdateWarValLabel);
        oUpdateWarValVBox.addItem(oUpdateWarValCheckbox);
        
        //add the Single Records, New Match Groups and Updates VBoxes to the MoveRecords Grid
        oMoveRecordsGrid.addContent(oSingleWarValVBox);
        oMoveRecordsGrid.addContent(oGroupsWarValVBox);
        oMoveRecordsGrid.addContent(oUpdateWarValVBox);

        //add TAG and GRID to the ProcessStepTypeView VBox
        oProcessStepTypeView.addItem(oMoveRecordsTag);
        oProcessStepTypeView.addItem(oMoveRecordsGrid);
    };

    oMDCConfigurationFAR._createActionForRecordsSection = function() {
        let oProcessStepTypeView = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeView");

        //Create Tag and Grid for "Action for records with errors"
        let oActionsForRecordsTag = this._createGenericTag("Action for records with errors");
        let oActionsForRecordsGrid = this._createGrid();
        
        //Create a VBox, Label and Select for Single Records
        let oSingleActValVBox = new sap.m.VBox();
        let SingleActValLabel = this._createLabel("SingleRecordsSelect", "Single Records");
        let SingleActValSelect = this._createSelect("SingleActVal");

        //Create a VBox, Label and Select for New Match Groups
        let oGroupsActValVBox = new sap.m.VBox();
        let oGroupsActValLabel = this._createLabel("GroupsActVal", "New Match Groups");
        let oGroupsActValSelect = this._createSelect("GroupsActVal");

        //Create a VBox, Label and Select for Updates
        let oUpdateActValVBox = new sap.m.VBox();
        let oUpdateActValLabel = this._createLabel("UpdateActVal", "Updates");
        let oUpdateActValSelect = this._createSelect("UpdateActVal");
        
        //add the Label and Checkbox to the Single Records VBox
        oSingleActValVBox.addItem(SingleActValLabel);
        oSingleActValVBox.addItem(SingleActValSelect);

        //add the Label and Checkbox to the New Match Groups VBox
        oGroupsActValVBox.addItem(oGroupsActValLabel);
        oGroupsActValVBox.addItem(oGroupsActValSelect);
        
        //Create a VBox, Label and Checkbox for Updates
        oUpdateActValVBox.addItem(oUpdateActValLabel);
        oUpdateActValVBox.addItem(oUpdateActValSelect);
        
        //add the Single Records, New Match Groups and Updates VBoxes to the ActionsForRecords Grid
        oActionsForRecordsGrid.addContent(oSingleActValVBox);
        oActionsForRecordsGrid.addContent(oGroupsActValVBox);
        oActionsForRecordsGrid.addContent(oUpdateActValVBox);

        //add TAG and GRID to the ProcessStepTypeView VBox
        oProcessStepTypeView.addItem(oActionsForRecordsTag);
        oProcessStepTypeView.addItem(oActionsForRecordsGrid);
    };


    oMDCConfigurationFAR.saveConfiguration = function(oController, oEvent, sOperationType) {
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let sSelectedRow = oMDConsolidationModel.getProperty("/configData/SelectedRow");
        let arrSelectedRow = sSelectedRow.split("/");
        let nSelectedRow = arrSelectedRow[arrSelectedRow.length-1];
        let nPreviousRow = nSelectedRow-1;
        let oPreviousRow = oMDConsolidationModel.getProperty("/selectedProcessTemplate/TEMPLATE2STEPNAV/results/" + nPreviousRow);
        let oProcessStepTypeSelect = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeSelect");
        let oProcessStepTypeView = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeView");
        let oProcessStepTypeTag = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeTag");
    //Add validation when operation is a Deletion, not validate the previos step just delete
    if(sOperationType !== "D"){
        if(oConfigData.StepType !== oPreviousRow.StepType){
            Utilities.showPopupAlert("The 'Predecessor Process Step Type: " + oProcessStepTypeSelect.getSelectedItem().getProperty("text") + "' does not match the Previous Step Type on the Table.", 
            MessageBox.Icon.ERROR, 
            "Action not possible");
            return;
        }
    }
        let promiseTransport = oController._getSelectedTransportPackage(true, false, false);
        // Task 13213: make the function asyncronous so we can call the corresponding service name
        promiseTransport.then(async oSelectedTransport => {
            sap.ui.core.BusyIndicator.show(300);

            let oPayload = {
                ConfigId: oConfigData.ConfigId,
                Description: oConfigData.Description,
                StepType: oConfigData.StepType,
                GroupsMtc: (oConfigData.GroupsMtc ? true : false),
                SingleBrc: (oConfigData.SingleBrc ? true : false),
                GroupsBrc: (oConfigData.GroupsBrc ? true : false),
                UpdateBrc: (oConfigData.UpdateBrc ? true : false),
                SingleWarVal: (oConfigData.SingleWarVal ? true : false),
                GroupsWarVal: (oConfigData.GroupsWarVal ? true : false),
                UpdateWarVal: (oConfigData.UpdateWarVal ? true : false),
                SingleActVal: oConfigData.SingleActVal,
                GroupsActVal: oConfigData.GroupsActVal,
                UpdateActVal: oConfigData.UpdateActVal,
                CustTransport: oSelectedTransport.customizingTransport,
                Operation: sOperationType,
                Message: "",
                MessageCode: ""
            };

            (new DMRDataService(
                oController,
                // Task 13213: call the corresponding service name depending on the system
				await oController.serviceName,
                "/FILTERREMOVESet",
                "Save Configuration",
                "/", // Root of the received data
                "Save Configuration"
            )).saveData(
                false,
                oPayload,
                null, {
                    success: {
                        fCallback: function (oDataResult, oResponse) {
                            sap.ui.core.BusyIndicator.hide();

                            if (oResponse.MessageCode === "E") {
                                Utilities.showPopupAlert("Error: " + oResponse.Message, MessageBox.Icon.ERROR, "Error");
                                return;
                            }

                            let arrConfigList = [];
                            let promiseConfigurationList = GetListsData.getConfigurationFARDetails(oController);
                            promiseConfigurationList.then(oData => {
                                arrConfigList.push(oController._getCreateConfigurationObject());
                                arrConfigList.push(oController._getEmptyConfigurationObject());
                                oData.forEach(oConfiguration => {
                                    arrConfigList.push({
                                        Zkey: "",
                                        Id: oConfiguration.ConfigId,
                                        Description: oConfiguration.Description
                                    });
                                });

                                oMDConsolidationModel.setProperty(oConfigData.SelectedRow + "/arrConfigList", arrConfigList);
                                oMDConsolidationModel.setProperty(oConfigData.SelectedRow + "/ConfigId", ( sOperationType === "D" ? "" : oConfigData.ConfigId ));
                                oMDConsolidationModel.setProperty("/configData", {});
                                
                                // Bug 12351 - Display/Edit/Delete/Add Configurations
                                oController._refreshTemplateDropDownList(oController);

                                if (oResponse.MessageCode === "S") {
                                    Utilities.showPopupAlert(oResponse.Message, MessageBox.Icon.SUCCESS, "Success");
                                }

                                let oMDCConfigurationFARFragment = oController._getMDCConfigurationFragment("FAR");
                                oProcessStepTypeView.destroyItems();
                                oProcessStepTypeTag.setVisible(false);
                                oMDCConfigurationFARFragment.close();
                            });
                            promiseConfigurationList.catch(oError => {
                                sap.ui.core.BusyIndicator.hide();
                                let message = jQuery.parseJSON(oError.responseText).error.message.value;
                                Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");
                            });
                        },
                        oParam: this
                    },
                    error: function (oError) {
                        sap.ui.core.BusyIndicator.hide();
                        let message = jQuery.parseJSON(oError.responseText).error.message.value;
                        Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");
                    }
                }
            );
        });
    };

    oMDCConfigurationFAR.onClickClose = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sSelectedRow = oMDConsolidationModel.getProperty("/configData/SelectedRow");
        let sOperationType = oMDConsolidationModel.getProperty("/configData/OperationType");
        let oProcessStepTypeView = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeView");
        let oProcessStepTypeTag = sap.ui.getCore().byId("MDCConfigurationFARFragmentID--ProcessStepTypeTag");

        oProcessStepTypeView.destroyItems();
        oProcessStepTypeTag.setVisible(false);

        if (sOperationType === "C") {
            oMDConsolidationModel.setProperty(sSelectedRow + "/ConfigId", undefined);
        }

        oMDConsolidationModel.setProperty("/configData", {});

        let oMDCConfigurationFARFragment = this._getMDCConfigurationFragment("FAR");
        oMDCConfigurationFARFragment.close();
    };

    oMDCConfigurationFAR.onClickDelete = function(oEvent) {
        this.MDCConfigurationFAR.saveConfiguration(this, oEvent, "D");
    };

    oMDCConfigurationFAR.onClickSave = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sOperationType = oMDConsolidationModel.getProperty("/configData/OperationType");
        this.MDCConfigurationFAR.saveConfiguration(this, oEvent, sOperationType);
    };

    return oMDCConfigurationFAR;
});