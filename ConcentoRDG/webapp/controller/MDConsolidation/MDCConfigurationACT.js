// Task 12351 - Display/Edit/Delete/Add Configurations
sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "dmr/mdg/supernova/SupernovaFJ/libs/DataService",
    "sap/m/MessageBox"
], function (GetListsData, Utilities, DMRDataService, MessageBox) {
	"use strict";

    let oMDCConfigurationACT = {};

    oMDCConfigurationACT.onLiveChangeConfiguration = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sConfigId = oEvent.getSource().getProperty("value").toUpperCase();
        oEvent.getSource().setValue(sConfigId);

        if (sConfigId) {
            if (sConfigId.substring(0, 1) !== "Z" && sConfigId.substring(0, 1) !== "Y") {
                oMDConsolidationModel.setProperty("/configData/ConfigIdValueState", "Error");
                oMDConsolidationModel.setProperty("/configData/ConfigIdValueStateText", "Configuration ID must start with Z or Y");
            } else {
                oMDConsolidationModel.setProperty("/configData/ConfigIdValueState", "None");
                oMDConsolidationModel.setProperty("/configData/ConfigIdValueStateText", "");
            }
        } else {
            oMDConsolidationModel.setProperty("/configData/ConfigIdValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/ConfigIdValueStateText", "Configuration ID is mandatory");
        }
    };

    oMDCConfigurationACT.onLiveChangeDescription = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        if (oEvent.getSource().getProperty("value")) {
            oMDConsolidationModel.setProperty("/configData/DescValueState", "None");
            oMDConsolidationModel.setProperty("/configData/DescValueStateText", "");
        } else {
            oMDConsolidationModel.setProperty("/configData/DescValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/DescValueStateText", "Configuration description is mandatory");
        }
    };

    // 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog
    oMDCConfigurationACT.onChangeTarget = function(oEvent) {
        let sId = oEvent.getParameter("id");
        let sTarget = oEvent.getSource().getProperty("selectedKey");
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sBoType = oMDConsolidationModel.getProperty("/selectedProcessTemplate/BoType");
        let arrConfigurationDropdownLists = oMDConsolidationModel.getProperty("/configData/ConfigurationDropdownLists");

        let sCase = "";
        let sCrTypeItems = "";
        let sCrTypeSelectedKey = "";
        let sCrTypeValueState = "";
        let sCrTypeValueStateText = "";
        let sCrTypeMaxNoRec = "";
        switch (sId) {
            case this.MDCConfigurationACTFragmentID + "--NewTarget":
                sCase = "N";
                sCrTypeItems = "NewCrTypeList";
                sCrTypeSelectedKey = "NewCrType";
                sCrTypeValueState = "NewCrTypeValueState";
                sCrTypeValueStateText = "NewCrTypeValueStateText";
                sCrTypeMaxNoRec = "NewCrMaxNoRec";
                break;
            case this.MDCConfigurationACTFragmentID + "--NewTargetErr":
                sCase = "NI";
                sCrTypeItems = "NewCrTypeErrList";
                sCrTypeSelectedKey = "NewCrTypeErr";
                sCrTypeValueState = "NewCrTypeErrValueState";
                sCrTypeValueStateText = "NewCrTypeErrValueStateText";
                sCrTypeMaxNoRec = "NewCrMaxNoRecErr";
                break;
            case this.MDCConfigurationACTFragmentID + "--UpdTarget":
                sCase = "U";
                sCrTypeItems = "UpdCrTypeList";
                sCrTypeSelectedKey = "UpdCrType";
                sCrTypeValueState = "UpdCrTypeValueState";
                sCrTypeValueStateText = "UpdCrTypeValueStateText";
                sCrTypeMaxNoRec = "UpdCrMaxNoRec";
                break;
            case this.MDCConfigurationACTFragmentID + "--UpdTargetErr":
                sCase = "UI";
                sCrTypeItems = "UpdCrTypeErrList";
                sCrTypeSelectedKey = "UpdCrTypeErr";
                sCrTypeValueState = "UpdCrTypeErrValueState";
                sCrTypeValueStateText = "UpdCrTypeErrValueStateText";
                sCrTypeMaxNoRec = "UpdCrMaxNoRecErr";
                break;
            case this.MDCConfigurationACTFragmentID + "--MtcTarget":
                sCase = "M";
                sCrTypeItems = "MtcCrTypeList";
                sCrTypeSelectedKey = "MtcCrType";
                sCrTypeValueState = "MtcCrTypeValueState";
                sCrTypeValueStateText = "MtcCrTypeValueStateText";
                sCrTypeMaxNoRec = "MtcCrMaxNoRec";
                break;
            case this.MDCConfigurationACTFragmentID + "--MtcTargetErr":
                sCase = "M";
                sCrTypeItems = "MtcCrTypeErrList";
                sCrTypeSelectedKey = "MtcCrTypeErr";
                sCrTypeValueState = "MtcCrTypeErrValueState";
                sCrTypeValueStateText = "MtcCrTypeErrValueStateText";
                sCrTypeMaxNoRec = "MtcCrMaxNoRecErr";
                break;
            default:
                break;
        }
        
        let arrCrTypeItems = arrConfigurationDropdownLists.filter(oElement => {
            return oElement.BoType === sBoType && oElement.Case === sCase && oElement.Target === sTarget;
        });

        arrCrTypeItems.unshift(this._getEmptyCrTypeObject());

        if (sTarget === "N" || sTarget === "1") {
            oMDConsolidationModel.setProperty("/configData/" + sCrTypeItems, arrCrTypeItems);
            oMDConsolidationModel.setProperty("/configData/" + sCrTypeSelectedKey, "");
            oMDConsolidationModel.setProperty("/configData/" + sCrTypeMaxNoRec, "0");
        } else {
            oMDConsolidationModel.setProperty("/configData/" + sCrTypeItems, arrCrTypeItems);
            oMDConsolidationModel.setProperty("/configData/" + sCrTypeSelectedKey, "");
            oMDConsolidationModel.setProperty("/configData/" + sCrTypeValueState, "Error");
            oMDConsolidationModel.setProperty("/configData/" + sCrTypeValueStateText, "CR Type is mandatory");
        }
    };
    
    // 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog
    oMDCConfigurationACT.onChangeCrType = function(oEvent) {
        let sId = oEvent.getParameter("id");
        let sCrType = oEvent.getSource().getProperty("selectedKey");
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        let sCrTypeValueState = "";
        let sCrTypeValueStateText = "";
        switch (sId) {
            case this.MDCConfigurationACTFragmentID + "--NewCrType":
                sCrTypeValueState = "NewCrTypeValueState";
                sCrTypeValueStateText = "NewCrTypeValueStateText";
                break;
            case this.MDCConfigurationACTFragmentID + "--NewCrTypeErr":
                sCrTypeValueState = "NewCrTypeErrValueState";
                sCrTypeValueStateText = "NewCrTypeErrValueStateText";
                break;
            case this.MDCConfigurationACTFragmentID + "--UpdCrType":
                sCrTypeValueState = "UpdCrTypeValueState";
                sCrTypeValueStateText = "UpdCrTypeValueStateText";
                break;
            case this.MDCConfigurationACTFragmentID + "--UpdCrTypeErr":
                sCrTypeValueState = "UpdCrTypeErrValueState";
                sCrTypeValueStateText = "UpdCrTypeErrValueStateText";
                break;
            case this.MDCConfigurationACTFragmentID + "--MtcCrType":
                sCrTypeValueState = "MtcCrTypeValueState";
                sCrTypeValueStateText = "MtcCrTypeValueStateText";
                break;
            case this.MDCConfigurationACTFragmentID + "--MtcCrTypeErr":
                sCrTypeValueState = "MtcCrTypeErrValueState";
                sCrTypeValueStateText = "MtcCrTypeErrValueStateText";
                break;
            default:
                break;
        }

        if (sCrType) {
            oMDConsolidationModel.setProperty("/configData/" + sCrTypeValueState, "None");
            oMDConsolidationModel.setProperty("/configData/" + sCrTypeValueStateText, "");
        } else {
            oMDConsolidationModel.setProperty("/configData/" + sCrTypeValueState, "Error");
            oMDConsolidationModel.setProperty("/configData/" + sCrTypeValueStateText, "CR Type is mandatory");
        }
    };

    oMDCConfigurationACT.onSelectReplication = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        if (oEvent.getParameter("selected")) {
            oMDConsolidationModel.setProperty("/configData/Replication", true);
        } else {
            oMDConsolidationModel.setProperty("/configData/Replication", false);
        }
    };

    oMDCConfigurationACT.onSelectDQRuleValidations = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        if (oEvent.getParameter("selected")) {
            oMDConsolidationModel.setProperty("/configData/DQRuleValidations", true);
        } else {
            oMDConsolidationModel.setProperty("/configData/DQRuleValidations", false);
        }
    };

    oMDCConfigurationACT.onSelectMDGValidations = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        if (oEvent.getParameter("selected")) {
            oMDConsolidationModel.setProperty("/configData/MDGValidations", true);
        } else {
            oMDConsolidationModel.setProperty("/configData/MDGValidations", false);
        }
    };

    oMDCConfigurationACT.onSelectBRFPlusValidations = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        if (oEvent.getParameter("selected")) {
            oMDConsolidationModel.setProperty("/configData/BRFPlusValidations", true);
        } else {
            oMDConsolidationModel.setProperty("/configData/BRFPlusValidations", false);
        }
    };

    oMDCConfigurationACT.saveConfiguration = function(oController, oMDConsolidationModel, sOperationType) {
        let bValidationError = false;
        let sBoType = oMDConsolidationModel.getProperty("/selectedProcessTemplate/BoType");
        let sSelectedRow = oMDConsolidationModel.getProperty("/configData/SelectedRow");
        let sConfigId = oMDConsolidationModel.getProperty("/configData/ConfigId");
        let sDescription = oMDConsolidationModel.getProperty("/configData/Desc");
        let sNewTarget = oMDConsolidationModel.getProperty("/configData/NewTarget");
        let sNewCrType = oMDConsolidationModel.getProperty("/configData/NewCrType");
        let sNewTargetErr = oMDConsolidationModel.getProperty("/configData/NewTargetErr");
        let sNewCrTypeErr = oMDConsolidationModel.getProperty("/configData/NewCrTypeErr");
        let sUpdTarget = oMDConsolidationModel.getProperty("/configData/UpdTarget");
        let sUpdCrType = oMDConsolidationModel.getProperty("/configData/UpdCrType");
        let sUpdTargetErr = oMDConsolidationModel.getProperty("/configData/UpdTargetErr");
        let sUpdCrTypeErr = oMDConsolidationModel.getProperty("/configData/UpdCrTypeErr");
        let sMtcTarget = oMDConsolidationModel.getProperty("/configData/MtcTarget");
        let sMtcCrType = oMDConsolidationModel.getProperty("/configData/MtcCrType");
        let sMtcTargetErr = oMDConsolidationModel.getProperty("/configData/MtcTargetErr");
        let sMtcCrTypeErr = oMDConsolidationModel.getProperty("/configData/MtcCrTypeErr");

        
        // Bug 12404 - Remove fields based on version related properties
        let sScheduleThreshold = oMDConsolidationModel.getProperty("/ShowScheduleThreshold") ? oMDConsolidationModel.getProperty("/configData/ScheduleThreshold") : 0;

        // Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog
        if (!sConfigId) {
            bValidationError = true;
            oMDConsolidationModel.setProperty("/configData/ConfigIdValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/ConfigIdValueStateText", "Configuration ID is mandatory");
        }
        if (!sDescription) {
            bValidationError = true;
            oMDConsolidationModel.setProperty("/configData/DescValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/DescValueStateText", "Configuration description is mandatory");
        }
        if ((sNewTarget === "2" || sNewTarget === "3") && sNewCrType === "") {
            bValidationError = true;
            oMDConsolidationModel.setProperty("/configData/NewCrTypeValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/NewCrTypeValueStateText", "CR Type is mandatory");
        }
        if ((sNewTargetErr === "2" || sNewTargetErr === "3") && sNewCrTypeErr === "") {
            bValidationError = true;
            oMDConsolidationModel.setProperty("/configData/NewCrTypeErrValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/NewCrTypeErrValueStateText", "CR Type is mandatory");
        }
        if ((sUpdTarget === "2" || sUpdTarget === "3") && sUpdCrType === "") {
            bValidationError = true;
            oMDConsolidationModel.setProperty("/configData/UpdCrTypeValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/UpdCrTypeValueStateText", "CR Type is mandatory");
        }
        if ((sUpdTargetErr === "2" || sUpdTargetErr === "3") && sUpdCrTypeErr === "") {
            bValidationError = true;
            oMDConsolidationModel.setProperty("/configData/UpdCrTypeErrValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/UpdCrTypeErrValueStateText", "CR Type is mandatory");
        }
        if ((sMtcTarget === "2" || sMtcTarget === "3") && sMtcCrType === "") {
            bValidationError = true;
            oMDConsolidationModel.setProperty("/configData/MtcCrTypeValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/MtcCrTypeValueStateText", "CR Type is mandatory");
        }
        if ((sMtcTargetErr === "2" || sMtcTargetErr === "3") && sMtcCrTypeErr === "") {
            bValidationError = true;
            oMDConsolidationModel.setProperty("/configData/MtcCrTypeErrValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/MtcCrTypeErrValueStateText", "CR Type is mandatory");
        }

        if (bValidationError) {
            Utilities.showPopupAlert("One or more mandatory fields are missing", MessageBox.Icon.ERROR, "Missing mandatory fields");
            return;
        } else {
            let oConfigurationModel = oMDConsolidationModel.getProperty("/configData");
            let promiseTransport = oController._getSelectedTransportPackage(true, false, false);
            // Task 13213: make the function asyncronous so we can call the corresponding service name
            promiseTransport.then(async oSelectedTransport => {
                sap.ui.core.BusyIndicator.show(300);
                
                let oPayload = {
                    ...oConfigurationModel,
                    Message: "",
                    MessageCode: "",
                    Operation: sOperationType,
                    CustTransport: oSelectedTransport.customizingTransport,
                    BoType: sBoType,
                    ConfigId: sConfigId,
                    Desc: sDescription,
                    // Bug 12733 - Adding validation for numbers to send 0 if they don't have value
                    ParallelProcs: isNaN(Number(oMDConsolidationModel.getProperty("/configData/ParallelProcs"))) ? 0 : Number(oMDConsolidationModel.getProperty("/configData/ParallelProcs")),
                    NewCrMaxNoRec: isNaN(Number(oMDConsolidationModel.getProperty("/configData/NewCrMaxNoRec"))) ? 0 : Number(oMDConsolidationModel.getProperty("/configData/NewCrMaxNoRec")),
                    NewCrMaxNoRecErr: isNaN(Number(oMDConsolidationModel.getProperty("/configData/NewCrMaxNoRecErr"))) ? 0 : Number(oMDConsolidationModel.getProperty("/configData/NewCrMaxNoRecErr")),
                    UpdCrMaxNoRec: isNaN(Number(oMDConsolidationModel.getProperty("/configData/UpdCrMaxNoRec"))) ? 0 : Number(oMDConsolidationModel.getProperty("/configData/UpdCrMaxNoRec")),
                    UpdCrMaxNoRecErr: isNaN(Number(oMDConsolidationModel.getProperty("/configData/UpdCrMaxNoRecErr"))) ? 0 : Number(oMDConsolidationModel.getProperty("/configData/UpdCrMaxNoRecErr")),
                    MtcCrMaxNoRec: isNaN(Number(oMDConsolidationModel.getProperty("/configData/MtcCrMaxNoRec"))) ? 0 : Number(oMDConsolidationModel.getProperty("/configData/MtcCrMaxNoRec")),
                    MtcCrMaxNoRecErr: isNaN(Number(oMDConsolidationModel.getProperty("/configData/MtcCrMaxNoRecErr"))) ? 0 : Number(oMDConsolidationModel.getProperty("/configData/MtcCrMaxNoRecErr")),
                    RetentionDays: isNaN(Number(oMDConsolidationModel.getProperty("/configData/RetentionDays"))) ? 0 : Number(oMDConsolidationModel.getProperty("/configData/RetentionDays")),
                    RetentionHours: isNaN(Number(oMDConsolidationModel.getProperty("/configData/RetentionHours"))) ? 0 : Number(oMDConsolidationModel.getProperty("/configData/RetentionHours")),
                    WaitingPeriod: isNaN(Number(oMDConsolidationModel.getProperty("/configData/WaitingPeriod"))) ? 0 : Number(oMDConsolidationModel.getProperty("/configData/WaitingPeriod")),
                    MDCHGPROCNMBROFOBJECTS: isNaN(Number(oMDConsolidationModel.getProperty("/configData/MDCHGPROCNMBROFOBJECTS"))) ? 0 : Number(oMDConsolidationModel.getProperty("/configData/MDCHGPROCNMBROFOBJECTS")),
                    ScheduleThreshold: isNaN(Number(sScheduleThreshold)) ? 0 : Number(sScheduleThreshold)
                    // Bug 12404 - Remove fields based on version related properties
                };

                delete oPayload.OperationType;
                delete oPayload.SelectedRow;
                delete oPayload.ConfigIdValueState;
                delete oPayload.ConfigIdValueStateText;
                delete oPayload.DescValueState;
                delete oPayload.DescValueStateText;
                delete oPayload.NewTargetList;
                delete oPayload.NewTargetErrList;
                delete oPayload.UpdTargetList;
                delete oPayload.UpdTargetErrList;
                delete oPayload.MtcTargetList;
                delete oPayload.MtcTargetErrList;
                // Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog
                delete oPayload.NewCrTypeList;
                delete oPayload.NewCrTypeValueState;
                delete oPayload.NewCrTypeValueStateText;
                delete oPayload.NewCrTypeErrList;
                delete oPayload.NewCrTypeErrValueState;
                delete oPayload.NewCrTypeErrValueStateText;
                delete oPayload.UpdCrTypeList;
                delete oPayload.UpdCrTypeValueState;
                delete oPayload.UpdCrTypeValueStateText;
                delete oPayload.UpdCrTypeErrList;
                delete oPayload.UpdCrTypeErrValueState;
                delete oPayload.UpdCrTypeErrValueStateText;
                delete oPayload.MtcCrTypeList;
                delete oPayload.MtcCrTypeValueState;
                delete oPayload.MtcCrTypeValueStateText;
                delete oPayload.MtcCrTypeErrList;
                delete oPayload.MtcCrTypeErrValueState;
                delete oPayload.MtcCrTypeErrValueStateText;
                delete oPayload.ConfigurationDropdownLists;
                // Bug 12733 - deleting the property StepType from the payload to save ACT configuration
                delete oPayload.StepType;
                
                // Bug 12523 - Custom adapter cannot be created in SD2 only
                (new DMRDataService(
                    oController,
                    // Task 13213: call the corresponding service name depending on the system
                    await oController.serviceName,
                    "/ACTIVATIONSet",
                    "Save Configuration",
                    "/", // Root of the received data
                    "Save Configuration"
                )).saveData(
                    false,
                    oPayload,
                    null, {
                        success: {
                            fCallback: function (oDataResult, oResponse) {
                                sap.ui.core.BusyIndicator.hide();

                                if (oResponse.MessageCode === "E") {
                                    Utilities.showPopupAlert("Error: " + oResponse.Message, MessageBox.Icon.ERROR, "Error");
                                    return;
                                }

                                let arrConfigList = [];
                                let promiseConfigurationList = GetListsData.getConfigurationACTDetails(oController, sBoType);
                                promiseConfigurationList.then(oData => {
                                    arrConfigList.push(oController._getCreateConfigurationObject());
                                    arrConfigList.push(oController._getEmptyConfigurationObject());
                                    oData.forEach(oConfiguration => {
                                        arrConfigList.push({
                                            Zkey: "",
                                            Id: oConfiguration.ConfigId,
                                            Description: oConfiguration.Desc
                                        });
                                    });

                                    oMDConsolidationModel.setProperty(sSelectedRow + "/arrConfigList", arrConfigList);
                                    oMDConsolidationModel.setProperty(sSelectedRow + "/ConfigId", ( sOperationType === "D"? "": sConfigId ));
                                    oMDConsolidationModel.setProperty("/configData", {});
                                    
                                    // Bug 12351 - Display/Edit/Delete/Add Configurations
                                    oController._refreshTemplateDropDownList(oController);

                                    if (oResponse.MessageCode === "S") {
                                        Utilities.showPopupAlert(oResponse.Message, MessageBox.Icon.SUCCESS, "Success");
                                    }

                                    let oMDCConfigurationACTFragment = oController._getMDCConfigurationFragment("ACT");
                                    oMDCConfigurationACTFragment.close();
                                });
                                promiseConfigurationList.catch(oError => {
                                    sap.ui.core.BusyIndicator.hide();
                                    let message = jQuery.parseJSON(oError.responseText).error.message.value;
                                    Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");
                                });
                            },
                            oParam: this
                        },
                        error: function (oError) {
                            sap.ui.core.BusyIndicator.hide();
                            let message = jQuery.parseJSON(oError.responseText).error.message.value;
                            Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");
                        }
                    }
                );
            });
        }
    };

    oMDCConfigurationACT.onClickClose = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sSelectedRow = oMDConsolidationModel.getProperty("/configData/SelectedRow");
        let sOperationType = oMDConsolidationModel.getProperty("/configData/OperationType");

        if (sOperationType === "C") {
            oMDConsolidationModel.setProperty(sSelectedRow + "/ConfigId", undefined);
        }

        oMDConsolidationModel.setProperty("/configData", {});

        let oMDCConfigurationACTFragment = this._getMDCConfigurationFragment("ACT");
        oMDCConfigurationACTFragment.close();
    };

    oMDCConfigurationACT.onClickDelete = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        this.MDCConfigurationACT.saveConfiguration(this, oMDConsolidationModel, "D");
    };

    oMDCConfigurationACT.onClickSave = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sOperationType = oMDConsolidationModel.getProperty("/configData/OperationType");
        this.MDCConfigurationACT.saveConfiguration(this, oMDConsolidationModel, sOperationType);
    };

    return oMDCConfigurationACT;
});