sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/model/GetLists",
    "sap/ui/model/json/JSONModel",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "dmr/mdg/supernova/SupernovaFJ/libs/DataService",
    "sap/m/MessageBox",
], function (GetListsData, JSONModel, Utilities, DMRDataService, MessageBox) {
	"use strict";

    let oMDCConfigurationMTC = {};

    oMDCConfigurationMTC.onLiveChangeConfiguration = async function(oEvent){
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");
        let arrMTCAllData = oMDConsolidationModel.getProperty("/configData/MTCAll");
        let sValue = oEvent.getSource().getValue().toUpperCase();
        oEvent.getSource().setValue(sValue);

        let bRepeatedValue = this.MDCConfigurationMTC._ValidateRepeatValue(arrMTCAllData, "Configuration", sValue);

        let oValueStateFunctionData = {
            sPath: "ConfigId",
            bRequired: true,
            sType: "text",
            bRepeatedValue: bRepeatedValue,
            bStartValidation: true,
            bIsRule: false
        };
        this.MDCConfigurationMTC._ValueStateHandler(oEvent, oValueStateFunctionData);
    };

    oMDCConfigurationMTC.onLiveChangeDescription = async function(oEvent){
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");

        let oValueStateFunctionData = {
            sPath: "Description",
            bRequired: true,
            sType: "text",
            bIsRule: false
        };

        this.MDCConfigurationMTC._UpdateConfStatusOnChange(oMDConsolidationModel);
        this.MDCConfigurationMTC._ValueStateHandler(oEvent, oValueStateFunctionData);
    };

    oMDCConfigurationMTC.onApprovalScoreChange = function(oEvent){
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");

        this.MDCConfigurationMTC._UpdateConfStatusOnChange(oMDConsolidationModel);
        this.MDCConfigurationMTC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationMTC.onConfigurationChange = function(oEvent){
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");

        this.MDCConfigurationMTC._UpdateConfStatusOnChange(oMDConsolidationModel);
    };

    oMDCConfigurationMTC._UpdateConfStatusOnChange = function (oMDConsolidationModel){
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        
        if(oConfigData.OperationType === "U" && !oConfigData.ConfigEdited){
            oConfigData.ConfigEdited = true;
            oConfigData.VisCancelConf = true;
            oConfigData.EbSaveBtn = true;
            oMDConsolidationModel.setProperty("/configData", oConfigData);
            oMDConsolidationModel.refresh();
        }
    };

    oMDCConfigurationMTC.onRowClick = function(oEvent){
        let oController = this;
        let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");
        let oClickedItem = oEvent.getParameters();
        let iSelectedIndex = oEvent.getSource().getSelectedIndex();

        // in case the user deselects the row
        if(iSelectedIndex === -1){
            oMTCFormView.destroyItems();
            oConfigData.EbAddBtn = false;
            oConfigData.VisDeleteRuleBtn = false;
            oConfigData.VisCancelRuleBtn = false;
            oMDConsolidationModel.refresh();
            return;
        }

        let sPath = oClickedItem.rowContext.getPath();

        oController.MDCConfigurationMTC._setButtonPropertiesOnRowSelection(oMDConsolidationModel, sPath);
        oController.MDCConfigurationMTC._changeFormView(oController, oMDConsolidationModel, sPath);
    };

    oMDCConfigurationMTC._changeFormView = async function(oController, oMDConsolidationModel, sPath){
        let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oSelectedRowData = oMDConsolidationModel.getProperty(sPath);
        let iLevel = sPath.split("/children").length -1;

        oMTCFormView.destroyItems();

        if (oConfigData.OperationType !== "C"){
            //reset all of the previous form data to undefined
            oController.MDCConfigurationMTC._resetFormModelData(oMDConsolidationModel);
        }

        //If a rule is selected
        if(iLevel===1){
            oController.MDCConfigurationMTC._createView(oController, oMDConsolidationModel, "Rule", "Add Attribute", "ruleData", oSelectedRowData);
            
        //If an attribute is selected
        }else if(iLevel===2){
            let arrFieldData = [];
            let arrFieldList = [];
            // bug 134484 - table name enables when the row is new 
            if(oSelectedRowData.RowStatus === "Success"){
                oMDConsolidationModel.setProperty(sPath + "/EbTableName", true);
            } else{
                oMDConsolidationModel.setProperty(sPath + "/EbTableName", false);
            }

            try {
                arrFieldList = await GetListsData.getConfigurationBRCFieldsSelect(oController, oConfigData.BoType, oSelectedRowData.TableName);
            } catch (oError) {
                if (oError.statusCode !== "404") {
                    Utilities.showPopupAlert(
                        "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                        MessageBox.Icon.ERROR, 
                        "Error"
                    );
                }
            }
            
            // change the object 
            arrFieldList.forEach(element => {
                let oTemp = {
                    key: element.Fieldname,
                    text: element.Fieldname + " - " + element.Fieldtext
                };
                
                arrFieldData.push(oTemp);
            });
            
            oSelectedRowData.FieldData = arrFieldData;
            oController.MDCConfigurationMTC._createView(oController, oMDConsolidationModel, "Attribute", "Add Condition", "attributeData", oSelectedRowData);
            
        //If a condition is selected
        }else if(iLevel===3){
            oController.MDCConfigurationMTC._createView(oController, oMDConsolidationModel, "Condition", "Add Condition", "conditionData", oSelectedRowData);
        }
    };

    oMDCConfigurationMTC._setButtonPropertiesOnRowSelection = function(oMDConsolidationModel, sPath){
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oRowData = oMDConsolidationModel.getProperty(sPath);
        
        if(oRowData.RowStatus === "Success"){
            oConfigData.VisCancelRuleBtn = true;
            oConfigData.CancelRuleBtnText = "Cancel";
            oConfigData.VisDeleteRuleBtn = false;
        } else{
            oConfigData.VisCancelRuleBtn = (oRowData.RowStatus === "Warning");
            oConfigData.CancelRuleBtnText = "Cancel Edit";
            oConfigData.VisDeleteRuleBtn = true;

        }
            
        //set the enabled property on corresponding buttons
        oConfigData.EbAddBtn = true;
        oConfigData.EbAddRuleBtn = true;
    };

    oMDCConfigurationMTC._setButtonPropertiesOnAddPress = function(oMDConsolidationModel){
        let oConfigData = oMDConsolidationModel.getProperty("/configData");

        oConfigData.VisCancelRuleBtn = true;
        oConfigData.EbAddBtn = true;

        if(oConfigData.OperationType!=="C"){
            oConfigData.VisDeleteRuleBtn = false;
        }
    };


    oMDCConfigurationMTC.onCancelConfPress = function(oEvent){
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oTempData = oMDConsolidationModel.getProperty("/tempConfigData");
        let oResetData = structuredClone(oTempData);

        oConfigData.ConfigId = oResetData.ConfigId;
        oConfigData.ConfigIdValueState = oResetData.ConfigIdValueState;
        oConfigData.ConfigIdValueStateText = oResetData.ConfigIdValueStateText;
        oConfigData.Description = oResetData.Description;
        oConfigData.DescriptionValueState = oResetData.DescriptionValueState;
        oConfigData.DescriptionValueStateText = oResetData.DescriptionValueStateText;
        oConfigData.ScoreSelection = oResetData.ScoreSelection;
        oConfigData.MTCUsage = oResetData.MTCUsage;
        oConfigData.UseClassification = oResetData.UseClassification;
        oConfigData.DuplCheckDflt = oResetData.DuplCheckDflt;
        oConfigData.ApprScore = oResetData.ApprScore;
        // Bug 13448: added approval score value state
        oConfigData.ApprScoreValueState = oResetData.ApprScoreValueState;
        oConfigData.eTag = oResetData.eTag;
        oConfigData.LastChanged = oResetData.LastChange;

                
        oConfigData.ConfigEdited = oResetData.ConfigEdited;
        oConfigData.VisCancelConf = oResetData.VisCancelConf;

        oMDConsolidationModel.refresh();
        this.MDCConfigurationMTC._ValidateFragmentToSave(oMDConsolidationModel);

    };

    oMDCConfigurationMTC.onCancelPress = function (){
        let oController = this;
        let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oTreeTable = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--ruleTreeTable");
        let iSelectedIndex = oTreeTable.getSelectedIndex();
        let sPath = oTreeTable.getContextByIndex(iSelectedIndex).getPath();
        let oRow = oMDConsolidationModel.getProperty(sPath);

        if(oRow.RowStatus === "Success"){
            // Show a message asking the user to confirm deletion. Exit if cancel 
            let promise = new Promise(function (resolve, reject) {
                let promiseShowPopupAlert = Utilities.showPopupAlert("This rule will be deleted. Continue?", MessageBox.Icon.WARNING,
                    "Delete?", [sap.m.MessageBox.Action.YES, sap.m.MessageBox.Action.NO]);
                promiseShowPopupAlert.then(function () {
                    resolve();
                }, function () {
                    reject();
                });
            });
    
            promise.then(async function () {
                
                let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");
                oController.MDCConfigurationMTC._removeRow(oConfigData, sPath);
                
                oMTCFormView.destroyItems();
                oConfigData.bIsCreating = true;
                
                oConfigData.VisCancelRuleBtn = false;
        
                //enabled the add buttons
                oConfigData.EbAddRuleBtn = true;
                oConfigData.EbAddBtn = true;
        
                oConfigData.bIsCreating = false;
                oMDConsolidationModel.refresh();
                oController.MDCConfigurationMTC._ValidateFragmentToSave(oMDConsolidationModel);
            });
        } else {
            let sRawPath = sPath.split("/configData")[1];
            let arrIndexes = sPath.split("/children/");
            let iLevel = arrIndexes.length -1;
            let oTempData = oMDConsolidationModel.getProperty("/tempConfigData" + sRawPath);
            let oResetData = structuredClone(oTempData);

            if(iLevel === 1){
                oRow.Configuration = oResetData.Configuration;
                oRow.MinScore = oResetData.MinScore;
                oRow.MinScoreValueState = oResetData.MinScoreValueState;
                oRow.Name = oResetData.Name;
                oRow.RuleId = oResetData.RuleId;
                oRow.SequenceNo = oResetData.SequenceNo;
                oRow.RuleName = oResetData.RuleName;
                oRow.RuleNameValueState = oResetData.RuleNameValueState;
                oRow.RuleNameValueStateText = oResetData.RuleNameValueStateText;
                oRow.RowStatus = oResetData.RowStatus;
            } else if(iLevel === 2){
                oRow.Configuration = oResetData.Configuration;
                oRow.RuleId = oResetData.RuleId;
                oRow.AttributeId = oResetData.AttributeId;
                oRow.SequenceNo = oResetData.SequenceNo;
                oRow.Name = oResetData.Name;
                oRow.EmptyMtcNull = oResetData.EmptyMtcNull;
                oRow.EmptyScore = oResetData.EmptyScore;
                // Bug 13448: added empty score value state
                oRow.EmptyScoreValueState = oResetData.EmptyScoreValueState;
                oRow.FieldData = oResetData.FieldData;
                oRow.FieldName = oResetData.FieldName;
                oRow.FieldNameValueState = oResetData.FieldNameValueState;
                oRow.FieldNameValueStateText = oResetData.FieldNameValueStateText;
                oRow.Fuzziness = oResetData.Fuzziness;
                // Bug 13448: added fuzziness value state
                oRow.FuzzinessValueState = oResetData.FuzzinessValueState;
                oRow.ScFactor = oResetData.ScFactor;
                // Bug 13448: added spellcheck factor value state
                oRow.ScFactorValueState = oResetData.ScFactorValueState;
                oRow.SimCalcMode = oResetData.SimCalcMode;
                oRow.TableName = oResetData.TableName;
                oRow.TableNameValueState = oResetData.TableNameValueState;
                oRow.TableNameValueStateText = oResetData.TableNameValueStateText;
                oRow.Weight = oResetData.Weight;
                // Bug 13448: added weight value state
                oRow.WeightValueState = oResetData.WeightValueState;
                oRow.RowStatus = oResetData.RowStatus;
            } else{
                oRow.Configuration = oResetData.Configuration;
                oRow.RuleId = oResetData.RuleId;
                oRow.AttributeId = oResetData.AttributeId;
                oRow.ConditionId = oResetData.ConditionId;
                oRow.SequenceNo = oResetData.SequenceNo;
                oRow.Name = oResetData.Name;
                oRow.Action = oResetData.Action;
                oRow.Condition = oResetData.Condition;
                oRow.ReplaceBy = oResetData.ReplaceBy;
                oRow.Value = oResetData.Value;
                oRow.RowStatus = oResetData.RowStatus;
            }

            oConfigData.VisCancelRuleBtn = false;
            oMDConsolidationModel.refresh();
            oController.MDCConfigurationMTC._changeFormView(oController, oMDConsolidationModel, sPath);
            oController.MDCConfigurationMTC._ValidateFragmentToSave(oMDConsolidationModel);
        }

    };

    oMDCConfigurationMTC._removeRow = function(oConfigData, sPath){

        let arrIndexes = sPath.split("/children/");
        let iLevel = arrIndexes.length -1;

        if(iLevel === 1){
            oConfigData.children.splice(arrIndexes[1], 1);
        } else if(iLevel === 2){
            oConfigData.children[arrIndexes[1]].children.splice(arrIndexes[2], 1);
        } else{
            oConfigData.children[arrIndexes[1]].children[arrIndexes[2]].children.splice(arrIndexes[3], 1);
        }

        return oConfigData;
    };

    oMDCConfigurationMTC._createView = function(oController, oMDConsolidationModel, sHeaderText, sAddButtonText, sPath,  oSelectedRowData){
        //set the header text on the right panel
        oMDConsolidationModel.setProperty("/configData/PanelText", sHeaderText);
        //set the Add button text
        oMDConsolidationModel.setProperty("/configData/AddBtnText", sAddButtonText);
        //set the condition data to the model
        oMDConsolidationModel.setProperty("/configData/" + sPath, oSelectedRowData);
        
        //create view
        if(sHeaderText === "Rule"){
            this._createRuleView(oMDConsolidationModel);
        } else if(sHeaderText === "Attribute"){
            this._createAttributeView(oController, oMDConsolidationModel);
        } else {
            this._createConditionView(oMDConsolidationModel);
        }
    };
    
    oMDCConfigurationMTC.onAddRulePress = function (){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");
        let oTreeTable = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--ruleTreeTable");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let iRuleNo = this.MDCConfigurationMTC._LastRuleNo(this);
        let iLastItem = oConfigData.children.length;
        
        let oNewRule = {
            Configuration: oConfigData.ConfigId,
            MinScore: 0.80,
            MinScoreValueState: "None",
            Name: "New_Rule_" + iRuleNo,
            RuleId: "RULE_" + iRuleNo,
            SequenceNo: iRuleNo,
            RuleName: "",
            RuleNameValueState: "Error",
            RuleNameValueStateText: "Field is mandatory",
            children: [],
            RowStatus: "Success"
        };

        oMTCFormView.destroyItems();

        //create rule view
        this.MDCConfigurationMTC._createRuleView(oMDConsolidationModel);
        //set the header text to "Rule"
        oConfigData.PanelText = "Rule";
        oConfigData.AddBtnText = "Add Attribute";

        if (oConfigData.OperationType !== "C"){
            //reset all of the previous form data to undefined
            this.MDCConfigurationMTC._resetFormModelData(oMDConsolidationModel);
        }
        //add the data to the config data
        oConfigData.children.push(oNewRule);
        oConfigData.ruleData = oNewRule;
        oConfigData.bIsCreating = true;

        this.MDCConfigurationMTC._setButtonPropertiesOnAddPress(oMDConsolidationModel);

        oTreeTable.collapseAll();
        oTreeTable.setSelectedIndex(iLastItem);

        this.MDCConfigurationMTC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationMTC.onAddPress = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");
        let oTreeTable = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--ruleTreeTable");
        let iSelectedIndex = oTreeTable.getSelectedIndex();

        if(iSelectedIndex<0){
            Utilities.showPopupAlert("Please select a row.", MessageBox.Icon.ERROR, "Error");
            return;
        }

        let sPath = oTreeTable.getContextByIndex(iSelectedIndex).getPath();
        let iLevel = sPath.split("/children").length -1;
        if(iLevel === 3){
            sPath = sPath.substring(0, sPath.lastIndexOf("/children"));
        }
        let arrIndexes = sPath.split("/children/");
        arrIndexes = arrIndexes.map(Number);
        let oRowData = oMDConsolidationModel.getProperty(sPath);
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        
        oMTCFormView.destroyItems();
        
        if(iLevel === 1){
            let iAttributeNo = this.MDCConfigurationMTC._LastAttributeNo(this);

            let oNewAttribute = {
                Configuration: oConfigData.ConfigId,
                RuleId: oRowData.RuleId,
                AttributeId: "ATTR_" + iAttributeNo,
                SequenceNo: iAttributeNo,
                Name: "New_Attribute_" + iAttributeNo,
                EmptyMtcNull: false,
                EmptyScore: 0.00,
                FieldData :[],
                FieldName: "",
                FieldNameValueState: "Error",
                FieldNameValueStateText: "Field is mandatory",    
                Fuzziness: 0.80,
                ScFactor: 0.90,
                SimCalcMode: "SEARCH",
                TableName: "",
                TableNameValueState: "Error",
                TableNameValueStateText: "Field is mandatory",    
                Weight: 1.00,
                children: [],
                RowStatus: "Success"
            };

            //create Attribute view
            this.MDCConfigurationMTC._createAttributeView(this, oMDConsolidationModel);

            //set the header text to "Attribute"
            oConfigData.PanelText = "Attribute";
            oConfigData.AddBtnText = "Add Condition";


            if (oConfigData.OperationType !== "C"){
                //reset all of the previous form data to undefined
                this.MDCConfigurationMTC._resetFormModelData(oMDConsolidationModel);
            }

            // //add the data to the config data
            oRowData.children.push(oNewAttribute);
            oConfigData.attributeData = oNewAttribute;
        } else {
            let iConditionNo = this.MDCConfigurationMTC._LastConditionNo(this);

            let oNewCondition = {
                Configuration: oConfigData.ConfigId,
                RuleId: oRowData.RuleId,
                AttributeId: oRowData.AttributeId,
                ConditionId: "COND_" + iConditionNo,
                SequenceNo: iConditionNo,
                Name: "New_Condition_" + iConditionNo,
                Action: "SKIPCOLUMN",
                Condition: "ifMissing",
                ReplaceBy: "",
                Value: "",
                RowStatus: "Success"
            };

            //create Condition view
            this.MDCConfigurationMTC._createConditionView(oMDConsolidationModel);
            
            //set the header text to "Condition"
            oConfigData.PanelText = "Condition";
            oConfigData.AddBtnText = "Add Condition";

            if (oConfigData.OperationType !== "C"){
                //reset all of the previous form data to undefined
                this.MDCConfigurationMTC._resetFormModelData(oMDConsolidationModel);
            }
            
            //add the data to the config data
            oRowData.children.push(oNewCondition);
            oConfigData.conditionData = oNewCondition;
        }

        oConfigData.bIsCreating = true;

        this.MDCConfigurationMTC._setButtonPropertiesOnAddPress(oMDConsolidationModel);
        oMDConsolidationModel.refresh();

        oTreeTable.collapseAll();
        let iNewSelectedIndex;
        let iLastItem;
        if(iLevel===1){
            iLastItem = oConfigData.children[arrIndexes[1]].children.length;
            iNewSelectedIndex = iLastItem + arrIndexes[1];
            oTreeTable.expand(arrIndexes[1]);
            oTreeTable.setSelectedIndex(iNewSelectedIndex);
        } else {
            iLastItem = oConfigData.children[arrIndexes[1]].children[arrIndexes[2]].children.length;
            iNewSelectedIndex = iLastItem + arrIndexes[1] + arrIndexes[2] + 1;
            oTreeTable.expand(arrIndexes[1]);
            oTreeTable.expand(arrIndexes[1] + arrIndexes[2] + 1);
            oTreeTable.setSelectedIndex(iNewSelectedIndex);
        }

        this.MDCConfigurationMTC._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationMTC.onClickClose = function() {
        let oController = this;
        let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let bChangesWereMade = this.MDCConfigurationMTC._ValidateIfChangesWereMade(oMDConsolidationModel);

        if(oConfigData.OperationType==="U"){
            let oValidationResult = this.MDCConfigurationMTC._validateMinRequirements(oController);
            if(oValidationResult.result === "E"){
                Utilities.showPopupAlert(oValidationResult.message, MessageBox.Icon.ERROR, "Error");
                return;
            }
        }

        if(bChangesWereMade){
            
            // Show a message asking the user to confirm deletion. Exit if cancel 
            let promise = new Promise(function (resolve, reject) {
                let promiseShowPopupAlert = Utilities.showPopupAlert("There are unsaved changes. If you continue without saving \nyou will lose your data. Continue?", MessageBox.Icon.WARNING,
                    "Delete?", [sap.m.MessageBox.Action.YES, sap.m.MessageBox.Action.NO]);
                promiseShowPopupAlert.then(function () {
                    resolve();
                }, function () {
                    reject();
                });
            });

            promise.then(async function () {
                oController.MDCConfigurationMTC._closeForm(oController, oMDConsolidationModel);
            });
        } else{
            oController.MDCConfigurationMTC._closeForm(oController, oMDConsolidationModel);
        }

    };

    oMDCConfigurationMTC._closeForm = function (oController, oMDConsolidationModel){
        let sSelectedRow = oMDConsolidationModel.getProperty("/configData/SelectedRow");
        let sOperationType = oMDConsolidationModel.getProperty("/configData/OperationType");
        let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");
        let oTreeTable = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--ruleTreeTable");
        oMTCFormView.destroyItems();

        if (sOperationType === "C") {
            oMDConsolidationModel.setProperty(sSelectedRow + "/ConfigId", undefined);
        }

        oMDConsolidationModel.setProperty("/configData", {});
        oMDConsolidationModel.setProperty("/tempConfigData", {});

        oController.MDCConfigurationMTC._resetFormModelData(oMDConsolidationModel);
        oTreeTable.collapseAll();

        let oMDCConfigurationMTCFragment = oController._getMDCConfigurationFragment("MTC");
        oMDCConfigurationMTCFragment.close();
    };

    oMDCConfigurationMTC._resetFormModelData = function(oMDConsolidationModel){
        oMDConsolidationModel.setProperty("/configData/ruleData", undefined);
        oMDConsolidationModel.setProperty("/configData/attributeData", undefined);
        oMDConsolidationModel.setProperty("/configData/conditionData", undefined);
    };

    oMDCConfigurationMTC._createRuleView = function(){
        let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");
        
        //create the VBox, Label and Input for Rule Name
        let oRuleVBox = new sap.m.VBox();
        let oRuleLabel = this._createLabel("RuleName", true, "Rule Name");
        let oRuleInput = this._createInput("RuleName", "Add rule name", "ruleData", true, true);
        
        //add the label and input of the Rule Name to the corresponding VBox
        oRuleVBox.addItem(oRuleLabel);
        oRuleVBox.addItem(oRuleInput);

        //create the VBox, Label and Input for Minimum Score
        let oMinimumScoreVBox = new sap.m.VBox();
        let oMinimumScoreLabel = this._createLabel("MinScore", false, "Minimum Score");
        let oMinimumScoreInput = this._createStepInput("MinScore", "ruleData");

        //add the label and input of the Minimum Score to the corresponding VBox
        oMinimumScoreVBox.addItem(oMinimumScoreLabel);
        oMinimumScoreVBox.addItem(oMinimumScoreInput);

        //add the VBoxes to the MTCFormView VBox
        oMTCFormView.addItem(oRuleVBox);
        oMTCFormView.addItem(oMinimumScoreVBox);
    };

    oMDCConfigurationMTC._createAttributeView = function(oController, oMDConsolidationModel){
        let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");
        let arrTableList = oMDConsolidationModel.getProperty("/configData/TableData");
        let arrFieldList = oMDConsolidationModel.getProperty("/configData/attributeData/FieldData");
        let arrTableData = [];

        //create the VBox, Label and Input for Table Name
        let oTableNameVBox = new sap.m.VBox();
        let oTableNameLabel = this._createLabel("TableName", true, "Table Name");

        arrTableList.forEach(element => {
            let oTemp = {
                key: element.TableName,
                text: element.TableName + " - " + element.TableDescription
            };

            arrTableData.push(oTemp);
        });

        let oTableNameInput = this._createSelect(oController, "TableName", "attributeData", arrTableData, true);

        //add the label and input of Table Name to the corresponding VBox
        oTableNameVBox.addItem(oTableNameLabel);
        oTableNameVBox.addItem(oTableNameInput);

        //create the VBox, Label and Input for Field Name
        let oFieldNameVBox = new sap.m.VBox();
        let oFieldNameLabel = this._createLabel("FieldName", true, "Field Name");
        let oFieldNameInput = this._createSelect(oController, "FieldName", "attributeData", arrFieldList, true);

        //add the label and input of Field Name to the corresponding VBox
        oFieldNameVBox.addItem(oFieldNameLabel);
        oFieldNameVBox.addItem(oFieldNameInput);

        //create the VBox, Label and Input for Weight
        let oWeightVBox = new sap.m.VBox();
        let oWeightLabel = this._createLabel("Weight", false, "Weight");
        let oWeightInput = this._createStepInput("Weight", "attributeData");

        //add the label and input of Weight to the corresponding VBox
        oWeightVBox.addItem(oWeightLabel);
        oWeightVBox.addItem(oWeightInput);

        //create the VBox, Label and Input for Fuzziness
        let oFuzzinessVBox = new sap.m.VBox();
        let oFuzzinessLabel = this._createLabel("Fuzziness", false, "Fuzziness");
        let oFuzzinessInput = this._createStepInput("Fuzziness", "attributeData");

        //add the label and input of Fuzziness to the corresponding VBox
        oFuzzinessVBox.addItem(oFuzzinessLabel);
        oFuzzinessVBox.addItem(oFuzzinessInput);

        //create the VBox, Label and Switch for Empty Matches switch
        let oEmptyMatchesVBox = new sap.m.VBox();
        let oEmptyMatchesLabel = this._createLabel("EmptyMtcNull", false, "Empty Matches Null");
        let oEmptyMatchesSwitch = this._createSwitch("EmptyMtcNull", "attributeData");

        //add the label and Switch of Empty Matches to the corresponding VBox
        oEmptyMatchesVBox.addItem(oEmptyMatchesLabel);
        oEmptyMatchesVBox.addItem(oEmptyMatchesSwitch);

        //create the VBox, Label and Input for Score
        let oScoreVBox = new sap.m.VBox();
        let oScoreLabel = this._createLabel("EmptyScore", false, "Score, if empty");
        let oScoreInput = this._createStepInput("EmptyScore", "attributeData");

        //add the label and input of Score to the corresponding VBox
        oScoreVBox.addItem(oScoreLabel);
        oScoreVBox.addItem(oScoreInput);

        //create the VBox, Label and Input for Spellcheck Factor
        let oSpellcheckVBox = new sap.m.VBox();
        let oSpellcheckLabel = this._createLabel("ScFactor", false, "Spellcheck Factor");
        let oSpellcheckInput = this._createStepInput("ScFactor", "attributeData");

        //add the label and input of Spellcheck Factor to the corresponding VBox
        oSpellcheckVBox.addItem(oSpellcheckLabel);
        oSpellcheckVBox.addItem(oSpellcheckInput);

        //create the VBox, Label and Select for similar calculation mode
        let oSimilarCalculationVBox = new sap.m.VBox();
        let oSimilarCalculationLabel = this._createLabel("SimCalcMode", false, "Similar Calculation Mode");
        let arrSimilarCalculationOptions = [
            {
                key: "SEARCH",
                text: "Search"
            }, 
            {
                key: "COMPARE",
                text: "Compare"
            },
            {
                key: "SYMMETRICSEARCH",
                text: "Symmetric Search"
            },
            {
                key: "SUBSTRINGSEARCH",
                text: "Substring Search"
            }];
        let oSimilarCalculationSelect = this._createSelect(oController, "SimCalcMode", "attributeData", arrSimilarCalculationOptions);

        //add the label and Select of Spellcheck Factor to the corresponding VBox
        oSimilarCalculationVBox.addItem(oSimilarCalculationLabel);
        oSimilarCalculationVBox.addItem(oSimilarCalculationSelect);

        //add the VBoxes to the MTCFormView VBox
        oMTCFormView.addItem(oTableNameVBox);
        oMTCFormView.addItem(oFieldNameVBox);
        oMTCFormView.addItem(oWeightVBox);
        oMTCFormView.addItem(oFuzzinessVBox);
        oMTCFormView.addItem(oEmptyMatchesVBox);
        oMTCFormView.addItem(oScoreVBox);
        oMTCFormView.addItem(oSpellcheckVBox);
        oMTCFormView.addItem(oSimilarCalculationVBox);
    };

    oMDCConfigurationMTC._createConditionView = function(oController){
        let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");

        //create the VBox, Label and Select for Condition
        let oConditionVBox = new sap.m.VBox();
        let oConditionLabel = this._createLabel("Condition", false, "Condition");
        let arrConditionOptions = [
            {
                key: "ifMissing",
                text: "If Missing"
            }, 
            {
                // Bug 13448: change "ifEqual" to "ifEquals"
                key: "ifEquals",
                text: "If Equal"
            },
            {
                // Bug 13163 - change it from "ifNotEqual" to "ifNotEquals"
                key: "ifNotEquals",
                text: "If Not Equal"
            }];
        let oConditionSelect = this._createSelect(oController, "Condition", "conditionData", arrConditionOptions);

        //add the label and Select of Condition to the corresponding VBox
        oConditionVBox.addItem(oConditionLabel);
        oConditionVBox.addItem(oConditionSelect);

        //create the VBox, Label and Input for Value
        let oValueVBox = new sap.m.VBox();
        let oValueLabel = this._createLabel("Value", false, "Value");
        let oValueInput = this._createInput("Value", "Add Value", "conditionData");

        //add the label and input of Value to the corresponding VBox
        oValueVBox.addItem(oValueLabel);
        oValueVBox.addItem(oValueInput);

        //create the VBox, Label and Select for Action
        let oActionVBox = new sap.m.VBox();
        let oActionLabel = this._createLabel("Action", false, "Action");
        let arrActionOption = [
            {
                key: "SKIPCOLUMN",
                text: "Skip Column"
            }, {
                key: "SKIPRULE",
                text: "Skip Rule"
            }, {
                key: "REPLACE",
                text: "Replace"
            },
        ];
        let oActionSelect = this._createSelect(oController, "Action", "conditionData", arrActionOption);

        //add the label and input of Action to the corresponding VBox
        oActionVBox.addItem(oActionLabel);
        oActionVBox.addItem(oActionSelect);

        //create the VBox, Label and Input for Replace
        let oReplaceVBox = new sap.m.VBox();
        let oReplaceLabel = this._createLabel("ReplaceBy", false, "Replace with");
        let oReplaceInput = this._createInput("ReplaceBy", "Add Replace", "conditionData");

        //add the label and input of Replace to the corresponding VBox
        oReplaceVBox.addItem(oReplaceLabel);
        oReplaceVBox.addItem(oReplaceInput);

        //add the VBoxes to the MTCFormView VBox
        oMTCFormView.addItem(oConditionVBox);
        oMTCFormView.addItem(oValueVBox);
        oMTCFormView.addItem(oActionVBox);
        oMTCFormView.addItem(oReplaceVBox);
    };
    
    /**
    Create Label parameters:
    * @param {String} sLabelFor : ID of the control that is going to give reference
    * @param {Boolean} bRequired : if the following control is required 
    * @param {String} sText : the text of the label
    * @returns
    */
    oMDCConfigurationMTC._createLabel = function(sLabelFor, bRequired, sText){
        let oLabel = new sap.m.Label({
            labelFor: sLabelFor,
            required: bRequired,
            text: sText
        }).addStyleClass("sapUiSmallMarginTop"); 

        return oLabel;
    };

    /**
    Create input parameters:
    * @param {String} sId : ID of the input
    * @param {String} sPlaceholder : placeholder of the input
    * @param {String} sPath : the variable name on the model where is going to be stored
    * @param {Boolean} bRequired : if it is required then an event is going to be added.
    * @param {String} sType : the type of the input, if number an event is going to be added.
    * @param {Boolean} bNonEditable : after save, it will be disabled
    * @returns
    */
    oMDCConfigurationMTC._createInput = function(sId, sPlaceholder, sPath, bRequired = false, bNonEditable = false){
        let oInput = new sap.m.Input({
            id: sId,
            placeholder: sPlaceholder,
            value: "{mdconsolidationModel>/configData/" + sPath + "/" + sId + "}",
        });

        if(bRequired){
            oInput.applySettings({
                valueState: "{mdconsolidationModel>/configData/" + sPath + "/" + sId + "ValueState}",
                valueStateText: "{mdconsolidationModel>/configData/" + sPath + "/" + sId + "ValueStateText}" 
            });

            let oValueStateFunctionData = {
                sId: sId,
                sPath: sPath,
                bRequired: bRequired,
                bIsRule: true
            };
            
            oInput.attachLiveChange(oValueStateFunctionData, this.onRuleNameLiveChange, this);
        } else {
            oInput.attachChange(this.onInputLiveChange, this);
        }

        if(bNonEditable) {
            oInput.applySettings({
                enabled: "{= ${mdconsolidationModel>/configData/" + sPath + "/RowStatus} === 'Success'}"
            });
        }

        return oInput;
    };

    /**
    Create Step Input parameters:
    * @param {String} sId : ID of the Switch
    * @param {String} sPath : the variable name on the model where is going to be stored
    * @returns
    */
    oMDCConfigurationMTC._createStepInput = function(sId, sPath){
        let oStepInput = new sap.m.StepInput({
            name: sId,
            id: sId,
            max: 1.00,
            min: 0.00,
            step: 0.01,
            displayValuePrecision: 2,
            value: "{mdconsolidationModel>/configData/" + sPath + "/" + sId + "}",
            valueState: "{mdconsolidationModel>/configData/" + sPath + "/" + sId + "ValueState}",
            valueStateText: "{mdconsolidationModel>/configData/" + sPath + "/" + sId + "ValueStateText}",
        });

        oStepInput.attachChange(this.onStepInputChange, this);
        
        return oStepInput;
    };

    /**
    Create Switch parameters:
    * @param {String} sId : ID of the Switch
    * @param {String} sPath : the variable name on the model where is going to be stored
    * @returns
    */
    oMDCConfigurationMTC._createSwitch = function(sId, sPath){
        let oSwitch = new sap.m.Switch({
            id: sId,
            state: "{mdconsolidationModel>/configData/" + sPath + "/" + sId + "}",
        });

        oSwitch.attachChange(this.onSwitchChange, this);

        return oSwitch;
    };

    /**
    Create Select parameters:
    * @param {String} sId : ID of the Switch
    * @param {String} sPath : the variable name on the model where is going to be stored
    * @param {Array} arrItems : array of the items of the selection, must be an object with 2 properties: key and text.
    * @returns
    */
    oMDCConfigurationMTC._createSelect = function(oController, sId, sPath, arrItems = [], bRequired){
        let oSelect = new sap.m.Select({
            id: sId,
            editable: true,
            selectedKey: "{mdconsolidationModel>/configData/" + sPath + "/" + sId + "}",
        });
        
        
        if(sId.includes("Table")||sId.includes("Field")){
            
            oSelect.applySettings({
                forceSelection: false,
            });

            let oItemTemplate = new sap.ui.core.Item({
                key: "{mdconsolidationModel>key}",
                text: "{mdconsolidationModel>text}"
            });

            if(sId.includes("Table")){
                oSelect.attachChange(sPath, this.onChangeMTCTable, oController);
                oSelect.attachLiveChange(sPath, this.onChangeMTCTable, oController);
            // bug 134484 - added new variable to table name
                oSelect.applySettings({
                    enabled: "{mdconsolidationModel>/configData/attributeData/EbTableName}"
                });

                oItemTemplate = new sap.ui.core.Item({
                    key: "{mdconsolidationModel>key}",
                    text: "{mdconsolidationModel>text}"
                });
                
                oSelect.bindItems({
                    path: "/configData/TableData",
                    model: "mdconsolidationModel",
			        template: oItemTemplate,
			        templateShareable: false,
                });

            } else if(sId.includes("Field")){
                oSelect.attachChange(sPath, this.onChangeMTCField, this);
                oSelect.attachLiveChange(sPath, this.onChangeMTCField, this);

                oSelect.applySettings({
                    enabled: "{mdconsolidationModel>/configData/attributeData/EbFieldName}"
                });

                oSelect.bindItems({
                    path: "/configData/attributeData/FieldData",
                    model: "mdconsolidationModel",
			        template: oItemTemplate,
			        templateShareable: false,
                });
            }

        } else {
            arrItems.forEach((el) => {
                oSelect.addItem(new sap.ui.core.Item(el));
            });

            oSelect.applySettings({
                forceSelection : true
            });

            oSelect.attachChange(this.onSelectChange, this);
            oSelect.attachLiveChange(this.onSelectChange, this);
        }

        if(bRequired){
            oSelect.applySettings({
                valueState: "{mdconsolidationModel>/configData/" + sPath + "/" + sId + "ValueState}",
                valueStateText: "{mdconsolidationModel>/configData/" + sPath + "/" + sId + "ValueStateText}" 
            });
        }

        return oSelect;
    };

    oMDCConfigurationMTC.onInputLiveChange = function(oEvent){
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");
        let oTreeTable = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--ruleTreeTable");
        let iSelectedIndex = oTreeTable.getSelectedIndex();
        let sPath = oTreeTable.getContextByIndex(iSelectedIndex).getPath();

        this._UpdateRowOnChange(oMDConsolidationModel, sPath);
    };

    oMDCConfigurationMTC.onSelectChange = function(oEvent){
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");
        let oTreeTable = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--ruleTreeTable");
        let iSelectedIndex = oTreeTable.getSelectedIndex();
        let sPath = oTreeTable.getContextByIndex(iSelectedIndex).getPath();

        this._UpdateRowOnChange(oMDConsolidationModel, sPath);
    };

    oMDCConfigurationMTC.onSwitchChange = function(oEvent){
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");
        let oTreeTable = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--ruleTreeTable");
        let iSelectedIndex = oTreeTable.getSelectedIndex();
        let sPath = oTreeTable.getContextByIndex(iSelectedIndex).getPath();

        this._UpdateRowOnChange(oMDConsolidationModel, sPath);
    };

    oMDCConfigurationMTC.onStepInputChange = function(oEvent){
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");
        let oTreeTable = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--ruleTreeTable");
        let iSelectedIndex = oTreeTable.getSelectedIndex();
        let sPath = oTreeTable.getContextByIndex(iSelectedIndex).getPath();
        let sId = oEvent.getSource().getId();

        this._UpdateRowOnChange(oMDConsolidationModel, sPath);

        let nMinValue = oEvent.getSource().getProperty("min");
        let nMaxValue = oEvent.getSource().getProperty("max");
        let nValue = oEvent.getSource().getValue();

        if(nValue >= nMinValue && nValue <= nMaxValue){
            // Bug 13448: change path 
            oMDConsolidationModel.setProperty(sPath + "/" + sId + "ValueState", "None");
        } else {
            oMDConsolidationModel.setProperty(sPath + "/" + sId + "ValueState", "Error");
        }
        this._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationMTC.onRuleNameLiveChange = function(oEvent, oData){
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");
        let arrRules = oMDConsolidationModel.getProperty("/configData/children");
        let oTreeTable = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--ruleTreeTable");
        let iSelectedIndex = oTreeTable.getSelectedIndex();
        let sPath = oTreeTable.getContextByIndex(iSelectedIndex).getPath();
        let sValue = oEvent.getSource().getValue();

        oMDConsolidationModel.setProperty(sPath + "/RuleName", sValue);

        let bRepeatedValue = this._ValidateRepeatValue(arrRules, "RuleName", sValue);
        oData.bRepeatedValue = bRepeatedValue;

        this._ValueStateHandler(oEvent, oData);
    };

    oMDCConfigurationMTC.onChangeMTCTable = async function(oEvent){
        let oController = this;
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oSelectedTable = oEvent.getSource().getSelectedKey();
        let oTreeTable = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--ruleTreeTable");
        let iSelectedIndex = oTreeTable.getSelectedIndex();
        let sPath = oTreeTable.getContextByIndex(iSelectedIndex).getPath();
        let arrFieldData = [];
        let arrFieldList = [];

        oMDConsolidationModel.setProperty("/configData/attributeData/TableNameValueState", "None");
        oMDConsolidationModel.setProperty("/configData/attributeData/TableNameValueState", "");
        
        try {
            arrFieldList = await GetListsData.getConfigurationBRCFieldsSelect(oController, oConfigData.BoType, oSelectedTable);
            oMDConsolidationModel.setProperty("/configData/attributeData/EbField", true);
            oMDConsolidationModel.setProperty("/configData/attributeData/FieldNameValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/attributeData/FieldNameValueStateText", "Field is mandatory");
        } catch (oError) {
            if (oError.statusCode !== "404") {
                Utilities.showPopupAlert(
                    "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                    MessageBox.Icon.ERROR, 
                    "Error"
                );
            }
        }
        
        // change the object 
        arrFieldList.forEach(element => {
            let oTemp = {
                key: element.Fieldname,
                text: element.Fieldname + " - " + element.Fieldtext
            };
            
            arrFieldData.push(oTemp);
        });
        
        oMDConsolidationModel.setProperty("/configData/attributeData/FieldName", "");
        oMDConsolidationModel.setProperty("/configData/attributeData/FieldData", arrFieldData);
        this.MDCConfigurationMTC._ValidateFragmentToSave(oMDConsolidationModel);
        this.MDCConfigurationMTC._UpdateRowOnChange(oMDConsolidationModel, sPath);

    };

    oMDCConfigurationMTC.onChangeMTCField = function(oEvent){
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");
        let oTreeTable = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--ruleTreeTable");
        let iSelectedIndex = oTreeTable.getSelectedIndex();
        let sPath = oTreeTable.getContextByIndex(iSelectedIndex).getPath();

        oMDConsolidationModel.setProperty("/configData/attributeData/FieldNameValueState", "None");
        // bug 134484 - field name state text should change to "" 
        oMDConsolidationModel.setProperty("/configData/attributeData/FieldNameValueStateText", "");
        this._ValidateFragmentToSave(oMDConsolidationModel);
        this._UpdateRowOnChange(oMDConsolidationModel, sPath);
    };

    oMDCConfigurationMTC._ValueStateHandler = function(oEvent, oData){
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");
        let sValue = oEvent.getSource().getValue();
        let sValueStateText = "";
        let sPath;

        // if the data is part of the treetable form use the path of the treetable
        if(oData.bIsRule){
            sPath = oData.sPath + "/" + oData.sId;
        } else {
            sPath = oData.sPath;
        }

        // 1-. first validate which value does the value state is going to have
        if(oData.bRequired && sValue === ""){
            sValueStateText = "Field is mandatory";

        } else if(oData.hasOwnProperty("bStartValidation") && oData.bStartValidation && sValue.substring(0, 1) !== "Z" && sValue.substring(0, 1) !== "Y"){
            sValueStateText = "Configuration ID must start with Z or Y";

        } else if(oData.hasOwnProperty("bRepeatedValue") && oData.bRepeatedValue){
            sValueStateText = "Name already exists";
        }

        //set the resulting value state depending on the value state text variable
        if(sValueStateText !== ""){
            oMDConsolidationModel.setProperty("/configData/" + sPath + "ValueState", "Error");
        } else {
            oMDConsolidationModel.setProperty("/configData/" + sPath + "ValueState", "None");
        }

        //set the resulting value state text
        oMDConsolidationModel.setProperty("/configData/" + sPath + "ValueStateText", sValueStateText);
        this._ValidateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationMTC._ValidateRepeatValue = function(arrData, sFieldName, sValue){
        let iCounter = 0;
        for (let i = 0; i < arrData.length; i++) {
            const element = arrData[i];
            
            if(element[sFieldName] === sValue){
                iCounter = iCounter + 1;
            }
        }

        return (iCounter > 1 ? true : false);
    };

    oMDCConfigurationMTC._UpdateRowOnChange = function(oMDConsolidationModel, sPath){
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oTreeTable = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--ruleTreeTable");
        let oRow = oMDConsolidationModel.getProperty(sPath);
        let iSelectedIndex = oTreeTable.getSelectedIndex();

        let iLevel = sPath.split("/children").length -1;
        let arrIndexes = sPath.split("/children/");
        arrIndexes = arrIndexes.map(Number);

        if(oConfigData.OperationType === "U" && oRow.RowStatus === "None"){

            oRow.RowStatus = "Warning";
            oConfigData.VisCancelRuleBtn = true;
            oConfigData.CancelRuleBtnText = "Cancel Edit";
            

            if (iLevel===1){
                oTreeTable.setSelectedIndex(iSelectedIndex);
            } else if (iLevel===2){
                oTreeTable.expand(arrIndexes[1]);
                oTreeTable.setSelectedIndex(iSelectedIndex);
            } else {
                oTreeTable.expand(arrIndexes[1]);
                oTreeTable.expand(arrIndexes[1] + arrIndexes[2] + 1);
                oTreeTable.setSelectedIndex(iSelectedIndex);
            }

            this._ValidateFragmentToSave(oMDConsolidationModel);
        }
    };

    oMDCConfigurationMTC._ValidateIfChangesWereMade = function(oMDConsolidationModel){
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let bChangesWereMade = false;

        bChangesWereMade = oConfigData.ConfigEdited;

        for (let i = 0; i < oConfigData.children.length; i++) {
            const oRuleElement = oConfigData.children[i];
            if (oRuleElement.RowStatus !== "None"){
                bChangesWereMade = true;
            }

            for (let j = 0; j < oRuleElement.children.length; j++) {
                const oAttributeElement = oRuleElement.children[j];
                if (oAttributeElement.RowStatus !== "None"){
                    bChangesWereMade = true;
                }

                for (let k = 0; k < oAttributeElement.children.length; k++) {
                    const oConditionElement = oAttributeElement.children[k];
                    if (oConditionElement.RowStatus !== "None"){
                        bChangesWereMade = true;
                    }
                }
            }
        }

        return bChangesWereMade;
    };

    oMDCConfigurationMTC._ValidateFragmentToSave = function(oMDConsolidationModel){
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let bErrorFound = false;
        
        
        let bChangesWereMade = this._ValidateIfChangesWereMade(oMDConsolidationModel);

        if(oConfigData.ConfigIdValueState === "Error" || oConfigData.DescriptionValueState === "Error" ){
            bErrorFound = true;
        }

        if(!bErrorFound){
            for (let i = 0; i < oConfigData.children.length; i++) {
                const oRuleElement = oConfigData.children[i];

                if(oRuleElement.RuleNameValueState === "Error" || oRuleElement.MinScoreValueState === "Error"){
                    bErrorFound = true;
                    break;
                }
    
                for (let j = 0; j < oRuleElement.children.length; j++) {
                    const oAttributeElement = oRuleElement.children[j];

                    if(oAttributeElement.EmptyScoreValueState === "Error" || oAttributeElement.FieldNameValueState === "Error" || oAttributeElement.FuzzinessValueState === "Error" || oAttributeElement.ScFactorValueState === "Error" ||
                        oAttributeElement.TableNameValueState === "Error" || oAttributeElement.WeightValueState === "Error"){
                        bErrorFound = true;
                        break;
                    }
                }
            }
        }

        if(bErrorFound || !bChangesWereMade){
            oConfigData.EbSaveBtn = false;
        } else{
            oConfigData.EbSaveBtn = true;    
        }
        oMDConsolidationModel.refresh();
    };

    oMDCConfigurationMTC.onClickDeleteRule = function() {
        let oController = this;
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let sText = oConfigData.PanelText;

        let iChangeStatus = oController.MDCConfigurationMTC.getUpdateStatus(oController, oConfigData);
        if(iChangeStatus !== 0){
            Utilities.showPopupAlert(
                "The configuration has been modified. Please save the configuration before performing a delete."
                , MessageBox.Icon.WARNING, "Cannot Delete.");
            return;
        }

        if(sText==="Rule"){
            this.MDCConfigurationMTC.deleteRule(oController, oMDConsolidationModel);
        } else if(sText==="Attribute"){
            this.MDCConfigurationMTC.deleteAttribute(oController, oMDConsolidationModel);
        } else {
            this.MDCConfigurationMTC.deleteCondition(oController, oMDConsolidationModel);
        }
    };

    oMDCConfigurationMTC._LastRuleNo = function(oController){
        let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let iMaxValue = 0 ;
        for (let i = 0; i < oConfigData.children.length; i++) {
            const oRuleElement = oConfigData.children[i];
            let sRuleNo = oRuleElement.RuleId.split("_")[1];
            let iRuleNo = Number(sRuleNo);

            if(iRuleNo > iMaxValue){
                iMaxValue = iRuleNo;
            }
        }

        return iMaxValue + 1;
    };

    oMDCConfigurationMTC._LastAttributeNo = function(oController){
        let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let iMaxValue = 0 ;
        for (let i = 0; i < oConfigData.children.length; i++) {
            const oRuleElement = oConfigData.children[i];

            for (let j = 0; j < oRuleElement.children.length; j++) {
                const oAttributeElement = oRuleElement.children[j];
                
                let sAttributeNo = oAttributeElement.AttributeId.split("_")[1];
                let iAttributeNo = Number(sAttributeNo);
    
                if(iAttributeNo > iMaxValue){
                    iMaxValue = iAttributeNo;
                }
            }
        }

        return iMaxValue + 1;
    };

    oMDCConfigurationMTC._LastConditionNo = function(oController){
        let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let iMaxValue = 0 ;
        for (let i = 0; i < oConfigData.children.length; i++) {
            const oRuleElement = oConfigData.children[i];

            for (let j = 0; j < oRuleElement.children.length; j++) {
                const oAttributeElement = oRuleElement.children[j];

                for (let k = 0; k < oAttributeElement.children.length; k++) {
                    const oConditionElement = oAttributeElement.children[k];
                    
                    let sConditionNo = oConditionElement.ConditionId.split("_")[1];
                    let iConditionNo = Number(sConditionNo);
        
                    if(iConditionNo > iMaxValue){
                        iMaxValue = iConditionNo;
                    }
                }
            }
        }

        return iMaxValue + 1;
    };

    oMDCConfigurationMTC._validateMinRequirements = function(oController){
        let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let sMessage = "";
        let oValidationResult = {
            result: "S",
            message: sMessage
        };

        if(oConfigData.children.length === 0){
            oValidationResult.result = "E";
            oValidationResult.message = "Error: Configuration '" + oConfigData.ConfigId + "' must have at least 1 Rule with 1 Attribute";
            return oValidationResult;
        }

        for (let i = 0; i < oConfigData.children.length; i++) {
            const oRuleElement = oConfigData.children[i];

            if(oRuleElement.children.length === 0){
                oValidationResult.result = "E";
                oValidationResult.message = "Error: Rule '" + oRuleElement.RuleName + "' must have at least 1 attribute";
                return oValidationResult;
            }
        }

        return oValidationResult;
    };

    oMDCConfigurationMTC._updateTreeTable = async function(oController, oMDConsolidationModel){
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let arrConfigDetails = [];
        let oConfigDetails = {};
        let arrRuleSet;

        try {
            arrConfigDetails = await GetListsData.getConfigurationMTCDetails(oController, oConfigData.ConfigId);

            if (arrConfigDetails && arrConfigDetails.length) {
                oConfigDetails = arrConfigDetails[0];
            }

        } catch (oError) {
            if (oError.statusCode !== "404") {
                Utilities.showPopupAlert(
                    "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                    MessageBox.Icon.ERROR, 
                    "Error"
                );
            }
        }

        arrRuleSet = oController._fuzzyRuleSetParse(oConfigDetails.FuzzyRuleSet.results);
        oConfigData.children = arrRuleSet;
        oConfigData.LastChanged = arrConfigDetails[0].LastChanged;
        oConfigData.eTag = oConfigDetails.__metadata.etag;
        oMDConsolidationModel.setProperty("/configData", oConfigData);
        return oConfigData;
    };
    

    /**
     * Check to see if there are any unsaved updates in the configuration.
     * return 
     *      0 = No Updates
     *      1 = Creation Process
     *      2 = Edited
     */
    oMDCConfigurationMTC.getUpdateStatus = function(oController, oConfigData) {
        // The configuration is being created, return 1
        if(oConfigData.OperationType === "C" ){
            return 1;
        }

        // Configuration description etc have been edited
        if(oConfigData.ConfigEdited) {
            return 1;
        }

        let iUpdateStatus = 0;
        // Traverse the tree to look for modifications 
        oConfigData.children.every(function(oRule){
            if(oRule.RowStatus !== "None"){
                iUpdateStatus = 2;
                return false;
            }

            oRule.children.every(function(oAttribute){
                if(oAttribute.RowStatus !== "None") {
                    iUpdateStatus = 2;
                    return false;
                }

                oAttribute.children.every(function(oCondition){
                    if(oCondition.RowStatus !== "None"){
                        iUpdateStatus = 2;
                        return false;
                    }
                    return true;
                });


                return (iUpdateStatus === 0);
            });

            return (iUpdateStatus === 0);
        });

        // No changes found
        return iUpdateStatus;
    };

    oMDCConfigurationMTC.saveConfiguration = function() {
        let oController = this;
        let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oDate = new Date();
        let sDate = oDate.getFullYear() + "-" + (oDate.getMonth()+1) + "-" + oDate.getDate() + "T" + oDate.getHours() + ":" + oDate.getMinutes() + ":" + oDate.getSeconds();
        
        let oValidationResult = this.MDCConfigurationMTC._validateMinRequirements(oController);
        if(oValidationResult.result === "E"){
            Utilities.showPopupAlert(oValidationResult.message, MessageBox.Icon.ERROR, "Error");
            return;
        }

        
        let arrPayload = [];
        let oConfigPayload = {};

        // if the operation Type is "C" (Create) which means is a New Configuration
        if(oConfigData.OperationType==="C"){

            oConfigPayload = {
                Configuration: oConfigData.ConfigId,
                Description: oConfigData.Description,
                OTC: oConfigData.BoType,
                MTCUsage: oConfigData.MTCUsage,
                UseClassification: oConfigData.UseClassification,
                DuplCheckDflt: oConfigData.DuplCheckDflt,
                ApprScore: String(oConfigData.ApprScore),
                ScoreSelection: oConfigData.ScoreSelection,
                LastChanged: sDate
            };

            arrPayload.push({
                endpoint: "/FuzzyConfigSet",
                method: "POST", 
                oPayload: oConfigPayload
            });

        // else if the configEdited is set to "True" which means is an updated configuration
        } else if(oConfigData.ConfigEdited){

            oConfigPayload = {
                Description: oConfigData.Description,
                OTC: oConfigData.BoType,
                MTCUsage: oConfigData.MTCUsage,
                UseClassification: oConfigData.UseClassification,
                DuplCheckDflt: oConfigData.DuplCheckDflt,
                ApprScore: String(oConfigData.ApprScore),
                ScoreSelection: oConfigData.ScoreSelection,
            };

            arrPayload.push({
                // Bug 13448 - remove the sap-client100
                endpoint: "/FuzzyConfigSet('"+ oConfigData.ConfigId +"')",
                method: "MERGE", 
                oPayload: oConfigPayload
            });
        }
        

        for (let i = 0; i < oConfigData.children.length; i++) {
            const oRuleElement = oConfigData.children[i];
            let oRulePayload = {};

            // if Rule Row Status is set to "Success" which means is a New Rule
            if(oRuleElement.RowStatus === "Success"){
                oRulePayload = {
                    Configuration: oConfigData.ConfigId,
                    RuleId: oRuleElement.RuleId,
                    RuleName: oRuleElement.RuleName,
                    SequenceNo: oRuleElement.SequenceNo,
                    MinScore: String(oRuleElement.MinScore),
                    LastChanged: (oConfigData.OperationType==="C" ? sDate : oConfigData.LastChanged),
                };

                arrPayload.push({
                    endpoint: "/FuzzyRuleSet",
                    method: "POST", 
                    oPayload: oRulePayload
                });

            // if Rule Row Status is set to "Warning" which means is an Edited Rule
            } else if(oRuleElement.RowStatus === "Warning"){
                oRulePayload = {
                    MinScore: String(oRuleElement.MinScore),
                };
                arrPayload.push({
                    // Bug 13448 - remove the sap-client100
                    endpoint: "/FuzzyRuleSet(Configuration='" + oConfigData.ConfigId + "',RuleId='" + oRuleElement.RuleId + "')",
                    method: "MERGE", 
                    oPayload: oRulePayload
                });
            }

            for (let j = 0; j < oRuleElement.children.length; j++) {
                const oAttributeElement = oRuleElement.children[j];
                let oAttributePayload = {};

                // if Attribute Row Status is set to "Success" which means is a New Attribute
                if(oAttributeElement.RowStatus === "Success"){
                    oAttributePayload = {
                        Configuration: oConfigData.ConfigId,
                        RuleId: oAttributeElement.RuleId,
                        AttributeId: oAttributeElement.AttributeId,
                        SequenceNo: oAttributeElement.SequenceNo,
                        TableName: oAttributeElement.TableName,
                        FieldName: oAttributeElement.FieldName,
                        Fuzziness: String(oAttributeElement.Fuzziness),
                        Weight: String(oAttributeElement.Weight),
                        EmptyMtcNull: oAttributeElement.EmptyMtcNull,
                        EmptyScore: String(oAttributeElement.EmptyScore),
                        ScFactor: String(oAttributeElement.ScFactor),
                        SimCalcMode: oAttributeElement.SimCalcMode,
                        LastChanged: (oConfigData.OperationType==="C" ? sDate : oConfigData.LastChanged),
                    };

                    arrPayload.push({
                        endpoint: "/FuzzyAttributeSet",
                        method: "POST", 
                        oPayload: oAttributePayload
                    });

                // if Attribute Row Status is set to "Warning" which means is an Edited Attribute
                } else if(oAttributeElement.RowStatus === "Warning"){
                    oAttributePayload = {
                        Fuzziness: String(oAttributeElement.Fuzziness),
                        Weight: String(oAttributeElement.Weight),
                        EmptyMtcNull: oAttributeElement.EmptyMtcNull,
                        EmptyScore: String(oAttributeElement.EmptyScore),
                        ScFactor: String(oAttributeElement.ScFactor),
                        SimCalcMode: oAttributeElement.SimCalcMode,
                    };
                    arrPayload.push({
                        // Bug 13448 - remove the sap-client100
                        endpoint: "/FuzzyAttributeSet(Configuration='" + oConfigData.ConfigId + "',RuleId='" + oAttributeElement.RuleId + "',AttributeId='" + oAttributeElement.AttributeId + "')",
                        method: "MERGE", 
                        oPayload: oAttributePayload
                    });
                }
                

                for (let k = 0; k < oAttributeElement.children.length; k++) {
                    const oConditionElement = oAttributeElement.children[k];
                    let oConditionPayload = {};

                    if(oConditionElement.RowStatus === "Success"){
                        oConditionPayload = {
                            Configuration: oConditionElement.Configuration,
                            RuleId: oConditionElement.RuleId,
                            AttributeId: oConditionElement.AttributeId,
                            ConditionId: oConditionElement.ConditionId,
                            SequenceNo: oConditionElement.SequenceNo,
                            Condition: oConditionElement.Condition,
                            Action: oConditionElement.Action,
                            Value: oConditionElement.Value,
                            ReplaceBy: oConditionElement.ReplaceBy,
                            LastChanged: (oConfigData.OperationType==="C" ? sDate : oConfigData.LastChanged),
                        };

                        arrPayload.push({
                            endpoint:"/FuzzyConditionSet",
                            method: "POST", 
                            oPayload: oConditionPayload
                        });
                    } else if(oConditionElement.RowStatus === "Warning"){
                        oConditionPayload = {
                            Condition: oConditionElement.Condition,
                            Action: oConditionElement.Action,
                            Value: oConditionElement.Value,
                            ReplaceBy: oConditionElement.ReplaceBy,
                        };

                        arrPayload.push({
                            // Bug 13448 - remove the sap-client100
                            endpoint: "/FuzzyConditionSet(Configuration='" + oConfigData.ConfigId + "',RuleId='" + oConditionElement.RuleId + "',AttributeId='" + oConditionElement.AttributeId + "',ConditionId='" + oConditionElement.ConditionId +"')",
                            method: "MERGE", 
                            oPayload: oConditionPayload
                        });
                    }
                }
            }
        }


        let promiseTransport = oController._getSelectedTransportPackage(true, false, false);
        promiseTransport.then(async oSelectedTransport => {
            sap.ui.core.BusyIndicator.show(1);
            
            let url = oController.getOwnerComponent().getModel("MDC_CUSTMIZING").sServiceUrl;
            let oDataService = new sap.ui.model.odata.v2.ODataModel(url);
            oDataService.setDeferredGroups(["editGroup", "createGroup"]);

            oDataService.setUseBatch(true);
            let bUpdateSubmit = false;
            let bCreateSubmit = false;

            for (let i = 0; i < arrPayload.length; i++) {
                const oPayloadElement = arrPayload[i];

                if(oPayloadElement.method === "POST"){
                    oDataService.create(oPayloadElement.endpoint, oPayloadElement.oPayload, {groupId: "createGroup"});
                    bCreateSubmit = true;
                } 
            }

            let promise = Promise.resolve();
            if(bCreateSubmit){
                promise = oController.MDCConfigurationMTC.postData(oController, oMDConsolidationModel, oConfigData, oDataService, oSelectedTransport.customizingTransport);
            }

            promise.then(function(){
                for (let i = 0; i < arrPayload.length; i++) {
                    const oPayloadElement = arrPayload[i];
    
                    if (oPayloadElement.method === "MERGE") {
                        oDataService.update(oPayloadElement.endpoint, oPayloadElement.oPayload, {groupId: "editGroup", eTag: oConfigData.eTag});
                        bUpdateSubmit = true;
                    }
                }

                let promiseUpdate = Promise.resolve();
                if(bUpdateSubmit){
                    promiseUpdate = oController.MDCConfigurationMTC.updateData(oController, oMDConsolidationModel, oConfigData, oDataService, oSelectedTransport.customizingTransport);
                } 

                return promiseUpdate;
            })

            .finally(function(){
                sap.ui.core.BusyIndicator.hide();
            });
        });
    };

    oMDCConfigurationMTC.postData = function(oController, oMDConsolidationModel, oConfigData, oDataService, sSelectedTransport){
        let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");
        let oMDCConfigurationMTCFragment = oController._getMDCConfigurationFragment("MTC");
        let pCreatePromise = new Promise((resolve, reject) => {

            oDataService.setHeaders({
                "sap-transportrequest":sSelectedTransport,
            });
            oDataService.submitChanges( {groupId: "createGroup", success: async (oData, oResponse)=>{
                if (!oResponse.body.includes("Error") && oConfigData.OperationType==="C") {
                    Utilities.showPopupAlert("Configuration created succesfully", MessageBox.Icon.SUCCESS, "Success");

                    let arrConfigList = [];
                    let promiseConfigurationList = GetListsData.getConfigurationMTCDetails(oController);
    
                    promiseConfigurationList.then(configData => {
                        let arrFilteredData = configData.filter(element => {
                            return element.OTC === oConfigData.BoType;
                        }) ;
                        arrConfigList.push(oController._getCreateConfigurationObject());
                        arrConfigList.push(oController._getEmptyConfigurationObject());
                        arrFilteredData.forEach(oConfiguration => {
                            arrConfigList.push({
                                Zkey: "",
                                Id: oConfiguration.Configuration,
                                Description: oConfiguration.Description
                            });
                        });
    
                        oMDConsolidationModel.setProperty(oConfigData.SelectedRow + "/arrConfigList", arrConfigList);
                        oMDConsolidationModel.setProperty(oConfigData.SelectedRow + "/ConfigId", oConfigData.ConfigId);
                        
                        oConfigData.ConfigEdited = false;
                        oConfigData.OperationType = "U";
                        oMDConsolidationModel.setProperty("/configData", oConfigData);
                        oMDConsolidationModel.setProperty("/tempConfigData", {});
                        
                        oController._refreshTemplateDropDownList(oController);
        
                        oMDCConfigurationMTCFragment.close();
                        resolve();
                    });
                    promiseConfigurationList.catch(oError => {
                        sap.ui.core.BusyIndicator.hide();
                        let message = jQuery.parseJSON(oError.responseText).error.message.value;
                        Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");
                        reject();
                    });

                } else if(!oResponse.body.includes("Error")  && oConfigData.OperationType==="U"){
                    Utilities.showPopupAlert("Rules succesfully created", MessageBox.Icon.SUCCESS, "Success");
                    oConfigData = await oController.MDCConfigurationMTC._updateTreeTable(oController, oMDConsolidationModel);
                    oConfigData.ConfigEdited = false;
                    oConfigData.EbSaveBtn = false;
                    oConfigData.VisCancelRuleBtn = false;
                    oConfigData.VisDeleteRuleBtn = false;
                    oConfigData.PanelText = "Form";
                    // Bug 13448 - change the bIsCreating to false
                    oConfigData.bIsCreating = false;
                    try {
                        let arrConfigDetails = await GetListsData.getConfigurationMTCDetails(oController, oConfigData.ConfigId);
                        let oConfigDetails;
                        if (arrConfigDetails && arrConfigDetails.length) {
                            oConfigDetails = arrConfigDetails[0];
                        }
                        
                        oConfigData.eTag = oConfigDetails.__metadata.etag;
                    } catch (oError) {
                        if (oError.statusCode !== "404") {
                            Utilities.showPopupAlert(
                                "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                                MessageBox.Icon.ERROR, 
                                "Error"
                            );
                            reject();
                        }
                    }
                    let tempConfigData = structuredClone(oConfigData);
                    oMDConsolidationModel.setProperty("/tempConfigData", tempConfigData);
                    oMDConsolidationModel.setProperty("/configData", oConfigData);
                    oMTCFormView.destroyItems();
                    oMDConsolidationModel.refresh();

                    resolve();
                } else {
                    if(oResponse.hasOwnProperty("data")){
                        // Bug 13448: updated the error message
                        let oResponseJSON = JSON.parse(oResponse.data.__batchResponses[0].response.body);
                        let sErrorMsg = oResponseJSON.error.message.value;
                        Utilities.showPopupAlert("Error: " + sErrorMsg, MessageBox.Icon.ERROR, "Error");
                    } else{
                        Utilities.showPopupAlert("Error: " + oResponse.message, MessageBox.Icon.ERROR, "Error");
                    }
                    reject();
                }

            }, error: (oResponse)=> {
                if(oResponse.hasOwnProperty("data")){
                    // Bug 13448: updated the error message
                    let oResponseJSON = JSON.parse(oResponse.data.__batchResponses[0].response.body);
                    let sErrorMsg = oResponseJSON.error.message.value;
                    Utilities.showPopupAlert("Error: " + sErrorMsg, MessageBox.Icon.ERROR, "Error");
                } else{
                    Utilities.showPopupAlert("Error: " + oResponse.message, MessageBox.Icon.ERROR, "Error");
                }
                    reject();
            }});
        });

        return pCreatePromise;
    };

    oMDCConfigurationMTC.updateData = function (oController, oMDConsolidationModel, oConfigData, oDataService, sSelectedTransport){
        let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");

        let pUpdatePromise = new Promise((resolve, reject) => {

            oDataService.setHeaders({
                "sap-transportrequest":sSelectedTransport,
                "If-Match": oConfigData.eTag
            });

            oDataService.submitChanges( {groupId: "editGroup", success: async (oData, oResponse)=>{
                sap.ui.core.BusyIndicator.hide();
                if (!oResponse.body.includes("Error")) {
                    Utilities.showPopupAlert("Changes saved succesfully", MessageBox.Icon.SUCCESS, "Success");
                    oConfigData = await oController.MDCConfigurationMTC._updateTreeTable(oController, oMDConsolidationModel);
                    oConfigData.ConfigEdited = false;
                    oConfigData.EbSaveBtn = false;
                    oConfigData.VisCancelRuleBtn = false;
                    oConfigData.VisDeleteRuleBtn = false;
                    oConfigData.VisCancelConf = false;
                    oConfigData.PanelText = "Form";
                    try {
                        let arrConfigDetails = await GetListsData.getConfigurationMTCDetails(oController, oConfigData.ConfigId);
                        let oConfigDetails;
                        if (arrConfigDetails && arrConfigDetails.length) {
                            oConfigDetails = arrConfigDetails[0];
                        }
                        
                        oConfigData.eTag = oConfigDetails.__metadata.etag;
                    } catch (oError) {
                        if (oError.statusCode !== "404") {
                            Utilities.showPopupAlert(
                                "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                                MessageBox.Icon.ERROR, 
                                "Error"
                            );
                            reject();
                        }
                    }
                    let tempConfigData = structuredClone(oConfigData);
                    oMDConsolidationModel.setProperty("/tempConfigData", tempConfigData);
                    oMDConsolidationModel.setProperty("/configData", oConfigData);

                    oMTCFormView.destroyItems();
                    oMDConsolidationModel.refresh();
                    resolve();
                } else {
                    if(oResponse.hasOwnProperty("data")){
                    // Bug 13448: updated the error message
                    let oResponseJSON = JSON.parse(oResponse.data.__batchResponses[0].response.body);
                    let sErrorMsg = oResponseJSON.error.message.value;
                    Utilities.showPopupAlert("Error: " + sErrorMsg, MessageBox.Icon.ERROR, "Error");
                    } else{
                        Utilities.showPopupAlert("Error: " + oResponse.message, MessageBox.Icon.ERROR, "Error");
                    }
                    reject();
                }

            }, error: (oResponse)=> {
                    if(oResponse.hasOwnProperty("data")){
                    // Bug 13448: updated the error message
                    let oResponseJSON = JSON.parse(oResponse.data.__batchResponses[0].response.body);
                    let sErrorMsg = oResponseJSON.error.message.value;
                    Utilities.showPopupAlert("Error: " + sErrorMsg, MessageBox.Icon.ERROR, "Error");
                    } else{
                        Utilities.showPopupAlert("Error: " + oResponse.message, MessageBox.Icon.ERROR, "Error");
                    }
                    reject();
            }});
        });
        return pUpdatePromise;

    };

    oMDCConfigurationMTC.deleteConfiguration = function(){
        let oController = this;
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let promiseTransport = oController._getSelectedTransportPackage(true, false, false);

        promiseTransport.then(oSelectedTransport => {
            sap.ui.core.BusyIndicator.show(300);

            let oHeaders = {
                "If-Match": oConfigData.eTag,
                "sap-transportrequest": oSelectedTransport.customizingTransport
            };

            (new DMRDataService(
                oController,
                "MDC_CUSTMIZING",
                "/FuzzyConfigSet(Configuration='" + oConfigData.ConfigId + "')",
                "Delete Configuration",
                "/", // Root of the received data
                "Delete Configuration"
            )).deleteData(
                false,
                null,
                null, {
                    success: {
                        fCallback: function (oDataResult, oResponse) {
                            sap.ui.core.BusyIndicator.hide();

                            let arrConfigList = [];
                            let promiseConfigurationList = GetListsData.getConfigurationMTCDetails(oController);
                            promiseConfigurationList.then(oData => {
                                let arrFilteredData = oData.filter(element => {
                                    return element.OTC === oConfigData.BoType;
                                }) ;
                                arrConfigList.push(oController._getCreateConfigurationObject());
                                arrConfigList.push(oController._getEmptyConfigurationObject());
                                arrFilteredData.forEach(oConfiguration => {
                                    arrConfigList.push({
                                        Zkey: "",
                                        Id: oConfiguration.Configuration,
                                        Description: oConfiguration.Description
                                    });
                                });

                                oMDConsolidationModel.setProperty(oConfigData.SelectedRow + "/arrConfigList", arrConfigList);
                                oMDConsolidationModel.setProperty(oConfigData.SelectedRow + "/ConfigId", "");
                                oMDConsolidationModel.setProperty("/configData", {});

                                oController._refreshTemplateDropDownList(oController);

                                if (oResponse.statusText.includes("Successful")) {
                                    Utilities.showPopupAlert(oResponse.statusText, MessageBox.Icon.SUCCESS, "Success");
                                }

                                let oMDCConfigurationMTCFragment = oController._getMDCConfigurationFragment("MTC");
                                oMDCConfigurationMTCFragment.close();
                            });
                            promiseConfigurationList.catch(oError => {
                                sap.ui.core.BusyIndicator.hide();
                                let message = jQuery.parseJSON(oError.responseText).error.message.value;
                                Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");
                            });
                        },
                        oParam: this
                    },
                    error: function (oError) {
                        sap.ui.core.BusyIndicator.hide();
                        let message = jQuery.parseJSON(oError.responseText).error.message.value;
                        Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");                        
                    }
                }, "", oHeaders
            );
        });
    };

    oMDCConfigurationMTC.deleteRule = function(oController, oMDConsolidationModel){
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oRuleData = oMDConsolidationModel.getProperty("/configData/ruleData");
        let promiseTransport = oController._getSelectedTransportPackage(true, false, false);
        let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");
        
        promiseTransport.then(oSelectedTransport => {
            sap.ui.core.BusyIndicator.show(300);

            let oHeaders = {
                "If-Match": oRuleData.eTag,
                "sap-transportrequest": oSelectedTransport.customizingTransport
            };

            (new DMRDataService(
                oController,
                "MDC_CUSTMIZING",
                "/FuzzyRuleSet(Configuration='" + oConfigData.ConfigId + "',RuleId='" + oRuleData.RuleId + "')",
                "Delete Rule",
                "/", // Root of the received data
                "Delete Rule"
            )).deleteData(
                false,
                null,
                null, {
                    success: {
                        fCallback: async function (oDataResult, oResponse) {
                            sap.ui.core.BusyIndicator.hide();
                            
                            if (oResponse.statusText.includes("Successful")) {
                                Utilities.showPopupAlert(oResponse.statusText, MessageBox.Icon.SUCCESS, "Success");
                                oConfigData = await oController.MDCConfigurationMTC._updateTreeTable(oController, oMDConsolidationModel);
                                oConfigData.EbSaveBtn = false;
                                oConfigData.VisCancelRuleBtn = false;
                                oConfigData.VisDeleteRuleBtn = false;
                                oConfigData.PanelText = "Form";
                                oMDConsolidationModel.setProperty("/configData", oConfigData);
                                oMTCFormView.destroyItems();
                                oMDConsolidationModel.refresh();
                            }
                        },
                        oParam: this
                    },
                    error: function (oError) {
                        sap.ui.core.BusyIndicator.hide();
                        let message = jQuery.parseJSON(oError.responseText).error.message.value;
                        Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");                        
                    }
                }, "", oHeaders
            );
        });
    };

    oMDCConfigurationMTC.deleteAttribute = function(oController, oMDConsolidationModel){
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");
        let oAttributeData = oMDConsolidationModel.getProperty("/configData/attributeData");
        let promiseTransport = oController._getSelectedTransportPackage(true, false, false);

        promiseTransport.then(oSelectedTransport => {
            sap.ui.core.BusyIndicator.show(300);

            let oHeaders = {
                "If-Match": oAttributeData.eTag,
                "sap-transportrequest": oSelectedTransport.customizingTransport
            };

            (new DMRDataService(
                oController,
                "MDC_CUSTMIZING",
                "/FuzzyAttributeSet(Configuration='" + oAttributeData.Configuration + "',RuleId='" + oAttributeData.RuleId + "',AttributeId='"+ oAttributeData.AttributeId +"')",
                "Delete Attribute",
                "/", // Root of the received data
                "Delete Attribute"
            )).deleteData(
                false,
                null,
                null, {
                    success: {
                        fCallback: async function (oDataResult, oResponse) {
                            sap.ui.core.BusyIndicator.hide();

                            if (oResponse.statusText.includes("Successful")) {
                                Utilities.showPopupAlert(oResponse.statusText, MessageBox.Icon.SUCCESS, "Success");
                                oConfigData = await oController.MDCConfigurationMTC._updateTreeTable(oController, oMDConsolidationModel);
                                oConfigData.EbSaveBtn = false;
                                oConfigData.VisCancelRuleBtn = false;
                                oConfigData.VisDeleteRuleBtn = false;
                                oConfigData.PanelText = "Form";

                                oMDConsolidationModel.setProperty("/configData", oConfigData);
                                oMTCFormView.destroyItems();
                                oMDConsolidationModel.refresh();
                            }
                        },
                        oParam: this
                    },
                    error: function (oError) {
                        sap.ui.core.BusyIndicator.hide();
                        let message = jQuery.parseJSON(oError.responseText).error.message.value;
                        Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");                        
                    }
                }, "", oHeaders
            );
        });
    };

    oMDCConfigurationMTC.deleteCondition = function(oController, oMDConsolidationModel){
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oMTCFormView = sap.ui.getCore().byId("MDCConfigurationMTCFragmentID--MTCFormView");
        let oConditionData = oMDConsolidationModel.getProperty("/configData/conditionData");

        let promiseTransport = oController._getSelectedTransportPackage(true, false, false);
        promiseTransport.then(oSelectedTransport => {
            sap.ui.core.BusyIndicator.show(300);

            let oHeaders = {
                "If-Match": oConditionData.eTag,
                "sap-transportrequest": oSelectedTransport.customizingTransport
            };

            (new DMRDataService(
                oController,
                "MDC_CUSTMIZING",
                "/FuzzyConditionSet(Configuration='" + oConditionData.Configuration + "',RuleId='" + oConditionData.RuleId + "',AttributeId='"+ oConditionData.AttributeId +"',ConditionId='"+ oConditionData.ConditionId +"')",
                "Delete Rule",
                "/", // Root of the received data
                "Delete Rule"
            )).deleteData(
                false,
                null,
                null, {
                    success: {
                        fCallback: async function (oDataResult, oResponse) {
                            sap.ui.core.BusyIndicator.hide();

                            if (oResponse.statusText.includes("Successful")) {
                                Utilities.showPopupAlert(oResponse.statusText, MessageBox.Icon.SUCCESS, "Success");
                                oConfigData = await oController.MDCConfigurationMTC._updateTreeTable(oController, oMDConsolidationModel);
                                oConfigData.EbSaveBtn = false;
                                oConfigData.VisCancelRuleBtn = false;
                                oConfigData.VisDeleteRuleBtn = false;
                                oConfigData.PanelText = "Form";
                                oMDConsolidationModel.setProperty("/configData", oConfigData);
                                oMTCFormView.destroyItems();
                                oMDConsolidationModel.refresh();
                            }
                        },
                        oParam: this
                    },
                    error: function (oError) {
                        sap.ui.core.BusyIndicator.hide();
                        let message = jQuery.parseJSON(oError.responseText).error.message.value;
                        Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");                        
                    }
                }, "", oHeaders
            );
        });
    };

    return oMDCConfigurationMTC;
});