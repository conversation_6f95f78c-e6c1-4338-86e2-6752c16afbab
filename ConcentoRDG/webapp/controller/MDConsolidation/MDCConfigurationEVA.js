// Task 12351 - Display/Edit/Delete/Add Configurations
sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/model/GetLists",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "dmr/mdg/supernova/SupernovaFJ/libs/DataService",
    "sap/m/MessageBox"
], function (GetListsData, Utilities, DMRDataService, MessageBox) {
	"use strict";

    let oMDCConfigurationEVA = {};

    oMDCConfigurationEVA.onLiveChangeConfiguration = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sConfigId = oEvent.getSource().getProperty("value").toUpperCase();
        oEvent.getSource().setValue(sConfigId);

        if (sConfigId) {
            if (sConfigId.substring(0, 1) !== "Z" && sConfigId.substring(0, 1) !== "Y") {
                oMDConsolidationModel.setProperty("/configData/ConfigIdValueState", "Error");
                oMDConsolidationModel.setProperty("/configData/ConfigIdValueStateText", "Configuration ID must start with Z or Y");
            } else {
                oMDConsolidationModel.setProperty("/configData/ConfigIdValueState", "None");
                oMDConsolidationModel.setProperty("/configData/ConfigIdValueStateText", "");
            }
        } else {
            oMDConsolidationModel.setProperty("/configData/ConfigIdValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/ConfigIdValueStateText", "Configuration ID is mandatory");
        }
        oMDCConfigurationEVA.validateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationEVA.onLiveChangeDescription = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        if (oEvent.getSource().getProperty("value")) {
            oMDConsolidationModel.setProperty("/configData/DescriptionValueState", "None");
            oMDConsolidationModel.setProperty("/configData/DescriptionValueStateText", "");
        } else {
            oMDConsolidationModel.setProperty("/configData/DescriptionValueState", "Error");
            oMDConsolidationModel.setProperty("/configData/DescriptionValueStateText", "Configuration description is mandatory");
        }

        oMDCConfigurationEVA.validateFragmentToSave(oMDConsolidationModel);
    };

    oMDCConfigurationEVA.onSelectDeleteResultsCheckbox = function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        if (oEvent.getParameter("selected")) {
            oMDConsolidationModel.setProperty("/configData/DeleteResults", true);
        } else {
            oMDConsolidationModel.setProperty("/configData/DeleteResults", false);
        }
    };

    oMDCConfigurationEVA.onSelectProvideScoreCheckbox = function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        if (oEvent.getParameter("selected")) {
            oMDConsolidationModel.setProperty("/configData/ProvideScores", true);
        } else {
            oMDConsolidationModel.setProperty("/configData/ProvideScores", false);
        }
    };

    oMDCConfigurationEVA.onSelectDeleteScoresCheckbox = function(oEvent){
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        if (oEvent.getParameter("selected")) {
            oMDConsolidationModel.setProperty("/configData/DeleteScores", true);
        } else {
            oMDConsolidationModel.setProperty("/configData/DeleteScores", false);
        }
    };

    oMDCConfigurationEVA.validateFragmentToSave = function(oMDConsolidationModel){
        let oConfigData = oMDConsolidationModel.getProperty("/configData");
        let oCreateButton = sap.ui.getCore().byId("MDCConfigurationEVAFragmentID--BtnSave");

        if(oConfigData.ConfigIdValueState === "None" && oConfigData.DescriptionValueState === "None"){
            oCreateButton.setEnabled(true);
        } else{
            oCreateButton.setEnabled(false);
        }
    };

    oMDCConfigurationEVA.saveConfiguration = function(oController, oEvent, sOperationType) {
        let oMDConsolidationModel = oEvent.getSource().getModel("mdconsolidationModel");
        let oConfigData = oMDConsolidationModel.getProperty("/configData");

        let promiseTransport = oController._getSelectedTransportPackage(true, false, false);
        // Task 13213: make the function asyncronous so we can call the corresponding service name
        promiseTransport.then(async oSelectedTransport => {
            sap.ui.core.BusyIndicator.show(300);
            
            let oPayload = {
                ConfigId: oConfigData.ConfigId,
                Description: oConfigData.Description,
                Parallel: Number(oConfigData.Parallel),
                PackageSize: Number(oConfigData.PackageSize),
                QueuePrefix: oConfigData.QueuePrefix,
                ProvideScores: (oConfigData.ProvideScores ? true : false),
                DeleteResults: (oConfigData.DeleteResults ? true : false),
                EvaluationResults: Number(oConfigData.EvaluationResults),
                DeleteScores: (oConfigData.DeleteScores ? true : false),
                EvaluationScores: Number(oConfigData.EvaluationScores),
                CustTransport: oSelectedTransport.customizingTransport,
                Operation: sOperationType,
                Message: "",
                MessageCode: ""
            };

            (new DMRDataService(
                oController,
                // Task 13213: call the corresponding service name depending on the system
				await oController.serviceName,
                "/EVALUATIONSet",
                "Save Configuration",
                "/", // Root of the received data
                "Save Configuration"
            )).saveData(
                false,
                oPayload,
                null, {
                    success: {
                        fCallback: function (oDataResult, oResponse) {
                            sap.ui.core.BusyIndicator.hide();

                            if (oResponse.MessageCode === "E") {
                                Utilities.showPopupAlert("Error: " + oResponse.Message, MessageBox.Icon.ERROR, "Error");
                                return;
                            }

                            let arrConfigList = [];
                            let promiseConfigurationList = GetListsData.getConfigurationEVADetails(oController);
                            promiseConfigurationList.then(oData => {
                                arrConfigList.push(oController._getCreateConfigurationObject());
                                arrConfigList.push(oController._getEmptyConfigurationObject());
                                oData.forEach(oConfiguration => {
                                    arrConfigList.push({
                                        Zkey: "",
                                        Id: oConfiguration.ConfigId,
                                        Description: oConfiguration.Description
                                    });
                                });

                                oMDConsolidationModel.setProperty(oConfigData.SelectedRow + "/arrConfigList", arrConfigList);
                                oMDConsolidationModel.setProperty(oConfigData.SelectedRow + "/ConfigId", ( sOperationType === "D" ? "" : oConfigData.ConfigId ));
                                oMDConsolidationModel.setProperty("/configData", {});
                                
                                // Bug 12351 - Display/Edit/Delete/Add Configurations
                                oController._refreshTemplateDropDownList(oController);

                                if (oResponse.MessageCode === "S") {
                                    Utilities.showPopupAlert(oResponse.Message, MessageBox.Icon.SUCCESS, "Success");
                                }

                                let oMDCConfigurationEVAFragment = oController._getMDCConfigurationFragment("EVA");
                                oMDCConfigurationEVAFragment.close();
                            });
                            promiseConfigurationList.catch(oError => {
                                sap.ui.core.BusyIndicator.hide();
                                let message = jQuery.parseJSON(oError.responseText).error.message.value;
                                Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");
                            });
                        },
                        oParam: this
                    },
                    error: function (oError) {
                        sap.ui.core.BusyIndicator.hide();
                        let message = jQuery.parseJSON(oError.responseText).error.message.value;
                        Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");
                    }
                }
            );
        });
    };

    oMDCConfigurationEVA.onClickClose = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sSelectedRow = oMDConsolidationModel.getProperty("/configData/SelectedRow");
        let sOperationType = oMDConsolidationModel.getProperty("/configData/OperationType");

        if (sOperationType === "C") {
            oMDConsolidationModel.setProperty(sSelectedRow + "/ConfigId", undefined);
        }

        oMDConsolidationModel.setProperty("/configData", {});

        let oMDCConfigurationEVAFragment = this._getMDCConfigurationFragment("EVA");
        oMDCConfigurationEVAFragment.close();
    };

    oMDCConfigurationEVA.onClickDelete = function(oEvent) {
        this.MDCConfigurationEVA.saveConfiguration(this, oEvent, "D");
    };

    oMDCConfigurationEVA.onClickSave = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sOperationType = oMDConsolidationModel.getProperty("/configData/OperationType");
        this.MDCConfigurationEVA.saveConfiguration(this, oEvent, sOperationType);
    };

    return oMDCConfigurationEVA;
});