// Task 12350 - Display/Edit/Delete/Add Adapter service
sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
    "dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "dmr/mdg/supernova/SupernovaFJ/libs/DataService",
    "sap/m/MessageBox"
], function (GetListsData, Utilities, DMRDataService, MessageBox) {
	"use strict";

    let oMDCAdapter = {};

    oMDCAdapter.onChangeAdapter = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        if (oEvent.getSource().getProperty("selectedKey")) {
            oMDConsolidationModel.setProperty("/MDCAdapterModel/AdapterValueState", "None");
            oMDConsolidationModel.setProperty("/MDCAdapterModel/AdapterValueStateText", "");
        } else {
            oMDConsolidationModel.setProperty("/MDCAdapterModel/AdapterValueState", "Error");
            oMDConsolidationModel.setProperty("/MDCAdapterModel/AdapterValueStateText", "Adapter is mandatory");
        }
    };

    oMDCAdapter.onLiveChangeDescription = function(oEvent) {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");

        if (oEvent.getSource().getProperty("value")) {
            oMDConsolidationModel.setProperty("/MDCAdapterModel/DescriptionValueState", "None");
            oMDConsolidationModel.setProperty("/MDCAdapterModel/DescriptionValueStateText", "");
        } else {
            oMDConsolidationModel.setProperty("/MDCAdapterModel/DescriptionValueState", "Error");
            oMDConsolidationModel.setProperty("/MDCAdapterModel/DescriptionValueStateText", "Description is mandatory");
        }
    };

    
    // Bug 13216 - _updateConfigList is a function that updates the array of the values on the configuration select box.
    oMDCAdapter._updateConfigList = async function(oController){
        let oMDConsolidationModel = oController.getView().getModel("mdconsolidationModel");
        let sSelectedAdapter = oMDConsolidationModel.getProperty("/MDCAdapterModel/Adapter");
        
        // Task 12351 - Display/Edit/Delete/Add Configurations
        let arrConfigList = [];
        arrConfigList.push(oController._getCreateConfigurationObject());
        arrConfigList.push(oController._getEmptyConfigurationObject());
        
        let sChangeRowPath = oMDConsolidationModel.getProperty("/MDCAdapterModel/SelectedRow");
        let sSelectedBoType = oMDConsolidationModel.getProperty("/selectedProcessTemplate/BoType");
        let sSelectedProcessGoal = oMDConsolidationModel.getProperty("/selectedProcessTemplate/ProcessGoal");
        let sSelectedStepType = oMDConsolidationModel.getProperty(sChangeRowPath + "/StepType");
        oMDConsolidationModel.setProperty(sChangeRowPath + "/ConfigId", undefined);
        if (sSelectedBoType) {
            let oMatchedBoType = oMDConsolidationModel.getProperty("/arrBoTypeList").find(oBoType => {
                return oBoType.BoType === sSelectedBoType;
            });
            if(oMatchedBoType && sSelectedProcessGoal) {
                let oMatchedProcessGoal = oMatchedBoType.BO2PROCESSGOALNAV.results.find(oProcessGoal =>{
                    return oProcessGoal.Name === sSelectedProcessGoal;
                });
                if(oMatchedProcessGoal && sSelectedStepType) {
                    let oMatchedStepType = oMatchedProcessGoal.PROGOAL2STEPNAV.results.find(oStepType => {
                       return oStepType.StepName === sSelectedStepType;
                    });
                    if(oMatchedStepType && sSelectedAdapter) {
                        let oMatchedAdapter = oMatchedStepType.STEPTOADAPTERNAV.results.find(oAdapter => {
                            return oAdapter.AdapterName === sSelectedAdapter;
                         });
                         if(oMatchedAdapter) {
                            
                            oController._validateConfigurationField(oMDConsolidationModel, oMatchedAdapter, sChangeRowPath);

                            // Bug 12433 - Some process templates not displaying in RDG
                            if (!oMatchedAdapter.hasOwnProperty("ADAP2CONFIGNAV")) {
                                oMatchedAdapter.ADAP2CONFIGNAV = { "results": [] };
                            }
                            if (!oMatchedAdapter.ADAP2CONFIGNAV.hasOwnProperty("results")) {
                                oMatchedAdapter.ADAP2CONFIGNAV.results = [];
                            }
                            // Task 12351 - Display/Edit/Delete/Add Configurations

                            if(oMatchedAdapter.StepName === "MTC"){

                                try {
                                    let arrOData = await GetListsData.getConfigurationMTCDetails(this);
                    
                                    if (arrOData.length) {
                                        let arrFilteredOData = arrOData.filter((el)=> el.OTC === oMatchedAdapter.BoType);
                                        arrFilteredOData.forEach((element)=>{
                                            let oTemp = {
                                                Zkey: "",
                                                Id: element.Configuration,
                                                Description: element.Description
                                            };

                                            arrConfigList.push(oTemp);

                                        });
                                    }
                    
                                } catch (oError) {
                                    if (oError.statusCode !== "404") {
                                        Utilities.showPopupAlert(
                                            "Error: " + jQuery.parseJSON(oError.responseText).error.message.value, 
                                            MessageBox.Icon.ERROR, 
                                            "Error"
                                        );
                                    }
                                }

                            } else {
                                oMatchedAdapter.ADAP2CONFIGNAV.results.forEach(oConfiguration => {
                                    if (oConfiguration.Id) {
                                        arrConfigList.push(oConfiguration);
                                    }
                                });
                            }
                            oMDConsolidationModel.setProperty(sChangeRowPath + "/arrConfigList", arrConfigList);
                            oMDConsolidationModel.setProperty(sChangeRowPath + "/ConfigId", "");
                         }
                    }
                }
            }
            oMDConsolidationModel.refresh();
        }
    };

    oMDCAdapter.saveAdapter = function(oController, oMDConsolidationModel, sOperationType) {
        let bValidationError = false;
        let sSelectedRow = oMDConsolidationModel.getProperty("/MDCAdapterModel/SelectedRow");
        // Bug 13562: changed BoType to BOType
        let sBOType = oMDConsolidationModel.getProperty("/MDCAdapterModel/BOType");
        let sStepType = oMDConsolidationModel.getProperty("/MDCAdapterModel/StepType");
        let sAdapter = oMDConsolidationModel.getProperty("/MDCAdapterModel/Adapter");
        // Bug 13216 - get all of the steps on the selected process template
        let arrProcessTemplateSteps = oMDConsolidationModel.getProperty("/selectedProcessTemplate/TEMPLATE2STEPNAV/results");
        let sDescription = oMDConsolidationModel.getProperty("/MDCAdapterModel/Description");
        let sUsage = oMDConsolidationModel.getProperty("/ShowProcessUsage") ? oMDConsolidationModel.getProperty("/MDCAdapterModel/Usage") : undefined;

        if (!sAdapter) {
            bValidationError = true;
            oMDConsolidationModel.setProperty("/MDCAdapterModel/AdapterValueState", "Error");
            oMDConsolidationModel.setProperty("/MDCAdapterModel/AdapterValueStateText", "Adapter is mandatory");
        }
        if (!sDescription) {
            bValidationError = true;
            oMDConsolidationModel.setProperty("/MDCAdapterModel/DescriptionValueState", "Error");
            oMDConsolidationModel.setProperty("/MDCAdapterModel/DescriptionValueStateText", "Description is mandatory");
        }

        // Task 12411 - [MDC] Create dropdowns for CR Type fields in Configurations Dialog
        if (bValidationError) {
            Utilities.showPopupAlert("One or more mandatory fields are missing", MessageBox.Icon.ERROR, "Missing mandatory fields");
            return;
        } else {
            let promiseTransport = oController._getSelectedTransportPackage(false, true, false);
            // Task 13213: make the function asyncronous so we can call the corresponding service name
            promiseTransport.then(async oSelectedTransport => {
                sap.ui.core.BusyIndicator.show(300);
                
                let oPayload = {
                    Operation: sOperationType,
                    WbTransport: oSelectedTransport.workbenchTransport,
                    BoType: sBOType,
                    StepType: sStepType,
                    ProcessStepAdapter: sAdapter,
                    AdapterDescription: sDescription,
                    Usage: sUsage,
                    Message: "",
                    MessageProperty: ""
                };

                // Bug 12523 - Custom adapter cannot be created in SD2 only
                (new DMRDataService(
                    oController,
                    // Task 13213: call the corresponding service name depending on the system
                    await oController.serviceName,
                    "/SPECIFYADAPTERSet",
                    "Save Adapter",
                    "/", // Root of the received data
                    "Save Adapter"
                )).saveData(
                    false,
                    oPayload,
                    null, {
                        success: {
                            // Bug 13216 - make the function asyncronous
                            fCallback: async function (oDataResult, oResponse) {
                                sap.ui.core.BusyIndicator.hide();

                                if (oResponse.MessageProperty === "E") {
                                    Utilities.showPopupAlert("Error: " + oResponse.Message, MessageBox.Icon.ERROR, "Error");
                                    return;
                                }

                                // Bug 13216 - call the updateAdapterList function that refreshes the values on the selectbox
                                await oController._updateAdapterList(oController, sBOType, arrProcessTemplateSteps);
                                oMDConsolidationModel.setProperty(sSelectedRow + "/Adapter", ( sOperationType === "D" ? "" : sAdapter ));

                                // Bug 13216 - if you are creating a new adapter, update the config list
                                if(sOperationType=== "C"){
                                    await oController.MDCAdapter._updateConfigList(oController);
                                }
                                oMDConsolidationModel.setProperty("/MDCAdapterModel", {});
                                
                                // Bug 12351 - Display/Edit/Delete/Add Configurations
                                oController._refreshTemplateDropDownList(oController);
                                
                                if (oResponse.MessageProperty === "S") {
                                    Utilities.showPopupAlert(oResponse.Message, MessageBox.Icon.SUCCESS, "Success");
                                }

                                let oMDCAdapterFragment = oController._getMDCAdapterFragment();
                                oMDCAdapterFragment.close();

                            },
                            oParam: this
                        },
                        // Bug 13562: changed the error function to a correct callback
                        error:{
                            fCallback: function (oError) {
                               sap.ui.core.BusyIndicator.hide();
                                let message = jQuery.parseJSON(oError.responseText).error.message.value;
                                Utilities.showPopupAlert("Error: " + message, MessageBox.Icon.ERROR, "Error");
                            }
                        }
                    }
                );
            });
        }
    };

    oMDCAdapter.onClickClose = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sSelectedRow = oMDConsolidationModel.getProperty("/MDCAdapterModel/SelectedRow");
        let sOperationType = oMDConsolidationModel.getProperty("/MDCAdapterModel/OperationType");

        if (sOperationType === "C") {
            oMDConsolidationModel.setProperty(sSelectedRow + "/Adapter", undefined);
            oMDConsolidationModel.setProperty(sSelectedRow + "/ConfigId", undefined);
            oMDConsolidationModel.setProperty(sSelectedRow + "/ebConfig", false);
        }

        oMDConsolidationModel.setProperty("/MDCAdapterModel", {});

        let oMDCAdapterFragment = this._getMDCAdapterFragment();
        oMDCAdapterFragment.close();
    };

    oMDCAdapter.onClickDelete = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        this.MDCAdapter.saveAdapter(this, oMDConsolidationModel, "D");
    };

    oMDCAdapter.onClickSave = function() {
        let oMDConsolidationModel = this.getView().getModel("mdconsolidationModel");
        let sOperationType = oMDConsolidationModel.getProperty("/MDCAdapterModel/OperationType");
        this.MDCAdapter.saveAdapter(this, oMDConsolidationModel, sOperationType);
    };

    return oMDCAdapter;
});