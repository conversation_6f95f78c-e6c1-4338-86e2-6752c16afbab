sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
    "sap/ui/model/json/JSONModel",
    "dmr/mdg/supernova/SupernovaFJ/libs/DataService",
    "dmr/mdg/supernova/SupernovaFJ/model/GetLists",
    "dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "sap/m/MessageBox",
    "dmr/mdg/supernova/SupernovaFJ/model/ModelMessages",
    "dmr/mdg/supernova/SupernovaFJ/model/BackGroundJob",
    "./FioriALPReportJson",
    "sap/ui/core/BusyIndicator"
    
], function (BaseController, JSONModel, DMRDataService, GetListsData, Utilities, MessageBox, ModelMessages, BackGroundJob, FioriALPReportJson, BusyIndicator) {
    "use strict";
    return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.Administrator.ProcessAnalytics.EndAnalyticalDetail", {
        PROCESS_ANALYTICAL_SECTION_ID: "processAnalyticalListView",
        CHANGE_ANALYTICAL_SECTION_ID: "changeAnalyticalListView",
        onInit: function () {
            this.oView = this.getView();
            this.ModelMessages = ModelMessages;
            this.oRouter = this.getOwnerComponent().getRouter();
            this.oRouter.attachRouteMatched(this._onRouteMatched, this);
            let oEndAnalyticalDetail = this.getView().getModel("EndAnalyticalDetail");
            if (!oEndAnalyticalDetail) {
                let oJSONModel = new JSONModel();
                this.getView().setModel(oJSONModel, "EndAnalyticalDetail");
            }
        },

        _onRouteMatched: function (oEvent) {
            let oRouteParams = oEvent.getParameter("arguments");
            let oEndAnalyticalDetail = this.getView().getModel("EndAnalyticalDetail");
            oEndAnalyticalDetail.setProperty("/routingInformation", oRouteParams);
            oEndAnalyticalDetail.setProperty("/Section", oRouteParams.Section);
            if (!oRouteParams.ViewName && !oRouteParams.isNew) {
                return; // Nothing to do.
            }
            
            // If View name is provided, load the details
            oEndAnalyticalDetail.setProperty("/isEditViewEnabled", false);
            if (oRouteParams.ViewName) { 
                this.loadAnalyticViewDetails();
            }
            // If it is new view, clear all and load the views list for name validation
            else if (oRouteParams.isNew === "X"){
                oEndAnalyticalDetail.setProperty("/detailView", {});
                oEndAnalyticalDetail.setProperty("/Fields", []);
                oEndAnalyticalDetail.setProperty("/detailView/isNewView", true);
                this.promiseAnalyticalViewList = this.loadAnalyticalViews();
            }
            this.loadModelDetails();
        },
        loadModelDetails: function () {
            let oController = this;
            let oEndAnalyticalDetail = this.getView().getModel("EndAnalyticalDetail");
            let sRouting = oEndAnalyticalDetail.getProperty("/routingInformation");
            if (sRouting.DataModel) {
                // get main entities list
                let promiseCDSMainEntities = GetListsData.getCDSMainEntityTypes(oController, sRouting.DataModel);
                promiseCDSMainEntities.then(function (oData) {
                    oEndAnalyticalDetail.setProperty("/mainEntityList", oData);
                });
            }

            // get analysis target
            let promiseCDSAnaTarget = GetListsData.getCDSAnaTarget(oController, sRouting.Section);
            promiseCDSAnaTarget.then(function (oData) {
                oEndAnalyticalDetail.setProperty("/anaTargetList", oData);
            });

            // get log domain values
            let promiseCDSLogDomain = GetListsData.getCDSLogDomain(oController);
            promiseCDSLogDomain.then(function (oData) {
                oEndAnalyticalDetail.setProperty("/logDomain", oData);
            });

            // get packages
            let promiseCDSPackage = GetListsData.getCDSPackage(oController);
            promiseCDSPackage.then(function (oData) {
                oEndAnalyticalDetail.setProperty("/packageList", oData);
            });
        },
        onMainEntitySelectionChange: function(oEvent){
            let oController = this;
            let oModel = this.getView().getModel("EndAnalyticalDetail");
            let oRoutingInfo = oModel.getProperty("/routingInformation");
            let arrFields = oModel.getProperty("/Fields");

            let sDataModel = oRoutingInfo.DataModel;
            let sSelectedMainEntity = oEvent.getParameter("selectedItem").getKey();

            // Set Entity busy
            oModel.setProperty("/EntityAttributeBusy", true);

            // get entities
            let promiseCDSEntities = GetListsData.getCDSEntityTypes(oController, sDataModel, sSelectedMainEntity);
            promiseCDSEntities.then(function (arrEntityData) {
                oModel.setProperty("/entityList", arrEntityData);
                // search of the current entity selected within the new entity list.
                // If not found, reset the selected key
                arrFields.forEach(function(fieldRow){
                    const entityFound = arrEntityData.find((entityInfo) =>  entityInfo.Entity === fieldRow.Entity);
                    if(!entityFound){
                        fieldRow.Entity = undefined;
                    }
                });
                oModel.setProperty("/Fields", arrFields);
                oModel.setProperty("/EntityAttributeBusy", true);
                
                // Update the attribute fields based on the updated entities
                oController.setAttributeArray();
            });
        },
        // Update the attribute information for each of the rows in the fields table
        setAttributeArray: function () {
            let oEndAnalyticalDetail = this.getView().getModel("EndAnalyticalDetail");
            let arrFields = oEndAnalyticalDetail.getProperty("/Fields");
            let oController = this;
            let promiseArray = [];
            // Set attribute busy
            oEndAnalyticalDetail.setProperty("/EntityAttributeBusy", true);
            arrFields.forEach((oRow, index) => {
                if (oRow.Entity) {
                    //get attribute list based on entity selection
                    promiseArray[index] = GetListsData.getCDSAttributes(oController, oRow.Model, oRow.Entity);
                } else {
                    // Return an emtpy list
                    promiseArray[index] = new Promise((resolve) => resolve([]));
                } 
            });

            return Promise.all(promiseArray).then(function(arrAttributesLists){

                arrFields.forEach(function(fieldRow, index){
                    let attributesList = arrAttributesLists[index];
                    fieldRow.arrAttributeList = attributesList;

                    // If the current selected attribute is not in the list, unset
                    if(!arrFields[index].Attribute){
                        return;
                    }

                    const attrFound = attributesList.find((attrInfo) =>  attrInfo.Attribute === fieldRow.Attribute);
                    if(!attrFound){
                        fieldRow.Attribute = undefined;
                    }

                });
                oEndAnalyticalDetail.setProperty("/Fields", arrFields);
                oEndAnalyticalDetail.setProperty("/EntityAttributeBusy", false);
            });
        },
        loadAnalyticViewDetails: function () {
            let oController = this;
            let oEndAnalyticalDetail = this.getView().getModel("EndAnalyticalDetail");
            oEndAnalyticalDetail.setProperty("/Fields", []);
            oEndAnalyticalDetail.setProperty("/detailView", {});
            let sRouting = oEndAnalyticalDetail.getProperty("/routingInformation");

            let promiseGetHeaders = undefined;
            if(sRouting.Section === this.PROCESS_ANALYTICAL_SECTION_ID){
                // get process analytical view header details for model, viewname
                promiseGetHeaders = GetListsData.getCDSAnaHeaders(oController, sRouting.DataModel, sRouting.ViewName);
            } else {
                promiseGetHeaders = GetListsData.getCDSChangeAnaHeaders(oController, sRouting.DataModel, sRouting.ViewName);
            }

            promiseGetHeaders.then(function (oData) {
                let parsedDate = new Date(oData[0].FinishedAt).toDateString();
                oData[0].FinishedAt = parsedDate;
                oEndAnalyticalDetail.setProperty("/detailView", oData[0]);

                // get entities
                let promiseCDSEntities = GetListsData.getCDSEntityTypes(oController, sRouting.DataModel, oData[0].MainEntity);
                promiseCDSEntities.then(function (oEntityData) {
                    oEndAnalyticalDetail.setProperty("/entityList", oEntityData);
                });
            });

            let promiseGetFields = undefined;
            if(sRouting.Section === this.PROCESS_ANALYTICAL_SECTION_ID){
                promiseGetFields = GetListsData.getCDSProcessAnaFields(oController, sRouting.ViewName);
            } else {
                promiseGetFields = GetListsData.getCDSChangeAnaFields(oController, sRouting.ViewName);
            }

            promiseGetFields.then(function (oData) {
                oEndAnalyticalDetail.setProperty("/Fields", oData);
                //update the fields table with attribute list property
                oController.setAttributeArray();
            });
        },
        
        onChangeEntity: function (oEvent) {
            let oController = this;
            let oEndAnalyticalDetail = this.getView().getModel("EndAnalyticalDetail");
            let sModel = oEndAnalyticalDetail.getProperty("/routingInformation/DataModel");
            let sChangeRowPath = oEvent.getSource().getBindingContext("EndAnalyticalDetail").getPath();
            let iChangeRowIndex = parseInt(sChangeRowPath.substring(sChangeRowPath.lastIndexOf("/") + 1));
            let oFieldsTableDetails = oEndAnalyticalDetail.getProperty("/Fields");
            let sSelectedEntityName = oEvent.getSource().getSelectedKey();
            //get attribute list based on entity selection
            let promiseCDSAttributes = GetListsData.getCDSAttributes(oController, sModel, sSelectedEntityName);
            // Set the attribute to busy until the data is received
            oEndAnalyticalDetail.setProperty("/EntityAttributeBusy", true);
            promiseCDSAttributes.then(function (oData) {
                oFieldsTableDetails[iChangeRowIndex].arrAttributeList = oData;
                let oMatchedAttribute = oFieldsTableDetails[iChangeRowIndex].arrAttributeList.find((oAttribute) => {
                    return oAttribute.Attribute === oFieldsTableDetails[iChangeRowIndex].Attribute;
                });
                if (!oMatchedAttribute) {
                    oFieldsTableDetails[iChangeRowIndex].Attribute = undefined;
                }
                oEndAnalyticalDetail.setProperty("/Fields", oFieldsTableDetails);
                oEndAnalyticalDetail.setProperty("/EntityAttributeBusy", false);
            });
        },

        onAddNewAttributes: function () {
            let oEndAnalyticalDetail = this.getView().getModel("EndAnalyticalDetail");
            let aFieldsList = oEndAnalyticalDetail.getProperty("/Fields");
            let oNewEntry = {};
            if (!Array.isArray(aFieldsList)) {
                // If aFieldsList is not an array or is falsy, initialize it as an empty array
                aFieldsList = [];
            }

            // Now, aFieldsList is guaranteed to be an array
            aFieldsList.push(oNewEntry);
            oEndAnalyticalDetail.setProperty("/Fields", aFieldsList);
        },
        onCancelEdit: function(){
            let oController = this;
            let oModel = oController.getView().getModel("EndAnalyticalDetail");
            let oRoutingInfo = oModel.getProperty("/routingInformation");
            let sDataModel = oRoutingInfo.DataModel;
            let sModelSection = oModel.getProperty("/Section");

            // Notify the user that all the changes will be lost 
            Utilities.showPopupAlert(
                oController.geti18nText("analyticalViews.end.confirmCancelEdit.popup.text"), 
                MessageBox.Icon.WARNING, 
                oController.geti18nText("analyticalViews.end.confirmCancelEdit.popup.title"),                [sap.m.MessageBox.Action.YES, sap.m.MessageBox.Action.NO])
            .then(function(){
                // If not new add view name 
                if(oRoutingInfo.isNew !== "X"){
                    oModel.setProperty("/isEditViewEnabled", false);
                    oController.loadAnalyticViewDetails();
                } else {
                    oController.oRouter.navTo("MainAnalytics", {
                        DataModel: sDataModel,
                        Section: sModelSection,
                    }, true);
                }
            });
        },
        onEditView: function() {
            let oEndAnalyticalDetail = this.getView().getModel("EndAnalyticalDetail");
            oEndAnalyticalDetail.setProperty("/isEditViewEnabled", true);
        },
        isEditModeFormatter: function(isEditViewEnabled, isNewView, FioriReportExists){
            return (isEditViewEnabled === true || isNewView === true || FioriReportExists === true );
        },
        onExit: function () {
            this.oRouter.detachRouteMatched(this.onRouteMatched, this);
        },
        onDeleteRow: function (oEvent) {
            let oItem = oEvent.getParameter("listItem");
            let oProcessAnalyticalTable = oEvent.getSource();

            // Get the fields data array
            let oEndAnalyticalDetail = this.getView().getModel("EndAnalyticalDetail");
            let arrList = oEndAnalyticalDetail.getProperty("/Fields");

            // Find the index of the deleted row
            let sClickedIndex = oProcessAnalyticalTable.getItems().indexOf(oItem);

            // Update the model by removing the item at the specified index
            if (arrList && arrList.length > sClickedIndex) {
                arrList.splice(sClickedIndex, 1);
                oProcessAnalyticalTable.removeSelections();
                oEndAnalyticalDetail.setProperty("/Fields", arrList);
            }

        },
        saveOrGenerateAnalyticalView: async function(oController, sSelectedTransport, bGenerate, sSelectedCusTransport){
            let oModel = oController.getView().getModel("EndAnalyticalDetail");
            let oDetailsHeader = oModel.getProperty("/detailView");
            let oFields = oModel.getProperty("/Fields");
            let sRouting = oModel.getProperty("/routingInformation");
            let sModelSection = oModel.getProperty("/Section");
            let bIsProcessAnalytics = (sModelSection === this.PROCESS_ANALYTICAL_SECTION_ID) ? true : false;
            let oHeaderData = {
                ViewName: oDetailsHeader.ViewName,
                Model: sRouting.DataModel,
                MainEntity: oDetailsHeader.MainEntity.split("-")[0].trim(), // Main entity is mandatory, it cannot be empty. Remove all checks
                Description: oDetailsHeader.Description,
                Package: oDetailsHeader.Package,
                AnaTarget: oDetailsHeader.AnaTarget,
                WbTransport: sSelectedTransport,
                CustTransport: sSelectedCusTransport
            };

            // If not called for generation, transport and field data need to be sent
            if(bGenerate !== true){
                if(bIsProcessAnalytics){
                    oHeaderData.processheadertofieldsnav = [];
                } else {
                    oHeaderData.changeheadertofieldsnav = [];
                }
            }

            // When saving Change Analytical Views add Log Domain and Auth Check 
            if(!bIsProcessAnalytics){
                oHeaderData.LogDomain = oDetailsHeader.LogDomain;
                oHeaderData.AuthCheck = oDetailsHeader.AuthCheck;
            }

            // Add the field data when called for saving
            if(bGenerate !== true){
                // Remove fields length check. The process does not allow the save to proceed if zero fields are added.
                oFields.forEach(function (item) {
                    item.Entity = item.Entity ? item.Entity.split("-")[0].trim() : undefined;
                    item.Attribute = item.Attribute ? item.Attribute.split("-")[0].trim() : undefined;
                    item.Domain = item.Domain ? item.Domain.split("-")[0].trim() : undefined;
                    let oField = {
                        Entity: item.Entity,
                        Attribute: item.Attribute,
                        Domain: item.Domain,
                        ViewName: oDetailsHeader.ViewName,
                        Model: sRouting.DataModel,
                        MainEntity: oDetailsHeader.MainEntity
                    };

                    // When saving Change Analytical Views add Log Domain and Auth Check 
                    if(!bIsProcessAnalytics){
                        oField.IsReport = item.IsReport;
                    }
                    if(bIsProcessAnalytics){
                        oHeaderData.processheadertofieldsnav.push(oField);
                    } else {
                        oHeaderData.changeheadertofieldsnav.push(oField);
                    }
                    
                });
            }

            // Chose the service path based on the view type
            let sServicePath = bIsProcessAnalytics? "/processanaheaderSet": "/changeanaheaderSet";        
            let sMessageTitlei18n = bGenerate?
                    "analyticalViews.end.generateView.tooltip":
                    "analyticalViews.end.saveView.tooltip";

            (new DMRDataService(
                oController,
                "PROCESS_ANALYTICS",
                sServicePath,
                "Create header Template with fields",
                "/", // Root of the received data
                "Create header Template with fields"
            ))
            .showBusyIndicator(true)
            .saveData(
                false,
                oHeaderData,
                null, {
                success: {
                    fCallback: function (oParams, oResponseData) {
                        let sMessage = oResponseData.Message;
                        let sMessageIcon = Utilities.getMessageIconBoxForType(oResponseData.MessageType);

                        let promiseShowPopupAlert =
                            Utilities.showPopupAlert(sMessage, sMessageIcon, oController.geti18nText(sMessageTitlei18n));
                        promiseShowPopupAlert.then(function () {
                            // navigate to middle screen
                            oController.oRouter.navTo("MainAnalytics", 
                                { 
                                    DataModel: sRouting.DataModel, 
                                    Section: sModelSection,
                                    ViewName: oDetailsHeader.ViewName 
                                }, true);
                        });
                    }
                },
                error: {
                    fCallback: function (oParams, oResponseData) {
                        let sMessage = oResponseData.Message;
                        Utilities.showPopupAlert("Error " + sMessage, MessageBox.Icon.ERROR, 
                            oController.geti18nText(sMessageTitlei18n));
                    }
                }
            });
        },

        deleteAnalyticalView: async function(oController, sSelectedTransport, bGenerate, sSelectedCusTransport){
            let oModel = oController.getView().getModel("EndAnalyticalDetail");
            let oDetailsHeader = oModel.getProperty("/detailView");
            let sRouting = oModel.getProperty("/routingInformation");
            let sModelSection = oModel.getProperty("/Section");
            let bIsProcessAnalytics = (sModelSection === this.PROCESS_ANALYTICAL_SECTION_ID) ? true : false;
            let oHeaderData = {
                ViewName: oDetailsHeader.ViewName,
                Model: sRouting.DataModel,
                WbTransport: sSelectedTransport,
                CustTransport: sSelectedCusTransport
            };

            // Chose the service path based on the view type
            let sServicePath = bIsProcessAnalytics? "/processanaheaderSet": "/changeanaheaderSet";
            // Determine whether to use oHeaderData or oDeleteData based on f_delete
            const uri = `(Model='${oHeaderData.Model}',ViewName='${oHeaderData.ViewName}',WbTransport='${oHeaderData.WbTransport}',CustTransport='${oHeaderData.CustTransport}')`;            
            let sMessageTitlei18n = "analyticalViews.end.deleteView.tooltip";

            (new DMRDataService(
                oController,
                "PROCESS_ANALYTICS",
                sServicePath + uri,
                "Create header Template with fields",
                "/", // Root of the received data
                "Create header Template with fields"
            ))
            .showBusyIndicator(true)
            .deleteData( 
                false,
                null,
                null, {
                success: {
                    fCallback: function (oParams, oResponseData) {
                        let sMessage = oController.geti18nText("analyticalViews.end.delete.message", 
                            [oController?.getView()?.getModel("EndAnalyticalDetail")?.getProperty("/detailView/ViewName") || "N/A"]);
                        
                        
                        let sMessageIcon = Utilities.getMessageIconBoxForType(oResponseData.MessageType);

                        let promiseShowPopupAlert =
                            Utilities.showPopupAlert(sMessage, sMessageIcon, oController.geti18nText(sMessageTitlei18n));
                        promiseShowPopupAlert.then(function () {
                            // navigate to middle screen
                            oController.oRouter.navTo("MainAnalytics", 
                                { 
                                    DataModel: sRouting.DataModel,
                                    refreshViews: "X",
                                    Section: undefined,
                                    ViewName: undefined
                                }, true);
                        });
                    }
                },
                error: {
                    fCallback: function (oParams, oResponseData) {
                        let sMessage = oResponseData.Message;
                        Utilities.showPopupAlert("Error " + sMessage, MessageBox.Icon.ERROR, 
                            oController.geti18nText(sMessageTitlei18n));
                    }
                }
            });
        },
        /**
         * Perform validations on the view to check if the view can be saved or generated.
         * These validations are limited to view content only.
         * @param {*} oController 
         * @param {*} bGenerate if true, check validations for generate, else for save
         * @returns 
         */
        canSaveOrGenerateView: function(oController){
            let oModel = oController.getView().getModel("EndAnalyticalDetail");
            let oDetailsHeader = oModel.getProperty("/detailView");
            let sModelSection = oModel.getProperty("/Section");
            let bIsProcessAnalytics = (sModelSection === this.PROCESS_ANALYTICAL_SECTION_ID) ? true : false;
            let arrFields = oModel.getProperty("/Fields");

            let sValueState = oDetailsHeader.viewNameValueState;
            let bInvalidViewName = false; 
            if (sValueState === sap.ui.core.ValueState.Error) {
                bInvalidViewName= true;
            } 
            
            // Check mandatory fields and set their value state
            let bMandatoryFieldsIncomplete = false; 
            if(!oDetailsHeader.ViewName){
                bMandatoryFieldsIncomplete = true;
                oDetailsHeader.viewNameValueState = sap.ui.core.ValueState.Error;
                oDetailsHeader.viewNameValueStateText 
                    = oController.geti18nText("analyticalViews.end.section.viewDetails.inputRequired.valueState");
            } 

            if(!oDetailsHeader.Description){
                bMandatoryFieldsIncomplete = true;
                oDetailsHeader.viewDescriptionValueState = sap.ui.core.ValueState.Error;
            } else {
                oDetailsHeader.viewDescriptionValueState = sap.ui.core.ValueState.None;
            }

            if(!oDetailsHeader.MainEntity){
                bMandatoryFieldsIncomplete = true;
                oDetailsHeader.viewMainEntityValueState = sap.ui.core.ValueState.Error;
            } else {
                oDetailsHeader.viewMainEntityValueState = sap.ui.core.ValueState.None;
            }

            if(!oDetailsHeader.Package){
                bMandatoryFieldsIncomplete = true;
                oDetailsHeader.viewPackageValueState = sap.ui.core.ValueState.Error;
            } else {
                oDetailsHeader.viewPackageValueState = sap.ui.core.ValueState.None;
            }

            // Validate that all the mandatory fields (entiy and attribute) are not empty
            let bFieldMantoryElementsMissing = false;
            let bAllFieldsDrillDown = true;
            arrFields.forEach((fieldRow) => {
                if(!fieldRow.Entity || !fieldRow.Attribute){
                    bFieldMantoryElementsMissing = true;
                }
                if(!bIsProcessAnalytics && ( fieldRow.IsReport === undefined || fieldRow.IsReport === false )){
                    bAllFieldsDrillDown = false;
                }
            });
            // Store the state to the model.
            oModel.setProperty("/detailView", oDetailsHeader);

            let promise = new Promise(function(resolve){
                let promiseAlertPopup = undefined;
                if(bInvalidViewName){
                    
                    promiseAlertPopup = Utilities.showPopupAlert(
                        oController.geti18nText("analyticalViews.end.saveView.popop.invalidViewName.text"), 
                        MessageBox.Icon.ERROR, 
                        oController.geti18nText("analyticalViews.end.saveView.tooltip"));
                } 
                if(bMandatoryFieldsIncomplete && promiseAlertPopup === undefined){
                    
                    promiseAlertPopup = Utilities.showPopupAlert(
                        oController.geti18nText("analyticalViews.end.saveView.popop.inputsMandatory.text"), 
                        MessageBox.Icon.ERROR, 
                        oController.geti18nText("analyticalViews.end.saveView.tooltip"));
                } 
                
                if (arrFields.length === 0 && promiseAlertPopup === undefined) {
                    //cannot save header with Zero fields
                    promiseAlertPopup = Utilities.showPopupAlert(
                        oController.geti18nText("analyticalViews.end.saveView.popop.noFieldsAdded.text"), 
                        MessageBox.Icon.ERROR, 
                        oController.geti18nText("analyticalViews.end.saveView.tooltip"));
                }

                if(promiseAlertPopup === undefined && bFieldMantoryElementsMissing){
                    promiseAlertPopup = Utilities.showPopupAlert(
                        oController.geti18nText("analyticalViews.end.saveView.popop.entityAttributeNeeded.text"),
                        MessageBox.Icon.ERROR, 
                        oController.geti18nText("analyticalViews.end.saveView.tooltip"));
                }

                if(promiseAlertPopup === undefined && !bIsProcessAnalytics && bAllFieldsDrillDown === true){
                    promiseAlertPopup = Utilities.showPopupAlert(
                        oController.geti18nText("analyticalViews.end.saveView.popop.atleast1RowNoDrillDown.text"),
                        MessageBox.Icon.ERROR, 
                        oController.geti18nText("analyticalViews.end.saveView.tooltip"));
                }

                if(promiseAlertPopup){
                    promiseAlertPopup.then(function(){
                        resolve(false); // View data is INCOMPLETE
                    });
                } else {
                    resolve(true); // View data is complete
                }
            });


            return promise;
        },
        
        onDeleteAnalyticalView: function ( ) {
            // Update the variable in the model
            let oModel = this.getView().getModel("EndAnalyticalDetail");
            if (oModel) {
                oModel.setProperty("/DeleteAn", "X"); // Set the variable to "X"
            }
        
                // Call the existing handler logic
                this.onsDeleteAnalyticalView( );
        },        

        onSaveAnalyticalView: async function () {
            let oController = this;
            let oModel = oController.getView().getModel("EndAnalyticalDetail");
            let sModelSection = oModel.getProperty("/Section");
            let sViewName = oModel.getProperty("/detailView/ViewName");
            let sRouting = oModel.getProperty("/routingInformation");

            let bCanSave = await oController.canSaveOrGenerateView(oController);
            if(!bCanSave){
                return;
            }

            let sBackgroundJobCheckType = 
                (sModelSection === this.PROCESS_ANALYTICAL_SECTION_ID)? 
                BackGroundJob.BACKGROUNDJOB_TYPE.PROCESS_ANALYTICAL_VIEW : 
                BackGroundJob.BACKGROUNDJOB_TYPE.CHANGE_ANALYTICAL_VIEW;
            let promise = BackGroundJob.checkStatus(
                            oController, sBackgroundJobCheckType,
                            { "sDataModel": sRouting.DataModel, "sViewName": sViewName }, 
                            false );

            promise.then(function(bJobRunning){
                if(bJobRunning){
                    return { then: function() {} };// Nothing to perform, break the promise chain
                }

                // Ask for the Workbench transport
                return oController._selectTransportPackage(true, true, false);
            })
            .then(function(oTransportPackageResponse){
                return oController.saveOrGenerateAnalyticalView(oController, oTransportPackageResponse.workbenchTransport, false, oTransportPackageResponse.customizingTransport);
            })
            .catch(function(){ // If the transport package selection was cancelled
                // Nothing to perform, break the promise chain
                return { then: function() {} };
            })
            .finally(function(){
                oModel.setProperty("/isEditViewEnabled", false);
            });
        },

        onsDeleteAnalyticalView: async function () {
            let oController = this;
            let oModel = oController.getView().getModel("EndAnalyticalDetail");
            let sModelSection = oModel.getProperty("/Section");
            let sViewName = oModel.getProperty("/detailView/ViewName");
            let sRouting = oModel.getProperty("/routingInformation");

            let bCanSave = await oController.canSaveOrGenerateView(oController);
            if(!bCanSave){
                return;
            }

            let sBackgroundJobCheckType = 
                (sModelSection === this.PROCESS_ANALYTICAL_SECTION_ID)? 
                BackGroundJob.BACKGROUNDJOB_TYPE.PROCESS_ANALYTICAL_VIEW : 
                BackGroundJob.BACKGROUNDJOB_TYPE.CHANGE_ANALYTICAL_VIEW;
            let promise = BackGroundJob.checkStatus(
                            oController, sBackgroundJobCheckType,
                            { "sDataModel": sRouting.DataModel, "sViewName": sViewName }, 
                            false );

            promise.then(function(bJobRunning){
                if(bJobRunning){
                    return { then: function() {} };// Nothing to perform, break the promise chain
                }

                // Ask for the Workbench transport
                return oController._selectTransportPackage(true, true, false);
            })
            .then(function(oTransportPackageResponse){
                return oController.deleteAnalyticalView(oController, oTransportPackageResponse.workbenchTransport, false, oTransportPackageResponse.customizingTransport);
            })
            .catch(function(){ // If the transport package selection was cancelled
                // Nothing to perform, break the promise chain
                return { then: function() {} };
            })
            .finally(function(){
                oModel.setProperty("/isEditViewEnabled", false);
            });
        },

        _selectTransportPackage: function(bSelectCustomizing, bSelectWorkbench, bSelectPackage) {
            // Read the existing transports and pass to the component
            let oModel = this.getView().getModel("EndAnalyticalDetail");

            let oDetailView = oModel.getProperty("/detailView");

            let promiseSelect = this._transportPackageSelectionDialog.open(
                bSelectCustomizing, oDetailView.customizingTransport, 
                bSelectWorkbench, oDetailView.workbenchTransport, 
                bSelectPackage, oDetailView.packageSelected
            );

            return promiseSelect;
        },

        onTransportPackageDialogCreated: function(oEvent){
            let comp = oEvent.getParameter("component");
            let oController = this;

            // store the component handle 
            this._transportPackageSelectionDialog = comp;

            this.getUsername()
                .then(function (oSapUserInfo) {
                    let sUsername;
                    sUsername = oSapUserInfo.Sapname;
                    if (!sUsername) {
                        Utilities.showPopupAlert(
                            oController.geti18nText("common.noLogin.description"), 
                            MessageBox.Icon.ERROR, 
                            oController.geti18nText("common.noLogin.title"));
                    } else {
                        comp.setUser(sUsername);
                    }

                });
        },

        onGenerateAnalyticalView: async function () {
            let oController = this;
            let oModel = oController.getView().getModel("EndAnalyticalDetail");
            let oHeader = oModel.getProperty("/detailView");
            let sModelSection = oModel.getProperty("/Section");
            let sRouting = oModel.getProperty("/routingInformation");

            // Check the status if CDS view
            let oCDSHeader = await GetListsData.getCDSAnaHeaders(oController, oHeader.Model, oHeader.ViewName);
            if (!oCDSHeader.length < 1) {
                if(oCDSHeader[0].Status === "Generated") {
                Utilities.showPopupAlert(
                    oController.geti18nText("analyticalViews.end.generateView.alreadyGenerated.popup.message"), 
                    MessageBox.Icon.INFORMATION, 
                    oController.geti18nText("analyticalViews.end.generateView.tooltip"));
                return;
                }
            }

            let bCanSave = await oController.canSaveOrGenerateView(oController);
            if(!bCanSave){
                return;
            }

            // check if model is in generated status. If not dont allow the view to generate
            let oModelDetails = await GetListsData.getCDSModels(oController, oHeader.Model);
            if (!oModelDetails.length < 1) {
                if (oModelDetails[0].Status !== "Generated") {
                    Utilities.showPopupAlert(
                        oController.geti18nText("analyticalViews.end.generateView.dataModelViewsNotGenerated.popup.message"), 
                        MessageBox.Icon.ERROR, 
                        oController.geti18nText("analyticalViews.end.generateView.tooltip"));
                    return;
                }
            }

            // Check if any background jobs are pending for generating view (datamodel)
            let promise = BackGroundJob.checkStatus(
                oController, BackGroundJob.BACKGROUNDJOB_TYPE.GENERATE_MODEL_VIEWS,
                { "sDataModel": sRouting.DataModel, "sViewName": sRouting.ViewName }, 
                false );

            promise.then(function(bJobRunning){
                if(bJobRunning){
                    return { then: function() {} };// Nothing to perform, break the promise chain
                }

                let sBackgroundJobCheckType = 
                (sModelSection === oController.PROCESS_ANALYTICAL_SECTION_ID)? 
                BackGroundJob.BACKGROUNDJOB_TYPE.PROCESS_ANALYTICAL_VIEW : 
                BackGroundJob.BACKGROUNDJOB_TYPE.CHANGE_ANALYTICAL_VIEW;
                return BackGroundJob.checkStatus(
                            oController, sBackgroundJobCheckType,
                            { "sDataModel": sRouting.DataModel, "sViewName": sRouting.ViewName }, 
                            false );
            })
            .then(function(bJobRunning){
                if(bJobRunning){
                    return { then: function() {} };// Nothing to perform, break the promise chain
                }

                // Select workbench transport 
                return oController._selectTransportPackage(true, true, false);
            })
            .then(function(oTransportPackageResponse){                
                return oController.saveOrGenerateAnalyticalView(oController, oTransportPackageResponse.workbenchTransport, true, oTransportPackageResponse.customizingTransport);
            });
        },
        onLiveChangeViewName: async function (oEvent) {
            let oController = this;
            let input = oEvent.getSource();
            let oModel = this.getView().getModel("EndAnalyticalDetail");
            let sViews = await this.promiseAnalyticalViewList; 

            /* Tranform the input to Upper Case */
            input.setValue(input.getValue().toUpperCase());
            let sInputValue = input.getValue();
            let firstLetter = sInputValue.slice(0, 1);

            /**
             * View Name Rules 
             * 1. Must start with Y or Z 
             * 2. Must contain only A-Z, 0-9, / and _
             * 3. Must not be duplicate
             */

            /* Starts with Y or Z check  */
            if(firstLetter !== "Z" && firstLetter !== "Y"){
                oModel.setProperty("/detailView/viewNameValueState", sap.ui.core.ValueState.Error);
                oModel.setProperty("/detailView/viewNameValueStateText", 
                    oController.geti18nText("analyticalViews.end.section.viewDetails.name.notStartZY.valueStateText"));
                return;
            }

            /* Name must only contain A-Z, 0-), / and _ */
            if (!/^[A-Z0-9/_]*$/.test(sInputValue)) {
                oModel.setProperty("/detailView/viewNameValueState", sap.ui.core.ValueState.Error);
                oModel.setProperty("/detailView/viewNameValueStateText", 
                    oController.geti18nText("analyticalViews.end.section.viewDetails.name.notValid.valueStateText"));
                return;
            }

            /* Check for duplicate */
            let oSelectedObject = sViews.find((element) => (element.ViewName === sInputValue));
            if (oSelectedObject) {
                oModel.setProperty("/detailView/viewNameValueState", sap.ui.core.ValueState.Error);
                oModel.setProperty("/detailView/viewNameValueStateText", 
                    oController.geti18nText("analyticalViews.end.section.viewDetails.name.alreadyExits.valueStateText"));
                return;
            }

            // If not error found, clear error start
            oModel.setProperty("/detailView/viewNameValueState", sap.ui.core.ValueState.None);
            oModel.setProperty("/detailView/viewNameValueStateText", "");
            return;
        },
        loadAnalyticalViews: function () {
            // get all analytical views
            return GetListsData.getCDSViews(this);
        },

        onPublishFioriReport: async function () {
            let oController = this;
            let oModel = oController.getView().getModel("EndAnalyticalDetail");
            let oHeader = oModel.getProperty("/detailView");
            let sModelSection = oModel.getProperty("/Section");
            let sRouting = oModel.getProperty("/routingInformation");

            if (oHeader.Status !== "Generated") {
                Utilities.showPopupAlert(
                    oController.geti18nText("analyticalViews.end.generateView.analyticalViewNotGenerated.popop.message"), 
                    MessageBox.Icon.INFORMATION, 
                    oController.geti18nText("analyticalViews.end.publishfiorireport.tooltip"));
                return;
            }

            // Check if any background jobs are pending for generating view
            let sBackgroundJobCheckType = 
            (sModelSection === oController.PROCESS_ANALYTICAL_SECTION_ID)? 
            BackGroundJob.BACKGROUNDJOB_TYPE.PROCESS_ANALYTICAL_VIEW : 
            BackGroundJob.BACKGROUNDJOB_TYPE.CHANGE_ANALYTICAL_VIEW;

            let promise = BackGroundJob.checkStatus(
                oController, sBackgroundJobCheckType,
                { "sDataModel": sRouting.DataModel, "sViewName": sRouting.ViewName }, 
                false );

            promise.then(function(bJobRunning){
                if(bJobRunning){
                    return { then: function() {} };// Nothing to perform, break the promise chain
                }

                // Ask for the Customizing Transport, Workbench transport and Package 
                return oController._selectTransportPackage(true, true, true);
            })
            .then(function(oTransportPackageResponse){
                oModel.setProperty("/detailView/customizingTransport", oTransportPackageResponse.customizingTransport );
                oModel.setProperty("/detailView/workbenchTransport", oTransportPackageResponse.workbenchTransport );
                oModel.setProperty("/detailView/packageSelected", oTransportPackageResponse.package );    
                // send request for save
                return oController.publishFioriReport(oController);
            });
            
        },

        generateFioriUUID: function (oController){
            let oData = {
                reportId: "",
                ddView1Id: "",
                ddView2Id: ""
            };
            return new Promise(function (resolve, reject) {
                (new DMRDataService(
                    oController,
                    "PROCESS_ANALYTICS",
                    "/generateuuidSet",
                    "Generate UUID",
                    "/", // Root of the received data
                    "Generate UUID"
                ))
                    .showBusyIndicator(false)
                    .saveData(
                        false,
                        oData,
                        null, {
                        success: {
                            fCallback: function (oParams, oResponseData) {
                                resolve(oResponseData);
                            }
                        },
                        error: {
                            fCallback: function (oParams, oErrorData) {
                                reject(oErrorData);
                            }
                        }
                    });
            });
        },

        publishFioriReport: async function (oController) {
            let oModel = oController.getView().getModel("EndAnalyticalDetail");
            let oDetailsHeader = oModel.getProperty("/detailView");
            let sRouting = oModel.getProperty("/routingInformation");
            let sModelSection = oModel.getProperty("/Section");
            let bIsProcessAnalytics = (sModelSection === this.PROCESS_ANALYTICAL_SECTION_ID) ? true : false;

            // get the json for report,ddviews, ddcolumns
            let sReportConfig = FioriALPReportJson.getReportJson();
            let sDDview1 = FioriALPReportJson.getDDView1Json();
            let sDDview2 = FioriALPReportJson.getDDView2Json();

            // Maintain column level details and title based on type of analytical view
            let sDDColumn1, sDDColumn2;
            if (bIsProcessAnalytics) {
                sDDColumn1 = FioriALPReportJson.getDDPAColumn();
                sDDColumn2 = FioriALPReportJson.getDDPAColumn();
                sReportConfig.Title = "ZRDG_P_ALP_" + oDetailsHeader.ViewName;
            } else {
                sDDColumn1 = FioriALPReportJson.getDDCAColumn();
                sDDColumn2 = FioriALPReportJson.getDDCAColumn();
                sReportConfig.Title = "ZRDG_C_ALP_" + oDetailsHeader.ViewName;
            }

            // Report config details
            BusyIndicator.show();
            let sDDview1Id, sDDview2Id;
            let oRptPromise = oController.generateFioriUUID(oController);
            oRptPromise.then(function (oResponseData) {
                return oResponseData;
            })
            .then(function(oResponseData){
                // assign unique ids
                let sRptConfigId = oResponseData.reportId;
                sDDview1Id = oResponseData.ddView1Id;
                sDDview2Id = oResponseData.ddView2Id;

                sReportConfig.Id = sDDview1.EvaluationId = sDDview2.EvaluationId = sDDColumn1.EvaluationId = sDDColumn2.EvaluationId = sRptConfigId;
                sReportConfig.RequestId = oDetailsHeader.workbenchTransport;
                sReportConfig.Package = oDetailsHeader.packageSelected; 
                sReportConfig.CustRequestId = oDetailsHeader.customizingTransport;
                sReportConfig.OdataUrl = sReportConfig.OdataUrl + oDetailsHeader.ViewName + "_CDS";
                sReportConfig.OdataEntityset = oDetailsHeader.ViewName;
                sReportConfig.ViewName = oDetailsHeader.ViewName;

                return oController.generateFioriReport(oController, "/REP_CONFIGS", sReportConfig);
            })
            .then(function () {
                    // success of report creation, call DD views 1 creation
                    sDDview1.Id = sDDview1Id;
                    return oController.generateFioriReport(oController, "/DD_VIEWS", sDDview1);
                })
            .then(function () {
                // Success of DD Views 1, call DD Views 2 Creation
                sDDview2.Id = sDDview2Id;
                return oController.generateFioriReport(oController, "/DD_VIEWS", sDDview2);
            })
            .then(function () {
                // success of DD Views 2, Call DD Columns 1 Creation
                sDDColumn1.ViewId = sDDview1Id;
                return oController.generateFioriReport(oController, "/DD_COLUMNS", sDDColumn1);
            })
            .then(function () {
                // success of DD Columns 1, Call DD Columns 2 Creation
                sDDColumn2.ViewId = sDDview2Id;
                return oController.generateFioriReport(oController, "/DD_COLUMNS", sDDColumn2);
            })
            .then(function () {
                // First call the report config service
                let sMessageTitlei18n = "analyticalViews.end.publishfiorireport.tooltip";
                let sMessagei18n = "analyticalViews.end.publishfiorireport.success.text";
                
                // on success of all services, send success message
                let promiseShowPopupAlert =
                    Utilities.showPopupAlert(oController.geti18nText(sMessagei18n), MessageBox.Icon.SUCCESS, oController.geti18nText(sMessageTitlei18n));
                promiseShowPopupAlert.then(function () {
                    // navigate to middle screen
                    oController.oRouter.navTo("MainAnalytics",
                        {
                            DataModel: sRouting.DataModel,
                            Section: sModelSection,
                            ViewName: oDetailsHeader.ViewName
                        }, true);
                });
            })
            .catch(function (oErrorData) {
                BusyIndicator.hide();
                try {
                    let sCode = JSON.parse(oErrorData.responseText).error.innererror.errordetails[0].code;
                    let sResponseMessage = JSON.parse(oErrorData.responseText).error.innererror.errordetails[0].message;
                    Utilities.showPopupAlert("Error code:\n" + sCode + "\n\n" + "Error message:\n" + sResponseMessage, MessageBox.Icon.ERROR, "Error");
                } catch (error) {
                    let sGenericMessage = oErrorData.statusCode + ": " + oErrorData.message;
                    Utilities.showPopupAlert("Error " + sGenericMessage, MessageBox.Icon.ERROR, "Error");
                }
            })
            .finally(function(){
                BusyIndicator.hide();
            });

        },

        generateFioriReport: async function (oController, sServicePath, OData) {
            return new Promise(function (resolve, reject) {
                (new DMRDataService(
                    oController,
                    "PUBLISH_FIORI",
                    sServicePath,
                    "Create report header Template with fields",
                    "/", // Root of the received data
                    "Create report headerTemplate with fields"
                ))
                    .showBusyIndicator(false)
                    .saveData(
                        false,
                        OData,
                        null, {
                        success: {
                            fCallback: function () {
                                resolve();
                            }
                        },
                        error: {
                            fCallback: function (oParams, oErrorData) {
                                reject(oErrorData);
                            }
                        }
                    });
            });
        }
    });
});