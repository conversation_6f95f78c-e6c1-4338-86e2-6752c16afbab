sap.ui.define([], function () {
    "use strict";
    return {
        getReportJson: function () {
            return {
                "Id": "",
                "RequestId": "",
                "Package": "",
                "CustRequestId": "",
                "AnnotationModel": "",
                "OwnerName": "",
                "OwnerEMail": "",
                "CDSAnnotationName": "",
                "CDSAnnotationVersion": "",
                "IsActive": "1",
                "Title": "",
                "SubTitle": "",
                "Type": "1",
                "Description": "",
                "OwnerId": "",
                "OdataUrl": "/sap/opu/odata/sap/",
                "OdataEntityset": "",
                "ViewName": "",
                "ColumnName": ""
            };
        },
        getDDView1Json: function () {
            return {
                "EvaluationId": "",
                "IsActive": "1",
                "Id": "",
                "Title": "",
                "ConfigOrder": 0,
                "DataLimit": -1,
                "ThresholdMeasure": "",
                "ColorScheme": "NONE",
                "AllowTotals": "0",
                "SmartChartType": 6,
                "Type": "1"
            };
        },
        getDDView2Json: function () {
            return {
                "EvaluationId": "",
                "IsActive": "1",
                "Id": "",
                "Title": "",
                "ConfigOrder": 1,
                "DataLimit": -1,
                "ThresholdMeasure": "",
                "ColorScheme": "NONE",
                "AllowTotals": "0",
                "SmartChartType": 255,
                "Type": "1"
            };
        },
        getDDPAColumn: function () {
            return {
                "EvaluationId": "",
                "IsActive": "1",
                "ViewId": "",
                "Name": "NumberOfMDGovChangeRequests",
                "Type": 2,
                "SortBy": "NumberOfMDGovChangeRequests",
                "Visibility": 2,
                "Axis": 1,
                "SortOrder": 1,
                "Color": "",
                "ColumnsOrder": 0
            };
        },
        getDDCAColumn: function () {
            return {
                "EvaluationId": "",
                "ViewId": "",
                "Name": "NumberOfChange",
                "Type": 2,
                "SortBy": "NumberOfChange",
                "Visibility": 2,
                "Axis": 1,
                "SortOrder": 1,
                "Color": "",
                "ColumnsOrder": 0
            };
        }
    };
});


