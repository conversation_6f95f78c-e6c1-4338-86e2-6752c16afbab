sap.ui.define([
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
	"sap/ui/model/json/JSONModel",
], function (
	BaseController, JSONModel) {
	"use strict";
	return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.Administrator.ProcessAnalytics.MainAnalytics", {
		onInit: function () {
			// Retrieve the router
			this.oRouter = sap.ui.core.UIComponent.getRouterFor(this);
			this.oRouter.attachRouteMatched(this._onRouteMatched, this);
			
			let oDataModel = this.getView().getModel();
            if (!oDataModel) {
                let oJSONModel = new JSONModel();
                this.getView().setModel(oJSONModel);
            }
		},

		_onRouteMatched: function (oEvent) {
			let oModel = this.getView().getModel();
			// Set the view display column property based on current status 
			let sRouteArgs = oEvent.getParameter("arguments");
			// If a view has been selected, change the view to 3 column, else set to 2 col
			let sLayout = sRouteArgs.ViewName || sRouteArgs.isNew ? "ThreeColumnsEndExpanded":"TwoColumnsMidExpanded";
			oModel.setProperty("/viewLayout", sLayout);
		},

		onExit: function () {
			this.oRouter.detachRouteMatched(this.onRouteMatched, this);
		}

	});
});