sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
    "sap/ui/model/json/JSONModel",
    "dmr/mdg/supernova/SupernovaFJ/libs/DataService",
    "dmr/mdg/supernova/SupernovaFJ/model/GetLists",
    "dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
    "sap/m/MessageBox",
    "dmr/mdg/supernova/SupernovaFJ/model/ModelMessages",
    "dmr/mdg/supernova/SupernovaFJ/model/BackGroundJob"
], function (BaseController, JSONModel, DMRDataService, GetListsData, Utilities, MessageBox, ModelMessages, BackGroundJob) {
    "use strict";
    return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.Administrator.ProcessAnalytics.MiddleAnalyticalList", {
        PROCESS_ANALYTICAL_SECTION_ID: "processAnalyticalListView",
        CHANGE_ANALYTICAL_SECTION_ID: "changeAnalyticalListView",
        PROCESS_ANALYTICAL_TABLE_ID: "processAnalyticalTable",
        CHANGE_ANALYTICAL_TABLE_ID: "changeAnalyticalTable",
        onInit: function () {
            this.oView = this.getView();
            this.ModelMessages = ModelMessages;
            this.oRouter = this.getOwnerComponent().getRouter();
            this.oRouter.attachRouteMatched(this._onRouteMatched, this);
            let oMiddleAnalyticalList = this.getView().getModel("MiddleAnalyticalList");
            if (!oMiddleAnalyticalList) {
                let oJSONModel = new JSONModel();
                this.getView().setModel(oJSONModel, "MiddleAnalyticalList");
            }
        },
        _onRouteMatched: function (oEvent) {
            let oRouteParams = oEvent.getParameter("arguments");
            let oMiddleAnalyticalList = this.getView().getModel("MiddleAnalyticalList");
            let oPreviosRouteParam = oMiddleAnalyticalList.getProperty("/routingInformation");

            // If the selected view is not set, default to Process Views
            let oMiddleAnalyticalSection = this.getView().byId("MiddleObjectPageLayout");
            let sSelectedSection = this.getView().getLocalId(oMiddleAnalyticalSection.getSelectedSection());
            if( !oRouteParams.Section ){
                oRouteParams.Section = sSelectedSection ? sSelectedSection : this.PROCESS_ANALYTICAL_SECTION_ID;
            }
            oMiddleAnalyticalList.setProperty("/routingInformation", oRouteParams);

            // if a data model has been selected load the screen
            let oController = this;
            if(oRouteParams.DataModel && 
                (!oPreviosRouteParam  || oRouteParams.refreshViews === "X" ||
                 oPreviosRouteParam.DataModel !== oRouteParams.DataModel || 
                 (oPreviosRouteParam.isNew==="X" && oPreviosRouteParam.isNew !== oRouteParams.isNew))){
                // Clear all selections 
                this.getView().byId(this.CHANGE_ANALYTICAL_TABLE_ID).removeSelections(true);
                this.getView().byId(this.PROCESS_ANALYTICAL_TABLE_ID).removeSelections(true);

                oController.loadProcessAnalyticalList()
                .then(function(){
                    return oController.loadChangeAnalyticalList();
                })
                .then(function(){
                    oController.loadDetailHeader();
                });
            }
        },
        onSectionSelectionChange: function(oEvent){
            let oMiddleAnalyticalList = this.getView().getModel("MiddleAnalyticalList");
            let oCurrentRouteParam = oMiddleAnalyticalList.getProperty("/routingInformation");

            // Idenfity the table in view 
            let isProcessView = oEvent.getParameter("section").getId().endsWith(this.PROCESS_ANALYTICAL_SECTION_ID);
            let sTableId = isProcessView? this.PROCESS_ANALYTICAL_TABLE_ID : this.CHANGE_ANALYTICAL_TABLE_ID;
            let sSection = isProcessView? this.PROCESS_ANALYTICAL_SECTION_ID : this.CHANGE_ANALYTICAL_SECTION_ID;

            // Find the table and retrieve the selected id (if any)
            let oTable = this.getView().byId(sTableId);
            let arrSelectedContextPaths = oTable.getSelectedContextPaths();
            let sSelectedViewName = undefined;
            // return if nothing is selected
            if(arrSelectedContextPaths.length !== 0){
                sSelectedViewName = oMiddleAnalyticalList.getProperty(arrSelectedContextPaths[0]).ViewName;
            }

            this.oRouter.navTo("MainAnalytics", 
                { DataModel: oCurrentRouteParam.DataModel, Section: sSection, ViewName: sSelectedViewName }, true);
        },
        loadDetailHeader: function () {
            let oController = this;
            let oMiddleAnalyticalList = this.getView().getModel("MiddleAnalyticalList");
            let sRouting = oMiddleAnalyticalList.getProperty("/routingInformation");
            // get model header details
            let promiseCDSModels = GetListsData.getCDSModels(oController, sRouting.DataModel);
            promiseCDSModels.then(function (oData) {
                if(oData[0].FinishedAt !== ""){
                let parsedDate = new Date(oData[0].FinishedAt).toDateString();
                oData[0].FinishedAt = parsedDate;
                }
                oMiddleAnalyticalList.setProperty("/detailView", oData[0]);
            });
            return promiseCDSModels;
        },

        loadChangeAnalyticalList: function () {
            let oMiddleAnalyticalList = this.getView().getModel("MiddleAnalyticalList");
            let sRouting = oMiddleAnalyticalList.getProperty("/routingInformation");
            // get process analytical views for model
            let promiseCDSAnaHeader = GetListsData.getCDSChangeAnaHeaders(this, sRouting.DataModel, undefined);
            promiseCDSAnaHeader.then(function (oData) {
                oMiddleAnalyticalList.setProperty("/changeAnaViews", oData);
            });
            return promiseCDSAnaHeader;
        },

        loadProcessAnalyticalList: function () {
            let oMiddleAnalyticalList = this.getView().getModel("MiddleAnalyticalList");
            let sRouting = oMiddleAnalyticalList.getProperty("/routingInformation");
            // get process analytical views for model
            let promiseCDSAnaHeader = GetListsData.getCDSAnaHeaders(this, sRouting.DataModel, undefined);
            promiseCDSAnaHeader.then(function (oData) {
                oMiddleAnalyticalList.setProperty("/processAnaViews", oData);
            });

            return promiseCDSAnaHeader;
        },

        onAnalyticalViewSelectionChange: function (oEvent) {
            let oMiddleAnalyticalList = this.getView().getModel("MiddleAnalyticalList");

            // Get the currently selected section 
            let sSelectedSection = oMiddleAnalyticalList.getProperty("/routingInformation").Section;
            // Retrieve the selected view 
            let sSelectedContext = oEvent.getParameter("listItem").getBindingContextPath();
            let sSelectedPath = undefined;
            if(sSelectedContext){
                sSelectedPath = oMiddleAnalyticalList.getProperty(sSelectedContext);
            }
            this.oRouter.navTo("MainAnalytics", 
                { DataModel: sSelectedPath.Model, Section: sSelectedSection, ViewName: sSelectedPath.ViewName }, true);
        },

        onAddView: function () {

            let oMiddleAnalyticalList = this.getView().getModel("MiddleAnalyticalList");
            let sRouting = oMiddleAnalyticalList.getProperty("/routingInformation");
            this.oRouter.navTo("MainAnalytics",
                { DataModel: sRouting.DataModel, Section: sRouting.Section, ViewName: undefined, isNew: "X" }, true);
        },

        onExit: function () {
            this.oRouter.detachRouteMatched(this.onRouteMatched, this);
        },
        onGenerateModelView: async function () {
            let oController = this;
            let oModel = oController.getView().getModel("MiddleAnalyticalList");
            let oHeader = oModel.getProperty("/detailView");
            let oHeaderData = {
                Model: oHeader.Model,
                Status: oHeader.Status
            };

            if (oHeader.Status === "Generated") {
                Utilities.showPopupAlert(
                    oController.geti18nText("analyticalViews.middle.modelViewAlreadyGenerated.text", oHeader.Model),
                    MessageBox.Icon.WARNING, 
                    oController.geti18nText("analyticalViews.middle.modelViewPopup.title"));
                return;
            }

            // check for background for model exists before generating views
            let promise = BackGroundJob.checkStatus(oController, BackGroundJob.BACKGROUNDJOB_TYPE.GENERATE_MODEL_VIEWS,
                { "sDataModel": oHeader.Model }, false );

            promise.then(function(bJobRunning){
                if(bJobRunning){
                    return; // Do nothing
                }

                // Send a request to generate views
                (new DMRDataService(
                    oController,
                    "PROCESS_ANALYTICS",
                    "/modelSet",
                    "Generate Model Views",
                    "/", // Root of the received data
                    "Generate Model Views"
                ))
                .showBusyIndicator(true)
                .saveData(
                    false,
                    oHeaderData,
                    null, {
                    success: {
                        fCallback: function (oParams, oResponseData) {
                            // get model header details
                            let promiseCDSModels = GetListsData.getCDSModels(oController, oHeader.Model, function () {
                                promiseCDSModels.then(function (oData) {
                                    oModel.setProperty("/detailView", oData[0]);
                                });
                            });

                            let sMessage = oResponseData.Message;
                            let sMessageIcon = Utilities.getMessageIconBoxForType(oResponseData.MessageType);
                            Utilities.showPopupAlert(sMessage, sMessageIcon, oController.geti18nText("analyticalViews.middle.modelViewPopup.title"));
                        }
                    },
                    error: {
                        fCallback: function (oParams, oResponseData) {
                            let sMessage = oResponseData.Message;
                            Utilities.showPopupAlert(sMessage, MessageBox.Icon.ERROR, oController.geti18nText("analyticalViews.middle.modelViewPopup.title"));
                        }
                    }
                });
            });
        },
    });
});