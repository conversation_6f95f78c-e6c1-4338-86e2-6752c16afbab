sap.ui.define([
    "dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
    "sap/ui/model/json/JSONModel",
    "dmr/mdg/supernova/SupernovaFJ/model/GetLists"
], function (
    BaseController, JSONModel, GetListsData) {
    "use strict";
    return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.Administrator.ProcessAnalytics.BeginModelList", {
        onInit: function () {
            this.oView = this.getView();
            this.oRouter = this.getOwnerComponent().getRouter();
            this.oRouter.attachRouteMatched(this._onRouteMatched, this);
            let oDataModel = this.getView().getModel("DataModel");
            if (!oDataModel) {
                let oJSONModel = new JSONModel();
                this.getView().setModel(oJSONModel, "DataModel");
            }
            this._InitDataModel(this);
        },
        _onRouteMatched: function (oEvent) {
            let oRouteParams = oEvent.getParameter("arguments");
            let oModel = this.getView().getModel("DataModel");
            oModel.setProperty("/routingInformation", oRouteParams);
        },
        onModelItemPress: function (oEvent) {
            let oModelItem = oEvent.getSource().getSelectedItem().getBindingContext("DataModel").getObject();
            this.oRouter.navTo("MainAnalytics", { DataModel: oModelItem.Model}, true);
        },
        _InitDataModel: function (oController) {
            let oView = this.getView();
            let oDataModel = oView.getModel("DataModel");
            let oMasterView = oDataModel.getProperty("/masterView");
            if (oMasterView) {
                return; // Do not load again.
            }

            let promiseCDSDataModels = GetListsData.getCDSModels(oController, undefined);
            promiseCDSDataModels
            .then(function (oData) {
                oDataModel.setProperty("/masterView", oData);
            })
            .then(function(){
                let oRouteParams = oDataModel.getProperty("/routingInformation");
                // If the Data Model is specified in the url params and no item is selected in the list, select the data model
                let oList = oView.byId("dataModelsList");
                let sDataModelSelected = oRouteParams.DataModel;
                if(sDataModelSelected && oList.getSelectedItem() === null){
                    let arrListItems = oList.getItems();
                    arrListItems.forEach(element => {
                        let oData = element.getBindingContext("DataModel").getObject();
                        if(oData.Model === sDataModelSelected){
                            oList.setSelectedItem(element);
                        }
                    });            
                }        
            });
        },
        onExit: function () {
            this.oRouter.detachRouteMatched(this.onRouteMatched, this);
        }

    });
});
