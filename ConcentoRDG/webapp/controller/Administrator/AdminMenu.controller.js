sap.ui.define([
	//"sap/ui/core/mvc/Controller",
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
	"sap/ui/core/Fragment",
	"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
	"sap/m/MessageBox",
	"sap/ui/core/Popup",
	"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
	"dmr/mdg/supernova/SupernovaFJ/model/models"
], function (BaseController, Fragment, DataService, MessageBox, Popup, Utilities, RDGModels) {
	"use strict";

	return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.AdminMenu", {
		oBundle: null,
		onInit: function () {

			// this.getUsername();
			// // this.getCurrentSystem();                          
			let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
			// Load the parameters received into the model
			oRouter.attachRouteMatched(this, this.onRouteMatched, this);

			this._oAdminMenuDetails = [{
				title: this.geti18nText("emaiTemplates.title"),
				description: this.geti18nText("emailTemplates.description"),
				target: "EmailTemplates",
				enabled: true,
				icon: "arobase",
				beta: false
			},
			{
				title: this.geti18nText("analyticalViews.title.short"),
				description: this.geti18nText("analyticalViews.title"),
				target: "MainAnalytics",
				enabled: true,
				icon: "area-chart",
				beta: false
			}
		];

			let oAdminMenuModel = this.getView().getModel("adminMenuModel");
			if (!oAdminMenuModel) {
				oAdminMenuModel = new sap.ui.model.json.JSONModel();
				this.getView().setModel(oAdminMenuModel, "adminMenuModel");
			}
			oAdminMenuModel.setProperty("/items", this._oAdminMenuDetails);
		},

		onRouteMatched: function () {
			// let bHome = false;
			// if (window.location.hash.includes("RouteHome")) {
			// 	bHome = true;
			// }

			// let oHomeModel = this.getView().getModel("mainMenuModel");
			// if (oHomeModel) {
			// 	oHomeModel.setProperty("/isHomePage", bHome);
			// }
		},
		
		onAdminMenuItemPressed: function (oEvent) {
			// Identify the source of the event  [Menu or Tile]
			let sSourceElement = oEvent.getSource().getMetadata().getName();
			let sTargetScreen = null;
			let sFeatureName = null;
			switch (sSourceElement) {
			case "sap.m.MenuItem":
				sTargetScreen = oEvent.getSource().getProperty("key");
				sFeatureName = oEvent.getSource().getText();
				break;
			case "sap.m.GenericTile":
				sFeatureName = oEvent.getSource().getHeader();
				sFeatureName = sFeatureName.split("[Beta]")[0].trim();
				// Search for the header in the _featureDetails
				let oFeatureDetails = this._oAdminMenuDetails.find(function (featureDetail) {
					if (featureDetail.title === sFeatureName) {
						return true;
					}
					return false;
				});
				sTargetScreen = oFeatureDetails.target;
				break;
			default:
				return; // Do nothing
			}

			let oRDGGlobalModel = RDGModels.getRDGGlobalModel();
			let oController = this;
			let sUsername;
			this.getUsername()
				.then(function (oSapUserInfo) {
					sUsername = oSapUserInfo.Sapname;

					if (!sUsername) {
						Utilities.showPopupAlert("Please login to continue.", MessageBox.Icon.ERROR, "Logged out");
					} else if (sTargetScreen === "Not Ready") {
						Utilities.showPopupAlert("Development of " + sFeatureName + " is in the pipeline.", MessageBox.Icon.INFORMATION, "Not Available");
					} else {
						// Navigate to the target Location
						oController.getRouter().navTo(sTargetScreen);
						let oUserInfo = oRDGGlobalModel.getProperty("/userInfo");
						oUserInfo.name = sUsername;
						//oRDGGlobalModel.setProperty("/userInfo", oUserInfo);
					}

				});
		}
		
	});
});