sap.ui.define([
		//"sap/ui/core/mvc/Controller",
		"dmr/mdg/supernova/SupernovaFJ/controller/BaseController",
		"sap/ui/model/json/JSONModel",
		"dmr/mdg/supernova/SupernovaFJ/libs/DataService",
		"dmr/mdg/supernova/SupernovaFJ/model/GetLists",
		"dmr/mdg/supernova/SupernovaFJ/libs/Utilities",
		"sap/m/MessageBox",
		"dmr/mdg/supernova/SupernovaFJ/model/ModelMessages"
	],
	function (
		BaseController, JSONModel,
		DMRDataService, GetListsData, Utilities, MessageBox, ModelMessages
	) {

		let EmailController = {};
		EmailController.onInit = function() {
			let oRouter = sap.ui.core.UIComponent.getRouterFor(this);
			this.ModelMessages = ModelMessages;
			oRouter.getRoute("EmailTemplates").attachMatched(this._onRouteMatched, this);
			let oEmailTemplateModel = this.getView().getModel("ViewEmailTemplate");
			if(!oEmailTemplateModel) {
				oEmailTemplateModel = new JSONModel();
				this.getView().setModel(oEmailTemplateModel, "ViewEmailTemplate");
			}
			if(!document.lastActiveElement){
				document.lastActiveElement = undefined;
			}

			document.addEventListener("focusin", this.focusInEventHandler);
			let oSelect = this.getView().byId("idCBoxEntitiesEmail");
			oSelect.ontap = function() {
				if(!oRouter){
					Utilities.showPopupAlert("Please select a Data Model", MessageBox.Icon.WARNING, "EMAIL TEMPLATE");
				}
			};

			// Task 12689: Change request process documents ( Email template ) (UI)
			oEmailTemplateModel.setProperty("/placeHolderMode", true);
			// END Task 12689: Change request process documents ( Email template ) (UI)
		};

		EmailController.onBeforeEditorInit = function(oEvent) {
			let oConfiguration = oEvent.getParameter("configuration");
			// oConfiguration.selector = "textarea#idRichTextEditor";
			oConfiguration.customToolbar = false;
			oConfiguration.showGroupFont = false;
			oConfiguration.showGroupLink = false;
			oConfiguration.showGroupClipboard = false;
			oConfiguration.showGroupInsert = false;
			oConfiguration.toolbar = "bold italic underline strikethrough align bullist numlist outdent indent styles table searchreplace";
			oConfiguration.statusbar = true;
			oConfiguration.branding = false;
			oConfiguration.elementpath = false;
			oConfiguration.invalid_elements = "img";
			oConfiguration.plugins = "autoresize lists table searchreplace wordcount";
			tinyMCE.init(oConfiguration);
		};

		EmailController.onRichTextEditorReady = function(oEvent){
			let oSource = oEvent.getSource();
			oSource.getNativeApiTinyMCE().on("click", this.focusInEventHandler);
		};
			
		EmailController._onRouteMatched = function (oEvent) {
				let oEmailTemplateModel = this.getView().getModel("ViewEmailTemplate");

				let oRouteParams = oEvent.getParameter("arguments");
				let sWorkflowTemplate = oRouteParams.template ? oRouteParams.template : undefined;
				this.getView().getModel("ViewEmailTemplate").setProperty("/workflowTemplate", sWorkflowTemplate);

				let promiseDataModelsList =
				GetListsData.getDataModelsList(this);

				promiseDataModelsList.then(function (oData) {
					oEmailTemplateModel.setProperty("/dataModelsList", oData.results);
				});
				oEmailTemplateModel.setProperty("/templateType", "new");
				oEmailTemplateModel.setProperty("/templateDetails", {
					TEMPLATE: "",
					DESCRIPTION: "",
					SUBJECT: "",
					BODY_TEXT: "",
					WbTransport: "",
					DATAMODEL: ""
				});
			};
			
		EmailController.getPlaceHoldersList = function (oController) {
			let oEmailTemplateModel = oController.getView().getModel("ViewEmailTemplate");
			let promiseCRAttributesList =
			GetListsData.getCRAttributes(oController);

			promiseCRAttributesList.then(function (oData) {
				oEmailTemplateModel.setProperty("/CRAttributesList", oData.results);
				oController.PlaceHolderSearchList.setListData(oData.results);
			});
		};	
		EmailController.getEmailTemplatesList = function (oController, template) {
			let oEmailTemplateModel = oController.getView().getModel("ViewEmailTemplate");
			let promiseEmailTemplatesList =
			GetListsData.getEmailTemplatesList(oController);

			promiseEmailTemplatesList.then(function (oData) {
				oEmailTemplateModel.setProperty("/EmailTemplatesList", oData.results);
				if(oController.EmailTemplatesSearchList) {
					oController.EmailTemplatesSearchList.setListData(oData.results);
					if(template) {
						let oTemplateList = oEmailTemplateModel.getProperty("/EmailTemplatesList");
						let sSelectedTemplate = template;
						
						let oMatchedTemplate = oTemplateList.find((templateRecord) => {
							return templateRecord.TEMPLATE === sSelectedTemplate;
						});
						oController.EmailTemplatesSearchList.setSelectedItemByTitleText(oMatchedTemplate.TEMPLATE, oMatchedTemplate.DESCRIPTION);
					}
				}
			});
		};
			
		EmailController.onTransportPackageDialogCreated = function (oEvent) {
			let comp = oEvent.getParameter("component");
			// store the component handle 
			this._transportPackageSelectionDialog = comp;
	        	
			this.getUsername()
				.then(function (oSapUserInfo) {
					let sUsername = oSapUserInfo.Sapname;
					if (!sUsername) {
						Utilities.showPopupAlert("Please login to continue.", MessageBox.Icon.ERROR, "Logged out");
					} else {
						comp.setUser(sUsername);
					}
	
				});
		};
	
		EmailController.onSelectDataModel = function (oEvent) {
			let oView = this.getView();
			let oEmailTemplateModel = oView.getModel("ViewEmailTemplate");
			oEmailTemplateModel.setProperty("/entityList", "");
			oEmailTemplateModel.setProperty("/attributeList", "");
			oEmailTemplateModel.setProperty("/selectedEntity", "");
			oEmailTemplateModel.setProperty("/selectedAttribute", "");
			// Get the data model name 
			let sModelName = oEvent.getSource().getSelectedKey();
			oEmailTemplateModel.setProperty("/templateDetails/DATAMODEL", sModelName);
			
			let promiseEntityList =
						GetListsData.getEntitiesInDataModel(
							this,
							sModelName, // for data model selected
							"UsmdModel",
							true // filter by first level entities only
						);
					promiseEntityList.then(function (data) {
						oEmailTemplateModel.setProperty("/entityList", data);
					});
		};
		
		EmailController.onEntitySelectionChangeHandler = function (oEvent) {
			let oView = this.getView();
			let oEmailTemplateModel = oView.getModel("ViewEmailTemplate");
			oEmailTemplateModel.setProperty("/attributeList", "");
			oEmailTemplateModel.setProperty("/selectedAttribute", "");
			let sSelectedEntity = oEvent.getSource().getSelectedKey();
		
			let sDataModel = oEmailTemplateModel.getProperty("/templateDetails/DATAMODEL");
			
			let promiseAttributeList = 
				GetListsData.getEntityAttributeList(this, sDataModel, sSelectedEntity, undefined);
			promiseAttributeList.then(function(oAttributes){
				oEmailTemplateModel.setProperty("/attributeList", oAttributes);
			});
			// Task 12689: Change request process documents ( Email template ) (UI)
			oView.byId("idCBoxAttributesEmail").setEnabled(true);
		};

		EmailController.onAttributeSelectionChangeHandler = function(){
			let oView = this.getView();
			oView.byId("btnInsert").setEnabled(true);
		};
		//END Task 12689: Change request process documents ( Email template ) (UI)

		
		EmailController.onLiveChangeTemplate = function (oEvent) {
			let input = oEvent.getSource();
			input.setValue(input.getValue().toUpperCase());
		};

		// Task 12689: Change request process documents ( Email template ) (UI)
		EmailController.onLiveChangeSubject = function (){
			let oSubjectInput = this.getView().byId("idSubject");
			let sSubject = oSubjectInput.getValue();

			if(sSubject){
				oSubjectInput.setValueState("None");
			} else{
				oSubjectInput.setValueState("Error");
			}
		};
		// END Task 12689: Change request process documents ( Email template ) (UI)

		
		EmailController.onClickEntity = function(){
			let oView = this.getView();
			let oEmailTemplateModel = oView.getModel("ViewEmailTemplate");
			let sDataModel = oEmailTemplateModel.getProperty("/templateDetails/DATAMODEL");
			if(!sDataModel){
				Utilities.showPopupAlert("Please select a Data Model", MessageBox.Icon.WARNING, "EMAIL TEMPLATE");
			}
		};

		EmailController.onPlaceholdersModeSwitchChange = function(oEvent){
			let oController = this;
			let sSwitchState = oEvent.getParameter("state");
			let oEmailTemplateModel = oController.getModel("ViewEmailTemplate");

			if(sSwitchState){
				oEmailTemplateModel.setProperty("/placeHolderMode", sSwitchState);
			} else{
				oEmailTemplateModel.setProperty("/placeHolderMode", sSwitchState);
			}
		};
		//END Task 12689: Change request process documents ( Email template ) (UI)
			
		EmailController.onEmailTemplatesSearchListCreated = function (oEvent) {
			let oController = this;
			let sWorkflowTemplate;
			let oViewEmailTemplateModel = this.getView().getModel("ViewEmailTemplate");
			let comp = oEvent.getParameter("component");
			oController.EmailTemplatesSearchList = comp;

			// Complete the mapping for data 
			oController.EmailTemplatesSearchList.setDataMapping({
				"title": "TEMPLATE",
				"description": "DESCRIPTION",
				"info": undefined
			});

			oController.EmailTemplatesSearchList.setHeaderText("Email Templates");
			
			if(oViewEmailTemplateModel) {
				sWorkflowTemplate = oViewEmailTemplateModel.getProperty("/workflowTemplate");

				/**
				 * If the email template are started from workflow (with an intent to edit or update)
				 * Make the search list visible, but disable the add and delete button
				 * for the email template feature. 
				 */
				this.EmailTemplatesSearchList._searchList.setVisible(true);
				if(!sWorkflowTemplate) {
					this.EmailTemplatesSearchList.setButtonInfo({
						icon: "sap-icon://add",
						toolTip: "New Template"
					});
				}
			}
			oController.getEmailTemplatesList(oController, sWorkflowTemplate);
			oController.EmailTemplatesSearchList.attachSelectionChange(oController.onSelectTemplate, oController);
			oController.EmailTemplatesSearchList.attachActionPressed(oController.onCreateTemplate, oController);
		};
		
		EmailController.onPlaceHolderSearchListCreated = function (oEvent) {
			let oController = this;
			let comp = oEvent.getParameter("component");
			oController.PlaceHolderSearchList = comp;

			// Complete the mapping for data 
			oController.PlaceHolderSearchList.setDataMapping({
				"title": "FieldName",
				"description": "Description",
				"info": undefined
			});

			oController.PlaceHolderSearchList.setHeaderText("Placeholders");
			oController.getPlaceHoldersList(oController);
			oController.PlaceHolderSearchList.attachSelectionChange(oController.onSelectPlaceHolder, oController);
		};
		
		EmailController.onSelectTemplate = function(oEvent) {
			let oView = this.getView();
			let oEmailTemplateModel = oView.getModel("ViewEmailTemplate");
			let selectedTemplate = oEvent.getParameters().data.TEMPLATE;
			let promiseEmailTemplateDetails = 
			GetListsData.getEmailTemplateDetails(this, selectedTemplate);
		
			promiseEmailTemplateDetails.then((oData) => {
				oEmailTemplateModel.setProperty("/templateDetails", oData.results[0]);
				oEmailTemplateModel.setProperty("/arrTemplatePlaceholders", oData.results[0].EMAILTEMPLATE2PLACEHOLDERSNAV.results);
				oEmailTemplateModel.setProperty("/templateType", "existing");
				oEmailTemplateModel.setProperty("/templateType", "existing");
				let oDatamodelComboBox = sap.ui.getCore().byId(oView.getId()+"--idEmailTemplateComboBox");
				// Force Display the list content with the updated data
				oDatamodelComboBox.syncPickerContent();
				// trigger the selection change event if an item was selected
				let oSelectedDatamodel = oDatamodelComboBox.getSelectedItem();
				if(oSelectedDatamodel) {
					oDatamodelComboBox.fireSelectionChange({selectedItem: oSelectedDatamodel});
				}
				// Task 12689: Change request process documents ( Email template ) (UI)
				this.onLiveChangeSubject();
				// END Task 12689: Change request process documents ( Email template ) (UI)
			});
      	};
		
		EmailController.onCreateTemplate = function () {
			let oView = this.getView();
			let oEmailTemplateModel = oView.getModel("ViewEmailTemplate");
			oEmailTemplateModel.setProperty("/templateDetails", {});
			oEmailTemplateModel.setProperty("/templateType", "new");
			this.EmailTemplatesSearchList.removeSelectedItem();
			this.onLiveChangeSubject();
		};
		
		EmailController.onSaveTemplate = function() {
			let oView = this.getView();
			let oController = this;
			let oEmailTemplateModel = oView.getModel("ViewEmailTemplate");
			let templateDetails = oEmailTemplateModel.getProperty("/templateDetails");
			let sTemplateType = oEmailTemplateModel.getProperty("/templateType");
			let arrTemplatePlaceholders = oEmailTemplateModel.getProperty("/arrTemplatePlaceholders");
			if(sTemplateType === "new" || !arrTemplatePlaceholders)
			{
				arrTemplatePlaceholders = [];
			}
	
			oController._selectTransportPackage(false, true, false)
			.then(function(oSelectedTransport) {
				let oData = {
					TEMPLATE: templateDetails.TEMPLATE,
					DATAMODEL: templateDetails.DATAMODEL,
					DESCRIPTION: templateDetails.DESCRIPTION,
					SUBJECT: templateDetails.SUBJECT,
					BODY_TEXT: templateDetails.BODY_TEXT,
					WbTransport: oSelectedTransport.workbenchTransport,
					PACKAGE: " ",
					MESSAGE: " ",
					MESSAGETYPE: " ",
					MESSAGECLASS: " ",
					EMAILTEMPLATE2PLACEHOLDERSNAV:
						arrTemplatePlaceholders
				};
				
				(new DMRDataService(
					oController,
					"EMAIL_TEMPLATE",
					"/EMAILTEMPLATESet",
					"saveTemplate",
					"/", // Root of the received data
					"Save Template"
				))
				.showBusyIndicator(true)
				.saveData(
					false,
					oData,
					null, {
						success: {
							fCallback: function (oParams, oResponseData) {
								if(oResponseData.MESSAGETYPE === "S"){
									Utilities.showPopupAlert(oResponseData.MESSAGE, MessageBox.Icon.SUCCESS, "EMAIL TEMPLATE SAVE SUCCESS")
									.then(function () {
										oController.getEmailTemplatesList(oController, templateDetails.TEMPLATE);
									});
								}
								else
								{
									Utilities.showPopupAlert(oResponseData.MESSAGE, MessageBox.Icon.ERROR, "EMAIL TEMPLATE SAVE FAIL");
								}
							},
							oParam: this
						},
						error: {
							fCallback: function () {
								Utilities.showPopupAlert("Email Template could not be saved/updated.", MessageBox.Icon.ERROR, "EMAIL TEMPLATE SAVE ERROR");
							}
						}
					}
				);
			});
		};

		EmailController._selectTransportPackage = function (bSelectCustomizing, bSelectWorkbench, bSelectPackage) {
		
			// Check if the component has not been created and stored, return with rejection
			if (!this._transportPackageSelectionDialog) {
				// If the component is not yet available, return a rejected promise
				return Promise.reject("Transport Package Selection Dialog is not ready.");
			}
			const oView = this.getView();
			const oEmailTemplateModel = oView.getModel("ViewEmailTemplate");

			// Retrieve necessary properties from the model
			const sPreselectCustomizingTr = oEmailTemplateModel.getProperty("/customizingTransport");
			const sPreselectWorkbenchTr = oEmailTemplateModel.getProperty("/workbenchTransport");
			const sPreselectPackage = oEmailTemplateModel.getProperty("/packageSelected");

			// Call the open() method on the component
			let promise = this._transportPackageSelectionDialog.open(
					bSelectCustomizing,
					sPreselectCustomizingTr,
					bSelectWorkbench,
					sPreselectWorkbenchTr,
					bSelectPackage,
					sPreselectPackage,
					true // Enable filtering based on package class
				);
			let returnPromise = promise.then(function(oResponseInfo){
				oEmailTemplateModel.setProperty("/customizingTransport", oResponseInfo.customizingTransport);
				oEmailTemplateModel.setProperty("/workbenchTransport", oResponseInfo.workbenchTransport);
				oEmailTemplateModel.setProperty("/packageSelected", oResponseInfo.package);
				return oResponseInfo;
			});

			return returnPromise;
		};

	
		EmailController.onSelectPlaceHolder = function(oEvent) {
			let sTextToInsert = "{{"+oEvent.getParameters().data.FieldName + "}}";

			this._InsertPlaceHolder(this, sTextToInsert);
			
			this.PlaceHolderSearchList.removeSelectedItem();
      	};

		EmailController.onInsertAttribute = function(){
			let oView = this.getView();
			let oEmailTemplateModel = oView.getModel("ViewEmailTemplate");
			let selectedEntity = oEmailTemplateModel.getProperty("/selectedEntity");
			let selectedAttribute = oEmailTemplateModel.getProperty("/selectedAttribute");
			// Task 12689: Change request process documents ( Email template ) (UI)
			let sSwitchMode = oEmailTemplateModel.getProperty("/placeHolderMode");
			let sTextToInsert = "{{"+selectedEntity+":"+selectedAttribute + (sSwitchMode ? "" : ":OLD" ) + "}}";
			// END Task 12689: Change request process documents ( Email template ) (UI)

			this._InsertPlaceHolder(this, sTextToInsert);
			
			oEmailTemplateModel.setProperty("/selectedEntity", undefined);
			oEmailTemplateModel.setProperty("/selectedAttribute", undefined);
			// Task 12689: Change request process documents ( Email template ) (UI)
			oView.byId("btnInsert").setEnabled(false);
			oView.byId("idCBoxAttributesEmail").setEnabled(false);
		};

		EmailController.focusInEventHandler = function(event){
			let target = event.target;

			if(target.id.includes("idSubject") || target.parentElement?.id === "tinymce" ){
				document.lastActiveElement = target;
			} else if( target.id.includes("idEmailTemplateComboBox") || target.id.includes("idTemplate") || target.id.includes("idTemplateDescription")) {
				document.lastActiveElement = undefined;
			} 
		};

		EmailController._InsertPlaceHolder = function(oController, sText) {
			if(	!document.lastActiveElement || (
				!document.lastActiveElement?.id.includes("idSubject") && 
				!(document.lastActiveElement?.parentElement?.id === "tinymce"))){
				sap.m.MessageBox.show(
					"Placeholders can only be added to Email Subject and Body.\nMove the cursor to the position you want to add the placeholder.", {
						icon: sap.m.MessageBox.Icon.ERROR,
						title: "Cannot add Placeholder"
					}
				);
				return;
			}

			sText = " " + sText;
			if(document.lastActiveElement?.id.includes("idSubject")){
				let sTextSubject = oController.byId("idSubject");
				let curPos = sTextSubject.getFocusInfo().selectionStart;
				let txtSubject = sTextSubject.getValue();
				sTextSubject.setValue(txtSubject.slice(0, curPos) + sText + txtSubject.slice(curPos));
			} else if(document.lastActiveElement?.parentElement?.id === "tinymce") {
				oController.insertContentIntoRichTextEditor(sText);
			}
		};

      	EmailController.insertContentIntoRichTextEditor = function(sStringContent) {
      		// Get the native api for the editor
			let richEditorObject = this.byId("idRichTextEditor");
			let richEditorNative = richEditorObject.getNativeApi();
			richEditorNative.insertContent(sStringContent);
      	};
      	
  		EmailController.createNewEmailTemplateEnabled= function (sDataModel, sTemplate, sSubject){
  			if (!sDataModel) {
				return false;
			} else if (!sTemplate) {
				return false;
			} else if (!sSubject) {
				return false;
			}
			return true;
  		};
  		
  		EmailController.onDeleteTemplate = function() {
			let oView = this.getView();
			let oController = this;
			let oEmailTemplateModel = oView.getModel("ViewEmailTemplate");
			let templateDetails = oEmailTemplateModel.getProperty("/templateDetails");
			let sTemplateType = oEmailTemplateModel.getProperty("/templateType");
			let arrTemplatePlaceholders = oEmailTemplateModel.getProperty("/arrTemplatePlaceholders");
			if(sTemplateType === "new" || !arrTemplatePlaceholders)
			{
				arrTemplatePlaceholders = [];
			}
	
			this._selectTransportPackage(false, true, false)
			.then(function(oSelectedTransport) {
				let oData = {
					TEMPLATE: templateDetails.TEMPLATE,
					DATAMODEL: templateDetails.DATAMODEL,
					DESCRIPTION: templateDetails.DESCRIPTION,
					SUBJECT: templateDetails.SUBJECT,
					BODY_TEXT: templateDetails.BODY_TEXT,
					WbTransport: oSelectedTransport.workbenchTransport,
					PACKAGE: " ",
					MESSAGE: " ",
					MESSAGETYPE: " ",
					MESSAGECLASS: " ",
					DELETE: "X",
					EMAILTEMPLATE2PLACEHOLDERSNAV:
						arrTemplatePlaceholders
				};
				
				(new DMRDataService(
					oController,
					"EMAIL_TEMPLATE",
					"/EMAILTEMPLATESet",
					"deleteTemplate",
					"/", // Root of the received data
					"Delete Template"
				))
				.showBusyIndicator(true)
				.saveData(
					false,
					oData,
					null, {
						success: {
							fCallback: function (oParams, oResponseData) {
								if(oResponseData.MESSAGETYPE === "S"){
									Utilities.showPopupAlert(oResponseData.MESSAGE, MessageBox.Icon.SUCCESS, "EMAIL TEMPLATE DELETE SUCCESS")
									.then(function () {
										oController.getEmailTemplatesList(oController, undefined);
										oController.EmailTemplatesSearchList.removeSelectedItem();
										oEmailTemplateModel.setProperty("/templateDetails", "");
									});
								}
								else
								{
									Utilities.showPopupAlert(oResponseData.MESSAGE, MessageBox.Icon.ERROR, "EMAIL TEMPLATE DELETE FAIL");
								}
							},
							oParam: this
						},
						error: {
							fCallback: function () {
								Utilities.showPopupAlert("Email Template could not be deleted.", MessageBox.Icon.ERROR, "EMAIL TEMPLATE DELETE ERROR")
								.then(function () {
								});
						}
					}
				});
			});
      	};

		EmailController.onExit = function () {
			document.removeEventListener("focusin", this.focusInEventHandler);
		};
		
		return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.Administrator.EmailTemplates", EmailController);
	});