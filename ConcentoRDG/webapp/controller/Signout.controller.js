sap.ui.define([
	//"sap/ui/core/mvc/Controller",
	"dmr/mdg/supernova/SupernovaFJ/controller/BaseController"
], function (BaseController) {
	"use strict";

	return BaseController.extend("dmr.mdg.supernova.SupernovaFJ.controller.Home", {
		onInit: function () {
			this.getView().setModel(new sap.ui.model.json.JSONModel(), "loggedOutModel");
			let oModel = this.getView().getModel("loggedOutModel");
			oModel.setProperty("/loggedOut", false);
			let win = window.open(window.location.origin + "/services/userapi/logout", "SignOFF",
				"popup");
			win.focus();
			setTimeout(function () {
				win.close();
			}, 3000);
		}
	});
});