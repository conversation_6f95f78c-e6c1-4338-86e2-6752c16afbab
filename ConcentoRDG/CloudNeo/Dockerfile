FROM node:20-bullseye AS nodebuild

RUN apt-get update -y 
RUN apt-get upgrade -y
RUN apt-get install make -y
RUN apt-get install -y node-rimraf

RUN npm install -g npm@latest

WORKDIR /usr/scr/webapp/

COPY . .
COPY package*.json ./

RUN ls

RUN npm install
RUN npm run build:regular

WORKDIR /usr/scr/webapp/CloudNeo

RUN npm install
RUN npm run build


FROM ppiper/neo-cli:latest as neo

ARG HOST
ARG USR
ARG PASS
ARG SUBACCOUNT

ENV HOST=$HOST
ARG USR=$USR
ARG PASS=$PASS
ARG SUBACCOUNT=$SUBACCOUNT

WORKDIR /usr/pipeline

COPY --from=nodebuild /usr/scr/webapp/CloudNeo /usr/pipeline

RUN ls -l -h

RUN neo.sh delete-mta --host $HOST --user $USR --password $PASS --account $SUBACCOUNT --id concentoRDGMTA

RUN neo.sh deploy-mta --host $HOST --user $USR --password $PASS --account $SUBACCOUNT --source /usr/pipeline/mta_archives/archive.mtar



