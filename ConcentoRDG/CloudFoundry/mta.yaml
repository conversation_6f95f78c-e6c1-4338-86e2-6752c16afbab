_schema-version: "3.2"
ID: ConcentoRDGMTA
version: 1.0.0

modules:
- name: ConcentoRDGMTA-destination-content
  type: com.sap.application.content
  requires:
  - name: ConcentoRDGMTA-destination-service
    parameters:
      content-target: true
  - name: ConcentoRDGMTA_html_repo_host
    parameters:
      service-key:
        name: ConcentoRDGMTA_html_repo_host-key
  - name: uaa_ConcentoRDGMTA
    parameters:
      service-key:
        name: uaa_ConcentoRDGMTA-key
  parameters:
    content:
      instance:
        destinations:
        - Name: ConcentoRDG_ConcentoRDGMTA_html_repo_host
          ServiceInstanceName: ConcentoRDGMTA-html5-app-host-service
          ServiceKeyName: ConcentoRDGMTA_html_repo_host-key
          sap.cloud.service: ConcentoRDG
        - Authentication: OAuth2UserTokenExchange
          Name: ConcentoRDG_uaa_ConcentoRDGMTA
          ServiceInstanceName: ConcentoRDGMTA-xsuaa-service
          ServiceKeyName: uaa_ConcentoRDGMTA-key
          sap.cloud.service: ConcentoRDG
        existing_destinations_policy: ignore
  build-parameters:
    no-source: true
- name: supernovafj
  type: html5
  path: ../
  build-parameters:
    build-result: CloudFoundry/dist
    builder: custom
    commands:
    - npm install
    - npm run build:cf
    - npm run createresource
    - npm run zip:cf
    supported-platforms: []
- name: ConcentoRDGMTA-app-content
  type: com.sap.application.content
  path: .
  requires:
  - name: ConcentoRDGMTA_html_repo_host
    parameters:
      content-target: true
  build-parameters:
    build-result: resources
    #requires:
    #- artifacts:
    #  - supernovafj.zip
    #  name: supernovafj
    #  target-path: resources/
resources:
- name: ConcentoRDGMTA-destination-service
  type: org.cloudfoundry.managed-service
  parameters:
    config:
      HTML5Runtime_enabled: true
      init_data:
        instance:
          destinations:
          - Authentication: NoAuthentication
            Name: ui5
            ProxyType: Internet
            Type: HTTP
            URL: https://ui5.sap.com/1.108.30/
          existing_destinations_policy: update
      version: 1.0.0
    service: destination
    service-name: ConcentoRDGMTA-destination-service
    service-plan: lite
- name: ConcentoRDGMTA_html_repo_host
  type: org.cloudfoundry.managed-service
  parameters:
    config:
    service: html5-apps-repo
    service-name: ConcentoRDGMTA-html5-app-host-service
    service-plan: app-host
- name: uaa_ConcentoRDGMTA
  type: org.cloudfoundry.managed-service
  parameters:
    config:
    path: ./xs-security.json
    service: xsuaa
    service-name: ConcentoRDGMTA-xsuaa-service
    service-plan: application
parameters:
  deploy_mode: html5-repo

