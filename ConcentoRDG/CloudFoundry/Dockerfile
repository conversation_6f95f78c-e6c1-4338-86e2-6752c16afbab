FROM node:20-bullseye

ARG ENDPOINT
ARG USR
ARG PASS
ARG ORG
ARG SAPCE

ENV ENDPOINT=$ENDPOINT
ARG USR=$USR
ARG PASS=$PASS
ARG ORG=$ORG
ARG SPACE=$SPACE

WORKDIR /usr/scr/webapp/

RUN echo 'debconf debconf/frontend select Noninteractive' | debconf-set-selections

RUN apt-get update -y && apt-get upgrade -y && apt-get install -f -y -q ca-certificates jq
RUN apt-get install make -y && apt-get install -y node-rimraf
RUN apt-get install wget -y
RUN apt-get install apt-utils -y

# RUN wget -q -O - https://packages.cloudfoundry.org/debian/cli.cloudfoundry.org.key | apt-key add -
RUN wget -q -O /etc/apt/trusted.gpg.d/cloudfoundry-cli.gpg https://packages.cloudfoundry.org/debian/cli.cloudfoundry.org.key
RUN echo "deb [trusted=yes] https://packages.cloudfoundry.org/debian stable main" > /etc/apt/sources.list.d/cloudfoundry-cli.list

RUN apt-get update -y
RUN apt-get upgrade -y
RUN apt-get install -f -y
# RUN apt-get install cf8-cli=8.6.0 -y -f
RUN apt-get update && apt-get install cf8-cli -y -f

# RUN echo "deb [trusted=yes] https://packages.cloudfoundry.org/debian stable main" > /etc/apt/sources.list.d/cloudfoundry-cli.list
# RUN apt-get update -y && apt-get upgrade -y  && apt-get install -y cf8-cli && cf install-plugin multiapps -f
# RUN apt-get update -y 
# RUN apt-get upgrade -y  
# RUN apt-get install -y -f cf8-cli 

RUN npm install -g npm@latest

RUN cf install-plugin multiapps -f

RUN cf login -a $ENDPOINT -u $USR -p $PASS -o "$ORG" -s $SPACE 

COPY . .
COPY package*.json ./

RUN npm install
RUN npm run build

RUN ls

WORKDIR /usr/scr/webapp/CloudFoundry
RUN ls

COPY ./CloudFoundry .
COPY ./CloudFoundry/package*.json ./
RUN ls

RUN npm install

COPY ./CloudFoundry/mta*.yaml ../

WORKDIR /usr/scr/webapp/CloudFoundry

RUN npm run build

# # RUN npm run undeploy

RUN npm run deploy

ENTRYPOINT ["ls"]
