{"name": "cloudfoundry", "version": "1.1.0", "description": "", "private": true, "devDependencies": {"mbt": "^1.2.24", "npm-build-zip": "^1.0.4", "rimraf": "^5.0.1"}, "scripts": {"Begin_Script_CloudFoundry": "NOT TO RUN=========================================", "choco": "choco install make", "multiapps": "cf install-plugin -r CF-Community multiapps", "clean": "rimraf resources mta_archives dist ", "build": "rimraf resources mta_archives && mbt build --mtar archive", "deploy": "cf deploy mta_archives/archive.mtar -f --retries 1", "undeploy": "cf undeploy ConcentoRDGMTA -f --delete-services --delete-service-keys --delete-service-brokers", "resourcesFolder": "mkdir resources", "End_Script_CloudFoundry": "=========================================NOT TO RUN"}, "ui5": {"dependencies": ["mbt", "<PERSON><PERSON><PERSON>"]}}