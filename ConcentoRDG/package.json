{"name": "SupernovaFJ", "version": "0.0.1", "description": "", "private": true, "devDependencies": {"@sap/ui5-builder-webide-extension": "1.1.9", "@sap/ux-ui5-tooling": "^1.15.1", "@stylistic/eslint-plugin-js": "^2.9.0", "@ui5/builder": "4.0.3", "@ui5/cli": "^4.0.8", "@ui5/fs": "4.0.1", "@ui5/logger": "4.0.1", "eslint": "9.12.0", "npm-build-zip": "^1.0.4", "serve": "^14.2.3", "ui5-task-zipper": "^3.3.0"}, "scripts": {"lint": "eslint --max-warnings 29 --config eslint.config.mjs webapp", "lint:fix": "eslint --fix .", "start": "fiori run --open index.html", "start:dist": "ui5 serve --config=ui5.yaml -o index.html", "build:regular": "ui5 build preload --config=ui5-deploy.yaml --exclude-task=generateStandaloneAppBundle --clean-dest --dest dist", "build:cf": "ui5 build preload  --config ui5-deploy.yaml --exclude-task=generateStandaloneAppBundle --clean-dest --dest ./CloudFoundry/dist", "build": "ui5 build self-contained --include-all-dependencies --exclude-task=generateStandaloneAppBundle --config ui5-deploy.yaml --clean-dest --dest ./CloudFoundry/dist", "createresource": "npx mkdirp ./CloudFoundry/resources", "zip:cf": "npm-build-zip --src ./CloudFoundry/dist --dst ./CloudFoundry/resources -n supernovafj --name_only true", "deleteDbgFile:cf": "rimraf --glob ./CloudFoundry/dist/**-dbg.controller.js && rimraf --glob ./CloudFoundry/dist/**-dbg.js && rimraf --glob ./CloudFoundry/dist/controller/**-dbg.controller.js && rimraf --glob ./CloudFoundry/dist/controller/**-dbg.js && rimraf --glob ./CloudFoundry/dist/controller/Administrator/**-dbg.controller.js && rimraf --glob ./CloudFoundry/dist/controller/Administrator/**-dbg.js && rimraf --glob ./CloudFoundry/dist/controller/DataModel/**-dbg.controller.js && rimraf --glob ./CloudFoundry/dist/controller/DataModel/**-dbg.js && rimraf --glob ./CloudFoundry/dist/controller/Interfaces/**-dbg.controller.js && rimraf --glob ./CloudFoundry/dist/controller/Interfaces/**-dbg.js && rimraf --glob ./dist/controller/Interfaces/Parsers/**-dbg.controller.js && rimraf --glob ./dist/controller/Interfaces/Parsers/**-dbg.js && rimraf --glob ./CloudFoundry/dist/controller/MDConsolidation/**-dbg.controller.js && rimraf --glob ./CloudFoundry/dist/controller/MDConsolidation/**-dbg.js && rimraf --glob ./CloudFoundry/dist/controller/Notifications/**-dbg.controller.js && rimraf --glob ./CloudFoundry/dist/controller/Notifications/**-dbg.js && rimraf --glob ./CloudFoundry/dist/controller/UIProperties/**-dbg.controller.js && rimraf --glob ./CloudFoundry/dist/controller/UIProperties/**-dbg.js && rimraf --glob ./CloudFoundry/dist/controller/Workflow/**-dbg.controller.js && rimraf --glob ./CloudFoundry/dist/controller/Workflow/**-dbg.js && rimraf --glob ./CloudFoundry/dist/controls/**-dbg.controller.js && rimraf --glob ./CloudFoundry/dist/controls/**-dbg.js", "delFolder:cf": "rimraf --glob ./CloudFoundry/dist/test-resources/*", "deleteDbgFile:neo": "rimraf --glob ./dist/**-dbg.controller.js && rimraf --glob ./dist/**-dbg.js && rimraf --glob ./dist/controller/**-dbg.controller.js && rimraf --glob ./dist/controller/**-dbg.js && rimraf --glob ./dist/controller/Administrator/**-dbg.controller.js && rimraf --glob ./dist/controller/Administrator/**-dbg.js && rimraf --glob ./dist/controller/DataModel/**-dbg.controller.js && rimraf --glob ./dist/controller/DataModel/**-dbg.js &&  rimraf --glob ./dist/controller/Interfaces/**-dbg.controller.js && rimraf --glob ./dist/controller/Interfaces/**-dbg.js && rimraf --glob ./dist/controller/Interfaces/Parsers/**-dbg.controller.js && rimraf --glob ./dist/controller/Interfaces/Parsers/**-dbg.js && rimraf --glob ./dist/controller/MDConsolidation/**-dbg.controller.js && rimraf --glob ./dist/controller/MDConsolidation/**-dbg.js && rimraf --glob ./dist/controller/Notifications/**-dbg.controller.js && rimraf --glob ./dist/controller/Notifications/**-dbg.js && rimraf --glob ./dist/controller/UIProperties/**-dbg.controller.js && rimraf --glob ./dist/controller/UIProperties/**-dbg.js && rimraf --glob ./dist/controller/Workflow/**-dbg.controller.js && rimraf --glob ./dist/controller/Workflow/**-dbg.js && rimraf --glob ./dist/controls/**-dbg.controller.js && rimraf --glob ./dist/controls/**-dbg.js"}, "ui5": {"dependencies": ["@sap/ux-ui5-tooling", "ui5-task-zipper", "@ui5/cli", "@sap/ui5-builder-webide-extension", "@ui5/builder", "@ui5/fs", "@ui5/logger"]}, "dependencies": {"@openui5/sap.ui.core": "^1.134.1"}}