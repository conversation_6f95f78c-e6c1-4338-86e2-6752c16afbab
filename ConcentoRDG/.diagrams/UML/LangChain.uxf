<diagram program="umletino" version="15.1"><zoom_level>15</zoom_level><help_text>JWT token generation in ABAP ? </help_text><element><id>UMLGeneric</id><coordinates><x>60</x><y>15</y><w>150</w><h>45</h></coordinates><panel_attributes>RDG (Browser)</panel_attributes><additional_attributes></additional_attributes></element><element><id>UMLGeneric</id><coordinates><x>255</x><y>15</y><w>150</w><h>45</h></coordinates><panel_attributes>CoPilot</panel_attributes><additional_attributes></additional_attributes></element><element><id>UMLGeneric</id><coordinates><x>450</x><y>15</y><w>150</w><h>45</h></coordinates><panel_attributes>Lang* Server</panel_attributes><additional_attributes></additional_attributes></element><element><id>UMLGeneric</id><coordinates><x>645</x><y>15</y><w>150</w><h>45</h></coordinates><panel_attributes>RDG OData</panel_attributes><additional_attributes></additional_attributes></element><element><id>UMLGeneric</id><coordinates><x>825</x><y>15</y><w>150</w><h>45</h></coordinates><panel_attributes>ZenDesk</panel_attributes><additional_attributes></additional_attributes></element><element><id>Relation</id><coordinates><x>105</x><y>45</y><w>60</w><h>705</h></coordinates><panel_attributes>lt=.</panel_attributes><additional_attributes>14;14;10;450</additional_attributes></element><element><id>Relation</id><coordinates><x>300</x><y>45</y><w>60</w><h>705</h></coordinates><panel_attributes>lt=.</panel_attributes><additional_attributes>14;14;10;450</additional_attributes></element><element><id>Relation</id><coordinates><x>495</x><y>45</y><w>60</w><h>705</h></coordinates><panel_attributes>lt=.</panel_attributes><additional_attributes>14;14;10;450</additional_attributes></element><element><id>Relation</id><coordinates><x>690</x><y>45</y><w>60</w><h>705</h></coordinates><panel_attributes>lt=.</panel_attributes><additional_attributes>14;14;10;450</additional_attributes></element><element><id>Relation</id><coordinates><x>870</x><y>45</y><w>60</w><h>705</h></coordinates><panel_attributes>lt=.</panel_attributes><additional_attributes>14;14;20;450</additional_attributes></element><element><id>Relation</id><coordinates><x>120</x><y>135</y><w>420</w><h>60</h></coordinates><panel_attributes>lt=-&gt;&gt;
Load Component</panel_attributes><additional_attributes>10;20;260;20</additional_attributes></element><element><id>Relation</id><coordinates><x>120</x><y>285</y><w>420</w><h>75</h></coordinates><panel_attributes>lt=&lt;-
on-chat-start
Function Call (auth)</panel_attributes><additional_attributes>10;20;260;20</additional_attributes></element><element><id>Relation</id><coordinates><x>120</x><y>345</y><w>615</w><h>60</h></coordinates><panel_attributes>lt=-&gt;&gt;
Get User Info</panel_attributes><additional_attributes>10;20;390;20</additional_attributes></element><element><id>Relation</id><coordinates><x>120</x><y>420</y><w>615</w><h>60</h></coordinates><panel_attributes>lt=&lt;&lt;-
Encrypted Information</panel_attributes><additional_attributes>10;20;390;20</additional_attributes></element><element><id>UMLGeneric</id><coordinates><x>690</x><y>345</y><w>30</w><h>120</h></coordinates><panel_attributes>bg=red
</panel_attributes><additional_attributes></additional_attributes></element><element><id>UMLClass</id><coordinates><x>720</x><y>315</y><w>165</w><h>180</h></coordinates><panel_attributes>halign=left
Encrypt 
  User Name, 
  Domain, 
  Access Key, 
  Client ID etc 
and return.</panel_attributes><additional_attributes></additional_attributes></element><element><id>UMLGeneric</id><coordinates><x>105</x><y>150</y><w>30</w><h>420</h></coordinates><panel_attributes>
bg=yellow</panel_attributes><additional_attributes></additional_attributes></element><element><id>Relation</id><coordinates><x>120</x><y>480</y><w>420</w><h>60</h></coordinates><panel_attributes>lt=-&gt;
 Callback with User Info</panel_attributes><additional_attributes>10;20;260;20</additional_attributes></element><element><id>UMLFrame</id><coordinates><x>60</x><y>105</y><w>960</w><h>480</h></coordinates><panel_attributes>Launch and Authenticate</panel_attributes><additional_attributes></additional_attributes></element><element><id>Relation</id><coordinates><x>300</x><y>210</y><w>240</w><h>60</h></coordinates><panel_attributes>lt=&lt;-
Load (if allowed)</panel_attributes><additional_attributes>10;20;140;20</additional_attributes></element><element><id>UMLClass</id><coordinates><x>495</x><y>135</y><w>375</w><h>60</h></coordinates><panel_attributes>Verify if the request origin is allowed. 
Load component if allowed. 
transparency=80
bg=yellow

</panel_attributes><additional_attributes></additional_attributes></element><element><id>UMLGeneric</id><coordinates><x>1020</x><y>15</y><w>150</w><h>60</h></coordinates><panel_attributes>Smple Flask 
Server</panel_attributes><additional_attributes></additional_attributes></element><element><id>UMLNote</id><coordinates><x>960</x><y>270</y><w>210</w><h>105</h></coordinates><panel_attributes>JWT token generation 
in ABAP !!! 

bg=blue</panel_attributes><additional_attributes></additional_attributes></element></diagram>