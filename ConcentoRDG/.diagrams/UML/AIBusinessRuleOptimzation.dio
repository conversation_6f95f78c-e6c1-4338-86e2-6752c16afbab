<mxfile host="65bd71144e">
    <diagram id="aDaqcGh1qzU2usNwkOvx" name="Page-1">
        <mxGraphModel dx="1206" dy="916" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="3" value="Analyze the rules for optimization" style="rounded=1;whiteSpace=wrap;html=1;flipV=1;" vertex="1" parent="1">
                    <mxGeometry x="310" y="173" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="Identify and report optimizations" style="rounded=1;whiteSpace=wrap;html=1;flipV=1;" vertex="1" parent="1">
                    <mxGeometry x="310" y="275" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="" style="edgeStyle=none;html=1;fontSize=15;" edge="1" parent="1" source="5" target="28">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="31" value="" style="edgeStyle=none;html=1;fontSize=15;" edge="1" parent="1" source="5" target="28">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="&lt;font style=&quot;font-size: 9px;&quot;&gt;(User Action)&lt;/font&gt;&lt;br&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;Generate Optimized Design Document?&lt;/font&gt;" style="rhombus;whiteSpace=wrap;html=1;flipV=1;" vertex="1" parent="1">
                    <mxGeometry x="295" y="363" width="150" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="" style="shape=actor;whiteSpace=wrap;html=1;fontSize=11;" vertex="1" parent="1">
                    <mxGeometry x="110" y="60" width="40" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="User Input Design Document" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;flipV=1;" vertex="1" parent="1">
                    <mxGeometry x="310" y="70" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="&lt;font style=&quot;font-size: 15px; font-weight: normal;&quot;&gt;Yes&lt;/font&gt;" style="text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
                    <mxGeometry x="439" y="380" width="50" height="25" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="" style="endArrow=classic;html=1;fontSize=15;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="9" target="3">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="360" y="390" as="sourcePoint"/>
                        <mxPoint x="410" y="340" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="" style="endArrow=classic;html=1;fontSize=15;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="3" target="4">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="360" y="390" as="sourcePoint"/>
                        <mxPoint x="410" y="340" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="" style="endArrow=classic;html=1;fontSize=15;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="4" target="5">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="360" y="390" as="sourcePoint"/>
                        <mxPoint x="410" y="340" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="32" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=15;" edge="1" parent="1" source="18" target="28">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="550" y="525"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="41" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=15;" edge="1" parent="1" source="18" target="40">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="Generate Optimized Design Document" style="rounded=1;whiteSpace=wrap;html=1;flipV=1;" vertex="1" parent="1">
                    <mxGeometry x="490" y="378" width="120" height="60" as="geometry"/>
                </mxCell>
                <object label="" id="19">
                    <mxCell style="endArrow=classic;html=1;fontSize=15;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="5" target="18">
                        <mxGeometry width="50" height="50" relative="1" as="geometry">
                            <mxPoint x="360" y="390" as="sourcePoint"/>
                            <mxPoint x="410" y="340" as="targetPoint"/>
                        </mxGeometry>
                    </mxCell>
                </object>
                <mxCell id="21" value="Modify Design Based on AI feedback" style="rounded=1;whiteSpace=wrap;html=1;flipV=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
                    <mxGeometry x="160" y="140" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=15;entryX=0;entryY=0.333;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.75;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="8" target="21">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="150" y="130" as="sourcePoint"/>
                        <mxPoint x="240" y="240" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="140" y="180"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="26" value="" style="edgeStyle=elbowEdgeStyle;elbow=horizontal;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=15;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="21" target="9">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="210" y="120" as="sourcePoint"/>
                        <mxPoint x="260" y="60" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="33" style="edgeStyle=none;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.25;entryY=1;entryDx=0;entryDy=0;fontSize=15;" edge="1" parent="1" source="28" target="8">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="120" y="575"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="43" value="" style="edgeStyle=none;html=1;fontSize=15;" edge="1" parent="1" source="28" target="42">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="&lt;font style=&quot;font-size: 9px;&quot;&gt;(User Action)&lt;/font&gt;&lt;br&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;Genenrate Rules &lt;br&gt;on RDG?&lt;/font&gt;" style="rhombus;whiteSpace=wrap;html=1;flipV=1;" vertex="1" parent="1">
                    <mxGeometry x="295" y="530" width="150" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="&lt;font style=&quot;font-size: 15px; font-weight: normal;&quot;&gt;No&lt;/font&gt;" style="text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
                    <mxGeometry x="330" y="450" width="50" height="25" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="&lt;font style=&quot;font-size: 15px; font-weight: normal;&quot;&gt;No&lt;/font&gt;" style="text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
                    <mxGeometry x="140" y="550" width="50" height="25" as="geometry"/>
                </mxCell>
                <mxCell id="40" value="Output document for user review" style="rounded=0;whiteSpace=wrap;html=1;fontSize=15;" vertex="1" parent="1">
                    <mxGeometry x="670" y="378" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="42" value="Generate Rules on RDG" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="310" y="680" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="&lt;font style=&quot;font-size: 15px; font-weight: normal;&quot;&gt;Yes&lt;/font&gt;" style="text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
                    <mxGeometry x="360" y="625" width="50" height="25" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>