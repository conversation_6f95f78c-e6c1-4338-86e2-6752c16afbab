<diagram program="umletino" version="15.1"><zoom_level>10</zoom_level><element><id>UMLActor</id><coordinates><x>210</x><y>30</y><w>60</w><h>110</h></coordinates><panel_attributes>User</panel_attributes><additional_attributes></additional_attributes></element><element><id>UMLGeneric</id><coordinates><x>500</x><y>30</y><w>100</w><h>30</h></coordinates><panel_attributes>AI</panel_attributes><additional_attributes></additional_attributes></element><element><id>UMLGeneric</id><coordinates><x>810</x><y>30</y><w>100</w><h>30</h></coordinates><panel_attributes>RDG</panel_attributes><additional_attributes></additional_attributes></element><element><id>Relation</id><coordinates><x>540</x><y>50</y><w>30</w><h>680</h></coordinates><panel_attributes>lt=.</panel_attributes><additional_attributes>10;10;10;660</additional_attributes></element><element><id>Relation</id><coordinates><x>850</x><y>50</y><w>30</w><h>690</h></coordinates><panel_attributes>lt=.</panel_attributes><additional_attributes>10;10;10;670</additional_attributes></element><element><id>Relation</id><coordinates><x>250</x><y>50</y><w>30</w><h>690</h></coordinates><panel_attributes>lt=.</panel_attributes><additional_attributes>10;10;10;670</additional_attributes></element><element><id>Relation</id><coordinates><x>250</x><y>110</y><w>320</w><h>40</h></coordinates><panel_attributes>lt=-&gt;
Upload design document</panel_attributes><additional_attributes>10;20;300;20</additional_attributes></element><element><id>UMLNote</id><coordinates><x>570</x><y>140</y><w>210</w><h>130</h></coordinates><panel_attributes>Note:
bg=#4499FF
fg=black
fontsize=10
Parse design document 
and extract information
1. Change Request 
2. Data Model 
3. Entity 
4. Rule Type
5. Rule Name
6. Rule Details</panel_attributes><additional_attributes></additional_attributes></element><element><id>UMLGeneric</id><coordinates><x>540</x><y>110</y><w>20</w><h>210</h></coordinates><panel_attributes>
bg=yellow</panel_attributes><additional_attributes></additional_attributes></element><element><id>UMLNote</id><coordinates><x>690</x><y>510</y><w>210</w><h>110</h></coordinates><panel_attributes>Note..
Compare information 
receieved from the design 
document agains the 
information received from 
RDG and notify the user of the
descripancies.
fontsize=10
bg=#4499FF
customelement=
drawArc(width-25,5,10,50,50,80,false) fg=red bg=red //Parameters (x, y, width, height, start, extent, open)
drawCircle(width-20,40,3) fg=red bg=red //Parameters (x, y, radius)
</panel_attributes><additional_attributes></additional_attributes></element><element><id>Relation</id><coordinates><x>250</x><y>320</y><w>320</w><h>40</h></coordinates><panel_attributes>lt=&lt;-
Notify the rules identified in the document</panel_attributes><additional_attributes>10;20;300;20</additional_attributes></element><element><id>Relation</id><coordinates><x>250</x><y>360</y><w>320</w><h>40</h></coordinates><panel_attributes>lt=&lt;-
Ask the specific area for which the status is needed</panel_attributes><additional_attributes>10;20;300;20</additional_attributes></element><element><id>Relation</id><coordinates><x>250</x><y>400</y><w>320</w><h>50</h></coordinates><panel_attributes>lt=-&gt;
Specify the area (CrType + optional entity ) to 
check the status</panel_attributes><additional_attributes>10;20;300;20</additional_attributes></element><element><id>UMLNote</id><coordinates><x>40</x><y>380</y><w>210</w><h>70</h></coordinates><panel_attributes>It is likely that the user would ask for 
the status of everything. In such a case, 
notify the user that the process will take 
time. 
bg=blue
fontsize=10</panel_attributes><additional_attributes></additional_attributes></element><element><id>UMLFrame</id><coordinates><x>170</x><y>470</y><w>770</w><h>160</h></coordinates><panel_attributes>Iterate for 
each rule</panel_attributes><additional_attributes></additional_attributes></element><element><id>Relation</id><coordinates><x>540</x><y>480</y><w>340</w><h>40</h></coordinates><panel_attributes>lt=-&gt;
Get Rule Information</panel_attributes><additional_attributes>10;20;320;20</additional_attributes></element><element><id>Relation</id><coordinates><x>540</x><y>510</y><w>160</w><h>80</h></coordinates><panel_attributes>lt=&lt;-
</panel_attributes><additional_attributes>10;60;140;60;140;10;10;10</additional_attributes></element><element><id>Relation</id><coordinates><x>250</x><y>570</y><w>320</w><h>50</h></coordinates><panel_attributes>lt=&lt;-
Notify the status 
Same / Different / Not Found </panel_attributes><additional_attributes>10;20;300;20</additional_attributes></element></diagram>