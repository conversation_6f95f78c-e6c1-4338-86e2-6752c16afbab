# yaml-language-server: $schema=https://sap.github.io/ui5-tooling/schema/ui5.yaml.json
specVersion: '3.1'
type: application
metadata:
  name: supernovafj
resources:
  configuration:
    propertiesFileSourceEncoding: UTF-8
builder:
  minification:
    excludes:
      - "!dmr/mdg/supernova/**-dbg.controller.js"
      - "!dmr/mdg/supernova/**-dbg.js"
  resources:
    excludes:
      - "/test/**"
      - "/localService/**"
      - "userInfo.json"
  # componentPreload:
  #   paths
  #     - "ConcentoRDGMTA/Component.js"
  #     - "ConcentoRDGMTA/Components/*/Component.js"
  customTasks:
  - name: webide-extension-task-updateNeoApp
    afterTask: replaceVersion
    configuration:
      appFolder: webapp
      destDir: dist
      nameSpace: supernovafj
  - name: webide-extension-task-updateManifestJson
    afterTask: webide-extension-task-updateNeoApp
    configuration:
      appFolder: webapp
      destDir: dist
  - name: webide-extension-task-lint
    afterTask: webide-extension-task-updateManifestJson
    configuration:
      appFolder: webapp
      destDir: dist
      nameSpace: supernovafj
  - name: webide-extension-task-resources
    afterTask: webide-extension-task-lint
    configuration:
      appFolder: webapp
      destDir: dist
      nameSpace: supernovafj
  - name: ui5-task-zipper
    afterTask: generateComponentPreload
    configuration:
      archiveName: supernovafj
      additionalFiles:
      - ./xs-app.json
  # - name: ui5-task-zipper
  #   afterTask: generateVersionInfo
  #   configuration:
  #     archiveName: webappZipper
#  - name: ui5-task-minifier
#    afterTask: minify
#    configuration:
#      html: false
#      css: false
#      json: false
#  - name: ui5-task-minify-xml
#    afterTask: minify
#    configuration:
#      minifyOptions:
#        removeComments: true
#        collapseEmptyElements: true
#        collapseWhitespaceInAttributeValues: true
#      fileExtensions:
#      - "js"