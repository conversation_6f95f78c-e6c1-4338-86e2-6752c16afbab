import json
from transformers import <PERSON><PERSON><PERSON><PERSON>, BartForConditionalGeneration
import re

class SingleValueValidationPayloadGenerator:
    def __init__(self, Change_Request, Data_model, Entity, attribute, rule_type, condition_string, model_path):
        """
        Initialize the class with the provided inputs.
        """
        self.change_request = Change_Request
        self.data_model = Data_model
        self.Entity = Entity
        self.attribute = attribute
        self.rule_type = rule_type
        self.condition_string = condition_string
        self.model_path = model_path
        
        # Load the pre-trained model and tokenizer
        self.tokenizer = BartTokenizer.from_pretrained(self.model_path)
        self.model = BartForConditionalGeneration.from_pretrained(self.model_path)
        
        # Call the model to generate conditions and message
        self.conditions = self.call_model_for_conditions() 

    def call_model_for_conditions(self):
        """
        Call the pre-trained model to generate conditions and custom messages.
        
        :return: A dictionary with conditions, operators, and custom messages.
        """

        input_text = "translate English to JSON: " + self.condition_string
        input_ids = self.tokenizer(input_text, return_tensors="pt").input_ids
     
        generated_ids = self.model.generate(input_ids, max_length=512, num_beams=2, early_stopping=True)

        output = self.tokenizer.decode(generated_ids[0], skip_special_tokens=True)
  
        output = self.parse_malformed_json(output)

        return output

    def parse_malformed_json(self, input_string):
        """
        Parses a malformed JSON string where multiple conditions are improperly grouped together
        into a list. It restructures the string into a valid JSON format.
        
        :param input_string: The malformed JSON string.
        :return: A dictionary with conditions and message.
        """

        message_match = re.search(r'"message":\s*"([^"]+)"', input_string)
        message = message_match.group(1) if message_match else ""


        condition_matches = re.findall(r'"attribute":\s*"([^"]+)",\s*"operator":\s*"([^"]+)",\s*"value":\s*"([^"]+)"', input_string)
    
        conditions = []
        for match in condition_matches:
            conditions.append({
                "attribute": match[0],
                "operator": match[1],
                "value": match[2]
            })

        parsed_dict = {
            "conditions": conditions,
            "message": message
        }
        
        return parsed_dict

    def map_operator(self, operator):
        """
        Map the natural language operators to system-friendly operators (e.g., 'equals' -> 'EQ').
        """
        operator_mapping = {
            "equals": "EQ",
            "not equal": "NE",
            "less than": "LT",
            "greater than": "GT",
            "between": "BT",
            "contain": "CP",
            "not contain": "NP"  # Example operator for not containing a pattern
        }
        return operator_mapping.get(operator.lower(), "EQ")  # Default to "EQ" if unknown operator

    def generate_condition_data(self):
        """
        Generate the condition data structure based on parsed conditions from the string.

        :return: A list of dictionaries formatted for condition data.
        """
        condition_data = []
        conditions = self.conditions["conditions"]

        # Add each parsed condition
        for idx, condition in enumerate(conditions, start=1):
            condition_data.append({
                "UsmdModel": self.data_model,
                "UsmdEntity": self.Entity,
                "UsmdAttribute": self.attribute,  # Attribute check
                "Operator": self.map_operator(condition["operator"]),
                "Value": condition["value"],
                "ExprId": str(idx),  # Create an expression ID based on the order
                "ExprType": "VALUECHECK"
            })

        # Optional: Add a hardcoded WF Step condition if needed
        wf_step_condition = {
            "UsmdModel": self.data_model,
            "UsmdEntity": "WF",
            "UsmdAttribute": "STEP",
            "Operator": "EQ",
            "Value": "AL",
            "ExprId": str(len(conditions) + 1)  # Add the next expression ID
        }
        condition_data.append(wf_step_condition)

        return condition_data

    def generate_attribute_data(self):
        """
        Generate the attributes structure based on input parameters.

        :return: A list of dictionaries formatted for attribute data.
        """
        # Create an expression based on parsed conditions using their expression IDs
        conditions = self.conditions["conditions"]
        expression_parts = [f"<{i + 1}>" for i in range(len(conditions))]
        expression = " or ".join(expression_parts)
        expression += f" and <{len(conditions) + 1}>"  # Append the hardcoded STEP expression

        attributes_data = [
            {
                "UsmdModel": self.data_model,
                "UsmdAttribute": self.attribute,
                "ExprType": "",  
                "Expression": expression,
                "Delete": "",  # Optional: can set this dynamically if needed
                "ATTRTOCONDITION": self.generate_condition_data(),  
                "ATTRTOMSGDET": self.generate_message_data() 
            }
        ]
        
        return attributes_data

    def generate_message_data(self):
        """
        Generate a message structure for validation error.

        :return: A list of message details.
        """
        message_data = [
            {
                "Message": self.conditions["message"], 
                "MessageType": "E",  
                "UsmdModel": self.data_model,
                "MessageClass": "" 
            }
        ]

        return message_data

    def generate_payload(self):
        """
        Generate the complete JSON payload for the OData service.

        :return: JSON payload as a dictionary
        """
        attributes_data = self.generate_attribute_data()

        # Build the payload structure
        payload = {
            "UsmdCreqType": self.change_request,
            "UsmdModel": self.data_model,
            "MODELTOENTITY": [
                {
                    "UsmdModel": self.data_model,
                    "UsmdEntity": self.Entity,
                    "ENTITYTOATTR": attributes_data 
                }
            ],
            "MODELTOMESSAGE": [
                {
                    "UsmdModel": self.data_model,
                    "MessageType": "",
                    "Message": ""
                }
            ]
        }
        
        return payload

    def to_json(self, payload):
        """
        Convert the payload to JSON format.

        :param payload: The payload dictionary
        :return: JSON string
        """
        return json.dumps(payload, indent=4)




# condition_string = """Validate postal code if greater than 99999 and city equal to Mexico send message Incorrect Postal Code"""
# # condition_string = """Validate BP_HEADER, if No equal to 0000000103 send message no bueno"""
# model_path = "/Users/<USER>/Documents/Copilot/bartfinetuned"  


# payload_generator = SingleValueValidationPayloadGenerator(
#     Change_Request="ZAJ_TSB1",
#     Data_model="BP",
#     Entity="BP_CENTRL",
#     attribute="BPKIND",
#     rule_type="validation",
#     condition_string="Default the BP Type (BPKIND) value based on the BP Grouping",
#     model_path=model_path
# )


# payload = payload_generator.generate_payload()

# print("Generated Payload:", payload_generator.to_json(payload))
