import re
import json

class MultiValidationPayloadGenerator:
    def __init__(self, change_request, data_model, entity, attribute, rule_type, condition):
        # Initialize the payload with top-level fields
        self.payload = {
            "UsmdModel": data_model,
            "UsmdCreqType": change_request,
            "Transport": "",
            "UsmdDevclass": "",
            "UsmdEntity": entity,
            "MODELTODRIVING": [],
            "MODELTOATTRVALUES": [],
            "MODELTOMESSAGE": [],
            "MODELTOWFSTEPCOUNTNAV": []
        }
        self.rule_type = rule_type
        self.attribute = attribute

        # Automatically process the condition during initialization
        self.parse_condition(condition)

    def parse_condition(self, condition):
        """Parse the condition to extract attributes, validation rules, and messages."""
        # A more flexible regex pattern to capture different conditions like eq, gt, lt, etc.
        condition_pattern = r'(\w+)(?:[-](\w+))?\s(eq|gt|lt|is)\s(\w+|empty)'
        matches = re.findall(condition_pattern, condition)

        if matches:
            # Dynamically handle all extracted conditions
            for idx, match in enumerate(matches, start=1):
                attr_entity, attr_name, operator, value = match

                # Add the validation rule to MODELTODRIVING and MODELTOATTRVALUES
                if operator == 'eq':
                    self.add_driving_value(attr_entity or self.payload['UsmdEntity'], attr_name, str(idx))
                    self.add_attr_value(f"{attr_entity}__{attr_name}", value, f"{value} description", str(idx), str(idx))
                elif operator == 'is' and value == 'empty':
                    self.add_driving_value(attr_entity or self.payload['UsmdEntity'], attr_name, str(idx))
                    self.add_attr_value(f"{attr_entity}__{attr_name}", "INIT", "", str(idx), str(idx))
                elif operator == 'gt':
                    self.add_driving_value(attr_entity or self.payload['UsmdEntity'], attr_name, str(idx))
                    self.add_attr_value(f"{attr_entity}__{attr_name}", value, f"Greater than {value}", str(idx), str(idx))
                elif operator == 'lt':
                    self.add_driving_value(attr_entity or self.payload['UsmdEntity'], attr_name, str(idx))
                    self.add_attr_value(f"{attr_entity}__{attr_name}", value, f"Less than {value}", str(idx), str(idx))

            # Extract message from the condition string by assuming everything after 'send message' as the message
            message_match = re.search(r'send message\s+(.*)', condition, re.IGNORECASE)
            if message_match:
                message = message_match.group(1)
                self.add_message(self.payload['UsmdModel'], "E", message)
        else:
            print("No valid condition found.")

    def add_driving_value(self, entity, attribute, col_num):
        """Add a driving value to the modeltodriving section with additional attributes."""
        self.payload['MODELTODRIVING'].append({
            "UsmdEntity": entity,
            "UsmdAttribute": attribute,
            "ColNum": col_num,
            "Attrdatatype": "CHAR",  # Example value, adjust as needed
            "Lenghttype": "000004" if attribute == "BPKIND" else "000241",  # Example logic, adjust as needed
            "Decimals": "000000",
            "Kind": "C"
        })

    def add_attr_value(self, col_name, col_value, col_value_descr, col_num, row_num):
        """Add an attribute value to the modeltoattrvalues section with extra fields."""
        self.payload['MODELTOATTRVALUES'].append({
            "ColName": col_name,
            "ColValue": col_value,
            "ColValueDescr": col_value_descr,
            "ColNum": col_num,
            "RowNum": row_num,
            "Operator": "EQ",
            "ATTRVALUESTOPLACEH": []  # Empty array for now, based on your example
        })

    def add_message(self, usmd_model, message_type, message):
        """Add a message to the modeltomessage section of the payload."""
        self.payload['MODELTOMESSAGE'].append({
            "UsmdModel": usmd_model,
            "MessageType": message_type,
            "Message": message
        })

    def add_workflow_step(self, wfstep, counter):
        """Add a workflow step count to the modeltowfstepcountnav section."""
        self.payload['MODELTOWFSTEPCOUNTNAV'].append({
            "Wfstep": wfstep,
            "Counter": counter
        })

    def generate_payload(self):
        """Return the final payload for deep entity creation."""
        return self.payload

    def to_json(self):
        """Convert the payload to JSON format."""
        return json.dumps(self.payload, indent=4)
