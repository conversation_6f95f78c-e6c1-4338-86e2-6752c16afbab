import re
import json

class MultiDerivationPayloadGenerator:
    def __init__(self, change_request, data_model, entity, rule_type, condition):
        self.change_request = change_request
        self.data_model = data_model
        self.entity = entity
        self.rule_type = rule_type
        self.condition = condition
        self.payload = {
            "UsmdModel": self.data_model,
            "UsmdCreqType": self.change_request,
            "UsmdEntity": self.entity,
            "UsmdDevclass": "ZDMR",
            "BadiImpl": "ZIMPL2_CRS_ENT_RL_SRVC",
            "MODELTODRIVING": [],
            "MODELTOATTRVALUES": [],
            "MODELTOMESSAGE": []
        }

    def add_conditions(self):
        # Split based on occurrences of "Derive" keyword, ignore empty strings
        condition_blocks = [f"Derive {block.strip()}" for block in self.condition.split("Derive") if block.strip()]

        for block in condition_blocks:
            block = block.strip()

            # Split into derived and driving parts based on "when"
            derived_part, driving_part = self.split_condition_block(block)
            if not derived_part or not driving_part:
                raise ValueError("Invalid condition format: missing derived or driving part")

            derived_attributes = self.parse_derived_part(derived_part.strip())
            driving_attribute, driving_value = self.parse_driving_part(driving_part.strip())

            # Add the driving entity and attribute to MODELTODRIVING
            self.payload["MODELTODRIVING"].append({
                "UsmdEntity": self.entity,  
                "UsmdAttribute": driving_attribute,
                "ColNum": str(len(self.payload["MODELTODRIVING"]) + 1),
                "Attrdatatype": "CHAR",
                "Lenghttype": "000004",
                "Decimals": "000000",
                "Kind": "C"
            })

            # Add each derived attribute to MODELTOATTRVALUES
            for row_num, (attribute, value) in enumerate(derived_attributes, start=1):
                self.payload["MODELTOATTRVALUES"].append({
                    "ColName": f"{self.entity}__{attribute}",
                    "ColValue": value,
                    "ColNum": str(len(self.payload["MODELTOATTRVALUES"]) + 1),
                    "RowNum": str(len(self.payload["MODELTOATTRVALUES"]) + 1),
                    "Operator": "EQ",
                    "Type": "1"
                })

    def split_condition_block(self, block):
        """
        Splits a block into derived and driving parts based on the "when" keyword.
        Returns: (derived_part, driving_part)
        """
        if "when" in block:
            derived_part, driving_part = block.split("when", 1)
            return derived_part.strip(), driving_part.strip()
        return None, None

    def parse_derived_part(self, derived_part):
        derived_pairs = derived_part.replace("Derive", "").strip().split("&")
        derived_attributes = []
        
        for pair in derived_pairs:
            attribute, value = pair.strip().split("to")
            derived_attributes.append((attribute.strip(), value.strip()))
        
        return derived_attributes
    
    def parse_driving_part(self, driving_part):
        match = re.search(r"(\w+) eq (\d+)", driving_part)
        if match:
            driving_attribute = match.group(1).strip()
            driving_value = match.group(2).strip()
            return driving_attribute, driving_value
        else:
            raise ValueError("Invalid driving condition format")

    def get_payload(self):
        return self.payload

    def to_json(self):
        """
        Convert the payload to JSON format.
        """
        return json.dumps(self.payload, indent=4)

