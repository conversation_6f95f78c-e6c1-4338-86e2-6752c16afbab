import json

class MandatoryPayload:
    def __init__(self, Change_Request, Data_model, Entity, attribute, rule_type, Condition):
        """
        Initialize the class with the provided inputs.
        """
        self.change_request = Change_Request
        self.data_model = Data_model
        self.Entity = Entity
        self.attribute = attribute
        self.rule_type = rule_type
        self.condition = Condition

    def generate_deep_brf_data(self):
        """
        Generate the deep_brf_data structure based on input parameters.

        :return: A list of dictionaries formatted for deep_brf_data.
        """

        mandt_value = "Yes" if "Mandatory" in self.condition else "No"

        deep_brf_data = [
            {
                "UsmdModel": self.data_model,  
                "UsmdEntity": self.Entity, 
                "brftoattrNav": [
                    {
                        "UsmdModel": self.data_model,
                        "UsmdAttribute": self.attribute,
                        "Mandt": mandt_value
                    }
                ]
            }
        ]
        
        return deep_brf_data

    def generate_payload(self):
        """
        Generate the complete JSON payload for the OData service.

        :return: JSON payload as a dictionary
        """
        deep_brf_data = self.generate_deep_brf_data()

        # Build the payload structure
        payload = {
            "UsmdCreqType": self.change_request,
            "UsmdModel": self.data_model,
            "modeltobrfNav": deep_brf_data,
            "modeltomessageNav": []  
        }
        
        return payload

    def to_json(self, payload):
        """
        Convert the payload to JSON format.

        :param payload: The payload dictionary
        :return: JSON string
        """
        return json.dumps(payload, indent=4)

Condition = ""
# mandatory_payload = MandatoryPayload(
    #     Change_Request=Change_Request,
    #     Data_model=Data_model,
    #     Entity=Entity,
    #     attribute=attribute,
    #     rule_type=rule_type,
    #     Condition=Condition
    # )

    # payload = mandatory_payload.generate_payload()

    # payload_json = mandatory_payload.to_json(payload)