import json
import re

class SingleDerivationPayloadGenerator:
    def __init__(self, Change_Request, Data_model, Entity, attribute, rule_type, condition):
        self.Change_Request = Change_Request
        self.Data_model = Data_model
        self.Entity = Entity
        self.attribute = attribute
        self.rule_type = rule_type
        self.condition = condition
        self.payload = {
            "UsmdModel": Data_model,
            "UsmdCreqType": Change_Request,
            "Transport": "",
            "MODELTOENTITY": [],
            "MODELTOMESSAGE": []
        }
        self.generate_payload()

    def parse_condition(self):
        conditions = []
        condition_patterns = [
            (r"(\w+)\s*by\s*(\w+)", "EQ"),  # Example: "Derive Account Group (KTOKK) by BP Grouping"
            (r"(greater than|less than|equals)\s*(\w+)", "GT" if "greater" in self.condition else "LT" if "less" in self.condition else "EQ")
        ]
        
        # Parsing the condition
        for pattern, operator in condition_patterns:
            match = re.search(pattern, self.condition)
            if match:
                attribute = match.group(1)
                value = match.group(2)
                conditions.append({
                    "attribute": attribute,
                    "operator": operator,
                    "value": value
                })
        return conditions

    def generate_message(self):
        # Extract a message based on the condition string
        return f"Derive {self.attribute} based on condition: {self.condition}"

    def generate_payload(self):
        # Add model-to-entity entry
        entity = self.add_model_to_entity(usmd_model=self.Data_model, usmd_entity=self.Entity)

        # Add attribute to the entity
        attribute_entry = self.add_attribute_to_entity(entity, usmd_model=self.Data_model, usmd_attribute=self.attribute,
                                                       expr_type="DERIVE", expression="Derived expression", is_needed="YES")

        # Parse the condition and add it
        conditions = self.parse_condition()
        for idx, cond in enumerate(conditions):
            self.add_condition_to_attribute(attribute_entry, usmd_model=self.Data_model, usmd_entity=self.Entity,
                                            usmd_attribute=cond['attribute'], operator=cond['operator'], 
                                            value=cond['value'], expr_id=str(idx + 1))

        # Add set value based on some derivation logic (this is a placeholder)
        self.add_set_value_to_attribute(attribute_entry, usmd_model=self.Data_model, usmd_attribute=self.attribute,
                                        value="DerivedValue", if_true="TrueCondition", if_false="FalseCondition")

        # Add message to the payload
        message = self.generate_message()
        self.add_message(usmd_model=self.Data_model, message_type="INFO", message=message)

    def add_model_to_entity(self, usmd_model, usmd_entity):
        entity = {
            "UsmdModel": usmd_model,
            "UsmdEntity": usmd_entity,
            "ENTITYTOATTR": []
        }
        self.payload["MODELTOENTITY"].append(entity)
        return entity

    def add_attribute_to_entity(self, entity, usmd_model, usmd_attribute, expr_type, expression, is_needed):
        attribute = {
            "UsmdModel": usmd_model,
            "UsmdAttribute": usmd_attribute,
            "ExprType": expr_type,
            "Expression": expression,
            "IsNeeded": is_needed,
            "ATTRTOCONDITION": [],
            "ATTRTOSETVALUE": []
        }
        entity["ENTITYTOATTR"].append(attribute)
        return attribute

    def add_condition_to_attribute(self, attribute, usmd_model, usmd_entity, usmd_attribute, operator, value, expr_id):
        condition = {
            "UsmdModel": usmd_model,
            "UsmdEntity": usmd_entity,
            "UsmdAttribute": usmd_attribute,
            "Operator": operator,
            "Value": value,
            "ExprId": expr_id
        }
        attribute["ATTRTOCONDITION"].append(condition)

    def add_set_value_to_attribute(self, attribute, usmd_model, usmd_attribute, value, if_true, if_false):
        set_value = {
            "UsmdModel": usmd_model,
            "UsmdAttribute": usmd_attribute,
            "Value": value,
            "IfTrue": if_true,
            "IfFalse": if_false
        }
        attribute["ATTRTOSETVALUE"].append(set_value)

    def add_message(self, usmd_model, message_type, message):
        message_entry = {
            "UsmdModel": usmd_model,
            "MessageType": message_type,
            "Message": message
        }
        self.payload["MODELTOMESSAGE"].append(message_entry)

    def get_payload(self):
        return self.payload

    def to_json(self, payload):
        """
        Convert the payload to JSON format.
        """
        return json.dumps(self.payload, indent=4)


# # Example of using the class to create the payload
# payload_generator = SingleDerivationPayloadGenerator(
#     Change_Request="ZAJ_TSB1",
#     Data_model="BP",
#     Entity="BP_CENTRL",
#     attribute="BPKIND",
#     rule_type="derivation",
#     condition="Derive Account Group (KTOKK) by BP Grouping"
# )

# # Get the final payload
# payload = payload_generator.get_payload()

# # Print the payload in JSON format
# print("Generated Payload:", payload_generator.to_json())
