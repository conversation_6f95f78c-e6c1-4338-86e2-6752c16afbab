#tooling.py
from ast import Set
import os
import json
import requests
from typing import Optional
import shutil
from base64 import b64encode
# import spacy
import subprocess
import re
from langchain_community.vectorstores import Weaviate
from langchain.tools import BaseTool
from langchain_openai import OpenAIEmbeddings
from langchain.chains import ConversationalR<PERSON>rie<PERSON><PERSON><PERSON><PERSON>
from langchain.memory import  ConversationBufferMemory
from langchain_community.chat_message_histories import ChatMessageHistory
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
import numpy as np
import sys
from weaviate.connect import ConnectionParams
import weaviate.classes.query as wq  # Uncomment this import for the Rerank functionality
from langchain_core.retrievers import BaseRetriever
from langchain_core.callbacks import CallbackManagerForRetrieverRun
from langchain_core.documents import Document
from typing import List, Any, Dict

from langchain_community.document_loaders import DirectoryLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter

import asyncio
import logging
from collections import OrderedDict
import weaviate
import chainlit as cl

import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import plotly.express as px
import pandas as pd
import pypdf
from openai import OpenAI
from openai import AsyncOpenAI

from rulePayloads import get_mandatory_payload, get_single_value_validation_payload, get_single_value_derivation_payload, get_multi_value_validation_payload, get_multi_value_derivation_payload
from dfmanagement import getFilteredDf
from pydantic.v1 import BaseModel
from langchain.tools import Tool

# from models_index import MODELS_INDEX

from openpyxl import load_workbook
from openpyxl.styles import PatternFill
from openpyxl.worksheet.dimensions import ColumnDimension

from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

from dotenv import load_dotenv
from requests.auth import HTTPBasicAuth
import warnings
from pydantic import BaseModel

import logging

import warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", message="The `dict` method is deprecated; use `model_dump` instead")

pd.set_option('display.max_colwidth', None)
pd.set_option('display.width', None)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("RDG-AI")

# load_dotenv()
# Get the directory containing this script
current_dir = os.path.dirname(os.path.abspath(__file__))
dotenv_path = os.path.join(current_dir, '.env')

# Load the .env file
load_dotenv(dotenv_path)

openai_api_key = os.getenv("OPENAI_API_KEY")
rdgguide = os.getenv("RDGGUIDE")


api_key = os.getenv("OPENAI_API_KEY")
weaviate_host = os.getenv("WEAVIATE_HOST")
AIclient = OpenAI(api_key=api_key)
username = os.getenv("username")
password = os.getenv("password")
subdomain = os.getenv("subdomain")

current_directory = os.path.dirname(os.path.abspath(__file__))

def log_info(message: str, color: str="\033[94m") -> None:
    print(f"{color}{message}\033[0m")

class Get_Zendesk_Tickets(BaseTool):
    name: str = 'Get_Zendesk_Tickets'
    description: str = 'Use this tool when you need to retrieve information about tickets or incidents.'

    def _run(self, question=""):
        log_info(f"[Tool Start] {self.name} called with question: {question}", "\033[95m")
        user = cl.user_session.get("user")
        url = f'https://{subdomain}.zendesk.com/api/v2/users/search?query={user.identifier}'
        autho = f"{username}:{password}"
        credentials = b64encode(autho.encode()).decode()

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Basic {credentials}",
        }

        try:
            response = requests.request("GET", url, headers=headers)
            if response:
                user_info = response.json()
                users = user_info.get('users', {})
                user_data_email = [x for x in users if x["email"] == user.identifier]
                authenticated_user = user_data_email[0] if len(user_data_email) == 1 else None

                if authenticated_user:
                    user_id = authenticated_user["id"]
                    url = f'https://{subdomain}.zendesk.com/api/v2/users/{user_id}/tickets/requested'
                    headers["Authorization"] = f"Basic {credentials}"
                    response = requests.request("GET", url, headers=headers)
                    tickets = response.json().get('tickets', {})
                    if len(tickets) > 0:
                        ticket_list = []
                        for ticket in tickets:
                            ticket_list.append({
                                "id": ticket["id"],
                                "subject": ticket["subject"],
                                "description": ticket["description"],
                                "priority": ticket["priority"],
                                "status": ticket["status"],
                                "url": f"https://{subdomain}.zendesk.com/agent/tickets/{ticket['id']}"
                            })
                        log_info(f"[Tool End] {self.name} returned {len(ticket_list)} tickets.", "\033[95m")
                        return ticket_list
                    else:
                        log_info(f"[Tool End] {self.name} found no tickets.", "\033[95m")
                        return "No tickets found for the provided user."
                else:
                    log_info(f"[Tool End] {self.name} could not authenticate the user.", "\033[95m")
                    return "Could not authenticate the user."
            else:
                log_info(f"[Tool End] {self.name} could not fetch any ticket.", "\033[95m")
                return "Could not fetch any ticket for the provided user."
        except Exception as e:
            log_info(f"[Tool Error] {self.name} encountered error: {e}", "\033[91m")
            return f"An error occurred in _run: {e}"


class createTicket(BaseTool):
    name: str = 'createTickets'
    description: str = (
        "Use this tool to create a ticket with the provided title and description. "
        "Make sure to provide both title and description parameters when calling this tool. "
        "Always send the ticket ID and ticket URL as part of the response to the user."
    )

    def _run(self, **kwargs):
        log_info(f"[Tool Start] {self.name} called with kwargs: {kwargs}", "\033[95m")
        result = asyncio.run(self._arun(**kwargs))
        log_info(f"[Tool End] {self.name} returned: {result}", "\033[95m")
        return result

    async def _arun(self, **kwargs):
        title = kwargs.get('title', '')
        description = kwargs.get('description', '')
        question = kwargs.get('question', '')
        if not title or not description:
            if question:
                try:
                    if isinstance(question, str):
                        try:
                            question_dict = json.loads(question)
                            title = question_dict.get("title", title)
                            description = question_dict.get("description", description)
                        except json.JSONDecodeError:
                            extracted_title, extracted_desc = self.extract_title_description(question)
                            if extracted_title:
                                title = extracted_title
                            if extracted_desc:
                                description = extracted_desc
                    elif isinstance(question, dict):
                        title = question.get("title", title)
                        description = question.get("description", description)
                except Exception as e:
                    return f"An error occurred while processing the input: {e}"
        if not title or not description:
            return "Ticket cannot be created as both title and description are required. Please provide both parameters."
        message = self.create_zendesk_ticket(title, description)
        return message

    def create_zendesk_ticket(self, title, description):
        user = cl.user_session.get("user")
        url = f'https://{subdomain}.zendesk.com/api/v2/tickets.json'
        payload = json.dumps({
            "ticket": {
                "subject": title,
                "comment": {"body": description},
                "requester": {
                    "locale_id": 1,
                    "name": user.identifier,
                    "email": user.identifier
                },
                "priority": "normal",
                "ticket_form_id": 360000774934,
                "custom_fields": [
                    {"id": 360016177393, "value": "rdg"},
                    {"id": 360056055854, "value": "sla_legacy"},
                    {"id": 360046526973, "value": "normal"}
                ]
            }
        })
        autho = f"{username}:{password}"
        credentials = b64encode(autho.encode()).decode()
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Basic {credentials}",
        }
        response = requests.request("POST", url, headers=headers, data=payload)
        responseJSON = response.json()
        ticketDetails = responseJSON.get('ticket')
        if ticketDetails is None:
            return "An error occurred while processing the request. Please try again later."
        ticketId = ticketDetails.get("id")
        ticketURL = f"https://{subdomain}.zendesk.com/agent/tickets/{ticketId}"
        if response.status_code == 201:
            return f"Ticket with ticket ID {ticketId} and ticket URL {ticketURL} was saved successfully."
        else:
            return "Failed to save ticket, please make sure your request contains title and description."

    def extract_title_description(self, input_string):
        patterns = [
            r"Title:\s*(.*?)\s*Description:\s*(.*)",
            r"Title[\s:]+([^\n]+)[\s\n]+Description[\s:]+([\s\S]+)",
            r"'([^']+)'[\s\S]*?'([^']+)'"
        ]
        for pattern in patterns:
            match = re.search(pattern, input_string, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(1).strip(), match.group(2).strip()
        return None, None

class WeaviateDataTool(BaseTool):
    name: str = 'WeaviateData'
    description: str = 'Use this tool for retrieving authoritative responses specific to Syniti and RDG. Not suitable for SAP-related questions or ticketing issues. Always mention the best source for your answer.'

    def _initialize_components(self):
        retriever = CustomRetriever()
        return retriever

    def _run(self, question):
        log_info(f"[Tool Start] {self.name} called with question: {question}", "\033[95m")
        result = asyncio.run(self._arun(question))
        log_info(f"[Tool End] {self.name} returned result.", "\033[95m")
        return result

    async def _arun(self, question):
        retriever = self._initialize_components()
        result = retriever.get_relevant_documents(question)
        return result

class CustomRetriever(BaseRetriever):
    def _get_relevant_documents(self, query: str, *, run_manager: CallbackManagerForRetrieverRun) -> List[Document]:
        documents = []
        response = retrieve(query)
        for obj in response.objects:
            text = obj.properties.get("text", "")
            source = obj.properties.get("source", {})
            document = Document(
                page_content=text,
                metadata={"source": source, "top_source": rdgguide}
            )
            documents.append(document)
        return documents

def retrieve(query):
    openai_api_key = os.getenv("OPENAI_API_KEY")
    client = weaviate.WeaviateClient(
        connection_params=ConnectionParams.from_params(
            http_host=weaviate_host,
            http_port="8080",
            http_secure=False,
            grpc_host=weaviate_host,
            grpc_port="50051",
            grpc_secure=False,
        ),
        additional_headers={"X-OpenAI-Api-Key": openai_api_key},
        additional_config=weaviate.config.AdditionalConfig(startup_period=10, timeout=(60, 180)),
        skip_init_checks=True
    )
    try:
        client.connect()
        prompt = """from all the sources figure out which one is the best one for the particular question. And if and only if the source is a link return the top source url link like this:
                    top source: the source url. if it's not an http link return it blank.
                    NEVER ASSUME and if you don't have a right link do not return it. furthermore if the document contains image return it as well"""
        Models = client.collections.get("KnowledgeBase")
        response = Models.query.hybrid(query=query, limit=30)
        return response
    finally:
        client.close()

class ProvideContextualHelp(BaseTool):
    name: str = 'provide_contextual_help'
    description: str = 'Use this tool when the user wants help with their current screen and pages context. Use the answer to query weaviate.'

    def _run(self, question):
        log_info(f"[Tool Start] {self.name} called with question: {question}", "\033[95m")
        result = asyncio.run(self._arun(question))
        log_info(f"[Tool End] {self.name} returned result.", "\033[95m")
        return result

    async def _arun(self, question):
        fn = cl.CopilotFunction(name="help", args={"msg": "help"})
        try:
            contextualData = await asyncio.wait_for(fn.acall(), timeout=1)
        except asyncio.TimeoutError:
            logging.error("Contextual data retrieval timed out")
            contextualData = {}
        if isinstance(contextualData, dict) and 'path' in contextualData:
            value = contextualData.get('path')
            question = contextualData.get('moreInfo', '') + " In RDG"
        else:
            question = "Help"
        return question

class Get_implementation_reports(BaseTool):
    name: str = 'Get_implementation_reports'
    description: str = 'Use this tool when you need to generate reports of objects etc. Any request that needs to build reports goes through this tool.'

    def _run(self, **kwargs):
        log_info(f"[Tool Start] {self.name} called with kwargs: {kwargs}", "\033[95m")
        result = asyncio.run(self._arun(**kwargs))
        log_info(f"[Tool End] {self.name} returned result.", "\033[95m")
        return result

    async def _arun(self, **kwargs):
        question = kwargs.get('question', '')
        function = kwargs.get('function', '')
        title = kwargs.get('title', "Report")
        fn = cl.CopilotFunction(name="report", args={"msg": "DataModel"})
        try:
            contextualData = await asyncio.wait_for(fn.acall(), timeout=1)
        except asyncio.TimeoutError:
            logging.error("Contextual data retrieval timed out")
            contextualData = {}
        if not function:
            if question:
                try:
                    question_dict = json.loads(question)
                    function = question_dict.get("function", "reportDataModelActive")
                except json.JSONDecodeError:
                    function = "reportDataModelActive"
            else:
                function = "reportDataModelActive"
        valid_functions = ["reportDataModelActive", "reportDataModelCustom", "reportEntities", "reportEntitiesByDataModel"]
        if function not in valid_functions:
            function = "reportDataModelActive"
        function_mapping = {
            "reportDataModelActive": {
                "data_key": 'reportDataModelActive',
                "columns": ['Status', 'Count'],
                "title": 'Pie Chart of Activity Status'
            },
            "reportDataModelCustom": {
                "data_key": 'reportDataModelCustom',
                "columns": ['CustomField', 'Value'],
                "title": 'Pie Chart of Custom Data Model'
            },
            "reportEntities": {
                "data_key": 'reportEntities',
                "columns": ['Entity', 'Count'],
                "title": 'Pie Chart of Entities',
                "slice_df": True
            },
            "reportEntitiesByDataModel": {
                "data_key": 'reportEntitiesByDataModel',
                "columns": ['DataModel', 'CustomEntities', 'StandardEntities'],
                "title": 'Bar Chart of Entities by Data Model',
                "bar_chart": True
            }
        }
        def create_pie_chart(df, title):
            labels = df.iloc[:, 0]
            values = df.iloc[:, 1]
            fig, ax = plt.subplots(figsize=(15, 12))
            ax.pie(values, labels=labels, autopct='%1.1f%%', startangle=90)
            ax.axis('equal')
            plt.title(title)
            return cl.Pyplot(name="plot", figure=fig, display="inline")
        def create_bar_chart(df, title):
            fig, ax = plt.subplots(figsize=(16, 12))
            df.plot(kind='bar', x='DataModel', y=['CustomEntities', 'StandardEntities'], stacked=True, ax=ax)
            for p in ax.patches:
                width, height = p.get_width(), p.get_height()
                x, y = p.get_xy()
                if height > 0:
                    ax.text(x + width / 2, y + height / 2, int(height), ha='center', va='center')
            ax.set_title('Report')
            ax.set_xlabel('Data Model')
            ax.set_ylabel('Number of Entities')
            plt.xticks(rotation=45)
            plt.tight_layout()
            return cl.Pyplot(name="chart", figure=fig, display="inline", size="large")
        elements = []
        df = None
        if function in function_mapping:
            data_config = function_mapping.get(function, {})
            data_key = data_config.get("data_key")
            if data_key:
                data = contextualData.get(data_key, {})
                if "bar_chart" in data_config:
                    df = pd.DataFrame(data.get('EntityInfoByDataMode', []))
                    elements = [create_bar_chart(df, data_config.get("title", "Default Title"))]
                else:
                    filtered_data = {k: v for k, v in data.items() if isinstance(v, (int, float))}
                    df = pd.DataFrame(list(filtered_data.items()), columns=data_config.get("columns", []))
                    if data_config.get("slice_df"):
                        df = df[1:]
                    elements = [create_pie_chart(df, data_config.get("title", "Default Title"))]
        await cl.Message(content=title, elements=elements, author="Supernova").send()
        if df is not None:
            if function == "reportEntitiesByDataModel":
                return f" this is a sample of the status {df.head()}"
            else:
                labels = df.iloc[:, 0]
                values = df.iloc[:, 1]
                return f"this is the status {labels} and their values {values}"
        else:
            return "No data available for the selected function."
def parse_values(value: Any) -> List[str]:
        if pd.isna(value):
            return []
        return [v.strip() for v in str(value)
                                .replace("\n", ",")
                                .replace(";", ",")
                                .split(",") if v.strip()]

def rule_type_to_text(rt: Any) -> str:
        mapping = {
            1: "Mandatory",
            2: "Single Value Validation",
            3: "Single Value Derivation",
            4: "Multi Value Validation",
            5: "Multi Value Derivation",
        }
        try:
            return mapping[int(float(rt))]
        except Exception:
            return str(rt)

def normalise_flags(df: pd.DataFrame, cols: List[str]) -> None:
        def rep(s: pd.Series) -> pd.Series:
            return (s.astype(str)
                    .str.upper()
                    .replace({"X": True, "-": False, "NAN": False, "": False}))
        df[cols] = df[cols].apply(rep)


class processDocumentForRule(BaseTool):


    name: str = 'process_document_for_rule'
    description: str = (
        "automatically call this tool to request rules document from the user whenever they need any general "
        "information related to their rules in the system but only if you don't have any info in your memory for documents"
    )

    def _run(self, question):
        log_info(f"[Tool Start] {self.name} called with question: {question}", "\033[95m")
        result = asyncio.run(self._arun(question))
        log_info(f"[Tool End] {self.name} returned result.", "\033[95m")
        return result

    def parse_values(self, value: str) -> list:
        """
        Splits a string by commas or semicolons (or both)
        and returns a list of trimmed non-empty parts.
        """
        # Optionally log the value being parsed (commented out if too verbose)
        # log_info(f"Parsing values from: {value}")
        if pd.isna(value):
            return []
        parts = re.split(r'[;,]+', str(value))
        return [p.strip() for p in parts if p.strip()]

    async def request_document(self, cl, question):
        log_info("Requesting document: " + question)
        files = None
        log_info("Prompting user for file upload...", "\033[94m")
        while files is None:

            try:
                files = await asyncio.wait_for(
                    cl.AskFileMessage(
                        content=question,
                        accept=[
                            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            "application/pdf"
                        ],
                        max_size_mb=20,
                        timeout=60,
                        max_files=1,
                        author='Supernova'
                    ).send(),
                    timeout=11
                )
                log_info(f"Files received: {files}", "\033[92m")
            except asyncio.TimeoutError:
                log_info("File upload timed out. Retrying...", "\033[91m")
                files = None

        file = files[0]
        await cl.Message(
            content=f"✅ `{file.name}` uploaded – **processing…**",
            author="Supernova",
        ).send()

        if file.type == "application/pdf":
            text = ""
            with open(file.path, "rb") as f:
                for page in pypdf.PdfReader(f).pages:
                    text += page.extract_text()
            raise ValueError("PDF parsing to rules table not implemented yet")
        else:
            df = pd.read_excel(file.path, sheet_name=0)

        # Ensure mandatory columns exist
        for col in ("Data model", "Entity", "Attribute","Condition", "Rule Type"):
            if col not in df.columns:
                df[col] = ""

        print(f"[rules] Sheet loaded ({len(df)} rows)", "\033[95m")

        # 3️⃣  Validate / fix Entity-Attribute using MODELS_INDEX --------------
        # df = df.apply(self.validate_and_fix_row, axis=1)

        # 4️⃣  Rule-type mapping & change-request detection --------------------
        df["Rule Type"] = df["Rule Type"].apply(rule_type_to_text)
        rule_idx = df.columns.get_loc("Rule Type")
        cr_cols  = list(df.columns[rule_idx + 1 :])
        normalise_flags(df, cr_cols)

        # 5️⃣  Explode into long format ---------------------------------------
        long_rows: List[Dict[str, Any]] = []
        for _, row in df.iterrows():
            for cr in cr_cols:
                if row[cr] is True:
                    long_rows.append(
                        {
                            "Change Request": cr.split(" ")[0].split("-")[0],
                            "Data model"    : row["Data model"],
                            "Rule Type"     : row["Rule Type"],
                            "Entity"        : row["Entity"],
                            "Attribute"     : row["Attribute"],
                            "Condition"     : row.get("Condition", ""),
                            # "Default"       : row.get("Default", ""),
                        }
                    )

        finaldf = pd.DataFrame(long_rows).drop_duplicates()
        print(f"[rules] Final dataframe → {len(finaldf)} rows", "\033[92m")

        # Store in session for re-use
        cl.user_session.set("rules_document_provided", True)
        cl.user_session.set("document_content", finaldf)

        return finaldf

    # ------------------------------------------------------------------
    # Internal validation helper
    # ------------------------------------------------------------------
    def validate_and_fix_row(self, row: pd.Series) -> pd.Series:
        entities_found: Set[str] = set()
        unknown_attrs: List[str] = []

        for attr in parse_values(row["Attribute"]):
            if attr in MODELS_INDEX:
                entities_found.update(MODELS_INDEX[attr])
            else:
                unknown_attrs.append(attr)

        if unknown_attrs:
            print(f"⚠️  Unknown attribute(s): {unknown_attrs}", "\033[93m")

        if entities_found:
            correct_entities = ", ".join(sorted(entities_found))
            current_entities = set(parse_values(row.get("Entity", "")))

            if not current_entities or current_entities != entities_found:
                print(
                    f"🔄  Setting Entity for attr(s) '{row['Attribute']}' → {correct_entities}",
                    "\033[96m",
                )
                row["Entity"] = correct_entities

        return row


    async def get_rules_from_rdg(self, data_model="BP", entity=None, change_request_type=None, data_type=None):
        loopForData = True
        responseData = []
        while loopForData:
            receivedLength = len(responseData)
            log_info(f"Calling CopilotFunction with offset: {receivedLength}", "\033[94m")
            fn = cl.CopilotFunction(
                name="report",
                args={
                    "msg": "BRF",
                    "action": "List",
                    "actionType": "ByEntity",
                    "params": {
                        "modelName": data_model,
                        "entityName": None,
                        "crType": None,
                        "offset": receivedLength
                    }
                }
            )
            try:
                res = await asyncio.wait_for(fn.acall(), timeout=5)
                log_info(f"Received data chunk of length: {len(res.get('data', []))}", "\033[92m")
            except asyncio.TimeoutError:
                log_info("Contextual data retrieval timed out.", "\033[91m")
                res = {"data": []}

            if len(res["data"]) == 0:
                log_info("No more data returned from RDG, exiting loop.", "\033[94m")
                loopForData = False
            else:
                responseData.extend(res["data"])

        log_info(f"Combined RDG response length: {len(responseData)}", "\033[92m")
        df = pd.DataFrame(responseData)
        columns_to_drop = ['CRTypeDescription', 'ModelDesc', 'EntityDesc', 'RulesetId', 'RulesetName', 'RuleType', 'AppType', 'UserRuleName']
        df_dropped = df.drop(columns=columns_to_drop)
        df_dropped = df_dropped.drop_duplicates()
        log_info("RDG data processed and filtered.", "\033[92m")
        return df_dropped


class filterDataframe(BaseTool):
    name: str = 'filterDataframe'
    description: str = "Use this tool to answer specific questions about the rules and documents in the document."

    def _run(self, question):
        log_info(f"[Tool Start] {self.name} called with question: {question}", "\033[95m")
        result = asyncio.run(self._arun(question))
        log_info(f"[Tool End] {self.name} returned result.", "\033[95m")
        return result

    async def _arun(self, *args, **kwargs):
        log_info("Starting to filter dataframe...", "\033[94m")
        # Parse parameters from the first argument
        if args and isinstance(args[0], str):
            try:
                params = json.loads(args[0])
                log_info("Parsed parameters from JSON input.", "\033[92m")
            except json.JSONDecodeError:
                log_info("Invalid JSON input provided.", "\033[91m")
                raise ValueError("Invalid JSON input")
        elif args and isinstance(args[0], dict):
            params = args[0]
            log_info("Received parameters as dictionary.", "\033[92m")
        else:
            params = {}
            log_info("No parameters provided; proceeding with empty parameters.", "\033[93m")

        document_content = cl.user_session.get("document_content")
        if document_content is None:
            log_info("No document content found in user session.", "\033[93m")
        else:
            log_info("Document content retrieved from user session.", "\033[92m")

        filtered_df = getFilteredDf(document_content, params)
        log_info("Filtered DataFrame successfully generated.", "\033[92m")
        return f""" Here's the filtered data in markdown format:
{filtered_df}

Please do not modify anything from this dataframe when displaying it to the user, and make sure that the right information matches the right column.

Additional information:

The 'Match Count' column shows the number of rules that match the other columns (excluding 'Condition') in the system and that was validated.
The 'DraftAi Count' column shows the number of rules that match the other columns (excluding 'Condition') in the system and that was not yet validated.
The 'Rule Name' column contains the name of the matched rule in the system.
If a row has a 'Match Count' of 0 or 'DraftAi Count' of 0, it means there are no matching entries in the system for the 'Change Request', 'Data Model', 'Entity', 'Attribute', and 'Rule Type'.
'Rule Type' can be one of the following: Mandatory, Single Value Derivation, Multi Value Derivation, Single Value Validation, or Multi Value Validation.
"""


class analyseDocument(BaseTool):


    name: str = 'analyse_Document'
    description: str = (
        "automatically call this tool to provide overview about general rule implementation, the purpose of this tool is to "
        "compare the content of the configuration rule document from the rules in the system, never set downloadable link for this tool"
    )

    def _run(self, question):
        log_info(f"[Tool Start] {self.name} called with question: {question}", "\033[95m")
        result = asyncio.run(self._arun(question))
        log_info(f"[Tool End] {self.name} returned result.", "\033[95m")
        return result

    async def _arun(self, question="Please upload the specification document"):
        log_info("Starting document analysis...", "\033[94m")
        processDocument = processDocumentForRule()
        rules_document_provided = cl.user_session.get("rules_document_provided", False)
        document_content = cl.user_session.get("document_content")

        if document_content is None:
            log_info("No document content found. Requesting document from user.", "\033[93m")
            await self._generate_and_send_excel_sample(None)
            finaldf = await processDocument.request_document(cl, "Please upload the specification document")
        else:
            log_info("Found existing document content in session.", "\033[92m")
            finaldf = cl.user_session.get("document_content")

        documentdf = pd.DataFrame(finaldf)
        log_info(f"Document DataFrame created with shape: {documentdf.shape}", "\033[92m")

        log_info("Grouping document by 'Data model'...", "\033[94m")
        groupedDataModel = documentdf.groupby('Data model')
        results = pd.DataFrame()
        for name, group in groupedDataModel:
            log_info(f"Retrieving system rules for Data Model: {name}", "\033[94m")
            result = await processDocument.get_rules_from_rdg(
                data_model=name,
                entity=None,
                change_request_type=None,
                data_type=None
            )
            results = pd.concat([results, result], ignore_index=True)
        log_info(f"Total system rules retrieved: {len(results)}", "\033[92m")

        results = pd.DataFrame(results)
        results = results.rename(
            columns={
                "CRTypeId": "Change Request",
                "ModelName": "Data model",
                "EntityName": "Entity",
                "AttributeName": "Attribute",
                "RuleDesc": "Rule Type",
                "RuleName": "Rule Name"
            }
        )
        original_documentdf = documentdf.copy()
        columns_to_normalize = ["Change Request", "Data model", "Entity", "Attribute", "Rule Type", "Rule Name"]

        normalized_results = self.normalize_columns(results.copy(), columns_to_normalize)
        normalized_documentdf = self.normalize_columns(documentdf.copy(), columns_to_normalize)

        log_info("Calculating Match_Count, DraftAi_Count, and compiling Rule Name...", "\033[94m")
        normalized_documentdf['Match_Count'] = normalized_documentdf.apply(
            lambda row: normalized_results[
                (normalized_results['Change Request'] == row['Change Request']) &
                (normalized_results['Data model'] == row['Data model']) &
                (normalized_results['Entity'] == row['Entity']) &
                (
                    True if row['Rule Type'] in ['multi value validation', 'multi value derivation']
                    else normalized_results['Attribute'] == row['Attribute']
                ) &
                (normalized_results['Rule Type'] == row['Rule Type']) &
                (normalized_results['AiGenerated'] == False)
            ].shape[0], axis=1
        )
        normalized_documentdf['DraftAi_Count'] = normalized_documentdf.apply(
            lambda row: normalized_results[
                (normalized_results['Change Request'] == row['Change Request']) &
                (normalized_results['Data model'] == row['Data model']) &
                (normalized_results['Entity'] == row['Entity']) &
                (
                    True if row['Rule Type'] in ['multi value validation', 'multi value derivation']
                    else normalized_results['Attribute'] == row['Attribute']
                ) &
                (normalized_results['Rule Type'] == row['Rule Type']) &
                (normalized_results['AiGenerated'] == True)
            ].shape[0], axis=1
        )
        normalized_documentdf['Rule Name'] = normalized_documentdf.apply(
            lambda row: ', '.join(normalized_results[
                (normalized_results['Change Request'] == row['Change Request']) &
                (normalized_results['Data model'] == row['Data model']) &
                (normalized_results['Entity'] == row['Entity']) &
                (
                    True if row['Rule Type'] in ['multi value validation', 'multi value derivation']
                    else normalized_results['Attribute'] == row['Attribute']
                ) &
                (normalized_results['Rule Type'] == row['Rule Type'])
            ]['Rule Name'].tolist()), axis=1
        )

        original_documentdf['Match_Count'] = normalized_documentdf['Match_Count']
        original_documentdf['Rule Name'] = normalized_documentdf['Rule Name']
        original_documentdf['DraftAi_Count'] = normalized_documentdf['DraftAi_Count']

        total_rules = len(original_documentdf)
        validated_rules = original_documentdf.loc[original_documentdf['Match_Count'] >= 1, 'Match_Count'].sum()
        draft_ai_rules = original_documentdf.loc[
            (original_documentdf['DraftAi_Count'] >= 1) & (original_documentdf['Match_Count'] == 0),
            'DraftAi_Count'
        ].sum()
        no_entry_rules = original_documentdf[
            (original_documentdf['Match_Count'] == 0) & (original_documentdf['DraftAi_Count'] == 0)
        ].shape[0]

        summary = {
            "Number of rules from the provided document": total_rules,
            "Number of rules with validated entries in the system": validated_rules,
            "Number of rules crafted by AI but not yet validated": draft_ai_rules,
            "Number of rules without entry in the system": no_entry_rules
        }
        log_info(f"Analysis summary: {summary}", "\033[92m")

        if 'Count' in original_documentdf.columns:
            original_documentdf.drop(columns=['Count'], inplace=True)

        if document_content is None:
            log_info("No document content found in session – generating and sending Excel file...", "\033[94m")
            await self._generate_and_send_excel(original_documentdf)

        sample_documentdf = original_documentdf[original_documentdf['Match_Count'] > 0]
        cl.user_session.set("document_content", original_documentdf)
        log_info("User session updated with document content.", "\033[92m")

        return f"""
After performing a thorough analysis of the provided document and comparing it with the data from the system, here is a comprehensive summary of the findings:

### Summary:
- **Number of rules from the provided document**: {summary['Number of rules from the provided document']}
- **Number of rules with validated entries in the system**: {summary['Number of rules with validated entries in the system']}
- **Number of rules crafted by AI but not yet validated**: {summary['Number of rules crafted by AI but not yet validated']}
- **Number of rules without entry in the system**: {summary['Number of rules without entry in the system']}

            ### Filtered DataFrame:
            Below is a sample table displaying some entries where matches were found in the system:
            {sample_documentdf[sample_documentdf['Rule Type'] == "Mandatory"]
                .head(3)
                .to_string(index=False)}

            """

    async def _generate_and_send_excel_sample(self, documentdf):
        try:
            headers = [
                "Seq", "Data model", "Data Domain", "Field description",
                "Entity", "Attribute", "Condition",
                "Rule Type", "Change Request 1", "Change Request 2", "Change Request 3"
            ]

            documentdf = pd.DataFrame(columns=headers)
            excel_file_path = './AI_create_rule_sample.xlsx'
            documentdf.to_excel(excel_file_path, index=False)

            wb = load_workbook(excel_file_path)
            ws = wb.active

            ws.auto_filter.ref = ws.dimensions

            green_fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")

            for cell in ws[1]:
                cell.fill = green_fill

            for column_cells in ws.columns:
                length = max(len(str(cell.value)) for cell in column_cells)
                col_letter = column_cells[0].column_letter
                ws.column_dimensions[col_letter].width = length + 2

            wb.save(excel_file_path)

            help_text = (
                "## Business Rules Creation Guide\n\n"
                "To create your business rule, you'll need to complete the standard Excel template provided below. This structured approach ensures your rule is properly configured and validated in the system.\n\n"
                "### Excel Template Column Guide\n\n"
                "- **Seq**: Sequential number for organizing multiple rules in your implementation order.\n"
                "- **Data model**: The SAP data model code (e.g., MM, BP, FI) that this rule applies to.\n"
                "- **Data Domain**: The business domain or functional area (e.g., Supplier, Material, Customer).\n"
                "- **Field description**: Brief description of the field's business purpose.\n"
                "- **Entity**: The entity or entities the rule applies to, separated by commas if multiple (e.g., BP_CENTRL, BP_VENDR).\n"
                "- **Attribute**: The attribute(s) being validated or derived, separated by commas if multiple (e.g., BU_SORT1, NAME1).\n"
                "- **Condition**: The logical condition that triggers the rule (e.g., if Material Type = 'FERT', BP_TYPE = '0001').\n"
                "- **Rule Type**: The specific type of business rule (e.g., Single Value Validation, Multi Value Derivation).\n"
                "- **Change Request Columns**: Columns indicating which Change Request types this rule applies to.\n"
                "    - Add a column for each Change Request type and mark with 'X' to associate rules.\n"
                "    - The system will process these associations during rule creation.\n\n"
                "### Important Implementation Notes\n\n"
                "- **Entity and Attribute fields**: Can contain multiple values separated by commas when the rule applies to multiple entities or attributes.\n"
                "- **Column names**: Must be preserved exactly as provided in the template.\n"
                "- **Required fields**: Data model, Entity, Attribute, and Rule Type are mandatory for all rules.\n"
                "- **Workflow steps**: For rules that should apply only at specific workflow steps, include this in the Condition field.\n\n"
                "### Next Steps\n\n"
                "1. Download the sample Excel template below\n"
                "2. Complete the required fields based on your business requirements\n"
                "3. Upload the completed template for validation and rule creation\n\n"
                "For comprehensive documentation, refer to the [RDG and AI User Guide](" + rdgguide + ").\n\n"
                "If you need assistance with rule configuration, please don't hesitate to ask."
            )


            msg = cl.Message(author="Supernova", content=help_text)
            await msg.send()

            await cl.File(
                name="AI_create_rule_sample.xlsx",
                path=excel_file_path
            ).send(for_id=msg.id)

        except Exception as e:
            print(f"Error while creating or sending Excel: {e}")


    async def _generate_and_send_excel(self, documentdf):
        excel_file_path = './document_comparison_results.xlsx'
        log_info(f"Generating Excel file at: {excel_file_path}", "\033[94m")
        documentdf.to_excel(excel_file_path, index=False)

        msg = cl.Message(
            author="Supernova",content="You can download the detailed results as an Excel file."
        )
        await msg.send()
        log_info("Excel file notification message sent to user.", "\033[92m")

        await cl.File(
            name="document_comparison_results.xlsx",
            path=excel_file_path
        ).send(for_id=msg.id)
        log_info("Excel file sent to user.", "\033[92m")

    def normalize_columns(self, df, columns):
            for col in columns:
                if col in df.columns:
                    df[col] = df[col].astype(str).str.strip().str.lower()
            return df

    def normalize_string(self, s):
        return str(s).strip().lower() if s else ''

class uploadnewfile(BaseTool):

    name: str = 'uploadnewfile'
    description: str = """ use this tool when the user wants to upload a document a retun the analysis """

    def _run(self, question):
        return asyncio.run(self._arun(question))

    async def _arun(self, question=""):
        cl.user_session.set("rules_document_provided", False)
        cl.user_session.set("document_content", None)

        analyse_document_instance = analyseDocument()
        analysis = await analyse_document_instance._arun(question = "Please upload the specifaction document")

        return analysis



class analyseRule(BaseTool):


    name: str = 'analyse_Rule'
    description: str = """  always call this tool with change_request, data_model, entity, attribute, rule_type, condition, and rule_name. the purpose of this tool is to provide more in depth detail for rule, it will take the information attached to a rule name and do more analysis """

    def _run(self, question):
        log_info(f"[Tool Start] {self.name} called with question: {question}", "\033[95m")
        result = asyncio.run(self._arun(question))
        log_info(f"[Tool End] {self.name} returned result.", "\033[95m")
        return result

        raise NotImplementedError("sync not implemented")

    # async def _arun(self, question="", data_model="BP", entity="BP_CENTRL", change_request="BP1P1", rule_type="Single Value Validation"):
    async def _arun(self, question="", change_request="", data_model="", entity="", attribute="",  rule_type="", condition="", rule_name=""):
        if not change_request:
            change_request = self.extract_param_from_question(question, 'change_request')
        if not data_model:
            data_model = self.extract_param_from_question(question, 'data_model', default='BP')
        if not entity:
            entity = self.extract_param_from_question(question, 'entity')
        if not attribute:
            attribute = self.extract_param_from_question(question, 'attribute')
        if not rule_type:
            rule_type = self.extract_param_from_question(question, 'rule_type')
        if not condition:
            condition = self.extract_param_from_question(question, 'condition')
        if not rule_name:
            rule_name = self.extract_param_from_question(question, 'rule_name')
        payload_content = ''
        payload_structure=''


        rule_type_lower = rule_type.lower()

        if "multi" in attribute.lower():
            if "validation" in rule_type_lower:
                payload_content, payload_structure = get_multi_value_validation_payload(
                    change_request, data_model, entity, attribute, rule_type, condition
                )
            elif "derivation" in rule_type_lower:
                payload_content, payload_structure = get_multi_value_derivation_payload(
                    change_request, data_model, entity, attribute, rule_type, condition
                )
            elif "" in rule_type_lower:
                print("multi mandatory to be implemented")
        else:
            if "mandatory" in rule_type_lower:
                payload_content, payload_structure = get_mandatory_payload(
                    change_request, data_model, entity, attribute, rule_type, condition
                )
            elif "validation" in rule_type_lower:
                payload_content, payload_structure = get_single_value_validation_payload(
                    change_request, data_model, entity, attribute, rule_type, condition
                )
            elif "derivation" in rule_type_lower:
                payload_content, payload_structure = get_single_value_derivation_payload(
                    change_request, data_model, entity, attribute, rule_type, condition
                )

        rule_name_upper = rule_name.upper()
        print(rule_name_upper)

        rule_name_upper = rule_name.upper()
        print(rule_name_upper)
        fn = cl.CopilotFunction(
                name="report",
                args={
                    "msg": "BRF",
                    "action": "Detail",
                    "actionType": "ByEntity",
                    "params": {
                        "ruleName": rule_name_upper
                    }
                }
            )

        # res = await fn.acall()
        try:
            res = await asyncio.wait_for(
                fn.acall(),
                timeout=1
            )
        except asyncio.TimeoutError:
            logging.error("Contextual data retrieval timed out")

        systemPayload = json.dumps(res["data"], indent=4)


        #  3. You are also provided with a sample payload: {payload_content}. Use this as a structural reference.
        return f"""
            Adopt the persona of an SAP MDG and BRF+ rule expert with deep knowledge of rule crafting and payload structures.

            ### Task:
            1. You have received a system-generated payload: {systemPayload}. This payload is associated with the {rule_type} rule in SAP MDG.
            2. Use the provided parameters:
            - Change Request: {change_request}
            - Data Model: {data_model}
            - Entity: {entity}
            - Attribute: {attribute}
            - Condition: {condition}

            3. Leverage your SAP MDG expertise to:
            - Craft the best possible final payload, adhering to the skeleton and patterns in the provided sample payload `{payload_content}`.
            - Ensure that the crafted payload aligns with both system requirements and SAP MDG best practices, while satisfying the condition `{condition}` and the structure `{payload_structure}`.

            ### Steps:
            1. Display the original system payload, the reference sample payload, and the final payload that you craft.
            2. Analyze the final payload to determine if it meets the conditions, parameters, and reference structure provided.
            3. Compare the final payload with both the system-generated payload and the sample payload, evaluating if the crafted result aligns with the specified requirements and structure.

            ### Output:
            Only display the original system payload, your crafted final payload, and a brief analysis about the system payload, because we want to know if the system payload is a valid one based on the one you crafted and your expertise.

            """
    def extract_param_from_question(self, question: str, param_name: str, default=None):
        try:
            question_dict = json.loads(question)
            return question_dict.get(param_name, default)
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON: {e}")
            return default

class GenerateRule(BaseTool):

    name: str = 'Generate_Rule'
    description: str =  """
                        Generate a consistent payload for rule generation based on user input.
                        Ensures the correct structure for parameters like change_request, data_model, entity, attribute, rule_type, and condition.
                        The rules must always be in json
                        """

    def _run(self, **kwargs):
        log_info(f"[Tool Start] {self.name} called with question: {kwargs}", "\033[95m")
        result = asyncio.run(self._arun(**kwargs))
        log_info(f"[Tool End] {self.name} returned result.", "\033[95m")
        return result

    async def _arun(self, **kwargs):

        allowed_params = {"question", "change_request", "data_model", "entity", "attribute", "rule_type", "condition", "rule_name"}

        params = {
        "question": kwargs.get("question"),
        "change_request": kwargs.get("change_request"),
        "data_model": kwargs.get("data_model"),
        "entity": kwargs.get("entity"),
        "attribute": kwargs.get("attribute"),
        "rule_type": kwargs.get("rule_type"),
        "condition": kwargs.get("condition"),
        "rule_name": kwargs.get("rule_name"),
    }
        # Override allowed keys if provided in kwargs and ignore extras
        for key in allowed_params:
            if key in kwargs:
                params[key] = kwargs[key]


        # Unpack parameters from our filtered dictionary
        question     = params["question"]
        change_request = params["change_request"]
        data_model     = params["data_model"]
        entity         = params["entity"]
        attribute      = params["attribute"]
        rule_type      = params["rule_type"]
        condition      = params["condition"]
        rule_name      = params["rule_name"]


        if not change_request:
            change_request = self.extract_param_from_question(question, 'change_request')
        if not data_model:
            data_model = self.extract_param_from_question(question, 'data_model', default='BP')
        if not entity:
            entity = self.extract_param_from_question(question, 'entity')
        if not attribute:
            attribute = self.extract_param_from_question(question, 'attribute')
        if not rule_type:
            rule_type = self.extract_param_from_question(question, 'rule_type')
        if not condition:
            condition = self.extract_param_from_question(question, 'condition')
        if not rule_name:
            rule_name = self.extract_param_from_question(question, 'rule_name')


        payload_structure=''
        overview = ''
        if rule_type is None:
            rule_type = "Multi"
        rule_type_lower = rule_type.lower()

        match rule_type_lower:
                case "mandatory":
                    payload_structure = get_mandatory_payload()
                case "single value validation":
                    payload_structure = get_single_value_validation_payload()
                case "single value derivation":
                    payload_structure = get_single_value_derivation_payload()
                case "multi value validation":
                    payload_structure = get_multi_value_validation_payload()
                case "multi value derivation":
                    overview, payload_structure = get_multi_value_derivation_payload()
                case _:
                    print("Multi mandatory rule type to be implemented")




         # 3) Prepare instructions and user prompt for the LLM
        system_instructions = f"""
                Your role is to adopt the persona of a seasoned SAP MDG and BRF+ rule expert.
                Your role is to adopt the persona of a seasoned SAP MDG and BRF+ rule expert.
                You are the best expert on any SAP MDG rule crafting and payload generation. That's all you know
                You have been given the following parameters:
                - Change Request: {change_request}
                - Data Model: {data_model}
                - Entities: {entity}
                - Attributes: {attribute}
                - Rule Type: {rule_type}
                - Condition: {condition}

                Your task: **Generate a BRF+ rule in JSON** following these steps:

                1) **Interpret the Rule Requirements**
                1) **Interpret the Rule Requirements**
                - The rule type is: **{rule_type}**. (It could be Mandatory, Single Value Validation, Single Value Derivation, Multi Value Validation, or Multi Value Derivation.)
                - Understand that multiple entities and attributes **may** appear in the user’s condition for driving attributes for both single value and multi value validation and derivation rules.
                - Single value derivation rules will only derive the value of one attribute. Multi value derivation rules may derive one or several attributes from the same entity.

                2) **Parse the Condition**  for any rule type except mandatory
                - Carefully read the user’s condition text: **“{condition}”**.
                - Carefully read the user’s condition text: **“{condition}”**.
                - The condition may contain the description of an SAP MDG attribute so that you can identify the entity and attribute involved. for example "concatenate industry sector with unit of measure for old material number" mentions three attributes MBRSH - Industry Sector. MEINS - Base Unit of Measure and BISMT - Old Material Number
                - Identify each condition clause:
                    - Entity name (e.g., `BP_CENTRL`)
                    - Attribute name (e.g., `BU_SORT1`)
                    - Operator (e.g., equals, >, <, etc.)
                    - Value (e.g., `"HELLO"`)
                    Each piece might come from a phrase like: “BU_SORT1 is HELLO.”
                    - Entity name (e.g., `BP_CENTRL`)
                    - Attribute name (e.g., `BU_SORT1`)
                    - Operator (e.g., equals, >, <, etc.)
                    - Value (e.g., `"HELLO"`)
                    Each piece might come from a phrase like: “BU_SORT1 is HELLO.”
                - Also extract any **message details** if present. This is for single value validation and multi value validation rules only.
                    - Message Type (e.g., `I`, `W`, `E`, etc.)
                    - Message Type (e.g., `I`, `W`, `E`, etc.)
                    - Message  (e.g., 'Name2 must be uppercase),
                    - Message Class (e.g., `0M`)
                    - Message Class (e.g., `0M`)
                    - Message Number (e.g., `507`)

                3) **Build the Condition Expressions**
                3) **Build the Condition Expressions**
                - For ALL THE RULES rules,  make sure to use operators from this list `EQ, NE, LT, LE, GT, GE, BT, NB, CP, NP`
                - If it’s a mandatory rule, adapt the condition to reflect “Mandatory” logic (e.g., "attribute must not be blank" or "attribute is required").
                - If it’s a mandatory rule, adapt the condition to reflect “Mandatory” logic (e.g., "attribute must not be blank" or "attribute is required").
                - If it’s derivation, figure out how the values are derived or set based on the condition.


                4) A few edge cases to consider:
                - If the value of an attribute is current date or references current date in the condition the value is then always -1.

                5) **Construct the JSON**
                - Use the JSON skeleton provided below (this skeleton is rule-type–specific; you’ll see it in the function that calls you).

                5) **Construct the JSON**
                - Use the JSON skeleton provided below (this skeleton is rule-type–specific; you’ll see it in the function that calls you).
                - Insert each entity-attribute pair into the correct sections, e.g.:
                    - `MODELTODRIVING` or `MODELTOATTRVALUES`, etc.
                - If multiple attributes or conditions, replicate the relevant sections for each.
                - Insert any message details under the appropriate message array (e.g., `ATTRTOMSGDET` or `MODELTOMESSAGE`, depending on the skeleton).
                    - `MODELTODRIVING` or `MODELTOATTRVALUES`, etc.
                - If multiple attributes or conditions, replicate the relevant sections for each.
                - Insert any message details under the appropriate message array (e.g., `ATTRTOMSGDET` or `MODELTOMESSAGE`, depending on the skeleton).
                - Always add `"DraftAi": "X"` to indicate AI involvement.

                6) **Final Output**
                - **Return the final rule in JSON** matching the skeleton below exactly.
                - **Do not** add any extra explanation, text, or formatting.
                6) **Final Output**
                - **Return the final rule in JSON** matching the skeleton below exactly.
                - **Do not** add any extra explanation, text, or formatting.
                - Only output valid JSON.

                ----------------------------------------------------------
                if rule type is multi value derivation: here's some overview: {overview}
                **JSON Skeleton to Use**:
                `{payload_structure}`.
                """



        user_prompt = (
            f""" **Important**:
            - If the user’s condition references a workflow step (e.g., “STEP is 00”), ensure you include it in the driving attributes. Otherwise this is not needed.
            - In case we have multi value rules, always make sure that the right attribute is mapped to the right entity.
            - In case we have multi value rules, always make sure that the right attribute is mapped to the right entity.
            - Do not change the attributes value passed to you, they are the ones that need to be used in the rule.
            - Never make up attributes or entities, always use the ones provided in Entities and Attributes.
            Now, proceed to generate the JSON. Remember to follow the instructions and the structure provided. """


        )




         # --- Start Notification ---
        in_progress_message = (
            "We have started creating the rule for you based on the provided parameters. "
            "This process might take a while. Please wait while we generate the rule."
        )
        await cl.Message(content=in_progress_message, author='Supernova').send()

        print(system_instructions)
        openai_task = asyncio.create_task(
            asyncio.to_thread(
                AIclient.chat.completions.create,
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_instructions},
                    {"role": "user", "content": user_prompt},
                ]
            )
        )

        done, pending = await asyncio.wait({openai_task}, timeout=5)


        if openai_task not in done:
            await cl.Message(
                content="This process is taking longer than expected. Please continue with other tasks; we'll notify you when it's complete.",
                author='Supernova'
            ).send()

        response = await openai_task

        content = response.choices[0].message.content.strip()
        # print(content)


        try:
            parsed_json = json.loads(content)
        except json.JSONDecodeError as e:
            print("Received invalid JSON:", content)
            raise e


        fn = cl.CopilotFunction(
            name="NOTIFY",
            args={
                "msg": "The rule payload is available for review.",
                "title": "Rule Generation completed",
            }
        )

        # res = await fn.acall()
        try:
            res = await asyncio.wait_for(
                fn.acall(),
                timeout=1
            )
        except asyncio.TimeoutError:
                logging.error("Contextual data retrieval timed out")

        capabilities = cl.user_session.get("capabilities")
        if 'SavingRule' in capabilities and capabilities['SavingRule']:
            return f"here's the payload to return to the user and show as is: {content}, and always ask if they want to save the rule"
        else:
            return f"here's the payload to return to the user and show as is: {content}"


    def extract_param_from_question(self, question: str, param_name: str, default=None):
        if question is None:
            return default
        try:
            question_dict = json.loads(question)
            return question_dict.get(param_name, default)
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON: {e}")
            return default

class SaveRule(BaseTool):

    name: str = 'Save_Rule'
    description: str = """ the purpose of this tool is to save the generated rule """

    def _run(self, **kwargs):
        log_info(f"[Tool Start] {self.name} called with question: {kwargs}", "\033[95m")
        result = asyncio.run(self._arun(**kwargs))
        log_info(f"[Tool End] {self.name} returned result.", "\033[95m")
        return result


    async def _arun(self, tool_input=None, **kwargs):
        payload = kwargs.get('payload', '')
        rule_type = kwargs.get('rule_type', '')
        # if not payload:
        #     payload = self.extract_param_from_question(question, 'payload')
        # if not rule_type:
        #     rule_type = self.extract_param_from_question(question, 'rule_type')

        arrayresponse = ''
        rule_type = rule_type.title()
        # Save the rule
        fn = cl.CopilotFunction(
            name="CREATE",
            args={
                "msg": "BRF",
                "payload": payload,
                "ruleType": rule_type
            }
        )

        # res = await fn.acall()
        try:
            res = await asyncio.wait_for(
                fn.acall(),
                timeout=1
            )
        except asyncio.TimeoutError:
                logging.error("Contextual data retrieval timed out")


        if res['status'] == 'Success':

            conditions = res['data']['conditions']

            if len(conditions) > 1:
                attribute = "Multi"
            elif len(conditions) == 1:
                attribute = conditions[0]['attribute']
            else:
                attribute = None

            saved_rule_info = {
                'change_request': res['data']['changeRequest'],
                'data_model': res['data']['dataModel'],
                'entity': res['data']['entity'],
                'attribute': attribute,
                'rule_type': res['data']['ruleType'],
                'rule_name': res['data']['ruleName'],
                'DraftAi': True
            }


            # Update DataFrame and summary
            documentdf = cl.user_session.get("document_content")

            if documentdf is not None:
               self.update_summary_after_save(saved_rule_info)
               summary = self.recalculate_summary()
            else:
                pass
            # Send feedback
            # await cl.Message(content=f"Rule saved successfully. Updated summary:\n{summary}").send()
            return f"This is the status returned from the system: {res}, please make sure to parse the object for the right information"
        else:
            # await cl.Message(content="Failed to save rule, please try again.").send()
            return f"This is the status returned from the system: {res},  please make sure to parse the object for the right information"

    def update_summary_after_save(self, saved_rule_info):
        # Access DataFrame
        documentdf = cl.user_session.get("document_content")
        original_documentdf = documentdf.copy()

        columns_to_normalize = ["Change Request", "Data model", "Entity", "Attribute", "Rule Type"]

        # Normalize DataFrame columns
        normalized_documentdf = self.normalize_columns(documentdf.copy(), columns_to_normalize)

        # Normalize variables from saved_rule_info
        change_request = self.normalize_string(saved_rule_info['change_request'])
        data_model = self.normalize_string(saved_rule_info['data_model'])
        entity = self.normalize_string(saved_rule_info['entity'])
        attribute = self.normalize_string(saved_rule_info['attribute'])
        rule_type = self.normalize_string(saved_rule_info['rule_type'])
        rule_name = saved_rule_info['rule_name']
        draft_ai = saved_rule_info.get('DraftAi', False)

        # Locate matching rows using the normalized values
        mask = (
            (normalized_documentdf['Change Request'] == change_request) &
            (normalized_documentdf['Data model'] == data_model) &
            (normalized_documentdf['Entity'] == entity) &
            (
                (normalized_documentdf['Attribute'] == attribute) if attribute else True
            ) &
            (normalized_documentdf['Rule Type'] == rule_type)
        )

        # Check if any rows match the mask
        if mask.any():
            print("Matching rows found.")
        else:
            print("No matching rows found.")

        # Proceed only if there are matching rows
        if mask.any():
            if 'DraftAi_Count' not in original_documentdf.columns:
                original_documentdf['DraftAi_Count'] = 0
            if draft_ai:
                original_documentdf.loc[mask, 'DraftAi_Count'] += 1

            original_documentdf.loc[mask, 'Rule Name'] = original_documentdf.loc[mask, 'Rule Name'].apply(
                lambda x: f"{x}, {rule_name}" if x else rule_name
            )

            cl.user_session.set("document_content", original_documentdf)
        else:
            print("No updates were made to the DataFrame.")

    def recalculate_summary(self):
        documentdf = cl.user_session.get("document_content")


        total_rules = len(documentdf)

        validated_rules = documentdf.loc[documentdf['Match_Count'] >= 1, 'Match_Count'].sum()

        draft_ai_rules = documentdf.loc[
                (documentdf['DraftAi_Count'] >= 1) & (documentdf['Match_Count'] == 0),
                'DraftAi_Count'
            ].sum()

        no_entry_rules = documentdf[
                (documentdf['Match_Count'] == 0) & (documentdf['DraftAi_Count'] == 0)
            ].shape[0]


        # Recalculate summary

        summary = {
            "Number of rules from the provided document": total_rules,
            "Number of rules with validated entries in the system": validated_rules,
            "Number of rules crafted by AI but not yet validated": draft_ai_rules,
            "Number of rules without entry in the system": no_entry_rules,
        }

        return f""" After saving the rule, here's the updated summary:
                - **Number of rules from the provided document**: {summary['Number of rules from the provided document']}
                - **Number of rules with validated entries in the system**: {summary['Number of rules with validated entries in the system']}
                - **Number of rules crafted by AI but not yet validated**: {summary['Number of rules crafted by AI but not yet validated']}
                - **Number of rules without entry in the system**: {summary['Number of rules without entry in the system']}
                """

    def normalize_columns(self, df, columns):
            for col in columns:
                if col in df.columns:
                    df[col] = df[col].astype(str).str.strip().str.lower()
            return df

    def normalize_string(self, s):
        return str(s).strip().lower() if s else ''

class CheckAttributeDeletion(BaseTool):
    name: str = "CheckAttributeDeletion"
    description: str = """
    Use this tool to check where a given attribute (in a particular Data Model and Entity)
    is used in Business Rules, Workflows, UI Properties, or other areas of the system.
    """

    def _run(self, question):
        log_info(f"[Tool Start] {self.name} called with question: {question}", "\033[95m")
        result = asyncio.run(self._arun(question))
        log_info(f"[Tool End] {self.name} returned result.", "\033[95m")
        return result

    async def _arun(self, query=None, **kwargs) -> str:
        logger.info(f"[CheckAttributeDeletion] _arun called with query: {query}, kwargs: {kwargs}")
        data_model, attribute, entity = self._extract_parameters(query, **kwargs)

        if not data_model:
            logger.error("[CheckAttributeDeletion] Missing required parameter 'data_model'.")
            return "Error: missing required parameter 'data_model'."

        await self._notify_user(data_model, attribute, entity)
        logger.info(f"[CheckAttributeDeletion] Notified user for data_model: {data_model}, attribute: {attribute}, entity: {entity}")

        process_document = processDocumentForRule()
        results = await self._process_business_rules(process_document, data_model)
        logger.info(f"[CheckAttributeDeletion] Retrieved business rules for data_model: {data_model}")

        impacted_rules_count, sample_rules_text = self._analyze_impacted_rules(results, attribute, entity)
        logger.info(f"[CheckAttributeDeletion] Impacted rules count: {impacted_rules_count}")

        workflowdata, impacted_workflows_info = await self._process_workflows(data_model, entity, attribute)
        logger.info(f"[CheckAttributeDeletion] Processed workflows. Impacted workflows: {len(impacted_workflows_info)}")

        self._save_workflow_data(workflowdata)
        logger.info(f"[CheckAttributeDeletion] Workflow data saved.")

        await self._notify_completion()
        logger.info(f"[CheckAttributeDeletion] Notified completion.")

        final_message = self._generate_final_message(attribute, entity, data_model, impacted_rules_count, sample_rules_text, impacted_workflows_info)
        logger.info(f"[CheckAttributeDeletion] Final message generated.")
        return final_message

    def _extract_parameters(self, query, **kwargs):
        logger.info(f"[CheckAttributeDeletion] _extract_parameters called with query: {query}, kwargs: {kwargs}")
        attribute, entity = None, None
        data_model = None

        if isinstance(query, str):
            try:
                data = json.loads(query)
                data_model = data.get("data_model")
                attribute = data.get("attribute")
                entity = data.get("entity")
                if data_model and entity and data_model + "/" in entity:
                    entity = entity.split("/")[-1]
            except (json.JSONDecodeError, TypeError):
                logger.warning("[CheckAttributeDeletion] Failed to parse query as JSON string.")
        elif isinstance(query, dict):
            data_model = query.get("data_model")
            attribute = query.get("attribute")
            entity = query.get("entity")
            if data_model and entity and data_model + "/" in entity:
                entity = entity.split("/")[-1]

        if "data_model" in kwargs:
            data_model = kwargs["data_model"]
        if "attribute" in kwargs:
            attribute = kwargs["attribute"]
        if "entity" in kwargs:
            entity = kwargs["entity"]
            if data_model and data_model + "/" in entity:
                entity = entity.split("/")[-1]

        logger.info(f"[CheckAttributeDeletion] Extracted parameters: data_model={data_model}, attribute={attribute}, entity={entity}")
        return data_model, attribute, entity

    async def _notify_user(self, data_model, attribute, entity):
        logger.info(f"[CheckAttributeDeletion] Notifying user for data_model={data_model}, attribute={attribute}, entity={entity}")
        if attribute:
            in_progress_message = (
                f"We have started checking the impact of deleting '{attribute}' "
                f"in the '{data_model}' Data Model. This process might take a while. "
                "You can do something else in the meantime; we'll notify you when it's done."
            )
        else:
            in_progress_message = (
                f"We have started checking the impact of deleting '{entity}' "
                f"in the '{data_model}' Data Model. This process might take a while. "
                "You can do something else in the meantime; we'll notify you when it's done."
            )
        await cl.Message(content=in_progress_message, author='Supernova').send()
        logger.info(f"[CheckAttributeDeletion] User notified with message: {in_progress_message}")

    async def _process_business_rules(self, process_document, data_model):
        logger.info(f"[CheckAttributeDeletion] _process_business_rules called for data_model={data_model}")
        try:
            result = await asyncio.wait_for(
                process_document.get_rules_from_rdg(
                    data_model=data_model,
                    entity=None,
                    change_request_type=None,
                    data_type=None
                ),
                timeout=10
            )
            logger.info(f"[CheckAttributeDeletion] Business rules fetched within timeout.")
        except asyncio.TimeoutError:
            logger.warning(f"[CheckAttributeDeletion] Business rules fetch timed out. Notifying user.")
            await cl.Message(
                content="This process is taking longer than expected. Please continue with other tasks; we'll notify you when it's complete.",
                author='Supernova'
            ).send()
            result = await process_document.get_rules_from_rdg(
                data_model=data_model,
                entity=None,
                change_request_type=None,
                data_type=None
            )
        logger.info(f"[CheckAttributeDeletion] Returning business rules DataFrame.")
        return pd.DataFrame(result)

    def _analyze_impacted_rules(self, results, attribute, entity):
        logger.info(f"[CheckAttributeDeletion] _analyze_impacted_rules called for attribute={attribute}, entity={entity}")
        if results.empty:
            logger.info(f"[CheckAttributeDeletion] No rules found in results DataFrame.")
            return 0, "No rules found"

        if attribute:
            impacted_rules = results[
                (results['AttributeName'].str.lower() == attribute.lower()) &
                (results['EntityName'].str.lower() == entity.lower())
            ]
            impacted_rules_empty_attribute = results[
                results['RuleDesc'].str.contains("Multi Value Validation|Multi Value Derivation", case=False, na=False)
            ]
            impacted_rules_empty_attribute["ExtractedAttributes"] = impacted_rules_empty_attribute["RuleExpr"].apply(self._extract_usmd_attributes)
            additional_impacted = impacted_rules_empty_attribute[
                impacted_rules_empty_attribute["ExtractedAttributes"].apply(lambda x: attribute.lower() in [attr.lower() for attr in x])
            ]
            all_impacted_rules = pd.concat([impacted_rules, additional_impacted], ignore_index=True)
        else:
            impacted_rules = results[
                (results['EntityName'].str.lower() == entity.lower())
            ]
            all_impacted_rules = pd.concat([impacted_rules], ignore_index=True)

        if all_impacted_rules.empty:
            logger.info(f"[CheckAttributeDeletion] No impacted rules found after filtering.")
            return 0, "No rules found"

        sample_size = min(5, len(all_impacted_rules))
        sample_impacted_rules = all_impacted_rules.sample(n=sample_size, random_state=42)
        columns_to_drop = ['RuleId', 'AiGenerated', 'RuleExpr', 'ExtractedAttributes']
        columns_to_drop = [col for col in columns_to_drop if col in sample_impacted_rules.columns]
        if columns_to_drop:
            sample_impacted_rules = sample_impacted_rules.drop(columns=columns_to_drop)
        sample_rules_text = sample_impacted_rules.to_string(index=False)

        logger.info(f"[CheckAttributeDeletion] Impacted rules count: {len(all_impacted_rules)}. Sample rules text generated.")
        return len(all_impacted_rules), sample_rules_text

    def _extract_usmd_attributes(self, rule_expr):
        logger.info(f"[CheckAttributeDeletion] _extract_usmd_attributes called.")
        try:
            expr_data = json.loads(rule_expr)
            attributes = []
            for key in ["modeltoderiving", "modeltodriving", "modeltoattrvalues", "modeltovalidation"]:
                if key in expr_data:
                    attributes.extend(
                        item.get("usmdAttribute", "")
                        for item in expr_data[key]
                        if "usmdAttribute" in item and item.get("usmdAttribute")
                    )
            logger.info(f"[CheckAttributeDeletion] Extracted attributes: {attributes}")
            return attributes
        except (json.JSONDecodeError, TypeError):
            logger.warning(f"[CheckAttributeDeletion] Failed to extract attributes from rule_expr.")
            return []

    async def _process_workflows(self, data_model, entity, attribute):
        logger.info(f"[CheckAttributeDeletion] _process_workflows called for data_model={data_model}, entity={entity}, attribute={attribute}")
        workflowdata = []
        impacted_workflows_info = []

        fn = cl.CopilotFunction(
            name="report",
            args={
                "msg": "Workflow",
                "actionType": "WfCrList",
                "params": {
                    "dataModel": data_model
                }
            }
        )

        try:
            res = await asyncio.wait_for(fn.acall(), timeout=5)
            logger.info(f"[CheckAttributeDeletion] WfCrList function called. Result type: {type(res)}")
        except asyncio.TimeoutError:
            logger.error("[CheckAttributeDeletion] WfCrList call timed out")
            res = {"data": []}

        if isinstance(res, dict) and "data" in res and res["data"] is not None:
            if isinstance(res["data"], list):
                crlist = pd.DataFrame(res["data"])
                logger.info(f"[CheckAttributeDeletion] Successfully retrieved {len(crlist)} CR records")
            else:
                logger.warning(f"[CheckAttributeDeletion] Expected list in res['data'] but got {type(res['data'])}")
                crlist = pd.DataFrame()
        else:
            logger.warning("[CheckAttributeDeletion] No valid data returned from workflow CR list query")
            crlist = pd.DataFrame()

        entity_to_check_lower = entity.lower() if entity else None
        attribute_to_check_lower = attribute.lower() if attribute else None

        if not crlist.empty and 'CRName' in crlist.columns:
            cr_names = crlist['CRName'].unique()
            for cr_name in cr_names:
                detail_fn = cl.CopilotFunction(
                    name="report",
                    args={
                        "msg": "Workflow",
                        "actionType": "WfDetails",
                        "params": {"CrType": cr_name}
                    }
                )

                try:
                    res_data = await detail_fn.acall()
                    logger.info(f"[CheckAttributeDeletion] WfDetails called for CR: {cr_name}")
                    if res_data is None or not isinstance(res_data, dict):
                        logger.warning(f"[CheckAttributeDeletion] Unexpected response for CR: {cr_name}")
                        continue

                    payload = res_data.get("data")
                    if payload is None:
                        logger.warning(f"[CheckAttributeDeletion] No data payload for CR: {cr_name}")
                        continue

                    if isinstance(payload, str):
                        try:
                            payload = json.loads(payload)
                        except Exception:
                            payload = {}

                    if isinstance(payload, list):
                        workflowdata.extend(payload)
                    else:
                        workflowdata.append(payload)

                    impact_found = self._check_workflow_impact(payload, entity_to_check_lower, attribute_to_check_lower)
                    if impact_found:
                        impacted_workflows_info.append({
                            "CRName": cr_name,
                            "WorkflowDetails": payload
                        })
                        logger.info(f"[CheckAttributeDeletion] Impact found in workflow for CR: {cr_name}")
                except Exception as e:
                    error_msg = f"Error while fetching workflow details for CR: {cr_name}: {str(e)}"
                    logger.error(error_msg)

        logger.info(f"[CheckAttributeDeletion] Workflow processing complete. Impacted workflows: {len(impacted_workflows_info)}")
        return workflowdata, impacted_workflows_info

    def _check_workflow_impact(self, payload, entity_to_check_lower, attribute_to_check_lower):
        logger.info(f"[CheckAttributeDeletion] _check_workflow_impact called.")
        def is_item_impacted(item_dict):
            if not isinstance(item_dict, dict):
                return False

            wf_entity_lower = ''
            if 'UsmdEntity' in item_dict:
                wf_entity_lower = item_dict.get('UsmdEntity', '').lower()
            elif 'EntityName' in item_dict:
                wf_entity_lower = item_dict.get('EntityName', '').lower()

            if entity_to_check_lower and wf_entity_lower != entity_to_check_lower:
                return False

            if attribute_to_check_lower is None and wf_entity_lower == entity_to_check_lower:
                return True

            attribute_found = False
            for key, value in item_dict.items():
                if isinstance(value, dict):
                    if 'UsmdAttribute' in value and value.get('UsmdAttribute', '').lower() == attribute_to_check_lower:
                        attribute_found = True
                        break
                    if 'AttributeName' in value and value.get('AttributeName', '').lower() == attribute_to_check_lower:
                        attribute_found = True
                        break

                if isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):
                            if 'UsmdAttribute' in item and item.get('UsmdAttribute', '').lower() == attribute_to_check_lower:
                                attribute_found = True
                                break
                            if 'AttributeName' in item and item.get('AttributeName', '').lower() == attribute_to_check_lower:
                                attribute_found = True
                                break

            return attribute_found

        o_workflow_info = self._parse_json(payload.get('oWorkflowInfo'))
        arr_workflow_data = self._parse_json(payload.get('arrWorkflowData', []))

        impact_found = False
        if is_item_impacted(o_workflow_info):
            impact_found = True

        if not impact_found and isinstance(arr_workflow_data, list):
            for workflow_item in arr_workflow_data:
                if is_item_impacted(workflow_item):
                    impact_found = True
                    break

                wf_to_prstep_nav = workflow_item.get('wfToPrstepNav', {})
                if isinstance(wf_to_prstep_nav, dict):
                    results = wf_to_prstep_nav.get('results', [])
                    if isinstance(results, list):
                        for result_item in results:
                            if is_item_impacted(result_item):
                                impact_found = True
                                break

        logger.info(f"[CheckAttributeDeletion] Workflow impact found: {impact_found}")
        return impact_found

    def _parse_json(self, data):
        logger.info(f"[CheckAttributeDeletion] _parse_json called.")
        try:
            return json.loads(data) if isinstance(data, str) else data
        except Exception:
            logger.warning(f"[CheckAttributeDeletion] Failed to parse JSON data.")
            return {} if isinstance(data, str) else data

    def _save_workflow_data(self, workflowdata):
        logger.info(f"[CheckAttributeDeletion] _save_workflow_data called.")
        if workflowdata:
            workflowdf = pd.DataFrame(workflowdata)
            workflowdf.to_excel("Workflow.xlsx")
            logger.info(f"[CheckAttributeDeletion] Saved workflow data to Workflow.xlsx")
            print(f"Saved workflow data to Workflow.xlsx")
        else:
            logger.info(f"[CheckAttributeDeletion] No workflow data to save.")
            print("No workflow data to save")

    async def _notify_completion(self):
        logger.info(f"[CheckAttributeDeletion] _notify_completion called.")
        fn = cl.CopilotFunction(
            name="NOTIFY",
            args={
                "msg": "The impact analysis is available for review.",
                "title": "Attribute Deletion Impact Analysis completed",
            }
        )
        try:
            await fn.acall()
            logger.info(f"[CheckAttributeDeletion] Completion notification sent.")
        except Exception as e:
            logger.error(f"[CheckAttributeDeletion] Notification failed: {str(e)}")

    def _generate_final_message(self, attribute, entity, data_model, impacted_rules_count, sample_rules_text, impacted_workflows_info):
        logger.info(f"[CheckAttributeDeletion] _generate_final_message called.")
        impacted_workflows_count = len(impacted_workflows_info)
        workflow_info_text = ""
        rules_info_text = ""

        if impacted_workflows_count > 0:
            cr_names = ', '.join([wf.get('CRName', 'Unknown') for wf in impacted_workflows_info])
            workflow_info_text = (
                f"- Workflows impacted: {impacted_workflows_count}\n"
                f"- Impacted Change Request Types: {cr_names}"
            )
        else:
            workflow_info_text = "- No workflows were impacted."

        if impacted_rules_count > 0:
            rules_info_text = (
                f"- Business Rules impacted: {impacted_rules_count}\n"
                f"- Here are some of the impacted rules (as a table):\n{sample_rules_text}"
            )
        else:
            rules_info_text = "- No Business Rules are impacted."

        final_message = (
            f"**Impact Analysis for Deleting '{attribute}' under entity: {entity} in Data Model: '{data_model}'**\n\n"
            f"{rules_info_text}\n\n"
            f"{workflow_info_text}\n\n"
            "Use caution before deleting the attribute, as these references could break dependent processes.\n\n"
            "**Notification**: The process is now complete!"
        )
        logger.info(f"[CheckAttributeDeletion] Final message ready for user.")
        return final_message
