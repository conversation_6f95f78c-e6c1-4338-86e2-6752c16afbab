# models_index.py  ----------------------------------------------------------
from __future__ import annotations
from pathlib import Path
import pandas as pd, re, time, json


################################################################################
# 1.  Utility: best-effort “data model” name                                   #
################################################################################
def _infer_data_model(xlsx_path: Path) -> str:
    """
    Try to discover the data-model name:

    1. Look into the first ~10 cells of the first sheet for a string like
       'Data model' / 'Model' followed by a value.
    2. Otherwise fall back to the *file stem* (without extension).
    """
    try:
        peek = pd.read_excel(xlsx_path, sheet_name=0, nrows=5, header=None).astype(str)
        flat  = " ".join(peek.fillna("").values.ravel()).lower()
        m     = re.search(r"data\s*model[:\s]+([a-z0-9_\-]+)", flat)
        if m:
            return m.group(1).upper()
    except Exception:
        pass
    # Fallback – file name before the first '_', e.g.  MDG_FICA_… → MDG
    return re.split(r"[_\-]", xlsx_path.stem, 1)[0].upper()


################################################################################
# 2.  Index builder                                                            #
################################################################################
def build_models_index(directory: Path) -> dict:
    """
    Scan every .xlsx in *directory* and return:

        { data_model : { 'attributes': {...}, 'tables': {...} } }

    • Each sheet that has both 'Attribute' AND 'Entity Type' columns is parsed.
    • Entities are accumulated into a *set* to avoid duplicates.
    """
    t0 = time.perf_counter()
    index: dict[str, dict] = {}

    for xlsx_path in directory.glob("*.xlsx"):
        data_model = _infer_data_model(xlsx_path)
        top        = index.setdefault(data_model, {"attributes": {}, "tables": {}})

        try:
            xls = pd.ExcelFile(xlsx_path)
        except Exception as e:
            print(f"[models-index] ⚠️  Cannot open {xlsx_path.name}: {e}")
            continue

        for sheet_name in xls.sheet_names:
            try:
                df = xls.parse(sheet_name, skiprows=2)
            except Exception:
                continue

            # Sheet is useful only if it has both columns
            if not {"Attribute", "Entity Type"}.issubset(df.columns):
                continue

            # Normalise and drop NaNs
            sub = df[["Attribute", "Entity Type"]].dropna()

            for _, row in sub.iterrows():
                # Split on commas / semi-colons so   "ENT1, ENT2"   works
                attrs  = [a.strip() for a in str(row["Attribute"]).split(",") if a.strip()]
                ents   = [e.strip() for e in str(row["Entity Type"]).split(",") if e.strip()]

                # ── Global per-DM view ───────────────────────────────────────
                for attr in attrs:
                    top["attributes"].setdefault(attr, set()).update(ents)

                # ── Per-table (sheet) view ───────────────────────────────────
                sheet_dict = top["tables"].setdefault(sheet_name, {})
                for attr in attrs:
                    sheet_dict.setdefault(attr, set()).update(ents)

    print(
        f"[models-index] built {len(index)} data-models "
        f"in {time.perf_counter() - t0:.1f}s"
    )
    return index


################################################################################
# 3.  Build once and export as MODELS_INDEX                                    #
################################################################################
MODELS_INDEX = build_models_index(Path(__file__).parent / "models")

# For curiosity / debugging:
print(json.dumps(MODELS_INDEX, indent=2, default=list))
