# app.py
import chainlit as cl
from dotenv import load_dotenv
from typing import Dict, Optional
import os
import asyncio
import uuid
from langchain_core.messages import HumanMessage, AIMessage
from langchain_community.cache import InMemoryCache
import langchain
# import prompt_rule_generator as promptRuleGenerator
# from langchain.schema import SystemMessage
# 1) Import LangGraph:
from langgraph.graph import StateGraph
from langgraph.checkpoint.memory import MemorySaver 

from CleanCallbackHandler import CleanCallbackHandler
from NoTracingCallbackHandler import NoTracingCallbackHandler

os.environ["LANGCHAIN_TRACING"] = "false"  
os.environ.pop("LANGCHAIN_ENDPOINT", None)
os.environ.pop("LANGCHAIN_API_KEY", None)

from agents import (
    AgentState,
    router_node,
    router_decision,
    ticket_agent_tool,
    rules_agent_tool,
    weaviate_agent_tool,
    data_model_agent_tool,
    impact_analysis_agent_tool,
    direct_answer_agent
)

# Initialize environment & global variables
langchain.cache = InMemoryCache()
load_dotenv()
secret = os.environ.get("CHAINLIT_AUTH_SECRET")
global_capabilities = {}


@cl.header_auth_callback
async def header_auth_callback(headers: Dict) -> Optional[cl.User]:
    """
    Return None 
    """
    return None

commands = [
    {"id": "Rule_Creator", "icon": "text", "description": "Use the rule creator command to generate rules"},
]

@cl.set_starters
async def set_starters(current_user: cl.User):
    """
    Configure the clickable "starters" in the UI.
    """
    starters = [
        cl.Starter(
            label="Explain data model implementation in RDG",
            message="Provide a detailed and step-by-step explanation on how to implement data model in RDG",
            icon="/public/icons/meeting.svg",
        ),
        cl.Starter(
            label="List my open tickets",
            message="What are all my open tickets",
            icon="/public/icons/list.svg",
        ),
        cl.Starter(
            label="Log a ticket",
            message="Help me create a ticket",
            icon="/public/icons/issue.svg",
        ),
    ]

    if current_user.metadata.get("capabilities"):
        capabilities = current_user.metadata["capabilities"]
        try:
            if 'DataModelStatus' in capabilities and capabilities['DataModelStatus']:
                starters.append(
                    cl.Starter(
                        label="Data Model Status",
                        message="Give me a report of my active data model status",
                        icon="/public/icons/report.svg"
                    )
                )
            if 'ContextualSearch' in capabilities and capabilities['ContextualSearch']:
                starters.append(
                    cl.Starter(
                        label="Help",
                        message="I need help with this current context",
                        icon="/public/icons/h.png"
                    )
                )
            if 'BusinessRulesStatus' in capabilities and capabilities['BusinessRulesStatus']:
                starters.append(
                    cl.Starter(
                        label="Rules status",
                        message="What is the status of my rules implementation?",
                        icon="/public/icons/rules.svg"
                    )
                )
        except KeyError as e:
            print(f"KeyError in capabilities: {e}")

    return starters


def create_agent_graph():
    """
    Build a LangGraph-based workflow with top-level memory persistence.
    """
    workflow = StateGraph(AgentState)

    # 1. Create a single memory store for the entire graph
    memory = MemorySaver()

    # 2. Add each node in your workflow
    workflow.add_node("router", router_node)
    workflow.add_node("ticket_agent", ticket_agent_tool)
    workflow.add_node("rules_agent", rules_agent_tool)
    workflow.add_node("weaviate_agent", weaviate_agent_tool)
    workflow.add_node("data_model_agent", data_model_agent_tool)
    workflow.add_node("impact_analysis_agent", impact_analysis_agent_tool)
    workflow.add_node("direct_answer", direct_answer_agent)
    workflow.add_node("end", lambda s: s)  # End node

    # 3. From router, use router_decision
    workflow.add_conditional_edges(
        "router",
        router_decision,
        {
            "ticket_agent": "ticket_agent",
            "rules_agent": "rules_agent",
            "weaviate_agent": "weaviate_agent",
            "data_model_agent": "data_model_agent",
            "impact_analysis_agent": "impact_analysis_agent",
            "direct_answer": "direct_answer",
            "end": "end"
        }
    )

    # 4. Each mini-agent calls router_node + router_decision for next step
    for agent_node in ["ticket_agent", "rules_agent", "weaviate_agent", "data_model_agent", "impact_analysis_agent", "direct_answer"]:
        workflow.add_conditional_edges(
            agent_node,
            lambda state: router_decision(router_node(state)),
            {
                "ticket_agent": "ticket_agent",
                "rules_agent": "rules_agent",
                "weaviate_agent": "weaviate_agent",
                "data_model_agent": "data_model_agent",
                "impact_analysis_agent": "impact_analysis_agent",
                "direct_answer": "direct_answer",
                "end": "end"
            }
        )

    workflow.set_entry_point("router")
    workflow.set_finish_point("end")

    # 5. Compile the workflow with checkpointer=memory
    return workflow.compile(checkpointer=memory)


@cl.on_chat_start
async def on_chat_start():
    # await cl.context.emitter.set_commands(commands)
    app_user = cl.user_session.get("user")
    if app_user:
        global global_capabilities
        global_capabilities = app_user.metadata.get('capabilities', {})
        cl.user_session.set("capabilities", global_capabilities)

    # Generate a unique thread ID for the conversation
    if app_user and hasattr(app_user, "identifier"):
        thread_id = app_user.identifier
    else:
        # Otherwise, generate a new UUID.
        thread_id = str(uuid.uuid4())
    cl.user_session.set("thread_id", thread_id)
    print(f"Starting conversation with thread ID: {thread_id}")
    
    
     # Prepare callback handlers for streaming
    clean_cb = CleanCallbackHandler(stream_final_answer=True, answer_prefix_tokens=["Helpful", "Answer", ":"])
    no_tracing_cb = NoTracingCallbackHandler(stream_final_answer=True, answer_prefix_tokens=["Helpful", "Answer", ":"])
    cbAsync = cl.AsyncLangchainCallbackHandler(stream_final_answer=True,answer_prefix_tokens=["Helpful", "Answer", ":"])


    # Create the initial state; note we no longer use ConversationBufferWindowMemory
    initial_state: AgentState = {
        "messages": [],
        "current_agent": "",
        "context": {},
        "tools_output": {},
        "capabilities": {},
        "complete": False,
        "awaiting_user_input": False,
        "interaction_context": {},
        "next_node": "",
    }

    graph = create_agent_graph()
    cl.user_session.set("graph", graph)
    cl.user_session.set("initial_state", initial_state)
    cl.user_session.set("current_state", None)


@cl.on_message
async def on_message(message: cl.Message):
     # if message.command == "Rule_generator":
            
        #     # rule_related_tools = [t for t in configure_tools() if t.name in ["Generate_Rule", "gettingRightMDGProperties"]]
            
        #     # rule_agent_executor = create_rule_generator_agent(rule_related_tools)
        #     # user_rule_text = message.content 
        #     # cb = cl.AsyncLangchainCallbackHandler(stream_final_answer=True, answer_prefix_tokens=["Helpful", "Answer", ":"])
            
        #     # try:
        #     #     task = asyncio.create_task(
        #     #         rule_agent_executor.ainvoke({"input": user_rule_text}, {"callbacks": [cb]})
        #     #     )
        #     #     cl.user_session.set("agent_task", task)
        #     #     chunk = await task
                
        #     #     rule_payload = chunk.get('output')  # This is the rule payload
        #     #     cl.user_session.set("rule_payload", rule_payload)
        #     #     await stream_output(chunk)
       
        #     #     notify_general_agent_of_new_payload(rule_payload)
                
        #     # except Exception as e:
        #     #     await cl.Message(content=f"Error in rule generation: {e}", author="Supernova").send()
        # else:     
    try:
        graph = cl.user_session.get("graph")
        current_state = cl.user_session.get("current_state")
        if current_state:
            state = current_state.copy()
        else:
            state = cl.user_session.get("initial_state").copy()

        state["complete"] = False
        state["messages"].append(HumanMessage(content=message.content))

        if state.get("awaiting_user_input", False):
            await cl.Message(content="Processing your response...").send()

        # Retrieve the thread_id stored in the session
        import uuid
        thread_id = cl.user_session.get("thread_id")
        if not thread_id:
            thread_id = str(uuid.uuid4())
            cl.user_session.set("thread_id", thread_id)

        # Pass the thread_id in the configuration
        config = {"configurable": {"thread_id": thread_id}}

        # Prepare callback handlers for streaming
        clean_cb = CleanCallbackHandler(stream_final_answer=True, answer_prefix_tokens=["Helpful", "Answer", ":"])
        no_tracing_cb = NoTracingCallbackHandler(stream_final_answer=True, answer_prefix_tokens=["Helpful", "Answer", ":"])
        cbAsync = cl.AsyncLangchainCallbackHandler(
            stream_final_answer=True,
            answer_prefix_tokens=["Helpful", "Answer", ":"])


        # Pass the config along with callbacks
        task = asyncio.create_task(graph.ainvoke(state, config))
        cl.user_session.set("agent_task", task)
        final_state = await task
        cl.user_session.set("current_state", final_state)

        if final_state.get("error"):
            await cl.Message(content="I encountered an error. Please try again.").send()
            return

        messages = final_state["messages"]
        last_ai_message = next((m for m in reversed(messages) if isinstance(m, AIMessage)), None)
        if last_ai_message:
            await stream_output(last_ai_message.content)
        else:
            print("No AIMessage found in final_state messages.")

    except asyncio.CancelledError:
        await cl.Message(content="Task cancelled.").send()
    except Exception as e:
        await cl.Message(content=f"An error occurred: {str(e)}").send()


async def stream_output(full_text: str):
    """
    Streams AI response chunk by chunk to Chainlit.
    """
    msg = cl.Message(content="", author="Supernova")
    for chunk in full_text:
        await msg.stream_token(chunk)


@cl.on_stop
async def on_stop():
    """
    If user manually clicks 'Stop' in the UI, we cancel the current agent task.
    """
    task = cl.user_session.get("agent_task")
    if task:
        task.cancel()
        await cl.Message(content="Task stopped").send()


if __name__ == "__main__":
    from chainlit.cli import run_chainlit
    run_chainlit(__file__)
