def getFilteredDf(df, params):
    import pandas as pd

    # Make sure it won't truncate wide columns
    pd.set_option('display.max_colwidth', None)
    pd.set_option('display.width', None)  

    filterdDF = df.copy()
    # Define valid columns
    valid_columns = [
        'Change Request',
        'Data Model',
        'Entity',
        'Attribute',
        'Rule Type',
        'Condition',
        'Match Count',
        'Draftai Count',  
        'Rule Name'
    ]

    # Standardize dataframe column names
    filterdDF.columns = filterdDF.columns.str.replace('_', ' ').str.title()

    # Normalize parameter keys to match the dataframe columns
    normalized_params = {}
    for key, value in params.items():
        normalized_key = key.replace('_', ' ').title()
        if normalized_key in valid_columns:
            normalized_params[normalized_key] = value
        else:
            # Handle special parameters like 'Sort', 'SortBy', 'Limit'
            special_key = key.strip().title()
            if special_key in ['Sort', 'Sortby', 'Limit']:
                normalized_params[special_key] = value

    # Helper function to parse operator-value pairs from strings like ">0", "<=100"
    def parse_operator_value(val_str):
        # Define possible operators and check for matches
        operators = ['>=', '<=', '>', '<', '!=', '==']
        for op in operators:
            if val_str.startswith(op):
                # After removing the operator, try converting the rest to a float
                remainder = val_str[len(op):].strip()
                try:
                    num_val = float(remainder)
                    return op, num_val
                except ValueError:
                    # If we can't parse the number, return None
                    return None, None
        return None, None  # No operator found

    # Apply filters based on the normalized parameters
    for column, value in normalized_params.items():
        if column in valid_columns:
            if isinstance(value, str):
                value = value.strip().upper()

                # Check if this is an operator-based numeric condition in string form
                op, num_val = parse_operator_value(value)
                if op is not None and num_val is not None:
                    # Apply operator-based numeric filtering
                    if op == '>':
                        filterdDF = filterdDF[filterdDF[column] > num_val]
                    elif op == '<':
                        filterdDF = filterdDF[filterdDF[column] < num_val]
                    elif op == '>=':
                        filterdDF = filterdDF[filterdDF[column] >= num_val]
                    elif op == '<=':
                        filterdDF = filterdDF[filterdDF[column] <= num_val]
                    elif op == '!=':
                        filterdDF = filterdDF[filterdDF[column] != num_val]
                    elif op == '==':
                        filterdDF = filterdDF[filterdDF[column] == num_val]
                else:
                    # Fallback to the original string logic
                    if value == 'NOT NULL':
                        filterdDF = filterdDF[filterdDF[column].notna()]
                    elif value == 'NULL':
                        filterdDF = filterdDF[filterdDF[column].isna()]
                    else:
                        filterdDF = filterdDF[
                            filterdDF[column].astype(str).str.contains(value, case=False, na=False)
                        ]

            elif isinstance(value, (int, float)):
                filterdDF = filterdDF[filterdDF[column] == value]
            elif isinstance(value, dict):
                # Handle operator-based conditions given as dict
                operator = next(iter(value))
                filter_value = value[operator]
                if operator == '>':
                    filterdDF = filterdDF[filterdDF[column] > filter_value]
                elif operator == '<':
                    filterdDF = filterdDF[filterdDF[column] < filter_value]
                elif operator == '>=':
                    filterdDF = filterdDF[filterdDF[column] >= filter_value]
                elif operator == '<=':
                    filterdDF = filterdDF[filterdDF[column] <= filter_value]
                elif operator == '!=':
                    filterdDF = filterdDF[filterdDF[column] != filter_value]
                elif operator == '==':
                    filterdDF = filterdDF[filterdDF[column] == filter_value]
            else:
                filterdDF = filterdDF[filterdDF[column] == value]

    # Handle special parameters like 'Sort', 'SortBy', 'Limit'
    if 'Sort' in normalized_params and 'Sortby' in normalized_params:
        sort_column = normalized_params['Sortby'].replace('_', ' ').title()
        if sort_column in filterdDF.columns:
            ascending = normalized_params['Sort'].strip().lower() != 'desc'
            filterdDF = filterdDF.sort_values(by=sort_column, ascending=ascending)
    if 'Limit' in normalized_params:
        try:
            limit = int(normalized_params['Limit'])
            filterdDF = filterdDF.head(limit)
        except ValueError:
            pass  # Ignore if 'Limit' is not an integer
        
    # if 'Rule Type' in filterdDF.columns and 'Condition' in filterdDF.columns:
    #     mask = (filterdDF['Rule Type'] == 'Multi Value Derivation') & (filterdDF['Condition'].astype(str).str.len() > 500)
    
    # filterdDFwhithoutCondition = None
    # if mask.any():
    #     filterdDFwhithoutCondition = filterdDF.drop(columns='Condition', errors='ignore')
        
    # return filterdDFwhithoutCondition.to_string(index=False)
    # return filterdDF.to_string(index=False)
    # return filterdDF
    df_markdown = filterdDF.to_markdown(index=False, tablefmt="github")
    return df_markdown