# agents.py
from typing import TypedDict, List, Dict, Any, Optional
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
import chainlit as cl
from typing import Any, Dict, Optional
import os
import tooling

import asyncio
from langgraph.prebuilt import create_react_agent
from langchain_openai.chat_models import ChatOpenAI
from callbackLogging import run_with_dummy, run_chain_with_dummy
from CleanCallbackHandler import CleanCallbackHandler

from dotenv import load_dotenv
load_dotenv()

        
class AgentState(TypedDict):
    messages: List[BaseMessage]
    current_agent: str
    context: Dict[str, Any]
    tools_output: Dict[str, Any]
    capabilities: Dict[str, bool]
    complete: bool
    awaiting_user_input: bool
    interaction_context: Dict[str, Any]
    next_node: str
    
########################################
# 1) Decide Agent (the old "router" logic)
########################################
def decide_agent(user_text: str, conversation_text: str) -> str:
    from langchain_core.messages import SystemMessage, HumanMessage
    
    system_prompt = """You are an expert manager who understands the capabilities of all specialized mini-agents in this system for SAP MDG and Syniti Concento RDG. You must analyze the user's request and decide which of the following specialized mini-agents to route them to:

            1. **ticket_agent**  
            - Use for ticket creation, tracking, or management requests.  
            - Examples: “Create a ticket,” “Check my tickets,” “Update ticket status.”

            2. **rules_agent**  
            - Use for tasks that directly involve creating, analyzing, or generating business rules within the system.  
            - Examples: “Generate a rule,” “Analyze this rule document,” “Add this rule to the system.”  
            - If the user specifically wants to produce, edit, or inspect rules within MDG/RDG, go to rules_agent.  
            - **Important**: If a user only wants conceptual guidance or a general “how-to” about rules (e.g., “Explain how to manage business rules”), route them to weaviate_agent instead, because that’s general knowledge.

            3. **weaviate_agent**  
            - Use for general information or conceptual help about SAP MDG or Syniti Concento RDG, including high-level questions or how-tos.  
            - Examples: “How does RDG work?” “Explain how to manage business rules.” “I need help with this screen.”  
            - Also use for broad conceptual questions about data models or rules (“What is a rule?” “How do I implement rules in RDG?”).

            4. **data_model_agent**  
            - Use for data model–focused tasks such as generating or analyzing data model reports or insights.  
            - Examples: “Generate a data model report,” “Show me entity information,” “Data model status.”  
            - If the user’s question about data models is purely conceptual or ‘how to,’ send them to weaviate_agent.

            5. **impact_analysis_agent**  
            - Use for impact analysis tasks such as checking the impact of deleting an attribute or entity on business rules and workflows on the system.  
            - Examples: “What is the impact of deleting attribute X in data model Y for enty Z?”  
            - Examples: “What is the impact of deleting entity Z in data model Y?”

            6. If the user just says “help” (or anything indicating they need broad guidance), send them to weaviate_agent.

            7. If you can directly answer the user’s question without using any specialized tools or agents, route them to **direct_answer**.

            **Your response should be exactly one of**:  
            ticket_agent, rules_agent, weaviate_agent, data_model_agent, impact_analysis_agent, direct_answer
            No extra text or explanation—just the name of the chosen agent.


"""

    msgs = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=f"Conversation:\n{conversation_text}\nUser: {user_text}")
    ]
    cbAsync = cl.AsyncLangchainCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )
    clean_cb = CleanCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )
    model = ChatOpenAI(model_name="gpt-4o", 
                       temperature=0,
                    #    callbacks=[cbAsync, clean_cb]
                       )

    # response = model.invoke(msgs)

    response = run_with_dummy("decide_agent", system_prompt, conversation_text, user_text, msgs, model, clean_cb, cbAsync)

    # 9) Finally, parse the label
    label = response.content.strip().lower()
    valid_labels = [
        "ticket_agent", 
        "rules_agent", 
        "weaviate_agent", 
        "data_model_agent", 
        "impact_analysis_agent",
        "direct_answer"
    ]
    if label not in valid_labels:
        label = "direct_answer"
    return label

########################################
# 2) The "router_node" function that returns an updated AgentState (dict)
########################################
def router_node(state: AgentState) -> AgentState:
    """
    Node function that updates the next_node in state by calling decide_agent.
    """
    # Check for completion or user input
    if state.get("complete", False):
        state["next_node"] = "end"
        return state

    # If we are waiting for user input (the agent asked a question last turn),
    # then route back to that agent to continue.
    if state.get("awaiting_user_input", False) and state.get("current_agent"):
        state["next_node"] = state["current_agent"]
        return state

   # Get last user message + entire conversation
    conversation_so_far = state["messages"]
    if not conversation_so_far:
        state["next_node"] = "direct_answer"
        return state

    user_msg = conversation_so_far[-1]
    user_text = user_msg.content if isinstance(user_msg, HumanMessage) else ""
    conv_text = "\n".join(f"{m.type}: {m.content}" for m in conversation_so_far)

    # Grab capabilities to handle special cases
    capabilities = cl.user_session.get("capabilities") or {}

    # Decide
    chosen = decide_agent(user_text, conv_text)
    if chosen == "rules_agent" and not capabilities.get("BusinessRulesStatus"):
        chosen = "direct_answer"
    elif chosen == "data_model_agent" and not capabilities.get("DataModelStatus"):
        chosen = "direct_answer"

    state["next_node"] = chosen
    return state

def router_decision(state: AgentState) -> str:
    """
    Returns the next node decision as a string.
    """
    return state["next_node"]

########################################
# 4) The specialized "mini-agent" node functions
########################################
def weaviate_agent_tool(state: AgentState) -> AgentState:
    conversation_so_far = state["messages"]
    user_msg = conversation_so_far[-1] if conversation_so_far else HumanMessage(content="")
    system_msg = SystemMessage(content="""You are the Weaviate Knowledge Base agent. And you know everything about concento RDG, when answering always provide your source when it's a url so we can click on it.
                               when possible enhance then answer from weaviate data with your own knowledge. Do not make stuff up.""")

    all_messages = [system_msg] + conversation_so_far + [user_msg]
    tools = [tooling.ProvideContextualHelp(), tooling.WeaviateDataTool()]
    
    cbAsync = cl.AsyncLangchainCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )
    clean_cb = CleanCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )
    llm = ChatOpenAI(
        model_name="gpt-4o",
        temperature=0.5,
        # callbacks=[cbAsync, clean_cb]
    )

    mini_agent = create_react_agent(llm, tools=tools)
    
    chain_input = {
        "messages": all_messages,
        "remaining_steps": 8
    }
    
    response_state = run_chain_with_dummy(
        chain=mini_agent,
        run_name="weaviate_agent_tool",
        chain_input=chain_input,
        clean_cb=clean_cb,
        cbAsync=cbAsync,
        system_prompt="",
        conversation_text="\n".join(m.content for m in conversation_so_far),
        user_text=user_msg.content
    )

    
    
    final_msgs = response_state["messages"]
    last_msg = final_msgs[-1] if final_msgs else None
    final_text = last_msg.content if isinstance(last_msg, AIMessage) else "No output from Weaviate agent."

    new_state = state.copy()
    new_messages = list(state["messages"])
    new_messages.append(AIMessage(content=final_text))
    new_state["messages"] = new_messages
    new_state["current_agent"] = "weaviate_agent"
    new_state["complete"] = True

    return new_state

def ticket_agent_tool(state: AgentState) -> AgentState:
    """
    ...
    (Same content as your ticket agent, but it must return the updated 'state' dict at the end)
    """
    conversation_so_far = state["messages"]
    user_msg = conversation_so_far[-1] if conversation_so_far else HumanMessage(content="")

    system_msg = SystemMessage(content=""" ### Ticket Management:
                            - **Ticket Creation**:
                            If a user wants to create a ticket, follow these steps:
                            1. First, use the Get_Zendesk_Tickets tool with the ticket title to search for similar tickets
                              the tool will all the ticket for the user, you have to understand the user request and compare it with the list to see if there's any one that is similar to the user request.
                              - If similar tickets are found, show them to the user and ask the user if they want to create a new ticket.
                              - If no similar tickets are found, proceed to the next step which is the creation of the ticket.
                            2. Generate an appropriate title and description for the ticket when none are found.
                            3. Use only the 'title' and 'description' parameters to call the ticket creation tool.
                            4. Always use the tools with proper parameters:
                               - For Get_Zendesk_Tickets: Get_Zendesk_Tickets(title="Your title")
                               - For createTicket: createTickets(title="Your title", description="Your description")

                            5. Always include the ticket ID and URL in your response when a ticket is created.

                            - **Ticket Retrieval**:
                            If a user requests to view existing tickets, retrieve them using the Get_Zendesk_Tickets tool. Display relevant information such as ticket ID, status, and description.
    """)

    # 3) Build the final messages for the mini-agent
    all_messages = [system_msg] + conversation_so_far + [user_msg]
    
    
    # 4) Tools for the mini-agent
    tools = [
        tooling.createTicket(),
        tooling.Get_Zendesk_Tickets(),
    ]
    
    cbAsync = cl.AsyncLangchainCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )
    clean_cb = CleanCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )
    llm = ChatOpenAI(
        model_name="gpt-4o",
        temperature=0.5,
        # callbacks=[cbAsync, clean_cb]
    )

    mini_agent = create_react_agent(llm, tools=tools)
    
    chain_input = {
        "messages": all_messages,
        "remaining_steps": 8
    }
    
    response_state = run_chain_with_dummy(
        chain=mini_agent,
        run_name="ticket_agent_tool",
        chain_input=chain_input,
        clean_cb=clean_cb,
        cbAsync=cbAsync,
        system_prompt="",
        conversation_text="\n".join(m.content for m in conversation_so_far),
        user_text=user_msg.content
    )
    
    
    final_msgs = response_state["messages"]
    last_msg = final_msgs[-1] if final_msgs else None

    # 6) Build new state
    final_text = ""
    awaiting_user_input = False
    new_interaction_context = {}

    if isinstance(last_msg, AIMessage):
        final_text = last_msg.content
    else:
        final_text = "No output from ticket agent."

    # If your agent chooses to ask the user a question with "AWAITING_USER_DECISION", handle it
    if "AWAITING_USER_DECISION" in final_text:
        awaiting_user_input = True
        user_question = final_text.replace("AWAITING_USER_DECISION", "").strip()
        final_text = user_question
        new_interaction_context = {
            "context": user_msg.content,
            "similar_tickets": "...",
            "title": "...",
            "description": "...",
            "question": user_question
        }

    new_state = state.copy()
    new_messages = list(state["messages"])
    # Save the AI's final text as a new AIMessage in the conversation
    new_messages.append(AIMessage(content=final_text))
    new_state["messages"] = new_messages
    new_state["awaiting_user_input"] = awaiting_user_input
    new_state["interaction_context"] = new_interaction_context
    new_state["current_agent"] = "ticket_agent"
    new_state["complete"] = True

    return new_state

def rules_agent_tool(state: AgentState) -> AgentState:
    """
    The rules agent. Return the updated state dict at the end.
    """
    conversation_so_far = state["messages"]
    user_msg = conversation_so_far[-1] if conversation_so_far else HumanMessage(content="")
    
    capabilities = cl.user_session.get("capabilities") or {}
    if 'SavingRule' in capabilities and capabilities['SavingRule']:
        SavingRuleCapabilitiesPrompt = """
        - if the user asks to save the rule make sure that is was initially crafted and and if it was crafted or generated just save it. If it was not already crafted or generated ask them if they want you to generate it first
        - After crafting a new rule using the generating rule tools, propose to the user the option to save the rule. If the user requests to save the rule or agrees when prompted, ensure that a rule exists before proceeding.

        - When the user confirms saving the rule, always call the Save_Rule tool with the generated payload and rule type in the exact following format:
          payload: <payload_in_json>,
          rule_type: <rule_type>
          
        - Important: Do not include any additional parameters or alter the format when invoking the Save_Rule tool. The tool should only be called with the payload and rule_type keys, exactly as specified above.
          Avoid: Calling the Save_Rule tool with different or extra parameters like UsmdCreqType, UsmdModel, etc. Ensure consistency in the parameters to prevent unexpected behavior.
        """
    else:
        SavingRuleCapabilitiesPrompt = "Inform the user that saving rule capabilities are not available."
    
    system_msg = SystemMessage(content=f""" you are the rules agent, you are an expert in SAP MDG and BRF+ rules here are you supposed to proceed:
        - Automatically call the analyseDocument' tool whenever the user asks questions about rules. 
        - Use the returned information to provide insights, but never offer a downloadable link for this case.

        - If the user wants to upload a new file, call the 'uploadnewfile' tool immediately.

        ### User Interactions After Document Processing:
        - **General Questions:** 
        # Strict Instruction:
        - If the user’s question is about any content, metrics, or entries extracted from the document, you MUST transform their request into a JSON filter and call the tool `filterDataframe` with that JSON as a string. No exceptions.

        YOU MUST ALWAYS:
          1. Convert the user’s natural language request about the document into a JSON dictionary representing the filter conditions.
          2. Call filterDataframe with that JSON dictionary as a string, every single time.
          No exceptions. If the question references "entries," "document content," or "data from the document," you must convert the user’s request into JSON and call the tool filterDataframe. 

          The DataFrame has the following columns: change_request, data_model, entity, attribute, rule_type, condition, match_count, DraftAi_Count, rule_name.
          and regarding the rule type here's the list of the possible values: ['mandatory', 'single value validation', 'multi value validation', 'single value derivation', 'multi value derivation']
          The output JSON should include the fields and values needed to filter the DataFrame.
          and ALWAYS call the filterDataframe with that JSON passed as a string, it always needs to be a string

          Examples:
          1. Natural language: "What are the top 20 mandatory rules without entry in the system?"
            JSON output: {{
              "Rule Type": "mandatory",
              "Match_Count": 0,
              "DraftAi_Count" : 0
              "Limit": 20
            }}

          2. Natural language: "I need 10 single validation rules with entry but not yet validated."
            JSON output: {{
              "Rule Type": "single validation",
              "Rule Name": "not null",
              "DraftAi_Count": ">=1"
              "Limit": 10
            }}
          a rule with no entry in the system has a match count 0 and draftai count 0 
          The 'Match Count' column shows the number of rules that match the other columns (excluding 'Condition') in the system and that was validated.
          The 'DraftAi Count' column shows the number of rules that match the other columns (excluding 'Condition') in the system and that was not yet validated.
        
          always replace single validation or derivation rule with Single Value Validation or Derivation rule and  multi validation or derivation with Multi Value Validation or Derivation rule
          Present Results Without Modification: Display the filtered data exactly as it appears in the dataframe—without any modifications or transformations.
          Display in Table Format: Present the result in a clear table format for easy understanding.
          
          Never make up data or provide inaccurate information. Always use the data available in the dataframe to generate responses.
          
        - **Specific Rule Analysis:** 
          If a user mentions a specific rule name or say something similar to analyse this rule, call the 'analyze rule'. Only call this tool if rule_name is not empty and call it using the following parameters:
            - change_request, data_model, entity, attribute, rule_type, condition, and rule_name.
          
          Your job:
          1. Craft a JSON payload that adheres to SAP MDG best practices for rule creation.
          2. Compare the system-generated payload with your crafted one.
          3. Provide feedback on whether the payload from the system  is well-crafted. That final feedback has to always be about the system payload in comparison to the one you crafted 
       ### Rule Generation Process:
        When generating a new rule, you must:
        - Ensure the correct payload format with these fields:
            - change_request
            - data_model
            - entity
            - attribute
            - rule_type
            - condition
        
        **Tool Invocation Rule**:  
        **Always** call `Generate_Rule` with only these parameters as keyword arguments: `change_request`, `data_model`, `entity`, `attribute`, `rule_type`, `condition`.  
        For example:  
        ```  
        Generate_Rule(change_request="", data_model="", entity="", attribute="", rule_type="", condition="")
        ```  
        
        **Do not** pass a JSON payload or extra parameters (like `UsmdModel`, `UsmdCreqType`) directly to `Generate_Rule`.  
        The `Generate_Rule` tool will return a final JSON payload as its output. Present this JSON to the user as the result, without modification or additional formatting.

        {SavingRuleCapabilitiesPrompt}
  
          """)
    all_messages = [system_msg] + conversation_so_far + [user_msg]
    tools = [
        tooling.analyseRule(),
        tooling.GenerateRule(),
        tooling.analyseDocument(),
        tooling.uploadnewfile(),
        tooling.filterDataframe(),
        tooling.SaveRule()
    ]
    
    cbAsync = cl.AsyncLangchainCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )
    clean_cb = CleanCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )

    llm = ChatOpenAI(
        model_name="gpt-4o",
        temperature=0,
        # callbacks=[cbAsync]
    )

    mini_agent = create_react_agent(llm, tools=tools)
    
    chain_input = {
        "messages": all_messages,
        "remaining_steps": 8
    }
    
    response_state = run_chain_with_dummy(
        chain=mini_agent,
        run_name="rules_agent_tool",
        chain_input=chain_input,
        clean_cb=clean_cb,
        cbAsync=cbAsync,
        system_prompt="",
        conversation_text="\n".join(m.content for m in conversation_so_far),
        user_text=user_msg.content
    )
    # response_state = mini_agent.invoke({
    #      "messages": all_messages,
    #      "remaining_steps": 8
    #  })
   
    final_msgs = response_state["messages"]
    last_msg = final_msgs[-1] if final_msgs else None

    final_text = last_msg.content if isinstance(last_msg, AIMessage) else "No output from rules agent."

    new_state = state.copy()
    new_messages = list(state["messages"])
    new_messages.append(AIMessage(content=final_text))
    new_state["messages"] = new_messages
    new_state["current_agent"] = "rules_agent"
    new_state["complete"] = True

    return new_state

def data_model_agent_tool(state: AgentState) -> AgentState:
    conversation_so_far = state["messages"]
    user_msg = conversation_so_far[-1] if conversation_so_far else HumanMessage(content="")
    system_msg = SystemMessage(
        content="""You are the Data Model Specialist.
                    If the user asks for object counts Reports, use the appropriate function from the following list:
                    - reportDataModelActive
                    - reportDataModelCustom
                    - reportEntities
                    - reportEntitiesByDataModel
        
        Your task is to:
        1. Determine if the user's question is related to data model counts or reports implementation in the system. If so, select the correct function to pass to the parameter 'function' 
        2. Generate an appropriate title to pass to the 'title' parameter based on the user's question.
           e.g., if the user asks for the number of active data models, the title should be 'Active Data Models'. (e.g., Get_implementation_reports(title='Active Data Models', function='reportDataModelActive')).
        
        **Note:** If the user's question pertains to rules (e.g., counts of specific data model without rules  entries or specific rule conditions), redirect them to the appropriate rule tools, as described in the 'Business Rules Capabilities' section. Data model capabilities are not applicable to rule analysis.
        """)
    all_messages = [system_msg] + conversation_so_far + [user_msg]

    tools = [tooling.Get_implementation_reports()]
    
    cbAsync = cl.AsyncLangchainCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )
    clean_cb = CleanCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )
    llm = ChatOpenAI(
        model_name="gpt-4o",
        temperature=0.5,
        # callbacks=[cbAsync, clean_cb]
    )

    mini_agent = create_react_agent(llm, tools=tools)
    
    chain_input = {
        "messages": all_messages,
        "remaining_steps": 8
    }
    
    response_state = run_chain_with_dummy(
        chain=mini_agent,
        run_name="data_model_agent_tool",
        chain_input=chain_input,
        clean_cb=clean_cb,
        cbAsync=cbAsync,
        system_prompt="",
        conversation_text="\n".join(m.content for m in conversation_so_far),
        user_text=user_msg.content
    )
    
    final_msgs = response_state["messages"]
    last_msg = final_msgs[-1] if final_msgs else None
    final_text = last_msg.content if isinstance(last_msg, AIMessage) else "No output from data model agent."

    new_state = state.copy()
    new_messages = list(state["messages"])
    new_messages.append(AIMessage(content=final_text))
    new_state["messages"] = new_messages
    new_state["current_agent"] = "data_model_agent"
    new_state["complete"] = True

    return new_state

def impact_analysis_agent_tool(state: AgentState) -> AgentState:
    conversation_so_far = state["messages"]
    user_msg = conversation_so_far[-1] if conversation_so_far else HumanMessage(content="")
    system_msg = SystemMessage(
        content="""You are the impact analysis agent.
                     When the user asks about the impact of deleting an attribute or entity in the system you must:
      Parse the input to extract the following fields, if available. The attribute is not needed if not passed:
        - data_model
        - entity
        - attribute
      If some values are missing from the user’s input, extract only the ones that are present and omit the missing fields from the JSON.
      Call the CheckAttributeDeletion tool, passing a JSON object with the extracted values.
      for example: 
        - User input: "What is the impact of deleting attribute X in data model Y for entity Z?"
        - User input: "What is the impact of deleting entity Z in data model Y?"
                
                      """)
    all_messages = [system_msg] + conversation_so_far + [user_msg]

    tools = [tooling.CheckAttributeDeletion()]
    
    cbAsync = cl.AsyncLangchainCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )
    clean_cb = CleanCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )
    llm = ChatOpenAI(
        model_name="gpt-4o",
        temperature=0.5,
        # callbacks=[cbAsync, clean_cb]
    )

    mini_agent = create_react_agent(llm, tools=tools)
    
    chain_input = {
        "messages": all_messages,
        "remaining_steps": 8
    }
    
    response_state = run_chain_with_dummy(
        chain=mini_agent,
        run_name="impact_analysis_agent_tool",
        chain_input=chain_input,
        clean_cb=clean_cb,
        cbAsync=cbAsync,
        system_prompt="",
        conversation_text="\n".join(m.content for m in conversation_so_far),
        user_text=user_msg.content
    )
    
    final_msgs = response_state["messages"]
    last_msg = final_msgs[-1] if final_msgs else None
    final_text = last_msg.content if isinstance(last_msg, AIMessage) else "No output from data model agent."

    new_state = state.copy()
    new_messages = list(state["messages"])
    new_messages.append(AIMessage(content=final_text))
    new_state["messages"] = new_messages
    new_state["current_agent"] = "data_model_agent"
    new_state["complete"] = True

    return new_state

def direct_answer_agent(state: AgentState) -> AgentState:
    conversation_so_far = state["messages"]
    user_msg = conversation_so_far[-1] if conversation_so_far else HumanMessage(content="")
    system_msg = SystemMessage(content="You are a generalist agent that can answer any question without using any tools about Syniti RDG and SAP MDG. they answers should only be withoin these scopes")

    all_messages = [system_msg] + conversation_so_far + [user_msg]
    cbAsync = cl.AsyncLangchainCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )
    clean_cb = CleanCallbackHandler(
        stream_final_answer=True, 
        answer_prefix_tokens=["Helpful", "Answer", ":"]
    )
    llm = ChatOpenAI(
        model_name="gpt-4o",
        temperature=0.5,
        # callbacks=[cbAsync, clean_cb]
    )

    mini_agent = create_react_agent(llm, tools=[])
    
    chain_input = {
        "messages": all_messages,
        "remaining_steps": 8
    }
    
    response_state = run_chain_with_dummy(
        chain=mini_agent,
        run_name="direct_answer_agent_tool",
        chain_input=chain_input,
        clean_cb=clean_cb,
        cbAsync=cbAsync,
        system_prompt="",
        conversation_text="\n".join(m.content for m in conversation_so_far),
        user_text=user_msg.content
    )
    final_msgs = response_state["messages"]
    last_msg = final_msgs[-1] if final_msgs else None
    final_text = last_msg.content if isinstance(last_msg, AIMessage) else "No output from direct_answer agent."

    new_state = state.copy()
    new_messages = list(state["messages"])
    new_messages.append(AIMessage(content=final_text))
    new_state["messages"] = new_messages
    new_state["current_agent"] = "direct_answer"
    new_state["complete"] = True

    return new_state
