name: Deploy RDG-AI to QAS
on:
  pull_request:
    types: 
      - closed
    branches:
      - QAS
jobs:
    RDG-AI-Deploy-to-QAS:
      if: github.event.pull_request.merged == true
      runs-on: ubuntu-latest
      name: Deploy RDG-AI to QAS
      steps:
        - uses: actions/checkout@v4.1.1
          with:
            fetch-depth: 1
        - name: Install Cloud Foundry CLI
          run: |
            sudo apt update
            sudo apt install -y wget gnupg
            wget -qO- https://packages.cloudfoundry.org/debian/cli.cloudfoundry.org.key | gpg --dearmor | sudo tee /usr/share/keyrings/cloudfoundry-cli-archive-keyring.gpg > /dev/null
            echo "deb [signed-by=/usr/share/keyrings/cloudfoundry-cli-archive-keyring.gpg] https://packages.cloudfoundry.org/debian stable main" | sudo tee /etc/apt/sources.list.d/cloudfoundry-cli.list
            sudo apt update
            sudo apt install -y cf8-cli
          
        - name: run CF commands to deploy DEV
          id: cf-deploy
          env:
            file: ./manifest-dev.yml
            procfile: Procfile
            Repository_name: rdgaidev
            cf_endpoint: https://api.cf.us10.hana.ondemand.com
            org: '"Data Migration Resources Inc._rdg-lab-g4ygl9bi"'
          run: |
            cf login -a $cf_endpoint -u ${{secrets.CF_USERNAME}} -p ${{secrets.CF_PASSWORD}} -o "\"Data Migration Resources Inc._rdg-lab-g4ygl9bi"\" -s RDGLABQAS
            cf delete rdg-ai-qas -r -f
            cf push rdg-ai-qas -f manifest-qas.yml
      # - name: run CF commands to deploy QAS
        #   id: cf-deploy
        #   env:
        #     cf_endpoint: https://api.cf.us10.hana.ondemand.com
        #   run: |
        #     curl -L "https://packages.cloudfoundry.org/stable?release=linux64-binary&source=github" | tar -zx
        #     chmod +x cf
        #     chmod a+rwx ./Procfile
        #     sudo mv cf /usr/local/bin
        #     cf login -a $cf_endpoint -u ${{secrets.CF_USERNAME}} -p ${{secrets.CF_PASSWORD}} -o "\"Data Migration Resources Inc._rdg-lab-g4ygl9bi"\" -s RDGLABQAS
        #     cf delete rdg-ai-qas -r -f
        #     cf push rdg-ai-qas -f manifest-qas.yml